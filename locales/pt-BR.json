{"product": {"variants": {"choose_option": "Escolha um {{ option }}", "chosen_option_html": "{{ option }}: <span>{{ value }}</span>", "choose_variant": "Escolha uma variante"}, "price": {"range_html": "{{ price_min }} - {{ price_max }}", "unit_pricing_html": "{{ unit_quantity }} | {{ unit_price }} / {{ unit_measurement}}"}, "badge": {"sale_percentage_range_html": "Economize até {{ price_percent }}%", "sale_percentage_single_html": "<PERSON>var {{ price_percent }}%", "sale_money_range_html": "Economize até {{ price }}", "sale_money_single_html": "<PERSON><PERSON> {{ price }}", "sold_out": "Vendido", "sale": "<PERSON><PERSON><PERSON>", "in_stock": "Em estoque"}}, "store_availability": {"general": {"in_stock": "Em estoque", "out_of_stock": "Fora de estoque", "view_store_info": "Ver informações da loja", "check_other_stores": "Verifique a disponibilidade em outras lojas", "available_for_pick_up_at_html": "Recol<PERSON> disponível em <b>{{ location_name }}</b>", "unavailable_for_pick_up_at_html": "Recolha atualmente indisponível em <b>{{ location_name }}</b>", "available_for_pick_up_at_time_html": "<PERSON><PERSON><PERSON><PERSON> di<PERSON><PERSON>, {{ pick_up_time }}", "unavailable_for_pick_up_at_time_html": "Coleta atualmente indisponível", "kilometers": "km", "miles": "mi"}}, "structured_data": {"breadcrumbs": {"cart": "<PERSON><PERSON><PERSON>", "collections": "Coleções", "page": "<PERSON><PERSON><PERSON><PERSON> {{ page }}", "products": "<PERSON><PERSON><PERSON>", "search": "Busca", "tags_html": "<PERSON><PERSON> \"{{ tags }}\""}}, "recipient": {"form": {"checkbox_label": "Eu quero enviar isso como um presente", "email_label": "E-mail do destinatário", "email_placeholder": "E-mail do destinatário *", "error_message": "O e-mail não é válido", "name_label": "Nome do destinatário (opcional)", "name_placeholder": "Nome do destinatário (opcional)", "message_label": "Mensagem (opcional)", "message_placeholder": "Mensagem (opcional)", "max_characters": "<PERSON><PERSON><PERSON><PERSON> de {{ max_characters }} caracteres"}}, "sections": {"complementary_product_block": {"from": "A partir de", "now": "<PERSON><PERSON><PERSON>", "price_per_unit_html": "{{ total_quantity }} | {{ unit_price }} / {{ unit_measure }}", "view_details": "Ver produto"}}, "date_formats": {"month_day_year": "%B %d, %Y"}, "general": {"404": {"title": "Página não encontrada", "subtext": "<PERSON><PERSON><PERSON><PERSON>, mas a página solicitada não existe.", "continue_shopping_html": "<PERSON>e pesquisar ou <a href=\"{{ continue_link }}\">continuar comprando →</a>"}, "accessibility": {"star_review_text": "<PERSON><PERSON><PERSON><PERSON>", "star_reviews_text": "Avaliações", "star_reviews_info": "{{ rating_value }} fora de {{ rating_max }} estrelas"}, "pagination": {"previous": "Anterior", "next": "Próximo", "pagination_button": "<PERSON><PERSON><PERSON>"}, "password_page": {"opening_soon": "Abertura em Breve", "spread_the_word": "Espalhe a novidade", "or": "ou", "change_your_password_settings": "alterar as configurações de sua senha", "login_form_heading": "Entre na loja usando a senha", "login_form_password_label": "<PERSON><PERSON>", "login_form_password_placeholder": "<PERSON><PERSON> se<PERSON>a", "login_form_submit": "Entrar", "signup_form_submit": "Enviar", "signup_form_success": "Iremos enviar-lhe um e-mail para a direita antes de abrirmos!", "admin_link_html": "Você é o dono da loja? <a href=\"/admin\" class=\"text-link\">Faça login aqui</a>", "password_link": "Entre usando a senha", "powered_by_shopify_html": "Esta loja será patrocinada por <a href=\"https://shopify.pxf.io/pixel-union\" target=\"_blank\">Shopify</a>"}, "meta": {"tagged_html": "<PERSON><PERSON> \"{{ tags }}\"", "page": "<PERSON><PERSON><PERSON><PERSON> {{ page_number }}"}, "forms": {"post_error": "Nem todos os campos foram preenchidos corretamente", "post_field_error_html": "O <strong>{{ field }}</strong> {{ error }}", "required_field": "Indica um campo obrigatório"}, "newsletter_form": {"name": "Nome", "email": "E-mail", "subscriber_first_name": "Nome", "subscriber_last_name": "Sobrenome", "success_text": "<PERSON><PERSON><PERSON> por participar da nossa lista de e-mails!", "placeholder": "Digite seu endereço de e-mail", "submit": "Inscrever-se"}, "breadcrumbs": {"home": "Home", "page": "Página {{ current_page }} de {{ pages }}", "page_text": "<PERSON><PERSON><PERSON><PERSON>", "of_text": "de"}, "search": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pesquise alguma coisa em nossa loja.", "no_results_html": "Verifique a ortografia ou use uma palavra ou frase diferente.", "advanced_search_html": "Pesquisa Avançada →", "suggestions": "Sugestões", "no_results": "Nenhum resultado encontrado.", "search_for": "Pesquise \"{{ search_terms }}\"", "products": "<PERSON><PERSON><PERSON>", "pages": "<PERSON><PERSON><PERSON><PERSON>", "posts": "Publicações", "pages_and_posts": "Páginas e publicações", "results_count": {"zero": "{{ count }} resultados para \"{{ search_terms }}\"", "one": "{{ count }} resultado para \"{{ search_terms }}\"", "other": "{{ count }} resultados para \"{{ search_terms }}\""}, "placeholder": "", "placeholder_with_shop_name": "Pesquisar {{ shop_name }}...", "submit": "<PERSON><PERSON><PERSON><PERSON>", "common_terms": "Comumente pesquisado"}, "language": {"dropdown_label": "Idioma"}, "country": {"dropdown_label": "<PERSON><PERSON>"}}, "gallery": {"filter": {"all": "Todos"}}, "blogs": {"general": {"pagination_button": "<PERSON><PERSON><PERSON>", "continue_reading_html": "Ver artigo completo", "tagged": "marcado", "view_all": "<PERSON><PERSON> as categorias"}, "sidebar": {"recent_articles": "Artigos recentes", "categories": "Categorias", "follow_us": "Siga-nos"}, "article": {"read_time": "min ler", "posted_in_html": "{{ tags }}", "additional_articles": "Ver artigo completo", "author_on_date_html": "Postado em {{ date }} por {{ author }}", "by_author": "por {{ author }}", "by": "por", "previous_article_html": "Anterior", "next_article_html": "Próximo", "tags": "<PERSON><PERSON>", "author": "Autor", "comment_meta_html": "<strong>{{ author }}</strong> em <strong><time datetime=\"{{ date }}\">{{ date }}</time></strong>"}, "counts": {"comments_with_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "comments": {"response_title": "Comentários", "response_count": {"zero": "Respostas", "one": "{{ count }} Re<PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}, "title": "Deixe um comentário", "name": "Nome", "email": "E-mail", "comment": "<PERSON><PERSON><PERSON><PERSON>", "comment_placeholder": "O que você gostaria de dizer?", "post": "<PERSON><PERSON> come<PERSON>", "moderated": "Os comentários serão aprovados antes de serem exibidos.", "success_moderated": "Seu comentário foi postado com sucesso. Ele será aprovado antes de ser exibido.", "success": "Seu comentário foi postado com sucesso! Obrigado!"}}, "collections": {"general": {"all": "Todos", "title": "Coleções de Produtos", "no_matches": "Nenhum produto encontrado nesta coleção", "link_title": "Procurar {{ title }}", "all_collection_title": "Todos {{ title }}", "quick_shop": "+ <PERSON><PERSON><PERSON>", "pagination_button": "<PERSON><PERSON><PERSON>", "new": "Nova", "coming_soon": "Em Breve", "best_seller": "<PERSON><PERSON>", "staff_pick": "Escolha <PERSON>", "pre_order": "Para Encomendar", "sale": "<PERSON><PERSON><PERSON>", "view_all": "Ver todos", "view_product_details": "Ver todos os detalhes do produto", "sold_out": "Vendido"}, "sidebar": {"collections": "Coleções", "product_types": "Tipos", "vendors": "Vendedores", "tags": "<PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "apply_filter": "Aplicar", "clear_all": "<PERSON><PERSON> tudo", "from": "A partir de", "to": "Para"}, "sorting": {"title": "Ordenar por", "sort_collection": "Ordenar {{ title }}", "filter": "Filtrar por", "filter_collection": "Filtrar {{ title }}", "featured": "Em destaque", "best_selling": "<PERSON><PERSON> vendidos", "az": "Ordem alfabética: A-Z", "za": "Ordem alfabética: Z-A", "price_ascending": "Preço: menor para maior", "price_descending": "Preço: maior para menor", "date_descending": "Data: novo para antigo", "date_ascending": "Data: antigo para novo"}}, "products": {"general": {"previous_product_html": "Anterior", "next_product_html": "Próximo", "from": "a partir de"}, "product": {"sold_out": "Vendido", "unavailable": "Indisponível", "quantity": "Quantidade", "add_to_cart": "Adicionar ao car<PERSON>ho", "add_to_cart_success": "<PERSON><PERSON><PERSON><PERSON>", "vendor": "<PERSON><PERSON><PERSON>", "related_items": "Itens relacionados", "collections": "Coleções", "product_types": "Tipo", "tags": "Categoria", "savings": "Você economiza", "products_tagged": "Produtos marcados {{ tag }}", "items_left_count": {"one": "item deixado", "other": "itens deixados"}, "view_in_your_space": "Ver en tu espacio", "select_variant": "Por favor, selecione todas as suas opções"}, "notify_form": {"email": "Digite seu endereço de e-mail...", "send": "Enviar", "message_content": "Notifique-me quando o seguinte produto estiver disponível novamente: {{ product }} | {{ url }}", "email_content": "Notifique-me quando o seguinte produto estiver disponível novamente: ", "post_success": "Obrigado! Iremos notificá-lo quando este produto estiver disponível!", "post_error": "Favor fornecer um endereço de e-mail válido."}, "item": {"price": {"price_per_unit_html": "{{total_quantity}} | {{unit_price}} / {{unit_measure}}"}}}, "contact": {"form": {"name": "Nome", "email": "E-mail", "phone": "Telefone", "message": "Mensagem", "send": "Enviar", "post_success": "<PERSON><PERSON><PERSON> por entrar em contato conosco. Responderemos o mais rápido possível.", "checkbox_validation": "Por favor, verifique se pelo menos uma caixa de seleção está marcada."}}, "customer": {"account": {"title": "<PERSON><PERSON>", "details": "<PERSON>al<PERSON> da conta", "view_addresses": "<PERSON><PERSON> endereç<PERSON>", "return": "Voltar para detalhes da conta", "primary_address": "Endereço primário"}, "orders": {"title": "Histórico de Pedidos", "order_number": "Pedido", "date": "Data", "payment_status": "Status do pagamento", "fulfillment_status": "Status do atendimento", "total": "Total", "none": "Você ainda não fez nenhum pedido."}, "activate_account": {"title": "Ativar Conta", "subtext": "Crie uma senha para ativar sua conta.", "submit": "Ativar Conta", "cancel": "Recusar convite", "password": "<PERSON><PERSON>", "password_confirm": "Confirme a senha", "or": "ou"}, "addresses": {"no_addresses": "Nenhum endereço", "title": "<PERSON><PERSON> endereç<PERSON>", "default": "Padrão", "add_new": "Adicionar um novo endereço", "edit_address": "<PERSON><PERSON>", "company": "Empresa", "address1": "Endereço 1", "address2": "Endereço 2", "city": "Cidade", "country": "<PERSON><PERSON>", "province": "Estado", "zip": "CEP", "phone": "Telefone", "set_default": "Definir como endereço padrão", "add": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>r endere<PERSON>", "first_name": "Nome", "last_name": "Sobrenome", "cancel": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir", "or": "ou"}, "login": {"title": "Área do cliente", "returning_customers": "Clientes cadastrados", "new_customers": "Novos clientes", "forgot_password": "Esque<PERSON>u a senha?", "sign_up_html": "Inscrever-se", "new_customer_label": "Novo cliente?", "sign_in": "Entrar", "cancel": "Retornar à loja", "guest_title_html": "Continuar como Convidado →", "first_name": "Nome", "last_name": "Sobrenome", "email": "E-mail", "password": "<PERSON><PERSON>", "guest_continue": "<PERSON><PERSON><PERSON><PERSON>", "or": "ou"}, "recover_password": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtext": "Enviaremos um e-mail para você redefinir sua senha.", "success": "Enviamos um e-mail com um link para você atualizar sua senha.", "email": "E-mail", "submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "or": "ou"}, "reset_password": {"title": "Redefinir a senha da conta", "subtext": "Digite uma nova senha para {{ email }}", "submit": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "password_confirm": "Confirmar <PERSON><PERSON><PERSON>"}, "order": {"title": "Pedido {{ name }}", "date": "Efe<PERSON><PERSON> em {{ date }}", "note": "Notas de pedidos", "note_placeholder": "Nenhuma nota de ordem foi fornecida.", "cancelled": "Pedido cancelado em {{ date }}", "cancelled_reason": "Motivo: {{ reason }}", "billing_address": "Endereço de cobrança", "payment_status": "Status do pagamento", "shipping_address": "Endereço para entrega", "fulfillment_status": "Status do atendimento", "discount": "Desconto", "shipping": "<PERSON><PERSON>", "tax": "<PERSON><PERSON><PERSON>", "product": "Produ<PERSON>", "sku": "Número de referência", "price": "Preço", "quantity": "Quantidade", "total": "Total", "fulfilled_at": "Realizado em {{ date }}", "subtotal": "Subtotal"}, "register": {"title": "C<PERSON><PERSON> conta", "sign_up": "Inscrever-se", "cancel": "Retornar à loja", "returning_customer_label": "Já é cliente?", "sign_in_html": "Entrar", "first_name": "Nome", "last_name": "Sobrenome", "email": "E-mail", "password": "<PERSON><PERSON>", "or": "ou"}}, "homepage": {"onboarding": {"product_title": "Nome do seu produto", "product_description": "Essa área é usada para descrever os detalhes do seu produto. Diga aos clientes sobre a aparência, toque e estilo de seu produto. Adicione detalhes como cor, materiais usados, tamanho e onde foi fabricado.", "collection_title": "Nome de sua coleção", "blog_title": "Título de sua postagem", "blog_excerpt": "Sua loja ainda não publicou nenhuma postagem de blog. Um blog pode ser usado para falar sobre o lançamento de novos produtos, dicas e outras novidades que queira compartilhar com os clientes. Você também pode ver nosso blog de e-commerce do Shopify para inspiração e conselhos para sua própria loja e blog.", "blog_author": "Nome do autor", "no_content": "Atualmente, esta seção não inclui nenhum conteúdo. Adicione conteúdo nesta seção usando a barra lateral.", "menu_title": "Título do menu", "caption_headline": "<PERSON><PERSON><PERSON><PERSON>", "caption_subtitle": "<PERSON>a", "promotion_name": "Título da promoção", "promotion_description": "Texto da promoção", "blogpost_title": "<PERSON><PERSON><PERSON><PERSON>", "caption_button": "Botão"}}, "gift_cards": {"issued": {"title": "Aqui está seu cartão presente de {{ value }} para {{ shop }}!", "subtext": "Aqui está seu cartão presente!", "disabled": "Desabilitado", "expired": "Vencido em {{ expiry }}", "active": "Com vencimento em {{ expiry }}", "redeem": "Use este código no pagamento para resgatar seu cartão presente", "shop_link": "<PERSON><PERSON> <PERSON><PERSON> compras", "print": "Imprimir"}}, "home_page": {"recent_articles": "Artigos recentes"}, "cart": {"general": {"title": "<PERSON><PERSON><PERSON>pra<PERSON>", "products": "<PERSON><PERSON><PERSON>", "remove": "Remover", "price": "Preço", "quantity": "Quantidade", "total": "Total", "item": "<PERSON><PERSON>", "uploaded_file": "arquivo car<PERSON>", "continue_browsing_html": "<PERSON>ão há itens no seu carrinho.", "continue_shopping_link_html": "Continuar comprando →", "tax_and_shipping": "Tributos e frete calculados no checkout", "taxes_and_shipping_policy_at_checkout_html": "Tributos e <a href=\"{{ link }}\">frete</a> calculados no checkout", "taxes_included_but_shipping_at_checkout": "Tributos incluídos e frete calculado no checkout", "taxes_included_and_shipping_policy_html": "Tributos incluídos. <a href=\"{{ link }}\">Frete</a> calculado no checkout.", "orders_processed_in_currency_html": "Os pedidos serão processados em {{ currency }}.", "note": "Observação", "note_detailed": "Inclua quaisquer notas ou instruções especiais para o seu pedido aqui:", "agree_to_terms_html": "Concordo com os Termos e Condições", "view_terms": "[Ver termos]", "agree_to_terms_warning": "Você deve concordar com os termos e condições para checkout.", "or": "ou", "subtotal": "Total do carrinho", "update": "Atualizar subtotal", "checkout": "Concluir Compra"}, "shipping_calculator": {"title": "Calculadora de taxas de envio", "estimated_shipping": "previsão de entrega", "country": "<PERSON><PERSON>", "province": "Estado", "zip_code": "CEP", "heading": "C<PERSON>l<PERSON>lo de frete", "submit_button_label": "Calcular frete", "submit_button_label_disabled": "Calculando...", "no_shipping_destination": "Não enviamos para esse destino.", "at": "de", "available_rates": "Há uma taxa de envio disponível para", "additional_rates_part_1": "Há taxas de envio de", "additional_rates_part_2": "disponíveis para", "additional_rates_part_3": "a partir de", "multiple_rates": "Nós achamos {{ number_of_rates }} taxas de envio disponíveis para {{ address }}, começando às {{ rate }}.", "one_rate": "Encontramos uma taxa de envio disponível para {{ address }}.", "no_rates": "<PERSON><PERSON><PERSON><PERSON>, não enviamos para este destino.", "rate_value": "{{ rate_title }} de {{ rate }}", "is_not_valid": "não é válido", "is_not_blank": "não pode estar em branco", "is_not_supported": "não é suportado"}}, "layout": {"general": {"menu": "<PERSON><PERSON>", "cart": "<PERSON><PERSON><PERSON>", "edit_cart": "<PERSON><PERSON>", "go_to_cart": "Ir ao car<PERSON>ho", "checkout": "Concluir Compra", "continue_shopping": "Continuar comprando", "cart_note": "Observação", "empty_cart": "<PERSON><PERSON> carrinho está vazio", "subtotal": "Subtotal", "savings": "Descontos em produtos", "pick_a_currency": "Selecione uma moeda", "social": "Social", "close_window": "<PERSON><PERSON><PERSON>", "product_subtotal": "Total do produto (antes dos descontos)", "designer_credits_html": "<a href=\"http://outofthesandbox.com/\" target=\"_blank\" title=\"Flex Shopify Theme by Out of the Sandbox\"><PERSON><PERSON><PERSON> por Out of the Sandbox</a>."}, "social_sharing": {"title": "Compartilhar", "x_title": "Comp<PERSON><PERSON><PERSON> isso no X", "facebook_title": "Compartilhar no Facebook", "pinterest_title": "Compartilhar no Pinterest", "pinterest_share": "{{ title }} de {{ name }}", "email_title": "Enviar por e-mail a um amigo", "email_subject": "<PERSON><PERSON><PERSON> você possa gostar de {{ title }}", "email_message": "<PERSON><PERSON><PERSON>, eu estava pesquisando {{ name }} e encontrei {{ title }}. Quis compartilhar isso com você."}, "customer": {"logged_in_as": "Registrado como", "log_out": "<PERSON><PERSON>", "log_in": "Entrar", "my_account": "<PERSON><PERSON>", "create_account": "Criar uma conta"}, "counts": {"items": {"one": "item", "other": "itens"}, "items_with_count": {"one": "{{ count }} item", "other": "{{ count }} itens"}}}}