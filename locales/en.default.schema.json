{"sections": {"product": {"blocks": {"form": {"show_gift_card_recipient_form": {"label": "Show recipient information form for gift card products", "info": "Gift card products can optionally be sent direct to a recipient along with a personal message. [Learn more](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}, "complementary_products": {"name": "Complementary products", "settings": {"paragraph": {"content": "To select complementary products, add the Search & Discovery app. [Learn more](https://shopify.dev/themes/product-merchandising/recommendations)"}, "heading": {"label": "Heading"}, "product_recommendation_limit": {"label": "Maximum products to show"}, "products_per_slide": {"label": "Number of products per page"}}}}}, "age_gate": {"name": "Page age gate", "minimum_age": {"label": "Minimum age required to access page"}, "presets": {"name": "Page age gate"}}}, "settings_schema": {"age_gate": {"name": "Age gate", "enable_site_wide": {"label": "Enable site-wide"}, "minimum_age": {"label": "Minimum age required to access site"}, "heading": {"label": "Heading"}, "description": {"label": "Description"}, "show_logo": {"label": "Show logo", "info": "Visit [brand settings](/admin/settings/branding) to edit logo."}}}}