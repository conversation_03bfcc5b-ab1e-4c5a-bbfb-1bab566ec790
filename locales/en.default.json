{"product": {"variants": {"choose_variant": "Choose a variant", "choose_option": "Choose a {{ option }}", "chosen_option_html": "{{ option }}: <span>{{ value }}</span>"}, "price": {"range_html": "{{ price_min }} - {{ price_max }}", "unit_pricing_html": "{{ unit_quantity }} | {{ unit_price }} / {{ unit_measurement}}"}, "badge": {"sale_percentage_range_html": "Save up to {{ price_percent }}%", "sale_percentage_single_html": "Save {{ price_percent }}%", "sale_money_range_html": "Save up to {{ price }}", "sale_money_single_html": "Save {{ price }}", "sold_out": "Sold out", "sale": "Sale", "in_stock": "In stock"}}, "store_availability": {"general": {"in_stock": "In stock", "out_of_stock": "Out of stock", "view_store_info": "View store information", "check_other_stores": "Check availability at other stores", "available_for_pick_up_at_html": "Pickup available at <b>{{ location_name }}</b>", "unavailable_for_pick_up_at_html": "Pickup currently unavailable at <b>{{ location_name }}</b>", "available_for_pick_up_at_time_html": "Pickup available, {{ pick_up_time }}", "unavailable_for_pick_up_at_time_html": "Pickup currently unavailable", "kilometers": "km", "miles": "mi"}}, "structured_data": {"breadcrumbs": {"cart": "<PERSON><PERSON>", "collections": "Collections", "page": "Page {{ page }}", "products": "Products", "search": "Search", "tags_html": "Tagged \"{{ tags }}\""}}, "recipient": {"form": {"checkbox_label": "I want to send this as a gift", "email_label": "Recipient email", "email_placeholder": "Recipient email *", "error_message": "Email is not valid", "name_label": "Recipient name (optional)", "name_placeholder": "Recipient name (optional)", "message_label": "Message (optional)", "message_placeholder": "Message (optional)", "max_characters": "{{ max_characters }} characters max"}}, "sections": {"complementary_product_block": {"from": "from", "now": "now", "view_details": "View product", "price_per_unit_html": "{{ total_quantity }} | {{ unit_price }} / {{ unit_measure }}"}}, "age_gate": {"form": {"confirm": "Confirm age and proceed to {{ shop }}", "error": "Shop access is limited to adults age {{ age }} and older.", "day": "Day", "month": "Month", "year": "Year", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}}, "date_formats": {"month_day_year": "%B %d, %Y"}, "general": {"404": {"title": "Page Not Found", "subtext": "We're sorry, but the page you requested could not be found.", "continue_shopping_html": "Try searching or <a href=\"{{ continue_link }}\">continue shopping &rarr;</a>"}, "accessibility": {"star_review_text": "Review", "star_reviews_text": "Reviews", "star_reviews_info": "{{ rating_value }} out of {{ rating_max }} stars"}, "pagination": {"previous": "Previous", "next": "Next", "pagination_button": "Load more"}, "password_page": {"opening_soon": "Opening Soon", "spread_the_word": "Spread the word", "or": "or", "change_your_password_settings": "change your password settings", "login_form_heading": "Enter store using password", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_submit": "Enter", "signup_form_submit": "Submit", "signup_form_success": "We will send you an email right before we open!", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"text-link\">Log in here</a>", "password_link": "Enter using password", "powered_by_shopify_html": "This shop will be powered by <a href=\"https://shopify.pxf.io/pixel-union\" target=\"_blank\">Shopify</a>"}, "meta": {"tagged_html": "Tagged \"{{ tags }}\"", "page": "Page {{ page_number }}"}, "forms": {"post_error": "Not all the fields have been filled out correctly", "post_field_error_html": "The <strong>{{ field }}</strong> {{ error }}", "required_field": "Indicates a required field"}, "newsletter_form": {"name": "Name", "email": "Email", "subscriber_first_name": "First Name", "subscriber_last_name": "Last Name", "success_text": "Thank you for joining our mailing list!", "placeholder": "Email address", "submit": "Sign Up"}, "breadcrumbs": {"home": "Home", "page": "Page {{ current_page }} of {{ pages }}", "page_text": "Page", "of_text": "of"}, "search": {"title": "Search", "description": "Find what you seek", "no_results_html": "Check the spelling or use a different word or phrase.", "advanced_search_html": "Advanced Search &#8594;", "suggestions": "Suggestions", "no_results": "No results found.", "search_for": "Search for \"{{ search_terms }}\"", "products": "Products", "pages": "Pages", "posts": "Posts", "pages_and_posts": "Pages & Posts", "results_count": {"zero": "{{ count }} results for \"{{ search_terms }}\"", "one": "{{ count }} result for \"{{ search_terms }}\"", "other": "{{ count }} results for \"{{ search_terms }}\""}, "placeholder": "", "placeholder_with_shop_name": "Search {{ shop_name }}...", "submit": "Search", "common_terms": "Commonly searched"}, "country": {"dropdown_label": "Country"}, "language": {"dropdown_label": "Language"}}, "gallery": {"filter": {"all": "All"}}, "blogs": {"general": {"pagination_button": "Load more", "continue_reading_html": "Read More", "tagged": "tagged", "view_all": "All Categories"}, "sidebar": {"recent_articles": "Recent Articles", "categories": "Popular Topics", "follow_us": "Follow Us"}, "article": {"read_time": "min read", "posted_in_html": "{{ tags }}", "additional_articles": "Related Blog Posts", "author_on_date_html": "Posted on {{ date }} by {{ author }}", "by_author": "by {{ author }}", "by": "by", "previous_article_html": "Previous", "next_article_html": "Next", "tags": "Tags", "author": "Author", "comment_meta_html": "<strong>{{ author }}</strong> on <strong><time datetime=\"{{ date }}\">{{ date }}</time></strong>"}, "counts": {"comments_with_count": {"one": "{{ count }} Comment", "other": "{{ count }} Comments"}}, "comments": {"response_title": "Comments", "response_count": {"zero": "Responses", "one": "{{ count }} Response", "other": "{{ count }} Responses"}, "title": "Leave a comment (all fields required)", "name": "Name", "email": "Email", "comment": "Comment", "comment_placeholder": "What would you like to say?", "post": "Submit", "moderated": "Comments will be approved before showing up.", "success_moderated": "Your comment was posted successfully. It will be approved before showing up.", "success": "Your comment was posted successfully! Thank you!"}}, "collections": {"general": {"all": "All", "title": "Product Collections", "no_matches": "No products found in this collection", "link_title": "Browse {{ title }}", "all_collection_title": "All {{ title }}", "quick_shop": "Quick View", "pagination_button": "Load More Products", "new": "New", "coming_soon": "Coming Soon", "best_seller": "Best Seller", "staff_pick": "Staff Pick", "pre_order": "Pre-Order", "sale": "Sale", "sold_out": "Sold Out", "view_all": "View All", "view_product_details": "View full details"}, "sidebar": {"collections": "Collections", "product_types": "Types", "vendors": "Vend<PERSON>", "tags": "Tags", "clear": "Clear", "apply_filter": "Apply", "clear_all": "Clear all", "from": "From", "to": "To"}, "sorting": {"title": "Sort by", "sort_collection": "Sort {{ title }}", "filter": "Filter by", "filter_collection": "Filter {{ title }}", "featured": "Featured", "best_selling": "Best Selling", "az": "Alphabetically: A-Z", "za": "Alphabetically: Z-A", "price_ascending": "Price: Low to High", "price_descending": "Price: High to Low", "date_descending": "Date: New to Old", "date_ascending": "Date: Old to New"}}, "products": {"general": {"previous_product_html": "Previous", "next_product_html": "Next", "from": "from"}, "product": {"select_variant": "Please select all your options", "sold_out": "Sold Out", "unavailable": "Add to cart", "quantity": "Qty", "add_to_cart": "Add to cart", "add_to_cart_success": "Added", "vendor": "<PERSON><PERSON><PERSON>", "related_items": "Related Items", "collections": "Collections", "product_types": "Type", "tags": "Category", "savings": "You save:", "products_tagged": "Products tagged {{ tag }}", "items_left_count": {"one": "item left", "other": "items left"}, "view_in_your_space": "View in your space"}, "item": {"price": {"price_per_unit_html": "{{ total_quantity }} | {{ unit_price }} / {{ unit_measure }}"}}, "notify_form": {"email": "Email address", "send": "Send", "message_content": "Please notify me when the following product is back in stock: {{ product }} | {{ url }}", "email_content": "Please notify me when the following product is back in stock: ", "post_success": "Thanks! We will notify you when this product becomes available!", "post_error": "Please provide a valid email address."}}, "contact": {"form": {"name": "Name", "email": "Email", "phone": "Phone Number", "message": "Message", "send": "Submit", "post_success": "Thanks for contacting us. We'll get back to you as soon as possible.", "checkbox_validation": "Please make sure at least one checkbox is checked."}}, "customer": {"account": {"title": "My Account", "details": "Account Details", "view_addresses": "View Addresses", "return": "Return to Account Details", "primary_address": "Primary Address"}, "orders": {"title": "Order History", "order_number": "Order", "date": "Date", "payment_status": "Payment Status", "fulfillment_status": "Fulfillment Status", "total": "Total", "none": "You haven't placed any orders yet."}, "activate_account": {"title": "Activate Account", "subtext": "Create your password to activate your account.", "submit": "Activate Account", "cancel": "Decline Invitation", "password": "Password", "password_confirm": "Confirm Password", "or": "or"}, "addresses": {"no_addresses": "No addresses currently on file", "title": "Your Addresses", "default": "<PERSON><PERSON><PERSON>", "add_new": "Add a New Address", "edit_address": "Edit address", "company": "Company", "address1": "Address1", "address2": "Address2", "city": "City", "country": "Country", "province": "Province", "zip": "Postal/Zip Code", "phone": "Phone", "set_default": "Set as default address", "add": "Add Address", "update": "Update Address", "first_name": "First Name", "last_name": "Last Name", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "or": "or"}, "login": {"title": "Customer <PERSON><PERSON>", "returning_customers": "Returning Customers", "new_customers": "New Customers", "forgot_password": "Forgot your password?", "sign_up_html": "Sign up", "new_customer_label": "New Customer?", "sign_in": "<PERSON><PERSON>", "cancel": "Return to Store", "guest_title_html": "Continue as a Guest &rarr;", "first_name": "First name", "last_name": "Last name", "email": "Email address", "password": "Password", "guest_continue": "Continue", "or": "or"}, "recover_password": {"title": "Reset Password", "subtext": "We will send you an email to reset your password.", "success": "We've sent you an email with a link to update your password.", "email": "Email address", "submit": "Submit", "cancel": "Cancel", "or": "or"}, "reset_password": {"title": "Reset account password", "subtext": "Enter a new password for {{ email }}", "submit": "Reset Password", "password": "Password", "password_confirm": "Confirm Password"}, "order": {"title": "Order {{ name }}", "date": "Placed on {{ date }}", "note": "Order notes", "note_placeholder": "No order notes were provided.", "cancelled": "Order Cancelled on {{ date }}", "cancelled_reason": "Reason: {{ reason }}", "billing_address": "Billing Address", "payment_status": "Payment Status", "shipping_address": "Shipping Address", "fulfillment_status": "Fulfillment Status", "discount": "Discount", "shipping": "Shipping", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "fulfilled_at": "Fulfilled {{ date }}", "subtotal": "Subtotal"}, "register": {"title": "Create Account", "sign_up": "Sign Up", "cancel": "Return to Store", "returning_customer_label": "Returning Customer?", "sign_in_html": "<PERSON><PERSON>", "first_name": "First name", "last_name": "Last name", "email": "Email address", "password": "Password", "or": "or"}}, "homepage": {"onboarding": {"product_title": "Your product's name", "product_description": "This area is used to describe your product’s details. Tell customers about the look, feel, and style of your product. Add details on color, materials used, sizing, and where it was made.", "caption_headline": "Your headline", "caption_subtitle": "Subtitle", "caption_button": "Linked button", "menu_title": "Menu title", "promotion_name": "Your promotion headline", "promotion_description": "Promotion description appears here.", "collection_title": "Collection title", "blog_title": "News", "blogpost_title": "Your post's title", "blog_excerpt": "Your store hasn’t published any blog posts yet. A blog can be used to talk about new product launches, tips, or other news you want to share with your customers. You can check out Shopify’s ecommerce blog for inspiration and advice for your own store and blog.", "blog_author": "Author name", "no_content": "This section doesn’t currently include any content. Add content to this section using the sidebar."}}, "gift_cards": {"issued": {"title": "Here's your {{ value }} gift card for {{ shop }}!", "subtext": "Here's your gift card!", "disabled": "Disabled", "expired": "Expired on {{ expiry }}", "active": "Expires on {{ expiry }}", "redeem": "Use this code at checkout to redeem your gift card", "shop_link": "Start shopping", "print": "Print"}}, "home_page": {"recent_articles": "Recent Articles"}, "cart": {"general": {"title": "Shopping Cart", "products": "Products", "remove": "Remove", "price": "Price", "quantity": "Quantity", "total": "Total", "item": "<PERSON><PERSON>", "uploaded_file": "uploaded file", "continue_browsing_html": "There are no items in your cart.", "continue_shopping_link_html": "Continue Shopping", "tax_and_shipping": "Taxes and shipping calculated at checkout", "taxes_and_shipping_policy_at_checkout_html": "Taxes and <a href=\"{{ link }}\">shipping</a> calculated at checkout", "taxes_included_but_shipping_at_checkout": "Tax included and shipping calculated at checkout", "taxes_included_and_shipping_policy_html": "Tax included. <a href=\"{{ link }}\">Shipping</a> calculated at checkout.", "orders_processed_in_currency_html": "Orders will be processed in {{ currency }}.", "note": "Order Notes", "note_detailed": "Include any notes or special instructions for your order here:", "agree_to_terms_html": "I Agree with the Terms & Conditions", "view_terms": "[View Terms]", "agree_to_terms_warning": "You must agree with the terms and conditions to checkout.", "or": "or", "subtotal": "Cart Total", "update": "Update Subtotal", "checkout": "Checkout"}, "shipping_calculator": {"title": "Shipping rates calculator", "estimated_shipping": "Estimated cost", "country": "Country", "province": "State / Province", "zip_code": "Zip / Postal Code", "is_not_valid": "is not valid", "is_not_blank": "can't be blank", "is_not_supported": "is not supported", "heading": "Shipping rates calculator", "submit_button_label": "Calculate", "submit_button_label_disabled": "Calculating...", "no_shipping_destination": "We do not ship to this destination.", "at": "at", "available_rates": "There is one shipping rate available for", "additional_rates_part_1": "There are", "additional_rates_part_2": "shipping rates available for", "additional_rates_part_3": "starting at", "multiple_rates": "We found {{ number_of_rates }} shipping rates available for {{ address }}, starting at {{ rate }}.", "one_rate": "We found one shipping rate available for {{ address }}.", "no_rates": "Sorry, we do not ship to this destination.", "rate_value": "{{ rate_title }} at {{ rate }}"}}, "layout": {"general": {"menu": "<PERSON><PERSON>", "cart": "<PERSON><PERSON>", "edit_cart": "<PERSON>", "go_to_cart": "Go to cart", "checkout": "Checkout", "continue_shopping": "Continue", "empty_cart": "Your Cart is Empty", "cart_note": "Order Notes", "subtotal": "Cart Total", "savings": "Product Discounts", "pick_a_currency": "Pick a currency", "social": "Follow", "close_window": "Close", "product_subtotal": "Product Total (before discounts)", "designer_credits_html": "<a href=\"https://outofthesandbox.com/\" target=\"_blank\" title=\"Flex Shopify Theme by Out of the Sandbox\">Designed by Out of the Sandbox</a>."}, "social_sharing": {"title": "Share", "x_title": "Share this on X", "facebook_title": "Share this on Facebook", "pinterest_title": "Share this on Pinterest", "pinterest_share": "{{ title }} from {{ name }}", "email_title": "Email this to a friend", "email_subject": "Thought you might like {{ title }}", "email_message": "Hey, I was browsing {{ name }} and found {{ title }}. I wanted to share it with you."}, "customer": {"logged_in_as": "Logged in as", "log_out": "Logout", "log_in": "<PERSON><PERSON>", "my_account": "Account", "create_account": "Create an account"}, "counts": {"items": {"one": "item", "other": "items"}, "items_with_count": {"one": "{{ count }} item", "other": "{{ count }} items"}}}}