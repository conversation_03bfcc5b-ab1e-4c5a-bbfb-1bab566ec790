{"store_availability": {"general": {"in_stock": "Em estoque", "out_of_stock": "Fora de estoque", "view_store_info": "Ver informações da loja", "check_other_stores": "Verifique a disponibilidade em outras lojas", "available_for_pick_up_at_html": "Recol<PERSON> disponível em <b>{{ location_name }}</b>", "unavailable_for_pick_up_at_html": "Recolha atualmente indisponível em <b>{{ location_name }}</b>", "available_for_pick_up_at_time_html": "<PERSON><PERSON><PERSON><PERSON> di<PERSON><PERSON>, {{ pick_up_time }}", "unavailable_for_pick_up_at_time_html": "Coleta atualmente indisponível", "kilometers": "km", "miles": "mi"}}, "structured_data": {"breadcrumbs": {"cart": "<PERSON><PERSON><PERSON>", "collections": "Coleções", "page": "<PERSON><PERSON><PERSON><PERSON> {{ page }}", "products": "<PERSON><PERSON><PERSON>", "search": "Busca", "tags_html": "<PERSON><PERSON> \"{{ tags }}\""}}, "date_formats": {"month_day_year": "%B %d, %Y"}, "general": {"404": {"title": "A Página Não Foi Encontrada", "subtext": "<PERSON><PERSON><PERSON><PERSON>, mas a página que solicitou não existe.", "continue_shopping_html": "<PERSON>te pesquisar ou <a href=\"{{ continue_link }}\">continuar a comprar →</a>"}, "accessibility": {"star_review_text": "<PERSON><PERSON><PERSON><PERSON>", "star_reviews_text": "Avaliações", "star_reviews_info": "{{ rating_value }} fora de {{ rating_max }} estrelas"}, "pagination": {"previous": "Anterior", "next": "Próximo", "pagination_button": "<PERSON><PERSON><PERSON>"}, "password_page": {"opening_soon": "Abre Brevemente", "spread_the_word": "Passe a palavra", "or": "ou", "change_your_password_settings": "alterar as configurações de sua senha", "login_form_heading": "Entre na loja usando a palavra-passe", "login_form_password_label": "Palavra-passe", "login_form_password_placeholder": "A sua palavra-passe", "login_form_submit": "Entrar", "signup_form_submit": "Submeter", "signup_form_success": "Iremos enviar-lhe um e-mail para a direita antes de abrirmos!", "admin_link_html": "É o dono da loja? <a href=\"/admin\" class=\"text-link\">Inicie sessão aqui</a>", "password_link": "Entre usando a palavra-passe", "powered_by_shopify_html": "Esta loja será movida <a href=\"https://shopify.pxf.io/pixel-union\" target=\"_blank\">Shopify</a>"}, "meta": {"tagged_html": "<PERSON><PERSON> \"{{ tags }}\"", "page": "<PERSON><PERSON><PERSON><PERSON> {{ page_number }}"}, "forms": {"post_error": "Nem todos os campos foram correctamente preenchidos", "post_field_error_html": "O <strong>{{ field }}</strong> {{ error }}", "required_field": "Indica um campo obrigatório"}, "newsletter_form": {"name": "Nome", "email": "E-mail", "subscriber_first_name": "Primeiro Nome", "subscriber_last_name": "<PERSON>lt<PERSON>", "success_text": "<PERSON><PERSON><PERSON> por participar da nossa lista de e-mails!", "placeholder": "Introduza o seu endereço de e-mail", "submit": "Inscreva-se"}, "breadcrumbs": {"home": "Início", "page": "Página {{ current_page }} de {{ pages }}", "page_text": "<PERSON><PERSON><PERSON><PERSON>", "of_text": "de"}, "search": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pesquisar tudo na loja.", "no_results_html": "Verifique a ortografia ou use uma palavra ou frase diferente.", "advanced_search_html": "Pesquisa Avançada →", "suggestions": "Sugestões", "no_results": "<PERSON>ão foram encontrados resultados.", "search_for": "Pesquise \"{{ search_terms }}\"", "products": "<PERSON><PERSON><PERSON>", "pages": "<PERSON><PERSON><PERSON><PERSON>", "posts": "Publicações", "pages_and_posts": "Páginas e publicações", "results_count": {"zero": "{{ count }} resultados para \"{{ search_terms }}\"", "one": "{{ count }} resultado para \"{{ search_terms }}\"", "other": "{{ count }} resultados para \"{{ search_terms }}\""}, "placeholder": "", "placeholder_with_shop_name": "Pesquisar {{ shop_name }}...", "submit": "Pesquisa", "common_terms": "Comumente pesquisado"}, "language": {"dropdown_label": "Idioma"}, "country": {"dropdown_label": "<PERSON><PERSON>"}}, "gallery": {"filter": {"all": "<PERSON><PERSON>"}}, "blogs": {"general": {"pagination_button": "<PERSON><PERSON><PERSON>", "continue_reading_html": "Visualizar o artigo completo", "tagged": "marcado", "view_all": "<PERSON><PERSON> As Categorias"}, "sidebar": {"recent_articles": "<PERSON><PERSON><PERSON>", "categories": "Categorias", "follow_us": "Siga-Nos"}, "article": {"read_time": "min ler", "posted_in_html": "{{ tags }}", "additional_articles": "Visualizar o artigo completo", "author_on_date_html": "Publicado em {{ date }} por {{ author }}", "by_author": "por {{ author }}", "by": "por", "previous_article_html": "Anterior", "next_article_html": "Se<PERSON><PERSON>", "tags": "Etiquetas", "author": "Autor", "comment_meta_html": "<strong>{{ author }}</strong> em <strong><time datetime=\"{{ date }}\">{{ date }}</time></strong>"}, "counts": {"comments_with_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "comments": {"response_title": "Comentários", "response_count": {"zero": "Respostas", "one": "{{ count }} Re<PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}, "title": "Deixe um comentário", "name": "Nome", "email": "E-mail", "comment": "<PERSON><PERSON><PERSON><PERSON>", "comment_placeholder": "O que você gostaria de dizer?", "post": "Publicar um comentário", "moderated": "Os comentários serão aprovados antes de serem apresentados.", "success_moderated": "O seu comentário foi publicado com êxito. Será aprovado antes de ser apresentado.", "success": "O seu comentário foi publicado com êxito! Obrigado!"}}, "collections": {"general": {"all": "<PERSON><PERSON>", "title": "Colecções de Produtos", "no_matches": "Não foram encontrados produtos nesta colecção", "link_title": "Navegar por {{ title }}", "all_collection_title": "<PERSON><PERSON> em {{ title }}", "quick_shop": "+ Comp<PERSON>", "pagination_button": "<PERSON><PERSON><PERSON>", "new": "Novo", "coming_soon": "Em Breve", "best_seller": "<PERSON><PERSON>", "staff_pick": "Escolha <PERSON>", "pre_order": "Para Encomendar", "sale": "Saldos", "view_all": "<PERSON><PERSON>", "view_product_details": "Ver informação pormenorizada do produto", "sold_out": "Vendido"}, "sidebar": {"collections": "Colecçõ<PERSON>", "product_types": "Tipos", "vendors": "Vendedores", "tags": "Etiquetas", "clear": "<PERSON><PERSON><PERSON>", "apply_filter": "Aplicar", "clear_all": "<PERSON><PERSON> tudo", "from": "A partir de", "to": "Para"}, "sorting": {"title": "Ordenar por", "sort_collection": "Ordenar {{ title }}", "filter": "Filtrar por", "filter_collection": "Filtrar {{ title }}", "featured": "Destacado(a)", "best_selling": "<PERSON><PERSON>(a)", "az": "Por ordem alfabética: A-Z", "za": "Por ordem alfabética: Z-A", "price_ascending": "Preço: Do Mais Baixo para o Mais Alto", "price_descending": "Preço: Do Mais Alto para o Mais Baixo", "date_descending": "Data: <PERSON> para o Mais Antigo", "date_ascending": "Data: <PERSON> para o Mais Recente"}}, "products": {"general": {"previous_product_html": "Anterior", "next_product_html": "Se<PERSON><PERSON>", "from": "de"}, "product": {"sold_out": "Esgotado", "unavailable": "Não Disponível", "quantity": "Quantidade", "add_to_cart": "Adicionar ao car<PERSON>ho", "add_to_cart_success": "<PERSON><PERSON><PERSON><PERSON>", "vendor": "<PERSON><PERSON><PERSON>", "related_items": "Itens Relacionados", "collections": "Colecçõ<PERSON>", "product_types": "Tipo", "tags": "Categoria", "savings": "Você economiza", "products_tagged": "Produtos marcados {{ tag }}", "items_left_count": {"one": "item descartado", "other": "itens descartados"}, "view_in_your_space": "Ver no seu espaço", "select_variant": "Por favor, selecione todas as suas opções"}, "notify_form": {"email": "Introduza o seu endereço de e-mail...", "send": "Enviar", "message_content": "Notifique-me quando o seguinte produto estiver disponível novamente: {{ product }} | {{ url }}", "email_content": "Notifique-me quando o seguinte produto estiver disponível novamente: ", "post_success": "Obrigado! Avisá-lo-emos quando o produto já estiver disponível!", "post_error": "Por favor, dê-nos um endereço de e-mail válido."}, "item": {"price": {"price_per_unit_html": "{{total_quantity}} | {{unit_price}} / {{unit_measure}}"}}}, "contact": {"form": {"name": "Nome", "email": "E-mail", "phone": "Número de Telefone", "message": "Mensagem", "send": "Enviar", "post_success": "Obrigada por nos ter contactado. Entraremos em contacto consigo tão breve quanto possível.", "checkbox_validation": "Por favor, verifique se pelo menos uma caixa de seleção está marcada."}}, "customer": {"account": {"title": "A Minha Conta", "details": "<PERSON><PERSON><PERSON>", "view_addresses": "<PERSON><PERSON>", "return": "Voltar aos Det<PERSON> da Conta", "primary_address": "Endereço primário"}, "orders": {"title": "Históric<PERSON> de Encomendas", "order_number": "Encomenda", "date": "Data", "payment_status": "Situação do Pagamento", "fulfillment_status": "Situação do Cumprimento", "total": "Total", "none": "Ainda não fez nenhuma encomenda."}, "activate_account": {"title": "Activar a Conta", "subtext": "Crie a sua palavra-passe, para activar a sua conta.", "submit": "Activar a Conta", "cancel": "Recusar o Convite", "password": "Palavra-passe", "password_confirm": "Confirmar <PERSON>e", "or": "ou"}, "addresses": {"no_addresses": "Nenhum endereço", "title": "As suas moradas", "default": "Predefinido(a)(os)(as)", "add_new": "Adicionar uma Nova Morada", "edit_address": "Editar a morada", "company": "Empresa", "address1": "Morada1", "address2": "Morada2", "city": "Cidade", "country": "<PERSON><PERSON>", "province": "<PERSON>v<PERSON><PERSON>", "zip": "Código Postal/Zip", "phone": "Telefone", "set_default": "Definir como morada predefinida", "add": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "Primeiro Nome", "last_name": "<PERSON>lt<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "or": "ou"}, "login": {"title": "Acesso de Cliente", "returning_customers": "Clientes Recorrentes", "new_customers": "Novos Clientes", "forgot_password": "Esque<PERSON>u-se da sua palavra-passe?", "sign_up_html": "Inscreva-se", "new_customer_label": "Novo Cliente ?", "sign_in": "<PERSON><PERSON><PERSON>", "cancel": "Voltar à Loja", "guest_title_html": "Continuar como Convidado →", "first_name": "Primeiro Nome", "last_name": "<PERSON>lt<PERSON>", "email": "E-mail", "password": "Palavra-passe", "guest_continue": "<PERSON><PERSON><PERSON><PERSON>", "or": "ou"}, "recover_password": {"title": "Recuperar a Palavra-passe", "subtext": "Vamos enviar-lhe um e-mail, para recuperar a sua palavra-passe.", "success": "Enviámos-lhe um e-mail com um link, para actualizar a sua palavra-passe.", "email": "E-mail", "submit": "Submeter", "cancel": "<PERSON><PERSON><PERSON>", "or": "ou"}, "reset_password": {"title": "Recuperar a Palavra-passe da conta", "subtext": "Introduza uma nova palavra-passe para {{ email }}", "submit": "Recuperar a Palavra-passe", "password": "Palavra-passe", "password_confirm": "Confirmar a Palavra-passe"}, "order": {"title": "Encomenda {{ name }}", "date": "<PERSON><PERSON> em {{ date }}", "note": "Notas de pedidos", "note_placeholder": "Nenhuma nota de ordem foi fornecida.", "cancelled": "Encomenda Cancelada em {{ date }}", "cancelled_reason": "Motivo: {{ reason }}", "billing_address": "Morada para Facturação", "payment_status": "Situação do Pagamento", "shipping_address": "Morada para Envio", "fulfillment_status": "Situação de Cumprimento", "discount": "Desconto", "shipping": "<PERSON><PERSON>", "tax": "Imposto", "product": "Produ<PERSON>", "sku": "SKU (Unidade de Manutenção de Stock)", "price": "Preço", "quantity": "Quantidade", "total": "Total", "fulfilled_at": "Realizado {{ date }}", "subtotal": "Subtotal"}, "register": {"title": "<PERSON><PERSON><PERSON>", "sign_up": "Inscrever", "cancel": "Voltar à Loja", "returning_customer_label": "Cliente Recorrente?", "sign_in_html": "<PERSON><PERSON><PERSON>", "first_name": "Primeiro Nome", "last_name": "<PERSON>lt<PERSON>", "email": "E-mail", "password": "Palavra-passe", "or": "ou"}}, "homepage": {"onboarding": {"product_title": "Nome do seu produto", "product_description": "Essa área é usada para descrever os detalhes do seu produto. Diga aos clientes sobre a aparência, toque e estilo de seu produto. Adicione detalhes como cor, materiais usados, tamanho e onde foi fabricado.", "collection_title": "Nome de sua coleção", "blog_title": "Título de sua postagem", "blog_excerpt": "Sua loja ainda não publicou nenhuma postagem de blog. Um blog pode ser usado para falar sobre o lançamento de novos produtos, dicas e outras novidades que queira compartilhar com os clientes. Você também pode ver nosso blog de e-commerce do Shopify para inspiração e conselhos para sua própria loja e blog.", "blog_author": "Nome do autor", "no_content": "Atualmente, esta seção não inclui nenhum conteúdo. Adicione conteúdo nesta seção usando a barra lateral.", "menu_title": "Título do menu", "caption_headline": "<PERSON><PERSON><PERSON><PERSON>", "caption_subtitle": "<PERSON>a", "promotion_name": "Título da promoção", "promotion_description": "Texto da promoção", "blogpost_title": "<PERSON><PERSON><PERSON><PERSON>", "caption_button": "Botão"}}, "gift_cards": {"issued": {"title": "Aqui está o seu cartão-oferta de {{ value }} para {{ shop }}!", "subtext": "Aqui está o seu cartão-oferta!", "disabled": "Desactivado", "expired": "<PERSON><PERSON><PERSON><PERSON> em {{ expiry }}", "active": "Caduca em {{ expiry }}", "redeem": "Utilize este código quando finalizar a compra, para descontar o seu cartão oferta", "shop_link": "<PERSON><PERSON><PERSON> a Comprar", "print": "Imprimir"}}, "home_page": {"recent_articles": "Artigos recentes"}, "cart": {"general": {"title": "Carrinho de Compras", "products": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON>", "price": "Preço", "quantity": "Quantidade", "total": "Total", "item": "<PERSON><PERSON>", "uploaded_file": "ficheiro transferido", "continue_browsing_html": "Não existe qualquer item no seu carrinho.", "continue_shopping_link_html": "Con<PERSON><PERSON><PERSON> a Comprar →", "tax_and_shipping": "Tributos e frete calculados no checkout", "taxes_and_shipping_policy_at_checkout_html": "Tributos e <a href=\"{{ link }}\">frete</a> calculados no checkout", "taxes_included_but_shipping_at_checkout": "Tributos incluídos e frete calculado no checkout", "taxes_included_and_shipping_policy_html": "Tributos incluídos. <a href=\"{{ link }}\">Frete</a> calculado no checkout.", "orders_processed_in_currency_html": "As encomendas serão processadas em {{ currency }}.", "note": "<PERSON>a", "note_detailed": "Inclua quaisquer notas ou instruções especiais para o seu pedido aqui:", "agree_to_terms_html": "Concordo com os Termos e Condições", "view_terms": "[Ver termos]", "agree_to_terms_warning": "Você deve concordar com os termos e condições para checkout.", "or": "ou", "subtotal": "Total do carrinho", "update": "Actualizar o Subtotal", "checkout": "Finalizar a Compra"}, "shipping_calculator": {"title": "Calculadora de taxas de envio", "estimated_shipping": "custo de envio estimado", "country": "<PERSON><PERSON>", "province": "<PERSON>v<PERSON><PERSON>", "zip_code": "Código Postal/Zip", "heading": "Calculadora de custos de envio ", "submit_button_label": "Calcular custos de envio", "submit_button_label_disabled": "A calcular...", "no_shipping_destination": "Não fazemos envios para este destino.", "at": "de", "available_rates": "Há um custo de envio disponível para", "additional_rates_part_1": "Há", "additional_rates_part_2": "custos de envio disponíveis para", "additional_rates_part_3": "a partir de", "multiple_rates": "Nós achamos {{ number_of_rates }} taxas de envio disponíveis para {{ address }}, começando às {{ rate }}.", "one_rate": "Encontramos uma taxa de envio disponível para {{ address }}.", "no_rates": "<PERSON><PERSON><PERSON><PERSON>, não enviamos para este destino.", "rate_value": "{{ rate_title }} de {{ rate }}", "is_not_valid": "não é válido", "is_not_blank": "não pode estar em branco", "is_not_supported": "não é suportado"}}, "layout": {"general": {"menu": "<PERSON><PERSON>", "cart": "<PERSON><PERSON><PERSON>", "edit_cart": "<PERSON><PERSON>", "go_to_cart": "<PERSON><PERSON> <PERSON><PERSON>", "checkout": "Finalizar Compra", "continue_shopping": "Con<PERSON><PERSON><PERSON> a Comprar", "empty_cart": "O Seu Carrinho está Vazio", "cart_note": "<PERSON>a", "subtotal": "Subtotal", "savings": "Descontos em produtos", "pick_a_currency": "Escolha uma moeda", "social": "Social", "close_window": "<PERSON><PERSON><PERSON>", "product_subtotal": "Total do produto (antes dos descontos)", "designer_credits_html": "<a href=\"http://outofthesandbox.com/\" target=\"_blank\" title=\"Flex Shopify Theme by Out of the Sandbox\">Concebido por Out of the Sandbox</a>."}, "social_sharing": {"title": "Partilhar", "x_title": "Comp<PERSON><PERSON><PERSON> isso no X", "facebook_title": "<PERSON><PERSON><PERSON> isto no Facebook", "pinterest_title": "<PERSON><PERSON>he isto no Pinterest", "pinterest_share": "{{ title }} from {{ name }}", "email_title": "<PERSON><PERSON> isto, por e-mail, a um amigo", "email_subject": "Achei que ias gostar de {{ title }}", "email_message": "Olá! Estava a navegar pela {{ name }} e encontrei {{ title }}. Quero partilhar contigo."}, "customer": {"logged_in_as": "Sessão iniciada como", "log_out": "<PERSON><PERSON><PERSON><PERSON>", "log_in": "<PERSON><PERSON><PERSON>", "my_account": "A Minha Conta", "create_account": "<PERSON><PERSON><PERSON> um<PERSON>"}, "counts": {"items": {"one": "item", "other": "itens"}, "items_with_count": {"one": "{{ count }} item", "other": "{{ count }} itens"}}}}