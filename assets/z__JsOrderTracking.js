(function () {
  "use strict";

  // Configuration - Update these with your actual API details
  const API_CONFIG = {
    // Replace with your actual third-party API endpoint
    endpoint: "https://d50c-113-192-46-158.ngrok-free.app/order-tracking",
    // Add any required headers or authentication
    headers: {
      "Content-Type": "application/json",
      Authorization:
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJjdXN0b21lci1hY2Nlc3MiLCJpc3MiOiJjb3JzLWJhY2tlbmQiLCJzdWIiOiJjdXN0b21lciIsImlhdCI6MTc1MTU0MDYzMiwiZXhwIjoxNzgzMDc2NjMyfQ.O/ptJy6ylYtXPTsADaCyR8+C01WQ0nONwgFfofJQZc4", // Uncomment and add your API key if needed
    },
  };

  let form,
    orderNumberInput,
    customerEmailInput,
    successMessage,
    errorMessage,
    resultsContainer,
    trackButton;

  function init() {
    form = document.getElementById("order-tracking-form");
    orderNumberInput = document.getElementById("order-number");
    customerEmailInput = document.getElementById("customer-email");
    successMessage = document.getElementById("tracking-success");
    errorMessage = document.getElementById("tracking-error");
    resultsContainer = document.getElementById("order-status-results");
    trackButton = document.getElementById("track-order-btn");

    if (!form) {
      console.warn("Order tracking form not found");
      return;
    }
    form.addEventListener("submit", handleFormSubmit);
    orderNumberInput.addEventListener("input", validateOrderNumber);
    customerEmailInput.addEventListener("input", validateEmail);
  }

  function handleFormSubmit(event) {
    event.preventDefault();
    hideMessages();
    const orderNumber = orderNumberInput.value.trim();
    const customerEmail = customerEmailInput.value.trim();
    if (!validateForm(orderNumber, customerEmail)) {
      return;
    }
    setLoadingState(true);
    trackOrder(orderNumber, customerEmail);
  }

  function validateForm(orderNumber, customerEmail) {
    let isValid = true;
    let errorMsg = "";

    if (!orderNumber) {
      errorMsg += "Order number is required. ";
      isValid = false;
    }

    if (!customerEmail) {
      errorMsg += "Email address is required. ";
      isValid = false;
    } else if (!isValidEmail(customerEmail)) {
      errorMsg += "Please enter a valid email address. ";
      isValid = false;
    }

    if (!isValid) {
      showError(errorMsg);
    }

    return isValid;
  }

  function validateOrderNumber() {
    const orderNumber = orderNumberInput.value.trim();
    if (orderNumber && orderNumber.length < 3) {
      orderNumberInput.setCustomValidity(
        "Order number must be at least 3 characters long"
      );
    } else {
      orderNumberInput.setCustomValidity("");
    }
  }

  function validateEmail() {
    const email = customerEmailInput.value.trim();

    if (email && !isValidEmail(email)) {
      customerEmailInput.setCustomValidity(
        "Please enter a valid email address"
      );
    } else {
      customerEmailInput.setCustomValidity("");
    }
  }

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Make API call to track order
  async function trackOrder(orderNumber, customerEmail) {
    try {
      const requestData = {
        orderNumber: orderNumber,
        email: customerEmail,
      };

      const response = await fetch(API_CONFIG.endpoint, {
        method: "POST",
        headers: API_CONFIG.headers,
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Handle successful response
      handleTrackingSuccess(data);
    } catch (error) {
      console.error("Error tracking order:", error);
      handleTrackingError(error);
    } finally {
      setLoadingState(false);
    }
  }

  // Handle successful tracking response
  function handleTrackingSuccess(data) {
    hideMessages();
    showSuccess(
      "Order found! Here are the details click on Link to view full order status:"
    );
    displayOrderStatus(data);
  }

  // Handle tracking error
  function handleTrackingError(error) {
    let errorMsg = "Unable to track your order. ";

    if (error.message.includes("404")) {
      errorMsg =
        "Order not found. Please check your order number and email address.";
    } else if (error.message.includes("500")) {
      errorMsg = "Server error. Please try again later.";
    } else if (error.name === "TypeError" && error.message.includes("fetch")) {
      errorMsg =
        "Network error. Please check your internet connection and try again.";
    } else {
      errorMsg += "Please try again or contact customer support.";
    }

    showError(errorMsg);
  }

  // Display order status information
  function displayOrderStatus(orderData) {
    const statusContent = resultsContainer.querySelector(
      ".order-status-content"
    );

    let html = "<h3>📦 Order Details</h3>";

    // Order basic information section
    html += '<div class="order-section">';

    if (orderData.shopifyOrderNumber) {
      html += `<div class="status-item">
        <span class="status-label">Order Number</span>
        <span class="status-value">#${orderData.shopifyOrderNumber}</span>
      </div>`;
    }

    if (orderData.orderStatus) {
      html += `<div class="status-item">
        <span class="status-label">Order Status</span>
        <span class="status-value status-${orderData.orderStatus.toLowerCase()}">${
        orderData.orderStatus
      }</span>
      </div>`;
    }

    if (orderData.orderDate) {
      const orderDate = new Date(orderData.orderDate).toLocaleDateString(
        "en-US",
        {
          year: "numeric",
          month: "long",
          day: "numeric",
        }
      );
      html += `<div class="status-item">
        <span class="status-label">Order Date</span>
        <span class="status-value">${orderDate}</span>
      </div>`;
    }

    if (orderData.statusUpdatedAt) {
      const lastUpdate = new Date(orderData.statusUpdatedAt).toLocaleString(
        "en-US",
        {
          year: "numeric",
          month: "short",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
        }
      );
      html += `<div class="status-item">
        <span class="status-label">Last Updated</span>
        <span class="status-value">${lastUpdate}</span>
      </div>`;
    }

    if (orderData.itemCount) {
      html += `<div class="status-item">
        <span class="status-label">Total Items</span>
        <span class="status-value">${orderData.itemCount} item(s)</span>
      </div>`;
    }

    // Payment information
    if (orderData.paymentInformation) {
      const payment = orderData.paymentInformation;
      html += `<div class="status-item">
        <span class="status-label">Order Total</span>
        <span class="status-value"><strong>$${payment.total_price}</strong></span>
      </div>`;
    }

    html += "</div>"; // Close order-section

    // Customer information section
    if (orderData.customerFirstName || orderData.customerEmail) {
      html += "<h4>👤 Customer Information</h4>";
      html += '<div class="order-section">';

      if (orderData.customerFirstName && orderData.customerLastName) {
        html += `<div class="status-item">
          <span class="status-label">Customer Name</span>
          <span class="status-value">${orderData.customerFirstName} ${orderData.customerLastName}</span>
        </div>`;
      }

      if (orderData.customerEmail) {
        html += `<div class="status-item">
          <span class="status-label">Email Address</span>
          <span class="status-value">${orderData.customerEmail}</span>
        </div>`;
      }

      html += "</div>"; // Close customer section
    }

    // Order Status URL - prominently displayed
    if (orderData.orderStatusUrl) {
      html += `<div class="status-item status-url-item">
        <span class="status-label">🔗 Full Order Tracking</span>
        <div class="status-url-container">
          <a href="${orderData.orderStatusUrl}" target="_blank" class="status-url-link">
            <span>🔍</span> View Complete Status
          </a>
          <button type="button" class="copy-url-btn" onclick="copyToClipboard('${orderData.orderStatusUrl}')">
            📋 Copy Link
          </button>
        </div>
      </div>`;
    }

    // Line items section
    if (orderData.lineItems && orderData.lineItems.length > 0) {
      html += "<h4>📋 Order Items</h4>";
      orderData.lineItems.forEach((item, index) => {
        html += `<div class="status-item line-item">
          <div class="line-item-header">
            <span class="status-label">Item ${index + 1}</span>
            <span class="item-number">#${item.itemNumber}</span>
          </div>
          <div class="line-item-details">
            <div><strong>Quantity:</strong> ${item.quantity}</div>
            <div><strong>Status:</strong> <span class="item-status">${
              item.status
            }</span></div>
            ${
              item.priority
                ? `<div><strong>Priority:</strong> <span class="item-priority">${item.priority}</span></div>`
                : ""
            }
          </div>
        </div>`;
      });
    }

    statusContent.innerHTML = html;
    resultsContainer.style.display = "block";

    // Smooth scroll to results
    resultsContainer.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  }

  // Show success message
  function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = "block";
    errorMessage.style.display = "none";
  }

  // Show error message
  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = "block";
    successMessage.style.display = "none";
    resultsContainer.style.display = "none";
  }

  // Hide all messages
  function hideMessages() {
    successMessage.style.display = "none";
    errorMessage.style.display = "none";
  }

  // Set loading state
  function setLoadingState(isLoading) {
    if (isLoading) {
      trackButton.textContent = "Tracking...";
      trackButton.disabled = true;
      form.style.opacity = "0.7";
    } else {
      trackButton.textContent = "Track Order";
      trackButton.disabled = false;
      form.style.opacity = "1";
    }
  }

  // Helper function to copy URL to clipboard
  window.copyToClipboard = function (text) {
    navigator.clipboard
      .writeText(text)
      .then(function () {
        // Show temporary success message
        const btn = event.target;
        const originalText = btn.textContent;
        btn.textContent = "Copied!";
        btn.style.backgroundColor = "#28a745";
        setTimeout(() => {
          btn.textContent = originalText;
          btn.style.backgroundColor = "";
        }, 2000);
      })
      .catch(function (err) {
        console.error("Could not copy text: ", err);
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
      });
  };

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }
})();
