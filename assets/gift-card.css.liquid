/*============================================================================
  Table of Contents

  #General Variables
  #Sass Mixins
  #Base Styles
  #Media Queries
  #Print Styles
  #Keyframe Animations
==============================================================================*/
/*============================================================================
  #General Variables
==============================================================================*/
/*============================================================================
  #Sass Mixins
==============================================================================*/
.clearfix {
  *zoom: 1;
}
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}

/*============================================================================
  #Base Styles
==============================================================================*/
header, nav, section, article, aside, footer {
  display: block;
}

.template-giftcard,
.template-giftcard body {
  background: {{ settings.shop_bg_color }};
}
.template-giftcard a,
.template-giftcard body a {
  text-decoration: none;
}

.template-giftcard .wrapper {
  margin: 0 auto;
  max-width: 588px;
}
.template-giftcard .wrapper img, .template-giftcard .wrapper object, .template-giftcard .wrapper iframe {
  max-width: 100%;
}

.giftcard-header {
  padding: 30px 0;
  font-size: 1em;
  text-align: center;
  animation: fadein 0.5s ease-in-out both 0.4s;
}

.shop-url {
  display: none;
}

.giftcard {
  animation: slideup 0.8s ease-in-out;
}

.giftcard__border {
  background-color: #eee;
  border-radius: 4px;
  border: 1px solid #e1e1e1;
  padding: 1em;
  animation: container-slide 0.8s ease-in-out;
}

.giftcard__content {
  *zoom: 1;
  background-color: #fff;
  color: #666;
  border: 1px solid #e1e1e1;
  border-radius: 3px;
  animation: cardslide 0.8s ease-in-out;
}
.giftcard__content:after {
  content: "";
  display: table;
  clear: both;
}

.giftcard__header {
  *zoom: 1;
  border-bottom: 1px solid {{ settings.border_color }};
  padding: 15px;
}
.giftcard__header:after {
  content: "";
  display: table;
  clear: both;
}

.giftcard__title {
  text-align: center;
  margin-bottom: 0;
}

.giftcard__tag {
  display: block;
  float: right;
  background-color: gray;
  border: 1px solid transparent;
  color: #fff;
  padding: 10px;
  border-radius: 4px;
  font-size: 0.75em;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1;
}

.giftcard__tag--active {
  background: transparent;
  color: #666;
  border: 1px solid #999999;
}

.giftcard__wrap {
  position: relative;
  margin: 15px 15px 30px;
}
.giftcard__wrap img {
  position: relative;
  display: block;
  border-radius: 10px;
  z-index: 2;
}
.giftcard__wrap:before, .giftcard__wrap:after {
  content: "";
  position: absolute;
  width: 47px;
  height: 47px;
  z-index: 3;
}
.giftcard__wrap:before {
  background: url("//cdn.shopify.com/s/assets/gift-card/corner-top-left-2ba3edcd9e97ba146cd01a8161365c5e.svg") 0 0 no-repeat;
  top: -1px;
  left: -1px;
}
.giftcard__wrap:after {
  background: url("//cdn.shopify.com/s/assets/gift-card/corner-bottom-right-1fb9bf49ff9564325e6b7c0fb0a7ff45.svg") 0 0 no-repeat;
  bottom: -1px;
  right: -1px;
}
.lt-ie9 .giftcard__wrap:before, .lt-ie9 .giftcard__wrap:after {
  display: none;
}

.giftcard__code {
  position: absolute;
  bottom: 30px;
  text-align: center;
  width: 100%;
  z-index: 50;
}

.giftcard__code--medium {
  font-size: 0.875em;
}

.giftcard__code--small {
  font-size: 0.75em;
}

.giftcard__code__inner {
  display: inline-block;
  vertical-align: baseline;
  background-color: #fff;
  padding: 0.5em;
  border-radius: 4px;
  max-width: 450px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}
.giftcard__code--small .giftcard__code__inner {
  overflow: auto;
}

.giftcard__code__text {
  font-weight: 400;
  font-size: 1.875em;
  text-transform: uppercase;
  border-radius: 2px;
  border: 1px dashed {{ settings.border_color }};
  padding: 0.4em 0.5em;
  display: inline-block;
  vertical-align: baseline;
  color: #777;
  line-height: 1;
}
.disabled .giftcard__code__text {
  color: #999;
  text-decoration: line-through;
}

.giftcard__amount {
  position: absolute;
  top: 0;
  right: 0;
  color: #fff;
  font-size: 2.75em;
  line-height: 1.2;
  padding: 15px;
  z-index: 50;
}
.giftcard__amount strong {
  display: block;
  text-shadow: 3px 3px 0 rgba(0, 0, 0, 0.1);
}

.giftcard__amount--medium {
  font-size: 2em;
}

.tooltip {
  display: block;
  position: absolute;
  top: -50%;
  right: 50%;
  margin-top: 16px;
  z-index: 3;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  animation: popup 0.5s ease-in-out both 0.7s;
}
.tooltip:before {
  content: "";
  display: block;
  position: absolute;
  left: 100%;
  bottom: 0;
  width: 0;
  height: 0;
  margin-left: -5px;
  margin-bottom: -5px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 5px solid #333;
  border-top: 5px solid rgba(51, 51, 51, 0.9);
}

.tooltip__label {
  display: block;
  position: relative;
  right: -50%;
  border: none;
  border-radius: 4px;
  background: #333;
  background: rgba(51, 51, 51, 0.9);
  min-height: 14px;
  font-weight: 400;
  font-size: 12px;
  text-decoration: none;
  line-height: 16px;
  text-shadow: none;
  padding: 0.5em 0.75em;
  margin-left: 0.25em;
}
.tooltip__label small {
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: #b3b3b3;
  font-size: 0.875em;
}

.giftcard__instructions {
  text-align: center;
  margin: 0 15px 30px;
}

.giftcard__actions {
  position: relative;
  border-top: 1px solid {{ settings.border_color }};
  padding: 30px 15px;
  text-align: center;
  overflow: hidden;
}

.action-link {
  position: absolute;
  left: 15px;
  top: 50%;
  font-size: 0.875rem;
  font-weight: bold;
  display: block;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  color: gray;
  margin-top: -10px;
}
.action-link:hover, .action-link:focus {
  color: #666;
}

.action-link__print {
  display: inline-block;
  vertical-align: baseline;
  width: 17px;
  height: 17px;
  vertical-align: middle;
  margin-right: 5px;
  opacity: 0.25;
  background-image: url("//cdn.shopify.com/s/assets/gift-card/icon-print-164daa1ae32d10d1f9b83ac21b6f2c70.png");
  background-repeat: no-repeat;
  background-position: 0 0;
}
.svg .action-link__print {
  background-image: url("//cdn.shopify.com/s/assets/gift-card/icon-print-6a10b2fb86d223b8c783c9696eaf4c31.svg");
}
.action-link:hover .action-link__print {
  opacity: 0.4;
}

.giftcard__footer {
  text-align: center;
  padding: 60px 0;
  animation: fadein 0.5s ease-in-out both 0.4s;
}

#QrCode img {
  padding: 30px;
  border: 1px solid {{ settings.border_color }};
  border-radius: 4px;
  margin: 0 auto 30px;
}

/*============================================================================
  #Media Queries
==============================================================================*/
/*================ Medium-down width ================*/
@media screen and (max-width: 580px) {
  .giftcard {
    font-size: 12px;
  }
  .giftcard-header {
    padding: 30px 0;
  }
  .giftcard__border {
    padding: 15px;
  }
  .giftcard__actions {
    padding: 15px;
  }
  .giftcard__actions .btn {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }
  .action-link {
    display: none;
  }
}
/*================ Small width ================*/
@media screen and (max-width: 400px) {
  .giftcard__amount strong {
    text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
  }
  .giftcard__wrap:before,
  .giftcard__wrap:after {
    display: none;
  }
  .giftcard__code {
    font-size: 0.75em;
  }
  .giftcard__code--medium {
    font-size: 0.65em;
  }
  .giftcard__code--small {
    font-size: 0.55em;
  }
}
/*================ Small height ================*/
@media screen and (max-height: 800px) {
  .header-logo img {
    max-height: 90px;
  }
}
/*============================================================================
  #Print Styles
==============================================================================*/
@media print {
  @page {
    margin: 0.5cm;
  }
  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }
  h2, h3 {
    page-break-after: avoid;
  }
  html, body {
    background-color: #fff;
  }
  .giftcard-header {
    padding: 10px 0;
  }
  .giftcard__content,
  .giftcard__border {
    border: 0 none;
  }
  .giftcard__actions,
  .giftcard__wrap:before,
  .giftcard__wrap:after,
  .tooltip,
  .add-to-apple-wallet {
    display: none;
  }
  .giftcard__title {
    float: none;
    text-align: center;
  }
  .giftcard__code__text {
    color: #555;
  }
  .shop-url {
    display: block;
  }
  .logo {
    color: #58686F;
  }
}
/*============================================================================
  #Keyframe Animations
==============================================================================*/
@keyframes slideup {
  0% {
    opacity: 0;
    transform: translateY(2000px) rotate(10deg);
  }
  60% {
    opacity: 1;
    transform: translateY(-30px);
  }
  80% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}
@keyframes popup {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  60% {
    opacity: 1;
    transform: translateY(-10px);
  }
  80% {
    transform: translateY(2px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes container-slide {
  0% {
    opacity: 0;
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
@keyframes fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 100;
  }
}