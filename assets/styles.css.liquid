{% assign button_primary_color = settings.button_primary_bg_color %}
{% assign button_secondary_color = settings.button_primary_bg_color--highlight %}
{% assign button_font = settings.button__font %}
{% assign regular_font = settings.regular__font %}
@font-face {
  font-family: "omnes";
  src: url('./Omnes-Regular.woff2') format('woff2'),
        url('./Omnes-Regular.woff') format('woff');
}
@charset "UTF-8";
/*!
 * animate.css -http://daneden.me/animate
 * Version - 3.5.2
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2017 Daniel Eden
 */

 /*
|--------------------------------------------------------------------------
| New Adobe Fonts
|--------------------------------------------------------------------------
*/

/* Here we will be seeing if the settings in typography is enabled
if it is enabled we are going to add in the different font families with !important
to be able to overide the previous settings
Everything here can be found in /config/settings_schema.json
"name": "Typography" */

{%- if settings.omnes_font -%}
h1:not(.zpa-published-page-holder h1),
h2:not(.zpa-published-page-holder h2),
h3:not(.zpa-published-page-holder h3),
h4:not(.zpa-published-page-holder h4),
h5:not(.zpa-published-page-holder h5),
h6:not(.zpa-published-page-holder h6),
p:not(.zpa-published-page-holder p),
button:not(.zpa-published-page-holder button),
ul:not(.zpa-published-page-holder ul),
small:not(.zpa-published-page-holder small),
em:not(.zpa-published-page-holder em),
.text,
.money,
.dragBoxText,
.dragDropText,
.orText,
.sale,
.shopify-installments {
  font-family: omnes, sans-serif !important;
}
{%- endif -%}

.shopify-installments__learn-more {
  display: block;
}

{%- if settings.omnes_font_label -%}
label:not(.zpa-published-page-holder label) {
  font-family: omnes, sans-serif !important;
}
{%- endif -%}

{%- if settings.omnes_font_pdp_description -%}
#prod-desc {
  font-family: omnes, sans-serif !important; 
}
{%- endif -%}

/*
|--------------------------------------------------------------------------
| Product Sections
|--------------------------------------------------------------------------
*/

/* Here we are going to see if the new styles for product sections are enabled 
if so we are going to get the classes for each color and style it in the theme settings
we are going to use !important to be able to override previous settings
Everything here can be found in the /config/settings_schema.json  `*/

{%- if settings.enable_new_product_sections_styles -%}
.product__how-to-submit__tabs .contents>div .content .links a {
  background-color: {{ settings.product_tabs_button_background_color }} !important;
  color: {{ settings.product_tabs_button_color }} !important;
}
.product__how-to-submit__tabs .contents>div .content .links a:hover {
  background-color: {{ settings.product_tabs_button_background_color_hover }} !important;
  color: {{ settings.product_tabs_button_color_hover }} !important;
}
.product__faq__questions>div h3:before {
  color: {{ settings.product_faq_section_icon_color }} !important;
}
.product__how-to-submit__tabs .tabs {
  background: {{ settings.submit_pet_pictures_tabs_bg }} !important;
  background-color: {{ settings.submit_pet_pictures_tabs_bg }} !important;
}
.product__how-to-submit__tabs .tabs>div:hover {
  background: {{ settings.submit_pet_pictures_tabs_bg_hover }} !important;
  background-color: {{ settings.submit_pet_pictures_tabs_bg_hover }} !important;
}
.product__how-to-submit__tabs .tabs .active {
  background: {{ settings.submit_pet_pictures_tabs_active_bg }} !important;
  background-color: {{ settings.submit_pet_pictures_tabs_active_bg }} !important;
}
.product__how-to-submit__tabs .contents {
  border-right: 1px solid {{ settings.product_tabs_content_border_color }} !important;
  border-left: 1px solid {{ settings.product_tabs_content_border_color }} !important;
  border-bottom: 1px solid {{ settings.product_tabs_content_border_color }} !important;
}
{%- endif -%}
@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}
.bounceIn {
  animation-duration: 0.75s;
  animation-name: bounceIn;
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.fadeIn {
  animation-name: fadeIn;
}
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
.fadeInDown {
  animation-name: fadeInDown;
}
@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
.fadeInLeft {
  animation-name: fadeInLeft;
}
@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
.fadeInRight {
  animation-name: fadeInRight;
}
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
.fadeInUp {
  animation-name: fadeInUp;
}
@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.fadeOut {
  animation-name: fadeOut;
}
@keyframes fadeOutUp {
  0% {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
}
.fadeOutUp {
  animation-name: fadeOutUp;
}
@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 1;
  }
}
.zoomIn {
  animation-name: zoomIn;
}
@keyframes zoomOut {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
.zoomOut {
  animation-name: zoomOut;
}
@keyframes slideInLeft {
  0% {
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
    opacity: 1;
  }
  to {
    transform: translateZ(0);
    opacity: 1;
  }
}
.slideInLeft {
  animation-name: slideInLeft;
}
@keyframes slideInRight {
  0% {
    transform: translate3d(100%, 0, 0);
    visibility: visible;
    opacity: 1;
  }
  to {
    transform: translateZ(0);
    opacity: 1;
  }
}
.slideInRight {
  animation-name: slideInRight;
}
@keyframes slideOutRight {
  0% {
    transform: translateZ(0);
  }
  to {
    visibility: hidden;
    transform: translate3d(100%, 0, 0);
  }
}
.slideOutRight {
  animation-name: slideOutRight;
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animated.infinite {
  animation-iteration-count: infinite;
}

.animated.delay-1s {
  animation-delay: 1s;
}

.animated.delay-2s {
  animation-delay: 2s;
}

.animated.delay-3s {
  animation-delay: 3s;
}

.animated.delay-4s {
  animation-delay: 4s;
}

.animated.delay-5s {
  animation-delay: 5s;
}

.animated.fast {
  animation-duration: 0.8s;
}

.animated.faster {
  animation-duration: 0.5s;
}

.animated.slow {
  animation-duration: 2s;
}

.animated.slower {
  animation-duration: 3s;
}

@media (print) {
  .animated {
    animation: unset !important;
    transition: none !important;
  }
}
/*! Flickity v2.1.2
https://flickity.metafizzy.co
---------------------------------------------- */
.flickity-enabled {
  position: relative;
}

.flickity-enabled:focus {
  outline: 0;
}

.flickity-viewport {
  overflow: hidden;
  position: relative;
  height: 100%;
}

.flickity-slider {
  position: absolute;
  width: 100%;
  height: 100%;
}

.flickity-enabled.is-draggable {
  -webkit-tap-highlight-color: transparent;
  tap-highlight-color: transparent;
  -webkit-user-select: none;
  user-select: none;
}

.flickity-enabled.is-draggable .flickity-viewport {
  cursor: move;
  cursor: grab;
}

.flickity-enabled.is-draggable .flickity-viewport.is-pointer-down {
  cursor: grabbing;
}

.flickity-button {
  position: absolute;
  background: hsla(0, 0%, 100%, 0.75);
  border: none;
  color: #333;
}

.flickity-button:hover {
  background: #fff;
  cursor: pointer;
}

.flickity-button:focus {
  outline: 0;
  box-shadow: 0 0 0 5px #19f;
}

.flickity-button:active {
  opacity: 0.6;
}

.flickity-button:disabled {
  opacity: 0.3;
  cursor: auto;
  pointer-events: none;
}

.flickity-button-icon {
  fill: #333;
}

.flickity-prev-next-button {
  top: 50%;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  transform: translateY(-50%);
}

.flickity-prev-next-button.previous {
  left: 10px;
}

.flickity-prev-next-button.next {
  right: 10px;
}

.flickity-rtl .flickity-prev-next-button.previous {
  left: auto;
  right: 10px;
}

.flickity-rtl .flickity-prev-next-button.next {
  right: auto;
  left: 10px;
}

.flickity-prev-next-button .flickity-button-icon {
  position: absolute;
  left: 20%;
  top: 20%;
  width: 60%;
  height: 60%;
}

.flickity-page-dots {
  position: absolute;
  width: 100%;
  bottom: -25px;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  line-height: 1;
}

.flickity-rtl .flickity-page-dots {
  direction: rtl;
}

.flickity-page-dots .dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0 8px;
  background: #333;
  border-radius: 50%;
  opacity: 0.25;
  cursor: pointer;
}

.flickity-page-dots .dot.is-selected {
  opacity: 1;
}

/* flickity-fade */
.flickity-enabled.is-fade .flickity-slider > * {
  pointer-events: none;
  z-index: 0;
}

.flickity-enabled.is-fade .flickity-slider > .is-selected {
  pointer-events: auto;
  z-index: 1;
}

/*! Lazyframe
https://github.com/vb/lazyframe
---------------------------------------------- */
.lazyframe {
  position: relative;
  background-color: currentColor;
  background-repeat: no-repeat;
  background-size: cover;
}

.lazyframe__title {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 15px 17px;
  z-index: 3;
}

.lazyframe__title:after {
  z-index: -1;
}

.lazyframe:hover {
  cursor: pointer;
}

.lazyframe:before {
  display: block;
  content: "";
  width: 100%;
  padding-top: 100%;
}

.lazyframe[data-ratio="16:9"]:before {
  padding-top: 56.25%;
}

.lazyframe[data-ratio="4:3"]:before {
  padding-top: 75%;
}

.lazyframe[data-ratio="1:1"]:before {
  padding-top: 100%;
}

.lazyframe iframe {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  width: 100%;
  height: 100%;
}

.lazyframe[data-vendor=youtube], .lazyframe[data-vendor=youtube_nocookie] {
  background-color: #e52d27;
  font-family: Roboto, Arial, Helvetica, sans-serif;
}

.lazyframe[data-vendor=youtube] .lazyframe__title, .lazyframe[data-vendor=youtube_nocookie] .lazyframe__title {
  color: #eee;
  font-family: Roboto, Arial, Helvetica, sans-serif;
  font-size: 18px;
  text-shadow: rgba(0, 0, 0, 0.498039) 0px 0px 2px;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;
  transition: color 0.1s cubic-bezier(0.4, 0, 1, 1);
}

.lazyframe[data-vendor=youtube] .lazyframe__title:hover, .lazyframe[data-vendor=youtube_nocookie] .lazyframe__title:hover {
  color: #fff;
}

.lazyframe[data-vendor=youtube] .lazyframe__title:before, .lazyframe[data-vendor=youtube_nocookie] .lazyframe__title:before {
  content: "";
  display: block;
  background: linear-gradient(rgba(0, 0, 0, 0.2), transparent);
  height: 98px;
  width: 100%;
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
  -webkit-tap-highlight-color: transparent;
}

.lazyframe[data-vendor=youtube]:before, .lazyframe[data-vendor=youtube_nocookie]:before {
  padding-top: 56.25%;
}

.lazyframe[data-vendor=youtube][data-ratio="16:9"]:before, .lazyframe[data-vendor=youtube_nocookie][data-ratio="16:9"]:before {
  padding-top: 56.25%;
}

.lazyframe[data-vendor=youtube][data-ratio="4:3"]:before, .lazyframe[data-vendor=youtube_nocookie][data-ratio="4:3"]:before {
  padding-top: 75%;
}

.lazyframe[data-vendor=youtube][data-ratio="1:1"]:before, .lazyframe[data-vendor=youtube_nocookie][data-ratio="1:1"]:before {
  padding-top: 100%;
}

.lazyframe[data-vendor=youtube]:after, .lazyframe[data-vendor=youtube_nocookie]:after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 68px;
  height: 48px;
  margin-left: -34px;
  margin-top: -24px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgNTEyIDUxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMjU1LjcgNDQ2LjNjLTUzLjMuMy0xMDYuNi0uNC0xNTkuOC0zLjMtMTcuNC0xLTM0LjctMi41LTUwLjQtMTFDMzUgNDI2LjMgMjcgNDE4LjQgMjIgNDA3LjIgMTMuMiAzODguNiAxMC41IDM2OSA5IDM0OWMtMy40LTQxLjMtMy42LTgyLjYtMS44LTEyMy44IDEtMjIgMS42LTQ0IDYuOC02NS41IDItOC40IDUtMTYuNiA4LjgtMjQuNEMzMiAxMTcgNDggMTA4IDY3LjMgMTA0YzE2LjItMyAzMi44LTMgNDkuMy0zLjcgNTYtMi4zIDExMi0zLjUgMTY4LTMgNDMgLjYgODYuMiAxLjcgMTI5LjMgNCAxMy4yLjYgMjYuNi44IDM5LjMgNS41IDE3LjIgNi40IDMwIDE3LjIgMzcgMzQuNyA2LjYgMTYuOCA5LjIgMzQuMiAxMC42IDUyIDMuOCA0OC43IDQgOTcuMy43IDE0Ni0xIDE2LjMtMi4yIDMyLjctNi41IDQ4LjgtOS43IDM3LTMyLjggNTEuNS02Ni43IDUzLjgtMzYuMiAyLjUtNzIuNSAzLjgtMTA4LjggNC4zLTIxLjMuMi00Mi43IDAtNjQgMHpNMjAzLjIgMzQ0TDM0OCAyNjQuN2wtMTQ0LjgtNzkuM1YzNDR6IiBmaWxsPSIjIzFmMWYxZiIvPjxwYXRoIGQ9Ik0yMDMuMiAzNDRWMTg1LjVMMzQ4IDI2NC44IDIwMy4yIDM0NHoiIGZpbGw9IiNGRUZERkQiLz48L3N2Zz4=");
  background-position: center center;
  background-size: 100%;
  background-repeat: no-repeat;
  opacity: 0.81;
  border: none;
  z-index: 4;
}

.lazyframe[data-vendor=youtube]:hover:after, .lazyframe[data-vendor=youtube_nocookie]:hover:after {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgNTEyIDUxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMjU1LjcgNDQ2LjNjLTUzLjMuMy0xMDYuNi0uNC0xNTkuOC0zLjMtMTcuNC0xLTM0LjctMi41LTUwLjQtMTFDMzUgNDI2LjMgMjcgNDE4LjQgMjIgNDA3LjIgMTMuMiAzODguNiAxMC41IDM2OSA5IDM0OWMtMy40LTQxLjMtMy42LTgyLjYtMS44LTEyMy44IDEtMjIgMS42LTQ0IDYuOC02NS41IDItOC40IDUtMTYuNiA4LjgtMjQuNEMzMiAxMTcgNDggMTA4IDY3LjMgMTA0YzE2LjItMyAzMi44LTMgNDkuMy0zLjcgNTYtMi4zIDExMi0zLjUgMTY4LTMgNDMgLjYgODYuMiAxLjcgMTI5LjMgNCAxMy4yLjYgMjYuNi44IDM5LjMgNS41IDE3LjIgNi40IDMwIDE3LjIgMzcgMzQuNyA2LjYgMTYuOCA5LjIgMzQuMiAxMC42IDUyIDMuOCA0OC43IDQgOTcuMy43IDE0Ni0xIDE2LjMtMi4yIDMyLjctNi41IDQ4LjgtOS43IDM3LTMyLjggNTEuNS02Ni43IDUzLjgtMzYuMiAyLjUtNzIuNSAzLjgtMTA4LjggNC4zLTIxLjMuMi00Mi43IDAtNjQgMHpNMjAzLjIgMzQ0TDM0OCAyNjQuN2wtMTQ0LjgtNzkuM1YzNDR6IiBmaWxsPSIjREQyQzI4Ii8+PHBhdGggZD0iTTIwMy4yIDM0NFYxODUuNUwzNDggMjY0LjggMjAzLjIgMzQ0eiIgZmlsbD0iI0ZFRkRGRCIvPjwvc3ZnPg==");
  opacity: 1;
}

.lazyframe[data-vendor=vimeo] {
  background-color: #00adef;
}

.lazyframe[data-vendor=vimeo] .lazyframe__title {
  font-family: "Helvetica Neue", Helvetica, Arial;
  color: #00adef;
  font-size: 20px;
  font-weight: bold;
  text-rendering: optimizeLegibility;
  -webkit-user-select: none;
          user-select: none;
  -webkit-font-smoothing: auto;
  -webkit-tap-highlight-color: transparent;
  background-color: rgba(0, 0, 0, 0.5);
}

.lazyframe[data-vendor=vimeo]:before {
  padding-top: 48.25%;
}

.lazyframe[data-vendor=vimeo][data-ratio="16:9"]:before {
  padding-top: 56.25%;
}

.lazyframe[data-vendor=vimeo][data-ratio="4:3"]:before {
  padding-top: 75%;
}

.lazyframe[data-vendor=vimeo][data-ratio="1:1"]:before {
  padding-top: 100%;
}

.lazyframe[data-vendor=vimeo]:after {
  content: "";
  height: 40px;
  width: 65px;
  display: block;
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 3;
  background-color: rgba(0, 0, 0, 0.5);
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgYmFzZVByb2ZpbGU9InRpbnkiIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI0ZGRiIgZD0iTTcuNzY1IDE2Ljg5bDguNDctNC44OS04LjQ3LTQuODkiLz48L3N2Zz4=");
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 5px;
  position: relative;
}

.lazyframe[data-vendor=vimeo]:hover:after {
  background-color: #00adef;
}

.lazyframe[data-vendor=vine] {
  background-color: #00bf8f;
}

.lazyframe[data-vendor=vine] .lazyframe__title {
  color: #fff;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 16px;
  white-space: nowrap;
  z-index: 3;
  positon: relative;
}

.lazyframe[data-vendor=vine] .lazyframe__title:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
  height: 115px;
  padding: 24px 70px 24px 24px;
  background: linear-gradient(to top, rgba(23, 23, 23, 0) 0, rgba(23, 23, 23, 0.7) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#b3171717", endColorstr="#00171717", GradientType=0 );
}

.lazyframe[data-vendor=vine]:before {
  padding-top: 100%;
}

.lazyframe[data-vendor=vine][data-ratio="16:9"]:before {
  padding-top: 56.25%;
}

.lazyframe[data-vendor=vine][data-ratio="4:3"]:before {
  padding-top: 75%;
}

.lazyframe[data-vendor=vine][data-ratio="1:1"]:before {
  padding-top: 100%;
}

.lazyframe[data-vendor=vine]:after {
  content: "";
  width: 60px;
  height: 60px;
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 4;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMzYiIGhlaWdodD0iMTM2IiB2aWV3Qm94PSIwIDAgMTM2IDEzNiI+PHBhdGggZmlsbD0iI0ZGRiIgZD0iTTU2IDQ0Yy0uNyAwLTEuNC4yLTIgLjUtMS4yLjgtMiAyLTIgMy41djQwYzAgMS40LjggMi44IDIgMy41LjYuMyAxLjMuNSAyIC41czEuNC0uMiAyLS41bDM0LjYtMjBjMS4zLS43IDItMiAyLTMuNSAwLTEuNC0uNy0yLjgtMi0zLjVMNTggNDQuNWMtLjYtLjMtMS4zLS41LTItLjV6Ii8+PC9zdmc+");
  background-color: rgba(0, 0, 0, 0.5);
  background-size: cover;
  background-repeat: no-repeat;
  margin-top: -30px;
  margin-left: -30px;
  border-radius: 50%;
}

.lazyframe[data-vendor=vine]:hover:after {
  background-color: rgba(0, 0, 0, 0.75);
}

/* #Plyr
================================================== */
@keyframes plyr-progress {
  to {
    background-position: 25px 0;
  }
}
@keyframes plyr-popup {
  0% {
    opacity: 0.5;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes plyr-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.plyr {
  -moz-osx-font-smoothing: auto;
  -webkit-font-smoothing: subpixel-antialiased;
  direction: ltr;
  font-family: Avenir, "Avenir Next", "Helvetica Neue", "Segoe UI", Helvetica, Arial, sans-serif;
  font-variant-numeric: tabular-nums;
  font-weight: 500;
  line-height: 1.7;
  max-width: 100%;
  min-width: 50px;
  position: relative;
  text-shadow: none;
  transition: box-shadow 0.3s ease;
}

.plyr audio, .plyr video {
  border-radius: inherit;
  height: auto;
  vertical-align: middle;
  width: 100%;
}

.plyr button {
  font: inherit;
  line-height: inherit;
  width: auto;
}

.plyr:focus {
  outline: 0;
}

.plyr--full-ui {
  box-sizing: border-box;
}

.plyr--full-ui *, .plyr--full-ui ::after, .plyr--full-ui ::before {
  box-sizing: inherit;
}

.plyr--full-ui a, .plyr--full-ui button, .plyr--full-ui input, .plyr--full-ui label {
  touch-action: manipulation;
}

.plyr__badge {
  background: #4a5764;
  border-radius: 2px;
  color: #fff;
  font-size: 9px;
  line-height: 1;
  padding: 3px 4px;
}

.plyr--full-ui ::-webkit-media-text-track-container {
  display: none;
}

.plyr__captions {
  animation: plyr-fade-in 0.3s ease;
  bottom: 0;
  color: #fff;
  display: none;
  font-size: 14px;
  left: 0;
  padding: 10px;
  position: absolute;
  text-align: center;
  transition: transform 0.4s ease-in-out;
  width: 100%;
}

.plyr__captions .plyr__caption {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 2px;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
  line-height: 185%;
  padding: 0.2em 0.5em;
  white-space: pre-wrap;
}

.plyr__captions .plyr__caption div {
  display: inline;
}

.plyr__captions span:empty {
  display: none;
}

@media (min-width: 480px) {
  .plyr__captions {
    font-size: 16px;
    padding: 20px;
  }
}
@media (min-width: 768px) {
  .plyr__captions {
    font-size: 18px;
  }
}
.plyr--captions-active .plyr__captions {
  display: block;
}

.plyr:not(.plyr--hide-controls) .plyr__controls:not(:empty) ~ .plyr__captions {
  transform: translateY(-40px);
}

.plyr__control {
  background: 0 0;
  border: 0;
  border-radius: 3px;
  color: inherit;
  cursor: pointer;
  flex-shrink: 0;
  overflow: visible;
  padding: 7px;
  position: relative;
  transition: all 0.3s ease;
}

.plyr__control svg {
  display: block;
  fill: currentColor;
  height: 18px;
  pointer-events: none;
  width: 18px;
}

.plyr__control:focus {
  outline: 0;
}

.plyr__control.plyr__tab-focus {
  box-shadow: 0 0 0 5px rgba(0, 179, 255, 0.5);
  outline: 0;
}

a.plyr__control {
  text-decoration: none;
}

a.plyr__control::after, a.plyr__control::before {
  display: none;
}

.plyr__control.plyr__control--pressed .icon--not-pressed, .plyr__control.plyr__control--pressed .label--not-pressed, .plyr__control:not(.plyr__control--pressed) .icon--pressed, .plyr__control:not(.plyr__control--pressed) .label--pressed {
  display: none;
}

.plyr--audio .plyr__control.plyr__tab-focus, .plyr--audio .plyr__control:hover, .plyr--audio .plyr__control[aria-expanded=true] {
  background: #00b3ff;
  color: #fff;
}

.plyr--video .plyr__control.plyr__tab-focus, .plyr--video .plyr__control:hover, .plyr--video .plyr__control[aria-expanded=true] {
  background: #00b3ff;
  color: #fff;
}

.plyr__control--overlaid {
  background: rgba(0, 179, 255, 0.8);
  border: 0;
  border-radius: 100%;
  color: #fff;
  display: none;
  left: 50%;
  padding: 15px;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.plyr__control--overlaid svg {
  left: 2px;
  position: relative;
}

.plyr__control--overlaid:focus, .plyr__control--overlaid:hover {
  background: #00b3ff;
}

.plyr--playing .plyr__control--overlaid {
  opacity: 0;
  visibility: hidden;
}

.plyr--full-ui.plyr--video .plyr__control--overlaid {
  display: block;
}

.plyr--full-ui ::-webkit-media-controls {
  display: none;
}

.plyr__controls {
  align-items: center;
  display: flex;
  justify-content: flex-end;
  text-align: center;
}

.plyr__controls .plyr__progress__container {
  flex: 1;
  min-width: 0;
}

.plyr__controls .plyr__controls__item {
  margin-left: 2.5px;
}

.plyr__controls .plyr__controls__item:first-child {
  margin-left: 0;
  margin-right: auto;
}

.plyr__controls .plyr__controls__item.plyr__progress__container {
  padding-left: 2.5px;
}

.plyr__controls .plyr__controls__item.plyr__time {
  padding: 0 5px;
}

.plyr__controls .plyr__controls__item.plyr__progress__container:first-child, .plyr__controls .plyr__controls__item.plyr__time + .plyr__time, .plyr__controls .plyr__controls__item.plyr__time:first-child {
  padding-left: 0;
}

.plyr__controls .plyr__controls__item.plyr__volume {
  padding-right: 5px;
}

.plyr__controls .plyr__controls__item.plyr__volume:first-child {
  padding-right: 0;
}

.plyr__controls:empty {
  display: none;
}

.plyr--audio .plyr__controls {
  background: #fff;
  border-radius: inherit;
  color: #4a5764;
  padding: 10px;
}

.plyr--video .plyr__controls {
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7));
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
  bottom: 0;
  color: #fff;
  left: 0;
  padding: 20px 5px 5px;
  position: absolute;
  right: 0;
  transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
  z-index: 3;
}

@media (min-width: 480px) {
  .plyr--video .plyr__controls {
    padding: 35px 10px 10px;
  }
}
.plyr--video.plyr--hide-controls .plyr__controls {
  opacity: 0;
  pointer-events: none;
  transform: translateY(100%);
}

.plyr [data-plyr=airplay], .plyr [data-plyr=captions], .plyr [data-plyr=fullscreen], .plyr [data-plyr=pip] {
  display: none;
}

.plyr--airplay-supported [data-plyr=airplay], .plyr--captions-enabled [data-plyr=captions], .plyr--fullscreen-enabled [data-plyr=fullscreen], .plyr--pip-supported [data-plyr=pip] {
  display: inline-block;
}

.plyr__menu {
  display: flex;
  position: relative;
}

.plyr__menu .plyr__control svg {
  transition: transform 0.3s ease;
}

.plyr__menu .plyr__control[aria-expanded=true] svg {
  transform: rotate(90deg);
}

.plyr__menu .plyr__control[aria-expanded=true] .plyr__tooltip {
  display: none;
}

.plyr__menu__container {
  animation: plyr-popup 0.2s ease;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  bottom: 100%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  color: #4a5764;
  font-size: 16px;
  margin-bottom: 10px;
  position: absolute;
  right: -3px;
  text-align: left;
  white-space: nowrap;
  z-index: 3;
}

.plyr__menu__container > div {
  overflow: hidden;
  transition: height 0.35s cubic-bezier(0.4, 0, 0.2, 1), width 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

.plyr__menu__container::after {
  border: 4px solid transparent;
  border-top-color: rgba(255, 255, 255, 0.9);
  content: "";
  height: 0;
  position: absolute;
  right: 15px;
  top: 100%;
  width: 0;
}

.plyr__menu__container [role=menu] {
  padding: 7px;
}

.plyr__menu__container [role=menuitem], .plyr__menu__container [role=menuitemradio] {
  margin-top: 2px;
}

.plyr__menu__container [role=menuitem]:first-child, .plyr__menu__container [role=menuitemradio]:first-child {
  margin-top: 0;
}

.plyr__menu__container .plyr__control {
  align-items: center;
  color: #4a5764;
  display: flex;
  font-size: 14px;
  padding: 4px 11px;
  -webkit-user-select: none;
  user-select: none;
  width: 100%;
}

.plyr__menu__container .plyr__control > span {
  align-items: inherit;
  display: flex;
  width: 100%;
}

.plyr__menu__container .plyr__control::after {
  border: 4px solid transparent;
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.plyr__menu__container .plyr__control--forward {
  padding-right: 28px;
}

.plyr__menu__container .plyr__control--forward::after {
  border-left-color: rgba(74, 87, 100, 0.8);
  right: 5px;
}

.plyr__menu__container .plyr__control--forward.plyr__tab-focus::after, .plyr__menu__container .plyr__control--forward:hover::after {
  border-left-color: currentColor;
}

.plyr__menu__container .plyr__control--back {
  font-weight: 500;
  margin: 7px;
  margin-bottom: 3px;
  padding-left: 28px;
  position: relative;
  width: calc(100% - 14px);
}

.plyr__menu__container .plyr__control--back::after {
  border-right-color: rgba(74, 87, 100, 0.8);
  left: 7px;
}

.plyr__menu__container .plyr__control--back::before {
  background: #c1c9d1;
  box-shadow: 0 1px 0 #fff;
  content: "";
  height: 1px;
  left: 0;
  margin-top: 4px;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 100%;
}

.plyr__menu__container .plyr__control--back.plyr__tab-focus::after, .plyr__menu__container .plyr__control--back:hover::after {
  border-right-color: currentColor;
}

.plyr__menu__container .plyr__control[role=menuitemradio] {
  padding-left: 7px;
}

.plyr__menu__container .plyr__control[role=menuitemradio]::after, .plyr__menu__container .plyr__control[role=menuitemradio]::before {
  border-radius: 100%;
}

.plyr__menu__container .plyr__control[role=menuitemradio]::before {
  background: rgba(0, 0, 0, 0.1);
  content: "";
  display: block;
  flex-shrink: 0;
  height: 16px;
  margin-right: 10px;
  transition: all 0.3s ease;
  width: 16px;
}

.plyr__menu__container .plyr__control[role=menuitemradio]::after {
  background: #fff;
  border: 0;
  height: 6px;
  left: 12px;
  opacity: 0;
  top: 50%;
  transform: translateY(-50%) scale(0);
  transition: transform 0.3s ease, opacity 0.3s ease;
  width: 6px;
}

.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::before {
  background: #00b3ff;
}

.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::after {
  opacity: 1;
  transform: translateY(-50%) scale(1);
}

.plyr__menu__container .plyr__control[role=menuitemradio].plyr__tab-focus::before, .plyr__menu__container .plyr__control[role=menuitemradio]:hover::before {
  background: rgba(0, 0, 0, 0.1);
}

.plyr__menu__container .plyr__menu__value {
  align-items: center;
  display: flex;
  margin-left: auto;
  margin-right: -5px;
  overflow: hidden;
  padding-left: 25px;
  pointer-events: none;
}

.plyr--full-ui input[type=range] {
  -webkit-appearance: none;
  background: 0 0;
  border: 0;
  border-radius: 26px;
  color: #00b3ff;
  display: block;
  height: 19px;
  margin: 0;
  padding: 0;
  transition: box-shadow 0.3s ease;
  width: 100%;
}

.plyr--full-ui input[type=range]::-webkit-slider-runnable-track {
  background: 0 0;
  border: 0;
  border-radius: 2.5px;
  height: 5px;
  -webkit-transition: box-shadow 0.3s ease;
  transition: box-shadow 0.3s ease;
  -webkit-user-select: none;
  user-select: none;
  background-image: linear-gradient(to right, currentColor var(--value, 0), transparent var(--value, 0));
}

.plyr--full-ui input[type=range]::-webkit-slider-thumb {
  background: #fff;
  border: 0;
  border-radius: 100%;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2);
  height: 13px;
  position: relative;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  width: 13px;
  -webkit-appearance: none;
  margin-top: -4px;
}

.plyr--full-ui input[type=range]::-moz-range-track {
  background: 0 0;
  border: 0;
  border-radius: 2.5px;
  height: 5px;
  -moz-transition: box-shadow 0.3s ease;
  transition: box-shadow 0.3s ease;
  user-select: none;
}

.plyr--full-ui input[type=range]::-moz-range-thumb {
  background: #fff;
  border: 0;
  border-radius: 100%;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2);
  height: 13px;
  position: relative;
  -moz-transition: all 0.2s ease;
  transition: all 0.2s ease;
  width: 13px;
}

.plyr--full-ui input[type=range]::-moz-range-progress {
  background: currentColor;
  border-radius: 2.5px;
  height: 5px;
}

.plyr--full-ui input[type=range]::-ms-track {
  background: 0 0;
  border: 0;
  border-radius: 2.5px;
  height: 5px;
  -ms-transition: box-shadow 0.3s ease;
  transition: box-shadow 0.3s ease;
  user-select: none;
  color: transparent;
}

.plyr--full-ui input[type=range]::-ms-fill-upper {
  background: 0 0;
  border: 0;
  border-radius: 2.5px;
  height: 5px;
  -ms-transition: box-shadow 0.3s ease;
  transition: box-shadow 0.3s ease;
  user-select: none;
}

.plyr--full-ui input[type=range]::-ms-fill-lower {
  background: 0 0;
  border: 0;
  border-radius: 2.5px;
  height: 5px;
  -ms-transition: box-shadow 0.3s ease;
  transition: box-shadow 0.3s ease;
  user-select: none;
  background: currentColor;
}

.plyr--full-ui input[type=range]::-ms-thumb {
  background: #fff;
  border: 0;
  border-radius: 100%;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2);
  height: 13px;
  position: relative;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
  width: 13px;
  margin-top: 0;
}

.plyr--full-ui input[type=range]::-ms-tooltip {
  display: none;
}

.plyr--full-ui input[type=range]:focus {
  outline: 0;
}

.plyr--full-ui input[type=range]::-moz-focus-outer {
  border: 0;
}

.plyr--full-ui input[type=range].plyr__tab-focus::-webkit-slider-runnable-track {
  box-shadow: 0 0 0 5px rgba(0, 179, 255, 0.5);
  outline: 0;
}

.plyr--full-ui input[type=range].plyr__tab-focus::-moz-range-track {
  box-shadow: 0 0 0 5px rgba(0, 179, 255, 0.5);
  outline: 0;
}

.plyr--full-ui input[type=range].plyr__tab-focus::-ms-track {
  box-shadow: 0 0 0 5px rgba(0, 179, 255, 0.5);
  outline: 0;
}

.plyr--full-ui.plyr--video input[type=range]::-webkit-slider-runnable-track {
  background-color: rgba(255, 255, 255, 0.25);
}

.plyr--full-ui.plyr--video input[type=range]::-moz-range-track {
  background-color: rgba(255, 255, 255, 0.25);
}

.plyr--full-ui.plyr--video input[type=range]::-ms-track {
  background-color: rgba(255, 255, 255, 0.25);
}

.plyr--full-ui.plyr--video input[type=range]:active::-webkit-slider-thumb {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2), 0 0 0 3px rgba(255, 255, 255, 0.5);
}

.plyr--full-ui.plyr--video input[type=range]:active::-moz-range-thumb {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2), 0 0 0 3px rgba(255, 255, 255, 0.5);
}

.plyr--full-ui.plyr--video input[type=range]:active::-ms-thumb {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2), 0 0 0 3px rgba(255, 255, 255, 0.5);
}

.plyr--full-ui.plyr--audio input[type=range]::-webkit-slider-runnable-track {
  background-color: rgba(193, 201, 209, 0.66);
}

.plyr--full-ui.plyr--audio input[type=range]::-moz-range-track {
  background-color: rgba(193, 201, 209, 0.66);
}

.plyr--full-ui.plyr--audio input[type=range]::-ms-track {
  background-color: rgba(193, 201, 209, 0.66);
}

.plyr--full-ui.plyr--audio input[type=range]:active::-webkit-slider-thumb {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2), 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.plyr--full-ui.plyr--audio input[type=range]:active::-moz-range-thumb {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2), 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.plyr--full-ui.plyr--audio input[type=range]:active::-ms-thumb {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(35, 41, 47, 0.2), 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.plyr__poster {
  background-color: #000;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: contain;
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  transition: opacity 0.2s ease;
  width: 100%;
  z-index: 1;
}

.plyr--stopped.plyr__poster-enabled .plyr__poster {
  opacity: 1;
}

.plyr__time {
  font-size: 14px;
}

.plyr__time + .plyr__time::before {
  content: "⁄";
  margin-right: 10px;
}

@media (max-width: 767px) {
  .plyr__time + .plyr__time {
    display: none;
  }
}
.plyr--video .plyr__time {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
}

.plyr__tooltip {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 3px;
  bottom: 100%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  color: #4a5764;
  font-size: 14px;
  font-weight: 500;
  left: 50%;
  line-height: 1.3;
  margin-bottom: 10px;
  opacity: 0;
  padding: 5px 7.5px;
  pointer-events: none;
  position: absolute;
  transform: translate(-50%, 10px) scale(0.8);
  transform-origin: 50% 100%;
  transition: transform 0.2s 0.1s ease, opacity 0.2s 0.1s ease;
  white-space: nowrap;
  z-index: 2;
}

.plyr__tooltip::before {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(255, 255, 255, 0.9);
  bottom: -4px;
  content: "";
  height: 0;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
  width: 0;
  z-index: 2;
}

.plyr .plyr__control.plyr__tab-focus .plyr__tooltip, .plyr .plyr__control:hover .plyr__tooltip, .plyr__tooltip--visible {
  opacity: 1;
  transform: translate(-50%, 0) scale(1);
}

.plyr .plyr__control:hover .plyr__tooltip {
  z-index: 3;
}

.plyr__controls > .plyr__control:first-child .plyr__tooltip, .plyr__controls > .plyr__control:first-child + .plyr__control .plyr__tooltip {
  left: 0;
  transform: translate(0, 10px) scale(0.8);
  transform-origin: 0 100%;
}

.plyr__controls > .plyr__control:first-child .plyr__tooltip::before, .plyr__controls > .plyr__control:first-child + .plyr__control .plyr__tooltip::before {
  left: 16px;
}

.plyr__controls > .plyr__control:last-child .plyr__tooltip {
  left: auto;
  right: 0;
  transform: translate(0, 10px) scale(0.8);
  transform-origin: 100% 100%;
}

.plyr__controls > .plyr__control:last-child .plyr__tooltip::before {
  left: auto;
  right: 16px;
  transform: translateX(50%);
}

.plyr__controls > .plyr__control:first-child .plyr__tooltip--visible, .plyr__controls > .plyr__control:first-child + .plyr__control .plyr__tooltip--visible, .plyr__controls > .plyr__control:first-child + .plyr__control.plyr__tab-focus .plyr__tooltip, .plyr__controls > .plyr__control:first-child + .plyr__control:hover .plyr__tooltip, .plyr__controls > .plyr__control:first-child.plyr__tab-focus .plyr__tooltip, .plyr__controls > .plyr__control:first-child:hover .plyr__tooltip, .plyr__controls > .plyr__control:last-child .plyr__tooltip--visible, .plyr__controls > .plyr__control:last-child.plyr__tab-focus .plyr__tooltip, .plyr__controls > .plyr__control:last-child:hover .plyr__tooltip {
  transform: translate(0, 0) scale(1);
}

.plyr--video {
  background: #000;
  overflow: hidden;
}

.plyr--video.plyr--menu-open {
  overflow: visible;
}

.plyr__video-wrapper {
  background: #000;
  border-radius: inherit;
  overflow: hidden;
  position: relative;
  z-index: 0;
}

.plyr__video-embed, .plyr__video-wrapper--fixed-ratio {
  height: 0;
  padding-bottom: 56.25%;
}

.plyr__video-embed iframe, .plyr__video-wrapper--fixed-ratio video {
  border: 0;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  -webkit-user-select: none;
  user-select: none;
  width: 100%;
}

.plyr--full-ui .plyr__video-embed > .plyr__video-embed__container {
  padding-bottom: 240%;
  position: relative;
  transform: translateY(-38.28125%);
}

.plyr__progress {
  left: 6.5px;
  margin-right: 13px;
  position: relative;
}

.plyr__progress input[type=range], .plyr__progress__buffer {
  margin-left: -6.5px;
  margin-right: -6.5px;
  width: calc(100% + 13px);
}

.plyr__progress input[type=range] {
  position: relative;
  z-index: 2;
}

.plyr__progress .plyr__tooltip {
  font-size: 14px;
  left: 0;
}

.plyr__progress__buffer {
  -webkit-appearance: none;
  background: 0 0;
  border: 0;
  border-radius: 100px;
  height: 5px;
  left: 0;
  margin-top: -2.5px;
  padding: 0;
  position: absolute;
  top: 50%;
}

.plyr__progress__buffer::-webkit-progress-bar {
  background: 0 0;
}

.plyr__progress__buffer::-webkit-progress-value {
  background: currentColor;
  border-radius: 100px;
  min-width: 5px;
  -webkit-transition: width 0.2s ease;
  transition: width 0.2s ease;
}

.plyr__progress__buffer::-moz-progress-bar {
  background: currentColor;
  border-radius: 100px;
  min-width: 5px;
  -moz-transition: width 0.2s ease;
  transition: width 0.2s ease;
}

.plyr__progress__buffer::-ms-fill {
  border-radius: 100px;
  -ms-transition: width 0.2s ease;
  transition: width 0.2s ease;
}

.plyr--video .plyr__progress__buffer {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
  color: rgba(255, 255, 255, 0.25);
}

.plyr--audio .plyr__progress__buffer {
  color: rgba(193, 201, 209, 0.66);
}

.plyr--loading .plyr__progress__buffer {
  animation: plyr-progress 1s linear infinite;
  background-image: linear-gradient(-45deg, rgba(35, 41, 47, 0.6) 25%, transparent 25%, transparent 50%, rgba(35, 41, 47, 0.6) 50%, rgba(35, 41, 47, 0.6) 75%, transparent 75%, transparent);
  background-repeat: repeat-x;
  background-size: 25px 25px;
  color: transparent;
}

.plyr--video.plyr--loading .plyr__progress__buffer {
  background-color: rgba(255, 255, 255, 0.25);
}

.plyr--audio.plyr--loading .plyr__progress__buffer {
  background-color: rgba(193, 201, 209, 0.66);
}

.plyr__volume {
  align-items: center;
  display: flex;
  flex: 1;
  position: relative;
}

.plyr__volume input[type=range] {
  margin-left: 5px;
  position: relative;
  z-index: 2;
}

@media (min-width: 480px) {
  .plyr__volume {
    max-width: 90px;
  }
}
@media (min-width: 768px) {
  .plyr__volume {
    max-width: 110px;
  }
}
.plyr--is-ios .plyr__volume {
  display: none !important;
}

.plyr--is-ios.plyr--vimeo [data-plyr=mute] {
  display: none !important;
}

.plyr:-webkit-full-screen {
  background: #000;
  border-radius: 0 !important;
  height: 100%;
  margin: 0;
  width: 100%;
}

.plyr:fullscreen {
  background: #000;
  border-radius: 0 !important;
  height: 100%;
  margin: 0;
  width: 100%;
}

.plyr:-webkit-full-screen video {
  height: 100%;
}

.plyr:fullscreen video {
  height: 100%;
}

.plyr:-webkit-full-screen .plyr__video-wrapper {
  height: 100%;
  position: static;
}

.plyr:fullscreen .plyr__video-wrapper {
  height: 100%;
  position: static;
}

.plyr:-webkit-full-screen.plyr--vimeo .plyr__video-wrapper {
  height: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.plyr:fullscreen.plyr--vimeo .plyr__video-wrapper {
  height: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen {
  display: block;
}

.plyr:fullscreen .plyr__control .icon--exit-fullscreen {
  display: block;
}

.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen + svg {
  display: none;
}

.plyr:fullscreen .plyr__control .icon--exit-fullscreen + svg {
  display: none;
}

.plyr:-webkit-full-screen.plyr--hide-controls {
  cursor: none;
}

.plyr:fullscreen.plyr--hide-controls {
  cursor: none;
}

@media (min-width: 1024px) {
  .plyr:-webkit-full-screen .plyr__captions {
    font-size: 21px;
  }
  .plyr:fullscreen .plyr__captions {
    font-size: 21px;
  }
}
.plyr:-webkit-full-screen {
  background: #000;
  border-radius: 0 !important;
  height: 100%;
  margin: 0;
  width: 100%;
}

.plyr:-webkit-full-screen video {
  height: 100%;
}

.plyr:-webkit-full-screen .plyr__video-wrapper {
  height: 100%;
  position: static;
}

.plyr:-webkit-full-screen.plyr--vimeo .plyr__video-wrapper {
  height: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen {
  display: block;
}

.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen + svg {
  display: none;
}

.plyr:-webkit-full-screen.plyr--hide-controls {
  cursor: none;
}

@media (min-width: 1024px) {
  .plyr:-webkit-full-screen .plyr__captions {
    font-size: 21px;
  }
}
.plyr:-moz-full-screen {
  background: #000;
  border-radius: 0 !important;
  height: 100%;
  margin: 0;
  width: 100%;
}

.plyr:-moz-full-screen video {
  height: 100%;
}

.plyr:-moz-full-screen .plyr__video-wrapper {
  height: 100%;
  position: static;
}

.plyr:-moz-full-screen.plyr--vimeo .plyr__video-wrapper {
  height: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.plyr:-moz-full-screen .plyr__control .icon--exit-fullscreen {
  display: block;
}

.plyr:-moz-full-screen .plyr__control .icon--exit-fullscreen + svg {
  display: none;
}

.plyr:-moz-full-screen.plyr--hide-controls {
  cursor: none;
}

@media (min-width: 1024px) {
  .plyr:-moz-full-screen .plyr__captions {
    font-size: 21px;
  }
}
.plyr:-ms-fullscreen {
  background: #000;
  border-radius: 0 !important;
  height: 100%;
  margin: 0;
  width: 100%;
}

.plyr:-ms-fullscreen video {
  height: 100%;
}

.plyr:-ms-fullscreen .plyr__video-wrapper {
  height: 100%;
  position: static;
}

.plyr:-ms-fullscreen.plyr--vimeo .plyr__video-wrapper {
  height: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen {
  display: block;
}

.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen + svg {
  display: none;
}

.plyr:-ms-fullscreen.plyr--hide-controls {
  cursor: none;
}

@media (min-width: 1024px) {
  .plyr:-ms-fullscreen .plyr__captions {
    font-size: 21px;
  }
}
.plyr--fullscreen-fallback {
  background: #000;
  border-radius: 0 !important;
  height: 100%;
  margin: 0;
  width: 100%;
  bottom: 0;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 10000000;
}

.plyr--fullscreen-fallback video {
  height: 100%;
}

.plyr--fullscreen-fallback .plyr__video-wrapper {
  height: 100%;
  position: static;
}

.plyr--fullscreen-fallback.plyr--vimeo .plyr__video-wrapper {
  height: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.plyr--fullscreen-fallback .plyr__control .icon--exit-fullscreen {
  display: block;
}

.plyr--fullscreen-fallback .plyr__control .icon--exit-fullscreen + svg {
  display: none;
}

.plyr--fullscreen-fallback.plyr--hide-controls {
  cursor: none;
}

@media (min-width: 1024px) {
  .plyr--fullscreen-fallback .plyr__captions {
    font-size: 21px;
  }
}
.plyr__ads {
  border-radius: inherit;
  bottom: 0;
  cursor: pointer;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}

.plyr__ads > div, .plyr__ads > div iframe {
  height: 100%;
  position: absolute;
  width: 100%;
}

.plyr__ads::after {
  background: rgba(35, 41, 47, 0.8);
  border-radius: 2px;
  bottom: 10px;
  color: #fff;
  content: attr(data-badge-text);
  font-size: 11px;
  padding: 2px 6px;
  pointer-events: none;
  position: absolute;
  right: 10px;
  z-index: 3;
}

.plyr__ads::after:empty {
  display: none;
}

.plyr__cues {
  background: currentColor;
  display: block;
  height: 5px;
  left: 0;
  margin: -2.5px 0 0;
  opacity: 0.8;
  position: absolute;
  top: 50%;
  width: 3px;
  z-index: 3;
}

.plyr__preview-thumb {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 3px;
  bottom: 100%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  opacity: 0;
  padding: 3px;
  pointer-events: none;
  position: absolute;
  transform: translate(0, 10px) scale(0.8);
  transform-origin: 50% 100%;
  transition: transform 0.2s 0.1s ease, opacity 0.2s 0.1s ease;
  z-index: 2;
}

.plyr__preview-thumb--is-shown {
  opacity: 1;
  transform: translate(0, 0) scale(1);
}

.plyr__preview-thumb::before {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(255, 255, 255, 0.9);
  bottom: -4px;
  content: "";
  height: 0;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
  width: 0;
  z-index: 2;
}

.plyr__preview-thumb__image-container {
  background: #c1c9d1;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  z-index: 0;
}

.plyr__preview-thumb__image-container img {
  height: 100%;
  left: 0;
  max-height: none;
  max-width: none;
  position: absolute;
  top: 0;
  width: 100%;
}

.plyr__preview-thumb__time-container {
  bottom: 6px;
  left: 0;
  position: absolute;
  right: 0;
  white-space: nowrap;
  z-index: 3;
}

.plyr__preview-thumb__time-container span {
  background-color: rgba(0, 0, 0, 0.55);
  border-radius: 2px;
  color: #fff;
  font-size: 14px;
  padding: 3px 6px;
}

.plyr__preview-scrubbing {
  bottom: 0;
  filter: blur(1px);
  height: 100%;
  left: 0;
  margin: auto;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  transition: opacity 0.3s ease;
  width: 100%;
  z-index: 1;
}

.plyr__preview-scrubbing--is-shown {
  opacity: 1;
}

.plyr__preview-scrubbing img {
  height: 100%;
  left: 0;
  max-height: none;
  max-width: none;
  object-fit: contain;
  position: absolute;
  top: 0;
  width: 100%;
}

.plyr--no-transition {
  transition: none !important;
}

.plyr__sr-only {
  clip: rect(1px, 1px, 1px, 1px);
  overflow: hidden;
  border: 0 !important;
  height: 1px !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
}

.plyr [hidden] {
  display: none !important;
}

/* #Model Viewer
================================================== */
.shopify-model-viewer-ui {
  position: relative;
  display: block;
  cursor: pointer;
}

.shopify-model-viewer-ui model-viewer {
  transform: translateZ(0);
  z-index: 1;
}

.shopify-model-viewer-ui model-viewer.shopify-model-viewer-ui__disabled {
  pointer-events: none;
}

.shopify-model-viewer-ui.shopify-model-viewer-ui--fullscreen model-viewer {
  position: relative;
  width: 100vw;
  height: 100vh;
}

.shopify-model-viewer-ui.shopify-model-viewer-ui--fullscreen .shopify-model-viewer-ui__control-icon--exit-fullscreen {
  display: block;
}

.shopify-model-viewer-ui.shopify-model-viewer-ui--fullscreen .shopify-model-viewer-ui__control-icon--enter-fullscreen {
  display: none;
}

.shopify-model-viewer-ui.shopify-model-viewer-ui--desktop.shopify-model-viewer-ui--child-focused .shopify-model-viewer-ui__controls-area, .shopify-model-viewer-ui.shopify-model-viewer-ui--desktop:hover .shopify-model-viewer-ui__controls-area {
  opacity: 1;
}

.shopify-model-viewer-ui:not(.shopify-model-viewer-ui--desktop) .shopify-model-viewer-ui__controls-area {
  display: none;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
  display: flex;
  flex-direction: column;
  background: #fff;
  opacity: 0;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 1;
  transition: opacity 0.1s linear;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area:not(.shopify-model-viewer-ui__controls-area--playing) {
  display: none;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button {
  color: #3a3a3a;
  border-radius: 0;
  border: none;
  margin: 0;
  cursor: pointer;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button:not(.focus-visible) {
  outline: 0;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control {
  padding: 0;
  height: 44px;
  width: 44px;
  background: 0 0;
  position: relative;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover {
  color: rgba(0, 0, 0, 0.55);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control.focus-visible:focus, .shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active {
  color: rgba(0, 0, 0, 0.55);
  background: rgba(0, 0, 0, 0.05);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:not(:last-child):after {
  position: absolute;
  content: "";
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  width: 28px;
  bottom: 0;
  right: 8px;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__control-icon {
  width: 44px;
  height: 44px;
  fill: none;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster {
  background: #fff;
  position: absolute;
  border: 1px solid rgba(0, 0, 0, 0.05);
  top: 50%;
  left: 50%;
  padding: 0;
  transform: translate3d(-50%, -50%, 0);
  height: 62px;
  width: 62px;
  z-index: 1;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus, .shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover {
  color: rgba(0, 0, 0, 0.55);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__poster-control-icon {
  width: 60px;
  height: 60px;
  z-index: 1;
  fill: none;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__control-icon--exit-fullscreen {
  display: none;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__control-icon--enter-fullscreen {
  display: block;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__spritesheet {
  display: none;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

/* # Variables
================================================== */
{% assign color_background = settings.shop_bg_color %}
{% assign border_color = settings.border_color %}
{% liquid
    assign text = settings.regular_color
    assign text_invert = '#ffffff'

    assign lightness = text | color_extract: 'lightness'

    if lightness > 50
      assign text_invert = settings.regular_color
    endif
  %}
{% liquid
    assign link = settings.link_color
    assign link_invert = '#ffffff'

    assign lightness = link | color_extract: 'lightness'

    if lightness > 50
      assign link_invert = settings.regular_color
    endif
  %}
{% liquid
    assign sale_sticker_color = settings.banner_sale
    assign sale_sticker_color_text = '#ffffff'
    assign banner_sale_text = settings.banner_sale_text

    assign lightness = sale_sticker_color | color_extract: 'lightness'

    if lightness > 50
      assign sale_sticker_color_text = settings.regular_color
    endif
  %}
{% liquid
    assign new_sticker_color = settings.banner_new
    assign new_sticker_color_text = '#ffffff'

    assign lightness = new_sticker_color | color_extract: 'lightness'

    if lightness > 50
      assign new_sticker_color_text = settings.regular_color
    endif
  %}
{% liquid
    assign bestseller_sticker_color = settings.banner_bestseller
    assign bestseller_sticker_color_text = '#ffffff'

    assign lightness = bestseller_sticker_color | color_extract: 'lightness'

    if lightness > 50
      assign bestseller_sticker_color_text = settings.regular_color
    endif
  %}
{% liquid
    assign comingsoon_sticker_color = settings.banner_comingsoon
    assign comingsoon_sticker_color_text = '#ffffff'

    assign lightness = comingsoon_sticker_color | color_extract: 'lightness'

    if lightness > 50
      assign comingsoon_sticker_color_text = settings.regular_color
    endif
  %}
{% liquid
    assign staffpick_sticker_color = settings.banner_staffpick
    assign staffpick_sticker_color_text = '#ffffff'

    assign lightness = staffpick_sticker_color | color_extract: 'lightness'

    if lightness > 50
      assign staffpick_sticker_color_text = settings.regular_color
    endif
  %}
{% liquid
    assign preorder_sticker_color = settings.banner_preorder
    assign preorder_sticker_color_text = '#ffffff'

    assign lightness = preorder_sticker_color | color_extract: 'lightness'

    if lightness > 50
      assign preorder_sticker_color_text = settings.regular_color
    endif
  %}
{% assign drop_down_menu_active_color = settings.dropdown_link_color %}
/* # Mixins
================================================== */
ul.tabs, .tabs, .pagination-previous,
.pagination-next,
.pagination-link,
.pagination-ellipsis, .file, .breadcrumb, .button, .age-gate__confirm_btn, .is-unselectable, .close, .delete {
  -webkit-user-select: none;
          user-select: none;
}

{% case settings.icon %}
    {% when 'icon_solid' %}
      {% assign icon_down_caret = '"\e902"' %}
      {% assign icon_x = '"\e903"' %}
    {% when 'icon_outline' %}
      {% assign icon_down_caret = '"\e904"' %}
      {% assign icon_x = '"\e905"' %}
    {% when 'icon_brush' %}
      {% assign icon_down_caret = '"\e900"' %}
      {% assign icon_x = '"\e901"' %}
  {% endcase %}
.mobile-menu-link::after, .navbar-link:not(.is-arrowless)::after, .select:not(.is-multiple):not(.is-loading):not(.is-arrowless)::after, .age-gate__select-wrapper:not(.is-multiple):not(.is-loading):not(.is-arrowless)::after {
  font-family: "flex-icon" !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: {{ icon_down_caret }};
  display: block;
  pointer-events: none;
  position: absolute;
  transform-origin: center;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8em;
}

.tabs:not(:last-child), .highlight:not(:last-child), .block:not(:last-child), .notification:not(:last-child), .content:not(:last-child),
.shopify-policy__body:not(:last-child), .title:not(:last-child),
.subtitle:not(:last-child) {
  margin-bottom: 1.5rem;
}

.delete {
  -webkit-appearance: none;
          appearance: none;
  background-color: rgba(10, 10, 10, 0.2);
  border: none;
  border-radius: 290486px;
  cursor: pointer;
  pointer-events: auto;
  display: inline-block;
  flex-grow: 0;
  flex-shrink: 0;
  font-size: 0;
  height: 20px;
  max-height: 20px;
  max-width: 20px;
  min-height: 20px;
  min-width: 20px;
  outline: none;
  position: relative;
  vertical-align: top;
  width: 20px;
}
.delete::before, .delete::after {
  background-color: hsl(0, 0%, 100%);
  content: "";
  display: block;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform-origin: center center;
}
.delete::before {
  height: 2px;
  width: 50%;
}
.delete::after {
  height: 50%;
  width: 2px;
}
.delete:hover, .delete:focus {
  background-color: rgba(10, 10, 10, 0.3);
}
.delete:active {
  background-color: rgba(10, 10, 10, 0.4);
}
.is-small.delete {
  height: 16px;
  max-height: 16px;
  max-width: 16px;
  min-height: 16px;
  min-width: 16px;
  width: 16px;
}
.is-medium.delete {
  height: 24px;
  max-height: 24px;
  max-width: 24px;
  min-height: 24px;
  min-width: 24px;
  width: 24px;
}
.is-large.delete {
  height: 32px;
  max-height: 32px;
  max-width: 32px;
  min-height: 32px;
  min-width: 32px;
  width: 32px;
}

.close {
  -webkit-appearance: none;
          appearance: none;
  background-color: rgba(10, 10, 10, 0.2);
  border: none;
  border-radius: 290486px;
  cursor: pointer;
  pointer-events: auto;
  display: inline-block;
  display: inline-flex;
  flex-grow: 0;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 20px;
  max-height: 20px;
  max-width: 20px;
  min-height: 20px;
  min-width: 20px;
  width: 20px;
  padding: 0;
  color: currentColor;
  transition: 0.2s linear;
}
.close .icon {
  fill: currentColor;
  height: 50%;
  max-height: 50%;
  max-width: 50%;
  min-height: 50%;
  min-width: 50%;
  width: 50%;
}
.close:hover, .close:focus {
  background-color: rgba(10, 10, 10, 0.3);
}
.close:active {
  background-color: rgba(10, 10, 10, 0.4);
}
.is-small.close {
  height: 16px;
  max-height: 16px;
  max-width: 16px;
  min-height: 16px;
  min-width: 16px;
  width: 16px;
}
.is-medium.close {
  height: 24px;
  max-height: 24px;
  max-width: 24px;
  min-height: 24px;
  min-width: 24px;
  width: 24px;
}
.is-large.close {
  height: 32px;
  max-height: 32px;
  max-width: 32px;
  min-height: 32px;
  min-width: 32px;
  width: 32px;
}

.control.is-loading::after, .select.is-loading::after, .is-loading.age-gate__select-wrapper::after, .loader, .button.is-loading::after, .is-loading.age-gate__confirm_btn::after {
  animation: spinAround 500ms infinite linear;
  border: 2px solid hsl(0, 0%, 86%);
  border-radius: 290486px;
  border-right-color: transparent;
  border-top-color: transparent;
  content: "";
  display: block;
  height: 1em;
  position: relative;
  width: 1em;
}

/* # Reset
================================================== */
/*! minireset.css v0.0.3 | MIT License | github.com/jgthms/minireset.css */
html,
body,
p,
ol,
ul,
li,
dl,
dt,
dd,
blockquote,
figure,
fieldset,
legend,
textarea,
pre,
iframe,
hr,
h1,
.age-gate__heading,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

h1, .age-gate__heading,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: normal;
}

ul {
  list-style: none;
}

button,
input,
select,
textarea {
  margin: 0;
}

html {
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: inherit;
}

img,
audio,
video {
  height: auto;
  max-width: 100%;
}

iframe {
  border: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
  text-align: left;
}

/* # Generic
================================================== */
html {
  min-width: 300px;
  overflow-x: hidden;
  overflow-y: auto;
  font-size: {{ settings.regular_font_size | append: 'px' }};
  background-color: {{ settings.shop_bg_color }};
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -webkit-text-size-adjust: 100%;
          text-size-adjust: 100%;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

main {
  min-height: 30vh;
}

article,
aside,
figure,
footer,
header,
main,
hgroup,
section {
  display: block;
}

body,
button,
input,
select,
textarea {
  font-family: {{ settings.regular__font.family }}, {{ settings.regular__font.fallback_families }};
}

code,
pre {
  -moz-osx-font-smoothing: auto;
  -webkit-font-smoothing: auto;
  font-family: monospace;
}

a {
  color: {{ link }};
  text-decoration: none;
  cursor: pointer;
  transition: color 0.3s ease-in-out;
}
a strong {
  color: currentColor;
}
a:hover {
  color: {{ settings.link_hover_color }};
}

code {
  padding: 0.25em 0.5em 0.25em;
  font-size: 0.875em;
  font-weight: normal;
  color: hsl(348, 100%, 61%);
  background-color: {{ color_background }};
}

hr {
  display: block;
  height: 2px;
  margin: 1.5rem 0;
  background-color: {{ color_background }};
  border: none;
}

img {
  max-width: 100%;
  height: auto;
}

input[type=checkbox],
input[type=radio] {
  vertical-align: baseline;
}

small {
  font-size: 0.875rem;
}

span {
  font-style: inherit;
  font-weight: inherit;
}

strong {
  font-weight: 700;
}

fieldset {
  border: none;
}

pre {
  -webkit-overflow-scrolling: touch;
  padding: 1.25rem 1.5rem;
  overflow-x: auto;
  font-size: 0.875rem;
  color: {{ text }};
  word-wrap: normal;
  white-space: pre;
  background-color: {{ color_background }};
}
pre code {
  padding: 0;
  font-size: 1em;
  color: currentColor;
  background-color: transparent;
}

table td,
table th {
  text-align: left;
  vertical-align: top;
}
table th {
  color: hsl(0, 0%, 21%);
}

.table.is-bordered td,
.table.is-bordered th {
  padding: 5px;
  border: 1px solid {{ border_color }};
}

.table.is-striped tbody tr:not(.is-selected):nth-child(even) {
  background-color: hsl(0, 0%, 98%);
}

.noscript {
  display: none;
}

.site-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.8);
  opacity: 1;
  visibility: visible;
  transition: opacity 350ms;
}
.site-overlay.site-overlay--hidden {
  opacity: 0;
  visibility: hidden;
}
[data-site-header-layout=search_focus] .site-overlay, [data-site-header-layout=vertical] .site-overlay {
  transition-duration: 300ms;
}

/* # Fonts
================================================== */
{%- capture font_settings_list -%}
    {{ settings.regular__font | font_face: font_display: 'swap' }}~
    {{ settings.regular__font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.regular__font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.regular__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.logo__font | font_face: font_display: 'swap' }}~
    {{ settings.logo__font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.logo__font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.logo__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.heading__font | font_face: font_display: 'swap' }}~
    {{ settings.heading__font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.heading__font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.heading__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.preheading_font | font_face: font_display: 'swap' }}~
    {{ settings.preheading_font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.preheading_font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.preheading_font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.subheading_font | font_face: font_display: 'swap' }}~
    {{ settings.subheading_font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.subheading_font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.subheading_font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.nav__font | font_face: font_display: 'swap' }}~
    {{ settings.nav__font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.nav__font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.nav__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.nav__font | font_modify: 'weight', 'lighter' | font_face: font_display: 'swap' }}~
    {{ settings.nav__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'lighter' | font_face: font_display: 'swap' }}~
    {{ settings.button__font | font_face: font_display: 'swap' }}~
    {{ settings.button__font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.button__font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.button__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.button__font | font_modify: 'weight', 'lighter' | font_face: font_display: 'swap' }}~
    {{ settings.button__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'lighter' | font_face: font_display: 'swap' }}~
    {{ settings.banner_heading__font | font_face: font_display: 'swap' }}~
    {{ settings.banner_heading__font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.banner_heading__font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.banner_heading__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.banner_text__font | font_face: font_display: 'swap' }}~
    {{ settings.banner_text__font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
    {{ settings.banner_text__font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
    {{ settings.banner_text__font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}

  {%- endcapture -%}
  {%- assign font_array = font_settings_list | split: '~' -%}

  {%- for name in font_array -%}
    {%- unless name contains 'error' -%}
      {{ name }}
    {%- endunless -%}
  {%- endfor -%}
@font-face {
  font-family: "flex-icon";
  src: url({{ 'flex-icon.ttf' | asset_url }}) format("truetype"), url({{ 'flex-icon.woff' | asset_url }}) format("woff"), url({{ 'flex-icon.svg' | asset_url }}) format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
/* # Typography
================================================== */
body {
  font-family: {{ settings.regular__font.family }}, {{ settings.regular__font.fallback_families }};
  font-size: {{ settings.regular_font_size | append: 'px' }};
  font-weight: {{ settings.regular__font.weight }};
  line-height: 1.5;
  color: {{ settings.regular_color }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, .age-gate__heading,
.h1 {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 798px) {
  h1, .age-gate__heading,
  .h1 {
    font-size: {{ settings.heading_size | times: 0.8 | floor | append: 'px' }};
  }
}
h1 > a, .age-gate__heading > a,
h1 > a:link,
h1 > a:visited,
.h1 > a,
.h1 > a:link,
.h1 > a:visited {
  color: {{ settings.heading_color }};
}
h1 > a:hover, .age-gate__heading > a:hover,
h1 > a:focus,
.age-gate__heading > a:focus,
.h1 > a:hover,
.h1 > a:focus {
  color: {{ settings.link_hover_color }};
}

h2,
.h2 {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | times: 0.9 | floor | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 798px) {
  h2,
  .h2 {
    font-size: {{ settings.heading_size | times: 0.9 | floor | times: 0.8 | floor | append: 'px' }};
  }
}
h2 > a,
h2 > a:link,
h2 > a:visited,
.h2 > a,
.h2 > a:link,
.h2 > a:visited {
  color: {{ settings.heading_color }};
}
h2 > a:hover,
h2 > a:focus,
.h2 > a:hover,
.h2 > a:focus {
  color: {{ settings.link_hover_color }};
}

h3,
.h3 {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | times: 0.8 | floor | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 798px) {
  h3,
  .h3 {
    font-size: {{ settings.heading_size | times: 0.8 | floor | times: 0.8 | floor | append: 'px' }};
  }
}
h3 > a,
h3 > a:link,
h3 > a:visited,
.h3 > a,
.h3 > a:link,
.h3 > a:visited {
  color: {{ settings.heading_color }};
}
h3 > a:hover,
h3 > a:focus,
.h3 > a:hover,
.h3 > a:focus {
  color: {{ settings.link_hover_color }};
}

h4,
.h4 {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | times: 0.7 | floor | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 798px) {
  h4,
  .h4 {
    font-size: {{ settings.heading_size | times: 0.7 | floor | times: 0.8 | floor | append: 'px' }};
  }
}
h4 > a,
h4 > a:link,
h4 > a:visited,
.h4 > a,
.h4 > a:link,
.h4 > a:visited {
  color: {{ settings.heading_color }};
}
h4 > a:hover,
h4 > a:focus,
.h4 > a:hover,
.h4 > a:focus {
  color: {{ settings.link_hover_color }};
}

h5,
.h5 {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | times: 0.65 | floor | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 798px) {
  h5,
  .h5 {
    font-size: {{ settings.heading_size | times: 0.65 | floor | times: 0.8 | floor | append: 'px' }};
  }
}
h5 > a,
h5 > a:link,
h5 > a:visited,
.h5 > a,
.h5 > a:link,
.h5 > a:visited {
  color: {{ settings.heading_color }};
}
h5 > a:hover,
h5 > a:focus,
.h5 > a:hover,
.h5 > a:focus {
  color: {{ settings.link_hover_color }};
}

h6,
.h6 {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | times: 0.6 | floor | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 798px) {
  h6,
  .h6 {
    font-size: {{ settings.heading_size | times: 0.6 | floor | times: 0.8 | floor | append: 'px' }};
  }
}
h6 > a,
h6 > a:link,
h6 > a:visited,
.h6 > a,
.h6 > a:link,
.h6 > a:visited {
  color: {{ settings.heading_color }};
}
h6 > a:hover,
h6 > a:focus,
.h6 > a:hover,
.h6 > a:focus {
  color: {{ settings.link_hover_color }};
}

a {
  color: {{ link }};
  text-decoration: none;
  cursor: pointer;
}
a strong {
  color: currentColor;
}
a:hover {
  color: {{ settings.link_hover_color }};
}

sub {
  font-size: smaller;
}

em,
i {
  font-style: italic;
}

strong,
b {
  font-weight: bold;
}

small {
  font-size: smaller;
}

blockquote {
  font-size: larger;
  font-style: italic;
  line-height: 1.2;
}
blockquote cite {
  display: block;
  font-size: smaller;
}
blockquote cite::before {
  content: "— ";
}

/* Text alignment */
.text-align-center {
  text-align: center;
}

.text-align-left,
.text-align-start {
  text-align: left;
}

.text-align-right,
.text-align-end {
  text-align: right;
}

.text-align-justify {
  text-align: justify;
}

/* Mobile text alignment */
@media only screen and (max-width: 798px) {
  .text-align--mobile-left {
    text-align: left;
  }
}

@media only screen and (max-width: 798px) {
  .text-align--mobile-center {
    text-align: center;
  }
}

@media only screen and (max-width: 798px) {
  .text-align--mobile-right {
    text-align: right;
  }
}

@media only screen and (max-width: 798px) {
  .text-align--mobile-justify {
    text-align: justify;
  }
}

.is-capitalized {
  text-transform: capitalize;
}

.is-lowercase {
  text-transform: lowercase;
}

.is-uppercase {
  text-transform: uppercase;
}

.is-italic {
  font-style: italic !important;
}

.text-is-large {
  font-size: larger;
}

.text-is-medium {
  font-size: initial;
}

.text-is-small {
  font-size: smaller;
}

.title,
.subtitle {
  overflow-wrap: break-word;
}
.title em,
.title span,
.subtitle em,
.subtitle span {
  font-weight: inherit;
}
.title sub,
.subtitle sub {
  font-size: 0.75em;
}
.title sup,
.subtitle sup {
  font-size: 0.75em;
}
.title .tag,
.subtitle .tag {
  vertical-align: middle;
}

.title {
  font-size: {{ settings.heading_size | append: 'px' }}; 
  font-weight: {{ settings.heading__font.weight }};
  line-height: 1.125;
  color: {{ settings.heading_color }};
}
.title strong {
  font-weight: inherit;
  color: inherit;
}
.title + .highlight {
  margin-top: -0.75rem;
}
.title:not(.is-spaced) + .subtitle:not(.modal_price) {
  margin-top: -1.25rem;
}
.title.is-1 {
  font-size: 3rem;
}
.title.is-2 {
  font-size: 2.5rem;
}
.title.is-3 {
  font-size: 2rem;
}
.title.is-4 {
  font-size: 1.5rem;
}
.title.is-5 {
  font-size: 1.25rem;
}
.title.is-6 {
  font-size: 1rem;
}
.title.is-7 {
  font-size: 0.875rem;
}
.title.is-8 {
  font-size: 0.75rem;
}

.subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.25;
  color: {{ settings.heading_color }};
}
.subtitle strong {
  font-weight: 600;
  color: inherit;
}
.subtitle:not(.is-spaced) + .title {
  margin-top: -1.25rem;
}
.subtitle.is-1 {
  font-size: 3rem;
}
.subtitle.is-2 {
  font-size: 2.5rem;
}
.subtitle.is-3 {
  font-size: 2rem;
}
.subtitle.is-4 {
  font-size: 1.5rem;
}
.subtitle.is-5 {
  font-size: 1.25rem;
}
.subtitle.is-6 {
  font-size: 1rem;
}
.subtitle.is-7 {
  font-size: 0.875rem;
}
.subtitle.is-8 {
  font-size: 0.75rem;
}

.large-heading {
  padding-bottom: 40px;
}

.signature {
  font-family: fantasy;
}

{% liquid
    assign divider_border_width = settings.heading_divider_width | append: 'px'
    if settings.heading_divider_width == 1
      assign divider_border_width = 'thin'
    endif
  %}
.heading-divider:not(.heading-divider--vertical) {
  border-color: {{ settings.divider_color }};
  border-style: solid;
  border-width: {{ divider_border_width }};
  border-bottom: none;
}

.heading-divider--short {
  width: 70px;
}

.heading-divider--long {
  width: 100%;
}

.heading-divider--vertical {
  width: {{ settings.heading_divider_width | append: 'px' }};
  height: 40px;
  background: {{ settings.divider_color }};
}

@keyframes spinAround {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
/* # Helpers
================================================== */
.scroll-locked {
  overflow: hidden;
  touch-action: manipulation;
}

.is-flex {
  display: flex;
}

.is-inline-flex {
  display: inline-flex;
}

/* Alignment */
.is-align-start,
.is-align-left {
  align-items: flex-start;
}

.is-align-end,
.is-align-right {
  align-items: flex-end;
}

.is-align-self-end,
.is-align-self-right {
  align-self: flex-end;
}

.is-align-baseline {
  align-items: baseline;
}

.is-align-center {
  align-items: center;
}

.is-align-stretch {
  align-items: stretch;
}

/* Mobile alignment */
@media only screen and (max-width: 798px) {
  .is-align--mobile-start {
    align-items: flex-start;
  }
}

@media only screen and (max-width: 798px) {
  .is-align--mobile-center {
    align-items: center;
  }
}

@media only screen and (max-width: 798px) {
  .is-align--mobile-end {
    align-items: flex-end;
  }
}

/* Justify */
.is-justify-start,
.is-justify-left {
  justify-content: flex-start;
}

.is-justify-end,
.is-justify-right {
  justify-content: flex-end;
}

.is-justify-center {
  justify-content: center;
}

.is-justify-space-around {
  justify-content: space-around;
}

.is-justify-space-between {
  justify-content: space-between;
}

/* Mobile justify */
@media only screen and (max-width: 798px) {
  .is-mobile-justify-start {
    justify-content: flex-start;
  }
}

@media only screen and (max-width: 798px) {
  .is-mobile-justify-center {
    justify-content: center;
  }
}

@media only screen and (max-width: 798px) {
  .is-mobile-justify-end {
    justify-content: flex-end;
  }
}

.is-flex-nowrap {
  flex-wrap: nowrap;
}

.is-flex-wrap {
  flex-wrap: wrap;
}

.is-flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}

.is-flex-row {
  flex-direction: row;
}

.is-flex-row-reverse {
  flex-direction: row-reverse;
}

.is-flex-column {
  flex-direction: column;
}

.is-flex-column-reverse {
  flex-direction: column-reverse;
}

@media only screen and (max-width: 480px) {
  .is-flex-column--mobile {
    flex-direction: column;
  }
}

@media only screen and (max-width: 480px) {
  .is-flex-column-reverse-mobile {
    flex-direction: column-reverse;
  }
}

.is-order-aligned-left {
  order: -1;
}
@media only screen and (max-width: 480px) {
  .is-order-aligned-left {
    order: inherit;
  }
}

.is-order-aligned-right {
  order: 1;
}
@media only screen and (max-width: 480px) {
  .is-order-aligned-right {
    order: inherit;
  }
}

.is-pulled-left {
  float: left !important;
}

.is-pulled-right {
  float: right !important;
}

.is-clipped {
  overflow: hidden !important;
}

.is-hidden {
  display: none !important;
}

.is-visible {
  display: block !important;
}

.is-sr-only {
  border: none !important;
  clip: rect(0, 0, 0, 0) !important;
  height: 0.01em !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  white-space: nowrap !important;
  width: 0.01em !important;
}

.visually-hidden, .age-gate__select-label {
  position: absolute !important;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  border: 0;
}

.is-invisible {
  visibility: hidden !important;
}

@media only screen and (min-width: 481px) and (max-width: 798px) {
  .is-hidden-small {
    display: none !important;
  }
}
@media only screen and (min-width: 799px) and (max-width: 1024px) {
  .is-hidden-medium {
    display: none !important;
  }
}
@media only screen and (min-width: 1025px) and (max-width: 1400px) {
  .is-hidden-large {
    display: none !important;
  }
}
@media only screen and (max-width: 480px) {
  .is-hidden-small-down {
    display: none !important;
  }
}
@media only screen and (min-width: 481px) {
  .is-hidden-small-up {
    display: none !important;
  }
}
@media only screen and (max-width: 798px) {
  .is-hidden-mobile-only {
    display: none !important;
  }
}
@media only screen and (min-width: 799px) {
  .is-hidden-desktop-only {
    display: none !important;
  }
}
.is-invisible {
  visibility: hidden !important;
}

.has-padding-top {
  padding-top: 20px;
}

.has-padding-bottom {
  padding-bottom: 20px;
}

.has-padding-left {
  padding-left: 20px;
}

.has-padding-right {
  padding-right: 20px;
}

.has-padding {
  padding: 20px;
}

.has-large-padding-top {
  padding-top: calc(20px * 2);
}

.has-large-padding-bottom {
  padding-bottom: calc(20px * 2);
}

.has-small-padding-top {
  padding-top: calc(20px / 2);
}

.has-small-padding-bottom {
  padding-bottom: calc(20px / 2);
}

.has-margin-top {
  margin-top: 20px;
}

.has-margin-bottom {
  margin-bottom: 20px;
}

.has-margin-left {
  margin-left: 20px;
}

.has-margin-right {
  margin-right: 20px;
}

.has-margin {
  margin: 20px;
}

.border--true {
  border: 1px solid {{ border_color }};
}

.border-top {
  border: none;
  border-top: 1px solid {{ border_color }};
}

.border-bottom {
  border: none;
  border-bottom: 1px solid {{ border_color }};
}

.border-right {
  border: none;
  border-right: 1px solid {{ border_color }};
}

.border-left {
  border: none;
  border-left: 1px solid {{ border_color }};
}

.box {
  border-radius: 6px;
  box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1), 0 0 0 1px rgba(10, 10, 10, 0.1);
}

.container-border--top::before {
  display: block;
  content: "";
  height: 0;
  width: calc(100% - 20px);
  border-top: thin solid {{ settings.border_color }};
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  float: left;
}
@media only screen and (max-width: 480px) {
  .container-border--top::before {
    width: 100%;
  }
}

.container-border--bottom::after {
  display: block;
  content: "";
  height: 0;
  width: calc(100% - 20px);
  border-top: thin solid {{ settings.border_color }};
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  clear: both;
}
@media only screen and (max-width: 480px) {
  .container-border--bottom::after {
    width: 100%;
  }
}

.has-columns--2 {
  column-count: 2;
}
@media only screen and (max-width: 480px) {
  .has-columns--2 {
    column-count: 1;
  }
}

.has-columns--3 {
  column-count: 3;
}
@media only screen and (max-width: 480px) {
  .has-columns--3 {
    column-count: 1;
  }
}

.is-marginless {
  margin: 0 !important;
}

.is-paddingless {
  padding: 0 !important;
}

.is-radiusless {
  border-radius: 0 !important;
}

.is-shadowless {
  box-shadow: none !important;
}

.is-relative {
  position: relative;
}

@media only screen and (max-width: 798px) {
  .is-fullwidth-mobile {
    width: 100vw !important;
    padding: 20px !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
  }
}

/* # Vendor override styles
================================================== */
[data-scroll-class] {
  opacity: 0;
  animation-delay: 0.2s;
}

.fancybox-thumbs__list a:before {
  border: 6px solid {{ settings.link_color }};
}

.fancybox-button {
  transition: opacity 0.3s ease-in-out;
}

.flickity-viewport {
  width: 100%;
}

.flickity-prev-next-button {
  position: absolute;
  display: flex;
}

.flickity-prev-next-button .flickity-button-icon {
  position: static;
  top: initial;
  left: initial;
  margin: auto;
}

.flickity-button {
  transition: background-color 0.3s ease-in-out;
}

.shopify-challenge__container {
  padding: 40px 0;
}

.flickity-resize .image-slideshow__slide {
  min-height: 100%;
}

.image-slideshow--fade .image-slideshow__slide {
  opacity: 0 !important;
  transition: opacity 1s ease !important;
}
.image-slideshow--fade .image-slideshow__slide.is-selected {
  opacity: 1 !important;
}

.lazyframe {
  background-color: #000 !important;
}

.slider .flickity-slider li,
.flexslider .flickity-slider li {
  width: 100%;
  list-style: none;
  padding-left: 0.1%; /* Fix for Flickity pixel line */
  padding-right: 0.1%; /* Fix for Flickity pixel line */
  margin: 0 !important; /* Overwrite content margin */
}
.slider .flickity-slider li img,
.flexslider .flickity-slider li img {
  width: 100%;
}
.slider .flickity-page-dots,
.flexslider .flickity-page-dots {
  margin: 0;
}

.zoomImg {
  background-color: {{ settings.shop_bg_color }};
}

/* # Grid/layout
================================================== */
.index {
  display: flex;
  flex-wrap: wrap;
}

#template-index {
  width: 100%;
}

.index-sections {
  overflow: hidden;
}

.dynamic-sections {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.shopify-section {
  width: 100%;
}
@media only screen and (max-width: 480px) {
  .shopify-section {
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.section {
  max-width: 1200px;
  width: 95%;
  margin-left: auto;
  margin-right: auto;
}
.section.has-background {
  width: calc(95% - 20px);
  max-width: calc(1200px - 20px);
  padding-left: calc(20px / 2);
  padding-right: calc(20px / 2);
}
.section.is-width-half {
  height: 100%;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: none;
}
@media only screen and (max-width: 798px) {
  .section.is-width-half {
    width: 100%;
  }
}
@media only screen and (max-width: 480px) {
  .section.is-width-half .container.small-down--has-limit {
    max-width: 1200px;
    width: 95%;
  }
}
.section.is-width-wide {
  width: 100%;
  max-width: none;
}
.section.is-width-wide .container.has-limit {
  max-width: 1200px;
  width: 95%;
}
@media only screen and (max-width: 480px) {
  .section.is-width-wide .container.has-limit {
    width: 100%;
  }
}
@media only screen and (max-width: 480px) {
  .section.is-width-wide .container.small-down--has-limit {
    max-width: 1200px;
    width: 95%;
    flex-wrap:nowrap;
  }
}

@media only screen and (max-width: 480px) {
  .section.section__wrapper {
    max-width: 1200px;
    width: 95%;
  }
}

.container {
  position: relative;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  flex: auto;
}
.container .column,
.container .columns {
  margin-left: calc(20px / 2);
  margin-right: calc(20px / 2);
}
.container.is-small {
  width: 50%;
}
@media only screen and (max-width: 480px) {
  .container.is-small {
    width: 60%;
  }
}
.container.is-medium {
  width: 70%;
}
@media only screen and (max-width: 480px) {
  .container.is-medium {
    width: 80%;
  }
}
.container.has-column-padding-bottom .column,
.container.has-column-padding-bottom .columns {
  margin-bottom: 20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
@media only screen and (max-width: 798px) {
  .row .column,
  .row .columns {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
}

[class*=offset-by] {
  position: relative;
}
@media only screen and (max-width: 480px) {
  [class*=offset-by] {
    position: static;
  }
}

@media only screen and (max-width: 798px) {
  .has-gutter-enabled .has-gutter--mobile {
    margin-bottom: 20px;
  }
}

.has-gutter-enabled.has-multirow-blocks .has-gutter {
  margin-bottom: 20px;
}

/*================ Build Base Grid Classes ================*/
.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.one {
  width: calc(6.25% - 20px);
}

.two {
  width: calc(12.5% - 20px);
}

.three {
  width: calc(18.75% - 20px);
}

.four {
  width: calc(25% - 20px);
}

.five {
  width: calc(31.25% - 20px);
}

.six {
  width: calc(37.5% - 20px);
}

.seven {
  width: calc(43.75% - 20px);
}

.eight {
  width: calc(50% - 20px);
}

.nine {
  width: calc(56.25% - 20px);
}

.ten {
  width: calc(62.5% - 20px);
}

.eleven {
  width: calc(68.75% - 20px);
}

.twelve {
  width: calc(75% - 20px);
}

.thirteen {
  width: calc(81.25% - 20px);
}

.fourteen {
  width: calc(87.5% - 20px);
}

.fifteen {
  width: calc(93.75% - 20px);
}

.sixteen {
  width: calc(100% - 20px);
}

.one-whole {
  width: calc(100% - 20px);
}

.one-half {
  width: calc(50% - 20px);
}

.one-third {
  width: calc(33.3333333333% - 20px);
}

.two-thirds {
  width: calc(66.6666666667% - 20px);
}

.one-fourth {
  width: calc(25% - 20px);
}

.two-fourths {
  width: calc(50% - 20px);
}

.three-fourths {
  width: calc(75% - 20px);
}

.one-fifth {
  width: calc(20% - 20px);
}

.two-fifths {
  width: calc(40% - 20px);
}

.three-fifths {
  width: calc(60% - 20px);
}

.four-fifths {
  width: calc(80% - 20px);
}

.one-sixth {
  width: calc(16.6666666667% - 20px);
}

.two-sixths {
  width: calc(33.3333333333% - 20px);
}

.three-sixths {
  width: calc(50% - 20px);
}

.four-sixths {
  width: calc(66.6666666667% - 20px);
}

.five-sixths {
  width: calc(83.3333333333% - 20px);
}

.one-seventh {
  width: calc(14.2857142857% - 20px);
}

.two-sevenths {
  width: calc(28.5714285714% - 20px);
}

.three-sevenths {
  width: calc(42.8571428571% - 20px);
}

.four-sevenths {
  width: calc(57.1428571429% - 20px);
}

.five-sevenths {
  width: calc(71.4285714286% - 20px);
}

.one-eighth {
  width: calc(12.5% - 20px);
}

.two-eighths {
  width: calc(25% - 20px);
}

.three-eighths {
  width: calc(37.5% - 20px);
}

.four-eighths {
  width: calc(50% - 20px);
}

.five-eighths {
  width: calc(62.5% - 20px);
}

.six-eighths {
  width: calc(75% - 20px);
}

.seven-eighths {
  width: calc(87.5% - 20px);
}

.one-tenth {
  width: calc(10% - 20px);
}

.two-tenths {
  width: calc(20% - 20px);
}

.three-tenths {
  width: calc(30% - 20px);
}

.four-tenths {
  width: calc(40% - 20px);
}

.five-tenths {
  width: calc(50% - 20px);
}

.six-tenths {
  width: calc(60% - 20px);
}

.seven-tenths {
  width: calc(70% - 20px);
}

.eight-tenths {
  width: calc(80% - 20px);
}

.nine-tenths {
  width: calc(90% - 20px);
}

.one-twelfth {
  width: calc(8.3333333333% - 20px);
}

.two-twelfths {
  width: calc(16.6666666667% - 20px);
}

.three-twelfths {
  width: calc(25% - 20px);
}

.four-twelfths {
  width: calc(33.3333333333% - 20px);
}

.five-twelfths {
  width: calc(41.6666666667% - 20px);
}

.six-twelfths {
  width: calc(50% - 20px);
}

.seven-twelfths {
  width: calc(58.3333333333% - 20px);
}

.eight-twelfths {
  width: calc(66.6666666667% - 20px);
}

.nine-twelfths {
  width: calc(75% - 20px);
}

.ten-twelfths {
  width: calc(83.3333333333% - 20px);
}

.eleven-twelfths {
  width: calc(91.6666666667% - 20px);
}

.offset-by-one {
  left: calc(6.25%);
}

.offset-by-two {
  left: calc(12.5%);
}

.offset-by-three {
  left: calc(18.75%);
}

.offset-by-four {
  left: calc(25%);
}

.offset-by-five {
  left: calc(31.25%);
}

.offset-by-six {
  left: calc(37.5%);
}

.offset-by-seven {
  left: calc(43.75%);
}

.offset-by-eight {
  left: calc(50%);
}

.offset-by-nine {
  left: calc(56.25%);
}

.offset-by-ten {
  left: calc(62.5%);
}

.offset-by-eleven {
  left: calc(68.75%);
}

.offset-by-twelve {
  left: calc(75%);
}

.offset-by-thirteen {
  left: calc(81.25%);
}

.offset-by-fourteen {
  left: calc(87.5%);
}

.offset-by-fifteen {
  left: calc(93.75%);
}

.equal-columns--outside-trim .one-half:nth-of-type(2n),
.equal-columns--outside-trim .eight:nth-of-type(2n),
.equal-columns--outside-trim .one-third:nth-of-type(3n),
.equal-columns--outside-trim .one-fourth:nth-of-type(4n),
.equal-columns--outside-trim .four:nth-of-type(4n),
.equal-columns--outside-trim .one-fifth:nth-of-type(5n),
.equal-columns--outside-trim .one-sixth:nth-of-type(6n),
.equal-columns--outside-trim .one-seventh:nth-of-type(7n),
.equal-columns--outside-trim .two:nth-of-type(8n) {
  margin-right: 0;
}
.equal-columns--outside-trim .one-half:nth-of-type(2n+1),
.equal-columns--outside-trim .eight:nth-of-type(2n+1),
.equal-columns--outside-trim .one-third:nth-of-type(3n+1),
.equal-columns--outside-trim .one-fourth:nth-of-type(4n+1),
.equal-columns--outside-trim .four:nth-of-type(4n+1),
.equal-columns--outside-trim .one-fifth:nth-of-type(5n+1),
.equal-columns--outside-trim .one-sixth:nth-of-type(6n+1),
.equal-columns--outside-trim .one-seventh:nth-of-type(7n+1),
.equal-columns--outside-trim .two:nth-of-type(8n+1) {
  margin-left: 0;
}
.equal-columns--outside-trim .one-whole:nth-of-type(1n+1) {
  width: 100%;
  margin-right: 0;
  margin-left: 0;
}
.equal-columns--outside-trim .one-half {
  width: calc(50% - (20px));
}
.equal-columns--outside-trim .one-third {
  width: calc(33.3333333333% - (20px - (20px / 3)));
}
.equal-columns--outside-trim .one-fifth {
  width: calc(20% - (20px - (20px / 5)));
}
.equal-columns--outside-trim .one-fourth {
  width: calc(25% - (20px - (20px / 4)));
}
.equal-columns--outside-trim .one-sixth {
  width: calc(16.6666666667% - (20px - (20px / 6)));
}
.equal-columns--outside-trim .one-seventh {
  width: calc(14.2857142857% - (20px - (20px / 7)));
}
.equal-columns--outside-trim .two {
  width: calc(12.5% - (20px - (20px / 8)));
}
.equal-columns--outside-trim .four {
  width: calc(25% - (20px - (20px / 4)));
}
.equal-columns--outside-trim .seven {
  width: calc(43.75% - (20px - (20px / 2)));
}
.equal-columns--outside-trim .eight {
  width: calc(50% - (20px / 2));
}
.equal-columns--outside-trim .nine {
  width: calc(56.25% - (20px - (20px / 2)));
}

.has-no-side-gutter.has-background {
  padding-right: 0px;
  padding-left: 0px;
}
.has-no-side-gutter .one-whole,
.has-no-side-gutter .one-half,
.has-no-side-gutter .eight,
.has-no-side-gutter .one-third,
.has-no-side-gutter .two-thirds,
.has-no-side-gutter .one-fourth,
.has-no-side-gutter .four,
.has-no-side-gutter .one-fifth,
.has-no-side-gutter .three-fifths,
.has-no-side-gutter .two-fifths,
.has-no-side-gutter .one-sixth,
.has-no-side-gutter .one-seventh,
.has-no-side-gutter .one-eighth,
.has-no-side-gutter .two {
  margin-right: 0;
  margin-left: 0;
}
.has-no-side-gutter .one-whole {
  width: 100%;
}
.has-no-side-gutter .one-half {
  width: 50%;
}
.has-no-side-gutter .one-third {
  width: 33.3333333333%;
}
.has-no-side-gutter .one-fourth {
  width: 25%;
}
.has-no-side-gutter .one-fifth {
  width: 20%;
}
.has-no-side-gutter .two-fifths {
  width: 40%;
}
.has-no-side-gutter .three-fifths {
  width: 60%;
}
.has-no-side-gutter .one-sixth {
  width: 16.6666666667%;
}
.has-no-side-gutter .one-seventh {
  width: 14.2857142857%;
}
.has-no-side-gutter .one-eighth {
  width: 12.5%;
}
.has-no-side-gutter .two-thirds {
  width: 66.6666666667%;
}
.has-no-side-gutter .two {
  width: 12.5%;
}
.has-no-side-gutter .four {
  width: 25%;
}
.has-no-side-gutter .seven {
  width: 43.75%;
}
.has-no-side-gutter .eight {
  width: 50%;
}
.has-no-side-gutter .nine {
  width: 56.25%;
}

.show {
  display: block !important;
}

.hide {
  display: none !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

/*============================================================================
  Generate breakpoint-specific column widths and push classes
    - Default column widths: $breakpoint-has-widths: ($small, $medium-up);
    - Default is no push classes
    - Will not work if `styles/global/grid.scss` is removed
==============================================================================*/
/*================ Build Responsive Grid Classes ================*/
@media only screen and (max-width: 1024px) {
  .large-down--hidden {
    display: none !important;
  }
  .large-down--visible {
    display: block !important;
  }
  .large-down--one {
    width: calc(6.25% - 20px);
  }
  .large-down--two {
    width: calc(12.5% - 20px);
  }
  .large-down--three {
    width: calc(18.75% - 20px);
  }
  .large-down--four {
    width: calc(25% - 20px);
  }
  .large-down--five {
    width: calc(31.25% - 20px);
  }
  .large-down--six {
    width: calc(37.5% - 20px);
  }
  .large-down--seven {
    width: calc(43.75% - 20px);
  }
  .large-down--eight {
    width: calc(50% - 20px);
  }
  .large-down--nine {
    width: calc(56.25% - 20px);
  }
  .large-down--ten {
    width: calc(62.5% - 20px);
  }
  .large-down--eleven {
    width: calc(68.75% - 20px);
  }
  .large-down--twelve {
    width: calc(75% - 20px);
  }
  .large-down--thirteen {
    width: calc(81.25% - 20px);
  }
  .large-down--fourteen {
    width: calc(87.5% - 20px);
  }
  .large-down--fifteen {
    width: calc(93.75% - 20px);
  }
  .large-down--sixteen {
    width: calc(100% - 20px);
  }
  .large-down--one-whole {
    width: calc(100% - 20px);
  }
  .large-down--one-half {
    width: calc(50% - 20px);
  }
  .large-down--one-third {
    width: calc(33.3333333333% - 20px);
  }
  .large-down--two-thirds {
    width: calc(66.6666666667% - 20px);
  }
  .large-down--one-fourth {
    width: calc(25% - 20px);
  }
  .large-down--two-fourths {
    width: calc(50% - 20px);
  }
  .large-down--three-fourths {
    width: calc(75% - 20px);
  }
  .large-down--one-fifth {
    width: calc(20% - 20px);
  }
  .large-down--two-fifths {
    width: calc(40% - 20px);
  }
  .large-down--three-fifths {
    width: calc(60% - 20px);
  }
  .large-down--four-fifths {
    width: calc(80% - 20px);
  }
  .large-down--one-sixth {
    width: calc(16.6666666667% - 20px);
  }
  .large-down--two-sixths {
    width: calc(33.3333333333% - 20px);
  }
  .large-down--three-sixths {
    width: calc(50% - 20px);
  }
  .large-down--four-sixths {
    width: calc(66.6666666667% - 20px);
  }
  .large-down--five-sixths {
    width: calc(83.3333333333% - 20px);
  }
  .large-down--one-seventh {
    width: calc(14.2857142857% - 20px);
  }
  .large-down--two-sevenths {
    width: calc(28.5714285714% - 20px);
  }
  .large-down--three-sevenths {
    width: calc(42.8571428571% - 20px);
  }
  .large-down--four-sevenths {
    width: calc(57.1428571429% - 20px);
  }
  .large-down--five-sevenths {
    width: calc(71.4285714286% - 20px);
  }
  .large-down--one-eighth {
    width: calc(12.5% - 20px);
  }
  .large-down--two-eighths {
    width: calc(25% - 20px);
  }
  .large-down--three-eighths {
    width: calc(37.5% - 20px);
  }
  .large-down--four-eighths {
    width: calc(50% - 20px);
  }
  .large-down--five-eighths {
    width: calc(62.5% - 20px);
  }
  .large-down--six-eighths {
    width: calc(75% - 20px);
  }
  .large-down--seven-eighths {
    width: calc(87.5% - 20px);
  }
  .large-down--one-tenth {
    width: calc(10% - 20px);
  }
  .large-down--two-tenths {
    width: calc(20% - 20px);
  }
  .large-down--three-tenths {
    width: calc(30% - 20px);
  }
  .large-down--four-tenths {
    width: calc(40% - 20px);
  }
  .large-down--five-tenths {
    width: calc(50% - 20px);
  }
  .large-down--six-tenths {
    width: calc(60% - 20px);
  }
  .large-down--seven-tenths {
    width: calc(70% - 20px);
  }
  .large-down--eight-tenths {
    width: calc(80% - 20px);
  }
  .large-down--nine-tenths {
    width: calc(90% - 20px);
  }
  .large-down--one-twelfth {
    width: calc(8.3333333333% - 20px);
  }
  .large-down--two-twelfths {
    width: calc(16.6666666667% - 20px);
  }
  .large-down--three-twelfths {
    width: calc(25% - 20px);
  }
  .large-down--four-twelfths {
    width: calc(33.3333333333% - 20px);
  }
  .large-down--five-twelfths {
    width: calc(41.6666666667% - 20px);
  }
  .large-down--six-twelfths {
    width: calc(50% - 20px);
  }
  .large-down--seven-twelfths {
    width: calc(58.3333333333% - 20px);
  }
  .large-down--eight-twelfths {
    width: calc(66.6666666667% - 20px);
  }
  .large-down--nine-twelfths {
    width: calc(75% - 20px);
  }
  .large-down--ten-twelfths {
    width: calc(83.3333333333% - 20px);
  }
  .large-down--eleven-twelfths {
    width: calc(91.6666666667% - 20px);
  }
  .large-down--offset-by-one {
    left: calc(6.25%);
  }
  .large-down--offset-by-two {
    left: calc(12.5%);
  }
  .large-down--offset-by-three {
    left: calc(18.75%);
  }
  .large-down--offset-by-four {
    left: calc(25%);
  }
  .large-down--offset-by-five {
    left: calc(31.25%);
  }
  .large-down--offset-by-six {
    left: calc(37.5%);
  }
  .large-down--offset-by-seven {
    left: calc(43.75%);
  }
  .large-down--offset-by-eight {
    left: calc(50%);
  }
  .large-down--offset-by-nine {
    left: calc(56.25%);
  }
  .large-down--offset-by-ten {
    left: calc(62.5%);
  }
  .large-down--offset-by-eleven {
    left: calc(68.75%);
  }
  .large-down--offset-by-twelve {
    left: calc(75%);
  }
  .large-down--offset-by-thirteen {
    left: calc(81.25%);
  }
  .large-down--offset-by-fourteen {
    left: calc(87.5%);
  }
  .large-down--offset-by-fifteen {
    left: calc(93.75%);
  }
  .equal-columns--outside-trim .large-down--one-half:nth-of-type(2n),
  .equal-columns--outside-trim .large-down--eight:nth-of-type(2n),
  .equal-columns--outside-trim .large-down--one-third:nth-of-type(3n),
  .equal-columns--outside-trim .large-down--one-fourth:nth-of-type(4n),
  .equal-columns--outside-trim .large-down--four:nth-of-type(4n),
  .equal-columns--outside-trim .large-down--one-fifth:nth-of-type(5n),
  .equal-columns--outside-trim .large-down--one-sixth:nth-of-type(6n),
  .equal-columns--outside-trim .large-down--one-seventh:nth-of-type(7n),
  .equal-columns--outside-trim .large-down--two:nth-of-type(8n) {
    margin-right: 0;
  }
  .equal-columns--outside-trim .large-down--one-half:nth-of-type(2n+1),
  .equal-columns--outside-trim .large-down--eight:nth-of-type(2n+1),
  .equal-columns--outside-trim .large-down--one-third:nth-of-type(3n+1),
  .equal-columns--outside-trim .large-down--one-fourth:nth-of-type(4n+1),
  .equal-columns--outside-trim .large-down--four:nth-of-type(4n+1),
  .equal-columns--outside-trim .large-down--one-fifth:nth-of-type(5n+1),
  .equal-columns--outside-trim .large-down--one-sixth:nth-of-type(6n+1),
  .equal-columns--outside-trim .large-down--one-seventh:nth-of-type(7n+1),
  .equal-columns--outside-trim .large-down--two:nth-of-type(8n+1) {
    margin-left: 0;
  }
}
@media only screen and (max-width: 1024px) and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--one-half.large-down--one-half,
  .equal-columns--outside-trim .large-down--eight.large-down--eight,
  .equal-columns--outside-trim .large-down--one-third.large-down--one-third,
  .equal-columns--outside-trim .large-down--one-fourth.large-down--one-fourth,
  .equal-columns--outside-trim .large-down--four.large-down--four,
  .equal-columns--outside-trim .large-down--one-fifth.large-down--one-fifth,
  .equal-columns--outside-trim .large-down--one-sixth.large-down--one-sixth,
  .equal-columns--outside-trim .large-down--one-seventh.large-down--one-seventh,
  .equal-columns--outside-trim .large-down--two.large-down--two {
    margin-right: 20px;
    margin-left: 20px;
  }
  .equal-columns--outside-trim .large-down--one-half:nth-of-type(2n),
  .equal-columns--outside-trim .large-down--eight:nth-of-type(2n),
  .equal-columns--outside-trim .large-down--one-third:nth-of-type(3n),
  .equal-columns--outside-trim .large-down--one-fourth:nth-of-type(4n),
  .equal-columns--outside-trim .large-down--four:nth-of-type(4n),
  .equal-columns--outside-trim .large-down--one-fifth:nth-of-type(5n),
  .equal-columns--outside-trim .large-down--one-sixth:nth-of-type(6n),
  .equal-columns--outside-trim .large-down--one-seventh:nth-of-type(7n),
  .equal-columns--outside-trim .large-down--two:nth-of-type(8n) {
    margin-right: 0;
  }
  .equal-columns--outside-trim .large-down--one-half:nth-of-type(2n+1),
  .equal-columns--outside-trim .large-down--eight:nth-of-type(2n+1),
  .equal-columns--outside-trim .large-down--one-third:nth-of-type(3n+1),
  .equal-columns--outside-trim .large-down--one-fourth:nth-of-type(4n+1),
  .equal-columns--outside-trim .large-down--four:nth-of-type(4n+1),
  .equal-columns--outside-trim .large-down--one-fifth:nth-of-type(5n+1),
  .equal-columns--outside-trim .large-down--one-sixth:nth-of-type(6n+1),
  .equal-columns--outside-trim .large-down--one-seventh:nth-of-type(7n+1),
  .equal-columns--outside-trim .large-down--two:nth-of-type(8n+1) {
    margin-left: 0;
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--one-whole:nth-of-type(1n+1) {
    width: 100%;
    margin-right: 0;
    margin-left: 0;
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--one-half {
    width: calc(50% - (20px));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--one-third {
    width: calc(33.3333333333% - (20px - (20px / 3)));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--one-fifth {
    width: calc(20% - (20px - (20px / 5)));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--one-fourth {
    width: calc(25% - (20px - (20px / 4)));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--one-sixth {
    width: calc(16.6666666667% - (20px - (20px / 6)));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--one-seventh {
    width: calc(14.2857142857% - (20px - (20px / 7)));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--two {
    width: calc(12.5% - (20px - (20px / 8)));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--four {
    width: calc(25% - (20px - (20px / 4)));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--seven {
    width: calc(43.75% - (20px - (20px / 2)));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--eight {
    width: calc(50% - (20px / 2));
  }
}
@media only screen and (max-width: 1024px) {
  .equal-columns--outside-trim .large-down--nine {
    width: calc(56.25% - (20px - (20px / 2)));
  }
}
@media only screen and (max-width: 1024px) {
  .has-no-side-gutter.has-background {
    padding-right: 0px;
    padding-left: 0px;
  }
  .has-no-side-gutter .large-down--one-whole,
  .has-no-side-gutter .large-down--one-half,
  .has-no-side-gutter .large-down--eight,
  .has-no-side-gutter .large-down--one-third,
  .has-no-side-gutter .large-down--two-thirds,
  .has-no-side-gutter .large-down--one-fourth,
  .has-no-side-gutter .large-down--four,
  .has-no-side-gutter .large-down--one-fifth,
  .has-no-side-gutter .large-down--three-fifths,
  .has-no-side-gutter .large-down--two-fifths,
  .has-no-side-gutter .large-down--one-sixth,
  .has-no-side-gutter .large-down--one-seventh,
  .has-no-side-gutter .large-down--one-eighth,
  .has-no-side-gutter .large-down--two {
    margin-right: 0;
    margin-left: 0;
  }
  .has-no-side-gutter .large-down--one-whole {
    width: 100%;
  }
  .has-no-side-gutter .large-down--one-half {
    width: 50%;
  }
  .has-no-side-gutter .large-down--one-third {
    width: 33.3333333333%;
  }
  .has-no-side-gutter .large-down--one-fourth {
    width: 25%;
  }
  .has-no-side-gutter .large-down--one-fifth {
    width: 20%;
  }
  .has-no-side-gutter .large-down--two-fifths {
    width: 40%;
  }
  .has-no-side-gutter .large-down--three-fifths {
    width: 60%;
  }
  .has-no-side-gutter .large-down--one-sixth {
    width: 16.6666666667%;
  }
  .has-no-side-gutter .large-down--one-seventh {
    width: 14.2857142857%;
  }
  .has-no-side-gutter .large-down--one-eighth {
    width: 12.5%;
  }
  .has-no-side-gutter .large-down--two-thirds {
    width: 66.6666666667%;
  }
  .has-no-side-gutter .large-down--two {
    width: 12.5%;
  }
  .has-no-side-gutter .large-down--four {
    width: 25%;
  }
  .has-no-side-gutter .large-down--seven {
    width: 43.75%;
  }
  .has-no-side-gutter .large-down--eight {
    width: 50%;
  }
  .has-no-side-gutter .large-down--nine {
    width: 56.25%;
  }
}
@media only screen and (max-width: 1024px) {
  .large-down--show {
    display: block !important;
  }
}
@media only screen and (max-width: 1024px) {
  .large-down--hide {
    display: none !important;
  }
}
@media only screen and (max-width: 1024px) {
  .large-down--text-left {
    text-align: left !important;
  }
}
@media only screen and (max-width: 1024px) {
  .large-down--text-right {
    text-align: right !important;
  }
}
@media only screen and (max-width: 1024px) {
  .large-down--text-center {
    text-align: center !important;
  }
}
@media only screen and (max-width: 798px) {
  .medium-down--hidden {
    display: none !important;
  }
  .medium-down--visible {
    display: block !important;
  }
  .medium-down--one {
    width: calc(6.25% - 20px);
  }
  .medium-down--two {
    width: calc(12.5% - 20px);
  }
  .medium-down--three {
    width: calc(18.75% - 20px);
  }
  .medium-down--four {
    width: calc(25% - 20px);
  }
  .medium-down--five {
    width: calc(31.25% - 20px);
  }
  .medium-down--six {
    width: calc(37.5% - 20px);
  }
  .medium-down--seven {
    width: calc(43.75% - 20px);
  }
  .medium-down--eight {
    width: calc(50% - 20px);
  }
  .medium-down--nine {
    width: calc(56.25% - 20px);
  }
  .medium-down--ten {
    width: calc(62.5% - 20px);
  }
  .medium-down--eleven {
    width: calc(68.75% - 20px);
  }
  .medium-down--twelve {
    width: calc(75% - 20px);
  }
  .medium-down--thirteen {
    width: calc(81.25% - 20px);
  }
  .medium-down--fourteen {
    width: calc(87.5% - 20px);
  }
  .medium-down--fifteen {
    width: calc(93.75% - 20px);
  }
  .medium-down--sixteen {
    width: calc(100% - 20px);
  }
  .medium-down--one-whole {
    width: calc(100% - 20px);
  }
  .medium-down--one-half {
    width: calc(50% - 20px);
  }
  .medium-down--one-third {
    width: calc(33.3333333333% - 20px);
  }
  .medium-down--two-thirds {
    width: calc(66.6666666667% - 20px);
  }
  .medium-down--one-fourth {
    width: calc(25% - 20px);
  }
  .medium-down--two-fourths {
    width: calc(50% - 20px);
  }
  .medium-down--three-fourths {
    width: calc(75% - 20px);
  }
  .medium-down--one-fifth {
    width: calc(20% - 20px);
  }
  .medium-down--two-fifths {
    width: calc(40% - 20px);
  }
  .medium-down--three-fifths {
    width: calc(60% - 20px);
  }
  .medium-down--four-fifths {
    width: calc(80% - 20px);
  }
  .medium-down--one-sixth {
    width: calc(16.6666666667% - 20px);
  }
  .medium-down--two-sixths {
    width: calc(33.3333333333% - 20px);
  }
  .medium-down--three-sixths {
    width: calc(50% - 20px);
  }
  .medium-down--four-sixths {
    width: calc(66.6666666667% - 20px);
  }
  .medium-down--five-sixths {
    width: calc(83.3333333333% - 20px);
  }
  .medium-down--one-seventh {
    width: calc(14.2857142857% - 20px);
  }
  .medium-down--two-sevenths {
    width: calc(28.5714285714% - 20px);
  }
  .medium-down--three-sevenths {
    width: calc(42.8571428571% - 20px);
  }
  .medium-down--four-sevenths {
    width: calc(57.1428571429% - 20px);
  }
  .medium-down--five-sevenths {
    width: calc(71.4285714286% - 20px);
  }
  .medium-down--one-eighth {
    width: calc(12.5% - 20px);
  }
  .medium-down--two-eighths {
    width: calc(25% - 20px);
  }
  .medium-down--three-eighths {
    width: calc(37.5% - 20px);
  }
  .medium-down--four-eighths {
    width: calc(50% - 20px);
  }
  .medium-down--five-eighths {
    width: calc(62.5% - 20px);
  }
  .medium-down--six-eighths {
    width: calc(75% - 20px);
  }
  .medium-down--seven-eighths {
    width: calc(87.5% - 20px);
  }
  .medium-down--one-tenth {
    width: calc(10% - 20px);
  }
  .medium-down--two-tenths {
    width: calc(20% - 20px);
  }
  .medium-down--three-tenths {
    width: calc(30% - 20px);
  }
  .medium-down--four-tenths {
    width: calc(40% - 20px);
  }
  .medium-down--five-tenths {
    width: calc(50% - 20px);
  }
  .medium-down--six-tenths {
    width: calc(60% - 20px);
  }
  .medium-down--seven-tenths {
    width: calc(70% - 20px);
  }
  .medium-down--eight-tenths {
    width: calc(80% - 20px);
  }
  .medium-down--nine-tenths {
    width: calc(90% - 20px);
  }
  .medium-down--one-twelfth {
    width: calc(8.3333333333% - 20px);
  }
  .medium-down--two-twelfths {
    width: calc(16.6666666667% - 20px);
  }
  .medium-down--three-twelfths {
    width: calc(25% - 20px);
  }
  .medium-down--four-twelfths {
    width: calc(33.3333333333% - 20px);
  }
  .medium-down--five-twelfths {
    width: calc(41.6666666667% - 20px);
  }
  .medium-down--six-twelfths {
    width: calc(50% - 20px);
  }
  .medium-down--seven-twelfths {
    width: calc(58.3333333333% - 20px);
  }
  .medium-down--eight-twelfths {
    width: calc(66.6666666667% - 20px);
  }
  .medium-down--nine-twelfths {
    width: calc(75% - 20px);
  }
  .medium-down--ten-twelfths {
    width: calc(83.3333333333% - 20px);
  }
  .medium-down--eleven-twelfths {
    width: calc(91.6666666667% - 20px);
  }
  .medium-down--offset-by-one {
    left: calc(6.25%);
  }
  .medium-down--offset-by-two {
    left: calc(12.5%);
  }
  .medium-down--offset-by-three {
    left: calc(18.75%);
  }
  .medium-down--offset-by-four {
    left: calc(25%);
  }
  .medium-down--offset-by-five {
    left: calc(31.25%);
  }
  .medium-down--offset-by-six {
    left: calc(37.5%);
  }
  .medium-down--offset-by-seven {
    left: calc(43.75%);
  }
  .medium-down--offset-by-eight {
    left: calc(50%);
  }
  .medium-down--offset-by-nine {
    left: calc(56.25%);
  }
  .medium-down--offset-by-ten {
    left: calc(62.5%);
  }
  .medium-down--offset-by-eleven {
    left: calc(68.75%);
  }
  .medium-down--offset-by-twelve {
    left: calc(75%);
  }
  .medium-down--offset-by-thirteen {
    left: calc(81.25%);
  }
  .medium-down--offset-by-fourteen {
    left: calc(87.5%);
  }
  .medium-down--offset-by-fifteen {
    left: calc(93.75%);
  }
  .equal-columns--outside-trim .medium-down--one-half:nth-of-type(2n),
  .equal-columns--outside-trim .medium-down--eight:nth-of-type(2n),
  .equal-columns--outside-trim .medium-down--one-third:nth-of-type(3n),
  .equal-columns--outside-trim .medium-down--one-fourth:nth-of-type(4n),
  .equal-columns--outside-trim .medium-down--four:nth-of-type(4n),
  .equal-columns--outside-trim .medium-down--one-fifth:nth-of-type(5n),
  .equal-columns--outside-trim .medium-down--one-sixth:nth-of-type(6n),
  .equal-columns--outside-trim .medium-down--one-seventh:nth-of-type(7n),
  .equal-columns--outside-trim .medium-down--two:nth-of-type(8n) {
    margin-right: 0;
  }
  .equal-columns--outside-trim .medium-down--one-half:nth-of-type(2n+1),
  .equal-columns--outside-trim .medium-down--eight:nth-of-type(2n+1),
  .equal-columns--outside-trim .medium-down--one-third:nth-of-type(3n+1),
  .equal-columns--outside-trim .medium-down--one-fourth:nth-of-type(4n+1),
  .equal-columns--outside-trim .medium-down--four:nth-of-type(4n+1),
  .equal-columns--outside-trim .medium-down--one-fifth:nth-of-type(5n+1),
  .equal-columns--outside-trim .medium-down--one-sixth:nth-of-type(6n+1),
  .equal-columns--outside-trim .medium-down--one-seventh:nth-of-type(7n+1),
  .equal-columns--outside-trim .medium-down--two:nth-of-type(8n+1) {
    margin-left: 0;
  }
}
@media only screen and (max-width: 798px) and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--one-half.medium-down--one-half,
  .equal-columns--outside-trim .medium-down--eight.medium-down--eight,
  .equal-columns--outside-trim .medium-down--one-third.medium-down--one-third,
  .equal-columns--outside-trim .medium-down--one-fourth.medium-down--one-fourth,
  .equal-columns--outside-trim .medium-down--four.medium-down--four,
  .equal-columns--outside-trim .medium-down--one-fifth.medium-down--one-fifth,
  .equal-columns--outside-trim .medium-down--one-sixth.medium-down--one-sixth,
  .equal-columns--outside-trim .medium-down--one-seventh.medium-down--one-seventh,
  .equal-columns--outside-trim .medium-down--two.medium-down--two {
    margin-right: 20px;
    margin-left: 20px;
  }
  .equal-columns--outside-trim .medium-down--one-half:nth-of-type(2n),
  .equal-columns--outside-trim .medium-down--eight:nth-of-type(2n),
  .equal-columns--outside-trim .medium-down--one-third:nth-of-type(3n),
  .equal-columns--outside-trim .medium-down--one-fourth:nth-of-type(4n),
  .equal-columns--outside-trim .medium-down--four:nth-of-type(4n),
  .equal-columns--outside-trim .medium-down--one-fifth:nth-of-type(5n),
  .equal-columns--outside-trim .medium-down--one-sixth:nth-of-type(6n),
  .equal-columns--outside-trim .medium-down--one-seventh:nth-of-type(7n),
  .equal-columns--outside-trim .medium-down--two:nth-of-type(8n) {
    margin-right: 0;
  }
  .equal-columns--outside-trim .medium-down--one-half:nth-of-type(2n+1),
  .equal-columns--outside-trim .medium-down--eight:nth-of-type(2n+1),
  .equal-columns--outside-trim .medium-down--one-third:nth-of-type(3n+1),
  .equal-columns--outside-trim .medium-down--one-fourth:nth-of-type(4n+1),
  .equal-columns--outside-trim .medium-down--four:nth-of-type(4n+1),
  .equal-columns--outside-trim .medium-down--one-fifth:nth-of-type(5n+1),
  .equal-columns--outside-trim .medium-down--one-sixth:nth-of-type(6n+1),
  .equal-columns--outside-trim .medium-down--one-seventh:nth-of-type(7n+1),
  .equal-columns--outside-trim .medium-down--two:nth-of-type(8n+1) {
    margin-left: 0;
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--one-whole:nth-of-type(1n+1) {
    width: 100%;
    margin-right: 0;
    margin-left: 0;
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--one-half {
    width: calc(50% - (20px));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--one-third {
    width: calc(33.3333333333% - (20px - (20px / 3)));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--one-fifth {
    width: calc(20% - (20px - (20px / 5)));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--one-fourth {
    width: calc(25% - (20px - (20px / 4)));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--one-sixth {
    width: calc(16.6666666667% - (20px - (20px / 6)));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--one-seventh {
    width: calc(14.2857142857% - (20px - (20px / 7)));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--two {
    width: calc(12.5% - (20px - (20px / 8)));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--four {
    width: calc(25% - (20px - (20px / 4)));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--seven {
    width: calc(43.75% - (20px - (20px / 2)));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--eight {
    width: calc(50% - (20px / 2));
  }
}
@media only screen and (max-width: 798px) {
  .equal-columns--outside-trim .medium-down--nine {
    width: calc(56.25% - (20px - (20px / 2)));
  }
}
@media only screen and (max-width: 798px) {
  .has-no-side-gutter.has-background {
    padding-right: 0px;
    padding-left: 0px;
  }
  .has-no-side-gutter .medium-down--one-whole,
  .has-no-side-gutter .medium-down--one-half,
  .has-no-side-gutter .medium-down--eight,
  .has-no-side-gutter .medium-down--one-third,
  .has-no-side-gutter .medium-down--two-thirds,
  .has-no-side-gutter .medium-down--one-fourth,
  .has-no-side-gutter .medium-down--four,
  .has-no-side-gutter .medium-down--one-fifth,
  .has-no-side-gutter .medium-down--three-fifths,
  .has-no-side-gutter .medium-down--two-fifths,
  .has-no-side-gutter .medium-down--one-sixth,
  .has-no-side-gutter .medium-down--one-seventh,
  .has-no-side-gutter .medium-down--one-eighth,
  .has-no-side-gutter .medium-down--two {
    margin-right: 0;
    margin-left: 0;
  }
  .has-no-side-gutter .medium-down--one-whole {
    width: 100%;
  }
  .has-no-side-gutter .medium-down--one-half {
    width: 50%;
  }
  .has-no-side-gutter .medium-down--one-third {
    width: 33.3333333333%;
  }
  .has-no-side-gutter .medium-down--one-fourth {
    width: 25%;
  }
  .has-no-side-gutter .medium-down--one-fifth {
    width: 20%;
  }
  .has-no-side-gutter .medium-down--two-fifths {
    width: 40%;
  }
  .has-no-side-gutter .medium-down--three-fifths {
    width: 60%;
  }
  .has-no-side-gutter .medium-down--one-sixth {
    width: 16.6666666667%;
  }
  .has-no-side-gutter .medium-down--one-seventh {
    width: 14.2857142857%;
  }
  .has-no-side-gutter .medium-down--one-eighth {
    width: 12.5%;
  }
  .has-no-side-gutter .medium-down--two-thirds {
    width: 66.6666666667%;
  }
  .has-no-side-gutter .medium-down--two {
    width: 12.5%;
  }
  .has-no-side-gutter .medium-down--four {
    width: 25%;
  }
  .has-no-side-gutter .medium-down--seven {
    width: 43.75%;
  }
  .has-no-side-gutter .medium-down--eight {
    width: 50%;
  }
  .has-no-side-gutter .medium-down--nine {
    width: 56.25%;
  }
}
@media only screen and (max-width: 798px) {
  .medium-down--show {
    display: block !important;
  }
}
@media only screen and (max-width: 798px) {
  .medium-down--hide {
    display: none !important;
  }
}
@media only screen and (max-width: 798px) {
  .medium-down--text-left {
    text-align: left !important;
  }
}
@media only screen and (max-width: 798px) {
  .medium-down--text-right {
    text-align: right !important;
  }
}
@media only screen and (max-width: 798px) {
  .medium-down--text-center {
    text-align: center !important;
  }
}
@media only screen and (max-width: 480px) {
  .small-down--hidden {
    display: none !important;
  }
  .small-down--visible {
    display: block !important;
  }
  .small-down--one {
    width: calc(6.25% - 20px);
  }
  .small-down--two {
    width: calc(12.5% - 20px);
  }
  .small-down--three {
    width: calc(18.75% - 20px);
  }
  .small-down--four {
    width: calc(25% - 20px);
  }
  .small-down--five {
    width: calc(31.25% - 20px);
  }
  .small-down--six {
    width: calc(37.5% - 20px);
  }
  .small-down--seven {
    width: calc(43.75% - 20px);
  }
  .small-down--eight {
    width: calc(50% - 20px);
  }
  .small-down--nine {
    width: calc(56.25% - 20px);
  }
  .small-down--ten {
    width: calc(62.5% - 20px);
  }
  .small-down--eleven {
    width: calc(68.75% - 20px);
  }
  .small-down--twelve {
    width: calc(75% - 20px);
  }
  .small-down--thirteen {
    width: calc(81.25% - 20px);
  }
  .small-down--fourteen {
    width: calc(87.5% - 20px);
  }
  .small-down--fifteen {
    width: calc(93.75% - 20px);
  }
  .small-down--sixteen {
    width: calc(100% - 20px);
  }
  .small-down--one-whole {
    width: calc(100% - 20px);
  }
  .small-down--one-half {
    width: calc(50% - 20px);
  }
  .small-down--one-third {
    width: calc(33.3333333333% - 20px);
  }
  .small-down--two-thirds {
    width: calc(66.6666666667% - 20px);
  }
  .small-down--one-fourth {
    width: calc(25% - 20px);
  }
  .small-down--two-fourths {
    width: calc(50% - 20px);
  }
  .small-down--three-fourths {
    width: calc(75% - 20px);
  }
  .small-down--one-fifth {
    width: calc(20% - 20px);
  }
  .small-down--two-fifths {
    width: calc(40% - 20px);
  }
  .small-down--three-fifths {
    width: calc(60% - 20px);
  }
  .small-down--four-fifths {
    width: calc(80% - 20px);
  }
  .small-down--one-sixth {
    width: calc(16.6666666667% - 20px);
  }
  .small-down--two-sixths {
    width: calc(33.3333333333% - 20px);
  }
  .small-down--three-sixths {
    width: calc(50% - 20px);
  }
  .small-down--four-sixths {
    width: calc(66.6666666667% - 20px);
  }
  .small-down--five-sixths {
    width: calc(83.3333333333% - 20px);
  }
  .small-down--one-seventh {
    width: calc(14.2857142857% - 20px);
  }
  .small-down--two-sevenths {
    width: calc(28.5714285714% - 20px);
  }
  .small-down--three-sevenths {
    width: calc(42.8571428571% - 20px);
  }
  .small-down--four-sevenths {
    width: calc(57.1428571429% - 20px);
  }
  .small-down--five-sevenths {
    width: calc(71.4285714286% - 20px);
  }
  .small-down--one-eighth {
    width: calc(12.5% - 20px);
  }
  .small-down--two-eighths {
    width: calc(25% - 20px);
  }
  .small-down--three-eighths {
    width: calc(37.5% - 20px);
  }
  .small-down--four-eighths {
    width: calc(50% - 20px);
  }
  .small-down--five-eighths {
    width: calc(62.5% - 20px);
  }
  .small-down--six-eighths {
    width: calc(75% - 20px);
  }
  .small-down--seven-eighths {
    width: calc(87.5% - 20px);
  }
  .small-down--one-tenth {
    width: calc(10% - 20px);
  }
  .small-down--two-tenths {
    width: calc(20% - 20px);
  }
  .small-down--three-tenths {
    width: calc(30% - 20px);
  }
  .small-down--four-tenths {
    width: calc(40% - 20px);
  }
  .small-down--five-tenths {
    width: calc(50% - 20px);
  }
  .small-down--six-tenths {
    width: calc(60% - 20px);
  }
  .small-down--seven-tenths {
    width: calc(70% - 20px);
  }
  .small-down--eight-tenths {
    width: calc(80% - 20px);
  }
  .small-down--nine-tenths {
    width: calc(90% - 20px);
  }
  .small-down--one-twelfth {
    width: calc(8.3333333333% - 20px);
  }
  .small-down--two-twelfths {
    width: calc(16.6666666667% - 20px);
  }
  .small-down--three-twelfths {
    width: calc(25% - 20px);
  }
  .small-down--four-twelfths {
    width: calc(33.3333333333% - 20px);
  }
  .small-down--five-twelfths {
    width: calc(41.6666666667% - 20px);
  }
  .small-down--six-twelfths {
    width: calc(50% - 20px);
  }
  .small-down--seven-twelfths {
    width: calc(58.3333333333% - 20px);
  }
  .small-down--eight-twelfths {
    width: calc(66.6666666667% - 20px);
  }
  .small-down--nine-twelfths {
    width: calc(75% - 20px);
  }
  .small-down--ten-twelfths {
    width: calc(83.3333333333% - 20px);
  }
  .small-down--eleven-twelfths {
    width: calc(91.6666666667% - 20px);
  }
  .small-down--offset-by-one {
    left: calc(6.25%);
  }
  .small-down--offset-by-two {
    left: calc(12.5%);
  }
  .small-down--offset-by-three {
    left: calc(18.75%);
  }
  .small-down--offset-by-four {
    left: calc(25%);
  }
  .small-down--offset-by-five {
    left: calc(31.25%);
  }
  .small-down--offset-by-six {
    left: calc(37.5%);
  }
  .small-down--offset-by-seven {
    left: calc(43.75%);
  }
  .small-down--offset-by-eight {
    left: calc(50%);
  }
  .small-down--offset-by-nine {
    left: calc(56.25%);
  }
  .small-down--offset-by-ten {
    left: calc(62.5%);
  }
  .small-down--offset-by-eleven {
    left: calc(68.75%);
  }
  .small-down--offset-by-twelve {
    left: calc(75%);
  }
  .small-down--offset-by-thirteen {
    left: calc(81.25%);
  }
  .small-down--offset-by-fourteen {
    left: calc(87.5%);
  }
  .small-down--offset-by-fifteen {
    left: calc(93.75%);
  }
  .equal-columns--outside-trim .small-down--one-half:nth-of-type(2n),
  .equal-columns--outside-trim .small-down--eight:nth-of-type(2n),
  .equal-columns--outside-trim .small-down--one-third:nth-of-type(3n),
  .equal-columns--outside-trim .small-down--one-fourth:nth-of-type(4n),
  .equal-columns--outside-trim .small-down--four:nth-of-type(4n),
  .equal-columns--outside-trim .small-down--one-fifth:nth-of-type(5n),
  .equal-columns--outside-trim .small-down--one-sixth:nth-of-type(6n),
  .equal-columns--outside-trim .small-down--one-seventh:nth-of-type(7n),
  .equal-columns--outside-trim .small-down--two:nth-of-type(8n) {
    margin-right: 0;
  }
  .equal-columns--outside-trim .small-down--one-half:nth-of-type(2n+1),
  .equal-columns--outside-trim .small-down--eight:nth-of-type(2n+1),
  .equal-columns--outside-trim .small-down--one-third:nth-of-type(3n+1),
  .equal-columns--outside-trim .small-down--one-fourth:nth-of-type(4n+1),
  .equal-columns--outside-trim .small-down--four:nth-of-type(4n+1),
  .equal-columns--outside-trim .small-down--one-fifth:nth-of-type(5n+1),
  .equal-columns--outside-trim .small-down--one-sixth:nth-of-type(6n+1),
  .equal-columns--outside-trim .small-down--one-seventh:nth-of-type(7n+1),
  .equal-columns--outside-trim .small-down--two:nth-of-type(8n+1) {
    margin-left: 0;
  }
}
@media only screen and (max-width: 480px) and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--one-half.small-down--one-half,
  .equal-columns--outside-trim .small-down--eight.small-down--eight,
  .equal-columns--outside-trim .small-down--one-third.small-down--one-third,
  .equal-columns--outside-trim .small-down--one-fourth.small-down--one-fourth,
  .equal-columns--outside-trim .small-down--four.small-down--four,
  .equal-columns--outside-trim .small-down--one-fifth.small-down--one-fifth,
  .equal-columns--outside-trim .small-down--one-sixth.small-down--one-sixth,
  .equal-columns--outside-trim .small-down--one-seventh.small-down--one-seventh,
  .equal-columns--outside-trim .small-down--two.small-down--two {
    margin-right: 20px;
    margin-left: 20px;
  }
  .equal-columns--outside-trim .small-down--one-half:nth-of-type(2n),
  .equal-columns--outside-trim .small-down--eight:nth-of-type(2n),
  .equal-columns--outside-trim .small-down--one-third:nth-of-type(3n),
  .equal-columns--outside-trim .small-down--one-fourth:nth-of-type(4n),
  .equal-columns--outside-trim .small-down--four:nth-of-type(4n),
  .equal-columns--outside-trim .small-down--one-fifth:nth-of-type(5n),
  .equal-columns--outside-trim .small-down--one-sixth:nth-of-type(6n),
  .equal-columns--outside-trim .small-down--one-seventh:nth-of-type(7n),
  .equal-columns--outside-trim .small-down--two:nth-of-type(8n) {
    margin-right: 0;
  }
  .equal-columns--outside-trim .small-down--one-half:nth-of-type(2n+1),
  .equal-columns--outside-trim .small-down--eight:nth-of-type(2n+1),
  .equal-columns--outside-trim .small-down--one-third:nth-of-type(3n+1),
  .equal-columns--outside-trim .small-down--one-fourth:nth-of-type(4n+1),
  .equal-columns--outside-trim .small-down--four:nth-of-type(4n+1),
  .equal-columns--outside-trim .small-down--one-fifth:nth-of-type(5n+1),
  .equal-columns--outside-trim .small-down--one-sixth:nth-of-type(6n+1),
  .equal-columns--outside-trim .small-down--one-seventh:nth-of-type(7n+1),
  .equal-columns--outside-trim .small-down--two:nth-of-type(8n+1) {
    margin-left: 0;
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--one-whole:nth-of-type(1n+1) {
    width: 100%;
    margin-right: 0;
    margin-left: 0;
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--one-half {
    width: calc(50% - (20px));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--one-third {
    width: calc(33.3333333333% - (20px - (20px / 3)));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--one-fifth {
    width: calc(20% - (20px - (20px / 5)));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--one-fourth {
    width: calc(25% - (20px - (20px / 4)));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--one-sixth {
    width: calc(16.6666666667% - (20px - (20px / 6)));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--one-seventh {
    width: calc(14.2857142857% - (20px - (20px / 7)));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--two {
    width: calc(12.5% - (20px - (20px / 8)));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--four {
    width: calc(25% - (20px - (20px / 4)));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--seven {
    width: calc(43.75% - (20px - (20px / 2)));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--eight {
    width: calc(50% - (20px / 2));
  }
}
@media only screen and (max-width: 480px) {
  .equal-columns--outside-trim .small-down--nine {
    width: calc(56.25% - (20px - (20px / 2)));
  }
}
@media only screen and (max-width: 480px) {
  .has-no-side-gutter.has-background {
    padding-right: 0px;
    padding-left: 0px;
  }
  .has-no-side-gutter .small-down--one-whole,
  .has-no-side-gutter .small-down--one-half,
  .has-no-side-gutter .small-down--eight,
  .has-no-side-gutter .small-down--one-third,
  .has-no-side-gutter .small-down--two-thirds,
  .has-no-side-gutter .small-down--one-fourth,
  .has-no-side-gutter .small-down--four,
  .has-no-side-gutter .small-down--one-fifth,
  .has-no-side-gutter .small-down--three-fifths,
  .has-no-side-gutter .small-down--two-fifths,
  .has-no-side-gutter .small-down--one-sixth,
  .has-no-side-gutter .small-down--one-seventh,
  .has-no-side-gutter .small-down--one-eighth,
  .has-no-side-gutter .small-down--two {
    margin-right: 0;
    margin-left: 0;
  }
  .has-no-side-gutter .small-down--one-whole {
    width: 100%;
  }
  .has-no-side-gutter .small-down--one-half {
    width: 50%;
  }
  .has-no-side-gutter .small-down--one-third {
    width: 33.3333333333%;
  }
  .has-no-side-gutter .small-down--one-fourth {
    width: 25%;
  }
  .has-no-side-gutter .small-down--one-fifth {
    width: 20%;
  }
  .has-no-side-gutter .small-down--two-fifths {
    width: 40%;
  }
  .has-no-side-gutter .small-down--three-fifths {
    width: 60%;
  }
  .has-no-side-gutter .small-down--one-sixth {
    width: 16.6666666667%;
  }
  .has-no-side-gutter .small-down--one-seventh {
    width: 14.2857142857%;
  }
  .has-no-side-gutter .small-down--one-eighth {
    width: 12.5%;
  }
  .has-no-side-gutter .small-down--two-thirds {
    width: 66.6666666667%;
  }
  .has-no-side-gutter .small-down--two {
    width: 12.5%;
  }
  .has-no-side-gutter .small-down--four {
    width: 25%;
  }
  .has-no-side-gutter .small-down--seven {
    width: 43.75%;
  }
  .has-no-side-gutter .small-down--eight {
    width: 50%;
  }
  .has-no-side-gutter .small-down--nine {
    width: 56.25%;
  }
}
@media only screen and (max-width: 480px) {
  .small-down--show {
    display: block !important;
  }
}
@media only screen and (max-width: 480px) {
  .small-down--hide {
    display: none !important;
  }
}
@media only screen and (max-width: 480px) {
  .small-down--text-left {
    text-align: left !important;
  }
}
@media only screen and (max-width: 480px) {
  .small-down--text-right {
    text-align: right !important;
  }
}
@media only screen and (max-width: 480px) {
  .small-down--text-center {
    text-align: center !important;
  }
}
/*================ Build Grid Push Classes ================*/
/* # Control
================================================== */
.pagination-previous,
.pagination-next,
.pagination-link,
.pagination-ellipsis, .file-cta,
.file-name, .select select, .age-gate__select-wrapper select, .input,
.textarea, .button, .age-gate__confirm_btn {
  -webkit-appearance: none;
          appearance: none;
  align-items: center;
  border: 1px solid transparent;
  border-radius: 0;
  box-shadow: none;
  display: inline-flex;
  font-size: 1rem;
  height: 2.25em;
  justify-content: flex-start;
  line-height: 1.5;
  padding-bottom: calc(0.375em - 1px);
  padding-left: calc(0.625em - 1px);
  padding-right: calc(0.625em - 1px);
  padding-top: calc(0.375em - 1px);
  position: relative;
  vertical-align: top;
}
.pagination-previous:focus,
.pagination-next:focus,
.pagination-link:focus,
.pagination-ellipsis:focus, .file-cta:focus,
.file-name:focus, .select select:focus, .age-gate__select-wrapper select:focus, .input:focus,
.textarea:focus, .button:focus, .age-gate__confirm_btn:focus, .is-focused.pagination-previous,
.is-focused.pagination-next,
.is-focused.pagination-link,
.is-focused.pagination-ellipsis, .is-focused.file-cta,
.is-focused.file-name, .select select.is-focused, .age-gate__select-wrapper select.is-focused, .is-focused.input,
.is-focused.textarea, .is-focused.button, .is-focused.age-gate__confirm_btn, .pagination-previous:active,
.pagination-next:active,
.pagination-link:active,
.pagination-ellipsis:active, .file-cta:active,
.file-name:active, .select select:active, .age-gate__select-wrapper select:active, .input:active,
.textarea:active, .button:active, .age-gate__confirm_btn:active, .is-active.pagination-previous,
.is-active.pagination-next,
.is-active.pagination-link,
.is-active.pagination-ellipsis, .is-active.file-cta,
.is-active.file-name, .select select.is-active, .age-gate__select-wrapper select.is-active, .is-active.input,
.is-active.textarea, .is-active.button, .is-active.age-gate__confirm_btn {
  outline: none;
}
[disabled].pagination-previous,
[disabled].pagination-next,
[disabled].pagination-link,
[disabled].pagination-ellipsis, [disabled].file-cta,
[disabled].file-name, .select select[disabled], .age-gate__select-wrapper select[disabled], [disabled].input,
[disabled].textarea, [disabled].button, [disabled].age-gate__confirm_btn, fieldset[disabled] .pagination-previous,
fieldset[disabled] .pagination-next,
fieldset[disabled] .pagination-link,
fieldset[disabled] .pagination-ellipsis, fieldset[disabled] .file-cta,
fieldset[disabled] .file-name, fieldset[disabled] .select select, fieldset[disabled] .age-gate__select-wrapper select, .select fieldset[disabled] select, .age-gate__select-wrapper fieldset[disabled] select, fieldset[disabled] .input,
fieldset[disabled] .textarea, fieldset[disabled] .button, fieldset[disabled] .age-gate__confirm_btn {
  cursor: not-allowed;
}

/* # Media
================================================== */
.media {
  align-items: flex-start;
  display: flex;
  text-align: left;
}
.media .content:not(:last-child) {
  margin-bottom: 0.75rem;
}
.media .media {
  border-top: 1px solid {{ border_color | color_modify: 'alpha', 0.5 }};
  display: flex;
  padding-top: 0.75rem;
}
.media .media .content:not(:last-child),
.media .media .control:not(:last-child) {
  margin-bottom: 0.5rem;
}
.media .media .media {
  padding-top: 0.5rem;
}
.media .media .media + .media {
  margin-top: 0.5rem;
}
.media + .media {
  border-top: 1px solid {{ border_color | color_modify: 'alpha', 0.5 }};
  margin-top: 1rem;
  padding-top: 1rem;
}
.media.is-large + .media {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
}

.media-left,
.media-right {
  flex-basis: auto;
  flex-grow: 0;
  flex-shrink: 0;
}

.media-left {
  margin-right: 1rem;
}

.media-right {
  margin-left: 1rem;
}

.media-content {
  flex-basis: auto;
  flex-grow: 1;
  flex-shrink: 1;
  text-align: left;
}

/* # Tile
================================================== */
.tile {
  display: flex;
  align-items: stretch;
  display: block;
  flex-basis: 0;
  flex-grow: 1;
  flex-shrink: 1;
  min-height: min-content;
  overflow: hidden;
}
.tile .tile-image {
  display: block;
}
.tile.is-ancestor {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  margin-top: -0.75rem;
}
.tile.is-ancestor:last-child {
  margin-bottom: -0.75rem;
}
.tile.is-ancestor:not(:last-child) {
  margin-bottom: 0.75rem;
}
.tile.is-child {
  margin: 0 !important;
}
.tile.is-parent {
  padding: 0.75rem;
}
.tile.is-vertical {
  flex-direction: column;
}
.tile.is-vertical > .tile.is-child:not(:last-child) {
  margin-bottom: 1.5rem !important;
}
@media only screen and (min-width: 481px) {
  .tile:not(.is-child) {
    display: flex;
  }
  .tile.is-1 {
    flex: none;
    width: 8.3333333333%;
  }
  .tile.is-2 {
    flex: none;
    width: 16.6666666667%;
  }
  .tile.is-3 {
    flex: none;
    width: 25%;
  }
  .tile.is-4 {
    flex: none;
    width: 33.3333333333%;
  }
  .tile.is-5 {
    flex: none;
    width: 41.6666666667%;
  }
  .tile.is-6 {
    flex: none;
    width: 50%;
  }
  .tile.is-7 {
    flex: none;
    width: 58.3333333333%;
  }
  .tile.is-8 {
    flex: none;
    width: 66.6666666667%;
  }
  .tile.is-9 {
    flex: none;
    width: 75%;
  }
  .tile.is-10 {
    flex: none;
    width: 83.3333333333%;
  }
  .tile.is-11 {
    flex: none;
    width: 91.6666666667%;
  }
  .tile.is-12 {
    flex: none;
    width: 100%;
  }
}

/* # Badge
================================================== */
.header-cart__icon {
  display: flex;
  justify-content: center;
  position: relative;
  width: max-content;
  margin: 0 auto;
}

.badge {
  height: 1rem;
  width: 1rem;
  position: absolute;
  top: calc(1rem * -0.3);
  left: calc(65% - 0.2rem);
  font-size: 0.7rem;
  background: {{ settings.header_cart_badge_color }};
  color: {{ color_background }};
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  letter-spacing: 0;
}

/* # Button
================================================== */
.button, .age-gate__confirm_btn {
  font-family: {{ settings.button__font.family }}, {{ settings.button__font.fallback_families }};
  font-weight: {{ settings.button__font.weight }};
  font-style: {{ settings.button__font.style }};
  letter-spacing: {{ settings.button_letter_spacing | append: 'px' }};
  background-color: hsl(0, 0%, 100%);
  border-color: hsl(0, 0%, 86%);
  border-width: 1px;
  color: hsl(0, 0%, 21%);
  cursor: pointer;
  justify-content: center;
  padding-bottom: calc(0.375em - 1px);
  padding-left: 1em;
  padding-right: 1em;
  padding-top: calc(0.375em - 1px);
  text-align: center;
  text-transform: {{ settings.button_font_style }};
  transition: background 0.2s ease-in-out;
  white-space: nowrap;
}
.button strong, .age-gate__confirm_btn strong {
  color: inherit;
}
.button .icon, .age-gate__confirm_btn .icon, .button .icon.is-small, .button .icon.is-medium, .button .icon.is-large {
  height: 1.5em;
  width: 1.5em;
}
.button .icon:first-child:not(:last-child), .age-gate__confirm_btn .icon:first-child:not(:last-child) {
  margin-left: calc(-0.375em - 1px);
  margin-right: 10px;
}
.button .icon.icon--cart, .age-gate__confirm_btn .icon.icon--cart {
  font-size: 14px;
}
.button .icon.icon--right, .age-gate__confirm_btn .icon.icon--right {
  margin-left: 10px;
  margin-right: calc(-0.375em - 1px);
}
.button .icon:first-child:last-child, .age-gate__confirm_btn .icon:first-child:last-child {
  margin-left: calc(-0.375em - 1px);
  margin-right: calc(-0.375em - 1px);
}
.button:hover, .age-gate__confirm_btn:hover, .button.is-hovered, .is-hovered.age-gate__confirm_btn {
  border-color: {{ settings.link_hover_color }};
  color: {{ settings.link_hover_color }};
}
.button:focus, .age-gate__confirm_btn:focus, .button.is-focused, .is-focused.age-gate__confirm_btn {
  border-color: {{ settings.link_hover_color }};
  color: {{ settings.link_hover_color }};
}
.button:focus:not(:active), .age-gate__confirm_btn:focus:not(:active), .button.is-focused:not(:active), .is-focused.age-gate__confirm_btn:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.button:active, .age-gate__confirm_btn:active, .button.is-active, .is-active.age-gate__confirm_btn {
  border-color: {{ settings.link_hover_color }};
  color: {{ settings.link_hover_color }};
}
.button.is-inverse, .is-inverse.age-gate__confirm_btn {
  background-color: hsl(0, 0%, 21%);
  border-color: transparent;
  color: hsl(0, 0%, 100%);
}
.button.is-text, .is-text.age-gate__confirm_btn {
  background-color: transparent;
  border-color: transparent;
  color: {{ text }};
  text-decoration: underline;
}
.button.is-text:hover, .is-text.age-gate__confirm_btn:hover, .button.is-text.is-hovered, .is-text.is-hovered.age-gate__confirm_btn, .button.is-text:focus, .is-text.age-gate__confirm_btn:focus, .button.is-text.is-focused, .is-text.is-focused.age-gate__confirm_btn {
  background-color: {{ color_background }};
  color: hsl(0, 0%, 21%);
}
.button.is-text:active, .is-text.age-gate__confirm_btn:active, .button.is-text.is-active, .is-text.is-active.age-gate__confirm_btn {
  background-color: {{ color_background | color_darken: 5 }};
  color: hsl(0, 0%, 21%);
}
.button.is-text[disabled], .is-text[disabled].age-gate__confirm_btn, fieldset[disabled] .button.is-text, fieldset[disabled] .is-text.age-gate__confirm_btn {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
}
.button.is-small, .is-small.age-gate__confirm_btn {
  font-size: 0.75rem;
}
.button.is-normal, .is-normal.age-gate__confirm_btn {
  font-size: 1rem;
}
.button.is-medium, .is-medium.age-gate__confirm_btn {
  font-size: 1.25rem;
}
.button.is-large, .is-large.age-gate__confirm_btn {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}
.button[disabled], [disabled].age-gate__confirm_btn, fieldset[disabled] .button, fieldset[disabled] .age-gate__confirm_btn {
  box-shadow: none;
  opacity: 0.5;
}
.button.is-fullwidth, .is-fullwidth.age-gate__confirm_btn {
  display: flex;
  width: 100%;
}
.button.is-loading, .is-loading.age-gate__confirm_btn {
  color: transparent !important;
  pointer-events: none;
}
.button.is-loading::after, .is-loading.age-gate__confirm_btn::after {
  position: absolute;
  left: calc(50% - (1em / 2));
  top: calc(50% - (1em / 2));
  position: absolute !important;
}
.button.is-loading--icon-only, .is-loading--icon-only.age-gate__confirm_btn {
  background: transparent !important;
  border: transparent !important;
  font-size: 2rem !important;
  outline: none;
}
.button.is-loading--icon-only::after, .is-loading--icon-only.age-gate__confirm_btn::after {
  color: {{ text }};
}
.button.is-static, .is-static.age-gate__confirm_btn {
  background-color: hsl(0, 0%, 96%);
  border-color: hsl(0, 0%, 86%);
  color: hsl(0, 0%, 48%);
  box-shadow: none;
  pointer-events: none;
}
.button.is-rounded, .is-rounded.age-gate__confirm_btn {
  border-radius: 290486px;
  padding-left: 1em;
  padding-right: 1em;
}

.buttons {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.buttons.are-small .button:not(.is-normal):not(.is-medium):not(.is-large), .buttons.are-small .age-gate__confirm_btn:not(.is-normal):not(.is-medium):not(.is-large) {
  font-size: 0.75rem;
}
.buttons.are-medium .button:not(.is-small):not(.is-normal):not(.is-large), .buttons.are-medium .age-gate__confirm_btn:not(.is-small):not(.is-normal):not(.is-large) {
  font-size: 1.25rem;
}
.buttons.are-large .button:not(.is-small):not(.is-normal):not(.is-medium), .buttons.are-large .age-gate__confirm_btn:not(.is-small):not(.is-normal):not(.is-medium) {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}
.buttons.has-addons .button:not(:first-child), .buttons.has-addons .age-gate__confirm_btn:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.buttons.has-addons .button:not(:last-child), .buttons.has-addons .age-gate__confirm_btn:not(:last-child) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  margin-right: -1px;
}
.buttons.has-addons .button:last-child, .buttons.has-addons .age-gate__confirm_btn:last-child {
  margin-right: 0;
}
.buttons.has-addons .button:hover, .buttons.has-addons .age-gate__confirm_btn:hover, .buttons.has-addons .button.is-hovered, .buttons.has-addons .is-hovered.age-gate__confirm_btn {
  z-index: 2;
}
.buttons.has-addons .button:focus, .buttons.has-addons .age-gate__confirm_btn:focus, .buttons.has-addons .button.is-focused, .buttons.has-addons .is-focused.age-gate__confirm_btn, .buttons.has-addons .button:active, .buttons.has-addons .age-gate__confirm_btn:active, .buttons.has-addons .button.is-active, .buttons.has-addons .is-active.age-gate__confirm_btn, .buttons.has-addons .button.is-selected, .buttons.has-addons .is-selected.age-gate__confirm_btn {
  z-index: 3;
}
.buttons.has-addons .button:focus:hover, .buttons.has-addons .age-gate__confirm_btn:focus:hover, .buttons.has-addons .button.is-focused:hover, .buttons.has-addons .is-focused.age-gate__confirm_btn:hover, .buttons.has-addons .button:active:hover, .buttons.has-addons .age-gate__confirm_btn:active:hover, .buttons.has-addons .button.is-active:hover, .buttons.has-addons .is-active.age-gate__confirm_btn:hover, .buttons.has-addons .button.is-selected:hover, .buttons.has-addons .is-selected.age-gate__confirm_btn:hover {
  z-index: 4;
}
.buttons.has-addons .button.is-expanded, .buttons.has-addons .is-expanded.age-gate__confirm_btn {
  flex-grow: 1;
}
.buttons.is-center {
  justify-content: center;
}
.buttons.is-right {
  justify-content: flex-end;
}

{% assign btn_primary_alpha = settings.button_primary_bg_color_transparency | divided_by: 100.00 %}
  {% assign btn_primary_hover_alpha = settings.button_primary_bg_color_transparency--highlight | divided_by: 100.00 %}
  {% assign btn_secondary_alpha = settings.button_secondary_bg_color_transparency | divided_by: 100.00 %}
  {% assign btn_secondary_hover_alpha = settings.button_secondary_bg_color_transparency--highlight | divided_by: 100.00 %}
{% liquid
    if settings.button_primary_padding == 'small'
      assign btn_primary_font_size = '0.75rem'
      assign btn_primary_line_height = 'inherit'
      assign btn_primary_width = 'auto'
    elsif settings.button_primary_padding == 'regular'
      assign btn_primary_font_size = '1rem'
      assign btn_primary_line_height = 'inherit'
      assign btn_primary_width = 'auto'
    else
      assign btn_primary_font_size = '1.25rem'
      assign btn_primary_line_height = '1.25'
      assign btn_primary_width = '100%'
    endif
  %}
{% liquid
    if settings.button_secondary_padding == 'small'
      assign btn_secondary_font_size = '0.75rem'
      assign btn_secondary_line_height = 'inherit'
      assign btn_secondary_width = 'auto'
    elsif settings.button_secondary_padding == 'regular'
      assign btn_secondary_font_size = '1rem'
      assign btn_secondary_line_height = 'inherit'
      assign btn_secondary_width = 'auto'
    else
      assign btn_secondary_font_size = '1.25rem'
      assign btn_secondary_line_height = '1.25'
      assign btn_secondary_width = '100%'
    endif
  %}
{% liquid
    if settings.button_link_padding == 'small'
      assign btn_link_style_font_size = '0.75rem'
      assign btn_link_style_line_height = 'inherit'
    elsif settings.button_link_padding == 'regular'
      assign btn_link_style_font_size = '1rem'
      assign btn_link_style_line_height = 'inherit'
    else
      assign btn_link_style_font_size = '1.25rem'
      assign btn_link_style_line_height = '1.25'
    endif
  %}
{% liquid
    if settings.button_cart_padding == 'small'
      assign btn_add_to_cart_font_size = '0.75rem'
      assign btn_add_to_cart_line_height = 'inherit'
      assign btn_add_to_cart_width = '100%'
    elsif settings.button_cart_padding == 'regular'
      assign btn_add_to_cart_font_size = '1rem'
      assign btn_add_to_cart_line_height = 'inherit'
      assign btn_add_to_cart_width = '100%'
    else
      assign btn_add_to_cart_font_size = '1.25rem'
      assign btn_add_to_cart_line_height = '1.25'
      assign btn_add_to_cart_width = '100% !important'
    endif
  %}
{% liquid
    assign button_link_underline_thickness = settings.button_link_underline_thickness | append: 'px'
    if settings.button_link_underline_thickness == 1
      assign button_link_underline_thickess = 'thin'
    endif
  %}
.button--primary,
a.button--primary.button {
  font-size: {{ btn_primary_font_size }};
  border-radius: {{ settings.button_primary_border_radius | append: 'px' }};
  width: {{ btn_primary_width }};
  line-height: {{ btn_primary_line_height }};
  height: auto;
  max-width: 100%;
  white-space: normal;
  overflow-wrap: normal;
}
.button--primary, .button--primary:link, .button--primary:visited,
a.button--primary.button,
a.button--primary.button:link,
a.button--primary.button:visited {
  color: {{ settings.button_primary_text_color }};
  background-color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
  border-color: {{ settings.button_primary_border_color }};
}
.button--primary:hover, .button--primary.is-hovered,
a.button--primary.button:hover,
a.button--primary.button.is-hovered {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.button--primary:focus, .button--primary.is-focused,
a.button--primary.button:focus,
a.button--primary.button.is-focused {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.button--primary:focus:not(:active), .button--primary.is-focused:not(:active),
a.button--primary.button:focus:not(:active),
a.button--primary.button.is-focused:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.button--primary:active, .button--primary.is-active,
a.button--primary.button:active,
a.button--primary.button.is-active {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.button--primary.is-inverted,
a.button--primary.button.is-inverted {
  color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
  background-color: {{ settings.button_primary_text_color }};
  border-color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
}
.button--primary.is-small,
a.button--primary.button.is-small {
  font-size: 0.75rem;
}
.button--primary.is-normal,
a.button--primary.button.is-normal {
  font-size: 1rem;
}
.button--primary.is-medium,
a.button--primary.button.is-medium {
  font-size: 1.25rem;
}
.button--primary.is-large,
a.button--primary.button.is-large {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}

.button--secondary,
.action_button--secondary,
a.button--secondary.button {
  font-size: {{ btn_secondary_font_size }};
  border-radius: {{ settings.button_secondary_border_radius | append: 'px' }};
  line-height: {{ btn_secondary_line_height }};
  width: {{ btn_secondary_width }};
  height: auto;
  max-width: 100%;
  white-space: normal;
}
.button--secondary, .button--secondary:link, .button--secondary:visited,
.action_button--secondary,
.action_button--secondary:link,
.action_button--secondary:visited,
a.button--secondary.button,
a.button--secondary.button:link,
a.button--secondary.button:visited {
  color: {{ settings.button_secondary_text_color }};
  background-color: {{ settings.button_secondary_bg_color | color_modify: 'alpha', btn_secondary_alpha }};
  border-color: {{ settings.button_secondary_border_color }};
}
.button--secondary:hover, .button--secondary.is-hovered,
.action_button--secondary:hover,
.action_button--secondary.is-hovered,
a.button--secondary.button:hover,
a.button--secondary.button.is-hovered {
  color: {{ settings.button_secondary_text_color--highlight }};
  border-color: {{ settings.button_secondary_border_color--highlight }};
  background-color: {{ settings.button_secondary_bg_color--highlight | color_modify: 'alpha', btn_secondary_hover_alpha }};
}
.button--secondary:focus, .button--secondary.is-focused,
.action_button--secondary:focus,
.action_button--secondary.is-focused,
a.button--secondary.button:focus,
a.button--secondary.button.is-focused {
  color: {{ settings.button_secondary_text_color--highlight }};
  border-color: {{ settings.button_secondary_border_color--highlight }};
  background-color: {{ settings.button_secondary_bg_color--highlight | color_modify: 'alpha', btn_secondary_hover_alpha }};
}
.button--secondary:focus:not(:active), .button--secondary.is-focused:not(:active),
.action_button--secondary:focus:not(:active),
.action_button--secondary.is-focused:not(:active),
a.button--secondary.button:focus:not(:active),
a.button--secondary.button.is-focused:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.button--secondary:active, .button--secondary.is-active,
.action_button--secondary:active,
.action_button--secondary.is-active,
a.button--secondary.button:active,
a.button--secondary.button.is-active {
  color: {{ settings.button_secondary_text_color--highlight }};
  border-color: {{ settings.button_secondary_border_color--highlight }};
  background-color: {{ settings.button_secondary_bg_color--highlight | color_modify: 'alpha', btn_secondary_hover_alpha }};
}
.button--secondary.is-inverted,
.action_button--secondary.is-inverted,
a.button--secondary.button.is-inverted {
  color: {{ settings.button_secondary_bg_color | color_modify: 'alpha', btn_secondary_alpha }};
  background-color: {{ settings.button_secondary_text_color }};
  border-color: {{ settings.button_secondary_bg_color | color_modify: 'alpha', btn_secondary_alpha }};
}
.button--secondary.is-small,
.action_button--secondary.is-small,
a.button--secondary.button.is-small {
  font-size: 0.75rem;
}
.button--secondary.is-normal,
.action_button--secondary.is-normal,
a.button--secondary.button.is-normal {
  font-size: 1rem;
}
.button--secondary.is-medium,
.action_button--secondary.is-medium,
a.button--secondary.button.is-medium {
  font-size: 1.25rem;
}
.button--secondary.is-large,
.action_button--secondary.is-large,
a.button--secondary.button.is-large {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}

.button--link-style,
a.button--link-style.button {
  font-size: {{ btn_link_style_font_size }};
  border: none;
  border-bottom: {{ button_link_underline_thickness }} {{ settings.button_link_underline_style }};
  height: auto;
  line-height: {{ btn_link_style_line_height }};
  padding: 0;
  transition: 0.3s linear;
  background: transparent;
  max-width: 100%;
  white-space: normal;
}
.button--link-style, .button--link-style:link, .button--link-style:visited,
a.button--link-style.button,
a.button--link-style.button:link,
a.button--link-style.button:visited {
  color: {{ settings.button_link_text_color }};
  border-bottom-color: {{ settings.button_link_text_color }};
}
.button--link-style:hover, .button--link-style.is-hovered,
a.button--link-style.button:hover,
a.button--link-style.button.is-hovered {
  color: {{ settings.button_link_text_color--highlight }};
  border-bottom-color: {{ settings.button_link_text_color--highlight }} !important;
}
.button--link-style:focus, .button--link-style.is-focused,
a.button--link-style.button:focus,
a.button--link-style.button.is-focused {
  color: {{ settings.button_link_text_color--highlight }};
  border-bottom-color: {{ settings.button_link_text_color--highlight }};
}
.button--link-style:active, .button--link-style.is-active,
a.button--link-style.button:active,
a.button--link-style.button.is-active {
  color: {{ settings.button_link_text_color--highlight }};
  border-bottom-color: {{ settings.button_link_text_color--highlight }};
}
.button--link-style.is-within-form,
a.button--link-style.button.is-within-form {
  padding-bottom: calc(0.375em - 1px);
  padding-left: 1em;
  padding-right: 1em;
  padding-top: calc(0.375em - 1px);
}

.button--add-to-cart,
a.button--add-to-cart.button {
  font-size: {{ btn_add_to_cart_font_size }};
  border-radius: {{ settings.button_cart_border_radius | append: 'px' }};
  width: {{ btn_add_to_cart_width }};
  line-height: {{ btn_add_to_cart_line_height }};
}
.button--add-to-cart, .button--add-to-cart:link, .button--add-to-cart:visited,
a.button--add-to-cart.button,
a.button--add-to-cart.button:link,
a.button--add-to-cart.button:visited {
  color: {{ settings.button_cart_text_color }};
  background-color: {{ settings.button_cart_bg_color }};
  border-color: {{ settings.button_cart_border_color }};
}
.button--add-to-cart:hover, .button--add-to-cart.is-hovered,
a.button--add-to-cart.button:hover,
a.button--add-to-cart.button.is-hovered {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }} !important;
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.button--add-to-cart:focus, .button--add-to-cart.is-focused,
a.button--add-to-cart.button:focus,
a.button--add-to-cart.button.is-focused {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }};
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.button--add-to-cart:focus:not(:active), .button--add-to-cart.is-focused:not(:active),
a.button--add-to-cart.button:focus:not(:active),
a.button--add-to-cart.button.is-focused:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.button--add-to-cart:active, .button--add-to-cart.is-active,
a.button--add-to-cart.button:active,
a.button--add-to-cart.button.is-active {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }};
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.button--add-to-cart.is-inverted,
a.button--add-to-cart.button.is-inverted {
  color: {{ settings.button_cart_bg_color }};
  background-color: {{ settings.button_cart_text_color }};
  border-color: {{ settings.button_cart_bg_color }};
}
.button--add-to-cart.is-small,
a.button--add-to-cart.button.is-small {
  font-size: 0.75rem;
}
.button--add-to-cart.is-normal,
a.button--add-to-cart.button.is-normal {
  font-size: 1rem;
}
.button--add-to-cart.is-medium,
a.button--add-to-cart.button.is-medium {
  font-size: 1.25rem;
}
.button--add-to-cart.is-large,
a.button--add-to-cart.button.is-large {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}

.shopify-challenge__button {
  font-size: {{ btn_primary_font_size }};
  border-radius: {{ settings.button_primary_border_radius | append: 'px' }};
  width: {{ btn_primary_width }};
  line-height: {{ btn_primary_line_height }};
  height: auto;
  max-width: 100%;
  white-space: normal;
  overflow-wrap: normal;
  padding: 0.5em 4em;
}
.shopify-challenge__button, .shopify-challenge__button:link, .shopify-challenge__button:visited {
  color: {{ settings.button_primary_text_color }};
  background-color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
  border-color: {{ settings.button_primary_border_color }};
}
.shopify-challenge__button:hover, .shopify-challenge__button.is-hovered {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.shopify-challenge__button:focus, .shopify-challenge__button.is-focused {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.shopify-challenge__button:focus:not(:active), .shopify-challenge__button.is-focused:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.shopify-challenge__button:active, .shopify-challenge__button.is-active {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.shopify-challenge__button.is-inverted {
  color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
  background-color: {{ settings.button_primary_text_color }};
  border-color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
}
.shopify-challenge__button.is-small {
  font-size: 0.75rem;
}
.shopify-challenge__button.is-normal {
  font-size: 1rem;
}
.shopify-challenge__button.is-medium {
  font-size: 1.25rem;
}
.shopify-challenge__button.is-large {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}

/* # Content
================================================== */
{% liquid
    assign content_table_cell_border_width = 0
    if settings.table_styles_enabled
      assign content_table_cell_border_width = 1
    endif
  %}
.content li + li,
.shopify-policy__body li + li {
  margin-top: 0.25em;
}
.content p:not(:last-child),
.content dl:not(:last-child),
.content ol:not(:last-child),
.content ul:not(:last-child),
.content blockquote:not(:last-child),
.content pre:not(:last-child),
.content table:not(:last-child),
.shopify-policy__body p:not(:last-child),
.shopify-policy__body dl:not(:last-child),
.shopify-policy__body ol:not(:last-child),
.shopify-policy__body ul:not(:last-child),
.shopify-policy__body blockquote:not(:last-child),
.shopify-policy__body pre:not(:last-child),
.shopify-policy__body table:not(:last-child) {
  margin-bottom: 1em;
}
.content h1, .content .age-gate__heading,
.shopify-policy__body h1,
.shopify-policy__body .age-gate__heading {
  font-size: 2em;
  margin-bottom: 0.5em;
  word-wrap: break-word;
}
.content h1:not(:first-child), .content .age-gate__heading:not(:first-child),
.shopify-policy__body h1:not(:first-child),
.shopify-policy__body .age-gate__heading:not(:first-child) {
  margin-top: 1em;
}
.content h2,
.shopify-policy__body h2 {
  font-size: 1.75em;
  margin-bottom: 0.5714em;
  word-wrap: break-word;
}
.content h2:not(:first-child),
.shopify-policy__body h2:not(:first-child) {
  margin-top: 1.1428em;
}
.content h3,
.shopify-policy__body h3 {
  font-size: 1.5em;
  margin-bottom: 0.6666em;
  word-wrap: break-word;
}
.content h3:not(:first-child),
.shopify-policy__body h3:not(:first-child) {
  margin-top: 1.3333em;
}
.content h4,
.shopify-policy__body h4 {
  font-size: 1.25em;
  margin-bottom: 0.8em;
  word-wrap: break-word;
}
.content h5,
.shopify-policy__body h5 {
  font-size: 1.125em;
  margin-bottom: 0.8888em;
  word-wrap: break-word;
}
.content h6,
.shopify-policy__body h6 {
  font-size: 1em;
  margin-bottom: 1em;
  word-wrap: break-word;
}
.content blockquote,
.shopify-policy__body blockquote {
  background-color: {{ color_background }};
  border-left: 5px solid {{ border_color }};
  padding: 1.25em 1.5em;
}
.content ol,
.shopify-policy__body ol {
  list-style-position: outside;
  margin-left: 2em;
  margin-top: 1em;
}
.content ol:not([type]),
.shopify-policy__body ol:not([type]) {
  list-style-type: decimal;
}
.content ol:not([type]).is-lower-alpha,
.shopify-policy__body ol:not([type]).is-lower-alpha {
  list-style-type: lower-alpha;
}
.content ol:not([type]).is-lower-roman,
.shopify-policy__body ol:not([type]).is-lower-roman {
  list-style-type: lower-roman;
}
.content ol:not([type]).is-upper-alpha,
.shopify-policy__body ol:not([type]).is-upper-alpha {
  list-style-type: upper-alpha;
}
.content ol:not([type]).is-upper-roman,
.shopify-policy__body ol:not([type]).is-upper-roman {
  list-style-type: upper-roman;
}
.content ul,
.shopify-policy__body ul {
  list-style: disc outside;
  margin-left: 2em;
  margin-top: 1em;
}
.content ul ul,
.shopify-policy__body ul ul {
  list-style-type: circle;
  margin-top: 0.5em;
}
.content ul ul ul,
.shopify-policy__body ul ul ul {
  list-style-type: square;
}
.content dd,
.shopify-policy__body dd {
  margin-left: 2em;
}
.content figure,
.shopify-policy__body figure {
  margin-left: 2em;
  margin-right: 2em;
  text-align: center;
}
.content figure:not(:first-child),
.shopify-policy__body figure:not(:first-child) {
  margin-top: 2em;
}
.content figure:not(:last-child),
.shopify-policy__body figure:not(:last-child) {
  margin-bottom: 2em;
}
.content figure img,
.shopify-policy__body figure img {
  display: inline-block;
}
.content figure figcaption,
.shopify-policy__body figure figcaption {
  font-style: italic;
}
.content pre,
.shopify-policy__body pre {
  -webkit-overflow-scrolling: touch;
  overflow-x: auto;
  padding: 1.25em 1.5em;
  white-space: pre;
  word-wrap: normal;
}
.content sup,
.content sub,
.shopify-policy__body sup,
.shopify-policy__body sub {
  font-size: 75%;
}
.content table,
.shopify-policy__body table {
  width: 100%;
}
.content table td,
.content table th,
.shopify-policy__body table td,
.shopify-policy__body table th {
  border: 1px solid {{ border_color }};
  border-width: {{ content_table_cell_border_width | append: 'px' }};
  padding: 0.5em 0.75em;
  vertical-align: top;
}
.content table th,
.shopify-policy__body table th {
  color: hsl(0, 0%, 21%);
  text-align: left;
}
.content table thead td,
.content table thead th,
.shopify-policy__body table thead td,
.shopify-policy__body table thead th {
  border-width: 0 0 2px;
  color: hsl(0, 0%, 21%);
}
.content table tfoot td,
.content table tfoot th,
.shopify-policy__body table tfoot td,
.shopify-policy__body table tfoot th {
  border-width: 2px 0 0;
  color: hsl(0, 0%, 21%);
}
.content.is-small,
.shopify-policy__body.is-small {
  font-size: 0.75rem;
}
.content.is-medium,
.shopify-policy__body.is-medium {
  font-size: 1.25rem;
}
.content.is-large,
.shopify-policy__body.is-large {
  font-size: 1.5rem;
}

/* #Currency / Language switcher
================================================== */
.selectors-form {
  margin-bottom: 0;
}

.selectors-form__wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
@media only screen and (min-width: 799px) and (max-width: 1024px) {
  .selectors-form__wrap {
    justify-content: center;
    width: 100%;
    margin-bottom: 10px;
  }
}

.selectors-form__item {
  margin-right: 20px;
}
.selectors-form__item:last-child {
  margin-right: 0;
}

.disclosure {
  position: relative;
}

.disclosure .disclosure__list-wrap {
  min-width: 100%;
}

@media only screen and (min-width: 799px) and (max-width: 1024px) {
  .disclosure--i18n,
  .disclosure--currency {
    margin-right: 0;
  }
}

.disclosure-text-style-none button.disclosure__toggle,
.disclosure-text-style-none button.disclosure__button {
  text-transform: capitalize;
}

.disclosure-text-style-uppercase button.disclosure__toggle,
.disclosure-text-style-uppercase button.disclosure__button {
  text-transform: uppercase;
}

.disclosure-text-style-lowercase button.disclosure__toggle,
.disclosure-text-style-lowercase button.disclosure__button {
  text-transform: lowercase;
}

.disclosure__toggle {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  padding: 10px;
  margin-top: 5px;
  font-family: {{ settings.nav__font.family }}, {{ settings.nav__font.fallback_families }};
  font-size: {{ settings.nav_font_size | append: 'px' }};
  font-style: {{ settings.nav__font.style }};
  font-weight: {{ settings.nav__font.weight }};
  color: {{ settings.regular_color }};
  text-transform: {{ settings.nav_font_style }};
  cursor: pointer;
  background-color: {{ settings.shop_bg_color }};
  border: 1px solid {{ settings.border_color }};
  border-radius: 2px;
}
.disclosure__toggle:active {
  border-color: {{ settings.border_color }};
  box-shadow: none;
}
.disclosure__toggle.is-clicked, .disclosure__toggle:focus, .disclosure__toggle:hover, .disclosure__toggle:focus-within {
  color: {{ settings.regular_color | color_modify: 'alpha', 0.33 }};
  background-color: {{ settings.shop_bg_color }};
  border-color: {{ settings.border_color }};
}
.disclosure__toggle.is-clicked .icon, .disclosure__toggle:focus .icon, .disclosure__toggle:hover .icon, .disclosure__toggle:focus-within .icon {
  transform: rotate(180deg);
}
.disclosure__toggle.is-clicked + .disclosure__list-wrap, .disclosure__toggle:focus + .disclosure__list-wrap, .disclosure__toggle:focus-within + .disclosure__list-wrap {
  pointer-events: all;
  visibility: visible;
  opacity: 1;
}
.disclosure__toggle .icon {
  flex: none;
  width: 1rem;
  height: 1rem;
  margin-left: 10px;
  pointer-events: none;
  transition: transform 0.2s linear;
  transform: rotate(0deg);
}

.disclosure-list {
  max-height: 400px;
  padding: 0 10px;
  margin-bottom: 0;
  margin-left: 0;
  overflow-y: auto;
  list-style: none;
  background-color: {{ settings.shop_bg_color }};
  border: 1px solid {{ settings.border_color }};
  border-radius: 2px;
}

li.disclosure-list__item {
  padding: 0;
  line-height: 1;
  text-transform: uppercase;
  list-style: none;
}

.disclosure__list-wrap {
  position: absolute;
  bottom: 100%;
  z-index: 12;
  padding-bottom: 10px;
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
}
.disclosure__list-wrap.animated {
  animation-duration: 0.5s;
}

button.disclosure__button {
  display: block;
  width: 100%;
  padding: 10px 0;
  font-family: {{ settings.nav__font.family }}, {{ settings.nav__font.fallback_families }};
  font-size: {{ settings.nav_font_size | append: 'px' }};
  font-style: {{ settings.nav__font.style }};
  font-weight: {{ settings.nav__font.weight }};
  color: {{ settings.regular_color }};
  text-align: left;
  text-transform: {{ settings.nav_font_style }};
  cursor: pointer;
  background-color: transparent;
  border: 0;
}
button.disclosure__button:hover, button.disclosure__button[aria-current=true] {
  color: {{ settings.regular_color | color_modify: 'alpha', 0.33 }};
}

/* Mobile currency/language switcher */
.selectors-form--mobile {
  padding: 0;
}
.selectors-form--mobile .selectors-wrap {
  flex-direction: column;
  justify-content: flex-start;
}
.selectors-form--mobile .selectors-form__item {
  max-width: 100%;
}
.selectors-form--mobile .disclosure {
  position: relative;
  z-index: 2;
  display: block;
  align-items: center;
  margin-top: 5px;
  color: {{ settings.regular_color }};
  cursor: pointer;
  background-color: {{ settings.shop_bg_color }};
  border: 1px solid {{ settings.border_color }};
  border-radius: 2px;
}
.selectors-form--mobile .disclosure:hover, .selectors-form--mobile .disclosure[aria-current=true] {
  color: {{ settings.regular_color | color_modify: 'alpha', 0.33 }};
}
.selectors-form--mobile .disclosure.is-clicked .icon {
  transform: rotate(180deg);
}
.selectors-form--mobile .disclosure .icon {
  transition: transform 0.2s linear;
  transform: rotate(0deg);
}
.selectors-form--mobile .disclosure__list-wrap {
  padding-bottom: 3px;
}
.selectors-form--mobile .disclosure__toggle {
  margin-top: 0;
  text-align: left;
  border: none;
}

/* # Icon
================================================== */
.icon {
  align-items: center;
  display: inline-flex;
  justify-content: center;
  height: 1.2rem;
  width: 1.2rem;
  fill: currentColor;
}
.is-large .icon {
  height: 1.8rem;
  width: 1.8rem;
}
.is-medium .icon {
  height: 1.2rem;
  width: 1.2rem;
}
.is-small .icon {
  height: 1rem;
  width: 1rem;
}
.icon svg {
  height: 100%;
  width: 100%;
}

/* # Image
================================================== */
/* Lazyloading styles */
{%- if settings.image_loading_style == 'appear' -%}
.transition--appear {
    opacity: 0;
    transition: opacity 0s !important, transform 0.3s ease-in-out;
  }
.transition--appear.lazyloaded {
    opacity: 1;
  }
{%- elsif settings.image_loading_style == 'fade-in' -%}
.transition--fade-in {
    opacity: 0;
    transition: opacity 0.3s ease-in;
  }
.transition--fade-in.lazyloaded {
    opacity: 1;
  }
{%- elsif settings.image_loading_style == 'blur-up' -%}
.transition--blur-up {
    -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
    filter: blur(5px);
    transform: translate3d(0, 0, 0);
    transition: filter 200ms;
  }
.transition--blur-up.lazyloaded {
    filter: blur(0px);
    transform: scale(1);
  }
{%- elsif settings.image_loading_style == 'color' -%}
.transition--color {
    opacity: 0;
    transition: opacity 0.3s ease-in;
  }
.transition--color.lazyloaded {
    opacity: 1;
  }
.image-element__wrap[style*=".png"] {
    background: transparent !important;
  }
{%- elsif settings.image_loading_style == 'none' -%}
{%- endif -%}
.image__container {
  display: block;
  margin-left: auto;
  margin-right: auto;
  font-size: 0;
}
.image__container img {
  width: 100%;
}

.image-element__wrap {
  overflow: hidden;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;
  height: 100%;
}
@media only screen and (max-width: 798px) {
  .has-image-crop--mobile-true .image-element__wrap {
    max-width: 100% !important; /* Overwrite stretch_width parameter */
  }
}

.image-element__wrap img {
  height: 100%;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

.has-image-crop .image-element__wrap {
  height: 100%;
}
.has-image-crop img {
  height: 100% !important;
  object-fit: cover;
}
.has-image-crop.image-crop--left img {
  object-fit: cover;
  object-position: left;
}
.has-image-crop.image-crop--right img {
  object-fit: cover;
  object-position: right;
}
.has-image-crop.image-crop--center img {
  object-fit: cover;
  object-position: center;
}
.has-image-crop.image-crop--none {
  height: auto;
}
.has-image-crop.image-crop--none img {
  position: static;
  height: auto !important;
  object-fit: fill;
}

/* # Notification
================================================== */
.notification {
  background-color: hsl(0, 0%, 86%);
  border-radius: 4px;
  padding: 1.25rem 2.5rem 1.25rem 1.5rem;
  position: relative;
}
.notification a:not(.button):not(.age-gate__confirm_btn):not(.dropdown-item) {
  color: currentColor;
  text-decoration: underline;
}
.notification strong {
  color: currentColor;
}
.notification code,
.notification pre {
  background: hsl(0, 0%, 100%);
}
.notification pre code {
  background: transparent;
}
.notification > .delete {
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
}
.notification .title,
.notification .subtitle,
.notification .content {
  color: currentColor;
}

/* # Other
================================================== */
.highlight {
  font-weight: 400;
  max-width: 100%;
  overflow: hidden;
  padding: 0;
}
.highlight pre {
  overflow: auto;
  max-width: 100%;
}

.number {
  align-items: center;
  background-color: {{ color_background }};
  border-radius: 290486px;
  display: inline-flex;
  font-size: 1.25rem;
  height: 2em;
  justify-content: center;
  margin-right: 1.5rem;
  min-width: 2.5em;
  padding: 0.25rem 0.5rem;
  text-align: center;
  vertical-align: top;
}

/* # Placeholder
================================================== */
.placeholder-svg {
  display: block;
  fill: {{ text | color_modify: 'alpha', 0.35 }};
  background-color: {{ text | color_modify: 'alpha', 0.1 }};
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  border: 1px solid {{ text | color_modify: 'alpha', 0.2 }};
}

.placeholder-background {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.placeholder-background .icon {
  border: 0;
}

/* # Social share
================================================== */
.share-btn.share-btn--x .button, .share-btn.share-btn--x .age-gate__confirm_btn {
  color: #000000;
}
.share-btn.share-btn--x .button:hover, .share-btn.share-btn--x .age-gate__confirm_btn:hover {
  border-color: #000000;
  background-color: #000000;
}
.share-btn.share-btn--facebook .button, .share-btn.share-btn--facebook .age-gate__confirm_btn {
  color: #4266B2;
}
.share-btn.share-btn--facebook .button:hover, .share-btn.share-btn--facebook .age-gate__confirm_btn:hover {
  border-color: #4266B2;
  background-color: #4266B2;
}
.share-btn.share-btn--pinterest .button, .share-btn.share-btn--pinterest .age-gate__confirm_btn {
  color: #E50122;
}
.share-btn.share-btn--pinterest .button:hover, .share-btn.share-btn--pinterest .age-gate__confirm_btn:hover {
  border-color: #E50122;
  background-color: #E50122;
}
.share-btn.share-btn--mail .button, .share-btn.share-btn--mail .age-gate__confirm_btn {
  color: #F14336;
}
.share-btn.share-btn--mail .button:hover, .share-btn.share-btn--mail .age-gate__confirm_btn:hover {
  border-color: #F14336;
  background-color: #F14336;
}
.share-btn .button, .share-btn .age-gate__confirm_btn {
  width: 38px;
  height: 38px;
}
.share-btn .button:hover, .share-btn .age-gate__confirm_btn:hover {
  color: #FFFFFF;
}

.social-share-buttons--rounded .button, .social-share-buttons--rounded .age-gate__confirm_btn {
  border-radius: 50%;
}

/* # Tag
================================================== */
.tags {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.tags .tag {
  margin-bottom: 0.5rem;
}
.tags .tag.tag--solid {
  background-color: {{ settings.link_color | color_modify: 'alpha', 0.10 }};
}
.tags .tag.tag--solid a {
  color: {{ link }};
}
.tags .tag.tag--solid:hover {
  background-color: {{ settings.link_color | color_modify: 'alpha', 1 }};
}
.tags .tag.tag--solid:hover a {
  color: {{ link_invert }};
}
.tags .tag.tag--outline {
  background-color: transparent;
  border: 1px solid {{ link }};
}
.tags .tag.tag--outline a {
  color: {{ link }};
}
.tags .tag.tag--outline:hover {
  background-color: {{ link }};
}
.tags .tag.tag--outline:hover a {
  color: {{ link_invert }};
}
.tags .tag:not(:last-child) {
  margin-right: 0.5rem;
}
.tags:last-child {
  margin-bottom: -0.5rem;
}
.tags:not(:last-child) {
  margin-bottom: 0;
}
.tags.are-medium .tag:not(.is-normal):not(.is-large) {
  font-size: 1rem;
}
.tags.are-large .tag:not(.is-normal):not(.is-medium) {
  font-size: 1.25rem;
}
.tags.are-outline .tag:not(.is-regular) {
  background-color: transparent;
  border: 1px solid {{ text }};
}
.tags.has-addons .tag {
  margin-right: 0;
}
.tags.has-addons .tag:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.tags.has-addons .tag:not(:last-child) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.tags.is-center {
  justify-content: center;
}
.tags.is-center .tag {
  margin-right: 0.25rem;
  margin-left: 0.25rem;
}
.tags.is-right {
  justify-content: flex-end;
}
.tags.is-right .tag:not(:first-child) {
  margin-left: 0.5rem;
}
.tags.is-right .tag:not(:last-child) {
  margin-right: 0;
}
.tags.has-addons .tag {
  margin-right: 0;
}
.tags.has-addons .tag:not(:first-child) {
  margin-left: 0;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.tags.has-addons .tag:not(:last-child) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.tag:not(body) {
  align-items: center;
  background-color: hsl(0, 0%, 86%);
  border-radius: 4px;
  color: {{ text }};
  display: inline-flex;
  font-size: 0.75rem;
  height: 2em;
  justify-content: center;
  line-height: 1.5;
  padding-left: 0.75em;
  padding-right: 0.75em;
  white-space: nowrap;
  margin-bottom: 0.3em;
  transition: background-color 0.3s ease-in-out;
}
.tag:not(body) a {
  color: {{ text }};
}
.tag:not(body) .delete {
  margin-left: 0.25rem;
  margin-right: -0.375rem;
}
.tag:not(body).is-normal {
  font-size: 0.75rem;
}
.tag:not(body).is-medium {
  font-size: 1rem;
}
.tag:not(body).is-large {
  font-size: 1.25rem;
}
.tag:not(body) .icon:first-child:not(:last-child) {
  margin-left: -0.375em;
  margin-right: 0.1875em;
}
.tag:not(body) .icon:last-child:not(:first-child) {
  margin-left: 0.1875em;
  margin-right: -0.375em;
}
.tag:not(body) .icon:first-child:last-child {
  margin-left: -0.375em;
  margin-right: -0.375em;
}
.tag:not(body).is-delete {
  margin-left: 1px;
  padding: 0;
  position: relative;
  width: 2em;
}
.tag:not(body).is-delete::before, .tag:not(body).is-delete::after {
  background-color: currentColor;
  content: "";
  display: block;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform-origin: center center;
}
.tag:not(body).is-delete::before {
  height: 1px;
  width: 50%;
}
.tag:not(body).is-delete::after {
  height: 50%;
  width: 1px;
}
.tag:not(body).is-delete:hover, .tag:not(body).is-delete:focus {
  background-color: #cfcfcf;
}
.tag:not(body).is-delete:active {
  background-color: #c2c2c2;
}
.tag:not(body).is-rounded {
  border-radius: 290486px;
}
.tag:not(body).is-outline {
  background-color: transparent;
  border: 1px solid {{ text }};
}

a.tag:hover {
  text-decoration: underline;
}

.tag--sale:not(body) {
  background-color: {{ sale_sticker_color }};
  color: {{ sale_sticker_color_text }};
}

.tag--new:not(body) {
  background-color: {{ new_sticker_color }};
  color: {{ new_sticker_color_text }};
}

.tag--best-seller:not(body) {
  background-color: {{ bestseller_sticker_color }};
  color: {{ bestseller_sticker_color_text }};
}

.tag--coming-soon:not(body) {
  background-color: {{ comingsoon_sticker_color }};
  color: {{ comingsoon_sticker_color_text }};
}

.tag--staff-pick:not(body) {
  background-color: {{ staffpick_sticker_color }};
  color: {{ staffpick_sticker_color_text }};
}

.tag--pre-order:not(body) {
  background-color: {{ preorder_sticker_color }};
  color: {{ preorder_sticker_color_text }};
}

/* # AJAX cart - mini and drawer
================================================== */
.cart__count--text::before,
.ajax-cart__cart-item-count::before {
  content: "(";
}
.cart__count--text::after,
.ajax-cart__cart-item-count::after {
  content: ")";
}

#theme-ajax-cart {
  z-index: 100;
  box-shadow: 0 8px 8px rgba(10, 10, 10, 0.1);
}
@media only screen and (min-width: 799px) {
  #theme-ajax-cart {
    max-height: 100vh;
    overflow-y: auto;
  }
}

.ajax-cart {
  width: 100%;
  font-size: {{ settings.dropdown_font_size | append: 'px' }};
  color: {{ text }};
  text-shadow: none;
  text-transform: {{ settings.dropdown_font_style }};
  letter-spacing: {{ settings.dropdown_letter_spacing }};
}
.ajax-cart a,
.ajax-cart a:active,
.ajax-cart a:visited {
  color: {{ drop_down_menu_active_color }};
}
.ajax-cart a:hover {
  color: {{ settings.dropdown_link_hover_color }};
}

.is-drawer,
.is-mini-cart .card {
  background-color: {{ settings.cart_background_color }};
}

.is-drawer.is-visible {
  overflow-y: auto;
}

.ajax-cart__form {
  max-height: 65vh;
  /* overflow: scroll; */
}

.ajax-cart__cart-count {
  position: relative;
  text-align: center;
}

.ajax-cart__close-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 1rem;
  line-height: 0;
  cursor: pointer;
}

.ajax-cart__cart-title {
  padding: 1rem 1rem;
  font-weight: bold;
  border-bottom: 2px solid {{ border_color | color_modify: 'alpha', 0.5 }};
}

.ajax-cart__product {
  position: relative;
  align-items: center;
  min-height: 120px;
  padding: 1rem 1rem 0;
  margin-top: 0;
}
.ajax-cart__product:last-child {
  margin-bottom: 1rem;
}

.ajax-cart__product-image {
  width: 20%;
}

.ajax-cart__quantity-box {
  max-width: 150px;
  margin-top: 10px;
}
.ajax-cart__quantity-box .quantity-wrapper.quantity-style--box .quantity-input-control--fill {
  width: auto;
}

.ajax-cart__right-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  align-self: flex-start;
  justify-content: space-between;
}

.ajax-cart__unit-price {
  font-size: 0.8em;
}

.ajax-cart__price .was-price {
  margin-left: 3px;
}

.ajax-cart__line-items {
  margin-top: 0.25rem;
}

.ajax-cart__line-items-discount .ajax-cart__price-comparison {
  display: flex;
}
.ajax-cart__line-items-discount .ajax-cart__price-comparison p,
.ajax-cart__line-items-discount .ajax-cart__price-comparison s {
  padding: 5px 5px 5px 0;
}
.ajax-cart__line-items-discount .line-item-discount__container {
  padding: 0;
  margin: 0;
  background: none;
}

.ajax-cart__details-wrapper {
  padding: 1rem 1rem;
  border-top: 2px solid {{ border_color | color_modify: 'alpha', 0.5 }};
}
.ajax-cart__details-wrapper .ajax-cart__details-row {
  padding: 10px 0;
}
@media only screen and (max-width: 798px) {
  .ajax-cart__details-wrapper .ajax-cart__row-description {
    max-width: 50%;
  }
}

.ajax-cart__note {
  width: 100%;
  min-height: 80px;
  padding: 0.625em;
  margin-top: 20px;
  font-size: inherit;
}
@media only screen and (max-width: 480px) {
  .ajax-cart__note {
    font-size: 16px;
  }
}

.ajax-cart__tos-checkbox {
  margin-top: 20px;
}

.ajax-cart__cart-message {
  margin-top: 20px;
}

.ajax-cart__empty-cart-message .icon {
  width: 2.4rem;
  height: 2.4rem;
}

.ajax-cart__empty-text {
  margin: 10px 0;
}

.ajax-cart__button {
  width: 100%;
  margin-top: 20px;
}
.ajax-cart__button.button .icon, .ajax-cart__button.age-gate__confirm_btn .icon {
  width: 1em;
  height: 1em;
}
.ajax-cart__button.button .icon:first-child:last-child, .ajax-cart__button.age-gate__confirm_btn .icon:first-child:last-child {
  margin-right: 5px;
  margin-left: 0;
}

.ajax-cart__cart-link {
  display: block;
  margin: 1.25rem 0;
  text-decoration: underline;
}

.ajax-cart__delete .close,
.ajax-cart__delete:hover .close {
  color: {{ text }};
}

.show-mini-cart #theme-ajax-cart.is-mini-cart {
  visibility: visible;
  opacity: 1;
  transition: opacity 0.3s ease-in;
}

#theme-ajax-cart.is-mini-cart {
  position: absolute;
  top: 100%;
  right: 0;
  width: 420px;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease-in;
}
#theme-ajax-cart.is-mini-cart .ajax-cart__empty-cart-message {
  padding-bottom: 1rem;
}
#theme-ajax-cart.is-mini-cart.theme-ajax-cart--header-vertical {
  top: 60%;
  left: 0;
  max-height: 50vh;
  overflow-y: auto;
}
@media only screen and (max-width: 798px) {
  #theme-ajax-cart.is-mini-cart.theme-ajax-cart--header-vertical {
    top: 100%;
    right: 0;
    left: auto;
    max-height: 100vh;
  }
}
#theme-ajax-cart.is-mini-cart.theme-ajax-cart--header-vertical a {
  color: {{ link }};
}
#theme-ajax-cart.is-mini-cart.theme-ajax-cart--header-vertical a:hover {
  color: {{ settings.link_hover_color }};
}

.ajax-cart--mini-cart .ajax-cart__close-icon {
  display: none;
}

@media only screen and (max-width: 1024px) {
  .touchevents .ajax-cart--mini-cart .ajax-cart__close-icon {
    display: block;
  }
}

.ajax-cart--drawer {
  position: relative;
  z-index: 50;
}

.ajax-cart__overlay {
  position: fixed;
  top: 0;
  right: -100%;
  bottom: 0;
  z-index: 49;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  opacity: 0;
  transition: opacity 0.2s;
}
.ajax-cart__overlay.is-visible {
  right: 0;
  opacity: 1;
}
@media only screen and (max-width: 798px) {
  .ajax-cart__overlay {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

#theme-ajax-cart.is-drawer {
  position: fixed;
  top: 0;
  right: -40%;
  bottom: 0;
  width: 40%;
  height: 100%;
  transition: right 0.3s ease-in-out;
}
@media only screen and (max-width: 798px) {
  #theme-ajax-cart.is-drawer {
    right: -60%;
    width: 60%;
  }
}
@media only screen and (max-width: 480px) {
  #theme-ajax-cart.is-drawer {
    right: -100%;
    width: 100%;
  }
}
#theme-ajax-cart.is-drawer.is-visible {
  right: 0;
}
@media only screen and (min-width: 799px) {
  #theme-ajax-cart.is-drawer.theme-ajax-cart--header-vertical {
    right: auto;
    left: -30%;
    width: 30%;
    margin-left: 20%;
    transition: left 0.3s ease-in-out;
  }
  #theme-ajax-cart.is-drawer.theme-ajax-cart--header-vertical.is-visible {
    left: 0;
  }
}

/* # Breadcrumb
================================================== */
.breadcrumb {
  font-size: 1rem;
  white-space: nowrap;
}
.breadcrumb a {
  align-items: center;
  color: {{ link }};
  display: flex;
  justify-content: center;
  padding: 0;
  line-height: 1;
}
.breadcrumb a:hover {
  color: {{ settings.link_hover_color }};
}
.breadcrumb li {
  align-items: center;
  display: flex;
  line-height: 2;
  white-space: normal;
}
.breadcrumb li:not(.tag):first-child span {
  padding-left: 0;
}
.breadcrumb li.is-active a {
  color: {{ settings.link_hover_color }};
  cursor: default;
  pointer-events: none;
}
.breadcrumb ul,
.breadcrumb ol {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
@media only screen and (max-width: 480px) {
  .breadcrumb ul,
  .breadcrumb ol {
    justify-content: center;
  }
}
.breadcrumb .icon:first-child {
  margin-right: 0.5em;
}
.breadcrumb .icon:last-child {
  margin-left: 0.5em;
}
.breadcrumb.is-center ol,
.breadcrumb.is-center ul {
  justify-content: center;
}
.breadcrumb.is-right ol,
.breadcrumb.is-right ul {
  justify-content: flex-end;
}
.breadcrumb.is-small {
  font-size: 0.75rem;
}
.breadcrumb.is-small .icon {
  height: 0.75rem;
  width: 0.75rem;
}
.breadcrumb.is-regular {
  font-size: 1rem;
}
.breadcrumb.is-regular .icon {
  height: 1rem;
  width: 1rem;
}
.breadcrumb.is-large {
  font-size: 1.2rem;
}
.breadcrumb.is-large .icon {
  height: 1.2rem;
  width: 1.2rem;
}
.breadcrumb .page-navigation-arrows a {
  display: inline;
}

.page-navigation-arrows {
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 480px) {
  .page-navigation-arrows {
    justify-content: center;
    text-align: center;
    width: 100%;
    padding-top: 0;
  }
}
.page-navigation-arrows .page-navigation__divider {
  padding: 0 5px;
}

.breadcrumb-separator {
  display: flex;
  align-items: flex-end;
}

@media only screen and (max-width: 480px) {
  .breadcrumb__container {
    flex-direction: column-reverse;
    flex-wrap: wrap;
  }
}

/* # Card
================================================== */
.card {
  background-color: {{ color_background }};
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.1);
  color: {{ text }};
  max-width: 100%;
  position: relative;
}

.card-header {
  background-color: transparent;
  align-items: stretch;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  width: 100%;
}

.card-header-title {
  align-items: center;
  display: flex;
  flex-grow: 1;
  font-weight: 700;
  padding: 0.75rem;
}
.card-header-title.is-center {
  justify-content: center;
}

.card-header-icon {
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: center;
  padding: 0.75rem;
}

.card-image {
  display: block;
  position: relative;
}

.card-content {
  background-color: transparent;
  padding: 1.5rem;
}

.card-footer {
  background-color: transparent;
  border-top: 1px solid {{ border_color }};
  align-items: stretch;
  display: flex;
}

.card-footer-item {
  align-items: center;
  display: flex;
  flex-basis: 0;
  flex-grow: 1;
  flex-shrink: 0;
  justify-content: center;
  padding: 0.75rem;
}
.card-footer-item:not(:last-child) {
  border-right: 1px solid {{ border_color }};
}

.faceted-filter-swatch {
  --faceted-filter-swatch-size: 20px;
  flex-shrink: 0;
  width: var(--faceted-filter-swatch-size);
  height: var(--faceted-filter-swatch-size);
  cursor: pointer;
  border: 1px solid {{ settings.border_color }};
}
.faceted-filter-swatch.faceted-filter-swatch--rounded {
  overflow: hidden;
  border-radius: 50%;
}
.faceted-filter-group-display__list-item-input:focus ~ .faceted-filter-swatch {
  outline: 5px auto -webkit-focus-ring-color;
}

.faceted-filter-swatch__color,
.faceted-filter-swatch__image {
  width: 100%;
  height: 100%;
}

.faceted-filter-swatch__color {
  background-color: var(--faceted-filter-swatch-color, transparent);
}

.faceted-filter-swatch__image {
  object-fit: cover;
}

/* # Form
================================================== */
{% assign file_name_border_color = border_color %}
.input,
.textarea {
  background-color: hsl(0, 0%, 100%);
  border-color: {{ settings.border_color }};
  color: hsl(0, 0%, 21%);
  box-shadow: none;
  max-width: 100%;
  width: 100%;
}
.input::-moz-placeholder,
.textarea::-moz-placeholder {
  color: rgba(54, 54, 54, 0.5);
}
.input::-webkit-input-placeholder,
.textarea::-webkit-input-placeholder {
  color: rgba(54, 54, 54, 0.5);
}
.input:-moz-placeholder,
.textarea:-moz-placeholder {
  color: rgba(54, 54, 54, 0.5);
}
.input:-ms-input-placeholder,
.textarea:-ms-input-placeholder {
  color: rgba(54, 54, 54, 0.5);
}
.input:hover, .input.is-hovered,
.textarea:hover,
.textarea.is-hovered {
  border-color: hsl(0, 0%, 71%);
}
.input:focus, .input.is-focused, .input:active, .input.is-active,
.textarea:focus,
.textarea.is-focused,
.textarea:active,
.textarea.is-active {
  border-color: {{ link }};
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.input[disabled], fieldset[disabled] .input,
.textarea[disabled],
fieldset[disabled] .textarea {
  background-color: {{ color_background }};
  border-color: {{ color_background }};
  box-shadow: none;
  color: hsl(0, 0%, 48%);
}
.input[disabled]::-moz-placeholder, fieldset[disabled] .input::-moz-placeholder,
.textarea[disabled]::-moz-placeholder,
fieldset[disabled] .textarea::-moz-placeholder {
  color: rgba(122, 122, 122, 0.3);
}
.input[disabled]::-webkit-input-placeholder, fieldset[disabled] .input::-webkit-input-placeholder,
.textarea[disabled]::-webkit-input-placeholder,
fieldset[disabled] .textarea::-webkit-input-placeholder {
  color: rgba(122, 122, 122, 0.3);
}
.input[disabled]:-moz-placeholder, fieldset[disabled] .input:-moz-placeholder,
.textarea[disabled]:-moz-placeholder,
fieldset[disabled] .textarea:-moz-placeholder {
  color: rgba(122, 122, 122, 0.3);
}
.input[disabled]:-ms-input-placeholder, fieldset[disabled] .input:-ms-input-placeholder,
.textarea[disabled]:-ms-input-placeholder,
fieldset[disabled] .textarea:-ms-input-placeholder {
  color: rgba(122, 122, 122, 0.3);
}
.input[readonly],
.textarea[readonly] {
  box-shadow: none;
}
.input.is-small,
.textarea.is-small {
  border-radius: 2px;
  font-size: 0.75rem;
}
.input.is-medium,
.textarea.is-medium {
  font-size: 1.25rem;
}
.input.is-large,
.textarea.is-large {
  font-size: 1.25rem;
  height: 2em;
}
.input.is-fullwidth,
.textarea.is-fullwidth {
  display: block;
  width: 100%;
}
.input.is-inline,
.textarea.is-inline {
  display: inline;
  width: auto;
}

.input.is-rounded {
  border-radius: 290486px;
  padding-left: 1em;
  padding-right: 1em;
}
.input.is-static {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
  padding-left: 0;
  padding-right: 0;
}
.input.is-primary-btn-style {
  border-radius: {{ settings.button_primary_border_radius | append: 'px' }};
  padding-left: 1em;
  padding-right: 1em;
}
.input.is-secondary-btn-style {
  border-radius: {{ settings.button_secondary_border_radius | append: 'px' }};
  padding-left: 1em;
  padding-right: 1em;
}

.textarea {
  display: block;
  max-width: 100%;
  min-width: 100%;
  padding: 0.625em;
  resize: vertical;
}
.textarea:not([rows]) {
  max-height: 600px;
  min-height: 120px;
}
.textarea[rows] {
  height: initial;
}
.textarea.has-fixed-size {
  resize: none;
}
.textarea.is-rounded {
  border-radius: 5px;
}
.textarea.is-primary-btn-style {
  border-radius: {{ settings.button_primary_border_radius | at_most: 5 | append: 'px' }};
  padding-left: 1em;
  padding-right: 1em;
}
.textarea.is-secondary-btn-style {
  border-radius: {{ settings.button_secondary_border_radius | at_most: 5 | append: 'px' }};
  padding-left: 1em;
  padding-right: 1em;
}

.checkbox,
.radio {
  cursor: pointer;
  display: inline-block;
  line-height: 1.25;
  position: relative;
}
.checkbox input,
.radio input {
  cursor: pointer;
}
.checkbox:hover,
.radio:hover {
  color: hsl(0, 0%, 21%);
}
.checkbox[disabled], fieldset[disabled] .checkbox,
.radio[disabled],
fieldset[disabled] .radio {
  color: hsl(0, 0%, 48%);
  cursor: not-allowed;
}

.radio + .radio {
  margin-left: 0.5em;
}

.select, .age-gate__select-wrapper {
  position: relative;
  display: inline-block;
  width: min-content;
  max-width: 100%;
  vertical-align: top;
}
.select:not(.is-multiple), .age-gate__select-wrapper:not(.is-multiple) {
  height: 2.25em;
}
.select:not(.is-multiple):not(.is-loading):not(.is-arrowless)::after, .age-gate__select-wrapper:not(.is-multiple):not(.is-loading):not(.is-arrowless)::after {
  border-color: {{ link }};
  right: 1.125em;
  z-index: 4;
}
.select.is-rounded select, .is-rounded.age-gate__select-wrapper select {
  border-radius: 290486px;
  padding-left: 1em;
}
.select.is-primary-btn-style select, .is-primary-btn-style.age-gate__select-wrapper select {
  border-radius: {{ settings.button_primary_border_radius | append: 'px' }};
  padding-left: 1em;
}
.select.is-secondary-btn-style select, .is-secondary-btn-style.age-gate__select-wrapper select {
  border-radius: {{ settings.button_secondary_border_radius | append: 'px' }};
  padding-left: 1em;
}
.select.is-wide, .is-wide.age-gate__select-wrapper {
  width: 100%;
}
.select.is-wide select, .is-wide.age-gate__select-wrapper select {
  width: inherit;
}
.select select, .age-gate__select-wrapper select {
  background-color: hsl(0, 0%, 100%);
  border-color: {{ settings.border_color }};
  color: hsl(0, 0%, 21%);
  cursor: pointer;
  display: block;
  font-size: 1em;
  max-width: 100%;
  outline: none;
}
.select select::-moz-placeholder, .age-gate__select-wrapper select::-moz-placeholder {
  color: rgba(54, 54, 54, 0.5);
}
.select select::-webkit-input-placeholder, .age-gate__select-wrapper select::-webkit-input-placeholder {
  color: rgba(54, 54, 54, 0.5);
}
.select select:-moz-placeholder, .age-gate__select-wrapper select:-moz-placeholder {
  color: rgba(54, 54, 54, 0.5);
}
.select select:-ms-input-placeholder, .age-gate__select-wrapper select:-ms-input-placeholder {
  color: rgba(54, 54, 54, 0.5);
}
.select select:hover, .age-gate__select-wrapper select:hover, .select select.is-hovered, .age-gate__select-wrapper select.is-hovered {
  border-color: hsl(0, 0%, 71%);
}
.select select:focus, .age-gate__select-wrapper select:focus, .select select.is-focused, .age-gate__select-wrapper select.is-focused, .select select:active, .age-gate__select-wrapper select:active, .select select.is-active, .age-gate__select-wrapper select.is-active {
  border-color: {{ link }};
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.select select[disabled], .age-gate__select-wrapper select[disabled], fieldset[disabled] .select select, fieldset[disabled] .age-gate__select-wrapper select {
  background-color: {{ color_background }};
  border-color: {{ color_background }};
  box-shadow: none;
  color: hsl(0, 0%, 48%);
}
.select select[disabled]::-moz-placeholder, .age-gate__select-wrapper select[disabled]::-moz-placeholder, fieldset[disabled] .select select::-moz-placeholder, fieldset[disabled] .age-gate__select-wrapper select::-moz-placeholder {
  color: rgba(122, 122, 122, 0.3);
}
.select select[disabled]::-webkit-input-placeholder, .age-gate__select-wrapper select[disabled]::-webkit-input-placeholder, fieldset[disabled] .select select::-webkit-input-placeholder, fieldset[disabled] .age-gate__select-wrapper select::-webkit-input-placeholder {
  color: rgba(122, 122, 122, 0.3);
}
.select select[disabled]:-moz-placeholder, .age-gate__select-wrapper select[disabled]:-moz-placeholder, fieldset[disabled] .select select:-moz-placeholder, fieldset[disabled] .age-gate__select-wrapper select:-moz-placeholder {
  color: rgba(122, 122, 122, 0.3);
}
.select select[disabled]:-ms-input-placeholder, .age-gate__select-wrapper select[disabled]:-ms-input-placeholder, fieldset[disabled] .select select:-ms-input-placeholder, fieldset[disabled] .age-gate__select-wrapper select:-ms-input-placeholder {
  color: rgba(122, 122, 122, 0.3);
}
.select select::-ms-expand, .age-gate__select-wrapper select::-ms-expand {
  display: none;
}
.select select[disabled]:hover, .age-gate__select-wrapper select[disabled]:hover, fieldset[disabled] .select select:hover, fieldset[disabled] .age-gate__select-wrapper select:hover {
  border-color: {{ color_background }};
}
.select select:not([multiple]):not(.is-arrowless), .age-gate__select-wrapper select:not([multiple]):not(.is-arrowless) {
  padding-right: 2.5em;
}
.select select[multiple], .age-gate__select-wrapper select[multiple] {
  height: auto;
  padding: 0;
}
.select select[multiple] option, .age-gate__select-wrapper select[multiple] option {
  padding: 0.5em 1em;
}
.select:not(.is-multiple):not(.is-loading):hover::after, .age-gate__select-wrapper:not(.is-multiple):not(.is-loading):hover::after {
  border-color: hsl(0, 0%, 21%);
}
.select.is-small, .is-small.age-gate__select-wrapper {
  border-radius: 2px;
  font-size: 0.75rem;
}
.select.is-medium, .is-medium.age-gate__select-wrapper {
  font-size: 1.25rem;
}
.select.is-large, .is-large.age-gate__select-wrapper {
  font-size: 1.25rem;
}
.select.is-disabled::after, .is-disabled.age-gate__select-wrapper::after {
  border-color: hsl(0, 0%, 48%);
}
.select.is-fullwidth, .is-fullwidth.age-gate__select-wrapper {
  width: 100%;
}
.select.is-fullwidth select, .is-fullwidth.age-gate__select-wrapper select {
  width: 100%;
}
.select.is-loading::after, .is-loading.age-gate__select-wrapper::after {
  margin-top: 0;
  position: absolute;
  right: 0.625em;
  top: 0.625em;
  transform: none;
}
.select.is-loading.is-small:after, .is-loading.is-small.age-gate__select-wrapper:after {
  font-size: 0.75rem;
}
.select.is-loading.is-medium:after, .is-loading.is-medium.age-gate__select-wrapper:after {
  font-size: 1.25rem;
}
.select.is-loading.is-large:after, .is-loading.is-large.age-gate__select-wrapper:after {
  font-size: 1.5rem;
}

.file {
  align-items: stretch;
  display: flex;
  justify-content: flex-start;
  position: relative;
}
.file.is-small {
  font-size: 0.75rem;
}
.file.is-medium {
  font-size: 1.25rem;
}
.file.is-medium .file-icon .fa {
  font-size: 21px;
}
.file.is-large {
  font-size: 1.5rem;
}
.file.is-large .file-icon .fa {
  font-size: 28px;
}
.file.has-name .file-cta {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.file.has-name .file-name {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.file.has-name.is-empty .file-cta {
  border-radius: 4px;
}
.file.has-name.is-empty .file-name {
  display: none;
}
.file.is-boxed .file-label {
  flex-direction: column;
}
.file.is-boxed .file-cta {
  flex-direction: column;
  height: auto;
  padding: 1em 3em;
}
.file.is-boxed .file-name {
  border-width: 0 1px 1px;
}
.file.is-boxed .file-icon {
  height: 1.5em;
  width: 1.5em;
}
.file.is-boxed .file-icon .fa {
  font-size: 21px;
}
.file.is-boxed.is-small .file-icon .fa {
  font-size: 14px;
}
.file.is-boxed.is-medium .file-icon .fa {
  font-size: 28px;
}
.file.is-boxed.is-large .file-icon .fa {
  font-size: 35px;
}
.file.is-boxed.has-name .file-cta {
  border-radius: 4px 4px 0 0;
}
.file.is-boxed.has-name .file-name {
  border-radius: 0 0 4px 4px;
  border-width: 0 1px 1px;
}
.file.is-center {
  justify-content: center;
}
.file.is-fullwidth .file-label {
  width: 100%;
}
.file.is-fullwidth .file-name {
  flex-grow: 1;
  max-width: none;
}
.file.is-right {
  justify-content: flex-end;
}
.file.is-right .file-cta {
  border-radius: 0 4px 4px 0;
}
.file.is-right .file-name {
  border-radius: 4px 0 0 4px;
  border-width: 1px 0 1px 1px;
  order: -1;
}

.file-label {
  align-items: stretch;
  display: flex;
  cursor: pointer;
  justify-content: flex-start;
  overflow: hidden;
  position: relative;
}
.file-label:hover .file-cta {
  background-color: #eeeeee;
  color: hsl(0, 0%, 21%);
}
.file-label:hover .file-name {
  border-color: {{ file_name_border_color | color_darken: 2.5 }};
}
.file-label:active .file-cta {
  background-color: #e8e8e8;
  color: hsl(0, 0%, 21%);
}
.file-label:active .file-name {
  border-color: {{ file_name_border_color | color_darken: 5 }};
}

.file-input {
  height: 100%;
  left: 0;
  opacity: 0;
  outline: none;
  position: absolute;
  top: 0;
  width: 100%;
}

.file-cta,
.file-name {
  border-color: {{ border_color }};
  border-radius: 4px;
  font-size: 1em;
  padding-left: 1em;
  padding-right: 1em;
  white-space: nowrap;
}

.file-cta {
  background-color: hsl(0, 0%, 96%);
  color: hsl(0, 0%, 29%);
}

.file-name {
  border-color: {{ file_name_border_color }};
  border-style: solid;
  border-width: 1px 1px 1px 0;
  display: block;
  max-width: 16em;
  overflow: hidden;
  text-align: left;
  text-overflow: ellipsis;
}

.file-icon {
  align-items: center;
  display: flex;
  height: 1em;
  justify-content: center;
  margin-right: 0.5em;
  width: 1em;
}
.file-icon .fa {
  font-size: 14px;
}

.label,
.selector-wrapper label {
  color: hsl(0, 0%, 21%);
  display: block;
  font-size: 1rem;
  font-weight: 400;
}
.label:not(:last-child),
.selector-wrapper label:not(:last-child) {
  margin-bottom: 0.5em;
}
.label.is-small,
.selector-wrapper label.is-small {
  font-size: 0.75rem;
}
.label.is-medium,
.selector-wrapper label.is-medium {
  font-size: 1.25rem;
}
.label.is-large,
.selector-wrapper label.is-large {
  font-size: 1.5rem;
}

.help {
  display: block;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.field:not(:last-child) {
  margin-bottom: 0.75rem;
}
.field.has-addons {
  display: flex;
  justify-content: flex-start;
}
.field.has-addons .control:not(:last-child) {
  margin-right: -1px;
}
.field.has-addons .control:not(:first-child):not(:last-child) .button, .field.has-addons .control:not(:first-child):not(:last-child) .age-gate__confirm_btn,
.field.has-addons .control:not(:first-child):not(:last-child) .input,
.field.has-addons .control:not(:first-child):not(:last-child) .select select,
.field.has-addons .control:not(:first-child):not(:last-child) .age-gate__select-wrapper select {
  border-radius: 0;
}
.field.has-addons .control:first-child:not(:only-child) .button, .field.has-addons .control:first-child:not(:only-child) .age-gate__confirm_btn,
.field.has-addons .control:first-child:not(:only-child) .input,
.field.has-addons .control:first-child:not(:only-child) .select select,
.field.has-addons .control:first-child:not(:only-child) .age-gate__select-wrapper select {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.field.has-addons .control:last-child:not(:only-child) .button, .field.has-addons .control:last-child:not(:only-child) .age-gate__confirm_btn,
.field.has-addons .control:last-child:not(:only-child) .input,
.field.has-addons .control:last-child:not(:only-child) .select select,
.field.has-addons .control:last-child:not(:only-child) .age-gate__select-wrapper select {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.field.has-addons .control .button:not([disabled]):hover, .field.has-addons .control .age-gate__confirm_btn:not([disabled]):hover, .field.has-addons .control .button:not([disabled]).is-hovered, .field.has-addons .control .age-gate__confirm_btn:not([disabled]).is-hovered,
.field.has-addons .control .input:not([disabled]):hover,
.field.has-addons .control .input:not([disabled]).is-hovered,
.field.has-addons .control .select select:not([disabled]):hover,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]):hover,
.field.has-addons .control .select select:not([disabled]).is-hovered,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]).is-hovered {
  z-index: 2;
}
.field.has-addons .control .button:not([disabled]):focus, .field.has-addons .control .age-gate__confirm_btn:not([disabled]):focus, .field.has-addons .control .button:not([disabled]).is-focused, .field.has-addons .control .age-gate__confirm_btn:not([disabled]).is-focused, .field.has-addons .control .button:not([disabled]):active, .field.has-addons .control .age-gate__confirm_btn:not([disabled]):active, .field.has-addons .control .button:not([disabled]).is-active, .field.has-addons .control .age-gate__confirm_btn:not([disabled]).is-active,
.field.has-addons .control .input:not([disabled]):focus,
.field.has-addons .control .input:not([disabled]).is-focused,
.field.has-addons .control .input:not([disabled]):active,
.field.has-addons .control .input:not([disabled]).is-active,
.field.has-addons .control .select select:not([disabled]):focus,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]):focus,
.field.has-addons .control .select select:not([disabled]).is-focused,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]).is-focused,
.field.has-addons .control .select select:not([disabled]):active,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]):active,
.field.has-addons .control .select select:not([disabled]).is-active,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]).is-active {
  z-index: 3;
}
.field.has-addons .control .button:not([disabled]):focus:hover, .field.has-addons .control .age-gate__confirm_btn:not([disabled]):focus:hover, .field.has-addons .control .button:not([disabled]).is-focused:hover, .field.has-addons .control .age-gate__confirm_btn:not([disabled]).is-focused:hover, .field.has-addons .control .button:not([disabled]):active:hover, .field.has-addons .control .age-gate__confirm_btn:not([disabled]):active:hover, .field.has-addons .control .button:not([disabled]).is-active:hover, .field.has-addons .control .age-gate__confirm_btn:not([disabled]).is-active:hover,
.field.has-addons .control .input:not([disabled]):focus:hover,
.field.has-addons .control .input:not([disabled]).is-focused:hover,
.field.has-addons .control .input:not([disabled]):active:hover,
.field.has-addons .control .input:not([disabled]).is-active:hover,
.field.has-addons .control .select select:not([disabled]):focus:hover,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]):focus:hover,
.field.has-addons .control .select select:not([disabled]).is-focused:hover,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]).is-focused:hover,
.field.has-addons .control .select select:not([disabled]):active:hover,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]):active:hover,
.field.has-addons .control .select select:not([disabled]).is-active:hover,
.field.has-addons .control .age-gate__select-wrapper select:not([disabled]).is-active:hover {
  z-index: 4;
}
.field.has-addons .control.is-expanded {
  flex-grow: 1;
}
.field.has-addons.has-addons-center {
  justify-content: center;
}
.field.has-addons.has-addons-right {
  justify-content: flex-end;
}
.field.has-addons.has-addons-fullwidth .control {
  flex-grow: 1;
  flex-shrink: 0;
}
.field.is-grouped {
  display: flex;
  justify-content: flex-start;
}
.field.is-grouped > .control {
  flex-shrink: 0;
}
.field.is-grouped > .control:not(:last-child) {
  margin-bottom: 0;
  margin-right: 0.75rem;
}
.field.is-grouped > .control.is-expanded {
  flex-grow: 1;
  flex-shrink: 1;
}
.field.is-grouped.is-grouped-center {
  justify-content: center;
}
.field.is-grouped.is-grouped-right {
  justify-content: flex-end;
}
.field.is-grouped.is-grouped-multiline {
  flex-wrap: wrap;
}
.field.is-grouped.is-grouped-multiline > .control:last-child, .field.is-grouped.is-grouped-multiline > .control:not(:last-child) {
  margin-bottom: 0.75rem;
}
.field.is-grouped.is-grouped-multiline:last-child {
  margin-bottom: -0.75rem;
}
.field.is-grouped.is-grouped-multiline:not(:last-child) {
  margin-bottom: 0;
}
@media only screen and (min-width: 799px) {
  .field.is-horizontal {
    display: flex;
  }
}

.field-label .label {
  font-size: inherit;
}
@media only screen and (min-width: 481px) {
  .field-label {
    margin-bottom: 0.5rem;
  }
}
@media only screen and (min-width: 799px) {
  .field-label {
    flex-basis: 0;
    flex-grow: 1;
    flex-shrink: 0;
    margin-right: 1.5rem;
    text-align: right;
  }
  .field-label.is-small {
    font-size: 0.75rem;
    padding-top: 0.375em;
  }
  .field-label.is-normal {
    padding-top: 0.375em;
  }
  .field-label.is-medium {
    font-size: 1.25rem;
    padding-top: 0.375em;
  }
  .field-label.is-large {
    font-size: 1.5rem;
    padding-top: 0.375em;
  }
}

.field-body .field .field {
  margin-bottom: 0;
}
@media only screen and (min-width: 799px) {
  .field-body {
    display: flex;
    flex-basis: 0;
    flex-grow: 5;
    flex-shrink: 1;
  }
  .field-body .field {
    margin-bottom: 0;
  }
  .field-body > .field {
    flex-shrink: 1;
  }
  .field-body > .field:not(.is-narrow) {
    flex-grow: 1;
  }
  .field-body > .field:not(:last-child) {
    margin-right: 0.75rem;
  }
}

.control {
  box-sizing: border-box;
  clear: both;
  font-size: 1rem;
  position: relative;
  text-align: left;
}
.control.has-icons-left .input:focus ~ .icon,
.control.has-icons-left .select:focus ~ .icon,
.control.has-icons-left .age-gate__select-wrapper:focus ~ .icon, .control.has-icons-right .input:focus ~ .icon,
.control.has-icons-right .select:focus ~ .icon,
.control.has-icons-right .age-gate__select-wrapper:focus ~ .icon {
  color: hsl(0, 0%, 48%);
}
.control.has-icons-left .input.is-small ~ .icon,
.control.has-icons-left .select.is-small ~ .icon,
.control.has-icons-left .is-small.age-gate__select-wrapper ~ .icon, .control.has-icons-right .input.is-small ~ .icon,
.control.has-icons-right .select.is-small ~ .icon,
.control.has-icons-right .is-small.age-gate__select-wrapper ~ .icon {
  font-size: 0.5rem;
}
.control.has-icons-left .input.is-regular ~ .icon,
.control.has-icons-left .select.is-regular ~ .icon,
.control.has-icons-left .is-regular.age-gate__select-wrapper ~ .icon, .control.has-icons-right .input.is-regular ~ .icon,
.control.has-icons-right .select.is-regular ~ .icon,
.control.has-icons-right .is-regular.age-gate__select-wrapper ~ .icon {
  font-size: 0.75rem;
}
.control.has-icons-left .input.is-medium ~ .icon,
.control.has-icons-left .select.is-medium ~ .icon,
.control.has-icons-left .is-medium.age-gate__select-wrapper ~ .icon, .control.has-icons-right .input.is-medium ~ .icon,
.control.has-icons-right .select.is-medium ~ .icon,
.control.has-icons-right .is-medium.age-gate__select-wrapper ~ .icon {
  font-size: 1rem;
}
.control.has-icons-left .input.is-large ~ .icon,
.control.has-icons-left .select.is-large ~ .icon,
.control.has-icons-left .is-large.age-gate__select-wrapper ~ .icon, .control.has-icons-right .input.is-large ~ .icon,
.control.has-icons-right .select.is-large ~ .icon,
.control.has-icons-right .is-large.age-gate__select-wrapper ~ .icon {
  font-size: 1.25rem;
}
.control.has-icons-left .icon, .control.has-icons-right .icon {
  color: hsl(0, 0%, 86%);
  height: 1.5em;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 1.8em;
  vertical-align: middle;
  z-index: 4;
}
.control.has-icons-left .input,
.control.has-icons-left .select select,
.control.has-icons-left .age-gate__select-wrapper select {
  padding-left: 2.25em;
}
.control.has-icons-left .icon {
  left: 0;
  top: 50%;
  transform: translate(40%, -50%);
}
.control.has-icons-right .input,
.control.has-icons-right .select select,
.control.has-icons-right .age-gate__select-wrapper select {
  padding-right: 2.25em;
}
.control.has-icons-right .icon {
  right: 0;
}
.control.has-icons-right .icon.is-right {
  left: initial;
}
.control.is-loading::after {
  position: absolute !important;
  right: 0.625em;
  top: 0.625em;
  z-index: 4;
}
.control.is-loading.is-small:after {
  font-size: 0.75rem;
}
.control.is-loading.is-medium:after {
  font-size: 1.25rem;
}
.control.is-loading.is-large:after {
  font-size: 1.5rem;
}

.newsletter-section.has-background {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
@media only screen and (max-width: 798px) {
  .newsletter-section.newsletter-section--is-width-half {
    height: auto;
  }
}
.newsletter-section .dark-overlay-true {
  position: static;
}
@media only screen and (min-width: 799px) {
  .newsletter-section.has-full-width-crop {
    padding: 0;
  }
  .newsletter-section.has-full-width-crop .column {
    margin: 0;
    width: 100%;
  }
  .newsletter-section.has-full-width-crop .newsletter__wrapper {
    max-width: 100%;
  }
}
.newsletter-section.newsletter-section--popup {
  margin: 0;
  padding: 0;
  width: 100%;
  max-width: none;
}
.newsletter-section.newsletter-section--popup .newsletter__wrapper {
  max-width: 100%;
  padding: 2em 2em 0 2em;
}

.newsletter__image--mobile-wrapper {
  display: none;
}
.newsletter__image--mobile-wrapper.has-image-crop {
  padding-bottom: 100%;
}
.newsletter__image--mobile-wrapper.has-image-crop img {
  position: absolute;
}
@media only screen and (max-width: 798px) {
  .newsletter__image--mobile-wrapper {
    display: block;
    overflow: hidden;
    position: relative;
  }
}

.newsletter-container {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  align-self: normal;
  width: 100%;
  z-index: 3;
  position: relative;
}

.newsletter-form.is-responsive {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.newsletter-form .field {
  justify-content: center;
  flex: 1 0 100%;
  margin-bottom: 20px;
}
.newsletter-form .field.is-stretched-width {
  flex: 3 0 50%;
  min-width: 200px;
  max-width: 100%;
}
.newsletter-form .field.is-default-width {
  flex: 1 0 auto;
  min-width: 150px;
}
.newsletter-form .field.is-default-width .button, .newsletter-form .field.is-default-width .age-gate__confirm_btn {
  width: 100%;
}
.newsletter-form .field.is-full-width {
  flex-basis: 100%;
}
.newsletter-form .field label {
  text-align: left;
}
.newsletter-form .field .control {
  width: calc(100% - 12px);
}
@media only screen and (max-width: 798px) {
  .newsletter-form .field .control {
    width: 100%;
  }
}

.newsletter__image {
  position: relative;
}
.newsletter__image img {
  position: absolute;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
}
@media only screen and (max-width: 798px) {
  .newsletter__image img {
    position: static;
  }
}
.newsletter__image.image-crop--none img {
  object-fit: contain;
  object-position: center;
}
@media only screen and (max-width: 798px) {
  .newsletter__image {
    display: none;
  }
}

@media only screen and (min-width: 799px) {
  .is-active-image {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

.newsletter__wrapper {
  padding: 40px;
  max-width: 55%;
}
@media only screen and (max-width: 480px) {
  .newsletter__wrapper {
    max-width: 100%;
  }
}

.newsletter__text-wrapper {
  width: calc(100% - 12px);
}

.newsletter__subheading {
  margin-bottom: 20px;
}

.contact-form__form-errors .form__error {
  margin-bottom: 15px;
  background: hsl(48, 100%, 67%);
}
.contact-form__form-errors .form__error strong {
  font-weight: 400;
}

@media only screen and (max-width: 480px) {
  .button, .age-gate__confirm_btn,
  .input,
  .textarea,
  .select select,
  .age-gate__select-wrapper select {
    font-size: 16px !important;
  }
}

.recipient-disclosure {
  --recipient-disclosure-top-margin: 0;
  --recipient-disclosure-bottom-margin: 0;
  --recipient-form-field-gap: 0;
  --recipient-form-label-gap: 0;
  --recipient-form-label-margin: 6px;
  --recipient-form-checkbox-width: 12px;
  --recipient-form-checkbox-border-width: 1px;
  --recipient-form-checkbox-border-color: #000000;
  --recipient-form-checkbox-border-radius: 0;
  --recipient-form-checkbox-svg-color: inherit;
  --recipient-form-error-color: #cc3333;
  margin: var(--recipient-disclosure-top-margin) 0 var(--recipient-disclosure-bottom-margin);
}

.recipient-disclosure__summary {
  position: relative;
  list-style: none;
}
.recipient-disclosure__summary::-webkit-details-marker {
  display: none;
}

.recipient-disclosure__checkbox {
  position: absolute;
  -webkit-appearance: none;
          appearance: none;
  width: var(--recipient-form-checkbox-width);
  height: var(--recipient-form-checkbox-width);
  border: var(--recipient-form-checkbox-border-width) solid var(--recipient-form-checkbox-border-color);
  border-radius: var(--recipient-form-checkbox-border-radius);
}
.recipient-disclosure__checkbox:checked ~ svg {
  visibility: visible;
}

.recipient-disclosure__checkbox-label {
  display: flex;
  align-items: center;
}
.recipient-disclosure__checkbox-label svg {
  visibility: hidden;
  position: absolute;
  width: var(--recipient-form-checkbox-width);
  height: var(--recipient-form-checkbox-width);
  color: var(--recipient-form-checkbox-svg-color);
}

.recipient-disclosure__checkbox,
.recipient-disclosure__checkbox-label {
  cursor: pointer;
}

.recipient-disclosure__checkbox-label-text {
  flex: 1;
  margin-left: calc(var(--recipient-form-checkbox-width) + var(--recipient-form-label-margin));
}

.recipient-form {
  display: flex;
  flex-direction: column;
  gap: var(--recipient-form-field-gap);
  margin-top: 1rem;
}

.recipient-form__input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--recipient-form-label-gap);
}

.recipient-form__input {
  box-sizing: border-box;
}

.recipient-form__error-message {
  display: none;
}
.recipient-form--has-errors .recipient-form__error-message {
  display: block;
}

.recipient-form__max-characters-message {
  display: block;
  margin-top: 0.25rem;
}

.recipient-disclosure {
  --recipient-disclosure-top-margin: 0.75rem;
  --recipient-disclosure-bottom-margin: 0.5rem;
  --recipient-form-label-margin: 0.75rem;
  --recipient-form-field-gap: 1.25rem;
  --recipient-form-label-gap: 0.5rem;
  --recipient-form-checkbox-width: 14px;
  --recipient-form-checkbox-border-color: {{ settings.border_color }};
  --recipient-form-error-color: #ea1a1a;
  width: 100%;
}
.product_form--has-quantity-box-true .recipient-disclosure {
  --recipient-disclosure-top-margin: 1.25rem;
}
@media only screen and (max-width: 798px) {
  .product_form--has-quantity-box-true .recipient-disclosure {
    --recipient-disclosure-top-margin: 1rem;
    --recipient-disclosure-bottom-margin: 0.25rem;
  }
}
.smart-payment-button--false.product_form--dropdown.product_form--has-quantity-box-true .recipient-disclosure {
  --recipient-disclosure-top-margin: 0;
  --recipient-disclosure-bottom-margin: 0.75rem;
}
@media only screen and (max-width: 798px) {
  .smart-payment-button--false.product_form--dropdown.product_form--has-quantity-box-true .recipient-disclosure {
    --recipient-disclosure-bottom-margin: 0.5rem;
  }
}
.smart-payment-button--false.product_form--radio.product_form--has-quantity-box-true .recipient-disclosure {
  --recipient-disclosure-top-margin: 0.75rem;
}
@media only screen and (max-width: 798px) {
  .smart-payment-button--false.product_form--radio.product_form--has-quantity-box-true .recipient-disclosure {
    --recipient-disclosure-top-margin: 0.5rem;
  }
}
.product_form--dropdown.product_form--has-quantity-box-false .recipient-disclosure {
  --recipient-disclosure-top-margin: 0;
  --recipient-disclosure-bottom-margin: 0.75rem;
}
@media only screen and (max-width: 798px) {
  .product_form--dropdown.product_form--has-quantity-box-false .recipient-disclosure {
    --recipient-disclosure-bottom-margin: 0.25rem;
  }
}

.recipient-disclosure__summary {
  box-sizing: border-box;
}

.recipient-disclosure__checkbox-label svg {
  background: {{ settings.link_color }};
  stroke: {{ settings.shop_bg_color }};
  stroke-width: 2;
}

.recipient-disclosure__checkbox-label-text {
  padding: 0.25rem 0;
}
@media only screen and (max-width: 798px) {
  .recipient-disclosure__checkbox-label-text {
    padding: 0.75rem 0;
  }
}

.recipient-form {
  margin-top: 0.75rem;
}
@media only screen and (max-width: 798px) {
  .recipient-form {
    margin: 0.5rem 0;
  }
}

.recipient-form--has-errors .recipient-form__input--email {
  border-color: var(--recipient-form-error-color);
}

.recipient-form__error-message {
  color: var(--recipient-form-error-color);
}

/* #Message
================================================== */
.message {
  font-size: 1rem;
}
.message strong {
  color: currentColor;
}
.message a:not(.button):not(.age-gate__confirm_btn):not(.tag):not(.dropdown-item) {
  color: currentColor;
  text-decoration: underline;
}
.message.is-small {
  font-size: 0.75rem;
}
.message.is-medium {
  font-size: 1.25rem;
}
.message.is-large {
  font-size: 1.5rem;
}

.message-header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  line-height: 1.25;
  padding: 0.75em 1em;
  position: relative;
}
.message-header .delete {
  flex-grow: 0;
  flex-shrink: 0;
  margin-left: 0.75em;
}
.message-header + .message-body {
  border-width: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.message-body {
  border-radius: 4px;
  border-style: solid;
  border-width: 0 0 0 4px;
  padding: 1.25em 1.5em;
}
.message-body code,
.message-body pre {
  background-color: hsl(0, 0%, 100%);
}
.message-body pre code {
  background-color: transparent;
}

/* # Navbar
================================================== */
{% liquid
    assign dropdown_background_opacity = settings.dropdown_background_opacity | divided_by: 100.00
    assign dropdown_background_color = settings.dropdown_background | color_modify: 'alpha', dropdown_background_opacity
  %}
.navbar {
  min-height: 3.25rem;
  position: relative;
}
.navbar > .container {
  align-items: stretch;
  display: flex;
  min-height: 3.25rem;
  width: 100%;
}
.navbar.has-shadow {
  box-shadow: 0 2px 0 0 {{ color_background }};
}
.navbar.is-transparent {
  background-color: transparent;
}

.is-fixed-bottom,
.is-fixed-top {
  left: 0;
  position: fixed;
  right: 0;
  z-index: 70;
}

.is-fixed-bottom {
  bottom: 0;
}
.is-fixed-bottom.has-shadow {
  box-shadow: 0 -2px 0 0 {{ color_background }};
}

.is-fixed-top {
  top: 0;
}

html.has-fixed-bottom,
body.has-fixed-bottom {
  padding-bottom: var(--header-height);
}

.fixed-message__text {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
@media only screen and (max-width: 798px) {
  .fixed-message__text {
    width: 100%;
  }
}

@media only screen and (max-width: 798px) {
  .fixed-message__button {
    padding-bottom: 20px;
    width: 100%;
  }
}
.fixed-message__button .button, .fixed-message__button .age-gate__confirm_btn {
  min-height: 2.25em;
  height: auto;
}

.navbar-brand,
.navbar-tabs {
  align-items: stretch;
  display: flex;
  flex-shrink: 0;
  min-height: 3.25rem;
}

.navbar-tabs {
  -webkit-overflow-scrolling: touch;
  max-width: 100vw;
  overflow-x: auto;
  overflow-y: hidden;
}

.navbar-burger {
  color: inherit;
  cursor: pointer;
  display: block;
  height: 3.25rem;
  position: relative;
  width: 3.25rem;
  margin-left: auto;
}
.navbar-burger span {
  background-color: currentColor;
  display: block;
  height: 1px;
  left: calc(50% - 8px);
  position: absolute;
  transform-origin: center;
  transition-duration: 86ms;
  transition-property: background-color, opacity, transform;
  transition-timing-function: ease-out;
  width: 16px;
}
.navbar-burger span:nth-child(1) {
  top: calc(50% - 6px);
}
.navbar-burger span:nth-child(2) {
  top: calc(50% - 1px);
}
.navbar-burger span:nth-child(3) {
  top: calc(50% + 4px);
}
.navbar-burger:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
.navbar-burger.is-active span:nth-child(1) {
  transform: translateY(5px) rotate(45deg);
}
.navbar-burger.is-active span:nth-child(2) {
  opacity: 0;
}
.navbar-burger.is-active span:nth-child(3) {
  transform: translateY(-5px) rotate(-45deg);
}

.navbar-menu {
  display: none;
}

.navbar-item {
  display: block;
  line-height: 1.5;
  padding: 0;
  position: relative;
}

.navbar-link {
  display: block;
  line-height: 1.5;
  padding: 1rem 0.75rem;
  position: relative;
}

a.navbar-item,
.navbar-link {
  cursor: pointer;
}

.navbar-item {
  display: block;
  flex-grow: 0;
  flex-shrink: 0;
}
.navbar-item img {
  max-height: 1.75rem;
}
.navbar-item.is-expanded {
  flex-grow: 1;
  flex-shrink: 1;
}
.navbar-item.is-tab {
  border-bottom: 1px solid transparent;
  min-height: 3.25rem;
  padding-bottom: calc(0.5rem - 1px);
}
.navbar-item.is-tab:hover {
  border-bottom-color: {{ link }};
}
.navbar-item.is-tab.is-active {
  border-bottom-color: {{ link }};
  border-bottom-style: solid;
  border-bottom-width: 3px;
  padding-bottom: calc(0.5rem - 3px);
}

.navbar-content {
  flex-grow: 1;
  flex-shrink: 1;
}

.navbar-link:not(.is-arrowless) {
  padding-right: 2.5em;
}
.navbar-link:not(.is-arrowless)::after {
  right: 1em;
}

.navbar-dropdown {
  padding-bottom: 1rem;
  padding-top: 1rem;
}
.navbar-dropdown .navbar-link,
.navbar-dropdown .navbar-item {
  color: {{ drop_down_menu_active_color }};
  font-size: {{ settings.dropdown_font_size | append: 'px' }};
  text-transform: {{ settings.dropdown_font_style }};
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.navbar-dropdown .navbar-link:hover,
.navbar-dropdown .navbar-item:hover {
  color: {{ settings.dropdown_link_hover_color }};
}
.navbar-dropdown .navbar-link {
  padding-top: 0;
}

.navbar-divider {
  background-color: {{ color_background }};
  border: none;
  display: none;
  height: 2px;
  margin: 0.5rem 0;
}

@media only screen and (max-width: 798px) {
  .navbar > .container {
    display: block;
  }
  .navbar-brand .navbar-item,
  .navbar-tabs .navbar-item {
    align-items: center;
    display: flex;
  }
  .navbar-link::after {
    display: none;
  }
  .navbar-menu {
    background-color: hsl(0, 0%, 100%);
    box-shadow: 0 8px 16px rgba(10, 10, 10, 0.1);
    padding: 0.5rem 0;
  }
  .navbar.is-fixed-bottom-touch, .navbar.is-fixed-top-touch {
    left: 0;
    position: fixed;
    right: 0;
    z-index: 70;
  }
  .navbar.is-fixed-bottom-touch {
    bottom: 0;
  }
  .navbar.is-fixed-bottom-touch.has-shadow {
    box-shadow: 0 -2px 3px rgba(10, 10, 10, 0.1);
  }
  .navbar.is-fixed-top-touch {
    top: 0;
  }
  .navbar.is-fixed-top .navbar-menu, .navbar.is-fixed-top-touch .navbar-menu {
    -webkit-overflow-scrolling: touch;
    max-height: calc(100vh - 3.25rem);
    overflow: auto;
  }
  html.has-navbar-fixed-top-touch,
  body.has-navbar-fixed-top-touch {
    padding-top: 3.25rem;
  }
  html.has-navbar-fixed-bottom-touch,
  body.has-navbar-fixed-bottom-touch {
    padding-bottom: 3.25rem;
  }
}
@media only screen and (min-width: 799px) {
  .navbar,
  .navbar-menu,
  .navbar-start,
  .navbar-end {
    align-items: stretch;
    display: flex;
  }
  .navbar {
    min-height: 3.25rem;
  }
  .navbar.is-spaced {
    padding: 1rem 2rem;
  }
  .navbar.is-spaced .navbar-start,
  .navbar.is-spaced .navbar-end {
    align-items: center;
  }
  .navbar.is-spaced a.navbar-item,
  .navbar.is-spaced .navbar-link {
    border-radius: 4px;
  }
  .navbar.is-transparent a.navbar-item:hover, .navbar.is-transparent a.navbar-item.is-active,
  .navbar.is-transparent .navbar-link:hover,
  .navbar.is-transparent .navbar-link.is-active {
    background-color: transparent !important;
  }
  .navbar.is-transparent .navbar-item.has-dropdown.is-active .navbar-link, .navbar.is-transparent .navbar-item.has-dropdown.is-hoverable:hover .navbar-link {
    background-color: transparent !important;
  }
  .navbar-burger {
    display: none;
  }
  .navbar-item,
  .navbar-link {
    align-items: center;
    display: flex;
  }
  .dropdown-click--false .navbar-item.has-dropdown:hover .navbar-dropdown, .dropdown-click--false .navbar-item.has-dropdown.show-dropdown .navbar-dropdown, .dropdown-click--false .navbar-item.has-mega-menu:hover .navbar-dropdown, .dropdown-click--false .navbar-item.has-mega-menu.show-dropdown .navbar-dropdown {
    visibility: visible;
    opacity: 1;
    display: block;
  }
  .dropdown-click--false .navbar-item.has-dropdown:hover .navbar-dropdown::after, .dropdown-click--false .navbar-item.has-dropdown.show-dropdown .navbar-dropdown::after, .dropdown-click--false .navbar-item.has-mega-menu:hover .navbar-dropdown::after, .dropdown-click--false .navbar-item.has-mega-menu.show-dropdown .navbar-dropdown::after {
    transform: translateY(-60%) rotate(180deg);
  }
  .dropdown-click--true .navbar-item.has-dropdown.is-opened .navbar-dropdown, .dropdown-click--true .navbar-item.has-mega-menu.is-opened .navbar-dropdown {
    visibility: visible;
    opacity: 1;
    display: block;
  }
  .navbar-item {
    display: flex;
  }
  .navbar-item.has-dropdown a:after, .navbar-item.has-mega-menu a:after {
    font-size: {{ settings.nav_font_size | append: 'px' }};
  }
  .navbar-item.has-dropdown-up .navbar-link::after {
    transform: rotate(135deg) translate(0.25em, -0.25em);
  }
  .navbar-item.has-dropdown-up .navbar-dropdown {
    border-top: none;
    bottom: 100%;
    box-shadow: 0 -8px 8px rgba(10, 10, 10, 0.1);
    top: auto;
  }
  .navbar-item.is-active:hover .navbar-dropdown .navbar.is-spaced, .navbar-item.is-active:hover .navbar-dropdown.is-boxed, .navbar-item.is-hoverable:hover .navbar-dropdown .navbar.is-spaced, .navbar-item.is-hoverable:hover .navbar-dropdown.is-boxed {
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0);
  }
  .has-submenu input:checked ~ .navbar-submenu {
    display: block;
  }
  .has-submenu label .navbar-link::after {
    top: 37%;
  }
  .has-submenu input:checked + label .navbar-link::after {
    transform: translateY(-60%) rotate(180deg);
  }
  .dropdown-style-horizontal .navbar-item.is-active, .dropdown-style-horizontal .navbar-item.is-hoverable:hover .navbar-dropdown .section {
    display: flex;
    flex-wrap: wrap;
  }
  .navbar-menu {
    flex-grow: 1;
    flex-shrink: 0;
  }
  .navbar-start {
    justify-content: flex-start;
    margin-right: auto;
  }
  .navbar-end {
    justify-content: flex-end;
    margin-left: auto;
  }
  .navbar-dropdown {
    font-size: {{ settings.dropdown_font_size | append: 'px' }};
    letter-spacing: {{ settings.dropdown_letter_spacing | append: 'px' }};
    text-transform: {{ settings.dropdown_font_style }};
    background-color: {{ dropdown_background_color }};
    box-shadow: 0 8px 8px rgba(10, 10, 10, 0.1);
    display: none;
    left: 0;
    position: absolute;
    top: 100%;
    z-index: 20;
    opacity: 0;
    transition: visibility 0s, opacity 0.5s linear;
  }
  .navbar-dropdown.navbar-dropdown--below-parent {
    top: 90%;
  }
  .navbar-dropdown.is-right {
    left: auto;
    right: 0;
  }
  .navbar-dropdown.is-vertical {
    padding: 1rem 0.8em;
    max-width: 300px;
    min-width: 200px;
  }
  .navbar-dropdown.is-horizontal {
    padding: 1.2em 0;
    width: 100%;
  }
  .navbar-dropdown .navbar-link span {
    max-width: 80%;
  }
  .show-nested-dropdown .has-submenu .navbar-submenu {
    display: block;
  }
  .navbar-link::after {
    transition: transform 0.2s linear;
  }
  .dropdown-click--false .has-dropdown:hover > .header__link,
  .dropdown-click--false .has-dropdown:hover > .header__link > a,
  .dropdown-click--false .has-mega-menu:hover > .header__link,
  .dropdown-click--false .has-mega-menu:hover > .header__link > a,
  .has-dropdown.is-opened > .header__link,
  .has-dropdown.is-opened > .header__link > a,
  .has-mega-menu.is-opened > .header__link,
  .has-mega-menu.is-opened > .header__link > a {
    color: {{ settings.header_link_hover_color }};
  }
  .dropdown-click--false .has-dropdown:hover .header__link::after,
  .dropdown-click--false .has-mega-menu:hover .header__link::after,
  .has-dropdown.is-opened .header__link::after,
  .has-mega-menu.is-opened .header__link::after {
    transform: translateY(-60%) rotate(180deg);
  }
  .horizontal-dropdown__column .navbar-submenu {
    margin-bottom: 0;
  }
  .navbar-submenu {
    display: none;
    margin-left: 20px;
    margin-top: 0;
    margin-bottom: 10px;
    border-left: 2px solid {{ drop_down_menu_active_color | color_modify: 'alpha', 0.3 }};
  }
  .navbar-submenu li:first-child .navbar-item {
    padding-top: 0;
  }
  .navbar-submenu li:last-child .navbar-item {
    padding-bottom: 0;
  }
  .navbar-submenu .navbar-item,
  .navbar-submenu .navbar-link {
    white-space: normal;
    padding: 10px 15px;
  }
  .has-small-vertical-spacing .menu__heading {
    padding-bottom: 5px;
  }
  .has-small-vertical-spacing .navbar-item {
    padding-top: 0;
    padding-bottom: 5px;
  }
  .has-small-vertical-spacing .navbar-link {
    padding-bottom: 5px;
  }
  .has-small-vertical-spacing > .navbar-item:last-child {
    padding-bottom: 0;
  }
  .has-medium-vertical-spacing .menu__heading {
    padding-bottom: 10px;
  }
  .has-medium-vertical-spacing .navbar-item {
    padding-top: 0;
    padding-bottom: 10px;
  }
  .has-medium-vertical-spacing .navbar-link {
    padding-bottom: 10px;
  }
  .has-medium-vertical-spacing > .navbar-item:last-child {
    padding-bottom: 0;
  }
  .has-large-vertical-spacing .menu__heading {
    padding-bottom: 15px;
  }
  .has-large-vertical-spacing .navbar-item {
    padding-top: 0;
    padding-bottom: 15px;
  }
  .has-large-vertical-spacing .navbar-link {
    padding-bottom: 15px;
  }
  .has-large-vertical-spacing > .navbar-item:last-child {
    padding-bottom: 0;
  }
  .navbar-divider {
    display: block;
  }
  .navbar > .container .navbar-brand,
  .container > .navbar .navbar-brand {
    margin-left: -0.75rem;
  }
  .navbar > .container .navbar-menu,
  .container > .navbar .navbar-menu {
    margin-right: -0.75rem;
  }
  .navbar.is-fixed-bottom-desktop, .navbar.is-fixed-top-desktop {
    left: 0;
    position: fixed;
    right: 0;
    z-index: 70;
  }
  .navbar.is-fixed-bottom-desktop {
    bottom: 0;
  }
  .navbar.is-fixed-bottom-desktop.has-shadow {
    box-shadow: 0 -2px 3px rgba(10, 10, 10, 0.1);
  }
  .navbar.is-fixed-top-desktop {
    top: 0;
  }
  html.has-navbar-fixed-top-desktop,
  body.has-navbar-fixed-top-desktop {
    padding-top: 3.25rem;
  }
  html.has-navbar-fixed-bottom-desktop,
  body.has-navbar-fixed-bottom-desktop {
    padding-bottom: 3.25rem;
  }
  html.has-spaced-navbar-fixed-top,
  body.has-spaced-navbar-fixed-top {
    padding-top: 5.25rem;
  }
  html.has-spaced-navbar-fixed-bottom,
  body.has-spaced-navbar-fixed-bottom {
    padding-bottom: 5.25rem;
  }
  .navbar-item.has-dropdown:hover .navbar-link, .navbar-item.has-dropdown.is-active .navbar-link {
    background-color: transparent;
  }
}
.box-shadow-false .navbar-dropdown,
.box-shadow-false .mega-menu {
  box-shadow: none;
}

/* # Pagination
================================================== */
.paginate {
  display: inline-block;
}
@media only screen and (max-width: 798px) {
  .paginate {
    width: 100%;
    margin-bottom: 20px;
  }
}

.pagination {
  font-size: 1rem;
  margin: -0.25rem;
}
.pagination .pagination-previous {
  margin-right: 16px;
}
.pagination .pagination-next {
  margin-left: 16px;
}
.pagination.is-small {
  font-size: 0.75rem;
}
.pagination.is-medium {
  font-size: 1.25rem;
}
.pagination.is-large {
  font-size: 1.5rem;
}
.pagination.is-rounded .pagination-previous,
.pagination.is-rounded .pagination-next {
  padding-left: 1em;
  padding-right: 1em;
  border-radius: 290486px;
}
.pagination.is-rounded .pagination-link {
  border-radius: 290486px;
}

.pagination,
.pagination-list {
  align-items: center;
  display: flex;
  justify-content: center;
  text-align: center;
}

.pagination-previous,
.pagination-next,
.pagination-link,
.pagination-ellipsis {
  font-size: 1em;
  padding-left: 0.5em;
  padding-right: 0.5em;
  justify-content: center;
  margin: 0.25rem;
  text-align: center;
}

.pagination-previous,
.pagination-next,
.pagination-link {
  border-color: {{ settings.regular_color | color_modify: 'alpha', 0.2 }};
  color: {{ text }};
  min-width: 2.25em;
}
.pagination-previous:hover,
.pagination-next:hover,
.pagination-link:hover {
  border-color: {{ settings.link_hover_color }};
  color: {{ settings.link_hover_color }};
}
.pagination-previous:focus,
.pagination-next:focus,
.pagination-link:focus {
  border-color: {{ settings.link_hover_color }};
}
.pagination-previous:active,
.pagination-next:active,
.pagination-link:active {
  box-shadow: inset 0 1px 2px rgba(10, 10, 10, 0.2);
}
.pagination-previous[disabled],
.pagination-next[disabled],
.pagination-link[disabled] {
  background-color: hsl(0, 0%, 86%);
  border-color: hsl(0, 0%, 86%);
  box-shadow: none;
  color: hsl(0, 0%, 48%);
  opacity: 0.5;
}

.pagination-previous,
.pagination-next {
  padding-left: 0.75em;
  padding-right: 0.75em;
  white-space: nowrap;
}

.pagination-link.is-current {
  background-color: {{ link }};
  border-color: {{ link }};
  color: {{ link_invert }};
}

.pagination-ellipsis {
  color: hsl(0, 0%, 71%);
  pointer-events: none;
}

@media only screen and (max-width: 798px) {
  .pagination .pagination-previous,
  .pagination .pagination-next {
    width: calc(50% - 10px);
    margin-left: 5px;
    margin-right: 5px;
  }
  .pagination.paginate--both {
    flex-wrap: wrap;
  }
  .pagination.paginate--both .pagination-previous,
  .pagination.paginate--both .pagination-next {
    order: 2;
  }
}
@media only screen and (min-width: 799px) {
  .pagination-list {
    flex-grow: 1;
    flex-shrink: 1;
    justify-content: flex-start;
  }
  .pagination {
    justify-content: space-between;
  }
  .pagination.is-center .pagination-previous {
    order: 1;
  }
  .pagination.is-center .pagination-list {
    justify-content: center;
    order: 2;
  }
  .pagination.is-center .pagination-next {
    order: 3;
  }
  .pagination.is-right .pagination-previous {
    order: 1;
  }
  .pagination.is-right .pagination-next {
    order: 2;
  }
  .pagination.is-right .pagination-list {
    justify-content: flex-end;
    order: 3;
  }
}
.pagination-button__infinite-scroll:not(.is-loading) {
  display: none;
}

/* # Tabs
================================================== */
.tabs {
  -webkit-overflow-scrolling: touch;
  display: flex;
  align-items: stretch;
  justify-content: flex-start;
}
.tabs a {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5em 1em;
  margin-bottom: -1px;
  color: {{ text }};
  word-break: break-word;
  vertical-align: top;
  border-bottom-color: {{ border_color }};
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.tabs a:hover {
  color: hsl(0, 0%, 21%);
  border-bottom-color: hsl(0, 0%, 21%);
}
.tabs a:hover * {
  color: hsl(0, 0%, 21%);
}
.tabs li {
  display: flex;
  margin-top: 0.25em;
}
.tabs li.active a, .tabs li.is-active a {
  border-bottom-color: {{ link }};
  color: {{ link }};
}
.tabs li.active a *, .tabs li.is-active a * {
  color: hsl(0, 0%, 21%);
}
.tabs li a * {
  margin-bottom: 0;
  font-size: 1rem;
}
.tabs ul {
  align-items: center;
  border-bottom-color: {{ border_color }};
  border-bottom-style: solid;
  border-bottom-width: 1px;
  display: flex;
  flex-grow: 1;
  flex-shrink: 0;
  justify-content: flex-start;
  margin-left: 0;
}
.tabs ul.is-left {
  padding-right: 0.75em;
}
.tabs ul.is-center {
  flex: none;
  justify-content: center;
  padding-left: 0.75em;
  padding-right: 0.75em;
}
.tabs ul.is-right {
  justify-content: flex-end;
  padding-left: 0.75em;
}
.tabs .icon:first-child {
  margin-right: 0.5em;
}
.tabs .icon:last-child {
  margin-left: 0.5em;
}
.tabs.is-center ul {
  justify-content: center;
}
.tabs.is-right ul {
  justify-content: flex-end;
}
.tabs.is-small {
  font-size: 0.75rem;
}
.tabs.is-medium {
  font-size: 1.25rem;
}
.tabs.is-large {
  font-size: 1.5rem;
}

.tabs-content {
  padding-left: 0;
  margin-left: 0;
  list-style: none;
  width: 100%;
}
.tabs-content > li {
  display: none;
  width: 100%;
}
.tabs-content > li.is-active, .tabs-content > li.active {
  display: block;
}

ul.tabs {
  -webkit-overflow-scrolling: touch;
  display: flex;
  flex-grow: 1;
  flex-shrink: 0;
  align-items: stretch;
  justify-content: flex-start;
  padding-left: 0;
  margin-left: 0;
  border-bottom-color: {{ border_color }};
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
ul.tabs a {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5em 1em;
  margin-bottom: -1px;
  color: {{ text }};
  word-break: break-word;
  vertical-align: top;
  border-bottom-color: {{ border_color }};
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
ul.tabs a:hover {
  color: hsl(0, 0%, 21%);
  border-bottom-color: hsl(0, 0%, 21%);
}
ul.tabs li {
  display: flex;
  margin-top: 0.25em;
}
ul.tabs li.active a, ul.tabs li.is-active a {
  color: {{ link }};
  border-bottom-color: {{ link }};
}
ul.tabs li a.active,
ul.tabs li a.is-active {
  color: {{ link }};
  border-bottom-color: {{ link }};
}
ul.tabs li a.active *,
ul.tabs li a.is-active * {
  color: {{ link }};
}

ul.tabs-content {
  margin-left: 0;
}

{%- if settings.tab_style == 'regular' -%}

ul.tabs a {
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
  }

ul.tabs a:hover {
    background-color: {{ color_background }};
    border-bottom-color: {{ border_color }};
  }

ul.tabs li.is-active a, ul.tabs li.active a {
    background-color: hsl(0, 0%, 100%);
    border-color: {{ border_color }};
    border-bottom-color: transparent !important;
  }

ul.tabs li a.is-active,
  ul.tabs li a.active {
    background-color: hsl(0, 0%, 100%);
    border-color: {{ border_color }};
    border-bottom-color: transparent !important;
  }
{%- elsif settings.tab_style == 'underline' -%}
ul.tabs a {
    padding-left: 0;
  }
{%- endif -%}
ul.tabs.is-fullwidth li {
  flex-grow: 1;
  flex-shrink: 0;
}
ul.tabs.is-toggle a {
  border-color: {{ border_color }};
  border-style: solid;
  border-width: 1px;
  margin-bottom: 0;
  position: relative;
}
ul.tabs.is-toggle a:hover {
  background-color: {{ color_background }};
  border-color: {{ border_color | color_lighten: 50 }};
  z-index: 2;
}
ul.tabs.is-toggle li + li {
  margin-left: -1px;
}
ul.tabs.is-toggle li:first-child a {
  border-radius: 4px 0 0 4px;
}
ul.tabs.is-toggle li:last-child a {
  border-radius: 0 4px 4px 0;
}
ul.tabs.is-toggle li.is-active a {
  background-color: {{ link }};
  border-color: {{ link }};
  color: {{ link_invert }};
  z-index: 1;
}
ul.tabs.is-toggle ul {
  border-bottom: none;
}
ul.tabs.is-toggle.is-toggle-rounded li:first-child a {
  border-bottom-left-radius: 290486px;
  border-top-left-radius: 290486px;
  padding-left: 1.25em;
}
ul.tabs.is-toggle.is-toggle-rounded li:last-child a {
  border-bottom-right-radius: 290486px;
  border-top-right-radius: 290486px;
  padding-right: 1.25em;
}

.surface-pick-up-embed {
  --surface-pick-up-embed-theme-success-color: rgb(50, 205, 50);
  --surface-pick-up-embed-theme-error-color: rgb(179, 58, 58);
  --surface-pick-up-embed-theme-paragraph-font-size: 16px;
  --surface-pick-up-embed-theme-paragraph-smaller-font-size: calc(var(--surface-pick-up-embed-theme-paragraph-font-size) - 4px);
  --surface-pick-up-embed-theme-body-font-weight-bold: 600;
  --surface-pick-up-embed-theme-body-text-color: #808080;
  --surface-pick-up-embed-theme-link-text-decoration: underline;
  --surface-pick-up-embed-row-gap: 10px;
  --surface-pick-up-embed-column-gap: 10px;
  display: grid;
  grid-template-columns: min-content auto;
  row-gap: var(--surface-pick-up-embed-row-gap);
  column-gap: var(--surface-pick-up-embed-column-gap);
  justify-content: flex-start;
  text-align: left;
}

.surface-pick-up-embed__in-stock-icon,
.surface-pick-up-embed__out-of-stock-icon {
  grid-column-start: 1;
  grid-column-end: 2;
  margin-top: 3px;
}

.surface-pick-up-embed__in-stock-icon {
  fill: var(--surface-pick-up-embed-theme-success-color);
}

.surface-pick-up-embed__out-of-stock-icon {
  fill: var(--surface-pick-up-embed-theme-error-color);
}

.surface-pick-up-embed__location-info,
.surface-pick-up-embed__modal-btn {
  grid-column-start: 2;
  grid-column-end: 3;
}

.surface-pick-up-embed__location-info {
  grid-row-start: 1;
  grid-row-end: 2;
}

.surface-pick-up-embed__location-availability {
  margin-top: 0;
  margin-bottom: 0;
  font-family: inherit;
  font-size: var(--surface-pick-up-embed-theme-paragraph-font-size);
  font-weight: inherit;
  color: var(--surface-pick-up-embed-theme-body-text-color);
}
.surface-pick-up-embed__location-availability b {
  font-weight: var(--surface-pick-up-embed-theme-body-font-weight-bold);
}

.surface-pick-up-embed__location-pick-up-time {
  font-size: var(--surface-pick-up-embed-theme-paragraph-smaller-font-size);
  color: var(--surface-pick-up-embed-theme-body-text-color);
}

.surface-pick-up-embed__modal-btn {
  grid-row-start: 2;
  grid-row-end: 3;
  justify-self: start;
  padding: 0;
  font-size: var(--surface-pick-up-embed-theme-paragraph-smaller-font-size);
  color: var(--surface-pick-up-embed-theme-body-text-color);
  text-align: left;
  -webkit-text-decoration: var(--surface-pick-up-embed-theme-link-text-decoration);
          text-decoration: var(--surface-pick-up-embed-theme-link-text-decoration);
  cursor: pointer;
  background-color: initial;
  border: 0;
}

.surface-pick-up-items {
  padding: 0;
  margin: 0;
}

.surface-pick-up-item {
  --surface-pick-up-item-theme-success-color: rgb(50, 205, 50);
  --surface-pick-up-item-theme-error-color: rgb(179, 58, 58);
  --surface-pick-up-item-theme-paragraph-font-size: 16px;
  --surface-pick-up-item-theme-paragraph-smaller-font-size: calc(var(--surface-pick-up-item-theme-paragraph-font-size) - 4px);
  --surface-pick-up-item-theme-body-font-weight-bold: 600;
  --surface-pick-up-item-theme-body-text-color: #808080;
  --surface-pick-up-item-theme-border-color: #d9d9d9;
  --surface-pick-up-item-theme-link-text-decoration: underline;
  --surface-pick-up-item-row-gap: 10px;
  --surface-pick-up-item-column-gap: 5px;
  --surface-pick-up-item-gap: 28px;
  display: grid;
  grid-template-columns: repeat(2, auto) 1fr;
  row-gap: var(--surface-pick-up-item-row-gap);
  column-gap: var(--surface-pick-up-item-column-gap);
  justify-content: flex-start;
  padding-bottom: var(--surface-pick-up-item-gap);
  margin: var(--surface-pick-up-item-gap) 0 0;
  text-align: left;
  border-bottom: 1px solid var(--surface-pick-up-item-theme-border-color);
}
.surface-pick-up-item:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.surface-pick-up-item__header {
  display: flex;
  grid-column: span 3;
  align-items: flex-end;
}

.surface-pick-up-item__pick-up-location {
  margin-top: 0;
  margin-bottom: 0;
  font-family: inherit;
  font-size: var(--surface-pick-up-item-theme-paragraph-font-size);
  font-weight: var(--surface-pick-up-item-theme-body-font-weight-bold);
  color: var(--surface-pick-up-item-theme-body-text-color);
}

.surface-pick-up-item__pick-up-distance {
  padding-left: 2rem;
  margin: 0 0 0 auto;
}

.surface-pick-up-item__in-stock-icon,
.surface-pick-up-item__out-of-stock-icon {
  grid-row-start: 2;
  grid-row-end: 3;
  grid-column-start: 1;
  grid-column-end: 2;
  margin-top: 1px;
}

.surface-pick-up-item__in-stock-icon {
  fill: var(--surface-pick-up-item-theme-success-color);
}

.surface-pick-up-item__out-of-stock-icon {
  fill: var(--surface-pick-up-item-theme-error-color);
}

.surface-pick-up-item__availability {
  grid-row-start: 2;
  grid-row-end: 3;
  grid-column-start: 2;
  grid-column-end: 3;
  font-size: var(--surface-pick-up-item-theme-paragraph-smaller-font-size);
  color: var(--surface-pick-up-item-theme-body-text-color);
}

.surface-pick-up-item__address-info {
  grid-row-start: 3;
  grid-row-end: 4;
  grid-column-start: 1;
  grid-column-end: 3;
  font-size: var(--surface-pick-up-item-theme-paragraph-smaller-font-size);
  font-style: normal;
  line-height: 1.4;
  color: var(--surface-pick-up-item-theme-body-text-color);
}
.surface-pick-up-item__address-info p {
  margin: 0;
}
.surface-pick-up-item__address-info a,
.surface-pick-up-item__address-info a:visited {
  color: inherit;
  text-decoration: none;
}
.surface-pick-up-item__address-info a:focus,
.surface-pick-up-item__address-info a:active,
.surface-pick-up-item__address-info a:hover {
  color: inherit;
}

.surface-pick-up-item__confirm-address {
  margin-top: var(--surface-pick-up-item-row-gap);
}

.surface-pick-up-item__confirm-address-icon {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 5px;
}

.surface-pick-up {
  opacity: 1;
  transition: opacity 0.3s ease-in;
}
.surface-pick-up.surface-pick-up--loading {
  visibility: hidden;
  opacity: 0;
}

.surface-pick-up-embed {
  --surface-pick-up-embed-theme-success-color: #099E4D;
  --surface-pick-up-embed-theme-error-color: #DE3618;
  --surface-pick-up-embed-theme-paragraph-font-size: {{ settings.regular_font_size | append: 'px' }};
  --surface-pick-up-embed-theme-paragraph-smaller-font-size: 0.85em;
  --surface-pick-up-embed-theme-body-font-weight-bold: 600;
  --surface-pick-up-embed-theme-body-text-color: {{ text }};
  --surface-pick-up-embed-theme-link-text-decoration: underline;
  --surface-pick-up-embed-row-gap: 10px;
  --surface-pick-up-embed-column-gap: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
}

.surface-pick-up-item {
  --surface-pick-up-item-theme-success-color: #099E4D;
  --surface-pick-up-item-theme-error-color: #DE3618;
  --surface-pick-up-item-theme-paragraph-font-size: {{ settings.regular_font_size | append: 'px' }};
  --surface-pick-up-item-theme-paragraph-smaller-font-size: 0.85em;
  --surface-pick-up-item-theme-body-font-weight-bold: 600;
  --surface-pick-up-item-theme-body-text-color: {{ text }};
  --surface-pick-up-item-theme-border-color: {{ border_color | color_modify: 'alpha', 0.5 }};
  --surface-pick-up-item-theme-link-text-decoration: underline;
  --surface-pick-up-item-row-gap: 0.8em;
  --surface-pick-up-item-column-gap: 0.5em;
  --surface-pick-up-item-gap: 12px;
  word-break: break-all;
}
@media only screen and (min-width: 799px) {
  .surface-pick-up-item {
    --surface-pick-up-item-gap: 22px;
  }
}
.surface-pick-up-item:last-child {
  padding-bottom: calc(var(--surface-pick-up-item-gap) / 2);
}

.surface-pick-up__modal.fancybox-content {
  padding: 2em;
}
.surface-pick-up__modal.fancybox-content .fancybox-close-small svg {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

.surface-pick-up__modal-header {
  display: flex;
  flex-direction: column;
  padding-bottom: 14px;
  border-bottom: 1px solid {{ border_color | color_modify: 'alpha', 0.5 }};
}
@media only screen and (min-width: 799px) {
  .surface-pick-up__modal-header {
    padding-bottom: 28px;
  }
}

.surface-pick-up__modal-title {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | times: 0.8 | floor | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: bold;
  word-break: break-all;
}
@media only screen and (max-width: 798px) {
  .surface-pick-up__modal-title {
    font-size: {{ settings.heading_size | times: 0.8 | floor | times: 0.8 | floor | append: 'px' }};
  }
}
.surface-pick-up__modal-title > a,
.surface-pick-up__modal-title > a:link,
.surface-pick-up__modal-title > a:visited {
  color: {{ settings.heading_color }};
}
.surface-pick-up__modal-title > a:hover,
.surface-pick-up__modal-title > a:focus {
  color: {{ settings.link_hover_color }};
}

.surface-pick-up__modal-subtitle {
  font-size: {{ settings.regular_font_size | append: 'px' }};
  word-break: break-all;
}

.surface-pick-up-embed__in-stock-icon,
.surface-pick-up-embed__out-of-stock-icon,
.surface-pick-up-item__in-stock-icon,
.surface-pick-up-item__out-of-stock-icon {
  width: 0.85em;
  height: 0.85em;
  margin-top: 0.3em;
}

.surface-pick-up-embed__location-availability {
  text-transform: none;
  letter-spacing: initial;
}

.surface-pick-up-item__pick-up-location {
  font-size: {{ settings.regular_font_size | append: 'px' }};
  font-weight: bold;
  text-transform: none;
  letter-spacing: initial;
}

.surface-pick-up-item__address-info {
  grid-column: span 3;
  font-size: 0.85em;
  line-height: 1.5;
}

.surface-pick-up-item__in-stock-icon,
.surface-pick-up-item__out-of-stock-icon {
  margin-top: 0.2em;
}

.surface-pick-up-item__pick-up-distance {
  flex-shrink: 0;
}

.price-ui {
  /* display: flex; */
  flex-wrap: wrap;
  row-gap: 0.25rem;
  color: {{ text }};
}

.price-ui,
.price-ui-badge {
  opacity: 1;
  transition: opacity 250ms ease-in-out;
}

.price-ui--loading,
.price-ui-badge--loading {
  opacity: 0;
}

.price--sale {
  color: {{ settings.sale_color }};
}

.price + .compare-at-price {
  margin-left: 5px;
}

.price .money,
.compare-at-price .money {
  display: inline-block;
}

.compare-at-price {
  text-decoration: line-through;
}
.compare-at-price .money {
  text-decoration: line-through;
}

.sale.savings {
  width: 100%;
}

.unit-pricing {
  order: 1;
  width: 100%;
  font-size: 1rem;
  color: {{ text }};
}

shopify-payment-terms {
  display: none;
  margin-top: 17px;
  margin-bottom: 15px;
  font-size: 1rem;
}

#icon-star {
  color: {{ settings.review_star }};
}

.icon-star-background {
  transition: none;
  transform: scaleX(var(--rating-scale, 0));
}

.icon-star-reference {
  position: absolute;
  left: -9999px;
  width: 0;
  height: 0;
}

.rating {
  display: flex;
  align-items: center;
  margin: 0.5rem 0;
  font-size: 0.8rem;
}

.rating__star-wrapper {
  display: flex;
}

.rating__star {
  width: 19px;
  height: auto;
}

.rating__star-1 {
  --rating-scale: calc(var(--rating-value));
}

.rating__star-2 {
  --rating-scale: calc(var(--rating-value) - 1);
}

.rating__star-3 {
  --rating-scale: calc(var(--rating-value) - 2);
}

.rating__star-4 {
  --rating-scale: calc(var(--rating-value) - 3);
}

.rating__star-5 {
  --rating-scale: calc(var(--rating-value) - 4);
}

.rating__text {
  display: none;
}

.rating__count {
  margin: 0 0 0 5px;
}

@media (forced-colors: active) {
  .rating__star-wrapper {
    display: none;
  }
  .rating__text {
    display: block;
  }
}
.options-selection__input-select-wrapper {
  display: flex;
  flex-direction: column;
}

.options-selection__input-select-label {
  order: 0;
}

.options-selection__input-select-chevron {
  order: 1;
}

.options-selection__input-select {
  order: 2;
}

.options-selection__select-label {
  display: block;
  font-size: 1rem;
  font-weight: 400;
  color: hsl(0, 0%, 21%);
}
.options-selection__select-label:not(:last-child) {
  margin-bottom: 0.5em;
}
.options-selection__select-label.is-small {
  font-size: 0.75rem;
}
.options-selection__select-label.is-medium {
  font-size: 1.25rem;
}
.options-selection__select-label.is-large {
  font-size: 1.5rem;
}

.age-gate {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}
.age-gate::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--background-color, #ffffff);
  content: "";
}

.age-gate__header {
  margin-bottom: 32px;
}

.age-gate__logo-wrapper {
  position: relative;
  display: block;
  max-width: 100px;
  margin: 0 auto;
}
.age-gate__logo-wrapper + .age-gate__heading,
.age-gate__logo-wrapper + .age-gate__description {
  margin-top: 32px;
}

.age-gate__logo {
  width: auto;
  height: auto;
  max-width: 100%;
}

.age-gate__heading {
  margin-top: 0;
  margin-bottom: 0;
}
.age-gate__heading + .age-gate__description {
  margin-top: 12px;
}

.age-gate__description {
  margin-top: 0;
  margin-bottom: 0;
}

.age-gate__content {
  position: relative;
  z-index: 1;
  /* Extra small devices (phones, 768px and down) */
  max-width: 75%;
  /* Medium devices (landscape tablets, 768px and up) */
}
@media only screen and (min-width: 768px) {
  .age-gate__content {
    max-width: 50%;
  }
}

.age-gate__form {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.age-gate__select-wrapper {
  /* Extra small devices (phones, 768px and down) */
  grid-column: 1/4;
  /* Medium devices (landscape tablets, 768px and up) */
}
@media only screen and (min-width: 768px) {
  .age-gate__select-wrapper {
    grid-column: unset;
  }
}

.age-gate__select {
  margin-top: 0;
  margin-bottom: 0;
  /* Medium devices (landscape tablets, 768px and up) */
}
@media only screen and (min-width: 768px) {
  .age-gate__select {
    margin-top: inherit;
    margin-bottom: inherit;
  }
}

.age-gate__confirm_btn {
  grid-column: 1/4;
}

.age-gate__error {
  grid-column: 1/4;
  margin-top: 10px;
  color: var(--color-error, #ea555c);
}

.age-gate__select-wrapper {
  width: 100%;
}
.age-gate__select-wrapper .age-gate__select {
  width: 100%;
  max-width: none;
}

.age-gate__confirm_btn {
  font-size: {{ btn_primary_font_size }};
  border-radius: {{ settings.button_primary_border_radius | append: 'px' }};
  width: {{ btn_primary_width }};
  line-height: {{ btn_primary_line_height }};
  height: auto;
  max-width: 100%;
  white-space: normal;
  overflow-wrap: normal;
  font-size: 1.25rem;
}
.age-gate__confirm_btn, .age-gate__confirm_btn:link, .age-gate__confirm_btn:visited {
  color: {{ settings.button_primary_text_color }};
  background-color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
  border-color: {{ settings.button_primary_border_color }};
}
.age-gate__confirm_btn:hover, .age-gate__confirm_btn.is-hovered {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.age-gate__confirm_btn:focus, .age-gate__confirm_btn.is-focused {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.age-gate__confirm_btn:focus:not(:active), .age-gate__confirm_btn.is-focused:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.age-gate__confirm_btn:active, .age-gate__confirm_btn.is-active {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.age-gate__confirm_btn.is-inverted {
  color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
  background-color: {{ settings.button_primary_text_color }};
  border-color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
}
.age-gate__confirm_btn.is-small {
  font-size: 0.75rem;
}
.age-gate__confirm_btn.is-normal {
  font-size: 1rem;
}
.age-gate__confirm_btn.is-medium {
  font-size: 1.25rem;
}
.age-gate__confirm_btn.is-large {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}

/* # Product
================================================== */
.shopify-product-reviews-badge {
  height: 30px;
  display: block;
}

.reviews-visibility-false {
  display: none;
}

.spr-badge-container {
  cursor: pointer;
  display: inline-block;
}

.product-title {
  margin: 0.5rem 0 1rem 0;
}
.product-block--first .product-title {
  margin-top: 0;
}

@media only screen and (max-width: 798px) {
  .product__images {
    margin-bottom: 60px;
  }
}

@media only screen and (min-width: 799px) {
  .product-gallery .flickity-prev-next-button {
    opacity: 0;
    visibility: hidden;
  }
  .product-gallery.display-arrows--true .product-gallery__main:hover .flickity-prev-next-button {
    opacity: 1;
    visibility: visible;
  }
}

.product-gallery__main {
  width: 100%;
}

.product-gallery__main,
.product-gallery__thumbnails.is-slide-nav--true {
  opacity: 0;
  transition: ease-in-out 0.2s opacity;
}
.product-gallery__main.flickity-enabled, .product-gallery__main.vertical-slider-enabled,
.product-gallery__thumbnails.is-slide-nav--true.flickity-enabled,
.product-gallery__thumbnails.is-slide-nav--true.vertical-slider-enabled {
  opacity: 1;
}

.product-gallery__image {
  width: 100%;
  height: auto;
}
.product-gallery__image img {
  width: 100%;
  height: auto;
  display: block;
}

.product-gallery__link {
  display: block;
}

.zoom-container {
  width: 100%;
  display: block;
}

.product-gallery.has-height-set model-viewer {
  margin: 0 auto;
}
.product-gallery.has-height-set .product-gallery__main img,
.product-gallery.has-height-set .product-gallery__main .plyr--html5 video {
  width: 100%;
}
.product-gallery.has-height-set .product-gallery__main img {
  object-fit: contain;
}
.product-gallery.has-height-set .product-gallery__main .plyr--youtube .plyr__video-embed {
  height: 100%;
}

.is-slide-nav--true .product-gallery__thumbnail {
  position: relative;
  opacity: 0.3;
}
.is-slide-nav--true .product-gallery__thumbnail.is-nav-selected {
  opacity: 1;
}

.product-thumbnail {
  padding-top: 10px;
  line-height: 1.2;
}

.product-thumbnail__vendor,
.product-thumbnail__price {
  display: block;
}

.thumbnail__caption {
  padding: 0 3px;
}

.thumbnail-swatch {
  display: flex;
  padding: 3px 3px 0;
}
.thumbnail-swatch.is-justify-left {
  margin-left: -5px;
}
.thumbnail-swatch.is-justify-right {
  margin-right: -5px;
}
.thumbnail-swatch .swatch {
  display: flex;
  box-sizing: border-box;
  border: 1px solid transparent;
  padding: 2px;
  margin: 0 3px;
}
.thumbnail-swatch .swatch:hover {
  border: 1px solid #000;
}
.thumbnail-swatch .swatch__style--circle,
.thumbnail-swatch .swatch__style--circle span,
.thumbnail-swatch .swatch__style--circle img {
  border-radius: 50%;
}
.thumbnail-swatch span {
  border: 1px solid {{ settings.border_color }};
  min-height: 20px;
  min-width: 20px;
  display: inline-block;
  background-size: cover;
  background-position: center;
}
.thumbnail-swatch span img {
  max-width: 20px;
  max-height: 20px;
  display: block;
}
.thumbnail-swatch span .swatch__image--empty {
  display: none;
}

.product-gallery__thumbnails--bottom-slider {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
.product-gallery__thumbnails--bottom-slider .flickity-button:disabled {
  display: none;
}
.product-gallery__thumbnails--bottom-slider .product-gallery__thumbnail {
  margin-right: 20px;
  width: 20%;
  text-align: center;
}

.product-gallery__thumbnails--bottom-thumbnails {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
.product-gallery__thumbnails--bottom-thumbnails.is-slide-nav--false .product-gallery__thumbnail {
  margin-bottom: 20px;
  line-height: 0;
}

@media only screen and (min-width: 799px) {
  .product-gallery--right-thumbnails:not(.product-gallery--image-amount-1) {
    display: flex;
  }
  .product-gallery--right-thumbnails:not(.product-gallery--image-amount-1) .product-gallery__main {
    width: 85%;
    height: auto;
    align-self: flex-start;
  }
  .product-gallery--right-thumbnails:not(.product-gallery--image-amount-1) .product-gallery__thumbnails {
    width: 15%;
    margin-left: 20px;
  }
  .product-gallery--right-thumbnails:not(.product-gallery--image-amount-1) .product-gallery__thumbnail {
    width: 100%;
    margin-bottom: 20px;
  }
  .product-gallery--right-thumbnails:not(.product-gallery--image-amount-1) .product-gallery__thumbnail:last-child {
    margin-bottom: 0;
  }
}

@media only screen and (min-width: 799px) {
  .product-gallery--left-thumbnails:not(.product-gallery--image-amount-1) {
    display: flex;
  }
  .product-gallery--left-thumbnails:not(.product-gallery--image-amount-1) .product-gallery__main {
    width: 85%;
    height: auto;
    align-self: flex-start;
    order: 2;
  }
  .product-gallery--left-thumbnails:not(.product-gallery--image-amount-1) .product-gallery__thumbnails {
    width: 15%;
    margin-right: 20px;
    order: 1;
  }
  .product-gallery--left-thumbnails:not(.product-gallery--image-amount-1) .product-gallery__thumbnail {
    width: 100%;
    margin-bottom: 20px;
  }
  .product-gallery--left-thumbnails:not(.product-gallery--image-amount-1) .product-gallery__thumbnail:last-child {
    margin-bottom: 0;
  }
}

.product-gallery__thumbnails--right-thumbnails,
.product-gallery__thumbnails--left-thumbnails {
  padding-top: 20px;
  display: flex;
  justify-content: center;
}
@media only screen and (min-width: 799px) {
  .product-gallery__thumbnails--right-thumbnails,
  .product-gallery__thumbnails--left-thumbnails {
    display: block;
    width: 100%;
    margin: 0 10px 10px 10px;
    padding-top: 0;
    justify-content: flex-start;
    align-self: flex-start;
  }
  .product-gallery__thumbnails--right-thumbnails.is-slide-nav--true,
  .product-gallery__thumbnails--left-thumbnails.is-slide-nav--true {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }
  .product-gallery__thumbnails--right-thumbnails.is-slide-nav--true::-webkit-scrollbar,
  .product-gallery__thumbnails--left-thumbnails.is-slide-nav--true::-webkit-scrollbar { /* WebKit */
    width: 0;
    height: 0;
  }
  .product-gallery__thumbnails--right-thumbnails .product-gallery__thumbnail,
  .product-gallery__thumbnails--left-thumbnails .product-gallery__thumbnail {
    margin: 0;
    width: 100%;
    line-height: 0;
    cursor: pointer;
  }
  .product-gallery__thumbnails--right-thumbnails .product-gallery__thumbnail:last-child,
  .product-gallery__thumbnails--left-thumbnails .product-gallery__thumbnail:last-child {
    margin-bottom: 0;
  }
  .product-gallery__thumbnails--right-thumbnails .product-gallery__thumbnail:before,
  .product-gallery__thumbnails--left-thumbnails .product-gallery__thumbnail:before {
    line-height: 0;
    color: transparent;
    display: block;
    text-align: center;
    content: counter(carousel-cell);
  }
}

.product__collections-list,
.product__type-list,
.product__tags-list {
  padding-bottom: 5px;
}
.product__collections-list.tags,
.product__type-list.tags,
.product__tags-list.tags {
  margin-bottom: 0;
}
.product__collections-list.tags .tag,
.product__type-list.tags .tag,
.product__tags-list.tags .tag {
  margin-bottom: 1rem;
}
.product__collections-list .product__classification-title,
.product__type-list .product__classification-title,
.product__tags-list .product__classification-title {
  margin-right: 0.5rem;
  margin-bottom: 1rem;
}

.savings {
  display: block;
}

.sold-out {
  color: {{ settings.regular_color | color_modify: 'alpha', 0.3 }};
}

/* # Product - image scroll
================================================== */
.product-template--image-scroll .product-gallery__main {
  opacity: 1;
}
.product-template--image-scroll .product-gallery__main:after {
  content: "flickity";
  display: none;
}
@media only screen and (min-width: 799px) {
  .product-template--image-scroll .product-gallery__main:after {
    content: "";
  }
}
.product-template--image-scroll .product-gallery__image {
  margin-bottom: 20px;
  opacity: 1 !important;
}
@media only screen and (min-width: 799px) {
  .product-template--image-scroll .product-gallery__thumbnails {
    display: none;
  }
}

/* # Product - Gallery
================================================== */
.product-gallery .product-gallery__main .flickity-prev-next-button {
  transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 798px) {
  .product-gallery .product-gallery__main .flickity-prev-next-button {
    opacity: 1;
    top: calc(100% + 15px);
    padding: 0;
    z-index: 3;
    height: initial;
    width: initial;
    transform: none;
    opacity: 0.5;
    background: none;
  }
  .product-gallery .product-gallery__main .flickity-prev-next-button:hover {
    opacity: 1;
  }
  .product-gallery .product-gallery__main .flickity-prev-next-button .flickity-button-icon {
    height: 30px;
    width: 30px;
    fill: {{ settings.regular_color }};
  }
  .product-gallery .product-gallery__main .flickity-prev-next-button.next {
    right: 30%;
  }
  .product-gallery .product-gallery__main .flickity-prev-next-button.previous {
    left: 30%;
  }
}
.product-gallery .flickity-page-dots {
  display: none;
}

@media only screen and (max-width: 798px) {
  .product-gallery--no-thumbnails .flickity-page-dots {
    display: block;
  }
}

.product-gallery__nav .gallery-cell {
  width: calc(20% - 10px);
  margin: 0 5px;
}

@media only screen and (max-width: 798px) {
  .product-gallery__thumbnails {
    padding-top: 0px;
    margin-top: 60px;
  }
}

.product-gallery__main.slideshow-transition--fade .flickity-slider .product-gallery__image {
  opacity: 0 !important;
  transition: opacity 1s ease !important;
}
.product-gallery__main.slideshow-transition--fade .flickity-slider .product-gallery__image.is-selected {
  opacity: 1 !important;
}

/* # Product - blocks
================================================== */
.block__related-products {
  width: 100%;
}

.block__product-reviews {
  min-width: 50%;
}

.dynamic-blocks--has-limit .section {
  width: 100%;
}
.dynamic-blocks--has-limit .section.has-background {
  width: calc(100% - 20px);
}

.section.product-info__block,
.section.product-gallery__block {
  display: flex;
  align-items: flex-start;
}

.block__product-gallery .product-gallery__container {
  width: 100%;
}

.product_section .has-product-sticker .sticker-holder__content {
  font-size: 16px;
}

/* # Related products
================================================== */
.related-products .products-slider .gallery-cell {
  padding: 0 10px;
}
@media only screen and (max-width: 798px) {
  .related-products .products-slider .gallery-cell {
    width: calc(50% - 20px);
  }
}

.complementary-products {
  --slide-item-padding: 1rem;
  --slide-item-outer-gap: 1rem;
  --slide-item-inner-gap: 1rem;
  --slide-item-border-color: black;
  --slide-item-border-thickness: 1px;
  --slider-dot-gap: 0.5rem;
  --slider-dot-size: 0.5rem;
  --slider-dot-color: #cacaca;
  --slider-active-dot-color: #787878;
}

.complementary-products__slider [data-slide] {
  position: absolute;
  display: flex;
  flex-direction: column;
  margin: 0 1rem;
  gap: var(--slide-item-outer-gap);
  width: 100%;
  height: auto;
}
.complementary-products__slider .flickity-page-dots {
  list-style-type: none;
  display: flex;
  justify-content: center;
  gap: var(--slider-dot-gap);
}
.complementary-products__slider .flickity-page-dots .dot {
  margin: 0;
  border-radius: 50%;
  background: var(--slider-dot-color);
  height: var(--slider-dot-size);
  width: var(--slider-dot-size);
}
.complementary-products__slider .flickity-page-dots .dot.is-selected {
  background: var(--slider-active-dot-color);
}

.complementary-products__grid {
  display: flex;
  flex-direction: column;
  gap: var(--slide-item-outer-gap);
}

.complementary-product {
  display: flex;
  flex-direction: row;
  gap: var(--slide-item-inner-gap);
  padding: var(--slide-item-padding);
  border: var(--slide-item-border-thickness) solid var(--slide-item-border-color);
}

.complementary-product__name,
.complementary-product__price-text {
  margin: 0;
}

.complementary-product__price-text {
  display: inline-block;
}

.complementary-product__image-link {
  display: block;
  position: relative;
  line-height: 0;
}

.complementary-products {
  --slide-item-padding: 0;
  --slide-item-outer-gap: 1.25rem;
  --slide-item-inner-gap: 1rem;
  --slide-item-border-thickness: 0;
  --slider-dot-size: 0.625rem;
  --slider-dot-gap: 1rem;
}

.complementary-products__container.complementary-products__container--grid {
  margin: 2.6667rem 0 1.5625rem;
}
.complementary-products__container.complementary-products__container--slider {
  margin: 2.6667rem 0 3.125rem;
}
.product-block--first .complementary-products__container {
  margin-top: 0;
}

.complementary-products__slider .flickity-button {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}
.complementary-products__slider .flickity-button.previous {
  left: 0.25rem;
}
.complementary-products__slider .flickity-button.next {
  right: 0.25rem;
}
@media only screen and (max-width: 1024px) {
  .complementary-products__slider .flickity-button {
    display: none;
  }
}
.complementary-products__slider:hover .flickity-button {
  opacity: 1;
  visibility: visible;
}
.complementary-products__slider .flickity-page-dots .dot,
.complementary-products__slider .flickity-page-dots .dot.is-selected {
  background: var(--color-body-text);
}

.complementary-products__title {
  font-family: {{ settings.regular__font.family }}, {{ settings.regular__font.fallback_families }};
  font-size: {{ settings.regular_font_size | append: 'px' }};
  font-weight: bold;
  margin: 2.6667rem 0 1rem;
}
.product-block--first .complementary-products__title {
  margin-top: 0;
}
.complementary-products__title + .complementary-products__container {
  margin-top: 0;
}

.complementary-product__name {
  font-family: {{ settings.regular__font.family }}, {{ settings.regular__font.fallback_families }};
  font-size: {{ settings.regular_font_size | append: 'px' }};
}
.complementary-product__name > .complementary-product__name-link {
  color: {{ link }};
}
.complementary-product__name > .complementary-product__name-link:hover {
  color: {{ settings.link_hover_color }};
}

.complementary-product__price-text {
  font-size: smaller;
  font-style: italic;
}
.complementary-product__price-text.complementary-product__price-text--now {
  display: none;
}
.complementary-product--on-sale .complementary-product__price-text.complementary-product__price-text--from {
  color: {{ settings.sale_color }};
}

.complementary-product--on-sale .complementary-product__price {
  color: {{ settings.sale_color }};
}

.complementary-product__unit-price {
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

.complementary-product__price-wrapper {
  padding-top: 3px;
  margin-bottom: 1rem;
}

.complementary-product__price-compare {
  opacity: 0.6;
  text-decoration: line-through;
  color: {{ settings.was_price_color }};
}

.complementary-product__image {
  flex: 0 0 25%;
}
@media only screen and (min-width: 481px) {
  .complementary-product__image {
    flex-basis: 20%;
  }
}

.complementary-product__details {
  flex-grow: 1;
  line-height: 1.2;
}

/* # Product form
================================================== */
{% liquid
    if settings.button_cart_padding == 'small'
      assign atc_button_height =  '35px'
      assign atc_button_width =  'calc(50% - 12px)'
      assign atc_button_margin =  '0 6px 0 0'
      assign atc_button_spb_margin =  '0 0 0 6px'
    elsif settings.button_cart_padding == 'regular'
      assign atc_button_height =  '44px'
      assign atc_button_width =  'calc(50% - 12px)'
      assign atc_button_margin =  '0 6px 0 0'
      assign atc_button_spb_margin =  '0 0 0 6px'
    else
      assign atc_button_height =  '60px'
      assign atc_button_width =  '100%'
      assign atc_button_margin =  '0 0 0 0'
      assign atc_button_spb_margin =  '12px 0 0 0'
    endif
  %}
.product__notify-form {
  margin-bottom: 1.5rem;
  max-width: 350px;
}

.notify-form__success-message {
  margin: 0;
}

.selector-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}
@media screen and (max-width:480px) {
  .selector-wrapper {
    margin-bottom: 20px;
  }
}
.selector-wrapper:last-of-type {
  margin-bottom: 1.5rem;
}
.product_form--dropdown.product_form--has-quantity-box-false .selector-wrapper:last-of-type {
  margin-bottom: 1rem;
}
.smart-payment-button--false.product_form--dropdown.product_form--has-quantity-box-true .selector-wrapper:last-of-type {
  margin-bottom: 1.25rem;
}
.selector-wrapper label {
  margin-bottom: 0.5rem;
}

.product_form--swatches .select-container {
  display: none;
}

.items_left {
  margin-bottom: 1.5rem;
}

.quantity-wrapper .quantity-element {
  min-height: {{ atc_button_height }};
}
.quantity-wrapper .quantity-input {
  text-align: center;
  width: 100%;
}
.quantity-wrapper .quantity-plus .icon,
.quantity-wrapper .quantity-minus .icon {
  padding: 0.3em;
}

.cart-warning {
  display: block;
  width: 100%;
  margin-top: 20px;
}

.cart-warning__message {
  display: inline-block;
}

.add_to_cart.action_button {
  margin-bottom: 0;
}

.shopify-payment-button {
  position: relative;
}

.shopify-payment-button button {
  padding-top: 10px;
  padding-bottom: 10px;
  margin-bottom: 0;
}

.shopify-payment-button__button.shopify-payment-button__button--hidden,
.shopify-payment-button__more-options.shopify-payment-button__button--hidden {
  display: none;
}

{%- if settings.button_style == 'rounded' -%}

.shopify-payment-button__button {
    border-radius: 3px;
  }
{%- elsif settings.button_style == 'pill_shaped' -%}
.shopify-payment-button__button {
    border-radius: 100px;
  }
{%- else -%}
.shopify-payment-button__button {
    border-radius: 0;
  }
{%- endif -%}

.shopify-payment-button__button--branded,
.shopify-payment-button__button--unbranded {
  min-height: {{ atc_button_height }};
  overflow: hidden;
}

.shopify-payment-button__more-options {
  max-width: 80%;
  padding: 16px 0 28px;
  margin: 0 auto;
  font-family: {{ settings.regular__font.family }}, {{ settings.regular__font.fallback_families }};
  font-size: 0.8rem;
  font-style: {{ settings.regular__font.style }};
  font-weight: normal;
  color: {{ settings.regular_color }};
  text-transform: none;
  letter-spacing: 0;
  box-shadow: none;
}
.shopify-payment-button__more-options:hover {
  background-color: transparent;
  border: none;
}

.purchase-details {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.product_section .smart-payment-button--true.product_form {
  max-width: 100%;
}

.product-quantity-box.purchase-details__quantity {
  margin-top: 12px;
}

@media only screen and (max-width: 798px) {
  .smart-payment-button--true .purchase-details {
    padding-bottom: 10px;
  }
}
@media only screen and (max-width: 798px) {
  .smart-payment-button--true .product-quantity-box.purchase-details__quantity {
    width: 100%;
    margin-right: 0;
  }
  .smart-payment-button--true .product-quantity-box.purchase-details__quantity .input.quantity {
    width: calc(100% - 88px);
  }
}

.smart-payment-button--false .purchase-details {
  align-items: flex-start;
}

.quantity-input.input {
  -moz-appearance: textfield;
  box-shadow: none;
  border: 0;
}
.quantity-input.input::-webkit-inner-spin-button, .quantity-input.input::-webkit-outer-spin-button {
  -webkit-appearance: none;
          appearance: none;
}

.quantity-plus.button, .quantity-plus.age-gate__confirm_btn,
.quantity-minus.button,
.quantity-minus.age-gate__confirm_btn {
  background-color: {{ settings.qty_background_color }};
}
.quantity-plus.button:hover, .quantity-plus.age-gate__confirm_btn:hover,
.quantity-minus.button:hover,
.quantity-minus.age-gate__confirm_btn:hover {
  background-color: {{ settings.qty_background_hover_color }};
}
.quantity-plus.button:hover .icon, .quantity-plus.age-gate__confirm_btn:hover .icon,
.quantity-minus.button:hover .icon,
.quantity-minus.age-gate__confirm_btn:hover .icon {
  fill: {{ settings.qty_icon_hover_color }};
}
.quantity-plus.button .icon, .quantity-plus.age-gate__confirm_btn .icon,
.quantity-minus.button .icon,
.quantity-minus.age-gate__confirm_btn .icon {
  fill: {{ settings.qty_icon_color }};
}

.quantity-plus.button[disabled], .quantity-plus[disabled].age-gate__confirm_btn,
.quantity-minus.button[disabled],
.quantity-minus[disabled].age-gate__confirm_btn {
  opacity: 1;
  background-color: {{ settings.qty_background_color | color_modify: 'alpha', 0.5 }};
}

.quantity-wrapper.field.has-addons .control:not(:last-child) {
  margin-right: 0;
}

.quantity-wrapper.quantity-style--box {
  border-radius: {{ settings.qty_border_radius | append: 'px' }};
}
.quantity-wrapper.quantity-style--box .quantity-minus {
  width: 44px;
  border-top-left-radius: {{ settings.qty_border_radius | append: 'px' }};
  border-bottom-left-radius: {{ settings.qty_border_radius | append: 'px' }};
}
.quantity-wrapper.quantity-style--box .minus-control {
  border-top-left-radius: {{ settings.qty_border_radius | append: 'px' }};
  border-bottom-left-radius: {{ settings.qty_border_radius | append: 'px' }};
}
.quantity-wrapper.quantity-style--box .quantity-plus {
  width: 44px;
  border-top-right-radius: {{ settings.qty_border_radius | append: 'px' }};
  border-bottom-right-radius: {{ settings.qty_border_radius | append: 'px' }};
}
.quantity-wrapper.quantity-style--box .plus-control {
  border-top-right-radius: {{ settings.qty_border_radius | append: 'px' }};
  border-bottom-right-radius: {{ settings.qty_border_radius | append: 'px' }};
}
.quantity-wrapper.quantity-style--box .quantity-input-control {
  width: 100%;
}
.quantity-wrapper.quantity-style--box .quantity-input-control input {
  border-top: thin solid {{ settings.border_color }};
  border-bottom: thin solid {{ settings.border_color }};
}
.quantity-wrapper.quantity-style--box .quantity-input-control--fill {
  width: 100%;
}
.quantity-wrapper.quantity-style--box .quantity-input {
  width: 100%;
}
.quantity-wrapper.quantity-style--box.is-medium .quantity-element {
  min-height: auto;
  min-width: 38px;
  height: 38px;
}
.quantity-wrapper.quantity-style--box.is-medium .quantity-plus,
.quantity-wrapper.quantity-style--box.is-medium .quantity-minus {
  width: 38px;
}

.quantity-wrapper.quantity-style--stacked {
  position: relative;
  border-radius: {{ settings.qty_border_radius | append: 'px' }} !important;
}
.quantity-wrapper.quantity-style--stacked .quantity-input-control {
  width: calc(100% - 48px);
}
.quantity-wrapper.quantity-style--stacked .minus-control {
  position: absolute;
  height: calc({{ atc_button_height }} / 2);
  bottom: 0;
  right: 0;
  z-index: 3;
  margin-right: 0;
  width: 48px;
}
.quantity-wrapper.quantity-style--stacked .plus-control {
  position: absolute;
  min-height: calc({{ atc_button_height }} / 2);
  top: 0;
  right: 0;
  z-index: 3;
  width: 48px;
}
.quantity-wrapper.quantity-style--stacked .quantity-input {
  border-left: thin solid {{ settings.border_color }};
  border-top: thin solid {{ settings.border_color }};
  border-bottom: thin solid {{ settings.border_color }};
  border-top-left-radius: {{ settings.qty_border_radius | append: 'px' }} !important;
  border-bottom-left-radius: {{ settings.qty_border_radius | append: 'px' }} !important;
  text-align: center;
  width: 100%;
}
.quantity-wrapper.quantity-style--stacked .quantity-minus {
  height: calc({{ atc_button_height }} / 2);
  min-height: calc({{ atc_button_height }} / 2);
  width: 48px;
  border-radius: 0;
  border-left: 0;
  border-bottom: thin solid {{ settings.border_color }};
  border-right: thin solid {{ settings.border_color }};
  border-bottom-right-radius: {{ settings.qty_border_radius | append: 'px' }} !important;
  margin-right: 1px;
  z-index: 4;
  opacity: 1 !important;
}
.quantity-wrapper.quantity-style--stacked .quantity-plus {
  height: calc({{ atc_button_height }} / 2);
  min-height: calc({{ atc_button_height }} / 2);
  width: 48px;
  border: 0;
  border-left: 0;
  border-top: thin solid {{ settings.border_color }};
  border-right: thin solid {{ settings.border_color }};
  border-top-right-radius: {{ settings.qty_border_radius | append: 'px' }};
  border-bottom-right-radius: 0;
  z-index: 4;
}

.product-quantity-box.purchase-details__quantity {
  margin-right: 10px;
  width: calc(50% - 12px);
}
@media only screen and (max-width: 480px) {
  .product-quantity-box.purchase-details__quantity {
    width: 100%;
    margin-right: 0;
  }
}
.product-quantity-box.purchase-details__quantity input.quantity {
  padding-top: 11px;
  padding-bottom: 11px;
  line-height: 1.4;
  margin-bottom: 0;
  width: calc(100% - 88px);
  min-height: {{ atc_button_height }};
}

@media only screen and (max-width: 798px) {
  .smart-payment-button--true .product-quantity-box.purchase-details__quantity {
    width: 100%;
    margin-right: 0;
  }
  .smart-payment-button--true .product-quantity-box.purchase-details__quantity .input.quantity {
    width: calc(100% - 88px);
  }
}

.purchase-details__buttons {
  display: flex;
  flex: 1 0 calc(50% - 12px);
  align-items: flex-end;
  flex-wrap: wrap;
  margin-left: 6px;
}
@media only screen and (max-width: 798px) {
  .purchase-details__buttons {
    margin-top: 20px;
    margin-left: 12px;
    flex: 1 0 calc(50% - 12px);
  }
}
@media only screen and (max-width: 480px) {
  .purchase-details__buttons {
    margin-top: 12px;
    margin-left: 0;
    flex: 1 0 100%;
  }
}
.purchase-details__buttons .button, .purchase-details__buttons .age-gate__confirm_btn {
  width: 100%;
  height: auto;
  align-items: center;
  min-height: {{ atc_button_height }};
}
{%- if settings.button_cart_padding == 'large' -%}
.purchase-details__buttons .button, .purchase-details__buttons .age-gate__confirm_btn {
    width: 100%;
  }
{%- endif -%}
.purchase-details__buttons .shopify-payment-button {
  flex: 1 0 100%;
  margin-bottom: 0;
  max-width: 100%;
}
@media only screen and (min-width: 1401px) {
  .purchase-details__buttons .shopify-payment-button {
    flex: 1 0 calc(50% - 12px);
    max-width: calc(50% - 12px);
  }
}
.purchase-details__buttons .action_button.action_button--secondary {
  background-color: transparent;
  border: 1px solid {{ border_color }};
  color: {{ text }};
  box-shadow: none;
  line-height: 1.2;
  padding: 10px 0;
  flex: 1 0 100%;
  margin: 0;
  max-width: 100%;
}
@media only screen and (min-width: 1401px) {
  .purchase-details__buttons .action_button.action_button--secondary {
    flex: 1 0 calc(50% - 12px);
    max-width: calc(50% - 12px);
    margin-right: 8px;
  }
}
@media only screen and (max-width: 798px) {
  .purchase-details__buttons .action_button.action_button--secondary {
    margin-bottom: 12px;
  }
}
.purchase-details__buttons .action_button.action_button--secondary:hover, .purchase-details__buttons .action_button.action_button--secondary:focus {
  border: 1px solid {{ settings.button_cart_bg_color }};
  background-color: {{ settings.button_cart_bg_color }};
  color: {{ settings.button_cart_text_color }};
}

.purchase-details .purchase-details__spb--true {
  flex: 1 0 100%;
  align-items: flex-start;
  margin-top: 12px;
  margin-left: 0;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .purchase-details .purchase-details__spb--true {
    width: 100%;
  }
}
@media only screen and (max-width: 798px) {
  .purchase-details .purchase-details__spb--true {
    margin-left: 0;
    margin-bottom: 0;
  }
}
.purchase-details .purchase-details__spb--true .action_button.action_button--secondary {
  flex: 1 0 {{ atc_button_width }};
  margin: {{ atc_button_margin }};
  max-width: calc({{ atc_button_width }});
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .purchase-details .purchase-details__spb--true .action_button.action_button--secondary {
    width: 100% !important;
    max-width: {{ atc_button_width }} !important;
  }
}
@media only screen and (max-width: 798px) {
  .purchase-details .purchase-details__spb--true .action_button.action_button--secondary {
    flex: 1 0 100%;
    margin-right: 0px;
    margin-top: 0;
    margin-bottom: 12px;
    max-width: 100%;
  }
}
.purchase-details .purchase-details__spb--true .shopify-payment-button {
  margin: {{ atc_button_spb_margin }};
  max-width: calc({{ atc_button_width }});
  line-height: 1.2;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .purchase-details .purchase-details__spb--true .shopify-payment-button {
    width: 100% !important;
    max-width: {{ atc_button_width }} !important;
  }
}
@media only screen and (max-width: 798px) {
  .purchase-details .purchase-details__spb--true .shopify-payment-button {
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 10px;
    max-width: 100%;
  }
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded {
  font-size: {{ btn_add_to_cart_font_size }};
  border-radius: {{ settings.button_cart_border_radius | append: 'px' }};
  width: {{ btn_add_to_cart_width }};
  line-height: {{ btn_add_to_cart_line_height }};
  font-family: {{ settings.button__font.family }}, {{ settings.button__font.fallback_families }};
  font-weight: {{ settings.button__font.weight }};
  font-style: {{ settings.button__font.style }};
  letter-spacing: {{ settings.button_letter_spacing | append: 'px' }};
  text-transform: {{ settings.button_font_style }};
  border: 1px solid {{ settings.button_cart_border_color }};
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded, .purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded:link, .purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded:visited {
  color: {{ settings.button_cart_text_color }};
  background-color: {{ settings.button_cart_bg_color }};
  border-color: {{ settings.button_cart_border_color }};
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded:hover, .purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-hovered {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }} !important;
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded:focus, .purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-focused {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }};
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded:focus:not(:active), .purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-focused:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded:active, .purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-active {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }};
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-inverted {
  color: {{ settings.button_cart_bg_color }};
  background-color: {{ settings.button_cart_text_color }};
  border-color: {{ settings.button_cart_bg_color }};
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-small {
  font-size: 0.75rem;
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-normal {
  font-size: 1rem;
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-medium {
  font-size: 1.25rem;
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-payment-button__button--unbranded.is-large {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}
.purchase-details .purchase-details__spb--true .shopify-payment-button .shopify-paymeny-button__button--branded {
  border-radius: {{ settings.button_cart_border_radius | append: 'px' }};
}
.purchase-details .purchase-details__spb--false {
  height: {{ atc_button_height }};
  margin: 12px 0 0;
  display: inline-block;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .purchase-details .purchase-details__spb--false {
    width: 100%;
  }
}
.purchase-details .purchase-details__spb--false .button--add-to-cart {
  height: inherit;
  width: 50%;
}
@media only screen and (max-width: 798px) {
  .purchase-details .purchase-details__spb--false .button--add-to-cart {
    width: 100%;
  }
}
.purchase-details.has-quantity-box-true .purchase-details__spb--false {
  max-width: 50%;
}
@media only screen and (max-width: 798px) {
  .purchase-details.has-quantity-box-true .purchase-details__spb--false {
    max-width: 100%;
  }
}
.purchase-details.has-quantity-box-true .purchase-details__spb--false .button--add-to-cart {
  width: 100%;
}

/* Animation for checkmark on add to cart button */
.button--add-to-cart {
  position: relative;
}
.button--add-to-cart .text {
  display: block;
  animation-duration: 0.5s;
}
.button--add-to-cart .fadeInDown.text {
  animation-duration: 0.8s;
}
.button--add-to-cart .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.button--add-to-cart .checkmark path {
  stroke-dasharray: 19.79 19.79;
  stroke-dashoffset: 19.79;
  stroke: {{ settings.button_cart_text_color--highlight }};
  opacity: 0;
}
.button--add-to-cart .checkmark.checkmark-active path {
  animation: drawCheckmark 0.5s linear alternate forwards;
}

@keyframes drawCheckmark {
  from {
    stroke-dashoffset: 19.79;
    opacity: 1;
  }
  to {
    stroke-dashoffset: 0;
    opacity: 1;
  }
}
.ie button .checkmark path {
  stroke-dashoffset: 0;
  opacity: 0;
}
.ie button .checkmark.checkmark-active path {
  animation: fadeCheckmark 0.5s linear alternate forwards;
}

@keyframes fadeCheckmark {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  button .checkmark path {
    stroke-dashoffset: 0;
    opacity: 0;
  }
  button .checkmark.checkmark-active path {
    animation: fadeCheckmark 0.5s linear alternate forwards;
  }
  @keyframes fadeCheckmark {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}
.shopify-product-form--unselected-error .options-selection__option-name[data-variant-option-chosen-value=false],
.shopify-product-form--unselected-error label[data-variant-option-chosen-value=false] {
  color: #DE3618;
}

/* # Product media
================================================== */
{% assign product_media_fill_color = settings.regular_color %}
 {% assign product_media_bg_color = settings.shop_bg_color %}
.product-gallery__model model-viewer,
.product-gallery__video iframe {
  width: 100%;
}

.product-gallery__model {
  position: relative;
}
.product-gallery__model model-viewer {
  width: 100%;
  min-height: 300px;
}

.view-in-your-space {
  padding: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 44px;
  background: {{ settings.shop_bg_color | color_darken: 10 }};
  color: {{ product_media_fill_color }};
  font: inherit;
  text-transform: none;
  border-radius: 0px;
  border: 0;
}
.view-in-your-space svg path:first-child {
  stroke: {{ product_media_fill_color }};
}
.view-in-your-space svg path:last-child {
  fill: {{ product_media_fill_color }};
}

.plyr--video .plyr__controls {
  background: {{ product_media_bg_color | color_modify: 'alpha', 0.6 }};
  padding: 10px;
}
.plyr--video .plyr__control {
  background: transparent;
  color: {{ product_media_fill_color }};
}
.plyr--video .plyr__control:hover {
  background: {{ product_media_bg_color }};
  color: {{ product_media_fill_color }};
}
.plyr--video .plyr__controls__item.plyr__time,
.plyr--video input[type=range],
.plyr--video .plyr__controls__item {
  color: {{ product_media_fill_color }};
}
.plyr--video .plyr__progress input[type=range] + .thumb {
  background-color: {{ product_media_fill_color }};
  color: {{ product_media_fill_color }};
}
.plyr--video input[type=range]::-webkit-slider-thumb {
  background-color: {{ product_media_fill_color }};
}
.plyr--video input[type=range]::-moz-range-thumb {
  background-color: {{ product_media_fill_color }};
}
.plyr--video input[type=range]::-ms-thumb {
  background-color: {{ product_media_fill_color }};
}
.plyr--video .plyr__progress__buffer,
.plyr--video .plyr__control[aria-expanded=true] {
  background-color: {{ product_media_fill_color | color_modify: 'alpha', 0.6 }};
}
.plyr--video .plyr__control.plyr__tab-focus {
  background-color: {{ product_media_fill_color }};
}
.plyr--video .plyr__control--overlaid {
  background: {{ product_media_bg_color }};
  border-radius: 0;
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 3px;
}
.plyr--video .plyr__control--overlaid svg {
  left: auto;
}
.plyr--video .plyr__control--overlaid:hover {
  background: {{ product_media_bg_color }};
}
.plyr--video .plyr__control--overlaid:hover .play-icon-button-control rect {
  opacity: 0.75;
}
.plyr--video .plyr__control .play-icon-button-control {
  width: 52px;
  height: 52px;
}
.plyr--video .plyr__control .play-icon-button-control rect {
  fill: {{ product_media_bg_color }};
}
.plyr--video .plyr__control .play-icon-button-control path {
  fill: {{ product_media_fill_color }};
}

.plyr__controls .plyr__controls__item.plyr__progress__container {
  padding-right: 10px;
  padding-left: 10px;
}

.plyr--full-ui .plyr__volume {
  background-color: {{ product_media_bg_color | color_modify: 'alpha', 0.6 }};
  border-radius: 5px;
  bottom: 105px;
  height: 40px;
  opacity: 0;
  padding: 10px 15px !important;
  position: absolute;
  transform: rotate(270deg);
  transition: visibility 0.4s linear 0.4s, opacity 0.4s linear 0.3s;
  width: 140px;
}
.plyr--full-ui .plyr__volume:before {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid {{ product_media_bg_color | color_modify: 'alpha', 0.25 }};
  bottom: 18px;
  content: "";
  left: -6px;
  position: absolute;
  transform: rotate(90deg);
  z-index: 2;
}
.plyr--full-ui .plyr__volume:hover {
  opacity: 1;
  visibility: visible;
}
.plyr--full-ui .plyr__volume.plyr__volume--is-visible {
  opacity: 1;
  transition: visibility 0.4s linear, opacity 0.4s linear;
}
.plyr--full-ui .plyr__volume input[type=range] {
  color: {{ product_media_fill_color }};
  margin: 0 auto;
}

.plyr--full-ui .plyr__volume input[type=range]::-webkit-slider-runnable-track,
.plyr--full-ui .plyr__volume input[type=range]::-moz-range-track,
.plyr--full-ui .plyr__volume input[type=range]::-webkit-slider-thumb,
.plyr--full-ui .plyr__volume input[type=range]::-moz-range-thumb {
  box-shadow: none;
}

.plyr--full-ui .plyr__volume input[type=range]::-ms-fill-upper,
.plyr--full-ui .plyr__volume input[type=range]::-ms-thumb,
.plyr--full-ui .plyr__volume input[type=range].plyr__tab-focus::-webkit-slider-runnable-track,
.plyr--full-ui .plyr__volume input[type=range].plyr__tab-focus::-moz-range-track,
.plyr--full-ui .plyr__volume input[type=range].plyr__tab-focus::-ms-track {
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.25);
}

.plyr--full-ui .plyr__volume input[type=range].plyr__tab-focus::-webkit-slider-thumb,
.plyr--full-ui .plyr__volume input[type=range].plyr__tab-focus::-moz-range-thumb,
.plyr--full-ui .plyr__volume input[type=range].plyr__tab-focus::-ms-thumb,
.plyr--full-ui .plyr__volume input[type=range]:active::-webkit-slider-thumb,
.plyr--full-ui .plyr__volume input[type=range]:active::-moz-range-thumb,
.plyr--full-ui .plyr__volume input[type=range]:active::-ms-thumb {
  box-shadow: none;
}

.plyr--audio .plyr--full-ui .plyr__volume {
  bottom: 125px;
  right: -37px;
}

.plyr--is-ios .plyr__volume {
  display: none !important;
}

.plyr__control[data-plyr=mute]:hover + .plyr__volume {
  opacity: 1;
  transition: visibility 0.4s linear, opacity 0.4s linear;
}

@media (min-width: 480px) {
  .plyr--full-ui .plyr__volume input[type=range] {
    max-width: 90px;
  }
}
@media (min-width: 750px) {
  .plyr--full-ui .plyr__volume input[type=range] {
    max-width: 110px;
  }
}
@media only screen and (max-width: 989px) {
  .plyr--is-touch .plyr__volume {
    display: none !important;
  }
}
.shopify-model-viewer-ui .shopify-model-viewer-ui__button {
  color: {{ product_media_fill_color }};
  background: {{ product_media_bg_color }};
}
.shopify-model-viewer-ui .shopify-model-viewer-ui__button[hidden] {
  display: none;
}
.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover,
.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
  color: {{ product_media_fill_color | color_modify: 'alpha', 0.55 }};
}
.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
  background: {{ product_media_bg_color }};
  border: 1px solid {{ product_media_fill_color | color_modify: 'alpha', 0.05 }};
}
.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:not(:last-child):after {
  border-bottom: 1px solid {{ product_media_fill_color | color_modify: 'alpha', 0.05 }};
}

@media only screen and (min-width: 799px) {
  .video-on-hover .plyr__controls,
  .video-on-hover .plyr__control--overlaid,
  .swap-true .plyr--youtube .plyr__controls,
  .swap-true .plyr--youtube .plyr__control--overlaid,
  .video-controls-enabled--false .plyr__controls,
  .video-controls-enabled--false .plyr__control--overlaid {
    opacity: 0;
  }
  .video-on-hover .plyr__controls > *,
  .video-on-hover .plyr__control--overlaid > *,
  .swap-true .plyr--youtube .plyr__controls > *,
  .swap-true .plyr--youtube .plyr__control--overlaid > *,
  .video-controls-enabled--false .plyr__controls > *,
  .video-controls-enabled--false .plyr__control--overlaid > * {
    pointer-events: none;
    cursor: auto;
  }
}

.product-template:not(.product-template--image-scroll) .product-gallery .product-gallery__image {
  visibility: hidden;
}
.product-template:not(.product-template--image-scroll) .product-gallery .product-gallery__image.is-selected {
  visibility: visible;
}

.plyr.plyr--stopped .plyr__controls {
  display: none;
}

.product_slider.product-height-set model-viewer {
  margin: 0 auto;
}
.product_slider.product-height-set .product-gallery__main img,
.product_slider.product-height-set .product-gallery__main .plyr--html5 video,
.product_slider.product-height-set .product-gallery__main .plyr--youtube {
  width: 100%;
  object-fit: contain;
}

.product-gallery__thumbnails .product-gallery__thumbnail {
  position: relative;
}
.product-gallery__thumbnails .media-badge {
  position: absolute;
  top: 0;
  right: 0;
  max-width: 33%;
  max-height: 33%;
  min-width: 20px;
  width: 25px;
  height: 25px;
}
.product-gallery__thumbnails .media-badge svg {
  width: 100%;
  height: 100%;
}
.product-gallery__thumbnails .media-badge path:first-child {
  fill: {{ product_media_bg_color }};
}
.product-gallery__thumbnails .media-badge path:last-child {
  fill: {{ product_media_fill_color }};
}
.product-gallery__thumbnails .media-badge g path:first-child {
  stroke: {{ product_media_fill_color }};
}
.product-gallery__thumbnails .media-badge .media-badge__outline {
  stroke: {{ product_media_fill_color | color_modify: 'alpha', 0.05 }};
}

/* # Product thumbnail
================================================== */
.product__thumbnail .rating {
  flex-direction: column;
}
.product__thumbnail .rating__star {
  width: 16px;
}
.product__thumbnail .rating__count {
  margin: 0;
}
@media only screen and (max-width: 798px) {
  .product__thumbnail.has-padding-bottom {
    padding-bottom: 40px;
  }
}

.product-image__wrapper {
  position: relative;
}
.product-image__wrapper video,
.product-image__wrapper iframe {
  width: 100%;
}
.product-image__wrapper:hover .thumbnail-overlay__container {
  display: flex;
}
.product-image__wrapper:hover .thumbnail-overlay__container .quick-shop__buttons,
.product-image__wrapper:hover .thumbnail-overlay__container .quick-shop__info {
  display: flex;
}
@media only screen and (max-width: 798px) {
  .product-image__wrapper:hover .thumbnail-overlay__container {
    opacity: 0;
    visibility: hidden;
  }
}

.thumbnail-overlay__container {
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  flex-direction: column;
  display: none;
  position: absolute;
  padding: 20px;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: {{ settings.product_hover_bg_color | color_modify: 'alpha', 0.7 }};
  color: {{ settings.product_hover_text_color }};
  z-index: 10;
}
.thumbnail-overlay__container a,
.thumbnail-overlay__container a:visited {
  color: {{ settings.product_hover_text_color }};
}
.thumbnail-overlay__container .spr-icon-star:before {
  color: {{ settings.product_hover_text_color }};
}
.thumbnail-overlay__container .product-thumbnail__title,
.thumbnail-overlay__container .product-thumbnail__price {
  color: {{ settings.product_hover_text_color }};
}
.thumbnail-overlay__container .quick-shop__info,
.thumbnail-overlay__container .quick-shop__buttons {
  display: flex;
  justify-content: center;
  display: none;
  width: 100%;
}
.thumbnail-overlay__container .quick-shop__info {
  align-items: center;
}
.thumbnail-overlay__container .quick-shop__buttons {
  align-items: flex-start;
  padding-top: 20px;
  position: relative;
}
.thumbnail-overlay__container .animated {
  animation-duration: 0.5s;
}
.thumbnail-overlay__container .sold-out {
  color: {{ settings.product_hover_text_color }};
}

@media only screen and (max-width: 798px) {
  .thumbnail__hover-overlay--true .quick-shop__info {
    display: none;
  }
}
.thumbnail__hover-overlay--true .thumbnail__caption {
  display: none;
}
@media only screen and (max-width: 798px) {
  .thumbnail__hover-overlay--true .thumbnail__caption {
    display: block;
  }
}

.hidden-product-link {
  line-height: 0;
  font-size: 0;
  color: transparent;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 11;
}

.has-thumbnail-sticker .image__container {
  position: relative;
}
.has-thumbnail-sticker .sticker-holder {
  display: flex;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 2;
}
.has-thumbnail-sticker .sticker-position-right {
  justify-content: flex-end;
  text-align: right;
}
.has-thumbnail-sticker .sticker-position-bottom-left {
  align-items: flex-end;
}
.has-thumbnail-sticker .sticker-position-bottom-right {
  justify-content: flex-end;
  align-items: flex-end;
  text-align: right;
}
.has-thumbnail-sticker .spr-badge {
  display: flex;
  flex-direction: column;
  padding-top: 3px;
  padding-right: 3px;
}
.has-thumbnail-sticker .spr-badge .spr-badge-starrating,
.has-thumbnail-sticker .spr-badge .spr-badge-caption {
  text-align: {{ settings.thumbnail_text_alignment }};
}
.has-thumbnail-sticker .thumbnail-overlay__container .spr-badge-starrating,
.has-thumbnail-sticker .thumbnail-overlay__container .spr-badge-caption {
  justify-content: center;
  text-align: center;
}
.has-thumbnail-sticker .spr-badge-starrating .spr-icon-star,
.has-thumbnail-sticker .spr-badge-starrating .spr-icon-star-empty,
.has-thumbnail-sticker .spr-badge-caption {
  font-size: 80%;
}
.has-thumbnail-sticker .product-thumbnail__title,
.has-thumbnail-sticker .product-thumbnail__price {
  display: block;
  padding-top: 3px;
}
.has-thumbnail-sticker .thumbnail-sticker:nth-child(1n+4) {
  display: none;
}

.thumbnail-sticker {
  display: flex;
  width: 100%;
  padding: 5px 10px;
  text-align: center;
}
.thumbnail-sticker.thumbnail-sticker--sale, .thumbnail-sticker.thumbnail-sticker--sold-out {
  background-color: {{ sale_sticker_color }};
  color: {{banner_sale_text}};
  /* color: {{ settings.sale_sticker_color_text }}; */
}
.thumbnail-sticker.thumbnail-sticker--new {
  background-color: {{ new_sticker_color }};
  color: {{ new_sticker_color_text }};
}
.thumbnail-sticker.thumbnail-sticker--best-seller {
  background-color: {{ bestseller_sticker_color }};
  color: {{ bestseller_sticker_color_text }};
}
.thumbnail-sticker.thumbnail-sticker--coming-soon {
  background-color: {{ comingsoon_sticker_color }};
  color: {{ comingsoon_sticker_color_text }};
}
.thumbnail-sticker.thumbnail-sticker--staff-pick {
  background-color: {{ staffpick_sticker_color }};
  color: {{ staffpick_sticker_color_text }};
}
.thumbnail-sticker.thumbnail-sticker--pre-order {
  background-color: {{ preorder_sticker_color }};
  color: {{ preorder_sticker_color_text }};
}

.thumbnail-sticker__text {
  font-size: 1em;
}
@media only screen and (max-width: 1024px) {
  .thumbnail .thumbnail-sticker__text {
    font-size: 0.8em;
  }
}

.sticker-holder.sticker-shape-square {
  flex-wrap: wrap;
}
.sticker-holder.sticker-shape-square .thumbnail-sticker__text {
  width: 100%;
}

.one-seventh .sticker-shape-square .sticker-holder__content,
.one-sixth .sticker-shape-square .sticker-holder__content,
.one-fifth .sticker-shape-square .sticker-holder__content,
.sticker-shape-square .sticker-holder__content {
  max-width: 50%;
  font-size: 12px;
}
@media only screen and (max-width: 480px) {
  .one-seventh .sticker-shape-square .sticker-holder__content,
  .one-sixth .sticker-shape-square .sticker-holder__content,
  .one-fifth .sticker-shape-square .sticker-holder__content,
  .sticker-shape-square .sticker-holder__content {
    max-width: 60%;
  }
}

.one-fourth .sticker-shape-square .sticker-holder__content {
  max-width: 40%;
  font-size: 14px;
}
@media only screen and (max-width: 480px) {
  .one-fourth .sticker-shape-square .sticker-holder__content {
    max-width: 60%;
  }
}

.one-third .sticker-shape-square .sticker-holder__content {
  max-width: 33.3334%;
  font-size: 14px;
}
@media only screen and (max-width: 480px) {
  .one-third .sticker-shape-square .sticker-holder__content {
    max-width: 60%;
  }
}

.one-half .sticker-shape-square .sticker-holder__content {
  max-width: 25%;
  font-size: 16px;
}
@media only screen and (max-width: 480px) {
  .one-half .sticker-shape-square .sticker-holder__content {
    max-width: 60%;
  }
}

.sticker-shape-round.sticker-position-bottom-right .thumbnail-sticker,
.sticker-shape-round.sticker-position-right .thumbnail-sticker {
  float: right;
  margin-right: 5px;
  margin-left: 0;
}

.sticker-shape-round .thumbnail-sticker {
  border-radius: 50%;
  display: inline-block;
  width: calc(33.3333% - 5px);
  padding: calc(33.3333% - 5px) 0 0;
  margin-left: 5px;
  margin-bottom: 5px;
  margin-top: 5px;
  position: relative;
}
.sticker-shape-round .thumbnail-sticker .thumbnail-sticker__text {
  position: absolute;
  top: 50%;
  left: 50%;
  line-height: 1.1;
  transform: translateX(-50%) translateY(-50%);
  font-size: 0.7em;
  word-wrap: normal;
}
@media only screen and (max-width: 798px) {
  .sticker-shape-round .thumbnail-sticker .thumbnail-sticker__text {
    font-size: 1em;
  }
}

.one-seventh .sticker-shape-round .sticker-holder__content,
.one-sixth .sticker-shape-round .sticker-holder__content,
.one-fifth .sticker-shape-round .sticker-holder__content,
.sticker-shape-round .sticker-holder__content {
  width: 75%;
  font-size: 12px;
}
@media only screen and (max-width: 1024px) {
  .one-seventh .sticker-shape-round .sticker-holder__content,
  .one-sixth .sticker-shape-round .sticker-holder__content,
  .one-fifth .sticker-shape-round .sticker-holder__content,
  .sticker-shape-round .sticker-holder__content {
    width: 80%;
  }
}
@media only screen and (max-width: 480px) {
  .one-seventh .sticker-shape-round .sticker-holder__content,
  .one-sixth .sticker-shape-round .sticker-holder__content,
  .one-fifth .sticker-shape-round .sticker-holder__content,
  .sticker-shape-round .sticker-holder__content {
    width: 100%;
  }
}

.has-vertical-header .one-fourth .sticker-shape-round .sticker-holder__content {
  width: 90%;
}
@media only screen and (min-width: 1401px) {
  .has-vertical-header .one-fourth .sticker-shape-round .sticker-holder__content {
    max-width: 60%;
  }
}

.one-fourth .sticker-shape-round .sticker-holder__content {
  width: 60%;
  font-size: 14px;
}
@media only screen and (max-width: 1024px) {
  .one-fourth .sticker-shape-round .sticker-holder__content {
    width: 80%;
  }
}
@media only screen and (max-width: 480px) {
  .one-fourth .sticker-shape-round .sticker-holder__content {
    width: 100%;
  }
}

.one-third .sticker-shape-round .sticker-holder__content,
.one-half .sticker-shape-round .sticker-holder__content {
  width: 50%;
  font-size: 18px;
}
.one-third .sticker-shape-round .sticker-holder__content .thumbnail-sticker,
.one-half .sticker-shape-round .sticker-holder__content .thumbnail-sticker {
  margin: 5px;
  width: 100%;
  max-width: 60px;
  padding: 30px;
}
@media only screen and (max-width: 1024px) {
  .one-third .sticker-shape-round .sticker-holder__content,
  .one-half .sticker-shape-round .sticker-holder__content {
    width: 80%;
  }
}
@media only screen and (max-width: 480px) {
  .one-third .sticker-shape-round .sticker-holder__content,
  .one-half .sticker-shape-round .sticker-holder__content {
    width: 100%;
  }
}

.one-half .sticker-shape-round .sticker-holder__content--product .thumbnail-sticker {
  margin: 0 5px 15px 0;
}

.one-third .sticker-shape-round.sticker-position-bottom-right .thumbnail-sticker,
.one-half .sticker-shape-round.sticker-position-bottom-right .thumbnail-sticker {
  margin-right: 10px;
  margin-left: 0;
}
@media only screen and (max-width: 798px) {
  .one-third .sticker-shape-round.sticker-position-bottom-right .thumbnail-sticker,
  .one-half .sticker-shape-round.sticker-position-bottom-right .thumbnail-sticker {
    font-size: 0.8em;
  }
}
@media only screen and (max-width: 798px) {
  .one-third .sticker-shape-round.sticker-position-bottom-right .thumbnail-sticker,
  .one-half .sticker-shape-round.sticker-position-bottom-right .thumbnail-sticker {
    font-size: 0.6em;
  }
}

@media only screen and (max-width: 1024px) {
  .one-half .sticker-shape-round .sticker-holder__content {
    width: 65%;
  }
}

@media only screen and (max-width: 480px) {
  .small-down--one-whole .sticker-shape-round .thumbnail-sticker {
    font-size: 0.9em;
    width: calc(22% - 10px);
    padding: calc(22% - 10px) 0 0;
  }
}

.product_section .product__information .thumbnail-sticker:nth-child(1n+4) {
  display: none;
}
.product_section .product__information .jdgm-preview-badge {
  margin:0 0 20px;
}

.has-product-sticker .sticker-holder__content--product-center {
  display: flex;
  justify-content: center;
  margin: 0px auto;
  max-width: 30%;
  padding-top: 20px;
  font-size: 16px;
}
@media only screen and (max-width: 798px) {
  .has-product-sticker .sticker-holder__content--product-center {
    font-size: 13px;
  }
}
.has-product-sticker .sticker-holder__content--product-center .thumbnail-sticker {
  margin: 0 5px;
}
.has-product-sticker .sticker-shape-round .sticker-holder__content--product {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  max-width: 45%;
  font-size: 14px;
}
@media only screen and (max-width: 798px) {
  .has-product-sticker .sticker-shape-round .sticker-holder__content--product {
    max-width: 80%;
  }
}
.has-product-sticker .sticker-shape-round .is-product-slideshow {
  max-width: 30%;
}
@media only screen and (max-width: 798px) {
  .has-product-sticker .sticker-shape-round .sticker-holder__content--product-center {
    max-width: 50%;
  }
}
@media only screen and (max-width: 480px) {
  .has-product-sticker .sticker-shape-round .sticker-holder__content--product-center {
    max-width: 80%;
  }
}
.has-product-sticker .sticker-shape-round .thumbnail-sticker {
  display: inline-block;
  float: none;
  width: calc(33.333% - 10px);
  padding: calc(33.3333% - 10px) 0 0;
  margin: 0 10px 0 0;
}
.has-product-sticker .sticker-shape-round .thumbnail-sticker .thumbnail-sticker__text {
  font-size: 1em;
}
.has-product-sticker .sticker-shape-round .thumbnail-sticker.thumbnail-sticker--hidden {
  display: none;
}
.has-product-sticker .sticker-shape-square .sticker-holder__content--product {
  display: flex;
  width: 100%;
  max-width: 75%;
}
@media only screen and (max-width: 798px) {
  .has-product-sticker .sticker-shape-square .sticker-holder__content--product {
    max-width: 100%;
    font-size: 14px;
  }
}
.has-product-sticker .sticker-shape-square .is-product-slideshow {
  max-width: 45%;
}
@media only screen and (max-width: 798px) {
  .has-product-sticker .sticker-shape-square .sticker-holder__content--product-center {
    max-width: 100%;
    font-size: 14px;
  }
}
.has-product-sticker .sticker-shape-square .thumbnail-sticker {
  display: inline-block;
  float: none;
  width: calc(33.333% - 10px);
  margin: 0 10px 15px 0;
}
.has-product-sticker .sticker-shape-square .thumbnail-sticker .sticker-text {
  font-size: 1em;
}
.has-product-sticker .sticker-shape-square .thumbnail-sticker.thumbnail-sticker--hidden {
  display: none;
}
.has-product-sticker .sticker-holder {
  display: block;
  font-size: 14px;
}

.has-secondary-image-swap img {
  visibility: visible;
  height: auto;
}
.has-secondary-image-swap .swap--visible {
  opacity: 0;
  visibility: hidden;
  height: 0 !important;
}

.product-thumbnail__unit-price {
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

/* # Quick shop
================================================== */
.quick-shop__popup,
.quickshop-forms__container {
  display: none;
}

.quick-shop .product-gallery,
.quick-shop .product-gallery__nav {
  opacity: 0;
  transition: opacity 0.5s ease;
}
.quick-shop.quick-shop--loaded .product-gallery,
.quick-shop.quick-shop--loaded .product-gallery__nav {
  opacity: 1;
}

.quick-shop__buttons {
  z-index: 12;
}

.quickshop__gallery--right {
  flex-direction: row-reverse;
}

.quick-shop__popup {
  padding: 40px 10px;
}
.quick-shop__popup .fancybox-close-small svg {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}
.quick-shop__popup .modal_price {
  font-size: 1.2em;
}
.quick-shop__popup .product_form {
  padding: 20px 0;
}
@media only screen and (max-width: 798px) {
  .quick-shop__popup .purchase-details__buttons {
    margin-top: 12px;
  }
}
@media only screen and (max-width: 798px) {
  .quick-shop__popup .select, .quick-shop__popup .age-gate__select-wrapper,
  .quick-shop__popup .select select,
  .quick-shop__popup .age-gate__select-wrapper select {
    width: auto;
  }
}
.quick-shop__popup .select-container {
  clear: both;
  overflow: hidden;
}
.quick-shop__popup .select, .quick-shop__popup .age-gate__select-wrapper {
  display: block;
  float: left;
  clear: both;
  margin-bottom: 10px;
}

.quick-shop__gallery {
  padding-right: 10px;
}

.quick-shop__text-wrap {
  padding-left: 10px;
}

.quick-shop .product-gallery__thumbnails--bottom-thumbnails {
  margin-top: 20px;
}

@media only screen and (max-width: 798px) {
  .quick-shop .product-gallery__nav {
    margin-top: 60px;
  }
}

@media only screen and (min-width: 799px) {
  .quick-shop .quickshop__arrows--true .product-gallery__main:hover .flickity-prev-next-button {
    opacity: 1;
    visibility: visible;
  }
}

.quick-shop__lightbox .fancybox-content {
  width: 90%;
  max-width: 900px;
  background-color: {{ settings.qs_popup_color }};
}

.quick-shop__popup .sticker-shape-square .sticker-holder__content,
.quick-shop__popup .sticker-shape-round .sticker-holder__content {
  max-width: 100%;
  font-size: 13px;
}

.product-quickshop {
  background-color: rgba(30, 30, 30, 0.9);
}
.product-quickshop .quickshop-template {
  margin: 0 auto;
  padding: 4rem 0;
  max-width: 900px;
}
.product-quickshop .quickshop-container {
  background-color: {{ settings.qs_popup_color }};
  padding: 44px !important;
}
.product-quickshop .shopify-section:not(.quickshop-template) {
  display: none;
}

/* # Size chart
================================================== */
.size-chart {
  display: none;
}
@media only screen and (max-width: 798px) {
  .size-chart {
    width: 100%;
    padding: 0;
  }
}
.size-chart .fancybox-close-small svg {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

.size-chart-wrap {
  flex: 1 0 60%;
  max-width: 900px;
  position: relative;
  padding: 40px;
}
@media only screen and (max-width: 798px) {
  .size-chart-wrap {
    padding: 10px;
  }
}
.size-chart-wrap.animated {
  opacity: 0;
}

.product__size-chart {
  display: inline-flex;
  align-items: center;
  font-size: 1rem;
  margin: 0.5rem 0;
  border-bottom: 1px solid {{ link }};
  transition: color 0.3s ease-in-out, border 0.3s ease-in-out;
}
.product__size-chart:hover {
  border-bottom: 1px solid {{ settings.link_hover_color }};
}

/* #Swatch Styles
================================================== */
.swatch-element.swatch--active {
  border-color: black;
  box-shadow: 0px 0px 0px 2px rgb(255, 255, 255);
}

.selector-wrapper label {
  margin-bottom: 0.5rem;
}

.swatch_options {
  margin-bottom: 1.5rem;
}

.swatch .option-title {
  margin-bottom: 0.5rem;
  width: 100%;
}

.swatch input {
  display: none;
}

.swatch label {
  float: left;
  min-width: 40px;
  height: 40px;
  margin: 0;
  font-size: 13px;
  text-align: center;
  line-height: 40px;
  white-space: nowrap;
  text-transform: uppercase;
  cursor: pointer;
  padding: 0 10px;
}

.swatch .color label {
  min-width: 34px;
  height: 34px !important;
  line-height: 34px;
  background-position: center;
  background-size: cover;
}

.swatch-element {
  display: flex;
  border: #e2e2e2 thin solid;
  min-width: 40px;
  min-height: 40px;
}
{%- if settings.swatch_style != 'square' -%}
.swatch-element {
    border-radius: 3px;
  }
{%- endif -%}

.swatch-element.color {
  padding: 3px;
}

.swatch-element.color,
.swatch-element.color label {
  border: #e2e2e2 thin solid;
}
{%- if settings.swatch_style != 'square' -%}
.swatch-element.color,
  .swatch-element.color label {
    border-radius: 50%;
  }
{%- endif -%}

.swatch-element.color label {
  padding: 0;
  margin: 0;
  width: 34px;
  height: 34px;
  overflow: hidden;
}

.swatch input:checked + .swatch-element {
  border-color: black;
  box-shadow: 0px 0px 0px 2px rgb(255, 255, 255);
}

.swatch .swatch__option {
  transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0px 8px 8px 0;
  position: relative;
  outline: none;
}

.swatch__options {
  display: flex;
  flex-wrap: wrap;
}

.crossed-out {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}

.swatch .swatch-element .crossed-out {
  display: none;
}

.swatch [data-variant-option-available=false] + .swatch-element .crossed-out {
  display: block;
  width: 100%;
  height: 100%;
}
{%- if settings.swatch_style != 'square' -%}
.swatch [data-variant-option-available=false] + .swatch-element .crossed-out {
    border-radius: 50%;
  }
{%- endif -%}
.swatch [data-variant-option-available=false] + .swatch-element.color [data-variant-image=true] {
  opacity: 1;
}
.swatch [data-variant-option-available=false] + .swatch-element label {
  text-decoration: line-through;
  opacity: 0.6;
}

.swatch input:disabled + .swatch-element .crossed-out,
.swatch input:disabled + .swatch-element label {
  cursor: not-allowed;
}

.swatch .tooltip {
  text-align: center;
  background: #333;
  background: rgba(51, 51, 51, 0.9);
  color: #fff;
  bottom: 100%;
  padding: 5px 10px;
  display: block;
  position: absolute;
  width: 120px;
  font-size: 13px;
  left: -35px;
  margin-bottom: 15px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.25s ease-out;
  box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.28);
  z-index: 60;
}

.swatch .tooltip:before {
  bottom: -20px;
  content: " ";
  display: block;
  height: 20px;
  left: 0;
  position: absolute;
  width: 100%;
}

.swatch .tooltip:after {
  border-left: solid transparent 10px;
  border-right: solid transparent 10px;
  border-top: solid rgba(51, 51, 51, 0.9) 10px;
  bottom: -10px;
  content: " ";
  height: 0;
  left: 50%;
  margin-left: -13px;
  position: absolute;
  width: 0;
}

.swatch .swatch-element:hover .tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.swatch input:disabled + .swatch-element {
  opacity: 0.5;
}

.swatch-element img[src*=no-image-50],
.swatch__image--empty {
  opacity: 0;
}

.swatch-element .swatch__image {
  height: 100%;
  object-fit: cover;
}

/* # Announcement bar
================================================== */
.announcement-bar {
  width: 100%;
  position: absolute;
}
.announcement-bar.is-small {
  font-size: 0.75rem;
}
.announcement-bar.is-regular {
  font-size: 1rem;
}
.announcement-bar.is-large {
  font-size: 1.5rem;
}
@media only screen and (max-width: 480px) {
  .announcement-bar.is-large {
    font-size: 1rem;
  }
}
.announcement-bar .is-width-standard .message-header {
  padding-left: 0;
  padding-right: 0;
}
.announcement-bar .is-width-wide .close {
  right: 10px;
}
.announcement-bar .announcement-bar__close {
  position: absolute;
  right: 0;
}
@media only screen and (max-width: 798px) {
  .announcement-bar .padding {
    display: none;
  }
}

.announcement-bar__text {
  display: flex;
  align-items: center;
}

.announcement-bar__icon {
  line-height: 0;
}

.show-close-icon-true {
  padding-left: 20px;
}

.announcement-bar__content {
  flex: 1 1 auto;
  display: flex;
  align-items: center;
}
.announcement-bar__content p {
  padding: 0 0.5em;
  display: inline-block;
}

.announcement-bar__shadow {
  opacity: 0;
  visibility: hidden;
  min-height: 20px;
}

.announcement-bar--visible .announcement-container {
  transition: all 0s linear;
  height: auto;
}

.announcement-container {
  height: 0;
  overflow: hidden;
  position: relative;
  z-index: 90;
  order: -1;
}

{%- if settings.header_layout == 'vertical' -%}

.announcement-container {
    grid-area: announcement;
  }

.scroll-locked .announcement-container {
    z-index: 0;
  }

{%- endif -%}
/* # Article (blog posts)
================================================== */
@media only screen and (min-width: 799px) {
  .author-share-wrap .blog-share > div {
    justify-content: flex-end;
  }
}

.comment-section {
  display: block;
}

.blog-author {
  padding-left: 0;
}
.blog-author img {
  width: 100px;
  height: 100px;
}
.blog-author p {
  font-size: 0.9rem;
}

@media only screen and (max-width: 798px) {
  .comment-section--cards img {
    width: 100px;
  }
}

.article-main .container > section {
  flex: auto;
}

/* # Banner
================================================== */
.banner__wrapper {
  overflow: hidden;
  position: relative;
  max-height: 100vh;
}
.banner__wrapper img {
  object-fit: cover;
  height: 100%;
}
.banner__wrapper.is-small .image-element__wrap {
  height: 30vh;
}
@media only screen and (max-width: 798px) {
  .banner__wrapper.is-small .image-element__wrap {
    height: auto;
  }
}
.banner__wrapper.is-medium .image-element__wrap {
  height: 60vh;
}
@media only screen and (max-width: 798px) {
  .banner__wrapper.is-medium .image-element__wrap {
    height: auto;
  }
}
.banner__wrapper.is-large .image-element__wrap {
  height: 90vh;
}
@media only screen and (max-width: 798px) {
  .banner__wrapper.is-large .image-element__wrap {
    height: auto;
  }
}

.banner__content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: auto;
  z-index: 3;
  width: 100%;
}

.banner__text {
  margin: auto;
  max-width: 900px;
}

.banner__heading {
  font-family: {{ settings.banner_heading__font.family }}, {{ settings.banner_heading__font.fallback_families }};
  font-weight: {{ settings.banner_heading__font.weight }};
  font-style: {{ settings.banner_heading__font.style }};
  font-size: {{ settings.banner_heading_size | append: 'px' }};
  text-transform: {{ settings.banner_heading_style }};
  color: {{ settings.banner_heading_color }};
}
@media only screen and (max-width: 1024px) {
  .banner__heading {
    font-size: 2.5em;
  }
}
@media only screen and (max-width: 480px) {
  .banner__heading {
    font-size: 2em;
  }
}

.banner__subheading {
  font-family: {{ settings.banner_text__font.family }}, {{ settings.banner_text__font.fallback_families }};
  font-weight: {{ settings.banner_text__font.weight }};
  font-style: {{ settings.banner_text__font.style }};
  font-size: {{ settings.banner_text_size | append: 'px' }};
  letter-spacing: {{ settings.banner_text_letter_spacing | append: 'px' }};
  color: {{ settings.banner_heading_color }};
}

.dark-overlay-true {
  position: relative;
}
.dark-overlay-true:after {
  content: "";
  position: absolute;
  z-index: 2;
  background: rgba(0, 0, 0, 0.5);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.collection-list__nav-wrapper {
  display: none;
  margin-bottom: 10px;
}
@media only screen and (max-width: 798px) {
  .collection-list__nav-wrapper {
    display: flex;
    justify-content: flex-end;
  }
}
@media only screen and (max-width: 480px) {
  .collection-list__nav-wrapper {
    justify-content: space-between;
  }
}

.collection-list__nav {
  color: {{ link }};
  cursor: pointer;
}
.collection-list__nav.collection-list__nav--prev {
  margin-right: 10px;
}
@media only screen and (max-width: 480px) {
  .collection-list__nav.collection-list__nav--prev {
    margin-right: 0;
  }
}

.collection-list__wrapper {
  /* Enable flickity on mobile */
}
.collection-list__wrapper .flickity-page-dots {
  bottom: 0;
}
@media only screen and (min-width: 799px) {
  .collection-list__wrapper .flickity-page-dots {
    display: none;
  }
}
@media only screen and (max-width: 798px) {
  .collection-list__wrapper.collection-list__wrapper--page-dots-true {
    padding-bottom: 45px;
  }
}
@media only screen and (max-width: 798px) {
  .collection-list__wrapper::after {
    display: none;
    content: "flickity";
  }
}

.collection-list__thumbnail {
  overflow: hidden;
}
.collection-list__thumbnail .product-wrap {
  position: relative;
  overflow: hidden;
}
.collection-list__thumbnail .product-wrap.enable-zoom-true:hover img, .collection-list__thumbnail .product-wrap.enable-zoom-true:hover svg {
  transform: scale(1.1);
}
.flickity-enabled .collection-list__thumbnail.has-gutter--mobile, .has-gutter-enabled.has-multirow-blocks .flickity-enabled .collection-list__thumbnail {
  margin-bottom: 0;
}
.collection-list__thumbnail img,
.collection-list__thumbnail svg {
  transition: all 0.3s ease-in-out;
}

.collection-list__caption-wrapper--below-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}
@media only screen and (max-width: 798px) {
  .collection-list__wrapper--page-dots-true .collection-list__caption-wrapper--below-image {
    padding-bottom: 0;
  }
}
.collection-list__caption-wrapper--below-image .title {
  text-align: center;
}

/* # Contact form
================================================== */
.contact-form .contact-form__form {
  padding: 40px;
}
@media only screen and (max-width: 798px) {
  .contact-form .contact-form__form {
    margin: 30px 0;
  }
}
.contact-form .contact-form__image {
  margin-bottom: 25px;
}
.contact-form .contact-form__blocks .contact-form__block {
  margin-bottom: 20px;
}
.contact-form .contact-form__blocks .contact-form__block label {
  color: inherit !important;
}
.contact-form .contact-form__social .social-icons {
  align-items: baseline;
  margin: 20px 0 0 0;
  list-style: none;
}
.contact-form .contact-form__social .social-icons li {
  padding-right: 10px;
}
.contact-form .quote {
  margin-bottom: 1rem;
}
.contact-form .text-align-right .social-icons {
  justify-content: flex-end;
}
.contact-form .text-align-center .social-icons {
  justify-content: center;
}

.contact-form--left {
  flex-direction: row-reverse;
}

@media only screen and (max-width: 798px) {
  .contact-form--right .contact-form__form,
  .contact-form--left .contact-form__form {
    margin-right: 0;
    margin-left: 0;
  }
}

.contact-form--center {
  justify-content: center;
}

/* # Custom contact form
================================================== */
.custom-contact-form__errors,
.custom-contact-form__success {
  margin-bottom: 20px;
}

.custom-contact-form__block {
  margin-bottom: 20px;
}
.custom-contact-form__block .label {
  color: inherit;
}

.custom-contact-form__block--checkbox ul,
.custom-contact-form__block--radio ul {
  margin: 0;
  list-style: none;
}

.custom-contact-form__block--checkbox ul li,
.custom-contact-form__block--radio ul li {
  display: flex;
  align-items: center;
}
.custom-contact-form__block--checkbox ul li .radio,
.custom-contact-form__block--checkbox ul li .checkbox,
.custom-contact-form__block--radio ul li .radio,
.custom-contact-form__block--radio ul li .checkbox {
  margin-right: 10px;
}

.blog-posts__nav-wrapper {
  display: none;
  margin-bottom: 10px;
}
@media only screen and (max-width: 798px) {
  .blog-posts__nav-wrapper {
    display: flex;
    justify-content: flex-end;
  }
}
@media only screen and (max-width: 480px) {
  .blog-posts__nav-wrapper {
    justify-content: space-between;
  }
}

.blog-posts__nav {
  color: {{ link }};
  cursor: pointer;
}
.blog-posts__nav.blog-posts__nav--prev {
  margin-right: 10px;
}
@media only screen and (max-width: 480px) {
  .blog-posts__nav.blog-posts__nav--prev {
    margin-right: 0;
  }
}

.blog-posts__wrapper {
  /* Enable flickity on mobile */
}
.blog-posts__wrapper .flickity-page-dots {
  bottom: 0;
}
@media only screen and (min-width: 799px) {
  .blog-posts__wrapper .flickity-page-dots {
    display: none;
  }
}
@media only screen and (max-width: 798px) {
  .blog-posts__wrapper.blog-posts__wrapper--page-dots-true {
    padding-bottom: 25px;
  }
}
@media only screen and (max-width: 798px) {
  .blog-posts__wrapper::after {
    display: none;
    content: "flickity";
  }
}

/* # Featured collection
================================================== */
.featured-collection-section .slider-gallery,
.block__featured-collection .slider-gallery {
  width: 100%;
}
@media only screen and (max-width: 798px) {
  .featured-collection-section .flickity-prev-next-button,
  .block__featured-collection .flickity-prev-next-button {
    display: none;
  }
}

.container.has-column-padding-bottom .featured-collection__button-container {
  margin-bottom: 0;
  text-align: center;
}
@media only screen and (min-width: 799px) {
  .featured-collection--slider .featured-collection__button-container {
    margin-top: 20px;
  }
}
@media only screen and (max-width: 798px) {
  .featured-collection--slider-mobile .featured-collection__button-container {
    margin-top: 20px;
  }
}
@media only screen and (max-width: 798px) {
  .featured-collection--grid-mobile.featured-collection--onboarding-false .featured-collection__button-container {
    margin-top: -20px;
  }
}

/* # Featured promotions
================================================== */
.featured-promotions .has-gutter-enabled .flickity-enabled .featured-promotions__block {
  margin-left: 10px;
  margin-right: 10px;
}
@media only screen and (max-width: 480px) {
  .featured-promotions .has-gutter-enabled .featured-promotions__block--2:first-child {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 480px) {
  .featured-promotions .is-width-standard .featured-promotions__wrapper.container .featured-promotions__block {
    margin-bottom: 20px;
  }
}
.featured-promotions .is-width-standard .featured-promotions__wrapper.container .featured-promotions__block:last-child {
  margin-bottom: 0;
}

.featured-promotions__nav-wrapper {
  justify-content: flex-end;
}
@media only screen and (max-width: 480px) {
  .featured-promotions__nav-wrapper {
    justify-content: space-between;
  }
}

.featured-promotions__nav {
  color: {{ link }};
  margin-bottom: 10px;
  cursor: pointer;
  transition: color 0.3s ease-in-out;
}
.featured-promotions__nav:first-child {
  margin-right: 10px;
}
.featured-promotions__nav:hover {
  color: {{ settings.link_hover_color }};
}

.is-width-wide .featured-promotions__nav:last-child {
  margin-right: 10px;
}

.featured-promotions__block {
  position: relative;
  display: inline-block;
}

.featured-promotions__block--has-link:hover .featured-promotions__content img,
.featured-promotions__block--has-link:hover .featured-promotions__content svg {
  transform: scale(1.1);
}
.featured-promotions__block--has-link:hover .has-border {
  border-width: 3px;
}

.featured-promotions__block.has-image-crop:after {
  content: "";
  display: block;
  padding-bottom: 100%;
}
.featured-promotions__block.has-image-crop .featured-promotions__content {
  position: absolute;
  width: 100%;
  height: 100%;
}

.featured-promotions__content {
  position: relative;
  overflow: hidden;
  font-size: 1rem;
}
.featured-promotions__content img,
.featured-promotions__content svg {
  transition: all 0.3s ease-in-out;
}
.featured-promotions__content:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
}

.featured-promotions__overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 3;
  transition: all 0.3s linear;
  padding: 0 20px;
  text-align: center;
}
.featured-promotions__overlay span {
  width: 50px;
  height: 50px;
}
.featured-promotions__overlay.has-border {
  border: thin solid;
}

.featured-promotions__title {
  margin: 10px 0;
  text-align: center;
  line-height: 1.2;
}

.featured-promotions__subtitle p {
  padding: 5px 0;
}

.featured-promotions__wrapper:after {
  content: "flickity";
  display: none;
}

@media only screen and (min-width: 799px) {
  .mobile-slider .featured-promotions__nav-wrapper {
    display: none;
  }
}
@media only screen and (min-width: 799px) {
  .mobile-slider .featured-promotions__wrapper:after {
    content: "";
  }
}

/* # Featured product
================================================== */
.featured-product .product-thumbnail__title {
  display: block;
  padding-top: 0.5em;
}
.featured-product .slideshow-enabled--false .product-gallery__image:not(:first-child) {
  display: none;
}

.featured-product-section .select, .featured-product-section .age-gate__select-wrapper,
.featured-product-section .select select,
.featured-product-section .age-gate__select-wrapper select {
  height: 44px;
}

/* # Footer
================================================== */
{% assign footer_alpha = settings.footer_border | color_extract: 'alpha' | floor %}
  {% assign footer_border = 0 %}
  {% if footer_alpha != 0 %}
    {% capture footer_border %}thin solid {{ settings.footer_border }}{% endcapture %}
  {% endif %}
.footer__container {
  width: 100%;
  background-color:#5461c8;
}

.footer__content {
  background-color: {{ settings.footer_background }};
  color: {{ settings.footer_text_color }};
  font-size: {{ settings.footer_font_size | append: 'px' }};
}
.footer__content a,
.footer__content a:visited {
  color: {{ settings.footer_link_color }};
}
.footer__content a:hover,
.footer__content a:active {
  color: {{ settings.footer_link_hover_color }};
}

.footer__heading {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-size: {{ settings.footer_heading_font_size | append: 'px' }};
  color: {{ settings.footer_text_color }};
  margin-bottom: 10px;
}

@media only screen and (max-width: 480px) {
  .footer__block {
    padding-bottom: 20px;
  }
}
.footer__block.block__logo .footer__heading {
  font-family: {{ settings.logo__font.family }}, {{ settings.logo__font.fallback_families }};
  font-weight: {{ settings.logo__font.weight }};
  font-style: {{ settings.logo__font.style }};
  font-size: {{ settings.logo_font_size | append: 'px' }};
  text-transform: {{ settings.logo_font_style }};
}

.footer .social-icons li {
  padding-right: 5px;
}

.footer__logo-wrapper {
  width: 100%;
}
.footer__logo-wrapper.is-small {
  max-width: 60%;
}
@media only screen and (max-width: 798px) {
  .footer__logo-wrapper.is-small {
    max-width: 30%;
  }
}
.footer__logo-wrapper.is-medium {
  max-width: 80%;
}
@media only screen and (max-width: 798px) {
  .footer__logo-wrapper.is-medium {
    max-width: 50%;
  }
}
.footer__logo-wrapper.is-large {
  max-width: 100%;
}
@media only screen and (max-width: 798px) {
  .footer__logo-wrapper.is-large {
    max-width: 70%;
  }
}
.footer__logo-wrapper .image-element__wrap {
  margin-right: 0;
  margin-left: 0;
}

.footer__credits {
  font-size: smaller;
}

.footer__credits p {
  display: inline;
}

{%- if settings.footer_layout == 'centered' -%}

.sub-footer {
    flex-direction: column;
    gap: 25px;
  }

.footer__content {
    /* border-bottom: {{ footer_border }}; */
    border-top: {{ footer_border }};
  }

.footer__menu {
    list-style: none;
    padding-left: 0;
    text-align: center;
  }

.footer__menu .footer__heading {
    display: none;
  }

.footer__menu-link {
    display: inline-block;
    padding: 0 5px;
  }

@media only screen and (max-width: 480px) {
    .footer__menu-link {
      display: block;
      padding: 5px 0;
    }
  }

.footer__logo-wrapper {
    margin: 0px auto;
  }

.footer__logo-wrapper.is-small {
    max-width: 200px;
  }

@media only screen and (max-width: 798px) {
    .footer__logo-wrapper.is-small {
      max-width: 100px;
    }
  }

.footer__logo-wrapper.is-medium {
    max-width: 250px;
  }

@media only screen and (max-width: 798px) {
    .footer__logo-wrapper.is-medium {
      max-width: 200px;
    }
  }

.footer__logo-wrapper.is-large {
    max-width: 350px;
  }

@media only screen and (max-width: 798px) {
    .footer__logo-wrapper.is-large {
      max-width: 300px;
    }
  }

.footer .social-icons {
    justify-content: center;
  }

.footer .social-icons li {
    padding: 0 5px;
  }

.footer .social-icons + .footer__follow-on-shop {
    margin-top: 20px;
  }

.footer__block.block__logo {
    overflow: hidden;
    width: 100%;
  }

.footer__block.block__text {
    max-width: 800px;
    margin: 0 auto;
    padding-left: 20px;
    padding-right: 20px;
  }

.footer-centered__disclosure {
    justify-content: center;
  }

@media only screen and (max-width: 480px) {
    .footer__credits {
      text-align: center;
    }
  }

@media only screen and (max-width: 480px) {
    .footer__payment-methods {
      text-align: center;
      justify-content: center;
    }
  }
{%- elsif settings.footer_layout == 'classic' -%}
.sub-footer {
    flex-direction: column;
    gap: 28px;
  }
.sub-footer__row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
  }
@media only screen and (max-width: 1024px) {
    .sub-footer__row {
      flex-direction: column-reverse;
      text-align: center;
    }
  }
.sub-footer__row > :nth-child(2) {
    margin-left: auto;
    text-align: right;
  }
@media only screen and (max-width: 1024px) {
    .sub-footer__row > :nth-child(2) {
      margin-inline: auto;
      text-align: center;
    }
  }
.sub-footer__row--first .sub-footer-right {
    width: 100%;
  }
@media only screen and (min-width: 1025px) {
    .sub-footer__row--first .sub-footer-right > :nth-child(1) {
      display: flex;
      justify-content: flex-end;
    }
  }
.sub-footer__row--first .sub-footer-right > :nth-child(2) {
    display: none;
  }
.sub-footer__row--first .sub-footer-right--selectors-only {
    display: none;
  }
@media only screen and (max-width: 1024px) {
    .sub-footer__row--first .sub-footer-right--selectors-only {
      display: block;
    }
  }
.sub-footer__row--first .sub-footer-right--payments-only {
    display: none;
  }
@media only screen and (max-width: 1024px) {
    .sub-footer__row--second.sub-footer__row--column {
      flex-direction: column;
    }
  }
.sub-footer__row--second .copyright {
    margin: 0;
  }
@media only screen and (min-width: 1025px) {
    .sub-footer__row--second .sub-footer-right > :nth-child(1) {
      display: flex;
      justify-content: flex-end;
    }
  }
.sub-footer__row--second .sub-footer-right > :nth-child(1):not(:only-child) {
    display: none;
  }
.sub-footer__row--second .sub-footer-right--selectors-only {
    display: block;
  }
@media only screen and (max-width: 1024px) {
    .sub-footer__row--second .sub-footer-right--selectors-only {
      display: none;
    }
  }
.sub-footer__row--second .sub-footer-right--payments-only {
    display: block;
  }
@media only screen and (max-width: 1024px) {
    .sub-footer__row--selectors-only .sub-footer-right--selectors-only {
      display: block;
    }
  }
@media only screen and (max-width: 1024px) {
    .sub-footer__row--payments-only .sub-footer-right--payments-only {
      display: block;
    }
  }
.footer__content {
    /* border-bottom: {{ footer_border }}; */
    border-top: {{ footer_border }};
  }
.footer__block.one-fifth .is-stretched-width {
    min-width: 100% !important;
  }
@media only screen and (max-width: 1024px) {
    .footer__block .is-stretched-width {
      min-width: 100% !important;
    }
  }
.footer-classic__disclosure {
    justify-content: flex-end;
  }
@media only screen and (max-width: 1024px) {
    .footer-classic__disclosure {
      justify-content: center;
    }
  }
.footer__credits {
    margin: 0;
  }
@media only screen and (max-width: 480px) {
    .footer__credits {
      text-align: center;
    }
  }
@media only screen and (max-width: 480px) {
    .footer__payment-methods {
      text-align: center;
      justify-content: center;
    }
  }
{%- elsif settings.footer_layout == 'promotional' -%}
.sub-footer {
    flex-direction: column;
    gap: 25px;
  }
.footer__container {
    border-bottom: {{ footer_border }};
    border-top: {{ footer_border }};
  }
.footer__block .newsletter-form {
    max-width: 80%;
  }
@media only screen and (max-width: 798px) {
    .footer__block .newsletter-form {
      max-width: 100%;
    }
  }
.footer__promo .link-list__block {
    padding-top: 20px;
  }
@media only screen and (max-width: 480px) {
    .footer__promo {
      flex-direction: column-reverse;
    }
  }
.footer__promo-container {
    padding: 50px;
  }
.footer__content {
    padding-left: 40px;
    padding-right: 40px;
  }
@media only screen and (max-width: 480px) {
    .footer__content {
      padding-left: 20px;
      padding-right: 20px;
    }
  }
.footer-link-list__block:first-child {
    padding-top: 0;
  }
@media only screen and (max-width: 480px) {
    .footer-link-list__block {
      padding-top: 20px;
    }
  }
.footer__block:not(:last-child) {
    padding-bottom: 20px;
  }
.footer__wrap {
    height: 100%;
  }
.footer__logo-wrapper {
    width: 100%;
  }
.footer__logo-wrapper.is-small {
    max-width: 150px;
  }
.footer__logo-wrapper.is-medium {
    max-width: 200px;
  }
.footer__logo-wrapper.is-large {
    max-width: 250px;
  }
.footer-menu__disclosure.footer-promotional__disclosure {
    justify-content: flex-start;
  }
{%- endif -%}
.footer__logo-wrapper {
  display: block;
}
.footer__logo-wrapper:hover .footer__heading, .footer__logo-wrapper:hover img {
  opacity: 0.7;
}
.footer__logo-wrapper .footer__heading,
.footer__logo-wrapper img {
  transition: opacity 0.3s ease-in;
}

/* Footer currency/language switcher */
.footer-menu__disclosure {
  display: flex;
  width: 100%;
}
@media only screen and (max-width: 1024px) {
  .footer-menu__disclosure {
    justify-content: center;
    max-width: 100%;
  }
  .footer-menu__disclosure .selectors-form {
    max-width: 100%;
  }
  .footer-menu__disclosure .selectors-form__wrap {
    max-width: 100%;
    margin-bottom: 0;
  }
  .footer-menu__disclosure .selectors-form__item {
    max-width: 100%;
  }
}

/* # Gallery
================================================== */
.gallery__nav-wrapper {
  display: none;
  margin-bottom: 10px;
}
@media only screen and (max-width: 798px) {
  .gallery__nav-wrapper {
    display: flex;
    justify-content: flex-end;
  }
}
@media only screen and (max-width: 480px) {
  .gallery__nav-wrapper {
    justify-content: space-between;
  }
}

.gallery__nav {
  color: {{ link }};
  cursor: pointer;
}
.gallery__nav.gallery__nav--prev {
  margin-right: 10px;
}
@media only screen and (max-width: 480px) {
  .gallery__nav.gallery__nav--prev {
    margin-right: 0;
  }
}

.gallery__wrapper {
  /* Enable flickity on mobile */
}
.gallery__wrapper .flickity-page-dots {
  bottom: 0;
}
@media only screen and (min-width: 799px) {
  .gallery__wrapper .flickity-page-dots {
    display: none;
  }
}
@media only screen and (max-width: 798px) {
  .gallery__wrapper.gallery__wrapper--page-dots-true.flickity-enabled {
    padding-bottom: 25px;
  }
}
@media only screen and (max-width: 798px) {
  .gallery__wrapper::after {
    display: none;
    content: "flickity";
  }
}
.gallery__wrapper.gallery__wrapper--classic {
  display: flex;
  flex-wrap: wrap;
}
.gallery__wrapper.gallery__wrapper--vertical-masonry, .gallery__wrapper.gallery__wrapper--horizontal-masonry {
  justify-content: center;
}
.gallery__wrapper.gallery__wrapper--horizontal-masonry {
  display: flex;
  flex-wrap: wrap;
}
.gallery__wrapper.gallery__wrapper--horizontal-masonry img {
  width: 100%;
}

.gallery__item {
  position: relative;
}
.gallery__item:hover .gallery-item__overlay {
  opacity: 1;
}
@media only screen and (max-width: 798px) {
  .gallery__wrapper--vertical-masonry.flickity-enabled .gallery__item {
    margin-right: calc(20px / 2);
    margin-left: calc(20px / 2);
  }
}
.has-no-side-gutter .gallery__item {
  margin: 0;
}
@media only screen and (max-width: 798px) {
  .has-gutter-enabled .gallery__item {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 798px) {
  .gallery__wrapper:not(.flickity-enabled) .gallery__item:last-child {
    margin-bottom: 0;
  }
}
@media only screen and (max-width: 798px) {
  .flickity-enabled.gallery__wrapper--page-dots-false .gallery__item {
    margin-bottom: 0;
  }
}

.has-image-crop .gallery__item-wrap {
  height: 100%;
}
@media only screen and (max-width: 798px) {
  .has-image-crop--mobile-true .gallery__item-wrap {
    height: 100%;
  }
}

.gallery-item__overlay {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.2s ease;
}
.gallery-item__overlay .icon {
  width: 2.4rem;
  height: 2.4rem;
  margin: 20px;
}

/* # Grid
================================================== */
.section--grid-section {
  margin: 0 auto;
  padding: 0 10px;
}
@media only screen and (max-width: 798px) {
  .section--grid-section {
    padding-inline: 0;
  }
}
.section--grid-section.text-align--left {
  --margin-inline: 0 auto;
  --justify-content: flex-start;
}
.section--grid-section.text-align--center {
  --margin-inline: auto;
  --justify-content: center;
}
.section--grid-section.text-align--right {
  --margin-inline: auto 0;
  --justify-content: flex-end;
}
.section--grid-section.mobile-text-align--left {
  --mobile-margin-inline: 0 auto;
  --mobile-text-align: left;
  --mobile-justify-content: flex-start;
}
.section--grid-section.mobile-text-align--center {
  --mobile-margin-inline: auto;
  --mobile-text-align: center;
  --mobile-justify-content: center;
}
.section--grid-section.mobile-text-align--right {
  --mobile-margin-inline: auto 0;
  --mobile-text-align: right;
  --mobile-justify-content: flex-end;
}
.section--grid-section .grid-items-wrapper {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(var(--column-count), 1fr);
  gap: var(--grid-gap, 20px);
}
@media only screen and (max-width: 798px) {
  .section--grid-section .grid-items-wrapper {
    display: flex;
    flex-direction: column;
  }
  .section--grid-section .grid-items-wrapper:after {
    content: "flickity";
    display: none;
  }
}
@media only screen and (max-width: 798px) {
  .section--grid-section .grid-item {
    margin-inline: 10px;
    width: calc(100% - 20px);
  }
}
@media only screen and (max-width: 798px) {
  .section--grid-section.is-width-wide .grid-item {
    margin-inline: 0;
    width: 100%;
  }
}
@media only screen and (max-width: 798px) {
  .section--grid-section.is-width-wide.section--grid-section--mobile-slider:not(.section--grid-section--no-gutter) .grid-item {
    margin-inline: 10px;
  }
}
.section--grid-section .grid-item__image-wrapper {
  position: relative;
  border-radius: var(--item-border-radius, 0);
  overflow: hidden;
}
.section--grid-section .grid-item__image-wrapper.enable-zoom--true:hover img, .section--grid-section .grid-item__image-wrapper.enable-zoom--true:hover svg {
  transform: scale(1.1);
}
.section--grid-section .grid-item__image-wrapper img,
.section--grid-section .grid-item__image-wrapper svg {
  transition: all 0.3s ease-in-out;
}
.section--grid-section .grid-item__image-wrapper .placeholder-svg {
  height: var(--image-height, auto);
  max-height: var(--image-height, none);
  border: none;
}
.section--grid-section .grid-item__image-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
  background-color: var(--overlay-background-color);
}
@media only screen and (max-width: 798px) {
  .section--grid-section .grid-item__desktop-image,
  .section--grid-section .grid-item__desktop-svg {
    display: none;
  }
}
@media only screen and (min-width: 799px) {
  .section--grid-section .grid-item__mobile-image,
  .section--grid-section .grid-item__mobile-svg {
    display: none;
  }
}
.section--grid-section .grid-item__text-content {
  width: var(--text-width);
  display: flex;
  flex-direction: column;
}
@media only screen and (max-width: 798px) {
  .section--grid-section .grid-item__text-content {
    width: var(--mobile-text-width);
  }
}
.section--grid-section .grid-item__heading + .grid-item__text {
  margin-top: 10px;
}
.section--grid-section .grid-item__heading {
  color: var(--heading-color);
  font-size: var(--heading-font-size);
}
@media only screen and (max-width: 798px) {
  .section--grid-section .grid-item__heading {
    font-size: var(--mobile-heading-font-size);
    text-align: var(--mobile-text-align);
  }
}
.section--grid-section .grid-item__text {
  color: var(--text-color);
}
@media only screen and (max-width: 798px) {
  .section--grid-section .grid-item__text {
    text-align: var(--mobile-text-align);
  }
}
.section--grid-section .grid-item__button {
  margin-top: 20px;
  justify-content: var(--justify-content);
}
@media only screen and (max-width: 798px) {
  .section--grid-section .grid-item__button {
    justify-content: var(--mobile-justify-content);
  }
}

.section--grid-section--no-gutter.is-width-wide {
  padding-inline: 0;
}
.section--grid-section--no-gutter.is-width-wide .grid-items-wrapper {
  margin-inline: 0;
}
@media only screen and (max-width: 798px) {
  .section--grid-section--no-gutter .grid-items-wrapper {
    margin-inline: 10px;
    width: auto;
  }
}
@media only screen and (max-width: 798px) {
  .section--grid-section--no-gutter .grid-item {
    margin-inline: 0;
    width: 100%;
  }
}

.section--grid-section:not(.section--grid-section--text-on-image) .grid-item__text-content {
  margin: 20px var(--margin-inline);
}
@media only screen and (max-width: 798px) {
  .section--grid-section:not(.section--grid-section--text-on-image) .grid-item__text-content {
    margin-inline: var(--mobile-margin-inline);
  }
}
.section--grid-section:not(.section--grid-section--text-on-image).section--grid-section--no-gutter .grid-item__text-content {
  margin-bottom: 40px;
}
@media only screen and (max-width: 798px) {
  .section--grid-section:not(.section--grid-section--text-on-image).section--grid-section--mobile-slider .grid-item__text-content {
    margin-bottom: 0;
  }
}
@media only screen and (min-width: 799px) {
  .section--grid-section:not(.section--grid-section--text-on-image) .grid-item--last-row .grid-item__text-content {
    margin-bottom: 0;
  }
}
@media only screen and (max-width: 798px) {
  .section--grid-section:not(.section--grid-section--text-on-image) .grid-item:last-of-type .grid-item__text-content {
    margin-bottom: 0;
  }
}

.section--grid-section--text-on-image .grid-item__text-content-wrapper {
  padding: 20px;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
}
.section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-horizontal-align--left {
  justify-content: flex-start;
}
.section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-horizontal-align--center {
  justify-content: center;
}
.section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-horizontal-align--right {
  justify-content: flex-end;
}
.section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-vertical-align--top {
  align-items: flex-start;
}
.section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-vertical-align--middle {
  align-items: center;
}
.section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-vertical-align--bottom {
  align-items: flex-end;
}
@media only screen and (max-width: 798px) {
  .section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-mobile-horizontal-align--left {
    justify-content: flex-start;
  }
  .section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-mobile-horizontal-align--center {
    justify-content: center;
  }
  .section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-mobile-horizontal-align--right {
    justify-content: flex-end;
  }
  .section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-mobile-vertical-align--top {
    align-items: flex-start;
  }
  .section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-mobile-vertical-align--middle {
    align-items: center;
  }
  .section--grid-section--text-on-image .grid-item__text-content-wrapper.grid-item-mobile-vertical-align--bottom {
    align-items: flex-end;
  }
}

@media only screen and (max-width: 798px) {
  .section--grid-section--show-nav-dots .grid-items-wrapper {
    padding-bottom: 45px;
  }
  .section--grid-section--show-nav-dots .flickity-page-dots {
    bottom: 0;
  }
}

.grid-section__nav-wrapper {
  margin-bottom: 10px;
  padding-inline: 10px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
@media only screen and (max-width: 480px) {
  .grid-section__nav-wrapper {
    justify-content: space-between;
    gap: 0;
  }
}
@media only screen and (min-width: 799px) {
  .grid-section__nav-wrapper {
    display: none;
  }
}
.grid-section__nav-wrapper .grid-section__nav {
  color: {{ link }};
  transition: color 0.3s ease-in-out;
  cursor: pointer;
}
.grid-section__nav-wrapper .grid-section__nav:hover {
  color: {{ settings.link_hover_color }};
}

/* # Header - Centered
================================================== */
{%- if settings.header_layout == 'centered' -%}
.top-bar {
    font-family: {{ settings.nav__font.family }}, {{ settings.nav__font.fallback_families }};
    font-weight: {{ settings.nav__font.weight }};
    font-style: {{ settings.nav__font.style }};
    letter-spacing: {{ settings.nav_letter_spacing | append: 'px' }};
    font-size: {{ settings.nav_font_size | append: 'px' }};
    text-transform: {{ settings.nav_font_style }};
  }
@media only screen and (max-width: 798px) {
    .top-bar {
      display: none;
    }
  }
.top-bar__content {
    width: 100%;
  }
.top-bar__info {
    padding-left: 20px;
  }
.top-bar__social {
    padding-right: 10px;
  }
.top-bar__social .social-icons {
    line-height: 1;
  }
.top-bar__social .icon {
    margin-top: 10px;
    margin-right: 10px;
  }
.top-bar__icons {
    height: 100%;
  }
.top-bar__icons .header-cart {
    padding-left: 0;
    padding-right: 0;
    margin-left: 5px;
  }
.top-bar__icons .header-cart > a {
    text-align: center;
    height: 100%;
    z-index: 50;
    position: relative;
    padding: 0 1em;
    display: flex;
    justify-content: center;
    align-items: center;
  }
[data-enable_overlay=true] {
    position: absolute;
    width: 100%;
  }
[data-enable_overlay=true] .header {
    position: absolute;
    top: 0px;
    width: 100%;
    z-index: 10;
  }
[data-enable_overlay=true] .overlay-logo {
    display: block;
  }
[data-enable_overlay=true] .overlay-logo + .primary-logo {
    display: none;
  }
[data-enable_overlay=true] .overlay-logo + .header__logo-text {
    display: none;
  }
.header {
    position: relative;
    background-color: {{ settings.header_background }};
    transition: background-color 0.3s linear, height 0.2s linear;
    z-index: 70;
  }
@media only screen and (max-width: 798px) {
    .header {
      display: none;
    }
  }
.header-layout--between {
    justify-content: space-between;
  }
.header-layout--above {
    flex-wrap: wrap;
    justify-content: center;
    padding-top: 10px;
    transition: padding 0.2s linear;
  }
.header-layout--above .header__brand {
    display: flex;
    justify-content: center;
    width: 100%;
  }
.header-layout--above .header__menu {
    order: 1;
    width: 100%;
  }
.header__brand {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
  }
.header__logo {
    transition: all 0.1s linear;
  }
.header__logo + .header__logo-text {
    display: none;
  }
.header__menu-items {
    align-content: center;
  }
.header__menu-toggle,
  .header__icons--sticky {
    visibility: hidden;
    opacity: 0;
    position: absolute;
  }
.header__menu {
    width: 50%;
  }
.action-area__link {
    align-items: center;
    justify-content: center;
    height: 100%;
  }
.action-area__link .select:not(.is-multiple), .action-area__link .age-gate__select-wrapper:not(.is-multiple) {
    height: auto;
  }
.header__icon-style-icons-text .header-cart > a {
    padding: 0 2em;
    flex-direction: column;
  }
.header__icon-style-icons-text .action-area__link {
    padding-left: 10px;
    padding-right: 10px;
  }
.header__icon-style-icons-text .action-area__link:last-child {
    padding-left: 0;
  }
.header__icon-style-icons-text .action-area__link.has-cart-count {
    padding: 0 !important;
  }
.header__icon-style-icons-text #currency-convertor {
    font-size: 0.7rem !important;
  }
.sticky-menu-wrapper {
    visibility: hidden;
    opacity: 0;
    max-height: 0;
    transition: visibility 0s linear 0.1s, opacity 0.33s linear, max-height 0.33s linear;
  }
.sticky-header__menu {
    padding: 10px 0;
  }
.header__icons--sticky {
    position: absolute;
    top: 0;
    right: 20px;
    bottom: 0;
    align-items: center;
    z-index: 40;
  }
.header__icons--sticky .icon-caption {
    opacity: 0;
    display: block;
    position: absolute;
  }
.header__icons--sticky .header-cart {
    align-self: stretch;
    width: auto;
    height: auto;
  }
.sticky-menu-wrapper.is-visible {
    visibility: visible;
    opacity: 1;
    transition: visibility 0s linear, opacity 0.33s linear 0.1s, max-height 0.33s linear 0.1s;
    max-height: 100%;
  }
.is-sticky .header__inner-content {
    position: relative;
  }
.is-sticky .header-layout--above {
    padding: 0;
  }
.is-sticky .header__brand {
    justify-content: center;
    width: 100%;
  }
.is-sticky .header__icons {
    display: flex;
    width: 25%;
    visibility: visible;
    opacity: 1;
    transition: visibility 0s linear, opacity 0.33s linear 0.1s;
  }
.is-sticky .header__menu-toggle {
    cursor: pointer;
    margin-left: 10px;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 40;
    visibility: visible;
    height: 100%;
    opacity: 1;
    transition: visibility 0s linear, opacity 0.33s linear 0.1s;
    display: flex;
    align-items: center;
  }
.is-sticky .header__menu-toggle .header__close-menu {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    transition: visibility 0s linear, opacity 0.33s linear 0.1s;
  }
.is-sticky .header__menu-toggle.is-active .header__open-menu {
    opacity: 0;
    visibility: hidden;
    position: absolute;
  }
.is-sticky .header__menu-toggle.is-active .header__close-menu {
    opacity: 1;
    visibility: visible;
    position: relative;
  }
.is-sticky .header__menu {
    display: none;
  }
.header__menu-items {
    height: 100%;
  }
.sticky-menu--show {
    justify-content: center;
    flex-wrap: wrap;
  }
.sticky-menu--show div.header__menu {
    display: block;
  }
/* Centered header currency/language switcher */
.header-menu__disclosure .selectors-form__item:last-child {
    margin-right: 10px;
  }
{%- endif -%}
/* # Header - Classic
================================================== */
{%- if settings.header_layout == 'classic' -%}
.header {
    position: relative;
    z-index: 20;
    background-color: {{ settings.header_background }};
    transition: background-color 0.3s linear, height 0.2s linear;
  }
.header__brand {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    padding: 1rem 0.75rem;
  }
.primary-logo {
    transition: all 0.1s linear;
  }
.primary-logo + .header__logo-text {
    display: none;
  }
.header__logo {
    transition: all 0.3s ease;
  }
.header__menu {
    flex: 1 1 auto;
    display: flex;
    flex-wrap: wrap;
  }
.action-area__link.has-cart-count {
    padding-right: 1.8em;
  }
[data-enable_overlay=false] .overlay-logo {
    display: none;
  }
[data-enable_overlay=true] {
    position: absolute;
    width: 100%;
  }
[data-enable_overlay=true] .header {
    position: absolute;
    top: 0px;
    width: 100%;
    z-index: 10;
  }
[data-enable_overlay=true] .overlay-logo {
    display: block;
  }
[data-enable_overlay=true] .overlay-logo + .primary-logo {
    display: none;
  }
[data-enable_overlay=true] .overlay-logo + .header__logo-text {
    display: none;
  }
[data-enable_sticky=true] .header {
    z-index: 50;
  }
.has-overlaid-header {
    position: absolute;
    width: 100%;
  }
{%- endif -%}
/* # Header - Search focus
================================================== */
{%- if settings.header_layout == 'search_focus' -%}
.navbar {
    flex-wrap: wrap;
  }
.header {
    position: relative;
    z-index: 70;
    background-color: {{ settings.header_background }};
    transition: background-color 0.3s linear, height 0.2s linear;
  }
@media only screen and (max-width: 798px) {
    .header {
      display: none;
    }
  }
.header__menu {
    width: 100%;
  }
.header__inner-content {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    padding: 1.5rem 0.75rem 0.5rem;
    width: 100%;
  }
.header__inner-content .header__currency-dropdown {
    overflow: visible;
  }
.header__logo {
    transition: all 0.1s linear;
    display: block;
  }
.header__logo + .header__logo-text {
    display: none;
  }
.header__search {
    flex: 1;
    padding: 0 2rem;
  }
.header__search .field {
    margin-bottom: 0;
  }
.header__search-bar {
    width: 100%;
  }
.header__search-bar input {
    padding: 20px 18px;
    border-radius: 0;
  }
.header__search-button {
    height: 100%;
    border-radius: 0;
  }
.header__menu-items {
    height: 100%;
    position: relative;
  }
.header-cart {
    position: relative;
  }
@media only screen and (max-width: 798px) {
    .header-cart {
      position: static;
    }
  }
.action-area__link.has-cart-count {
    padding-right: 1.8em;
  }
.header__menu-toggle {
    visibility: hidden;
    opacity: 0;
    position: absolute;
  }
.is-sticky .sticky-menu-wrapper {
    visibility: hidden;
    opacity: 0;
    max-height: 0;
    transition: visibility 0s linear 0.1s, opacity 0.33s linear, max-height 0.33s linear;
  }
.sticky-header__menu {
    padding: 10px 0;
  }
.sticky-menu-wrapper.is-visible {
    visibility: visible;
    opacity: 1;
    transition: visibility 0s linear, opacity 0.33s linear 0.1s, max-height 0.33s linear 0.1s;
    max-height: 100%;
  }
.is-sticky .header__inner-content {
    padding-top: 0.5rem;
  }
.is-sticky .header__menu-toggle {
    cursor: pointer;
    margin-right: 2rem;
    visibility: visible;
    position: relative;
    height: 100%;
    opacity: 1;
    transition: visibility 0s linear, opacity 0.33s linear 0.1s;
    display: flex;
    align-items: center;
  }
.is-sticky .header__menu-toggle .header__close-menu {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    transition: visibility 0s linear, opacity 0.33s linear 0.1s;
  }
.is-sticky .header__menu-toggle.is-active .header__open-menu {
    opacity: 0;
    visibility: hidden;
    position: absolute;
  }
.is-sticky .header__menu-toggle.is-active .header__close-menu {
    opacity: 1;
    visibility: visible;
    position: relative;
  }
{%- endif -%}
/* # Header - Vertical
================================================== */
{%- if settings.header_layout == 'vertical' -%}
.has-vertical-header {
    display: flex;
    flex-wrap: wrap;
    overflow-y: hidden;
  }
@media only screen and (min-width: 799px) {
    .has-vertical-header {
      display: grid;
      grid-template-areas: "header announcement" "header main-content" "header additional";
    }
  }
.has-vertical-header .is-beside-vertical-header ~ * {
    grid-area: additional;
  }
.is-beside-vertical-header {
    grid-area: main-content;
  }
@media only screen and (max-width: 798px) {
    .is-beside-vertical-header {
      width: 100%;
    }
  }
.header--vertical {
    position: relative;
    z-index: 100;
    grid-area: header;
    color: {{ settings.header_link_color }};
    background: {{ settings.header_background }};
  }
.header--vertical .block__icons {
    position: relative;
  }
.header--vertical .sidebar-block__heading {
    color: {{ settings.header_link_color }};
  }
.header--vertical .sidebar-block__content {
    padding-top: 0.9rem;
  }
.header--vertical a {
    color: {{ settings.header_link_color }};
  }
.header--vertical a:hover {
    color: {{ settings.header_link_hover_color }};
  }
@media only screen and (max-width: 798px) {
    .vertical-header__content {
      display: none;
    }
  }
.vertical-header__content .header__icons.header__icons--left {
    justify-content: flex-start;
  }
.vertical-header__content .header__icons.header__icons--center {
    justify-content: center;
  }
.vertical-header__menu-items {
    padding: 1rem 0 0;
  }
.vertical-header__block:first-child {
    padding-top: 20px;
  }
.header__logo {
    display: block;
  }
.header__logo-text {
    word-break: break-word;
  }
.block__social-media .icon {
    margin: 0 5px;
  }
.action-area__link.has-cart-count {
    padding-right: 1.8em;
  }
.vertical-header__block:not(.block__navigation) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
.vertical-header__first-level {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
.vertical-header__first-level .navbar-dropdown {
    left: 100%;
    top: 0;
    width: 22vw;
    padding: 1rem 0.8em;
    max-width: 300px;
    min-width: 200px;
  }
.vertical-header__first-level .vertical-header__first-level-link::after, .vertical-header__first-level.has-dropdown:hover .vertical-header__first-level-link::after, .vertical-header__first-level.has-mega-menu:hover .vertical-header__first-level-link::after, .vertical-header__first-level.has-dropdown.is-opened .vertical-header__first-level-link::after, .vertical-header__first-level.has-mega-menu.is-opened .vertical-header__first-level-link::after {
    transform: translateY(-110%) rotate(-90deg);
  }
.menu-alignment--center .vertical-header__first-level {
    padding-left: 0;
    padding-right: 0;
  }
.menu-alignment--center .vertical-header__first-level .vertical-header__first-level-link {
    padding-left: 2.5em;
    padding-right: 2.5em;
  }
.vertical-header__first-level-link {
    padding-left: 0;
    width: 100%;
    padding-top: 0;
    padding-bottom: 1rem;
  }
.vertical-header__first-level-link::after {
    transition: all 0s;
  }
.vertical-header__first-level-link.mobile-menu-link {
    padding: 0 0 1rem;
  }
.navbar-dropdown--fix-offscreen {
    top: -100% !important;
  }
@media only screen and (min-width: 799px) {
    .mega-menu--header-vertical.mega-menu {
      top: 0;
      left: 100%;
      bottom: 0;
      width: 22vw;
      min-height: 100vh;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
    .mega-menu--header-vertical.mega-menu .mega-menu__block {
      width: 100%;
    }
  }
/* Vertical header currency/language switcher */
.vertical-header__disclosure .selectors-wrap {
    justify-content: flex-start;
  }
.vertical-header__disclosure .disclosure__toggle,
  .vertical-header__disclosure .disclosure__button {
    color: {{ settings.regular_color }};
  }
.vertical-header__disclosure .disclosure__toggle:hover, .vertical-header__disclosure .disclosure__toggle[aria-current=true],
  .vertical-header__disclosure .disclosure__button:hover,
  .vertical-header__disclosure .disclosure__button[aria-current=true] {
    color: {{ settings.regular_color | color_modify: 'alpha', 0.33 }};
  }
.vertical-header__disclosure .disclosure__list-wrap {
    top: initial;
  }
.no-js .mega-menu-section {
    position: absolute;
    left: 0;
    width: 20%;
    height: 100vh;
    top: 0;
  }
.no-js .mega-menu__section {
    height: 100vh;
    top: 0;
    z-index: 200;
    left: 0;
  }
.no-js .mega-menu--header-vertical.mega-menu {
    width: 20vw;
  }
{%- endif -%}
/* # Header
================================================== */
@media only screen and (max-width: 798px) {
  header.header {
    display: none;
  }
}

.header,
.mobile-header {
  font-family: {{ settings.nav__font.family }}, {{ settings.nav__font.fallback_families }};
  font-weight: {{ settings.nav__font.weight }};
  font-style: {{ settings.nav__font.style }};
  letter-spacing: {{ settings.nav_letter_spacing | append: 'px' }};
  font-size: {{ settings.nav_font_size | append: 'px' }};
  text-transform: {{ settings.nav_font_style }};
}

.dropdown-style-horizontal {
  position: relative;
}

.dropdown-style-horizontal .navbar,
.dropdown-style-horizontal .navbar-item {
  position: initial;
}

.dropdown-style-horizontal .header__menu-items,
.header__dropdown--below-header.header__menu-items {
  height: 100%;
}
.dropdown-style-horizontal .header__menu-items .navbar-item.has-dropdown,
.header__dropdown--below-header.header__menu-items .navbar-item.has-dropdown {
  align-self: stretch;
}

.vertical-alignment-center .header__item {
  align-items: center;
}
.vertical-alignment-top .header__item {
  align-items: flex-start;
}
.vertical-alignment-bottom .header__item {
  align-items: flex-end;
}

.header__link,
.header__link > a {
  color: {{ settings.header_link_color }};
  fill: {{ settings.header_link_color }};
}
.header__link:hover, .header__link.is-active,
.header__link > a:hover,
.header__link > a.is-active {
  color: {{ settings.header_link_hover_color }};
  fill: {{ settings.header_link_hover_color }};
}

.header__logo-text {
  font-family: {{ settings.logo__font.family }}, {{ settings.logo__font.fallback_families }};
  font-weight: {{ settings.logo__font.weight }};
  font-style: {{ settings.logo__font.style }};
  font-size: {{ settings.logo_font_size | append: 'px' }};
  text-transform: {{ settings.logo_font_style }};
}

.header__logo-text,
h1.header__logo-text > a,
.header__logo-text.age-gate__heading > a {
  color: {{ settings.logo_text }};
}
.header__logo-text:hover,
h1.header__logo-text > a:hover,
.header__logo-text.age-gate__heading > a:hover {
  color: {{ settings.logo_text }};
}

.header__icons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.header__icon-style-text .header__icon,
.header__icon-style-text .header-cart__count--badge {
  display: none;
}

.header__icon-style-icons .icon-caption {
  display: none;
}

.header__icon-style-icons-text {
  text-transform: uppercase;
}
.header__icon-style-icons-text .select, .header__icon-style-icons-text .age-gate__select-wrapper {
  height: auto;
}
.header__icon-style-icons-text .icon-caption {
  padding: 0.3em 0 0;
  font-size: 0.7em;
}
.header__icon-style-icons-text .action-area__link.has-cart-count {
  padding-right: 1.8em;
}
.header__icon-style-icons-text .header-cart__caption {
  display: block;
}

.action-area__link:not(.has-cart-count) .header-cart__count {
  display: none;
}

.action-area__link {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 5px;
  height: 100%;
}
.action-area__link:last-child {
  padding-right: 0;
}
.vertical-alignment-center .action-area__link {
  justify-content: center;
}
.vertical-alignment-top .action-area__link {
  justify-content: flex-start;
}
.vertical-alignment-bottom .action-area__link {
  justify-content: flex-end;
}

.has-submenu label {
  display: block;
  position: relative;
}
.has-submenu .close-dropdown,
.has-submenu .close-sub-dropdown {
  position: absolute;
  cursor: pointer;
  top: 0;
  right: 0;
  height: 100%;
  width: 25%;
}
.has-submenu .has-no-link .close-dropdown,
.has-submenu .has-no-link .close-sub-dropdown {
  width: 100%;
}

[data-enable_overlay=false] .overlay-logo__content {
  display: none;
}
[data-enable_overlay=false] .logo__content {
  display: block;
}

[data-enable_overlay=true] .overlay-logo__content {
  display: block;
}
[data-enable_overlay=true] .logo__content {
  display: none;
}

.header__logo:hover img {
  opacity: 0.7;
}
.header__logo img {
  transition: opacity 0.3s ease-in;
}

/* Header currency/language switcher */
@media only screen and (min-width: 799px) and (max-width: 1024px) {
  .header-menu__disclosure .selectors-form__wrap {
    margin-bottom: 0;
  }
}
.header-menu__disclosure .disclosure {
  padding: 0.5rem 0;
}
.header-menu__disclosure .disclosure__toggle {
  margin-top: 0;
}
.header-menu__disclosure .disclosure__list-wrap {
  top: 100%;
  padding-top: 0;
  padding-bottom: 0;
  position: absolute;
  transform: translateY(-1px);
}

.no-js-main-navigation {
  display: none;
}

.no-js .mega-menu-section {
  display: block;
}
.no-js .mega-menu {
  position: relative;
}
.no-js a.header__link[href="#"] {
  pointer-events: none;
  cursor: pointer;
}
.no-js div.has-dropdown label a.navbar-link.header__link {
  pointer-events: none;
  cursor: pointer;
}
.no-js .no-js-main-navigation:checked + .mega-menu {
  display: block !important;
}
.no-js .no-js-main-navigation:checked + .navbar-dropdown {
  display: block !important;
  opacity: 1;
  visibility: visible;
}
.no-js .navbar-link:not(.is-arrowless)::after {
  display: none !important;
}
.no-js a.navbar-link.header__link:not(.is-arrowless) {
  padding-right: 0.75rem !important;
}
.no-js .navbar-dropdown {
  display: none !important;
}

/* # Heading
================================================== */
.heading-section__preheading {
  font-family: {{ settings.preheading_font.family }}, {{ settings.preheading_font.fallback_families }};
  font-size: {{ settings.preheading_size | append: 'px' }};
  font-weight: {{ settings.preheading_font.weight }};
  font-style: {{ settings.preheading_font.style }};
  text-transform: {{ settings.preheading_style }};
}

.heading-section__subheading {
  font-family: {{ settings.subheading_font.family }}, {{ settings.subheading_font.fallback_families }};
  font-size: {{ settings.subheading_size | append: 'px' }};
  font-weight: {{ settings.subheading_font.weight }};
  font-style: {{ settings.subheading_font.style }};
  text-transform: {{ settings.subheading_style }};
}

.heading-section__content {
  padding: 1.5rem 0;
}

.vertical-spacing-medium .heading-section__preheading + .heading-section__heading {
  padding-top: 10px;
}
.vertical-spacing-medium .heading-section__preheading + .heading-section__subheading {
  padding-top: 10px;
}
.vertical-spacing-medium .heading-section__heading + .heading-section__subheading {
  padding-top: 10px;
}
.vertical-spacing-medium .heading-divider {
  margin-top: 10px;
}

.vertical-spacing-large .heading-section__preheading + .heading-section__heading {
  padding-top: 20px;
}
.vertical-spacing-large .heading-section__preheading + .heading-section__subheading {
  padding-top: 20px;
}
.vertical-spacing-large .heading-section__heading + .heading-section__subheading {
  padding-top: 20px;
}
.vertical-spacing-large .heading-divider {
  margin-top: 20px;
}

.heading-wrapper {
  margin-bottom: 40px;
}
@media only screen and (max-width: 798px) {
  .heading-wrapper {
    margin-bottom: 20px;
  }
}
.heading-wrapper.text-align-center .heading-divider {
  margin: 0 auto;
}
.heading-wrapper.text-align-right .heading-divider {
  margin: 0 0 0 auto;
}

@media only screen and (min-width: 799px) {
  .has-heading-divider-below .heading-wrapper:not(.heading-divider-below) {
    margin-bottom: 0;
  }
  .has-heading-divider-below .heading-wrapper:not(.heading-divider-below) .heading-divider {
    display: none;
  }
}

@media only screen and (max-width: 798px) {
  .heading-divider-below {
    display: none;
  }
}

/* # Icon bar
================================================== */
.icon-bar__block {
  padding: 0.5rem 0.75rem;
}
@media only screen and (min-width: 799px) {
  .icon-bar__block:first-child {
    padding-left: 0;
  }
  .icon-bar__block:last-child {
    padding-right: 0;
  }
}
.icon-bar__block.is-small {
  font-size: 0.75rem;
}
.icon-bar__block.is-medium {
  font-size: {{ settings.regular_font_size | append: 'px' }};
}
.icon-bar__block.is-large {
  font-size: 1.5rem;
}
.icon-bar__block .icon-bar__text {
  padding: 0 5px;
}

/* # Icon with text column
================================================== */
.icon-with-text-column .icon {
  margin-bottom: 20px;
}
@media only screen and (max-width: 798px) {
  .icon-with-text-column .icon {
    margin: 20px 0 0;
  }
}
.icon-with-text-column .icon.is-small {
  width: 2.8rem;
  height: 2.8rem;
}
.icon-with-text-column .icon.is-medium {
  width: 3.6rem;
  height: 3.6rem;
}
.icon-with-text-column .icon.is-large {
  width: 5.4rem;
  height: 5.4rem;
}
@media only screen and (max-width: 798px) {
  .icon-with-text-column .icon.is--mobile-small {
    width: 2.8rem;
    height: 2.8rem;
  }
}
@media only screen and (max-width: 798px) {
  .icon-with-text-column .icon.is--mobile-medium {
    width: 3.6rem;
    height: 3.6rem;
  }
}
@media only screen and (max-width: 798px) {
  .icon-with-text-column .icon.is--mobile-large {
    width: 5.4rem;
    height: 5.4rem;
  }
}

/* # Image with text
================================================== */
@media only screen and (max-width: 798px) {
  .image-with-text .has-gutter-enabled .container {
    gap: 20px;
  }
}

.image-with-text__image-column .image-element__wrap {
  width: 100%;
}
@media only screen and (max-width: 798px) {
  .image-with-text__image-column {
    order: var(--image-flex-order);
    height: 100vw;
  }
}

.image-with-text__text-column {
  padding: 30px;
}
.image-with-text__text-column.image-with-text__alignment-left {
  justify-content: flex-start;
}
.image-with-text__text-column.image-with-text__alignment-center {
  justify-content: center;
}
.image-with-text__text-column.image-with-text__alignment-right {
  justify-content: flex-end;
}
@media only screen and (max-width: 798px) {
  .image-with-text__text-column {
    order: var(--text-flex-order);
  }
  .image-with-text__text-column.image-with-text__mobile-alignment-left {
    justify-content: flex-start;
  }
  .image-with-text__text-column.image-with-text__mobile-alignment-center {
    justify-content: center;
  }
  .image-with-text__text-column.image-with-text__mobile-alignment-right {
    justify-content: flex-end;
  }
}

.image-with-text__heading {
  padding-bottom: 20px;
  line-height: 1.2;
}
.image-with-text__heading.is-small {
  font-size: {{ settings.heading_size | times: 0.8 | floor | append: 'px' }};
}
.image-with-text__heading.is-regular {
  font-size: {{ settings.heading_size | times: 1.0 | floor | append: 'px' }};
}
.image-with-text__heading.is-large {
  font-size: {{ settings.heading_size | times: 1.5 | floor | append: 'px' }};
}

.image-with-text__button--text-align-left {
  justify-content: flex-start;
}
.image-with-text__button--text-align-center {
  justify-content: center;
}
.image-with-text__button--text-align-right {
  justify-content: flex-end;
}
@media only screen and (max-width: 798px) {
  .image-with-text__button--mobile-text-align-left {
    justify-content: flex-start;
  }
  .image-with-text__button--mobile-text-align-center {
    justify-content: center;
  }
  .image-with-text__button--mobile-text-align-right {
    justify-content: flex-end;
  }
}

.image-with-text__link {
  width: 100%;
  height: 100%;
  display: block;
  overflow: hidden;
}

.image-with-text__link:hover .image-with-text__image,
.image-with-text__link:hover svg {
  transform: scale(1.1);
}

.image-with-text__image,
.image-with-text__link svg {
  transition: transform 0.3s ease-in-out;
}

@media only screen and (max-width: 798px) {
  .block__image-with-text .has-gutter-enabled .image-with-text__column {
    margin-bottom: 20px;
  }
}

/* # Image with text overlay
================================================== */
.block__image-with-overlay .image-with-text-overlay__container,
.image-with-text-overlay .image-with-text-overlay__container,
.image-slideshow .image-with-text-overlay__container {
  width: 100%;
}
.block__image-with-overlay .caption-content,
.image-with-text-overlay .caption-content,
.image-slideshow .caption-content {
  padding: 30px;
  min-width: 350px;
}
@media only screen and (max-width: 480px) {
  .block__image-with-overlay .caption-content,
  .image-with-text-overlay .caption-content,
  .image-slideshow .caption-content {
    min-width: auto;
  }
}
@media only screen and (max-width: 480px) {
  .block__image-with-overlay .mobile-text--below-media > .container,
  .image-with-text-overlay .mobile-text--below-media > .container,
  .image-slideshow .mobile-text--below-media > .container {
    display: block;
  }
  .block__image-with-overlay .mobile-text--below-media .caption,
  .image-with-text-overlay .mobile-text--below-media .caption,
  .image-slideshow .mobile-text--below-media .caption {
    position: static;
    padding: 0;
    margin: 20px 0;
    text-align: center;
  }
  .block__image-with-overlay .mobile-text--below-media .caption-content,
  .image-with-text-overlay .mobile-text--below-media .caption-content,
  .image-slideshow .mobile-text--below-media .caption-content {
    padding: 20px 0;
  }
  .block__image-with-overlay .mobile-text--below-media.has-background .caption-content, .block__image-with-overlay .mobile-text--below-media.has-border .caption-content,
  .image-with-text-overlay .mobile-text--below-media.has-background .caption-content,
  .image-with-text-overlay .mobile-text--below-media.has-border .caption-content,
  .image-slideshow .mobile-text--below-media.has-background .caption-content,
  .image-slideshow .mobile-text--below-media.has-border .caption-content {
    padding: 30px;
  }
}
@media only screen and (max-width: 1024px) {
  .block__image-with-overlay .mobile-text--below-media .is-width-half .caption,
  .image-with-text-overlay .mobile-text--below-media .is-width-half .caption,
  .image-slideshow .mobile-text--below-media .is-width-half .caption {
    position: static;
    text-align: center;
    margin-top: 20px;
    margin-bottom: 20px;
    top: 0;
    transform: none;
  }
  .block__image-with-overlay .mobile-text--below-media .is-width-half .caption-content,
  .image-with-text-overlay .mobile-text--below-media .is-width-half .caption-content,
  .image-slideshow .mobile-text--below-media .is-width-half .caption-content {
    padding: 20px 0;
    width: 100%;
  }
}
@media only screen and (max-width: 480px) {
  .block__image-with-overlay .mobile-text--over-media .caption,
  .image-with-text-overlay .mobile-text--over-media .caption,
  .image-slideshow .mobile-text--over-media .caption {
    top: 50%;
    bottom: auto;
    transform: translate(0, -50%);
  }
  .block__image-with-overlay .mobile-text--over-media .caption .caption-content,
  .image-with-text-overlay .mobile-text--over-media .caption .caption-content,
  .image-slideshow .mobile-text--over-media .caption .caption-content {
    width: calc(100% - 20px);
  }
}
.block__image-with-overlay .pretext,
.block__image-with-overlay .subtitle,
.image-with-text-overlay .pretext,
.image-with-text-overlay .subtitle,
.image-slideshow .pretext,
.image-slideshow .subtitle {
  font-size: {{ settings.banner_text_size | append: 'px' }};
  font-family: {{ settings.banner_text__font.family }}, {{ settings.regular__font.fallback_families }};
  font-weight: {{ settings.banner_text__font.weight }};
  letter-spacing: {{ settings.banner_text_letter_spacing | append: 'px' }};
}
.block__image-with-overlay .subtitle.image-with-text-overlay__subheading,
.image-with-text-overlay .subtitle.image-with-text-overlay__subheading,
.image-slideshow .subtitle.image-with-text-overlay__subheading {
  padding-top: 10px;
}
.block__image-with-overlay .title,
.image-with-text-overlay .title,
.image-slideshow .title {
  padding-top: 10px;
}
.block__image-with-overlay .image-with-text-overlay__banner,
.image-with-text-overlay .image-with-text-overlay__banner,
.image-slideshow .image-with-text-overlay__banner {
  overflow: hidden;
  position: relative;
}
@media only screen and (max-width: 798px) {
  .block__image-with-overlay .image-with-text-overlay__banner,
  .image-with-text-overlay .image-with-text-overlay__banner,
  .image-slideshow .image-with-text-overlay__banner {
    overflow: visible;
  }
}
.block__image-with-overlay .image-with-text-overlay__banner h1.title, .block__image-with-overlay .image-with-text-overlay__banner .title.age-gate__heading,
.image-with-text-overlay .image-with-text-overlay__banner h1.title,
.image-with-text-overlay .image-with-text-overlay__banner .title.age-gate__heading,
.image-slideshow .image-with-text-overlay__banner h1.title,
.image-slideshow .image-with-text-overlay__banner .title.age-gate__heading {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | times: 2.0 | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 798px) {
  .block__image-with-overlay .image-with-text-overlay__banner h1.title, .block__image-with-overlay .image-with-text-overlay__banner .title.age-gate__heading,
  .image-with-text-overlay .image-with-text-overlay__banner h1.title,
  .image-with-text-overlay .image-with-text-overlay__banner .title.age-gate__heading,
  .image-slideshow .image-with-text-overlay__banner h1.title,
  .image-slideshow .image-with-text-overlay__banner .title.age-gate__heading {
    font-size: {{ settings.heading_size | times: 2.0 | times: 0.8 | floor | append: 'px' }};
  }
}
.block__image-with-overlay .image-with-text-overlay__banner h1.title > a, .block__image-with-overlay .image-with-text-overlay__banner .title.age-gate__heading > a,
.block__image-with-overlay .image-with-text-overlay__banner h1.title > a:link,
.block__image-with-overlay .image-with-text-overlay__banner h1.title > a:visited,
.image-with-text-overlay .image-with-text-overlay__banner h1.title > a,
.image-with-text-overlay .image-with-text-overlay__banner .title.age-gate__heading > a,
.image-with-text-overlay .image-with-text-overlay__banner h1.title > a:link,
.image-with-text-overlay .image-with-text-overlay__banner h1.title > a:visited,
.image-slideshow .image-with-text-overlay__banner h1.title > a,
.image-slideshow .image-with-text-overlay__banner .title.age-gate__heading > a,
.image-slideshow .image-with-text-overlay__banner h1.title > a:link,
.image-slideshow .image-with-text-overlay__banner h1.title > a:visited {
  color: {{ settings.heading_color }};
}
.block__image-with-overlay .image-with-text-overlay__banner h1.title > a:hover, .block__image-with-overlay .image-with-text-overlay__banner .title.age-gate__heading > a:hover,
.block__image-with-overlay .image-with-text-overlay__banner h1.title > a:focus,
.block__image-with-overlay .image-with-text-overlay__banner .title.age-gate__heading > a:focus,
.image-with-text-overlay .image-with-text-overlay__banner h1.title > a:hover,
.image-with-text-overlay .image-with-text-overlay__banner .title.age-gate__heading > a:hover,
.image-with-text-overlay .image-with-text-overlay__banner h1.title > a:focus,
.image-with-text-overlay .image-with-text-overlay__banner .title.age-gate__heading > a:focus,
.image-slideshow .image-with-text-overlay__banner h1.title > a:hover,
.image-slideshow .image-with-text-overlay__banner .title.age-gate__heading > a:hover,
.image-slideshow .image-with-text-overlay__banner h1.title > a:focus,
.image-slideshow .image-with-text-overlay__banner .title.age-gate__heading > a:focus {
  color: {{ settings.link_hover_color }};
}
.block__image-with-overlay .image-with-text-overlay__banner .placeholder-svg,
.image-with-text-overlay .image-with-text-overlay__banner .placeholder-svg,
.image-slideshow .image-with-text-overlay__banner .placeholder-svg {
  min-height: 400px;
}
.block__image-with-overlay .caption,
.image-with-text-overlay .caption,
.image-slideshow .caption {
  position: absolute;
  top: 50%;
  display: block;
  width: 100%;
  padding: 1%;
  pointer-events: none;
}
@media only screen and (min-width: 481px) {
  .block__image-with-overlay .caption,
  .image-with-text-overlay .caption,
  .image-slideshow .caption {
    transform: translateY(-50%);
  }
}
@media only screen and (min-width: 481px) {
  .block__image-with-overlay .caption.align-middle,
  .image-with-text-overlay .caption.align-middle,
  .image-slideshow .caption.align-middle {
    top: 50%;
    transform: translate(0, -50%);
  }
}
.block__image-with-overlay .caption.align-top,
.image-with-text-overlay .caption.align-top,
.image-slideshow .caption.align-top {
  top: 0;
  transform: none;
}
.block__image-with-overlay .caption.align-bottom,
.image-with-text-overlay .caption.align-bottom,
.image-slideshow .caption.align-bottom {
  top: auto;
  bottom: 0;
  transform: none;
}
.block__image-with-overlay .caption-content,
.image-with-text-overlay .caption-content,
.image-slideshow .caption-content {
  pointer-events: all;
  display: inline-block;
}
.block__image-with-overlay .caption-overlay-true,
.image-with-text-overlay .caption-overlay-true,
.image-slideshow .caption-overlay-true {
  z-index: 3;
}
.block__image-with-overlay .dark-overlay-true:after,
.image-with-text-overlay .dark-overlay-true:after,
.image-slideshow .dark-overlay-true:after {
  content: "";
  position: absolute;
  z-index: 2;
  background: rgba(0, 0, 0, 0.5);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.block__image-with-overlay .banner--full-link,
.image-with-text-overlay .banner--full-link,
.image-slideshow .banner--full-link {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  font-size: 0;
  line-height: 0;
}

.image-with-text-overlay__buttons {
  justify-content: space-between;
}
.image-with-text-overlay__buttons.are-small {
  max-width: 300px;
}
.image-with-text-overlay__buttons.are-regular {
  max-width: 400px;
}
.image-with-text-overlay__buttons.are-large {
  max-width: 500px;
}
.image-with-text-overlay__buttons.is-justify-left {
  justify-content: flex-start;
}
.image-with-text-overlay__buttons.is-justify-right {
  justify-content: flex-end;
}
.image-with-text-overlay__buttons.is-justify-center {
  justify-content: center;
}
.image-with-text-overlay__buttons .button, .image-with-text-overlay__buttons .age-gate__confirm_btn {
  flex: 0 0 calc(50% - 6px);
  margin-bottom: 12px;
  white-space: normal;
  margin-left: 0;
  margin-right: 0;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .image-with-text-overlay__buttons .button, .image-with-text-overlay__buttons .age-gate__confirm_btn {
    width: 100% !important;
    max-width: calc(50% - 6px) !important;
    margin: 0px auto;
  }
}
@media only screen and (max-width: 480px) {
  .image-with-text-overlay__buttons .button, .image-with-text-overlay__buttons .age-gate__confirm_btn {
    flex: 1 0 100%;
  }
}

/* # List of collections page
================================================== */
.list-collection-wrapper .list-collection__thumbnail {
  display: block;
  margin-bottom: 30px;
}

.collection-list .has-no-side-gutter .list-collection__thumbnail {
  margin-bottom: 0;
}

.list-collection__thumbnail {
  overflow: hidden;
}
.list-collection__thumbnail .product-wrap {
  position: relative;
  overflow: hidden;
}
.list-collection__thumbnail .product-wrap.enable-zoom-true:hover img, .list-collection__thumbnail .product-wrap.enable-zoom-true:hover svg {
  transform: scale(1.1);
}
.list-collection__thumbnail img,
.list-collection__thumbnail svg {
  transition: all 0.3s ease-in-out;
}

.collection-thumbnail-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
}

.collection-info__caption {
  display: flex;
  align-items: flex-end;
  flex-wrap: wrap;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 20px;
  z-index: 2;
}
.collection-info__caption .title {
  display: block;
  margin-bottom: 20px;
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
  font-size: {{ settings.heading_size | append: 'px' }};
  text-transform: {{ settings.heading_font_style }};
  line-height: 1.5;
  color: {{ settings.heading_color }};
  display: block;
  letter-spacing: {{ settings.heading_letter_spacing | append: 'px' }};
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 798px) {
  .collection-info__caption .title {
    font-size: {{ settings.heading_size | times: 0.8 | floor | append: 'px' }};
  }
}
.collection-info__caption .title > a,
.collection-info__caption .title > a:link,
.collection-info__caption .title > a:visited {
  color: {{ settings.heading_color }};
}
.collection-info__caption .title > a:hover,
.collection-info__caption .title > a:focus {
  color: {{ settings.link_hover_color }};
}

.collection-info__caption--below-image {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.collection-info__caption--below-image .title {
  text-align: center;
}

/* # Logo list
================================================== */
.logo-list .has-background {
  padding-top: 20px;
}
@media only screen and (max-width: 480px) {
  .logo-list .logo-list__wrapper {
    max-width: 1200px;
    width: 95%;
  }
}

/* # Map
================================================== */
.maps {
  width: 100%;
}

.map {
  height: 100%;
  width: 100%;
}

/* # Mobile Header
================================================== */
.mobile-menu--opened {
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
  height: 100vh;
}

#mobile-header .mobile-header__logo {
  width: 100%;
}

.mobile-header {
  position: relative;
  z-index: 10;
  width: 100%;
  background-color: {{ settings.header_background }};
  transition: background-color 0.3s linear, height 0.2s linear;
}
@media only screen and (min-width: 799px) {
  .mobile-header {
    display: none;
  }
}

.mobile-header__content {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  z-index: 10;
}
@media only screen and (max-width: 798px) {
  .mobile-header__content {
    padding: 0 20px;
  }
}
@media only screen and (max-width: 480px) {
  .mobile-header__content {
    padding: 0 10px;
  }
}

.mobile-menu__toggle-icon {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: visibility 0s linear, opacity 0.33s linear 0.1s;
  padding: 10px 5px 10px 0;
}
.mobile-menu__toggle-icon .icon {
  fill: {{ settings.header_link_color }};
}
.mobile-menu__toggle-icon .mobile-header__open-menu {
  transition: visibility 0s linear, opacity 0.33s linear 0.1s;
}
.mobile-menu__toggle-icon .mobile-header__close-menu {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  transition: visibility 0s linear, opacity 0.33s linear 0.1s;
}
.mobile-menu__toggle-icon.is-active .mobile-header__open-menu {
  opacity: 0;
  visibility: hidden;
  position: absolute;
}
.mobile-menu__toggle-icon.is-active .mobile-header__close-menu {
  opacity: 1;
  visibility: visible;
  position: relative;
}

.mobile-layout--left .mobile-logo {
  width: calc(50% - 10px);
}
.mobile-layout--left .mobile-dropdown,
.mobile-layout--left .mobile-icons {
  width: calc(25% - 10px);
}
.mobile-layout--left .mobile-icons {
  justify-content: flex-end;
}
.mobile-layout--left.mobile-logo__position--left .mobile-dropdown,
.mobile-layout--left.mobile-logo__position--left .mobile-icons {
  width: auto;
}

.mobile-logo,
.mobile-dropdown,
.mobile-icons {
  display: flex;
  align-items: center;
  padding: 0 5px;
}

.mobile-icons {
  order: 2;
}

.mobile-dropdown {
  order: 0;
}

.mobile-logo {
  order: 1;
  justify-content: center;
  flex-grow: 1;
  text-align: center;
  padding-top: 5px;
  padding-bottom: 5px;
}
.mobile-logo .image-element__wrap {
  max-width: {{ settings.mobile_logo_width | append: 'px' }} !important;
}

.mobile-logo__position--left .mobile-logo {
  order: 0;
  justify-content: left;
  text-align: left;
}
.mobile-logo__position--left .mobile-logo .image-element__wrap {
  margin-left: 0;
}

.mobile-layout--left.mobile-logo__position--below {
  flex-wrap: wrap;
}
.mobile-layout--left.mobile-logo__position--below .mobile-dropdown,
.mobile-layout--left.mobile-logo__position--below .mobile-icons {
  width: 50%;
}
.mobile-layout--left.mobile-logo__position--below .mobile-logo {
  display: none;
}

.mobile-layout--right .action-area__link.has-cart-count {
  padding-right: 5px;
}

.mobile-layout--right.mobile-logo__position--below {
  flex-wrap: wrap;
}
.mobile-layout--right.mobile-logo__position--below .mobile-icons {
  flex-grow: 1;
  flex-wrap: wrap;
  justify-content: flex-end;
}
.mobile-layout--right.mobile-logo__position--below .mobile-logo {
  display: none;
}

.mobile-dropdown__position--left {
  order: 0;
}

.mobile-dropdown__position--right {
  order: 3;
  padding-top: 5px;
}

.mobile-logo__outer {
  justify-content: center;
  display: flex;
  background-color: {{ color_background }};
  padding: 10px 0;
  width: 100%;
}
.mobile-logo__outer a {
  color: {{ settings.regular_color }};
}
.mobile-logo__outer .image-element__wrap {
  max-width: {{ settings.mobile_logo_width | append: 'px' }} !important;
}

.mobile-menu a {
  color: {{ settings.header_link_color }};
}
.mobile-menu a:hover {
  color: {{ settings.header_link_hover_color }};
}

.submenu__label:hover a {
  color: {{ settings.dropdown_link_hover_color }};
}

.mobile-menu__submenu {
  font-size: {{ settings.dropdown_font_size | append: 'px' }};
  letter-spacing: {{ settings.dropdown_letter_spacing | append: 'px' }};
  text-transform: {{ settings.dropdown_font_style }};
}

.mobile-menu__toggle-icon.is-active + .mobile-menu {
  transition: left 0.5s;
  left: 0;
}

.mobile-menu {
  max-height: 100%;
  min-height: 100vh;
  left: -100%;
  position: absolute;
  top: 100%;
  transition: left 0.3s ease-in-out;
  width: 100%;
  z-index: 40;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.mobile-menu .mobile-menu__item {
  align-items: center;
}
.mobile-menu .mobile-menu__item .icon {
  fill: {{ settings.mobile_menu_link_color }};
  margin-right: 12px;
}
.mobile-menu .mobile-menu__item .icon:hover {
  fill: {{ settings.mobile_menu_link_hover_color }};
}
.mobile-menu .mobile-menu__item select {
  color: {{ settings.header_link_color }};
}
.mobile-menu .mobile-menu__item select:hover {
  color: {{ settings.header_link_hover_color }};
}
.mobile-menu .mobile-menu-link,
.mobile-menu .mobile-menu__item {
  display: flex;
  line-height: 1.5;
  padding: 0.5rem 1.5rem;
}

.mobile-menu__first-level label {
  display: block;
  position: relative;
}
.mobile-menu__first-level .close-dropdown,
.mobile-menu__first-level .close-sub-dropdown {
  position: absolute;
  cursor: pointer;
  top: 0;
  right: 0;
  height: 100%;
  width: 25%;
  z-index: 2;
}
.mobile-menu__first-level .has-no-link .close-dropdown,
.mobile-menu__first-level .has-no-link .close-sub-dropdown {
  width: 100%;
}

.mobile-menu__content {
  padding-bottom: 100px;
}
.mobile-menu__content .icon-caption {
  padding: 0;
  font-size: 1em;
}
.mobile-menu__content .mobile-menu__list {
  padding-bottom: 100px;
}

.mobile__currency-dropdown {
  align-items: center;
  position: relative;
}
.mobile__currency-dropdown .icon-caption {
  position: absolute;
}
.mobile__currency-dropdown select {
  border: none;
  font-family: {{ settings.nav__font.family }}, {{ settings.nav__font.fallback_families }};
  font-weight: {{ settings.nav__font.weight }};
  font-style: {{ settings.nav__font.style }};
  font-size: {{ settings.nav_font_size | append: 'px' }};
  letter-spacing: {{ settings.nav_letter_spacing | append: 'px' }};
  text-transform: {{ settings.nav_font_style }};
  height: inherit;
  background: transparent;
  color: currentColor;
}

.mobile-menu-icons {
  display: flex;
  justify-content: center;
  position: absolute;
  top: 75vh;
  width: 100%;
  padding: 0;
}

.mobile-menu-link {
  position: relative;
}
.mobile-menu-link::after {
  border-color: {{ link }};
  right: 1.125em;
  z-index: 4;
  font-size: 1.1em;
}

.mobile-menu__submenu {
  transition: max-height 0.5s;
  border-left: 1px solid {{ settings.border_color }};
  margin-left: 20px;
  max-height: 0;
  overflow: hidden;
  padding-left: 0;
}
.mobile-menu__submenu.has-mega-menu {
  margin-left: 0;
  max-height: none;
  display: none;
}
.mobile-menu__submenu .mega-menu {
  display: block;
  position: relative;
  background-color: {{ color_background }} !important;
  left: 0;
}

.submenu__input:checked ~ .mobile-menu__submenu {
  transition: max-height 0.8s;
  max-height: 100vh;
}
.submenu__input:checked ~ .mobile-menu__submenu.has-dropdown {
  max-height: none;
}
.submenu__input:checked ~ .mobile-menu__submenu.has-mega-menu {
  max-height: none;
  display: block;
  border-left: 0;
}
.submenu__input:checked + label a:after {
  transform: translateY(-60%) rotate(180deg);
}
.submenu__input + label a:after {
  transition: transform 0.2s linear;
}

.header__icon-style-text .mobile-icons,
.header__icon-style-icons-text .mobile-icons {
  flex-wrap: wrap;
  justify-content: center;
}
.header__icon-style-text .mobile-icons.has-one-icon,
.header__icon-style-icons-text .mobile-icons.has-one-icon {
  justify-content: flex-end;
}
.header__icon-style-text .mobile-icons .action-area__link,
.header__icon-style-icons-text .mobile-icons .action-area__link {
  padding: 5px;
}
.header__icon-style-text .header-cart__caption,
.header__icon-style-icons-text .header-cart__caption {
  white-space: nowrap;
}

.header__icon-style-icons .mobile__currency-dropdown .icon-caption {
  display: block;
  opacity: 0;
}

.header__icon-style-text .mobile__currency-dropdown {
  line-height: 1.5;
  padding: 0.5rem 1.5rem;
}
.header__icon-style-text .mobile__currency-dropdown .icon-caption {
  position: relative;
}
.header__icon-style-text .mobile__currency-dropdown select,
.header__icon-style-text .mobile__currency-dropdown option {
  padding-left: 0px;
}

@-moz-document url-prefix() {
  .header__icon-style-text .mobile__currency-dropdown select {
    text-indent: -2px;
  }
}
.header__icon-style-icons-text .mobile__currency-dropdown select {
  padding-left: 25px;
}

.mobile-menu__toggle-button {
  display: none;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  width: 45px;
  z-index: 50;
  bottom: 0;
}
.mobile-menu__toggle-button.mobile-toggle__position--right {
  right: 0;
  left: initial;
}
@media only screen and (max-width: 798px) {
  .mobile-menu__toggle-button {
    display: block;
  }
}

/* # Mega menu
================================================== */
.mega-menu-section {
  display: none;
}

#header .is-width-wide .mega-menu {
  width: calc(100% + 20px);
  left: calc(-20px / 2);
}

.header--search-focus .mega-menu {
  overflow: auto;
}

.mega-menu {
  display: none;
  left: 0;
  font-size: {{ settings.dropdown_font_size | append: 'px' }};
  letter-spacing: {{ settings.dropdown_letter_spacing | append: 'px' }};
  text-transform: {{ settings.dropdown_font_style }};
  position: absolute;
  top: 100%;
  width: 100%;
  min-width: 100%;
  z-index: 20;
  box-shadow: 0 8px 8px rgba(10, 10, 10, 0.1);
}
@media only screen and (max-width: 798px) {
  .mega-menu {
    box-shadow: 0;
  }
}
.mega-menu.mega-menu--show {
  display: block;
}
.mega-menu.mega-menu--force-show {
  display: block !important;
  z-index: 30 !important;
}
.mega-menu a,
.mega-menu a:active,
.mega-menu a:visited {
  color: {{ drop_down_menu_active_color }};
}
.mega-menu a:hover {
  color: {{ settings.dropdown_link_hover_color }};
}
.mega-menu .menu__heading {
  margin-bottom: 10px;
  font-size: {{ settings.dropdown_heading_font_size | append: 'px' }};
  line-height: 1.2;
}
.mega-menu .mega-menu__linklist-link {
  display: inline-block;
  padding: 0.5em 0;
  line-height: 1;
}
.mega-menu .mega-menu__image {
  max-height: none;
}

.mega-menu__block {
  padding: 20px;
}
@media only screen and (max-width: 798px) {
  .mega-menu__block.block__empty-column {
    display: none;
  }
}
.mega-menu__block.block__featured-promo .button, .mega-menu__block.block__featured-promo .age-gate__confirm_btn {
  width: 100%;
}
.mega-menu__block .mega-menu__content {
  margin-top: 10px;
}
.mega-menu__block .mega-menu__content:first-child {
  margin-top: 0;
}
.mega-menu__block.block__featured-product .price {
  font-size: 0.9em;
}

.mega-menu__banner {
  padding: 0.75em 1em;
  width: 100%;
  transition: background-color 0.2s linear;
}
.mega-menu__banner .icon {
  flex-shrink: 0;
}
.mega-menu__banner .mega-menu__sticker-text {
  padding: 0 0.5em;
}
.mega-menu__banner.is-small {
  font-size: 0.8rem;
}
.mega-menu__banner.is-regular {
  font-size: 1rem;
}
.mega-menu__banner.is-large {
  font-size: 1.2rem;
}

.navbar-item.has-mega-menu {
  position: static;
}

.mega-menu__section.is-active .mega-menu {
  display: block;
}

.mega-menu__content {
  position: relative;
  width: 100%;
}
.mega-menu__content .has-thumbnail-sticker .sticker-holder__content {
  font-size: 14px;
}

/* # Popup
================================================== */
.popup-modal .fancybox-bg {
  opacity: 1;
}
.popup-modal .fancybox-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}
@media only screen and (max-width: 480px) {
  .popup-modal .fancybox-slide {
    align-items: flex-end;
    padding: 0;
  }
}

.popup__wrapper {
  display: none;
  padding: 0;
  max-width: 900px;
  width: 90%;
  position: relative;
  overflow: hidden;
}
@media only screen and (max-width: 480px) {
  .popup__wrapper {
    width: 100%;
  }
}
.popup__wrapper.animated {
  opacity: 0;
}
.popup__wrapper.has-image .newsletter-form {
  max-width: 100%;
}

.popup__text {
  font-size: 1rem;
}

.popup__close {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  margin: 10px;
  cursor: pointer;
}
.popup__close .icon--vertical-align {
  display: flex;
  justify-content: center;
  margin: auto;
}

.newsletter-section--popup {
  margin: 0;
  padding-top: 20px;
  width: 100%;
  max-width: none;
}
.newsletter-section--popup .newsletter__wrapper {
  max-width: 100%;
  padding: 0;
}

.popup__image {
  align-items: center;
  position: relative;
  min-width: 200px;
  width: 40%;
}
@media only screen and (max-width: 480px) {
  .popup__image {
    display: none;
  }
}
.popup__image .image-element__wrap,
.popup__image .image-element__wrap img {
  height: 100%;
}

.image-position-right .popup__image {
  order: 1;
}
.image-position-right .popup__close {
  left: 0;
  right: auto;
}

.popup__content {
  max-width: calc(75% - 80px);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1 0 60%;
  padding: 40px;
}
@media only screen and (max-width: 798px) {
  .popup__content {
    max-width: 100%;
    padding: 20px;
  }
}

.popup__header {
  width: calc(100% - 12px);
}
@media only screen and (max-width: 798px) {
  .popup__header {
    margin: 0 10px;
  }
}

/* # Rich text
================================================== */
.mobile-shrink-text span,
.mobile-shrink-text p {
  font-size: 1em;
  display: block;
}
@media only screen and (max-width: 798px) {
  .mobile-shrink-text span,
  .mobile-shrink-text p {
    font-size: 0.8em;
  }
}
@media only screen and (max-width: 480px) {
  .mobile-shrink-text span,
  .mobile-shrink-text p {
    font-size: 0.5em;
  }
}

.rich-text__content a.button, .rich-text__content a.age-gate__confirm_btn {
  max-width: 50%;
}
@media only screen and (max-width: 798px) {
  .rich-text__content a.button, .rich-text__content a.age-gate__confirm_btn {
    max-width: 100%;
  }
}

/* # Search
================================================== */
.search-form {
  position: relative;
}
@media only screen and (max-width: 798px) {
  .search-form {
    padding-top: 20px;
  }
}
.search-form .search__fields input[type=text] {
  outline: none;
  border-radius: 0;
  border: thin solid {{ border_color }};
  color: hsl(0, 0%, 48%);
}
.search-form .search__fields input[type=text]:active, .search-form .search__fields input[type=text]:focus {
  color: hsl(0, 0%, 4%);
  box-shadow: none;
}
.search-form .search__fields [data-icon=search] {
  padding: 5px;
  color: hsl(0, 0%, 4%);
}
.search-form .search__fields .field {
  margin-bottom: 0;
}
.search-form .search__fields .control button {
  padding: 0;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translate(20%, -50%);
  -webkit-appearance: none;
          appearance: none;
  border: none;
  background: none;
  cursor: pointer;
}
.search-form .search__fields .control button .icon {
  position: relative;
  transform: none;
  padding: 3px;
}
@media only screen and (max-width: 798px) {
  .search-form .search__fields .control button {
    font-size: 1.25rem;
  }
  .search-form .search__fields .control button .icon {
    transform: translateX(-5px);
  }
}

/* # Search page - default view and results
================================================== */
.search__main {
  flex-grow: 1;
}
.search__main .search__results__thumbnail {
  --thumbnail-max-width: 5rem;
}
.search__main .image-element__wrap img {
  height: auto;
}

.search__results-count.has-padding-bottom {
  padding-bottom: 10px;
}

.search__no-results__text {
  padding-bottom: 30px;
}

.search__results-list .has-padding-bottom {
  padding-bottom: 40px;
}

@media only screen and (max-width: 480px) {
  .search-result__image-container {
    padding-bottom: 20px;
  }
}

.search-result__description {
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.search__content .search__results-wrapper {
  position: relative;
}

/* # Search overlay & Popup
================================================== */
{% liquid
    assign dropdown_alpha = settings.dropdown_background_opacity | divided_by: 100.00
    assign search_overlay_bg_color = settings.dropdown_background | color_modify: 'alpha', dropdown_alpha
  %}
[data-show-search-trigger] {
  cursor: pointer;
}
[data-show-search-trigger] * {
  pointer-events: none;
}

.search-overlay__title,
.search-popup__title {
  color: {{ settings.search_heading_color }};
}

.search-overlay__form,
.search-popup__form {
  padding: 20px 0;
}
.search-overlay__form .field:not(:last-child),
.search-popup__form .field:not(:last-child) {
  padding-bottom: 0;
}

.search-menu__list {
  display: inline;
  list-style: none;
  padding-left: 5px;
}
@media only screen and (max-width: 798px) {
  .search-menu__list {
    padding-left: 0;
    padding-top: 5px;
  }
}

.search-menu__item {
  display: inline-block;
  padding: 0 5px;
}
@media only screen and (max-width: 798px) {
  .search-menu__item {
    width: 100%;
    padding: 0;
  }
}
.search-menu__item a,
.search-menu__item a:visited {
  color: {{ settings.dropdown_link_color }};
}
.search-menu__item a:hover, .search-menu__item a:focus,
.search-menu__item a:visited:hover,
.search-menu__item a:visited:focus {
  color: {{ settings.dropdown_link_hover_color }};
}

.search-overlay {
  background-color: {{ settings.shop_bg_color }};
  color: {{ settings.search_heading_color }};
  position: absolute;
  top: 100%;
  left: 0;
  display: none;
  width: 100%;
  z-index: 70;
}
.search-overlay.animated {
  animation-duration: 300ms;
}
.search-overlay.sticky-search {
  position: fixed;
}
.search-overlay.is-opened {
  display: block;
}
@media only screen and (min-width: 1025px) {
  .search-overlay .search-menu {
    max-width: 70%;
    margin: 0px auto;
  }
}
.search-overlay .search-menu__heading {
  font-weight: 700;
}
.search-overlay .search__results-wrapper {
  --results-wrapper-transition-duration: 150ms;
}
.search-overlay .search__results__thumbnail {
  flex: 0 0 14%;
}
@media only screen and (max-width: 1024px) {
  .search-overlay .search__results__thumbnail {
    flex: 0 0 18%;
  }
}

.search-overlay__close {
  cursor: pointer;
  position: absolute;
  top: 15px;
  right: 15px;
}
.search-overlay__close:hover {
  opacity: 0.5;
}

.search-overlay__wrapper {
  padding: 60px 0;
}

.search-popup {
  display: none;
  background-color: {{ settings.search_popup_bg }};
  color: {{ settings.search_heading_color }};
  max-width: 600px;
  padding: 30px 0;
  font-size: 1.2em;
}
.search-popup .search-overlay__wrapper {
  padding: 0 20px;
  width: 100%;
}
.search-popup .search-menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.25rem;
}
.search-popup .search-menu__heading {
  font-weight: 700;
}
.search-popup .search-menu__title {
  padding-bottom: 20px;
}
@media only screen and (max-width: 798px) {
  .search-popup .search-menu__title {
    padding-bottom: 10px;
  }
}
.search-popup .search-menu__item {
  width: 45%;
  padding: 0;
  display: block;
}
@media only screen and (max-width: 798px) {
  .search-popup .search-menu__item {
    width: 100%;
    text-align: center;
  }
}
.search-popup .search__results-wrapper {
  position: relative;
  background-color: {{ settings.search_popup_bg }};
}
.search-popup .search__results-wrapper .products-page-posts-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
}
.search-popup .search__results {
  background-color: {{ settings.search_popup_bg }};
}
.search-popup .search__results__thumbnail {
  --thumbnail-max-width: 20rem;
  flex: 0 0 12%;
}

@media only screen and (min-width: 1025px) {
  .search-popup__form {
    margin: 0px auto;
  }
}

.header--search-focus .search__results__thumbnail {
  flex: 0 0 18%;
}
@media only screen and (max-width: 1024px) {
  .header--search-focus .search__results__thumbnail {
    flex: 0 0 25%;
  }
}

/* # Search autocomplete
================================================== */
.search__results-wrapper {
  width: 100%;
  max-height: var(--results-max-height, 75vh);
  padding-inline: 20px;
  position: absolute;
  right: 0;
  left: auto;
  background-color: {{ color_background }};
  box-shadow: 0 8px 8px rgba(10, 10, 10, 0.1);
  overflow-y: auto;
  z-index: 30;
  transition: opacity var(--results-wrapper-transition-duration, 300ms);
}
.search__results-wrapper:empty {
  opacity: 0;
  transition: none;
}

.search__results {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
  list-style-type: none;
  background-color: {{ color_background }};
  letter-spacing: normal;
  text-transform: none;
}

.products-page-posts-wrapper {
  display: flex;
  gap: 3rem;
}
.header--vertical .products-page-posts-wrapper, .sidebar-block__search .products-page-posts-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
}

.search__results__item {
  display: block;
  width: 100%;
  padding-top: 10px;
  margin: 0;
  font-family: {{ settings.regular__font.family }}, {{ settings.regular__font.fallback_families }};
  line-height: 1.5;
}
.search__results__item--product:not(:first-child) {
  padding-top: 15px;
}
.search__results__item a {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: {{ settings.regular_color }};
}
.search__results__item a:hover {
  color: {{ settings.header_link_hover_color }};
}
.search__results__item .description__title {
  padding: 0 0 5px;
  color: inherit;
  font-family: inherit;
  font-size: 15px;
  font-weight: 700;
  letter-spacing: normal;
}
.search__results__item mark {
  color: inherit;
  background: none;
}
.search__results__suggestions .search__results__item .search__results__item__suggestions {
  white-space: pre-wrap;
}
.search__results__suggestions .search__results__item span {
  font-weight: bolder;
}

.search__results__products {
  flex: 1 1 50%;
}
.search__results__products .search__results__thumbnail {
  padding: 0;
  margin-right: 16px;
  max-width: var(--thumbnail-max-width, 6rem);
}
.search__results__products .search__results__thumbnail img {
  display: block;
}
.search__results__products .money {
  display: inline;
}

.search__results__pages-posts {
  flex: 1 1 50%;
}

.no-results {
  padding: 10px 0;
  margin: 32px 0 0;
  color: {{ settings.regular_color }};
  text-align: center;
}

.all-results {
  font-size: {{ btn_primary_font_size }};
  border-radius: {{ settings.button_primary_border_radius | append: 'px' }};
  width: {{ btn_primary_width }};
  line-height: {{ btn_primary_line_height }};
  height: auto;
  max-width: 100%;
  white-space: normal;
  overflow-wrap: normal;
  margin: 32px 0 15px;
  width: 100%;
  display: block;
  letter-spacing: normal;
}
.all-results, .all-results:link, .all-results:visited {
  color: {{ settings.button_primary_text_color }};
  background-color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
  border-color: {{ settings.button_primary_border_color }};
}
.all-results:hover, .all-results.is-hovered {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.all-results:focus, .all-results.is-focused {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.all-results:focus:not(:active), .all-results.is-focused:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.all-results:active, .all-results.is-active {
  color: {{ settings.button_primary_text_color--highlight }};
  border-color: {{ settings.button_primary_border_color--highlight }};
  background-color: {{ settings.button_primary_bg_color--highlight | color_modify: 'alpha', btn_primary_hover_alpha }};
}
.all-results.is-inverted {
  color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
  background-color: {{ settings.button_primary_text_color }};
  border-color: {{ settings.button_primary_bg_color | color_modify: 'alpha', btn_primary_alpha }};
}
.all-results.is-small {
  font-size: 0.75rem;
}
.all-results.is-normal {
  font-size: 1rem;
}
.all-results.is-medium {
  font-size: 1.25rem;
}
.all-results.is-large {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}

.results-heading {
  padding-block: 10px 5px;
  border-bottom: 1px solid {{ settings.regular_color | color_modify: 'alpha', 0.3 }};
  font-size: 0.875rem;
  letter-spacing: normal;
}

.vertical-header__block {
  letter-spacing: normal;
}
.vertical-header__block .search__results-wrapper {
  max-width: 40%;
}
@media only screen and (min-width: 1025px) {
  .vertical-header__block .search__results-wrapper {
    max-height: 95vh;
  }
}
.vertical-header__block .search__results__thumbnail {
  flex: 0 0 12%;
}
@media only screen and (max-width: 1024px) {
  .vertical-header__block .search__results__thumbnail {
    flex: 0 0 20%;
  }
}

.page-not-found-page .content .search__results {
  margin: 0;
}

/* #Mobile search
================================================== */
.mobile-search {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 999999;
  width: 100%;
  background: rgba(0, 0, 0, 0.8);
}
@media only screen and (min-width: 799px) {
  .mobile-search {
    display: none !important;
  }
}
.mobile-search form {
  background-color: white;
  margin: 0;
  padding: 10px 20px;
  position: relative;
  width: 100%;
}
.mobile-search .search__fields {
  margin-block: 10px;
}
.mobile-search input#q {
  font-size: 16px;
  padding-left: 40px;
  padding-right: 20px;
  padding-bottom: 12px;
  border: 0;
  border-bottom: 1px solid {{ settings.border_color }};
}
.mobile-search input#q:focus, .mobile-search input#q:active {
  border-bottom: 1px solid {{ settings.border_color }};
}
.mobile-search .control .icon {
  transform: translateY(-50%);
  padding: 0;
  pointer-events: initial;
  z-index: 2;
  cursor: pointer;
  color: hsl(0, 0%, 48%);
}
.mobile-search .control .icon.is-left {
  padding-left: 5px;
}
.mobile-search .control .icon.is-right {
  padding-right: 5px;
}
.mobile-search .search__results-wrapper {
  --results-wrapper-transition-duration: 150ms;
  padding: 0 10px;
  -webkit-overflow-scrolling: touch;
}
@media only screen and (max-width: 798px) {
  .mobile-search .search__results-wrapper {
    padding-inline: 20px;
  }
}
.mobile-search .search__results {
  width: 100%;
  margin-top: 0;
  padding: 0;
  position: relative;
}
.mobile-search .products-page-posts-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
}
@media only screen and (max-width: 798px) {
  .mobile-search .search__results__thumbnail {
    flex: 0 0 12%;
  }
}
@media only screen and (max-width: 480px) {
  .mobile-search .search__results__thumbnail {
    flex: 0 0 18%;
  }
}

/* # Sidebar
================================================== */
@media only screen and (max-width: 798px) {
  .sidebar-section {
    width: 100% !important;
  }{%- if settings.mobile_sidebar_position == 'above' -%}
    .sidebar-section {
      order: 0;
    }{%- else -%}
    .sidebar-section {
      order: 1;
    }{%- endif -%}
  .sidebar-section + .has-sidebar-option {
    width: 100% !important;
  }
}

.product-sidebar--mobile-above {
  padding-bottom: 40px;
}

.product-sidebar--mobile-below {
  padding-bottom: 20px;
}

.sidebar-block__heading {
  cursor: default;
  display: flex;
  align-items: center;
  position: relative;
}

.sidebar-block__search {
  padding-top: 1rem;
}

.sidebar__block.sidebar-toggle-active {
  padding-top: 10px;
  padding-bottom: 10px;
}
.sidebar__block:first-child {
  padding-top: 0;
}
.sidebar__block:last-child {
  padding-bottom: 0;
}

.sidebar-block__toggle + .sidebar-block__content {
  padding-top: 1rem;
}

.sidebar-block__toggle-icon {
  position: absolute;
  right: 0;
  border: none;
  top: 50%;
  transform: translateY(-50%);
  padding: 0;
  background: transparent;
}
.sidebar-block__toggle-icon:focus {
  outline: 0;
}
.sidebar-block__toggle-icon .icon {
  color: hsl(0, 0%, 4%);
  transition: transform 0.2s linear;
}

.icon-style--plus_and_minus .icon.icon--active + .icon {
  display: none;
}

.sidebar-toggle-active.is-active .icon-style--carets .icon {
  transform: rotate(180deg);
}
.sidebar-toggle-active.is-active .icon-style--plus_and_minus .icon.icon--active {
  display: none;
}
.sidebar-toggle-active.is-active .icon-style--plus_and_minus .icon.icon--active + .icon {
  display: block;
}

[data-sidebar-block__content--collapsible] {
  display: none;
}

.sidebar-block__recent-post {
  margin-bottom: 10px;
}
.sidebar-block__recent-post .label {
  font-weight: normal;
}

@media only screen and (min-width: 799px) {
  .block__featured-promo {
    width: 75%;
  }
}
.block__featured-promo .card-content {
  padding: 20px;
}
.block__featured-promo .sidebar__promo-content {
  margin-left: 0;
}

.block__newsletter .newsletter-section {
  width: 100%;
}
.block__newsletter .newsletter__wrapper {
  max-width: 100%;
}
.block__newsletter .newsletter__text {
  color: inherit !important;
}

.block__menu .sidebar-block__item {
  margin-bottom: 5px;
}

.block__tag-filter .tag-filter__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}
.block__tag-filter .tag-filter__label {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;
  color: {{ link }};
  transition: 0.2s linear;
  display: flex;
  align-items: center;
}
.block__tag-filter .tag-filter__label:hover {
  color: {{ settings.link_hover_color }};
}
.block__tag-filter .tag-filter__item.is-active .tag-filter__label {
  cursor: initial;
  color: initial;
}
.block__tag-filter .tag-filter__item.is-active .tag-filter__label:hover {
  color: initial;
}
.block__tag-filter .tag-filter__checkbox--regular {
  display: none;
}
.block__tag-filter .tag-filter__swatch {
  -webkit-appearance: none;
          appearance: none;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  vertical-align: top;
  background-size: cover;
  margin-right: 10px;
  font-size: 0;
  border: 1px solid {{ settings.border_color }};
}
{%- if settings.swatch_style != 'square' -%}
.block__tag-filter .tag-filter__swatch {
    border-radius: 50%;
  }
{%- endif -%}
.block__tag-filter .tag-filter__swatch input[style*=no-image-50],
.block__tag-filter .tag-filter__swatch input.swatch__image--empty {
  opacity: 0;
}
.block__tag-filter .tag-filter__checkbox--swatch {
  -webkit-appearance: none;
          appearance: none;
  width: 100%;
  height: 20px;
  cursor: pointer;
  background-position: center;
  background-size: cover;
}
{%- if settings.swatch_style != 'square' -%}
.block__tag-filter .tag-filter__checkbox--swatch {
    border-radius: 50%;
  }
{%- endif -%}

.faceted-filter-form .clear-filter {
  flex-shrink: 0;
  width: 10px;
  height: 10px;
}

.faceted-filter-group-summary__active-count {
  margin: 0 auto 0 0.5rem;
}

.faceted-filter-form {
  margin: 0;
}

.faceted-filter-group-display {
  margin: 0 0 20px;
}

.faceted-filter-group-display__header,
.sidebar-block__heading {
  display: flex;
  justify-content: space-between;
}

.faceted-filter-group-display__header {
  margin-bottom: 10px;
}

ul.faceted-filter-group-display__list {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.faceted-filter-group-display__list-item {
  margin-top: 0.313rem;
}

.faceted-filter-group-display__list-item-input {
  position: absolute;
  opacity: 0;
}
.faceted-filter-group-display__list-item-input:hover {
  cursor: pointer;
}
.faceted-filter-group-display__list-item-input:disabled {
  cursor: default;
}

.faceted-active-filters__remove-filter {
  word-break: break-word;
}

.faceted-filter-group-display__list-item-label {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0;
}

.faceted-filter-group-display__list-item-label-text {
  padding-left: 12px;
  font-size: 15px;
  font-weight: initial;
  color: {{ link }};
  word-break: break-word;
}
.faceted-filter-group-display__list-item-label-text:hover {
  color: {{ settings.link_hover_color }};
  cursor: pointer;
}
.faceted-filter-group-display__list-item-input:checked ~ .faceted-filter-group-display__list-item-label-text {
  font-weight: bold;
}

.faceted-filter-group-display__list-item-input:disabled ~ * {
  pointer-events: none;
  opacity: 0.5;
}

.faceted-filter-group-display__checkmark {
  stroke-width: 2;
  border: 1px solid {{ settings.border_color }};
  transition: border-color 100ms, background 100ms;
}
.faceted-filter-group-display__list-item-input:focus ~ .faceted-filter-group-display__checkmark {
  box-shadow: 0 0 0 2px {{ settings.shop_bg_color }}, 0 0 0 4px {{ settings.link_color }};
}
.faceted-filter-group-display__list-item-input:checked ~ .faceted-filter-group-display__checkmark {
  stroke: {{ settings.shop_bg_color }};
  background: {{ settings.link_color }};
  border-color: {{ settings.link_color }};
}
.faceted-filter-group-display__list-item-input:disabled ~ * .faceted-filter-group-display__checkmark {
  pointer-events: none;
}

.faceted-filter-group-display__price-range {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 15px;
}

.faceted-filter-group-display__price-range-from,
.faceted-filter-group-display__price-range-to {
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  grid-column-gap: 5px;
}
.faceted-filter-group-display__price-range-from .faceted-filter-group-display__price-range-input,
.faceted-filter-group-display__price-range-to .faceted-filter-group-display__price-range-input {
  margin: 0;
  padding: 10px;
}

.faceted-filter-group-display__price-range-label {
  grid-column: span 2;
  margin: 0;
  font-weight: bold;
}

.faceted-filter-group-display__submit {
  margin-top: 20px;
}

.js .faceted-filter-group-display__list-submit {
  display: none;
}

.faceted-active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  padding: 15px 0;
  border-bottom: 1px solid {{ settings.border_color }};
}
.sidebar-block--first .faceted-active-filters {
  padding-top: 0;
}

/* # Slideshow with text
================================================== */
@media only screen and (max-width: 798px) {
  .slideshow-with-text .has-gutter-enabled .image-slideshow {
    margin-bottom: 20px;
  }
}
.slideshow-with-text .text-slideshow__heading.banner__subheading {
  font-family: {{ settings.regular__font.family }}, {{ settings.regular__font.fallback_families }};
  font-size: {{ settings.regular_font_size | append: 'px' }};
  font-weight: {{ settings.regular__font.weight }};
  font-style: {{ settings.regular__font.style }};
}
.slideshow-with-text .text-slideshow__heading.banner__heading {
  font-family: {{ settings.heading__font.family }}, {{ settings.heading__font.fallback_families }};
  font-size: {{ settings.heading_size | append: 'px' }};
  font-weight: {{ settings.heading__font.weight }};
  font-style: {{ settings.heading__font.style }};
}
.slideshow-with-text .flickity-prev-next-button.next {
  right: 0;
}
.slideshow-with-text .flickity-prev-next-button.previous {
  left: 0;
}

.image-slideshow {
  opacity: 0;
  transition: opacity 0.4s;
  width: 100%;
  margin: 0 auto;
}
@media only screen and (max-width: 798px) {
  .image-slideshow {
    order: 1;
  }
}
.image-slideshow .placeholder-svg {
  height: 500px;
}
@media only screen and (max-width: 798px) {
  .image-slideshow .placeholder-svg {
    height: 300px;
  }
}
.image-slideshow .flickity-buttons-container {
  position: absolute;
  display: flex;
  bottom: 20px;
  right: 20px;
}
@media only screen and (max-width: 798px) {
  .image-slideshow .flickity-buttons-container {
    display: none;
  }
}
.image-slideshow .flickity-button {
  position: relative;
  transform: none;
}
.image-slideshow .flickity-button:first-child {
  margin-right: 10px;
}

.image-slideshow-position--left .flickity-buttons-container {
  left: 5%;
  right: auto;
}

.image-slideshow-position--right {
  order: 2;
}
@media only screen and (max-width: 798px) {
  .image-slideshow-position--right {
    order: 0;
  }
}

.image-slideshow.flickity-enabled {
  opacity: 1;
  height: auto;
}

.image-slideshow__slide {
  width: 100%;
}
.image-slideshow__slide .image-element__wrap {
  width: 100%;
  height: auto;
}

.text-slideshow {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.text-slideshow .flickity-viewport,
.text-slideshow .flickity-slider {
  height: 100%;
}
.text-slideshow .flickity-page-dots {
  align-self: flex-end;
  position: static;
  padding-bottom: 3em;
}
@media only screen and (max-width: 798px) {
  .text-slideshow .flickity-page-dots {
    padding-top: 3em;
    padding-bottom: 0;
    order: -1;
  }
}
.text-slideshow.flickity-page-dots--hidden .flickity-page-dots {
  display: none;
}
@media only screen and (max-width: 798px) {
  .text-slideshow.flickity-page-dots--hidden .flickity-page-dots {
    display: block;
  }
}

.text-slideshow__slide {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 3em;
}

.text-slideshow__content {
  width: 100%;
  opacity: 0;
  animation-delay: 0.2s;
}
.text-slideshow__content.animated.none {
  opacity: 1;
}
.text-slideshow__content .button, .text-slideshow__content .age-gate__confirm_btn {
  margin-top: 10px;
}

.text-slideshow__heading {
  padding-bottom: 20px;
  line-height: 1.5;
  margin: 0;
}
.text-slideshow__heading.subtitle {
  font-size: {{ settings.regular_font_size | append: 'px' }};
}

/* # Slideshow - classic
================================================== */
.slideshow-classic {
  width: 100%;
}
@media only screen and (max-width: 480px) {
  .slideshow-classic.page-dots--true {
    padding-bottom: 25px;
    margin-bottom: 25px;
  }
}
.slideshow-classic .flickity-prev-next-button .flickity-button-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 45%;
  height: 45%;
  transform: translateX(-50%) translateY(-50%);
}
.mobile-text--over-media .slideshow-classic .caption {
  top: 50%;
  transform: translateY(-50%);
}
@media only screen and (max-width: 480px) {
  .mobile-text--below-media .slideshow-classic .caption {
    position: static;
    padding: 0;
    margin: 20px 0;
    text-align: center;
  }
}
@media only screen and (max-width: 480px) {
  .mobile-text--below-media .slideshow-classic .caption-content {
    padding: 30px;
  }
}
.slideshow-classic .gallery-cell {
  width: 100%;
}
.slideshow-classic .gallery-cell .image-element__wrap {
  width: 100%;
}
@media only screen and (max-width: 480px) {
  .slideshow-classic .gallery-cell {
    display: block;
  }
}
.slideshow-classic .flickity-button,
.slideshow-classic .flickity-page-dots {
  position: absolute;
  top: auto;
  bottom: 25px;
}
.slideshow-classic .flickity-button {
  z-index: 10;
}
@media only screen and (max-width: 480px) {
  .slideshow-classic .flickity-button {
    display: none;
  }
}
.slideshow-classic .flickity-page-dots {
  bottom: 40px;
}
@media only screen and (max-width: 480px) {
  .slideshow-classic .flickity-page-dots {
    bottom: 0;
  }
}

.slideshow-classic__buttons {
  justify-content: space-between;
}
.slideshow-classic__buttons.are-small {
  max-width: 300px;
}
.slideshow-classic__buttons.are-regular {
  max-width: 400px;
}
.slideshow-classic__buttons.are-large {
  max-width: 500px;
}
.slideshow-classic__buttons.is-justify-left {
  justify-content: flex-start;
}
.slideshow-classic__buttons.is-justify-right {
  justify-content: flex-end;
}
.slideshow-classic__buttons.is-justify-center {
  justify-content: center;
}
.slideshow-classic__buttons .button, .slideshow-classic__buttons .age-gate__confirm_btn {
  flex: 0 0 calc(50% - 6px);
  margin-bottom: 12px;
  white-space: normal;
  margin-left: 0;
  margin-right: 0;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .slideshow-classic__buttons .button, .slideshow-classic__buttons .age-gate__confirm_btn {
    width: 100% !important;
    max-width: calc(50% - 6px) !important;
    margin: 0px auto;
  }
}
@media only screen and (max-width: 480px) {
  .slideshow-classic__buttons .button, .slideshow-classic__buttons .age-gate__confirm_btn {
    flex: 1 0 100%;
  }
}

.slideshow-classic__banner {
  overflow: hidden;
  position: relative;
}
@media only screen and (max-width: 798px) {
  .slideshow-classic__banner {
    overflow: visible;
  }
}
.slideshow-classic__banner h2.title {
  font-family: {{ settings.banner_heading__font.family }}, {{ settings.banner_heading__font.fallback_families }};
  font-weight: {{ settings.banner_heading__font.weight }};
  font-style: {{ settings.banner_heading__font.style }};
  font-size: {{ settings.banner_heading_size | append: 'px' }};
  text-transform: {{ settings.banner_heading_style }};
}
.slideshow-classic__banner .slideshow-classic__subheading {
  padding-top: 10px;
}
.slideshow-classic__banner .placeholder-svg {
  min-height: 400px;
}

/* # Testimonial
================================================== */
.testimonials {
  display: flex;
}
.testimonials::after {
  display: none;
  content: "flickity";
}
.testimonials .flickity-page-dots {
  bottom: 0;
}
@media only screen and (min-width: 799px) {
  .testimonials .flickity-page-dots {
    display: none;
  }
}
@media only screen and (max-width: 798px) {
  .testimonials.testimonials--page-dots-true {
    padding-bottom: 25px;
  }
}

.testimonial-block {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  min-height: 100%;
  overflow: hidden;
}
@media only screen and (max-width: 798px) {
  .testimonial-block {
    margin-bottom: 20px;
    min-height: initial;
  }
}
.testimonial-block.testimonial-border--true {
  border: thin solid {{ border_color }};
}
.testimonial-block.testimonial-align--center .testimonial__description,
.testimonial-block.testimonial-align--center .testimonial__name {
  justify-content: center;
  text-align: center;
}
.testimonial-block.testimonial-align--center .testimonial__image {
  left: 50%;
  margin-left: -50px;
}

.testimonial__description,
.testimonial__name {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
  width: 100%;
  position: relative;
}

.testimonial__name {
  align-self: flex-end;
  padding: 70px 40px 25px;
  max-height: 175px;
  height: 175px;
}
@media only screen and (max-width: 1024px) {
  .testimonial__name {
    font-size: 0.85rem;
  }
}
@media only screen and (max-width: 798px) {
  .testimonial__name {
    font-size: 1rem;
    max-height: 23vh;
    height: 23vh;
  }
}

.testimonial__description {
  align-self: center;
  padding: 40px;
  height: 100%;
  min-height: 250px;
}

.testimonial__image {
  position: absolute;
  margin-top: -50px;
  height: 100px;
  width: 100px;
  max-width: 100px;
  max-height: 100px;
  top: 0;
}
.testimonial__image.image-style--circle img, .testimonial__image.image-style--circle svg {
  border-radius: 50%;
}

.testimonial__nav-wrapper {
  justify-content: flex-end;
}
@media only screen and (max-width: 480px) {
  .testimonial__nav-wrapper {
    justify-content: space-between;
  }
}

.testimonial__nav {
  color: {{ link }};
  margin-bottom: 10px;
  cursor: pointer;
}
.testimonial__nav:first-child {
  margin-right: 10px;
}
.testimonial__nav:hover {
  color: {{ settings.link_hover_color }};
}

.testimonial-block.has-images-enabled-true .testimonial__name {
  margin-top: 40px;
}

.testimonial-block.has-images-enabled-false .testimonial__name .testimonial__description {
  padding: 40px;
}

.is-width-wide .testimonial__nav:last-child {
  margin-right: 10px;
}

@media only screen and (min-width: 799px) {
  .mobile-slider .testimonials:after {
    content: "";
  }
}

@media only screen and (min-width: 799px) {
  .desktop-slider--disabled .testimonial__nav-wrapper {
    display: none;
  }
}
.desktop-slider--disabled .testimonial__description {
  height: initial;
}

/* # Top bar
================================================== */
.top-bar {
  position: relative;
  z-index: 80;
}
@media only screen and (max-width: 798px) {
  .top-bar {
    display: none;
  }
}
.header-section ~ .top-bar {
  z-index: 60;
}
.scroll-locked .header-section ~ .top-bar {
  z-index: 0;
}

.top-bar .navbar-item {
  flex: 1 1 auto;
  text-align: center;
}

.top-bar__text {
  min-width: 25%;
}

.top-bar__item,
.top-bar.disclosure-enabled .top-bar__item {
  display: flex;
  align-items: center;
}
.top-bar__item:first-child,
.top-bar.disclosure-enabled .top-bar__item:first-child {
  flex: 1 1 auto;
  justify-content: flex-start;
}
.top-bar__item:last-child,
.top-bar.disclosure-enabled .top-bar__item:last-child {
  flex: 0 0 auto;
  justify-content: flex-end;
}

.top-bar.disclosure-enabled .top-bar__item {
  flex: 1 1 auto;
}
.top-bar.disclosure-enabled .top-bar__item.top-bar__social-media {
  flex: 1 1 auto;
  justify-content: flex-end;
}
.top-bar.disclosure-enabled .top-bar__item.top-bar__social-media .navbar-item {
  width: 100%;
  padding-right: 20px;
  flex: 1 1 auto;
}

.top-bar__menu {
  flex: 1 1 auto;
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 798px) {
  .top-bar__menu {
    justify-content: center;
  }
}

.top-bar__social-media a {
  line-height: 0;
  margin: 5px;
  display: block;
}
@media only screen and (max-width: 798px) {
  .top-bar__social-media .social-icons {
    justify-content: center;
  }
}

.top-bar__icons.cart--hidden {
  padding-right: 20px;
}

.top-bar__content .header__currency-dropdown select {
  color: inherit;
}
.top-bar__content .header__currency-dropdown select:hover {
  color: inherit;
}
.top-bar__content .header__icon-style-text .header__currency-dropdown select {
  padding-top: 0;
  padding-bottom: 0;
  height: auto;
  line-height: inherit;
}

/* # Video
================================================== */
@media only screen and (max-width: 480px) {
  .featured-video.is-width-half.has-video-added {
    background-color: transparent;
  }
}
.featured-video.is-width-half.has-video-added .video-wrapper {
  flex: 1;
}

.video__container {
  width: 100%;
}

@media only screen and (min-width: 799px) {
  .video-wrapper .plyr--video.plyr--paused .plyr__video-embed iframe {
    z-index: 5;
  }
}

.video-controls--false .plyr__controls {
  display: none;
}

.video-present--false [data-video-element] {
  display: none;
}

.video-wrapper {
  position: relative;
}
.video-wrapper.overlay--text_only .video__text-wrapper {
  position: relative;
}
.video-wrapper video {
  display: block;
  width: 100%;
  height: auto;
}
.video-wrapper .video__text-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 5;
}
.video-wrapper .video__text-container .overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: -1;
}
@media only screen and (max-width: 480px) {
  .video-wrapper .video__text-container {
    position: relative;
    margin: 0;
  }
}
.video-wrapper .video__text {
  height: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
}
@media only screen and (max-width: 480px) {
  .mobile-text--below-media .video-wrapper .video__text-outer-wrapper {
    justify-content: center;
  }
}
.video-wrapper .video__text-wrapper {
  margin: 0;
  padding: 30px;
}
.video-wrapper .video__text-wrapper .button, .video-wrapper .video__text-wrapper .age-gate__confirm_btn {
  max-width: 50%;
}
@media only screen and (max-width: 480px) {
  .video-wrapper .video__text-wrapper .button, .video-wrapper .video__text-wrapper .age-gate__confirm_btn {
    max-width: 100%;
  }
}
@media only screen and (max-width: 480px) {
  .video-wrapper {
    display: flex;
    flex-direction: column;
  }
}

@media only screen and (max-width: 480px) {
  .mobile-text--over-media .video__text-container {
    position: absolute;
  }
}

@media only screen and (max-width: 480px) {
  .mobile-text--below-media .video__text-container {
    margin-top: 20px;
    margin-bottom: 20px;
  }
}

/* # Accounts
================================================== */
.register__image img,
.login__image img {
  width: 100%;
}

.register__form img,
.login__form img {
  max-width: 200px;
  margin: 0;
}

.or {
  margin-left: 10px;
}

.login__recover {
  display: none;
}

.recover-note {
  margin: 0.75rem 0;
}

.action_bottom .button, .action_bottom .age-gate__confirm_btn,
.recover-options .button,
.recover-options .age-gate__confirm_btn {
  margin-right: 5px;
}

.order__unit-price {
  font-size: 0.875rem;
}

.address-table {
  display: flex;
  flex-wrap: wrap;
}

.address-table__address {
  margin-bottom: 1.5rem;
}
@media only screen and (max-width: 480px) {
  .address-table__address.column {
    margin: 1.5rem 0 0;
  }
}

.address-table__title {
  font-weight: bold;
}

.address-table__details {
  margin: 0.5rem 0;
}

/* # Blog
================================================== */
.blog__filter {
  margin-bottom: 20px;
}
@media only screen and (max-width: 480px) {
  .blog__filter .select, .blog__filter .age-gate__select-wrapper,
  .blog__filter select {
    width: 100%;
  }
}
.blog__filter .is-grouped {
  justify-content: flex-end;
}
@media only screen and (max-width: 798px) {
  .blog__filter .is-grouped {
    justify-content: flex-start;
  }
}

.blog-main {
  display: flex;
}
.blog-main .select, .blog-main .age-gate__select-wrapper {
  border-color: {{ settings.border_color }};
}
.blog-main .container > section {
  flex: auto;
}

.meta-info-list {
  font-size: 0.8em;
}
.meta-info-list .meta-info-list__item {
  margin-right: 5px;
  display: inline-block;
}
.meta-info-list .meta-info-list__item a,
.meta-info-list .meta-info-list__item span {
  padding-left: 5px;
  color: {{ settings.regular_color }};
}
.meta-info-list .meta-info-list__item:first-child a, .meta-info-list .meta-info-list__item:first-child span {
  padding-left: 0;
}
.meta-info-list .meta-info-list__item:last-child {
  margin-right: 0;
}
.meta-info-list .meta-info-list__item + li:before {
  content: "|";
  color: {{ text | color_modify: 'alpha', 0.3 }};
}

.blog-card {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  border: thin solid {{ border_color }};
  box-shadow: 0 2px 3px {{ settings.border_color | color_modify: 'alpha', 0.1 }}, 0 0 0 1px {{ settings.border_color | color_modify: 'alpha', 0.1 }};
}
@media only screen and (max-width: 798px) {
  .blog-card {
    margin-bottom: 20px;
  }
}
.blog-card.show-border-false {
  border: none;
  box-shadow: none;
}
.blog-card.show-border-false .card-content {
  padding-left: 0;
  padding-right: 0;
}
.blog-card.show-border-false .blog-card__read-more {
  margin-left: 0;
  margin-right: 0;
}
.blog-card .blog-card__content > div:not(:last-child),
.blog-card .meta-info > ul:not(:last-child) {
  margin-bottom: 1rem;
}
@media only screen and (min-width: 799px) {
  .blog-card .image-element__wrap {
    max-height: 15rem;
  }
}
.blog-card .media-content {
  font-size: {{ settings.heading_size | append: 'px' }};
}
.blog-card .media-content .title {
  font-size: 0.8em;
}
.blog-card .blog-card__read-more {
  margin: auto 1.5rem 1.5rem;
}
.blog-card .blog-card__read-more .button, .blog-card .blog-card__read-more .age-gate__confirm_btn {
  height: auto;
  white-space: normal;
}

@media only screen and (min-width: 799px) {
  .blog-card.is-horizontal {
    flex-direction: row;
    flex-grow: 0;
    flex-shrink: 1;
    max-height: 330px;
  }
  .blog-card.is-horizontal .blog-card__image {
    width: 33.33%;
  }
  .blog-card.is-horizontal .blog-card__image .image {
    position: relative;
    overflow: hidden;
    height: 100%;
    width: 100%;
  }
  .blog-card.is-horizontal .blog-card__image .image .image-element__wrap {
    height: 100%;
    max-height: none;
  }
  .blog-card.is-horizontal .blog-card__image .image .image-element__wrap img {
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
  .blog-card.is-horizontal .blog-card__content {
    display: flex;
    flex: 3;
    flex-direction: column;
  }
  .blog-card.is-horizontal.show-border-false {
    box-shadow: none;
  }
  .blog-card.is-horizontal.show-border-false .blog-card__content {
    padding: 1.5rem;
  }
  .blog-card.is-horizontal.show-border-false .blog-card__read-more {
    margin-left: 0;
  }
  .blog-card.is-horizontal .blog-card__read-more {
    margin: auto 0 0;
  }
}

@media only screen and (max-width: 798px) {
  .blog-card.is-horizontal .blog-card__read-more {
    margin: 0;
  }
  .blog-card .image-element__wrap {
    max-height: 20rem;
  }
}
.excerpt {
  position: relative;
  max-height: 6em;
  overflow: hidden;
}
.excerpt.excerpt-length-sm .truncation-fade {
  display: none;
}

.excerpt-length-lg .truncation-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: center;
  margin: 0;
  padding: 30px 0;
  background-image: linear-gradient(to bottom, {{ settings.shop_bg_color | color_to_rgb | color_modify: 'alpha', 0 }}, {{ settings.shop_bg_color | color_to_rgb | color_modify: 'alpha', 1 }});
}

.blog-card__link:hover .blog-card__image,
.blog-card__link:hover svg {
  transform: scale(1.1);
}

.blog-card__image,
.blog-card__link svg {
  transition: transform 0.3s ease-in-out;
}

/* # Cart
================================================== */
.quantity-box-enabled-true .cart__product-title {
  width: 49%;
}
.quantity-box-enabled-true .cart__price-title,
.quantity-box-enabled-true .cart__quantity-title,
.quantity-box-enabled-true .cart__total-title {
  width: 17%;
}

.quantity-box-enabled-false .cart__product-title {
  width: 66%;
}
.quantity-box-enabled-false .cart__price-title,
.quantity-box-enabled-false .cart__total-title {
  width: 17%;
}
.quantity-box-enabled-false .cart__description {
  width: 60%;
}
@media only screen and (max-width: 480px) {
  .quantity-box-enabled-false .cart__description {
    width: 100%;
  }
}

.cart__headings {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 10px;
}
@media only screen and (max-width: 1024px) {
  .cart__headings {
    display: none;
  }
}

.cart__price-title,
.cart__quantity-title,
.cart__total-title {
  text-align: center;
}

.cart__card {
  display: flex;
  align-items: center;
  border-top: 1px solid {{ border_color }};
  padding-top: 20px;
  margin-bottom: 20px;
}
@media only screen and (max-width: 798px) {
  .cart__card {
    align-items: flex-start;
  }
}

.item__title {
  font-size: 1.25em;
}

.cart__product-options {
  display: flex;
  flex-direction: column;
  padding-top: 20px;
}
@media only screen and (max-width: 798px) {
  .cart__product-options {
    padding-top: 0;
    font-size: 0.7em;
  }
}

.cart__info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 85%;
}
@media only screen and (max-width: 798px) {
  .cart__info {
    flex-direction: column;
    width: 60%;
    padding-left: 20px;
  }
}

.cart__total,
.cart__price {
  flex-direction: row;
  text-align: center;
}
.cart__total span,
.cart__price span {
  display: block;
}

.cart__unit-price {
  font-size: 0.875rem;
}

.cart__unit-price span {
  display: inline;
}

@media only screen and (max-width: 798px) {
  .cart__total {
    display: none;
  }
}

.cart__image {
  width: 15%;
}
@media only screen and (max-width: 798px) {
  .cart__image {
    width: 30%;
  }
}

.cart__description {
  width: 40%;
  padding: 0 20px;
}
@media only screen and (max-width: 798px) {
  .cart__description {
    width: 100%;
    padding: 0;
    text-align: left;
  }
}

.cart__total,
.cart__quantity,
.cart__price {
  width: 20%;
}
@media only screen and (max-width: 798px) {
  .cart__total,
  .cart__quantity,
  .cart__price {
    width: 100%;
    text-align: left;
  }
}

@media only screen and (max-width: 798px) {
  .cart__price .sale,
  .cart__price .original-price {
    display: inline-block;
  }
}

.cart__quantity {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}
.cart__quantity .purchase-details__quantity.product-quantity-box {
  margin-right: 0;
  width: 100%;
}
.cart__quantity .quantity-wrapper.quantity-style--box .quantity-input-control--fill {
  width: auto;
}
@media only screen and (min-width: 1025px) {
  .cart__quantity {
    justify-content: center;
  }
  .cart__quantity .quantity-input {
    width: 50px;
  }
}
@media only screen and (max-width: 798px) {
  .cart__quantity {
    margin-top: 20px;
  }
  .cart__quantity .purchase-details__quantity {
    width: 50%;
    max-width: 100px;
    margin-top: 0;
  }
}
@media only screen and (max-width: 480px) {
  .cart__quantity .quantity-style--stacked .quantity-input {
    width: 65px;
  }
}
.cart__quantity .quantity-style--box {
  max-width: 75%;
  margin: 0 auto;
}
@media only screen and (max-width: 798px) {
  .cart__quantity .quantity-style--box {
    max-width: 100%;
  }
}

.cart__quantity-warning {
  width: 100%;
  text-align: center;
  margin-top: 10px;
}

.cart__remove {
  position: absolute;
  right: 0;
  top: 10%;
}
@media only screen and (max-width: 798px) {
  .cart__remove {
    position: static;
    margin-left: 5px;
  }
}

.original-price {
  opacity: 0.2;
}

.line-item-discount__container {
  color: {{ settings.sale_color }};
  background: rgba(199, 0, 0, 0.1);
  border-radius: 3px;
  font-size: 0.8em;
  padding: 10px;
  margin: 5px 0;
}

.cart__cost-summary {
  background: #F2F2F2;
  padding: 20px;
  margin-top: 20px;
}
@media only screen and (max-width: 798px) {
  .cart__cost-summary {
    padding: 20px 15px;
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
    align-items: center;
  }
  .cart__cost-summary .offset-by-eight {
    left: 0;
  }
}
@media only screen and (max-width: 798px) {
  .cart__cost-summary .cart__discount-title {
    max-width: 75%;
  }
}
.cart__cost-summary .cart__discounts p,
.cart__cost-summary .cart__total-savings p {
  color: {{ settings.sale_color }};
}
.cart__cost-summary .cart__subtotal-container p {
  font-weight: bold;
}
.cart__cost-summary .cart__row {
  display: flex;
  justify-content: space-between;
  padding-top: 14px;
  text-align: right;
}
.cart__cost-summary .cart__row:first-child {
  padding-top: 0;
}
@media only screen and (max-width: 798px) {
  .cart__cost-summary .cart__row {
    text-align: left;
  }
}
.cart__cost-summary .cart__row .cart__row-description {
  width: 60%;
}

.cart__savings {
  justify-content: flex-end;
  padding: 20px 0;
  text-align: right;
}
@media only screen and (max-width: 798px) {
  .cart__savings {
    justify-content: center;
    font-size: 1.5em;
    padding: 20px 0 0;
  }
}

.price--sale,
.cart__savings,
.sale {
  color: {{ settings.sale_color }};
}

.compare-at-price,
.was-price {
  color: {{ settings.was_price_color }};
}

.cart__taxes-shipping-message {
  width: 100%;
}

.cart__notes {
  width: 90%;
  display: block;
}
@media only screen and (max-width: 798px) {
  .cart__notes {
    width: 100%;
  }
}
.cart__notes label {
  display: block;
  margin-bottom: 10px;
}

.cart__tos {
  padding-bottom: 20px;
}
.cart__tos .tos_agree {
  margin-right: 5px;
}

.cart__view-terms-container {
  display: inline-block;
}

.cart__cart-message {
  margin: 20px 0 0;
  text-align: right;
  display: block;
}
@media only screen and (max-width: 798px) {
  .cart__cart-message {
    text-align: center;
  }
}

.cart__featured-links {
  min-width: 236px; /* Set a minimum width to match the width of the checkout button */
  margin-top: 15px;
  text-align: center;
}
@media only screen and (max-width: 798px) {
  .cart__featured-links {
    min-width: 100%;
  }
}

.cart__checkout {
  min-width: 236px; /* Set a minimum width to match the width of additional checkout buttons */
  margin-left: 20px;
}
.cart__checkout .checkout {
  font-size: {{ btn_add_to_cart_font_size }};
  border-radius: {{ settings.button_cart_border_radius | append: 'px' }};
  width: {{ btn_add_to_cart_width }};
  line-height: {{ btn_add_to_cart_line_height }};
  width: 100%;
  min-height: 42px; /* Set a minimum height to match the height of additional checkout buttons */
}
.cart__checkout .checkout, .cart__checkout .checkout:link, .cart__checkout .checkout:visited {
  color: {{ settings.button_cart_text_color }};
  background-color: {{ settings.button_cart_bg_color }};
  border-color: {{ settings.button_cart_border_color }};
}
.cart__checkout .checkout:hover, .cart__checkout .checkout.is-hovered {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }} !important;
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.cart__checkout .checkout:focus, .cart__checkout .checkout.is-focused {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }};
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.cart__checkout .checkout:focus:not(:active), .cart__checkout .checkout.is-focused:not(:active) {
  box-shadow: 0 0 0 0.125em {{ link | color_modify: 'alpha', 0.25 }};
}
.cart__checkout .checkout:active, .cart__checkout .checkout.is-active {
  color: {{ settings.button_cart_text_color--highlight }};
  border-color: {{ settings.button_cart_border_color--highlight }};
  background-color: {{ settings.button_cart_bg_color--highlight }};
}
.cart__checkout .checkout.is-inverted {
  color: {{ settings.button_cart_bg_color }};
  background-color: {{ settings.button_cart_text_color }};
  border-color: {{ settings.button_cart_bg_color }};
}
.cart__checkout .checkout.is-small {
  font-size: 0.75rem;
}
.cart__checkout .checkout.is-normal {
  font-size: 1rem;
}
.cart__checkout .checkout.is-medium {
  font-size: 1.25rem;
}
.cart__checkout .checkout.is-large {
  font-size: 1.5rem;
  line-height: 1.25em;
  width: 100%;
}
@media only screen and (max-width: 798px) {
  .cart__checkout .checkout {
    min-height: 52px;
    margin-bottom: 15px;
  }
}
.cart__checkout .checkout.button .icon, .cart__checkout .checkout.age-gate__confirm_btn .icon {
  width: 1em;
  height: 1em;
}
.cart__checkout .checkout.button .icon:first-child:last-child, .cart__checkout .checkout.age-gate__confirm_btn .icon:first-child:last-child {
  margin-left: 0;
  margin-right: 5px;
}
@media only screen and (max-width: 798px) {
  .cart__checkout {
    width: 100%;
    margin-left: 0;
  }
}

.cart__checkout-elements {
  justify-content: flex-end;
}
@media only screen and (max-width: 798px) {
  .cart__checkout-elements {
    justify-content: center;
  }
}

@media only screen and (max-width: 798px) {
  .cart__buttons {
    flex-direction: column-reverse;
    flex-wrap: wrap;
  }
}

.additional-checkout-buttons {
  width: 100%;
  margin-top: 10px;
}

[data-shopify-buttoncontainer] {
  justify-content: flex-end;
}

.shipping-calculator select,
.shipping-calculator .select,
.shipping-calculator .age-gate__select-wrapper {
  width: 100%;
}
@media only screen and (max-width: 798px) {
  .shipping-calculator {
    flex-direction: column;
  }
  .shipping-calculator .control {
    min-width: 100%;
    margin-bottom: 20px;
  }
  .shipping-calculator input {
    width: 100%;
  }
}
@media only screen and (max-width: 798px) and (max-width: 798px) {
  .shipping-calculator .select, .shipping-calculator .age-gate__select-wrapper,
  .shipping-calculator select,
  .shipping-calculator input {
    height: 3.25em;
  }
}

.cart__shipping-calculator {
  width: 100%;
}
@media only screen and (max-width: 798px) {
  .cart__shipping-calculator {
    text-align: center;
    margin-top: 50px;
  }
}
@media only screen and (max-width: 798px) {
  .cart__shipping-calculator .cart__shipping-calculator-form {
    text-align: left;
  }
}
@media only screen and (max-width: 798px) {
  .cart__shipping-calculator .cart__shipping-calculator-form .select, .cart__shipping-calculator .cart__shipping-calculator-form .age-gate__select-wrapper {
    width: 100%;
  }
}
.cart__shipping-calculator .cart__shipping-title {
  padding-bottom: 50px;
}
@media only screen and (max-width: 798px) {
  .cart__shipping-calculator .cart__shipping-title {
    text-align: center;
  }
}
.cart__shipping-calculator label {
  padding-bottom: 20px;
  text-transform: uppercase;
  opacity: 0.5;
  display: block;
}
@media only screen and (max-width: 798px) {
  .cart__shipping-calculator label {
    padding-bottom: 10px;
  }
}
.cart__shipping-calculator .calc-field {
  margin-left: 0;
  margin-right: 20px;
  display: block;
}
@media only screen and (max-width: 798px) {
  .cart__shipping-calculator .calc-field {
    margin-right: 0;
    width: 100%;
  }
}

@media only screen and (max-width: 798px) {
  #get-rates-container {
    width: 100%;
  }
}

@media only screen and (max-width: 798px) {
  .get-rates {
    margin-top: 10px;
    flex: 1;
  }
}

.shipping-calculator__response-container {
  display: none;
}
.shipping-calculator__response-container.shipping-rates--display-rates {
  display: block;
}

.heading-wrapper--shipping-rates {
  margin-left: 0;
  margin-right: 0;
}
@media only screen and (max-width: 798px) {
  .heading-wrapper--shipping-rates {
    align-items: center;
  }
}

.shipping-rates__title {
  padding-top: 45px;
}
@media only screen and (max-width: 798px) {
  .shipping-rates__title {
    padding-top: 25px;
  }
}

.shipping-calculator__response ul > li {
  padding-bottom: 20px;
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 798px) {
  .shipping-calculator__response ul > li {
    justify-content: center;
  }
}
.shipping-calculator__response ul > li:last-child {
  padding-bottom: 0;
}
.shipping-calculator__response ul > li .icon {
  margin-right: 10px;
}
.shipping-calculator__response .shipping-calculator__message {
  padding-bottom: 20px;
}

.shipping_calculator__response--visible {
  display: block;
}

.cart__price .discount-area span {
  display: inline;
}

.continue-button {
  word-wrap: break-word;
}

/* # Collection
================================================== */
.collection__filters {
  margin-bottom: 20px;
}
.collection__filters .select, .collection__filters .age-gate__select-wrapper {
  max-width: 50%;
}
@media only screen and (max-width: 480px) {
  .collection__filters .select, .collection__filters .age-gate__select-wrapper {
    max-width: 100%;
  }
}
@media only screen and (max-width: 480px) {
  .collection__filters .select:first-child, .collection__filters .age-gate__select-wrapper:first-child {
    margin-bottom: 10px;
  }
}
@media only screen and (max-width: 480px) {
  .collection__filters .select, .collection__filters .age-gate__select-wrapper,
  .collection__filters select {
    width: 100%;
  }
}
.collection__filters .is-grouped {
  justify-content: flex-end;
}
@media only screen and (max-width: 798px) {
  .collection__filters .is-grouped {
    justify-content: flex-start;
  }
}

.collection-main .banner__text {
  margin: 0;
}
.collection-main .banner__content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.collection__main {
  flex-grow: 1;
}

@media only screen and (min-width: 481px) {
  .collection__sort-by-filter {
    margin-left: 10px;
  }
}

.sub-collection-main main {
  min-height: 0;
}

.collection__loading-icon {
  width: 100%;
  display: none;
}

/* #FAQ
================================================== */
.page-faq__column-wrap {
  width: 100%;
}

@media only screen and (max-width: 798px) {
  .column-wrapper .faq:first-of-type {
    margin-bottom: 20px;
  }
}
.column-wrapper .faq-accordion {
  width: 100%;
}
.column-wrapper .faq-accordion .image-element__wrap {
  padding: 0;
}
.column-wrapper .faq-accordion .image-element__wrap:first-child {
  margin-bottom: 20px;
}
.column-wrapper .faq-title {
  margin-bottom: 10px;
}
.column-wrapper .faq-title:not(:first-child) {
  margin-top: 40px;
}
.column-wrapper .faq--image {
  padding-left: 36px;
}
@media only screen and (max-width: 798px) {
  .column-wrapper .faq--image {
    padding-left: 0;
    padding-top: 20px;
  }
}

.accordion dt,
.faq-accordion dt {
  border-bottom: 1px solid {{ settings.border_color }};
  position: relative;
}
.accordion dt a,
.accordion dt button,
.faq-accordion dt a,
.faq-accordion dt button {
  background: none;
  border: 0;
  color: {{ settings.regular_color }};
  cursor: pointer;
  display: block;
  font-size: 1.2rem;
  padding: 20px 20px 20px 40px;
  text-align: left;
  width: 100%;
  transition: background-color 0.2s linear;
}
.accordion dt .accordion [aria-expanded=true] small,
.accordion dt .accordion-style--carets[aria-expanded=true] .icon,
.faq-accordion dt .accordion [aria-expanded=true] small,
.faq-accordion dt .accordion-style--carets[aria-expanded=true] .icon {
  transform: translateY(-60%) rotate(180deg);
}
.accordion dt small,
.accordion dt .icon,
.faq-accordion dt small,
.faq-accordion dt .icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.2s linear;
}
.accordion dt .accordion-style--plus_and_minus .icon,
.faq-accordion dt .accordion-style--plus_and_minus .icon {
  display: none;
}
.accordion dt .accordion-style--plus_and_minus .icon.icon--active,
.faq-accordion dt .accordion-style--plus_and_minus .icon.icon--active {
  display: block;
}

.accordion dt a[aria-expanded=true],
.accordion dt a:hover,
.faq-accordion dt button[aria-expanded=true],
.faq-accordion dt button:hover {
  background-color: {{ settings.heading_color | color_modify: 'alpha', 0.03 }};
}

.accordion dd,
.faq-accordion dd {
  display: none;
  padding: 20px 20px 20px 40px;
}

.faq-accordion dt .icon {
  color: {{ settings.link_color }};
}

.accordion dt:first-of-type small,
.accordion dt:first-of-type [aria-expanded=true] small {
  transform: translateY(-60%) rotate(270deg);
}
.accordion dt:first-of-type [aria-expanded=false] small {
  transform: translateY(-60%) rotate(180deg);
}

.accordion dd {
  margin-left: 0;
  display: none;
}
.accordion dd[aria-hidden=false] {
  display: block;
}

.accordion dt a small.right {
  font-family: "Arial"; /* Force font family to ensure accordion carets are displayed on iOS devices */
}

.accordion dd:not([aria-hidden=true]):first-of-type {
  display: block;
}

.accordion [aria-expanded=false] small {
  transform: translateY(-60%) rotate(180deg);
}

.accordion [aria-expanded=true] small {
  transform: translateY(-60%) rotate(270deg);
}

/* # Password page
================================================== */
#shopify-section-password-template {
  height: 100%;
}

#password-page-background {
  height: 100%;
  position: fixed;
  width: 100%;
  overflow: auto;
}

.password-page__newsletter {
  margin-top: 50px;
}
.password-page__newsletter .newsletter__title,
.password-page__newsletter .newsletter__text {
  color: inherit !important;
}
.password-page__newsletter p {
  margin-bottom: 20px;
}
.password-page__newsletter .newsletter-section {
  width: 100%;
}
.password-page__newsletter .newsletter__wrapper {
  max-width: 100%;
}

.password-page__social-media li {
  margin-right: 10px;
}
.password-page__social-media li .icon {
  height: 2rem;
  width: 2rem;
}

.modal-container__password #password {
  width: 100%;
}

#password-container {
  height: 100%;
}

@media only screen and (max-width: 1024px) {
  .password-page {
    min-width: 350px;
  }
}
@media only screen and (max-width: 798px) {
  .password-page {
    min-width: 90%;
  }
}
.password-page .password-page-message {
  font-size: 3em;
  line-height: 1.2;
  padding: 12px 0;
}
@media only screen and (max-width: 798px) {
  .password-page .password-page-message {
    font-size: 2em;
  }
}
.password-page .password-logo {
  max-width: 200px;
  margin: 0 auto;
}
.password-page .password-title {
  font-family: {{ settings.logo__font.family }}, {{ settings.logo__font.fallback_families }};
  font-weight: {{ settings.logo__font.weight }};
  font-style: {{ settings.logo__font.style }};
  font-size: {{ settings.logo_font_size | append: 'px' }};
  text-transform: {{ settings.logo_font_style }};
}
.password-page .overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  max-height: 100%;
  top: 0;
  left: 0;
  background: {{ settings.shop_bg_color | color_modify: 'alpha', 0.95 }};
}
.password-page .overlay-close {
  width: 150px;
  height: 50px;
  position: absolute;
  right: 10px;
  top: 10px;
  border: thin solid {{ settings.border_color }};
  font-size: 14px;
}
.password-page .overlay-data {
  opacity: 0;
  visibility: hidden;
}
.password-page .overlay-open {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.5s;
}

.modal-close {
  background: 0 0;
  height: 40px;
  position: fixed;
  right: 20px;
  top: 20px;
  width: 40px;
}
.modal-close:before, .modal-close:after {
  background-color: {{ settings.regular_color }};
}

.storefront-password-form {
  padding-bottom: 30px;
  overflow: auto;
  color: {{ settings.regular_color }};
}

.storefront-password-form input#password {
  padding: 10px;
  margin: 20px 0;
  width: 100%;
}

.storefront-password-form label {
  font-size: 0.9em;
  margin: 0 0 1em 0;
  text-align: center;
}

.storefront-password-form .actions {
  display: inline-block;
}

.storefront-password-form #password {
  width: 50%;
  display: inline-block;
}
@media only screen and (max-width: 480px) {
  .storefront-password-form #password {
    width: 100%;
  }
}

#owner {
  font-size: 0.9em;
  margin-top: -1em;
  opacity: 0.8;
  color: {{ settings.regular_color }};
}

/* Reset password page */
.reset-password {
  padding: 40px 0;
}

.reset-password__message {
  margin: 20px 0;
}

.pet-print-face-shelter-meals {
  color: #5461c8;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 15px 0;
}

.pet-print-face-shelter-meals .icon {
    width: 50px;
    margin-right: 8px;
}

.pet-print-face-shelter-meals .icon img {  
  height: auto;
  max-width: 100%;
}


.pet-create__button {
  background-color:  {{ settings.button_secondary_bg_color }};
  color:{{settings.button_secondary_text_color}};
  border-color: {{ settings.button_secondary_border_color }};
  border-radius:{{settings.button_secondary_border_radius | append: 'px'}};
  cursor: pointer;
  padding:10px 30px;
  transition:background .3s ease;
  text-transform:uppercase;
  font-weight:700;
  font-size:16px;
  font-family:{{button_font.family}};
  border: 1px solid;
  width:50%;
}
@media screen and (max-width:486px ) {
  .pet-create__button{
    width:100%;
  }
}

.pet-create__button:hover {
  background-color: {{ settings.button_secondary_bg_color--highlight }} !important;
  color: {{ settings.button_secondary_text_color }} !important;
  border-color: {{ settings.button_secondary_border_color--highlight }} !important;
  border-radius:{{settings.button_secondary_border_radius | append: 'px'}};
  cursor: pointer;
}

#add-to-cart-button {
  background-color: {{settings.button_cart_bg_color}};
  padding: 10px 20px;
  max-width: 300px;
  border: 1px solid {{settings.button_cart_border_color}};
  border-radius: 24px;
  color: {{settings.button_cart_text_color}};
  font-size: 20px;
  font-family: "Inter";
  font-weight: 600;
  letter-spacing: 3px;
  margin-top:30px;
}
#add-to-cart-button:hover{
  background-color:{{settings.button_cart_bg_color--highlight}};
  border: 1px solid {{settings.button_cart_border_color--highlight}};
  color:{{settings.button_cart_text_color--highlight}}
}
@media (max-width: 798px) {
  #add-to-cart-button {
    max-width:100%;
  }
}

.selector-wrapper{
  flex-direction: row;
  gap:10px;
  align-items:baseline;
}
.selector-wrapper label{
  font-weight:bold;
}
.selector-wrapper label::after{
  content:":";
}
.select select{
  border:1px solid black;
  border-radius:8px;
  font-size:17px;
}
.select select:hover{
  border:1px solid black;
  border-radius:8px;
}

.sizeChartBox {
  display: flex;
  justify-content: space-between;
}
.popup-chart-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  /* Ensure the popup appears on top of other content */
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  /* Dark semi-transparent background */
  z-index: 9998;
  /* Place the overlay behind the modal content */
}

.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 50%;
}
.sizeChartImageContainer{
  overflow-y:auto;
  max-height:572px;
}

.sizeChartImageContainer::-webkit-scrollbar-track {
  background-color: #f1f1f1; 
}

/* Scrollbar Handle */
.sizeChartImageContainer::-webkit-scrollbar-thumb {
  background-color: #888; 
  border-radius: 10px; 
}

/* Scrollbar Handle on hover */
.sizeChartImageContainer::-webkit-scrollbar-thumb:hover {
  background-color: #555; 
}


.pajamas-new-size-chart-open-header{
  font-family:{{settings.regular_font.family}}
  font-weight:600;
  font-style:normal;
  font-size:21px;
  text-transform:none;
  line-height:1.5;
  display:block;
}

.new-pajamas-size-chart-open-inner-wrap {
  text-align: center;
  /* Center align content */
  position: relative;
  /* Required for absolute positioning of close button */
}

.delete-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: gray;
  border: 1px solid gray;
  color: white;
  border-radius: 50%;
  padding: 0 8px 3px;
  cursor: pointer;
}
.sizeChartIcon{
  height: 0.7rem !important;
}
.sizeChartLinkText{
  font-size:1rem;
  font-weight:400;
  line-height:1.25;
}
@media screen and (min-width: 536px) and (max-width: 900px) {
  .modal-content {
    max-width: 100%; 
  }
}

@media screen and (max-width: 536px) {
  .sizeChartBox {
    display: flex;
    flex-direction: column-reverse;
    justify-content: space-between;
    margin: 10px 0;
  }
  .sizeChartIcon{
    height: 0.7rem !important;
  }

  .modal-content {
    max-width: 100%;
    top:30%;
    left:0;
    transform:unset;
    padding:30px;
  }
}

@media screen and (min-width: 1285px) {
  .modal-content {
    top:50%;
  }
}

.localeSelectorContainer{
  width:100%;
}

@media screen and (max-width:480px) {
  .locale-selectors__selector {
    width:250px !important;
  }
  .selector-wrapper{ 
    flex-direction: column;
    gap:0;
  }
}

/* Cart Rush */

.cart-rush {
  margin-left: auto; 
  font-size: 16px; 
  width: 100%;
  max-width: 380px;
}

@media screen and (max-width: 767px) {
  .cart-rush {
    max-width: none; 
    font-size: 14px; 
  }
}

.cart-rush--option, .cart-rush--option-pdp {
  display: flex;
  align-items: flex-start;
  margin-bottom: 5px;
  max-width: 100%;
}
.cart-rush--variant, .cart-rush--variant-pdp {
  width: auto;
  margin: 0 5px 0 0;
}

.cart-rush--variant input + i, .cart-rush--variant-pdp input + i {
  position: relative;
  width: 14px;
  height: 14px;
  display: block;
  top: 3px; 
}

@media screen and (max-width: 767px) {
  .cart-rush--variant input + i, .cart-rush--variant-pdp input + i {
    top: 2px; 
  }
}

.cart-rush--variant input + i:before, .cart-rush--variant-pdp input + i:before {
  content: ''; 
  display: block; 
  width: 14px; 
  height: 14px; 
  border-radius: 9999px; 
  border: 1px solid #5461c8 !important; 
}

.cart-rush--variant input + i:after, .cart-rush--variant-pdp input + i:after {
  content: ''; 
  width: 6px; 
  height: 6px; 
  border-radius: 9999px; 
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
  background: #5461c8; 
  display: none; 
}

.cart-rush--variant input:checked + i:after, .cart-rush--variant-pdp input:checked + i:after {
  display: block; 
}

.cart-rush--option label, .cart-rush--option-pdp label {
  flex: 1;
  text-align: left; 
  cursor: pointer; 
}

.cart-rush--price {
  text-align: right;
  font-weight: bold;
  margin-left: 20px; 
  position: relative;
  top: 2px; 
  color: #5461c8; 
}

.cart-rush--price s {
  /* opacity: 0.8; */
  font-weight: 400; 
  color:#6a696b; 
}

.rush-disclaimer {  
  float: right;
  clear: right;
  text-align: right;
  font-size: 12px;
  width: 236px;
  font-family: "omnes,sans-serif !important";
}
body.page-post-order-warranty .rush-disclaimer{
  margin: 10px 0;
  width: 100%;
  text-align: left;
}
.cart__disclaimer-text {
  /* max-width: 300px; */
  font-size: 14px;
  font-style: italic;
  color: #999;
  text-align: right;
  margin: 10px 0 10px auto;
}

@media screen and (max-width: 767px) {
  .cart__disclaimer-text {
    width: 100%;
    text-align: center;
    margin: 10px auto;
  }
}

/* # Font-Face
================================================== */
/*  This is the proper syntax for an @font-face file.
    Upload your font files to Assets and then
    copy your FontName into code below and remove
    comment brackets */
/*  @font-face {
      font-family: 'FontName';
      src: url('FontName.eot');
      src: url('FontName.eot?iefix') format('eot'),
           url('FontName.woff') format('woff'),
           url('FontName.ttf') format('truetype'),
           url('FontName.svg#webfontZam02nTh') format('svg');
      font-weight: normal;
      font-style: normal; }
*/
/* # Custom Styles
================================================== */

.uploaderHeading{
  display:block;
  font-size:1.17em;
  margin:1em 0 1em 0;
}

/* Post Purchase RUSH */
.post-order-rush-container {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
.post-order-rush-container .post-order-rush-image {
  width: 60%;
}
.post-order-rush-container .post-order-rush-details {
  width: 40%;
}
@media (max-width: 798px) {
  .post-order-rush-container {
    flex-wrap: wrap;
  }
  .post-order-rush-container .post-order-rush-image {
    width: 100%;
  }
  .post-order-rush-container .post-order-rush-details {
    width: 100%;
  }
}

.post-order-rush-container .post-order-rush-details .rush-description {
  font-size: .8em;
  margin-bottom: 30px;
}
.post-order-rush-container .post-order-rush-details .cart-rush--options {
  display: flex;
  flex-wrap: wrap;
  max-width: unset;
}

.cart-rush {
  margin-left: auto; 
  font-size: 16px; 
  width: 100%;
  max-width: 380px;
}

@media screen and (max-width: 767px) {
  .cart-rush {
    max-width: none; 
    font-size: 14px; 
  }
}

.cart-rush--option, .cart-rush--option-pdp {
  display: flex;
  /* justify-content: flex-start; */
  align-items: flex-start;
  /* align-content: center; */
  margin-bottom: 5px;
  /* width: 236px; */
  max-width: 100%;
  /* float: right; */
}
.cart-rush--options .cart-rush--option, .cart-rush--options-pdp .cart-rush--option-pdp {
  width: 100%;
}

.cart-rush--variant, .cart-rush--variant-pdp {
  width: auto;
  margin: 0 5px 0 0;
}

.cart-rush--variant input + i, .cart-rush--variant-pdp input + i {
  position: relative;
  width: 14px;
  height: 14px;
  display: block;
  top: 3px; 
}

@media screen and (max-width: 767px) {
  .cart-rush--variant input + i, .cart-rush--variant-pdp input + i {
    top: 2px; 
  }
}

.cart-rush--variant input + i:before, .cart-rush--variant-pdp input + i:before {
  content: ''; 
  display: block; 
  width: 14px; 
  height: 14px; 
  border-radius: 9999px; 
  border: 1px solid #5461c8 !important; 
}

.cart-rush--variant input + i:after, .cart-rush--variant-pdp input + i:after {
  content: ''; 
  width: 6px; 
  height: 6px; 
  border-radius: 9999px; 
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
  background: #5461c8; 
  display: none; 
}

.cart-rush--variant input:checked + i:after, .cart-rush--variant-pdp input:checked + i:after {
  display: block; 
}

.shop_pay_logo {
  height: 14px;
  width: 59px;
  vertical-align: middle;
  margin-bottom: 1px;
}
/* .button--add-to-cart {
  padding: 12px 30px 10px !important;
  font-size: 15px !important;
  min-width: 300px;
  text-align: center;
  letter-spacing: 1px !important;
  line-height: 1 !important;
  max-width:100% !important;
}

.button--add-to-cart .text {
  font-size: 15px !important;
  letter-spacing: 1px !important;
  text-transform: uppercase !important;
  font-weight: 600 !important;
} */