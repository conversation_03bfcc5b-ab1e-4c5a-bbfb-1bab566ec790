.uploadOuter .dragBox label {
  border: 3px solid #5461c8;
  border-radius: 16px;
  padding: 8px 20px;
  color: #5461c8;
}
.uploadOuter .dragBox strong {
  margin: 0 20px;
  font-weight: 500;
}
.uploadOuter .dragBoxUnique label {
  border: 3px solid #5461c8;
  border-radius: 16px;
  padding: 8px 20px;
  color: #5461c8;
}
.uploadOuter .dragBoxUnique strong {
  margin: 0 20px;
  font-weight: 500;
}

.dragBoxUnique {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 10px 0;
  margin: 0 auto;
  position: relative;
  text-align: center;
  font-weight: 500;
  color: #000;
  transition: transform 0.3s;
  border-radius: 20px;
  border: 1px solid #000;
  background: #edebf0;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  font-size: 22px;
  input[type="file"] {
    position: absolute;
    border-radius: 20px;
    height: 100%;
    width: 100%;
    opacity: 0;
    top: 0;
    left: 0;
    padding: 0;
  }
}
.delete-button-unique {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 5px;
  top: 5px;
  cursor: pointer;
  height: 25px;
  width: 25px;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(182, 178, 178, 0.6);
  color: white;
}
.image-uploader .selected-image-container img {
  width: 200px;
  height: 200px;
}
.image-uploader .selected-image-container {
  display: block;
  margin: auto auto 15px;
  position: relative;
}
.image-uploader {
  background-color: #eaeaea;
  border-radius: 20px;
  margin: auto;
}
.cropHeading {
  text-align: center;
  display: none;
  font-weight: bold;
  padding: 0px 15px;
}
.finishCropButton {
  display: flex;
  justify-content: center;
  display: none;
}
.finishCropButton button {
  cursor: pointer;
  background: #5461c8;
  padding: 12px 20px;
  width: 300px;
  max-width: 100%;
  border: 1px solid#5461c8;
  border-radius: 24px;
  color: #ffffff;
  font-size: 15px;
  font-family: Inter;
  font-weight: 600;
  letter-spacing: 1px;
  transition: background 0.15s ease;
}
.finishCropButton button:hover {
  background: #8089cf;
  border: 1px solid#8089cf;
}

.hide {
  display: none;
}
.cropper-crop-box,
.cropper-view-box {
  border-radius: 50%; /* Set border-radius to 50% for a circular shape */
}

.cropper-view-box {
  box-shadow: 0 0 0 1px #39f; /* Optional: Add a border around the circle */
  outline: 0; /* Optional: Remove outline for a cleaner look */
}
.image-uploader {
  position: relative;
}

.close-button {
  display: none;
  position: absolute;
  top: -8px;
  right: -5px;
  cursor: pointer;
  background-color: #5461c8;
  border: 1px solid #5461c8;
  border-radius: 50%;
  font-size: 16px;
  padding: 2px;
  margin: 0;
  line-height: 1;
  color: white;
}

.close-button:hover {
  color: white;
  border: 1px solid #5461c8;
  padding: 2px;
}
.close-button-simple {
  display: none;
  position: absolute;
  top: -8px;
  right: -5px;
  cursor: pointer;
  background-color: #5461c8;
  border: 1px solid #5461c8;
  border-radius: 50%;
  font-size: 16px;
  padding: 2px;
  margin: 0;
  line-height: 1;
  color: white;
}

.close-button-simple:hover {
  color: white;
  border: 1px solid #5461c8;
  padding: 2px;
}
.dragDropText {
  font-weight: 0;
}
#previewImageSimple {
  width: 250px;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
}
@media (max-width: 933px) {
  .orText {
    display: none;
  }
  .dragBoxText {
    display: none;
  }
  .dragDropText {
    display: none;
  }
  .dragBoxUnique {
    display: flex;
    flex-direction: column;
  }
  .dragBoxUnique {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 10px;
    margin: 0 auto;
    position: relative;
    text-align: center;
    font-weight: 500;
    transition: transform 0.3s;
    font-size: 22px;
    border: 0;
    background: transparent;
    box-shadow: none;
    input[type="file"] {
      position: absolute;
      border-radius: 20px;
      height: 100%;
      width: 100%;
      opacity: 0;
      top: 0;
      left: 0;
      padding: 0;
    }
  }
}
