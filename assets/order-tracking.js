/**
 * Order Tracking JavaScript
 * Handles form submission and API calls for order status tracking
 */

(function () {
  "use strict";

  // Configuration - Update these with your actual API details
  const API_CONFIG = {
    // Replace with your actual third-party API endpoint
    endpoint: "https://d50c-113-192-46-158.ngrok-free.app/order-tracking",
    // Add any required headers or authentication
    headers: {
      "Content-Type": "application/json",
      Authorization:
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJjdXN0b21lci1hY2Nlc3MiLCJpc3MiOiJjb3JzLWJhY2tlbmQiLCJzdWIiOiJjdXN0b21lciIsImlhdCI6MTc1MTU0MDYzMiwiZXhwIjoxNzgzMDc2NjMyfQ.O/ptJy6ylYtXPTsADaCyR8+C01WQ0nONwgFfofJQZc4", // Uncomment and add your API key if needed
    },
  };

  // DOM elements
  let form,
    orderNumberInput,
    customerEmailInput,
    successMessage,
    errorMessage,
    resultsContainer,
    trackButton;

  // Initialize when DOM is loaded
  function init() {
    // Get DOM elements
    form = document.getElementById("order-tracking-form");
    orderNumberInput = document.getElementById("order-number");
    customerEmailInput = document.getElementById("customer-email");
    successMessage = document.getElementById("tracking-success");
    errorMessage = document.getElementById("tracking-error");
    resultsContainer = document.getElementById("order-status-results");
    trackButton = document.getElementById("track-order-btn");

    if (!form) {
      console.warn("Order tracking form not found");
      return;
    }

    // Bind event listeners
    form.addEventListener("submit", handleFormSubmit);

    // Add input validation
    orderNumberInput.addEventListener("input", validateOrderNumber);
    customerEmailInput.addEventListener("input", validateEmail);
  }

  // Handle form submission
  function handleFormSubmit(event) {
    event.preventDefault();

    // Clear previous messages
    hideMessages();

    // Get form data
    const orderNumber = orderNumberInput.value.trim();
    const customerEmail = customerEmailInput.value.trim();

    // Validate inputs
    if (!validateForm(orderNumber, customerEmail)) {
      return;
    }

    // Show loading state
    setLoadingState(true);

    // Make API call
    trackOrder(orderNumber, customerEmail);
  }

  // Validate form inputs
  function validateForm(orderNumber, customerEmail) {
    let isValid = true;
    let errorMsg = "";

    if (!orderNumber) {
      errorMsg += "Order number is required. ";
      isValid = false;
    }

    if (!customerEmail) {
      errorMsg += "Email address is required. ";
      isValid = false;
    } else if (!isValidEmail(customerEmail)) {
      errorMsg += "Please enter a valid email address. ";
      isValid = false;
    }

    if (!isValid) {
      showError(errorMsg);
    }

    return isValid;
  }

  // Validate order number format (customize as needed)
  function validateOrderNumber() {
    const orderNumber = orderNumberInput.value.trim();

    // Add your order number validation logic here
    // Example: Check if it matches a specific pattern
    if (orderNumber && orderNumber.length < 3) {
      orderNumberInput.setCustomValidity(
        "Order number must be at least 3 characters long"
      );
    } else {
      orderNumberInput.setCustomValidity("");
    }
  }

  // Validate email format
  function validateEmail() {
    const email = customerEmailInput.value.trim();

    if (email && !isValidEmail(email)) {
      customerEmailInput.setCustomValidity(
        "Please enter a valid email address"
      );
    } else {
      customerEmailInput.setCustomValidity("");
    }
  }

  // Check if email is valid
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Make API call to track order
  async function trackOrder(orderNumber, customerEmail) {
    try {
      const requestData = {
        orderNumber: orderNumber,
        customerEmail: customerEmail,
      };

      const response = await fetch(API_CONFIG.endpoint, {
        method: "POST",
        headers: API_CONFIG.headers,
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Handle successful response
      handleTrackingSuccess(data);
    } catch (error) {
      console.error("Error tracking order:", error);
      handleTrackingError(error);
    } finally {
      setLoadingState(false);
    }
  }

  // Handle successful tracking response
  function handleTrackingSuccess(data) {
    // Hide error messages
    hideMessages();

    // Show success message
    showSuccess("Order found! Here are the details:");

    // Display order information
    displayOrderStatus(data);
  }

  // Handle tracking error
  function handleTrackingError(error) {
    let errorMsg = "Unable to track your order. ";

    if (error.message.includes("404")) {
      errorMsg =
        "Order not found. Please check your order number and email address.";
    } else if (error.message.includes("500")) {
      errorMsg = "Server error. Please try again later.";
    } else if (error.name === "TypeError" && error.message.includes("fetch")) {
      errorMsg =
        "Network error. Please check your internet connection and try again.";
    } else {
      errorMsg += "Please try again or contact customer support.";
    }

    showError(errorMsg);
  }

  // Display order status information
  function displayOrderStatus(orderData) {
    // Customize this based on your API response structure
    const statusContent = resultsContainer.querySelector(
      ".order-status-content"
    );

    let html = "<h3>Order Status</h3>";

    // Example structure - modify based on your API response
    if (orderData.orderNumber) {
      html += `<div class="status-item">
        <span class="status-label">Order Number:</span>
        <span>${orderData.orderNumber}</span>
      </div>`;
    }

    if (orderData.status) {
      html += `<div class="status-item">
        <span class="status-label">Status:</span>
        <span>${orderData.status}</span>
      </div>`;
    }

    if (orderData.trackingNumber) {
      html += `<div class="status-item">
        <span class="status-label">Tracking Number:</span>
        <span>${orderData.trackingNumber}</span>
      </div>`;
    }

    if (orderData.estimatedDelivery) {
      html += `<div class="status-item">
        <span class="status-label">Estimated Delivery:</span>
        <span>${orderData.estimatedDelivery}</span>
      </div>`;
    }

    if (orderData.lastUpdate) {
      html += `<div class="status-item">
        <span class="status-label">Last Update:</span>
        <span>${orderData.lastUpdate}</span>
      </div>`;
    }

    // Add tracking history if available
    if (orderData.trackingHistory && orderData.trackingHistory.length > 0) {
      html += "<h4>Tracking History</h4>";
      orderData.trackingHistory.forEach((event) => {
        html += `<div class="status-item">
          <span class="status-label">${event.date}:</span>
          <span>${event.description}</span>
        </div>`;
      });
    }

    statusContent.innerHTML = html;
    resultsContainer.style.display = "block";
  }

  // Show success message
  function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = "block";
    errorMessage.style.display = "none";
  }

  // Show error message
  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = "block";
    successMessage.style.display = "none";
    resultsContainer.style.display = "none";
  }

  // Hide all messages
  function hideMessages() {
    successMessage.style.display = "none";
    errorMessage.style.display = "none";
  }

  // Set loading state
  function setLoadingState(isLoading) {
    if (isLoading) {
      trackButton.textContent = "Tracking...";
      trackButton.disabled = true;
      form.style.opacity = "0.7";
    } else {
      trackButton.textContent = "Track Order";
      trackButton.disabled = false;
      form.style.opacity = "1";
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }
})();
