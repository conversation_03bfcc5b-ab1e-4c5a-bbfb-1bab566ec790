/******/ (() => {
  // webpackBootstrap
  var __webpack_exports__ = {};
  /* eslint-disable */

  window.PXUTheme.jsAjaxCart = {
    init: function ($section) {
      // Add settings from schema to current object
      window.PXUTheme.jsAjaxCart = $.extend(
        this,
        window.PXUTheme.getSectionData($section)
      );

      if (isScreenSizeLarge() || this.cart_action == "drawer") {
        this.initializeAjaxCart();
      } else {
        this.initializeAjaxCartOnMobile();
      }

      if (this.cart_action == "drawer") {
        this.ajaxCartDrawer = $("[data-ajax-cart-drawer]");

        $(document).on("click", "[data-ajax-cart-trigger]", function (e) {
          e.preventDefault();
          window.PXUTheme.jsAjaxCart.showDrawer();

          return false;
        });
      } else if (this.cart_action == "mini_cart") {
        this.showMiniCartOnHover();
      }

      $(document).on("click", ".ajax-submit", function (e) {
        e.preventDefault();
        const $addToCartForm = $(this).closest("form");
        window.PXUTheme.jsAjaxCart.addToCart($addToCartForm);

        return false;
      });

      $(document).on("click", "[data-ajax-cart-delete]", function (e) {
        e.preventDefault();
        const lineID = $(this).parents("[data-line-item]").data("line-item");
        window.PXUTheme.jsAjaxCart.removeFromCart(lineID);

        if (window.PXUTheme.jsCart) {
          window.PXUTheme.jsCart.removeFromCart(lineID);
        }

        return false;
      });

      $(document).on("click", "[data-ajax-cart-close]", function (e) {
        e.preventDefault();
        window.PXUTheme.jsAjaxCart.hideDrawer();
        window.PXUTheme.jsAjaxCart.hideMiniCart();

        return false;
      });
    },
    showMiniCartOnHover: function () {
      const $el = $("[data-ajax-cart-trigger]");

      $el.hover(
        function () {
          if (
            window.PXUTheme.theme_settings.header_layout == "centered" &&
            $(".header-sticky-wrapper").hasClass("is-sticky")
          ) {
            $(".header-sticky-wrapper [data-ajax-cart-trigger]").addClass(
              "show-mini-cart"
            );
          } else {
            $el.addClass("show-mini-cart");
          }
        },
        function () {
          $el.removeClass("show-mini-cart");
        }
      );
    },
    hideMiniCart: function () {
      if (this.cart_action != "mini_cart") return false;
      const $el = $("[data-ajax-cart-close]").parents(
        "[data-ajax-cart-trigger]"
      );
      $el.removeClass("show-mini-cart");
    },
    toggleMiniCart: function () {
      const $el = $(".mobile-header [data-ajax-cart-trigger]");

      // Removes url to the cart page so user is not redirected
      $el.attr("href", "#");

      $el.off("touchstart").on("touchstart", function (e) {
        // If user clicks inside the element, do nothing
        if (e.target.closest("[data-ajax-cart-mini_cart]")) {
          return;
        }

        // Loads content into ajaxCart container for mobile header
        window.PXUTheme.jsAjaxCart.initializeAjaxCartOnMobile();

        // If user clicks outside the element, toggle the mini cart
        $el.toggleClass("show-mini-cart");
      });
    },
    showDrawer: function () {
      if (this.cart_action != "drawer") return false;
      this.ajaxCartDrawer.addClass("is-visible");
      $(".ajax-cart__overlay").addClass("is-visible");
    },
    hideDrawer: function () {
      if (this.cart_action != "drawer") return false;
      this.ajaxCartDrawer.removeClass("is-visible");
      $(".ajax-cart__overlay").removeClass("is-visible");
    },
    removeFromCart: function (lineID, callback) {
      $.ajax({
        type: "POST",
        url: "/cart/change.js",
        data: "quantity=0&line=" + lineID,
        dataType: "json",
        success: function (cart) {
          window.PXUTheme.jsAjaxCart.updateView();
          if (typeof window.PXUTheme.jsAjaxCart !== "undefined") {
            window.checkWarranty(cart, function (res) {
              var updatedCart = res ? res : cart;
              $cartItem.addClass("animated zoomOut");
              $cartItem.remove();
              window.PXUTheme.jsAjaxCart.updateView();
              if (typeof window.PXUTheme.jsCart !== "undefined") {
                window.PXUTheme.jsAjaxCart.updateView();
              }
            });
          }
        },
        error: function (XMLHttpRequest, textStatus) {
          var response = eval("(" + XMLHttpRequest.responseText + ")");
          response = response.description;
        },
      });
    },
    initializeAjaxCart: function () {
      window.PXUTheme.asyncView
        .load(
          window.PXUTheme.routes.cart_url, // template name
          "ajax" // view name (suffix)
        )
        .done(({ html, options }) => {
          $("[data-ajax-cart-content]").html(html.content);

          // Converting the currencies
          if (window.PXUTheme.currencyConverter) {
            window.PXUTheme.currencyConverter.convertCurrencies();
          }
        })
        .fail(() => {
          // some error handling
        });
    },
    initializeAjaxCartOnMobile: function () {
      this.toggleMiniCart();

      window.PXUTheme.asyncView
        .load(
          window.PXUTheme.routes.cart_url, // template name
          "ajax" // view name (suffix)
        )
        .done(({ html, options }) => {
          $(".mobile-header [data-ajax-cart-content]").html(html.content);
        })
        .fail(() => {
          // some error handling
        });
    },
    addToCart: function ($addToCartForm) {
      const $addToCartBtn = $addToCartForm.find(".button--add-to-cart");
      this.recipientForm = $addToCartForm[0].querySelector(
        "[data-recipient-form]"
      );

      const validateAddToCartForm = function ($addToCartForm) {
        const hiddenPropertiesInputs = $addToCartForm[0].querySelectorAll(
          'input[type="hidden"][name^="properties"]'
        );
        if (hiddenPropertiesInputs.length === 0) {
          console.log("No 'properties' fields found in the form.");
          return false;
        }
        let hasImageInput = false;
        let hasGiftCardValue = false;

        hiddenPropertiesInputs.forEach(input => {
          // if (input.name.includes('_option_Gift_Card_Value') || input.name.includes('_rush_product')) {
          //   hasGiftCardValue = true;
          // }
          if (input.name.includes('_Bypass_Attribute') ) {
            hasGiftCardValue = true;
          }

          if (input.name.includes('image_url') && input.value.trim() !== "") {
            hasImageInput = true;
          }
        });

        // Bypass image check if a gift card value exists
        if (hasGiftCardValue) {
          return true;
        }

        if (hasImageInput) {
          return true;
        } else {
          console.log("At least one 'image_url' input is required.");
          return false;
        }
      };
      if (!validateAddToCartForm($addToCartForm)) {
        alert("something went wrong.");
        window.location.reload();
        return;
      }
      console.log("Proceeding with add-to-cart functionality...");
      if (this.recipientForm) {
        this.recipientForm.classList.remove("recipient-form--has-errors");
      }

      $addToCartForm.removeClass("shopify-product-form--unselected-error");

      if ($addToCartBtn[0].hasAttribute("data-options-unselected")) {
        const cartWarning = `<p class="cart-warning__message animated bounceIn">${window.PXUTheme.translation.select_variant}</p>`;

        $(".warning").remove();

        $addToCartForm
          .addClass("shopify-product-form--unselected-error")
          .find(".cart-warning")
          .html(cartWarning);

        $addToCartBtn.removeAttr("disabled").removeClass("disabled");

        $addToCartBtn.find(".icon").removeClass("zoomOut").addClass("zoomIn");

        $addToCartBtn
          .find("span:not(.icon)")
          .text($addToCartBtn.data("label"))
          .removeClass("zoomOut")
          .addClass("zoomIn");
      } else {
        if ($addToCartBtn) {
          //Serialize the Form
          var values = {};
          $.each($("form").serializeArray(), function (i, field) {
            values[field.name] = field.value;
          });
        }

        function addedToCart() {
          if (!isScreenSizeLarge()) {
            $el = $(".mobile-header [data-ajax-cart-trigger]");
            Shopify.theme.scrollToTop($el);
          } else {
            $el = $("[data-ajax-cart-trigger]");
          }

          $el
            .addClass("show-mini-cart")
            .delay(3000)
            .queue(function (next) {
              $(".theme-ajax-cart--header-classic")
                .addClass("hide-mini-cart")
                .delay(1000)
                .queue(function (next) {
                  $(".theme-ajax-cart--header-classic")
                    .removeClass("hide-mini-cart")
                    .removeAttr("style");
                  $el.removeClass("show-mini-cart");
                  next();
                });
              next();
            });

          $addToCartBtn.find("span").removeClass("fadeInDown");
        }

        window.setTimeout(function () {
          $addToCartBtn.removeAttr("disabled").removeClass("disabled");
          $addToCartBtn.find(".checkmark").removeClass("checkmark-active");
          $addToCartBtn
            .find(".text, .icon")
            .removeClass("zoomOut")
            .addClass("fadeInDown");
          $addToCartBtn.on(
            "webkitAnimationEnd oanimationend msAnimationEnd animationend",
            addedToCart
          );

          if ($addToCartBtn.closest("#eligible-for-rush").length > 0) {
            $addToCartBtn.closest("form").hide();
            $addToCartBtn
              .closest("#eligible-for-rush")
              .find("#eligible-for-rush-original-in-cart")
              .show();
          }
        }, 2000);

        const apiToken = document.querySelector(".apiKey")?.value;

        // start free product code
        let product_id = $("#free_product_div").data("product_id");
        let mainUrl = "";
        let hostUrl = window.location.host;
        const currentUrl = window.location.href;
        const baseUrl = currentUrl.replace(/\/[^/]*$/, "/");
        let nativeOptionsKeys = [];
        let freeInputKey = document.querySelector("#freeUniqueId");

        // check the host url for end point
        if (hostUrl == "cuddleclones-dev.myshopify.com") {
          mainUrl = "https://oms-dev.cuddleclones.com/";
        } else if (hostUrl == "cuddleclones.com") {
          mainUrl = "https://oms.cuddleclones.com/";
        } else {
          mainUrl = "https://oms-uat.cuddleclones.com/";
        }
        const endpoint = `${mainUrl}shopify_api/get_product_metafields?product_id=${product_id}`;

        // product _id in metafiled ajax
        let product_check = false;
        let variant_check = false;
        let offer_all_product = false;
        let free_product_id = "";
        let metafieldsUrl = "";
        let endpoint2 = "";

        $.ajax({
          url: endpoint,
          method: "GET",
          headers: {
            "api-token": apiToken,
          },
          success: function (response) {
            const productId = response;
            const variant_id = values["id"];
            if (productId) {
              $.each(productId?.response?.metafields, (index, value) => {
                if (value.key == "free_product_offer") {
                  var json_obj = JSON.parse(value.value);
                  $.each(json_obj, (index, val) => {
                    if (index == "product") {
                      free_product_id = val.id;
                      product_check = true;
                    }
                    if (index == "variants") {
                      $.each(val, (i, v) => {
                        if (v == variant_id) {
                          variant_check = true;
                        }
                      });
                    }
                    if (index == "for_all_product") {
                      offer_all_product = val;
                    }
                  });
                }

                if (
                  (product_check && variant_check) ||
                  (product_check && offer_all_product)
                ) {
                  var freeProductDiv =
                    document.getElementById("free_product_div");
                  if (freeProductDiv) {
                    if (product_check && variant_check) {
                      freeProductDiv.style.visibility = "visible";
                    } else if (product_check && offer_all_product) {
                      freeProductDiv.style.visibility = "visible";
                    } else {
                      freeProductDiv.style.visibility = "hidden";
                    }
                  }
                  $("#free_product_title").append("");
                  const meta_product_id = free_product_id;

                  // get handle of free product
                  endpoint2 = `${mainUrl}shopify_api/get_product?product_id=${meta_product_id}`;
                  metafieldsUrl = `${mainUrl}shopify_api/get_product_metafields?product_id=${meta_product_id}`;
                }
              });
            }
            $.ajax({
              url: endpoint2,
              method: "GET",
              headers: {
                "api-token": apiToken,
              },
              success: function (response) {
                $(".freeProductVariants").val(
                  JSON.stringify(response?.response?.product?.variants)
                );
                $(".productName").html("");
                $(".productComparePrice").html("");
                const data = response;
                const title = data?.response?.product?.title;
                const imageUrl = data?.response?.product?.image?.src;
                const compareAtPrice =
                  data?.response?.product?.variants[0]?.compare_at_price;
                $(".productName").append(title);
                $(".productImage").attr("src", imageUrl);
                $(".productComparePrice").append(`$${compareAtPrice}`);
              },
            });
            $.ajax({
              url: metafieldsUrl,
              method: "GET",
              headers: {
                "api-token": apiToken,
              },
              success: function (response) {
                metafieldsData = response?.response?.metafields;
                $(".productReviews").html("");
                $(".variantsContainer").html("");
                displayMetafields(metafieldsData);
                $(".freeProductKeys").val(JSON.stringify(nativeOptionsKeys));
              },
              error: function (error) {
                console.log("Error:", error);
              },
            });
          },
        });

        let metafieldsData = null;

        function displayMetafields(metafields) {
          let customOptions = [];
          let nativeOptions = [];
          freeInputKey.value = values["properties[_original_unique_key]"];
          metafields?.forEach((metafield) => {
            if (metafield.key === "badge") {
              let reviews = metafield.value;
              $(".productReviews").append(reviews);
            } else if (metafield.key === "custom_options") {
              customOptions = JSON.parse(metafield.value);
            } else if (metafield.key === "native_options") {
              nativeOptions = JSON.parse(metafield.value);
            }
          });
          for (var key in customOptions) {
            let option = customOptions[key];

            if (option.display && option.type === "dropdown") {
              // Create the hidden input element
              let hiddenInput = document.createElement("input");
              hiddenInput.type = "hidden";
              hiddenInput.name = `properties[${option.heading}]`;
              hiddenInput.value = option.options[0].value;
              hiddenInput.className = `${option.name}HiddenInput`;

              // Create the selector wrapper div
              let selectorWrapper = document.createElement("div");
              selectorWrapper.className = "selector-wrapper-free-product";

              // Create the label element
              let label = document.createElement("label");
              label.setAttribute("for", `data-variant-option`);
              label.setAttribute("data-variant-option-name", option.name);
              label.setAttribute(
                "data-variant-option-choose-name",
                option.name
              );
              label.innerHTML = option.heading;

              // Create the span element
              let span = document.createElement("span");
              span.className = "selectFreeVariant";
              span.setAttribute("data-dropdown-form-style", "");

              // Create the select element
              let select = document.createElement("select");
              select.className = "single-option-selector";
              select.setAttribute(
                "onchange",
                `getSelectedValue('${option.name}', this, '${option.heading}');`
              );
              select.id = `data-variant-option`;
              select.setAttribute("data-variant-option", "");
              select.setAttribute("data-variant-option-index", "");
              select.style.color = "black";

              // Create and append the default option
              const defaultOption = document.createElement("option");
              defaultOption.value = option.options[0].value; // Set default value to the first option
              defaultOption.selected = true;
              defaultOption.setAttribute(
                "data-variant-option-value-wrapper",
                ""
              );
              defaultOption.setAttribute("data-variant-option-value", "");
              defaultOption.setAttribute("data-variant-option-value-index", "");
              defaultOption.innerHTML = option.options[0].name; // Display the name of the first option

              select.appendChild(defaultOption);

              // Create and append the other options
              option.options.forEach((value, idx) => {
                if (idx !== 0) {
                  // Skip the first option as it's already added
                  const optionElement = document.createElement("option");
                  optionElement.value = value.value;
                  if (option.selected_value === value.value) {
                    optionElement.selected = true;
                    hiddenInput.value = value.value; // Update hidden input with selected value
                  }
                  optionElement.setAttribute(
                    "data-variant-option-value-wrapper",
                    ""
                  );
                  optionElement.setAttribute("data-variant-option-value", "");
                  optionElement.setAttribute(
                    "data-variant-option-value-index",
                    ""
                  );
                  optionElement.innerHTML = value.name;

                  select.appendChild(optionElement);
                }
              });

              // Append the select element to the span
              span.appendChild(select);

              // Append the label and span to the selector wrapper
              selectorWrapper.appendChild(label);
              selectorWrapper.appendChild(span);

              // Append the hidden input and selector wrapper to the desired parent element
              const targetContainer = $(".variantsContainer");
              targetContainer.append(hiddenInput);
              targetContainer.append(selectorWrapper);
            } else if (option.display && option.type === "swatches") {
              // Create the hidden input element
              let hiddenInput = document.createElement("input");
              hiddenInput.type = "hidden";
              hiddenInput.name = `properties[${option.heading}]`;
              hiddenInput.className = `${option.name}HiddenInput`;
              hiddenInput.value = option.options[0].name;

              // Create the main wrapper div
              let mainWrapper = document.createElement("div");
              mainWrapper.className = "swatches-select-free-product";
              mainWrapper.style.marginBottom = "10px";

              // Create the label wrapper div
              let labelWrapper = document.createElement("div");
              labelWrapper.className = "swatches-label-free-product";

              // Create the label element
              let label = document.createElement("label");
              label.setAttribute("for", `data-variant-option-`);
              label.setAttribute("data-variant-option-name", option.name);
              label.setAttribute(
                "data-variant-option-choose-name",
                option.name
              );

              label.innerHTML = `${option.heading}: <span id="${option.name}-selected-swatches">${option.options[0].name}</span>`;

              // Append the label to the label wrapper
              labelWrapper.appendChild(label);

              // Create the inner wrapper div
              let innerWrapper = document.createElement("div");
              innerWrapper.classList.add("free-swatches");
              innerWrapper.style.marginBottom = "15px";

              // Create the radio buttons and images
              option.options.forEach((value, index) => {
                let labelElement = document.createElement("label");
                labelElement.style.marginRight = "10px";

                let radioInput = document.createElement("input");
                radioInput.classList.add("customRadioInputs");
                radioInput.id = option.name;
                radioInput.type = "radio";
                radioInput.name = option.name;
                radioInput.value = value.name;

                if (index === 0) {
                  radioInput.checked = true;
                }

                let image = document.createElement("img");
                image.src = value.image;
                image.alt = value.name;
                image.title = value.name;
                image.width = 50;
                image.height = 50;
                image.setAttribute(
                  "onclick",
                  `selectBackground('${option.name}', '${value.name}', '${option.heading}')`
                );

                // Append the radio input and image to the label element
                labelElement.appendChild(radioInput);
                labelElement.appendChild(image);

                // Append the label element to the inner wrapper
                innerWrapper.appendChild(labelElement);
              });

              // Append the label wrapper and inner wrapper to the main wrapper
              mainWrapper.appendChild(labelWrapper);
              mainWrapper.appendChild(innerWrapper);

              // Append the hidden input and main wrapper to the desired parent element
              const targetContainer = $(".variantsContainer");
              targetContainer.append(hiddenInput);
              targetContainer.append(mainWrapper);
            } else if (option.display && option.type === "button") {
              // Create the hidden input element
              let hiddenInput = document.createElement("input");
              hiddenInput.type = "hidden";
              hiddenInput.name = `properties[${option.heading}]`;
              hiddenInput.className = `${option.name}HiddenInput`;
              hiddenInput.value = option.options[0].value;
              if (option.shape === "circle") {
                // Replace spaces with underscores in the heading
                const headingWithHyphen = option.heading.replace(/ /g, "_");

                // Create the main wrapper div
                const mainWrapper = document.createElement("div");
                mainWrapper.className = "selector-wrapper-free-product";

                // Create the label element
                const label = document.createElement("label");
                label.setAttribute("for", `data-variant-option-`);
                label.setAttribute("data-variant-option-name", option.name);
                label.setAttribute(
                  "data-variant-option-choose-name",
                  option.name
                );

                label.innerHTML = option.heading;

                // Create the span wrapper
                const span = document.createElement("span");

                // Create the buttons
                option.options.forEach((value, index) => {
                  const button = document.createElement("button");

                  button.type = "button";
                  button.className = `rounded-button ${headingWithHyphen}CircleButtonCustom`;
                  button.setAttribute(
                    "onclick",
                    `changeBorderColor('${option.name}', this, '.rounded-button', '${option.heading}', 'Circle')`
                  );

                  button.innerHTML = value.name;
                  if (index === 0) {
                    button.classList.add("selected"); // Add a class for selected state
                    button.style.border = "2px solid rgb(84, 97, 200)"; // Apply specific style
                  }

                  // Append the button to the span
                  span.appendChild(button);
                });

                // Append the label and span to the main wrapper
                mainWrapper.appendChild(label);
                mainWrapper.appendChild(span);

                // Append the main wrapper to the desired parent element
                const targetContainer = $(".variantsContainer");
                targetContainer.append(hiddenInput);
                targetContainer.append(mainWrapper);
              } else if (option.shape === "rectangle") {
                // Replace spaces with underscores in the heading
                const headingWithHyphen = option.heading.replace(/ /g, "_");

                // Create the main wrapper div
                const mainWrapper = document.createElement("div");
                mainWrapper.className = "selector-wrapper-free-product";

                // Create the label element
                const label = document.createElement("label");
                label.setAttribute("for", `data-variant-option-`);
                label.setAttribute("data-variant-option-name", option.name);
                label.setAttribute(
                  "data-variant-option-choose-name",
                  option.name
                );

                label.innerHTML = option.heading;

                // Create the span wrapper
                const span = document.createElement("span");

                // Create the buttons
                option.options.forEach((value, index) => {
                  const button = document.createElement("button");
                  button.type = "button";
                  button.className = `rectangle-button ${headingWithHyphen}RectangleButtonCustom`;
                  button.setAttribute(
                    "onclick",
                    `changeBorderColor('${option.name}', this, '.rectangle-button', '${option.heading}', 'Rectangle')`
                  );
                  button.innerHTML = value.name;

                  if (index === 0) {
                    button.classList.add("selected"); // Add a class for selected state
                    button.style.border = "2px solid rgb(84, 97, 200)"; // Apply specific style
                  }

                  // Append the button to the span
                  span.appendChild(button);
                });

                // Append the label and span to the main wrapper
                mainWrapper.appendChild(label);
                mainWrapper.appendChild(span);

                // Append the main wrapper to the desired parent element
                const targetContainer = $(".variantsContainer");
                targetContainer.append(hiddenInput);
                targetContainer.append(mainWrapper);
              }
            }
          }
          for (var key in nativeOptions) {
            if (nativeOptions.hasOwnProperty(key)) {
              let option = nativeOptions[key];

              nativeOptionsKeys.push(option.name);

              if (option.type === "dropdown") {
                const hiddenInput = document.createElement("input");
                hiddenInput.type = "hidden";
                hiddenInput.name = `properties[${option.name}]`;
                hiddenInput.classList.add(`${option.name}HiddenInput`);
                hiddenInput.value = option.options[0].value;

                const div = document.createElement("div");
                div.classList.add("selector-wrapper-free-product", option.name);

                // Create the label element
                const label = document.createElement("label");
                label.setAttribute("for", `data-variant-option-`);
                label.setAttribute("data-variant-option-name", option.heading);
                label.setAttribute(
                  "data-variant-option-choose-name",
                  option.heading
                );
                label.textContent = option.heading;
                div.appendChild(label);

                // Create the span element
                const span = document.createElement("span");
                span.classList.add("selectFreeVariant");
                span.setAttribute("data-dropdown-form-style", "");
                div.appendChild(span);

                // Create the select element
                const select = document.createElement("select");
                select.classList.add(
                  "single-option-selector",
                  "nativeLengthOption"
                );
                select.id = `data-variant-option-`;
                select.setAttribute(
                  "onchange",
                  `handleNativeAction("${option.heading}", this, "", "dropdown")`
                );
                // select.setAttribute('onchange', `handleNativeAction('${option.heading}', this, '', '${option.name}');`);
                select.setAttribute("data-variant-option", "");

                select.style.color = "black";
                span.appendChild(select);

                // Create and append the default option
                const defaultOption = document.createElement("option");
                defaultOption.value = option.options[0].value; // Set default value to the first option
                defaultOption.selected = true;
                defaultOption.setAttribute(
                  "data-variant-option-value-wrapper",
                  ""
                );
                defaultOption.setAttribute("data-variant-option-value", "");
                defaultOption.setAttribute(
                  "data-variant-option-value-index",
                  ""
                );
                defaultOption.innerHTML = option.options[0].name; // Display the name of the first option
                select.appendChild(defaultOption);

                // Create the options
                option.options.forEach((value, index) => {
                  if (value.display && index !== 0) {
                    const optionElement = document.createElement("option");
                    optionElement.value = value.value;
                    // if (
                    //   selectedVariant &&
                    //   option.selected_value == value.value
                    // ) {
                    //   optionElement.selected = true;
                    // }
                    optionElement.setAttribute(
                      "data-variant-option-value-wrapper",
                      ""
                    );
                    optionElement.setAttribute("data-variant-option-value", "");
                    optionElement.setAttribute(
                      "data-variant-option-value-index",
                      ""
                    );
                    optionElement.textContent = value.name;
                    select.appendChild(optionElement);
                  }
                });

                // Append the div to a target container
                const targetContainer = $(".variantsContainer");
                targetContainer.append(hiddenInput);
                targetContainer.append(div);
              } else if (option.type === "button") {
                const hiddenInput = document.createElement("input");
                hiddenInput.type = "hidden";
                hiddenInput.name = `properties[${option.name}]`;
                hiddenInput.classList.add(`${option.name}HiddenInput`);
                hiddenInput.value = option.options[0].value;

                if (option.shape === "circle") {
                  // Create the main div element
                  const div = document.createElement("div");
                  div.classList.add("selector-wrapper-free-product");

                  // Create the label element
                  const label = document.createElement("label");
                  label.setAttribute("for", `data-variant-option-`);
                  label.setAttribute(
                    "data-variant-option-name",
                    option.heading
                  );
                  label.setAttribute(
                    "data-variant-option-choose-name",
                    option.heading
                  );
                  label.textContent = option.heading;
                  div.appendChild(label);

                  // Create the span element
                  const span = document.createElement("span");
                  div.appendChild(span);

                  // Loop through the options and create buttons
                  option.options.forEach((value, index) => {
                    if (value.display) {
                      const headingWithHyphen = option.heading.replace(
                        /\s+/g,
                        "_"
                      );
                      const button = document.createElement("button");
                      button.type = "button";
                      button.classList.add(
                        "rounded-button",
                        `${headingWithHyphen}CircleButton`
                      );
                      button.setAttribute(
                        "onclick",
                        `handleNativeAction('${option.heading}', this, '.rounded-button', 'button', 'Circle')`
                      );
                      button.textContent = value.name;
                      if (index === 0) {
                        button.classList.add("selected"); // Add a class for selected state
                        button.style.border = "2px solid rgb(84, 97, 200)"; // Apply specific style
                      }
                      span.appendChild(button);
                    }
                  });

                  const targetContainer = $(".variantsContainer");
                  targetContainer.append(hiddenInput);
                  targetContainer.append(div);
                } else if (option.shape === "rectangle") {
                  // Create the main div element
                  const div = document.createElement("div");
                  div.classList.add(
                    "selector-wrapper-free-product",
                    "defaultNumberOfOptions"
                  );

                  // Create the label element
                  const label = document.createElement("label");
                  label.setAttribute("for", `data-variant-option-`);
                  label.setAttribute(
                    "data-variant-option-name",
                    option.heading
                  );
                  label.setAttribute(
                    "data-variant-option-choose-name",
                    option.heading
                  );
                  label.textContent = option.heading;
                  div.appendChild(label);

                  // Create the span element
                  const span = document.createElement("span");
                  div.appendChild(span);

                  // Loop through the options and create buttons
                  option.options.forEach((value, index) => {
                    if (value.display) {
                      const headingWithHyphen = option.heading.replace(
                        /\s+/g,
                        "_"
                      );
                      const button = document.createElement("button");
                      button.type = "button";
                      button.classList.add(
                        "rectangle-button",
                        `${headingWithHyphen}RectangleButton`
                      );
                      button.setAttribute(
                        "onclick",
                        `handleNativeAction('${option.heading}', this, '.rectangle-button', 'button', 'Rectangle')`
                      );
                      button.textContent = value.name;
                      if (index === 0) {
                        button.classList.add("selected"); // Add a class for selected state
                        button.style.border = "2px solid rgb(84, 97, 200)"; // Apply specific style
                      }
                      span.appendChild(button);
                    }
                  });

                  // Append the hidden input and the divs to a target container
                  const targetContainer = $(".variantsContainer");
                  targetContainer.append(hiddenInput);
                  targetContainer.append(div);
                }
              } else if (option.type === "swatches") {
                const hiddenInput = document.createElement("input");
                hiddenInput.type = "hidden";
                hiddenInput.name = `properties[${option.name}]`;
                hiddenInput.classList.add(`${option.name}HiddenInput`);
                hiddenInput.value = option.options[0].name;

                // Create the main swatches-select div element
                const swatchesSelectDiv = document.createElement("div");
                swatchesSelectDiv.classList.add("swatches-select");
                swatchesSelectDiv.style.marginBottom = "10px";

                // Create the swatches-label div element
                const swatchesLabelDiv = document.createElement("div");
                swatchesLabelDiv.classList.add("swatches-label-free-product");

                // Create the label element
                const label = document.createElement("label");
                label.setAttribute("for", `data-variant-option-`);
                label.setAttribute("data-variant-option-name", option.heading);
                label.setAttribute(
                  "data-variant-option-choose-name",
                  option.heading
                );
                label.innerHTML = `${option.heading}: <span id="${option.heading}-selected-swatches">${option.options[0].name}</span>`;
                swatchesLabelDiv.appendChild(label);

                // Append the swatches-label div to the swatches-select div
                swatchesSelectDiv.appendChild(swatchesLabelDiv);

                // Create the inner div for the radio buttons and images
                const innerDiv = document.createElement("div");
                innerDiv.style.marginBottom = "15px";

                // Loop through the options and create radio buttons with images
                option.options.forEach((value, index) => {
                  if (value.display) {
                    const label = document.createElement("label");
                    label.style.marginRight = "10px";

                    const radioInput = document.createElement("input");
                    radioInput.type = "radio";
                    radioInput.name = option.name;
                    radioInput.value = value.name;
                    radioInput.style.width = "0px";
                    label.appendChild(radioInput);

                    if (index === 0) {
                      radioInput.checked = true;
                    }

                    const img = document.createElement("img");
                    img.src = value.image;
                    img.alt = value.name;
                    img.title = value.name;
                    img.width = 50;
                    img.height = 50;
                    img.onclick = function () {
                      handleNativeAction(
                        option.heading,
                        value.name,
                        "",
                        "swatches"
                      );
                    };
                    label.appendChild(img);

                    innerDiv.appendChild(label);
                  }
                });

                // Append the inner div to the swatches-select div
                swatchesSelectDiv.appendChild(innerDiv);

                // Append the hidden input and the swatches-select div to a target container
                const targetContainer = $(".variantsContainer");
                targetContainer.append(hiddenInput);
                targetContainer.append(swatchesSelectDiv);
              }
            }
          }
        }
        // end free product code

        let extraItems = [];
        const url = window.location.origin;

        const extraOptions = document.querySelector(".extraOptionsContainer");
        if (extraOptions) {
          let mainItemId = null;

          const formData = $addToCartForm.serializeArray();

          const primaryProduct = formData.reduce((acc, { name, value }) => {
            const match = name.match(/^properties\[(.+?)\]$/);

            if (match) {
              const extractedName = match[1];

              if (acc[extractedName]) {
                if (Array.isArray(acc[extractedName])) {
                  acc[extractedName].push(value);
                } else {
                  acc[extractedName] = [acc[extractedName], value];
                }
              } else {
                acc[extractedName] = value;
              }
            } else if (name === "id") {
              mainItemId = value;
            }
            return acc;
          }, {});

          const mainItem = {
            quantity: 1,
            id: mainItemId,
            properties: { ...primaryProduct },
          };

          extraCheckboxes = extraOptions?.querySelectorAll(".extraOption");

          extraCheckboxes.forEach((product) => {
            if (product.checked) {
              const isRush = product.dataset.identifier === "rush";

              const rushKeyValue = product.getAttribute("data-rush-key");

              const item = {
                quantity: 1,
                id: product.value,
                properties: {
                  ...(isRush
                    ? { _rush_key: rushKeyValue }
                    : { _unique_key: primaryProduct._original_unique_key }),
                },
              };
              extraItems.push(item);
            }
          });

          extraItems.unshift(mainItem);
        }

        async function addExtraItemsToCart(extraItems) {
          try {
            $addToCartBtn.attr("disabled", "disabled").addClass("disabled");

            $addToCartBtn
              .find("span")
              .removeClass("fadeInDown")
              .addClass("animated zoomOut");

            const response = await fetch(`${url}/cart/add.js`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ items: extraItems }),
            });

            if (!response.ok) {
              throw new Error(
                "Error adding extra items to cart: " + (await response.text())
              );
            }

            const cart = await response.json();

            let $el = $("[data-ajax-cart-trigger]");

            $addToCartBtn.find(".checkmark").addClass("checkmark-active");

            function addedToCart() {
              if (!isScreenSizeLarge()) {
                $el = $(".mobile-header [data-ajax-cart-trigger]");
                window.PXUTheme.scrollToTop($el);
              } else {
                $el = $("[data-ajax-cart-trigger]");
              }

              $el.addClass("show-mini-cart");

              $addToCartBtn.find("span").removeClass("fadeInDown");
            }

            window.setTimeout(function () {
              $addToCartBtn.removeAttr("disabled").removeClass("disabled");

              $addToCartBtn.find(".checkmark").removeClass("checkmark-active");

              $addToCartBtn
                .find(".text, .icon")
                .removeClass("zoomOut")
                .addClass("fadeInDown");

              $addToCartBtn.on(
                "webkitAnimationEnd oanimationend msAnimationEnd animationend",
                addedToCart
              );
            }, 1000);

            window.PXUTheme.jsAjaxCart.showDrawer();
            window.PXUTheme.jsAjaxCart.updateView();

            if (window.PXUTheme.jsCart) {
              $.ajax({
                dataType: "json",
                async: false,
                cache: false,
                dataType: "html",
                url: "/cart",
                success: function (html) {
                  const cartForm = $(html).find(".cart__form");
                  $(".cart__form").replaceWith(cartForm);
                },
              });
            }
          } catch (error) {
            console.error(
              "An error occurred while adding extra items to the cart:",
              error
            );
          }
        }

        if (extraOptions && extraItems.length > 0) {
          addExtraItemsToCart(extraItems);
        } else {
          $.ajax({
            url: "/cart/add.js",
            dataType: "json",
            cache: false,
            type: "post",
            data: $addToCartForm.serialize(),
            beforeSend: function () {
              $addToCartBtn.attr("disabled", "disabled").addClass("disabled");

              $addToCartBtn
                .find("span")
                .removeClass("fadeInDown")
                .addClass("animated zoomOut");
            },
            success: function (product) {
              let $el = $("[data-ajax-cart-trigger]");

              $addToCartBtn.find(".checkmark").addClass("checkmark-active");

              function addedToCart() {
                if (!isScreenSizeLarge()) {
                  $el = $(".mobile-header [data-ajax-cart-trigger]");
                  window.PXUTheme.scrollToTop($el);
                } else {
                  $el = $("[data-ajax-cart-trigger]");
                }

                $el.addClass("show-mini-cart");

                $addToCartBtn.find("span").removeClass("fadeInDown");
              }

              window.setTimeout(function () {
                $addToCartBtn.removeAttr("disabled").removeClass("disabled");

                $addToCartBtn
                  .find(".checkmark")
                  .removeClass("checkmark-active");

                $addToCartBtn
                  .find(".text, .icon")
                  .removeClass("zoomOut")
                  .addClass("fadeInDown");

                $addToCartBtn.on(
                  "webkitAnimationEnd oanimationend msAnimationEnd animationend",
                  addedToCart
                );
              }, 1000);

              window.PXUTheme.jsAjaxCart.showDrawer();
              window.PXUTheme.jsAjaxCart.updateView();

              if (window.PXUTheme.jsCart) {
                $.ajax({
                  dataType: "json",
                  async: false,
                  cache: false,
                  dataType: "html",
                  url: "/cart",
                  success: function (html) {
                    const cartForm = $(html).find(".cart__form");
                    $(".cart__form").replaceWith(cartForm);
                  },
                });
              }
            },
            error: (XMLHttpRequest) => {
              const response = eval("(" + XMLHttpRequest.responseText + ")");

              $(".warning").remove();

              let cartWarning;

              if (response.errors && response.errors.email) {
                this.recipientForm.classList.add("recipient-form--has-errors");
              } else {
                cartWarning = `<p class="cart-warning__message animated bounceIn">${response.description.replace(
                  "All 1 ",
                  "All "
                )}</p>`;
              }

              $addToCartForm.find(".cart-warning").html(cartWarning);

              $addToCartBtn.removeAttr("disabled").removeClass("disabled");

              $addToCartBtn
                .find(".icon")
                .removeClass("zoomOut")
                .addClass("zoomIn");

              $addToCartBtn
                .find("span:not(.icon)")
                .text($addToCartBtn.data("label"))
                .removeClass("zoomOut")
                .addClass("zoomIn");
            },
          });
        }
      }
    },
    updateView: function () {
      window.PXUTheme.asyncView
        .load(
          window.PXUTheme.routes.cart_url, // template name
          "ajax" // view name (suffix)
        )
        .done(({ html, options }) => {
          if (options.item_count > 0) {
            const itemList = $(html.content).find(".ajax-cart__list");
            const cartDetails = $(html.content).find(
              ".ajax-cart__details-wrapper"
            );

            $(".ajax-cart__list").replaceWith(itemList);
            $(".ajax-cart__details-wrapper").replaceWith(cartDetails);
            $(".ajax-cart__empty-cart-message").addClass("is-hidden");
            $(".ajax-cart__form").removeClass("is-hidden");
            $("[data-ajax-cart-trigger]").addClass("has-cart-count");
            $('[data-bind="itemCount"]').text(options.item_count);
          } else {
            $(".ajax-cart__empty-cart-message").removeClass("is-hidden");
            $(".ajax-cart__form").addClass("is-hidden");
            $("[data-ajax-cart-trigger]").removeClass("has-cart-count");
            $('[data-bind="itemCount"]').text("0");
          }

          if (window.PXUTheme.currencyConverter) {
            window.PXUTheme.currencyConverter.convertCurrencies();
          }
        })
        .fail(() => {
          // some error handling
        });
    },
    unload: function ($section) {
      // Clear event listeners in theme editor
      $(".ajax-submit").off();
      $("[data-ajax-cart-delete]").off();
    },
  };

  /******/
})();
