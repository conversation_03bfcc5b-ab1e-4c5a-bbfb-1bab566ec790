{% assign oms = shop.metafields.cuddleclones.api_details.value %}
const omsDetails = {{oms |  json}}
   
    const headerForImage = {
      'api-token': 'p?9?S-K6fQUM8F?3eyME4MHrlKZSQK0P!ykB6mrbkVROIPkOkz4mXUc!nCEE7Bzw!2KkFqiva!W7mBRqQ5S2QxBTA8F7U6dKlgR!FHW6XRoh69?i41B1vdX2aykcWBtFO2Pz6r?a=E=zSeMbB3Cwi6n90qpUWLHNv-GLvGWcoiyos1n0L3AmghRwc2Z/jM/qqkI9Cy-=C86!1iR!uN2czyBSJwp-!fjNumHPLqtMfZlUecTyrQ6O54m-KD8uEAOy',
    }
      var preview = document.getElementById("image-uploader-1");
      let cropper;
      var imageType='';

      function dragNdropUnique(event, containerId, number) {
        var previewImg = document.createElement("img");
        var files = event.target.files;

        if (files.length > 1) {
          alert("Please select only one image file.");
          return;
        }

        var file = files[0];
        var fileName = URL.createObjectURL(files[0]);

        if (file.type !== "image/jpg" && file.type !== "image/png" && file.type !== "image/bmp" && file.type !== "image/jpeg") {
          alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
          return;
        }

        if (!file.type.startsWith("image/")) {
          alert("Please select only image files.");
          return;
        }

        if (files[0].size > 15 * 1024 * 1024) {
          // Convert MB to bytes
          alert("Please select images smaller than 15MB.");
          return;
        }

        var imageType = file?.type;

        document.querySelector(".cropHeading").style.display = "block";
        document.querySelector(".finishCropButton").style.display = "flex";

        if (event.target.files.length) {
          // start file reader
          const reader = new FileReader();
          reader.onload = (event) => {
            if (event.target.result) {
              previewImg.setAttribute("id", "imagePreview");
              previewImg.setAttribute("alt", "image-cropped");
              cropper = new Cropper(previewImg, {
                viewMode: 1, // Set view mode to restrict the crop box within the container
                aspectRatio: 1, // Set aspect ratio to create a circular crop box
                guides: true, // Optional: Hide the guides for a cleaner look
                background: false,
                crop(event) {
                  const canvas = cropper.getCroppedCanvas({
                    width: 200, // Set desired output width
                    height: 200, // Set desired output height
                    imageSmoothingEnabled: true,
                    imageSmoothingQuality: "high",
                  });

                  const roundedCanvas = document.createElement("canvas");
                  const roundedContext = roundedCanvas.getContext("2d");
                  const radius = canvas.width / 2;

                  roundedCanvas.width = canvas.width;
                  roundedCanvas.height = canvas.height;

                  roundedContext.beginPath();
                  roundedContext.arc(radius, radius, radius, 0, Math.PI * 2);
                  roundedContext.closePath();
                  roundedContext.clip();

                  roundedContext.drawImage(canvas, 0, 0);
                },
              });
            }
          };
          reader.readAsDataURL(event.target.files[0]);
        }

        const deleteButtonDiv = document.createElement("div");
        deleteButtonDiv.classList.add("delete-button-unique");
        deleteButtonDiv.textContent = "X";
        deleteButtonDiv.addEventListener("click", function (event) {
          document.querySelector(".uploadOuter").style.display = "block";
          deleteUniqueImage(event);
        });

        const hiddenUniqueImage = document.querySelector(
          ".selected-image-1" + " img"
        );
        const hiddenUniqueInput = document.querySelector(
          ".uniqueUploadedImage-1"
        );

        previewImg.setAttribute("src", fileName);
        previewImg.setAttribute("alt", "characteristic-image-" + number);
        document.querySelector(".uploadOuter").style.display = "none";

        // Find existing image container if it exists
        var existingImageContainer = preview.querySelector(".selected-image-1");

        if (existingImageContainer) {
          // If there's already an image, replace it with the new one
          var existingImage = existingImageContainer.querySelector("img");

          existingImageContainer.appendChild(deleteButtonDiv); // Add new image
          existingImageContainer.appendChild(previewImg); // Add new image
        } else {
          // If there's no existing image, create a new image container
          existingImageContainer = document.createElement("div");
          existingImageContainer.classList.add("selected-image-1");
          existingImageContainer.appendChild(previewImg); // Add new image
        }
      }

      function deleteUniqueImage(event) {
        const imageContainer = event.target.closest(".selected-image-container");
        imageContainer.innerHTML = "";
      }
      let imgSrc;

      function handleCroppedImage(e) {
        document.querySelector(".cropHeading").style.display = "none";
        document.querySelector(".finishCropButton").style.display = "none";
        document.querySelector(".image-uploader").style.cssText =
          "max-width: 243px; margin: 0;";

        document.querySelector(".cropper-container").style.display = "none";

        // Show the cropped image container
        document.getElementById("imagePreview").style.display = "block";

        e.preventDefault();
        // get result to data uri
        let previewImg = document.getElementById("imagePreview");
        previewImg.style.setProperty("display", "block", "important");
        e.preventDefault();
        // get result to data uri
        imgSrc = cropper.getCroppedCanvas().toDataURL();
        //console.log("===========", imgSrc);

        // show image cropped
        previewImg.src =
          "https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif";
        previewImg.style.setProperty("border-radius", "50%");
        let blob = base64toBlob(
          imgSrc.replace(/^data:image\/(png|jpg|jpeg);base64,/, ""),
          imageType
        );
        uploadImage(blob);
      }

      function base64toBlob(base64Data, contentType) {
        contentType = contentType || "";
        var sliceSize = 1024;
        var byteCharacters = atob(base64Data);
        var byteArrays = [];

        for (
          var offset = 0;
          offset < byteCharacters.length;
          offset += sliceSize
        ) {
          var slice = byteCharacters.slice(offset, offset + sliceSize);

          var byteNumbers = new Array(slice.length);
          for (var i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
          }

          var byteArray = new Uint8Array(byteNumbers);
          byteArrays.push(byteArray);
        }

        var blob = new Blob(byteArrays, { type: contentType });
        //console.log("===============blob", blob);
        return blob;
      }

      function uploadImage(blob) {
        let previewImg = document.getElementById("imagePreview");

        let formData = new FormData();
        formData.append("image", blob, "image." + imageType.split("/")[1]);

        // Upload the new image
        fetch(`${omsDetails[0].url}image_uploader/print_image`, {
          method: "POST",
          body: formData,
          headers: {
            ...headerForImage,
          },
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
          })
          .then((data) => {
            previewImg.setAttribute("src", data.imageUrl);
            hiddenUniqueInput.value = data.imageUrl;
          })
          .catch((error) => {
            console.error("Error uploading new image:", error);
          });
      }