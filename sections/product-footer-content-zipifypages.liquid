{%- comment -%}
  Updated: 01/25/2024
  This file is system-generated and should not be modified. We reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost
{%- endcomment -%}{% assign contentprt = 'footer' %}{% if zp_override_template_name != blank %}{% assign zp_entity_marker = zp_override_template_name %}{% else %}{% assign zp_entity_marker = template.name %}{% endif %}{% if zp_override_template_suffix != blank %}{% assign zp_template_suffix = zp_override_template_suffix %}{% else %}{% assign zp_template_suffix = template.suffix %}{% endif %}{% assign zp_product_template_suffix = zp_template_suffix | remove: '-' %}{% assign zp_shop_dir_name = shop.permanent_domain | sha1 %}{% assign zp_collect_entity_content_parts = false %}{% assign zp_split_tests_available = false %}{% assign zp_use_json_entity_settings = false %}{% assign zp_use_only_custom_template = false %}{% assign zp_shop_metafields_loaded = false %}{% assign zp_current_entity_metafields_loaded = false %}{% assign zp_use_product_markup = false %}{% assign zp_use_current_entity_markup = false %}{% assign zp_use_native_entity_description = false %}{% assign zp_use_native_og_images = false %}{% assign zp_og_entity_type = 'website' %}{% assign zp_is_product_page = false %}{% assign zp_current_entity_index_state = nil %}{% if zp_template_suffix contains 'zipifypages' %}{% assign zp_enable_content_parsing = true %}{% else %}{% assign zp_enable_content_parsing = false %}{% endif %}{% if zp_enable_content_parsing and zp_entity_marker == 'article' %}{% assign zp_current_entity = article %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'aac' %}{% assign zp_scripts_metafield_key = 'articlescripts' %}{% assign zp_entity_data_metafield_key = 'articledata' %}{% assign zp_entity_styles_folder = 'articles' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_use_product_markup = true %}{% elsif zp_enable_content_parsing and zp_entity_marker == 'blog' %}{% assign zp_current_entity = blog %}{% assign zp_current_entity_content = '' | append: zp_current_entity.metafields['zipifypagesblogparts']['blogheaderfooter'] %}{% assign zp_entity_attributes_class = 'bac' %}{% assign zp_scripts_metafield_key = 'blogscripts' %}{% assign zp_entity_data_metafield_key = 'blogdata' %}{% assign zp_entity_styles_folder = 'blogs' %}{% assign zp_use_product_markup = true %}{% elsif zp_enable_content_parsing and zp_entity_marker == 'page' %}{% assign zp_current_entity = page %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'pac' %}{% assign zp_scripts_metafield_key = 'pagescripts' %}{% assign zp_entity_data_metafield_key = 'pagedata' %}{% assign zp_entity_styles_folder = 'pages' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_split_tests_available = true %}{% assign zp_use_product_markup = true %}{% elsif zp_entity_marker == 'index' %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% assign zp_shop_metafields_loaded = true %}{% assign zp_index_page = zp_shop_metafields['indexpage'] %}{% assign zp_index_page_handle = zp_shop_metafields['indexpagehandle'] %}{% if zp_index_page.type == 'page_reference' %}{% assign zp_current_entity = zp_index_page.value %}{% elsif zp_index_page_handle.size > 0 %}{% assign zp_current_entity = pages[zp_index_page_handle] %}{% else %}{% assign zp_current_entity = nil %}{% endif %}{% assign zp_current_entity_id_size = '' | append: zp_current_entity.id | size %}{% if zp_current_entity_id_size > 0 %}{% assign zp_entity_marker = 'page' %}{% assign zp_enable_content_parsing = true %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'pac' %}{% assign zp_scripts_metafield_key = 'pagescripts' %}{% assign zp_entity_data_metafield_key = 'pagedata' %}{% assign zp_entity_styles_folder = 'pages' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_split_tests_available = true %}{% assign zp_use_product_markup = true %}{% else %}{% assign zp_current_entity = nil %}{% assign zp_enable_content_parsing = false %}{% endif %}{% elsif zp_entity_marker == 'product' %}{% assign zp_current_entity = product %}{% assign zp_entity_attributes_class = 'ppac' %}{% assign zp_scripts_metafield_key = 'productpagescripts' %}{% assign zp_entity_data_metafield_key = 'productpagedata' %}{% assign zp_entity_styles_folder = 'product_pages' %}{% assign zp_use_viewport_meta = false %}{% assign zp_use_json_entity_settings = true %}{% assign zp_is_product_page = true %}{% if zp_template_suffix contains 'zipifypages-' %}{% assign zp_current_entity_metafields = shop.metafields[zp_product_template_suffix] %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_index_state = zp_current_entity_metafields['config'].value['page_index_state'] %}{% else %}{% assign zp_current_entity_index_state = zp_current_entity_metafields['config']['page_index_state'] %}{% endif %}{% if zp_use_meta_tags == true %}{% assign zp_use_meta_tags = true %}{% elsif zp_use_meta_tags == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_meta_tags = true %}{% else %}{% assign zp_use_meta_tags = false %}{% endif %}{% if zp_use_open_graph_tags == true %}{% assign zp_use_open_graph_tags = true %}{% elsif zp_use_open_graph_tags == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_open_graph_tags = true %}{% else %}{% assign zp_use_open_graph_tags = false %}{% endif %}{% if zp_use_favicon == true %}{% assign zp_use_favicon = true %}{% elsif zp_use_favicon == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_favicon = true %}{% else %}{% assign zp_use_favicon = false %}{% endif %}{% assign zp_display_content_for_header = false %}{% assign zp_use_native_entity_description = true %}{% assign zp_use_native_og_images = true %}{% assign zp_og_entity_type = 'product' %}{% assign zp_use_current_entity_markup = true %}{% assign zp_product_page_with_zp_layout = true %}{% else %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% assign zp_use_meta_tags = false %}{% assign zp_use_open_graph_tags = false %}{% assign zp_use_favicon = false %}{% assign zp_display_content_for_header = false %}{% assign zp_use_only_custom_template = true %}{% assign zp_product_page_with_zp_layout = false %}{% endif %}{% assign zp_current_entity_metafields_loaded = true %}{% assign zp_current_entity_data = zp_current_entity_metafields['config'] | default: zp_current_entity_metafields[zp_entity_data_metafield_key] | default: 'noassigneddata' %}{% if zp_current_entity_data == 'noassigneddata' %}{% assign zp_enable_content_parsing = false %}{% else %}{% assign zp_enable_content_parsing = true %}{% assign zp_split_tests_available = true %}{% endif %}{% assign zp_current_entity_data = nil %}{% else %}{% assign zp_current_entity = nil %}{% assign zp_current_entity_content = '' %}{% assign zp_use_meta_tags = false %}{% assign zp_use_open_graph_tags = false %}{% assign zp_use_viewport_meta = false %}{% assign zp_use_favicon = false %}{% assign zp_display_content_for_header = false %}{% endif %}{% if zp_enable_content_parsing %}{% unless zp_shop_metafields_loaded %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% endunless %}{% unless zp_current_entity_metafields_loaded %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% endunless %}{% if zp_current_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% if zp_use_only_custom_template %}{% assign zp_entity_custom_template = true %}{% elsif zp_current_entity.template_suffix == 'custom.zipifypages' %}{% assign zp_entity_custom_template = true %}{% elsif zp_current_entity_index_state == 'custom' %}{% assign zp_entity_custom_template = true %}{% else %}{% assign zp_entity_custom_template = false %}{% endif %}{% endif %}{% if zp_collect_entity_content_parts and zp_current_entity_content contains ':|zpendofcontent|:' %}{% assign zp_entity_with_multiparts = true %}{% else %}{% assign zp_entity_with_multiparts = false %}{% endif %}{% assign zp_gdpr_enabled = false %}{% assign zp_alternative_entity_present = false %}{% assign zp_split_single_page_render = false %}{% assign zp_split_test_redirect = false %}{% assign zp_split_test_view_type_redirect = false %}{% if zp_entity_with_multiparts %}{% assign zp_current_entity_content = '' | append: zp_current_entity_content | split: ':|zpendofcontent|:' | first %}{% endif %}{% if zp_enable_content_parsing and zp_split_tests_available %}{% if zp_product_page_with_zp_layout %}{% assign zp_current_entity_object_metafields = zp_current_entity_main_metafields %}{% else %}{% assign zp_current_entity_object_metafields = zp_current_entity_metafields %}{% endif %}{% if zp_current_entity_object_metafields['splittest'] != blank %}{% if zp_current_entity_object_metafields['splittest'].type == 'json' %}{% assign zp_split_test_data = zp_current_entity_object_metafields['splittest'].value %}{% assign zp_split_test_type = zp_split_test_data.type %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data.alternative_handle %}{% assign zp_split_dataset = zp_split_test_data.dataset | json %}{% assign zp_split_dataset = '' | append: zp_split_dataset %}{% assign zp_split_single_page_render = '' | append: zp_split_test_data.single_page_render %}{% assign zp_split_token = zp_split_test_data.token%}{% else %}{% assign zp_split_test_data = '' | append: zp_current_entity_object_metafields['splittest'] | split: ':|~|:' %}{% assign zp_split_test_type = zp_split_test_data[0] %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data[1] %}{% assign zp_split_dataset = zp_split_test_data[2] %}{% assign zp_split_single_page_render = '' | append: zp_split_test_data[3] %}{% assign zp_split_token = '' | append: zp_split_test_data[4] %}{% endif %}{% if zp_split_test_type == 'mainlayout' %}{% if zp_entity_marker == 'article' %}{% assign zp_alternative_entity = articles[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.content %}{% elsif zp_entity_marker == 'blog' %}{% assign zp_alternative_entity = blogs[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.metafields['zipifypagesblogparts']['blogheaderfooter'] %}{% elsif zp_entity_marker == 'page' %}{% assign zp_alternative_entity = pages[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.content %}{% else %}{% assign zp_alternative_entity = nil %}{% assign zp_alternative_entity_content = '' %}{% endif %}{% assign zp_alternative_entity_id_size = '' | append: zp_alternative_entity.id | size %}{% if zp_alternative_entity_id_size > 0 %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity_content | split: ':|zpendofcontent|:' | first %}{% assign zp_alternative_entity_present = true %}{% if zp_split_single_page_render == 'true' %}{% assign zp_split_single_page_render = true %}{% else %}{% assign zp_split_single_page_render = false %}{% endif %}{% assign zp_alternative_entity_title = '' | append: zp_alternative_entity.title %}{% assign zp_alternative_entity_metafields = zp_alternative_entity.metafields['zipifypages'] %}{% if zp_alternative_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_alternative_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_alternative_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% assign zp_entity_scripts = '' | append: zp_alternative_entity_metafields[zp_scripts_metafield_key] | split: '|;|~|;|' %}{% assign zp_alternative_header_scripts = '' | append: zp_entity_scripts[0] | strip %}{% assign zp_entity_scripts = '' %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_alternative_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_alternative_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data contains '"gdpr_enabled":true' %}{% assign zp_gdpr_enabled = true %}{% elsif zp_blocks_data contains '"gdpr_enabled":false' %}{% assign zp_gdpr_enabled = false %}{% elsif zp_blocks_data contains '"gdpr_status":"enable_with_checkbox"' or zp_blocks_data contains '"gdpr_status":"enable_without_checkbox"' %}{% assign zp_gdpr_enabled = true %}{% endif %}{% endif %}{% assign zp_alternative_entity_content_wrapper_class = "zp " | append: zp_entity_attributes_class | append: '-' | append: zp_alternative_entity.id %}{% if zp_alternative_entity.template_suffix == 'custom.zipifypages' %}{% assign zp_alternative_entity_custom_template = true %}{% else %}{% assign zp_alternative_entity_custom_template = false %}{% endif %}{% elsif zp_split_test_type == 'redirect' or zp_split_test_type == 'redirecttarget' %}{% assign zp_split_test_redirect = true %}{% elsif zp_split_test_type == 'viewtyperedirect' %}{% assign zp_split_test_view_type_redirect = true %}{% endif %}{% endif %}{% endif %}{% if zp_enable_content_parsing %}{% assign zp_entity_title = page_title | remove: ':|~|:' | remove: ':--:' | append: entity_title_suffix | strip %}{% if zp_gdpr_enabled == false %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_current_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_current_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data contains '"gdpr_enabled":true' %}{% assign zp_gdpr_enabled = true %}{% elsif zp_blocks_data contains '"gdpr_enabled":false' %}{% assign zp_gdpr_enabled = false %}{% elsif zp_blocks_data contains '"gdpr_status":"enable_with_checkbox"' or zp_blocks_data contains '"gdpr_status":"enable_without_checkbox"' %}{% assign zp_gdpr_enabled = true %}{% endif %}{% endif %}{% assign zp_blocks_data = '' %}{% endif %}{% if zp_enable_content_parsing %}{% if zp_current_entity_metafields['config'].type == 'json' and zp_current_entity_metafields['config'].value['page_last_sync_date'] != blank %}{% assign zp_object_settings = zp_current_entity_metafields['config'].value %}{% elsif zp_current_entity_metafields[zp_entity_data_metafield_key].type == 'json' %}{% assign zp_object_settings = zp_current_entity_metafields[zp_entity_data_metafield_key].value %}{% else %}{% assign zp_object_settings = zp_current_entity_metafields[zp_entity_data_metafield_key] %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['page_with_default_settings'] %}{% if zp_setting_value == 'true' %}{% assign zp_entity_with_default_styles = true %}{% else %}{% assign zp_entity_with_default_styles = false %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['page_fixed_layout'] %}{% if zp_setting_value == 'true' %}{% assign zp_entity_fixed_layout = true %}{% else %}{% assign zp_entity_fixed_layout = false %}{% endif %}{% assign zp_setting_value = nil %}{% if contentprt == 'footer' %}{% assign zp_current_entity_content = '' | append: zp_current_entity_metafields['footercontent'] %}{% assign zp_product_selector_suffix = contentprt %}{% elsif contentprt == 'header' %}{% assign zp_current_entity_content = '' | append: zp_current_entity_metafields['headercontent'] %}{% assign zp_product_selector_suffix = contentprt %}{% elsif contentprt == 'full' %}{% if zp_product_page_entity_tag.size > 0 %}{% assign zp_current_entity_content = '' | append: zp_product_page_entity_tag %}{% else %}{% assign zp_current_entity_content = '' | append: zp_product_page_entity_content %}{% endif %}{% assign zp_product_page_entity_content = nil %}{% assign zp_product_page_entity_tag = nil %}{% assign zp_product_selector_suffix = contentprt %}{% endif %}{% assign zp_current_entity_icons = zp_current_entity_metafields['icons'] %}{% unless zp_current_entity_icons == blank %}<svg style="display:none!important" xmlns="http://www.w3.org/2000/svg"><defs>{{ zp_current_entity_icons }}</defs></svg>{% endunless %}{% if zp_alternative_entity_present %}{% assign zp_current_entity_icons = zp_alternative_entity_metafields['icons'] %}{% unless zp_current_entity_icons == blank %}<svg style="display:none!important" xmlns="http://www.w3.org/2000/svg"><defs>{{ zp_current_entity_icons }}</defs></svg>{% endunless %}{% endif %}{% assign zp_current_entity_icons = nil %}{% assign zp_app_integrations = '' | append: zp_shop_metafields['appintegrations'] | split: ',' %}{% assign zp_original_product = product %}{% if zp_app_integrations contains 'recharge' %}{% assign zp_integrate_with_recharge = true %}{% else %}{% assign zp_integrate_with_recharge = false %}{% endif %}{% assign zp_shopify_options_selector = false %}{% assign zp_unit_price_enabled = false %}{% assign zp_settings_keys = zp_shop_metafields['blockssettings'] %}{% if zp_settings_keys != blank %}{% assign zp_use_json_blocks_settings = true %}{% if zp_settings_keys.type == 'json' %}{% assign zp_settings_keys = zp_settings_keys.value %}{% endif %}{% else %}{% assign zp_use_json_blocks_settings = false %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% assign zp_object_settings_data = '' | append: zp_shop_metafields['buyboxessettings'] | strip %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% endif %}{% for zp_setting_key_data in zp_settings_keys %}{% if zp_use_json_blocks_settings %}{% assign zp_setting_key = zp_setting_key_data | first %}{% assign zp_setting_value = zp_setting_key_data | last | strip %}{% else %}{% assign zp_setting_key = zp_setting_key_data %}{% assign zp_setting_value = '' | append: zp_settings_values[forloop.index0] | strip %}{% endif %}{% if zp_setting_key == 'show_unavailable_variants' %}{% if zp_setting_value == 'true' %}{% assign zp_shopify_options_selector = true %}{% endif %}{% elsif zp_setting_key == 'show_unit_price' %}{% if zp_setting_value == 'true' %}{% assign zp_unit_price_enabled = true %}{% endif %}{% endif %}{% endfor %}{% if zp_app_integrations contains 'bestcurrencyconverter' %}{% assign zp_amount_variants = 'amount|amount_no_decimals|amount_with_comma_separator|amount_no_decimals_with_comma_separator|amount_with_space_separator|amount_no_decimals_with_space_separator|amount_with_apostrophe_separator|amount_with_period_and_space_separator' | split: '|' %}{% assign zp_amount_format_template = shop.money_format %}{% assign zp_open_curly_bracket = '{' %}{% assign zp_closed_curly_bracket = '}' %}{% assign zp_amount_template = '[#!amount!#]' %}{% for zp_amount_variant in zp_amount_variants %}{% assign zp_format_parts = zp_amount_format_template | split: zp_amount_variant %}{% assign zp_amount_format_template = '' %}{% assign zp_is_start_init = false %}{% for zp_part in zp_format_parts %}{% if zp_part.size >= 2 %}{% assign zp_tmp = zp_part | strip | split: '' %}{% assign zp_first_symbol = zp_tmp[0] %}{% assign zp_second_symbol = zp_tmp[1] %}{% if zp_is_start_init == true and zp_first_symbol == zp_closed_curly_bracket and zp_second_symbol == zp_closed_curly_bracket %}{% assign zp_is_start_init = false %}{% assign zp_amount_format_template = zp_amount_format_template | rstrip %}{% assign zp_start_slice = 0 %}{% assign zp_end_slice = zp_amount_format_template.size | minus: 2 %}{% if zp_end_slice < 0 %}{% assign zp_end_slice = 0 %}{% endif %}{% assign zp_amount_format_template = zp_amount_format_template | slice: zp_start_slice, zp_end_slice %}{% assign zp_tmp_part = zp_part | lstrip %}{% assign zp_start_slice = 2 %}{% assign zp_end_slice = zp_tmp_part.size | minus: zp_start_slice %}{% if zp_end_slice < 0 %}{% assign zp_end_slice = 0 %}{% endif %}{% assign zp_tmp_part = zp_tmp_part | slice: zp_start_slice, zp_end_slice %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_amount_template | append: zp_tmp_part %}{% else %}{% assign zp_is_start_init = false %}{% if zp_amount_format_template.size > 0 %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_amount_variant %}{% endif %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_part %}{% endif %}{% assign zp_last_index = zp_tmp.size | minus: 1 %}{% assign zp_second_last_index = zp_tmp.size | minus: 2 %}{% if zp_last_index >= 0 %}{% assign zp_last_symbol = zp_tmp[zp_last_index] %}{% else %}{% assign zp_last_symbol = '' %}{% endif %}{% if zp_second_last_index >= 0 %}{% assign zp_second_last_symbol = zp_tmp[zp_second_last_index] %}{% else %}{% assign zp_second_last_symbol = '' %}{% endif %}{% if zp_last_symbol == zp_open_curly_bracket and zp_second_last_symbol == zp_open_curly_bracket %}{% assign zp_is_start_init = true %}{% endif %}{% if zp_is_start_init == false and zp_last_symbol == zp_open_curly_bracket and zp_second_last_symbol == zp_open_curly_bracket %}{% assign zp_is_start_init = true %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_part %}{% endif %}{% else %}{% assign zp_is_start_init = false %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_part | append: zp_amount_variant %}{% endif %}{% endfor %}{% endfor %}{% assign zp_amount_variants = '' %}{% assign zp_format_parts = '' %}{% endif %}{%- if zp_integrate_with_recharge %}<style>[data-zp-add-to-cart-form] .rc-container-wrapper{display:none!important}</style>{% endif -%}{% if zp_entity_with_multiparts %}{% for zp_part in zp_current_entity.metafields['zipifypagesparts'] %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_part[1] %}{% endfor %}{% endif %}{% assign zp_gb_ids = zp_current_entity_metafields['config'].value['globalblocks'] %}{% for zp_gb_id in zp_gb_ids %}{% capture zp_gb_content %}{% assign zp_gb_snippet_name = 'gb-' | append: zp_gb_id | append: '.zipifypages' %}{% include zp_gb_snippet_name %}{% endcapture %}{% capture zp_gb_tag_name %}<zp_gb_{{ zp_gb_id }}></zp_gb_{{ zp_gb_id }}>{% endcapture %}{% assign zp_gb_error = 'Could not find asset snippets/' | append: zp_gb_snippet_name | append: '.liquid' %}{% if zp_gb_content contains zp_gb_error %}{% assign zp_gb_content = '' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_gb_tag_name, zp_gb_content %}{% else %}{% assign zp_gb_styles =zp_gb_content | split: '<!--ZP_SEPARATOR-->' | first %}{% assign zp_gb_content = zp_gb_content | remove_first: zp_gb_styles | remove: '<!--ZP_SEPARATOR-->' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_gb_tag_name, zp_gb_content | prepend: zp_gb_styles %}{% endif %}{% endfor %}{% if zp_split_single_page_render == true %}{% assign zp_gb_ids = zp_alternative_entity_metafields['config'].value['globalblocks'] %}{% for zp_gb_id in zp_gb_ids %}{% capture zp_gb_content %}{% assign zp_gb_snippet_name = 'gb-' | append: zp_gb_id | append: '.zipifypages' %}{% include zp_gb_snippet_name %}{% endcapture %}{% capture zp_gb_tag_name %}<zp_gb_{{ zp_gb_id }}></zp_gb_{{ zp_gb_id }}>{% endcapture %}{% assign zp_gb_error = 'Could not find asset snippets/' | append: zp_gb_snippet_name | append: '.liquid' %}{% if zp_gb_content contains zp_gb_error %}{% assign zp_gb_content = '' %}{% assign zp_alternative_entity_content = zp_alternative_entity_content | replace: zp_gb_tag_name, zp_gb_content %}{% else %}{% assign zp_gb_styles =zp_gb_content | split: '<!--ZP_SEPARATOR-->' | first %}{% assign zp_gb_content = zp_gb_content | remove_first: zp_gb_styles | remove: '<!--ZP_SEPARATOR-->' %}{% assign zp_alternative_entity_content = zp_alternative_entity_content | replace: zp_gb_tag_name, zp_gb_content | prepend: zp_gb_styles %}{% endif %}{% endfor %}{% endif %}{% assign zp_split_parts_separator = '' %}{% if zp_split_single_page_render == true %}{% assign zp_split_parts_start = '<zp__script type="text/template" id="zp-main-content">' %}{% assign zp_split_parts_end = '</zp__script>' %}{% assign zp_split_parts_separator = '</zp__script><zp__script type="text/template" id="zp-alternative-content">' %}{% assign zp_replacement_key = '</script' %}{% assign zp_replacement_value = '<\/script' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_replacement_key, zp_replacement_value %}{% assign zp_current_entity_content = zp_split_parts_start | append: zp_current_entity_content | append: zp_split_parts_separator %}{% for zp_part in zp_alternative_entity.metafields['zipifypagesparts'] %}{% assign zp_alternative_entity_content = zp_alternative_entity_content | append: zp_part[1] %}{% endfor %}{% assign zp_booster_coupons_left = '' | append: zp_alternative_entity_metafields['boostercouponsleft'] | strip | default: '0' %}{% assign zp_booster_coupons_left = 0 | plus: zp_booster_coupons_left %}{% if zp_booster_coupons_left > 0 %}{% assign zp_booster_available_block_class = '' %}{% assign zp_booster_unavailable_block_class = 'hidden' %}{% else %}{% assign zp_booster_available_block_class = 'hidden' %}{% assign zp_booster_unavailable_block_class = '' %}{% endif %}{% assign zp_alternative_entity_content = zp_alternative_entity_content | replace: '<zp_booster_coupons_count></zp_booster_coupons_count>', zp_booster_coupons_left | replace: '__zp_booster_available_block_class__', zp_booster_available_block_class | replace: '__zp_booster_unavailable_block_class__', zp_booster_unavailable_block_class | replace: zp_replacement_key, zp_replacement_value %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_alternative_entity_content %}{% assign zp_alternative_entity_content = '' %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_split_parts_end %}{% endif %}{% assign zp_entity_product_tags = 'zp_rch_product6,zp_rch_product5,zp_rch_product4,zp_rch_product3,zp_rch_product2,zp_rch_product1,zp_bvo_product3,zp_bvo_product2,zp_bvo_product1,zp_bvh_product3,zp_bvh_product2,zp_bvh_product1,zp_bv_product3,zp_bv_product2,zp_bv_product1,zp_product3,zp_product2,zp_product1,zp_ofrbox_product,zp_product' | split: ',' %}{% assign zp_entity_product_tags_views = 'recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,best-value-offer-view.zipifypages,best-value-offer-view.zipifypages,best-value-offer-view.zipifypages,best-value-horizontal-view.zipifypages,best-value-horizontal-view.zipifypages,best-value-horizontal-view.zipifypages,best-value-view.zipifypages,best-value-view.zipifypages,best-value-view.zipifypages,three-products-view.zipifypages,three-products-view.zipifypages,three-products-view.zipifypages,offer-box-view.zipifypages,product-view.zipifypages' | split: ',' %}{% assign zp_product_iterator = 0 %}{% assign zp_cross_sell_product_iterator = 0 %}{% if zp_product_selector_suffix == nil %}{% assign zp_product_selector_suffix = '' %}{% endif %}{% assign zp_main_product_selector = 'zpproductselector' | append: zp_product_selector_suffix %}{% assign zp_main_product_selector_suffix = '-wrapper' %}{% assign zp_opened_cross_sell_tag = '<zpdcsproduct ' %}{% assign zp_closed_cross_sell_tag = '</zpdcsproduct>' %}{% assign zp_shw_vrntsslctr_lnktps = 'cart,checkout,cart_current_page,cart_external' | split: ',' %}{% assign zp_include_bold_snippets_link_types = 'cart,checkout,cart_current_page,cart_external' | split: ',' %}{% assign zp_incl_boldcmnsnpt = false %}{% if zp_use_product_markup %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '</zp_rch_product6>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product6>' | replace: '</zp_rch_product5>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product5>' | replace: '</zp_rch_product4>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product4>' | replace: '</zp_rch_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product3>' | replace: '</zp_rch_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product2>' | replace: '</zp_rch_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product1>' | replace: '</zp_bvo_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvo_product3>' | replace: '</zp_bvo_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvo_product2>' | replace: '</zp_bvo_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvo_product1>' | replace: '</zp_bvh_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvh_product3>' | replace: '</zp_bvh_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvh_product2>' | replace: '</zp_bvh_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvh_product1>' | replace: '</zp_bv_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bv_product3>' | replace: '</zp_bv_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bv_product2>' | replace: '</zp_bv_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bv_product1>' | replace: '</zp_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_product3>' | replace: '</zp_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_product2>' | replace: '</zp_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_product1>' | replace: '</zp_ofrbox_product>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_ofrbox_product>' | replace: '</zp_product>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_product>' | replace: '</zpdproduct>', '<zpprdtmpmrkp></zpprdtmpmrkp></zpdproduct>' | replace: '</zpdprdct', '<zpprdtmpmrkp></zpprdtmpmrkp></zpdprdct' | replace_first: '<zpprdtmpmrkp></zpprdtmpmrkp>', '<zpprdmrkp></zpprdmrkp>' | remove: '<zpprdtmpmrkp></zpprdtmpmrkp>' %}{% endif %}{% for zp_entity_product_tag in zp_entity_product_tags %}{% assign zp_opened_product_tag = '<' | append: zp_entity_product_tag | append: '>' %}{% unless zp_current_entity_content contains zp_opened_product_tag %}{% continue %}{% endunless %}{% assign zp_entity_product_view = zp_entity_product_tags_views[forloop.index0] %}{% assign zp_closed_product_tag = '</' | append: zp_entity_product_tag | append: '>' %}{% assign zp_entity_products = zp_current_entity_content | split: zp_opened_product_tag %}{% assign zp_current_entity_content = '' %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% for zp_part in zp_entity_products %}{% assign zp_product_settings_content = zp_part | append: ' ' | split: zp_closed_product_tag %}{% if zp_product_settings_content.size < 2 %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_part %}{% else %}{% assign zp_object_settings_data = zp_product_settings_content[0] %}{% if zp_object_settings_data contains '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_object_settings_data = zp_object_settings_data | remove: '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_display_product_markup = true %}{% else %}{% assign zp_display_product_markup = false %}{% endif %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% assign zp_product_settings_keys = zp_settings_keys %}{% assign zp_product_settings_values = zp_settings_values %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_product_image_position = '' %}{% assign zp_product_image = '' %}{% assign zp_product_image_alt = nil %}{% assign zp_product_image_custom_srcset = '' %}{% assign zp_product_image_attributes = '' %}{% assign zp_product_link_type = 'cart' %}{% assign zp_redirect_product_url = routes.cart_url %}{% assign zp_selected_variants = '' %}{% assign zp_product_discount_data = '' %}{% assign zp_product_custom_title = '' %}{% assign zp_product_custom_description = '' %}{% assign zp_three_products_blocks_count = '3' %}{% assign zp_product_element_id = nil %}{% assign zp_product_type = 'base' %}{% assign zp_bv_lbluptxt_value = '' %}{% assign zp_product_image_type = 'product' %}{% assign zp_product_unit_price_tag_included = false %}{% assign zp_product_image_lazy_load = false %}{% assign zp_product_calculate_bundle_price = false %}{% assign zp_product_default_quantity = '1' %}{% assign zp_product_sold_out_view_enabled = false %}{% assign zp_show_product_image_label = false %}{% assign zp_include_product_button_main_classes = true %}{% assign zp_include_product_button_alignment_classes = true %}{% assign zp_bv_product_static_compare_price = '' %}{% assign zp_bv_product_static_price = '' %}{% assign zp_bv_product_header_title = '' %}{% assign zp_bv_product_additional_description = '' %}{% assign zp_bv_product_description = '' %}{% assign zp_bv_product_additional_header_title = '' %}{% assign zp_product_first_description = '' %}{% assign zp_product_second_description = '' %}{% assign zp_product_static_body_html = '' %}{% assign zp_product_static_title = '' %}{% for zp_setting_key in zp_product_settings_keys %}{% assign zp_prd_stng_vl = '' | append: zp_product_settings_values[forloop.index0] %}{% if zp_setting_key == 'handle' %}{% assign zp_product_handle = zp_prd_stng_vl %}{% elsif zp_setting_key == 'imgpos' %}{% assign zp_product_image_position = zp_prd_stng_vl %}{% elsif zp_setting_key == 'slctvrnt' %}{% assign zp_selected_variants = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdimgsrcset' %}{% assign zp_product_image_custom_srcset = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'prdimg' %}{% assign zp_product_image = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdimgalt' %}{% assign zp_product_image_alt = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdurl' %}{% assign zp_redirect_product_url = zp_prd_stng_vl | replace: '&amp;', '&' | url_escape | replace: '%23', '#' | replace: '%25', '%' %}{% elsif zp_setting_key == 'prdbtnltp' %}{% assign zp_product_link_type = zp_prd_stng_vl %}{% elsif zp_setting_key == 'shwqty' %}{% if zp_prd_stng_vl == 'true' or zp_prd_stng_vl == '1' %}{% assign zp_product_show_quantity = true %}{% else %}{% assign zp_product_show_quantity = false %}{% endif %}{% elsif zp_setting_key == 'prddscntdata' %}{% assign zp_product_discount_data = zp_prd_stng_vl | split: ':' %}{% elsif zp_setting_key == 'prdtype' %}{% assign zp_product_type = zp_prd_stng_vl %}{% elsif zp_setting_key == 'ttl1cont' %}{% assign zp_bv_product_additional_header_title = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'ttl2cont' %}{% assign zp_bv_product_header_title = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'ttl3cont' %}{% assign zp_bv_product_static_compare_price = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'ttl4cont' %}{% assign zp_bv_product_static_price = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'ttl5cont' %}{% assign zp_bv_product_additional_description = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'txtcont' %}{% assign zp_bv_product_description = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'txt1cont' %}{% assign zp_product_first_description = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'txt2cont' %}{% assign zp_product_second_description = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'blckscnt' %}{% assign zp_three_products_blocks_count = zp_prd_stng_vl %}{% elsif zp_setting_key == 'lbluptxt' %}{% assign zp_bv_lbluptxt_value = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdimgtp' %}{% assign zp_product_image_type = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdbdhtml' %}{% assign zp_product_custom_description = zp_prd_stng_vl %}{% assign zp_product_static_body_html = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'prdttl' %}{% assign zp_product_custom_title = zp_prd_stng_vl %}{% assign zp_product_static_title = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'imgimgattr' %}{% assign zp_product_image_attributes = zp_prd_stng_vl %}{% elsif zp_setting_key == 'shwimglbl' %}{% if zp_prd_stng_vl == 'true' %}{% assign zp_show_product_image_label = true %}{% endif %}{% elsif zp_setting_key == 'eid' %}{% assign zp_product_element_id = zp_prd_stng_vl %}{% elsif zp_setting_key == 'qty' %}{% assign zp_product_default_quantity = zp_prd_stng_vl %}{% elsif zp_setting_key == 'unprcincl' %}{% if zp_prd_stng_vl == '1' %}{% assign zp_product_unit_price_tag_included = true %}{% endif %}{% elsif zp_setting_key == 'lzld' %}{% if zp_prd_stng_vl == '1' %}{% assign zp_product_image_lazy_load = true %}{% endif %}{% elsif zp_setting_key == 'sldtv' %}{% if zp_prd_stng_vl == '1' %}{% assign zp_product_sold_out_view_enabled = true %}{% endif %}{% elsif zp_setting_key == 'bndlprc' %}{% if zp_prd_stng_vl == '1' %}{% assign zp_product_calculate_bundle_price = true %}{% endif %}{% elsif zp_setting_key == 'prdbtninmncl' %}{% if zp_prd_stng_vl == '0' %}{% assign zp_include_product_button_main_classes = false %}{% endif %}{% elsif zp_setting_key == 'prdbtninalcl' %}{% if zp_prd_stng_vl == '0' %}{% assign zp_include_product_button_alignment_classes = false %}{% endif %}{% endif %}{% endfor %}{% assign zp_selected_variants = zp_selected_variants | split: ',' %}{% assign product = all_products[zp_product_handle] %}{% assign zp_product_id_size = '' | append: product.id | size %}{% assign zp_product_found = true %}{% if zp_product_id_size < 1 %}{% assign product = nil %}{% assign zp_product_found = false %}{% endif %}{% if zp_product_link_types != blank %}{% assign zp_shw_vrntsslctr = false %}{% for zp_link_type in zp_product_link_types %}{% if zp_shw_vrntsslctr_lnktps contains zp_link_type %}{% assign zp_shw_vrntsslctr = true %}{% break %}{% endif %}{% endfor %}{% elsif zp_shw_vrntsslctr_lnktps contains zp_product_link_type %}{% assign zp_shw_vrntsslctr = true %}{% else %}{% assign zp_shw_vrntsslctr = false %}{% endif %}{% assign zp_show_variants_selectors = zp_shw_vrntsslctr %}{% assign zp_enable_subscription_widget = zp_shw_vrntsslctr %}{% if zp_shw_vrntsslctr %}{% assign zp_incl_boldsnpts = true %}{% else %}{% assign zp_incl_boldsnpts = false %}{% endif %}{% if zp_incl_boldsnpts and zp_app_integrations contains 'productoptionsbybold' %}{% assign zp_intgrt_wboldprdopts = true %}{% else %}{% assign zp_intgrt_wboldprdopts = false %}{% endif %}{% if zp_incl_boldsnpts and zp_app_integrations contains 'quantitybreaksbybold' %}{% assign zp_intgrt_wboldqtbrk = true %}{% else %}{% assign zp_intgrt_wboldqtbrk = false %}{% endif %}{% if zp_app_integrations contains 'subscriptionsbybold' %}{% assign zp_incl_boldsbsrpns = true %}{% else %}{% assign zp_incl_boldsbsrpns = false %}{% endif %}{% capture zp_product_snippet %}{% assign zp_product_iterator = zp_product_iterator | plus: 1 %}{% assign zp_product_selector = zp_main_product_selector | append: zp_product_iterator %}{% assign zp_product_wrapper_selector = zp_product_selector | append: zp_main_product_selector_suffix %}{% include zp_entity_product_view with product %}{% endcapture %}{% assign zp_object_settings_data = 'txtblattr:--:txtblclass:##:class:##::|~|:ttl5blattr:--:ttl5blclass:##:class:##::|~|:ttl4blattr:--:ttl4blclass:##:class:##::|~|:ttl3blattr:--:ttl3blclass:##:class:##::|~|:ttl2blattr:--:ttl2blclass:##:class:##::|~|:prdttlcss:--:prdttlclass:##:class:##::|~|:prdlbtnattr:--:prdlbtnclass:##:class:##::|~|:prdattr:--:prdclass:##:class:##::|~|:prdbtnattr:--:prdbtnclass:##:class:##::|~|:prdblattr:--:prdblclass:##:class:##::|~|:mcontclstpadd:--:mcontclstclass:##:class:##::|~|:mcontattr:--:mcontclass:##:class:##::|~|:lblblattr:--:lblblclass:##:class:##::|~|:imgblattr:--:imgblclass:##:class:##::|~|:img2blattr:--:img2blclass:##:class:##::|~|:descfnts:--:descclass:##:class:##::|~|:bvuppclr:--:bvuppclass:##:class:##::|~|:bvprcsv:--:bvprcsvclass:##:class:##::|~|:bvcrnrattr:--:bvcrnrclass:##:class:##::|~|:bprcttlcss:--:bprcttlclass:##:class:##:' %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_setting_key in zp_settings_keys %}{% assign zp_prd_stng_vl = nil %}{% for zp_prd_stng_k in zp_product_settings_keys %}{% assign zp_product_key = zp_prd_stng_k | downcase %}{% assign zp_replacement_key = zp_setting_key | downcase %}{% if zp_product_key == zp_replacement_key %}{% assign zp_prd_stng_vl = '' | append: zp_product_settings_values[forloop.index0] | strip %}{% break %}{% endif %}{% endfor %}{% assign zp_replace_data = zp_settings_values[forloop.index0] | split: ':##:' %}{% assign zp_replacement_key = 'zps_' | append: zp_replace_data[0] | downcase %}{% assign zp_find_attr_sq = '' | append: zp_replace_data[1] | strip | append: "='" %}{% assign zp_find_attr_dq = '' | append: zp_replace_data[1] | strip | append: '="' %}{% assign zp_replacement_default = '' | append: zp_replace_data[2] | strip %}{% unless zp_prd_stng_vl == nil %}{% assign zp_replacement_value = zp_prd_stng_vl | split: zp_find_attr_sq %}{% assign zp_replacement_value = zp_replacement_value[1] | split: "'" | first | strip %}{% if zp_replacement_value.size < 1 %}{% assign zp_replacement_value = zp_prd_stng_vl | split: zp_find_attr_dq %}{% assign zp_replacement_value = zp_replacement_value[1] | split: '"' | first | strip %}{% endif %}{% if zp_replacement_value.size < 1 %}{% assign zp_replacement_value = zp_replacement_default %}{% endif %}{% assign zp_replacement_key_url_escape = zp_replacement_key | append: 'url_escape' | downcase %}{% assign zp_replacement_value_url_escape = zp_replacement_value | url_escape %}{% assign zp_replacement_key_escape = zp_replacement_key | append: 'escape' | downcase %}{% assign zp_replacement_value_escape = zp_replacement_value | escape %}{% assign zp_product_snippet = zp_product_snippet | replace: zp_replacement_key_url_escape, zp_replacement_value_url_escape | replace: zp_replacement_key_escape, zp_replacement_value_escape | replace: zp_replacement_key, zp_replacement_value %}{% endunless %}{% endfor %}{% assign zp_replace_data = '' %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% for zp_setting_key in zp_product_settings_keys %}{% assign zp_replacement_key = 'zps_' | append: zp_setting_key | downcase %}{% assign zp_replacement_value = '' | append: zp_product_settings_values[forloop.index0] %}{% if zp_replacement_key == 'zps_prdbtncpt' %}{% assign zp_replacement_value = zp_replacement_value | replace: '\', '&bsol;' %}{% endif %}{% assign zp_replacement_key_url_escape = zp_replacement_key | append: 'url_escape' | downcase %}{% assign zp_replacement_value_url_escape = zp_replacement_value | url_escape %}{% assign zp_replacement_key_escape = zp_replacement_key | append: 'escape' | downcase %}{% assign zp_replacement_value_escape = zp_replacement_value | escape %}{% assign zp_product_snippet = zp_product_snippet | replace: zp_replacement_key_url_escape, zp_replacement_value_url_escape | replace: zp_replacement_key_escape, zp_replacement_value_escape | replace: zp_replacement_key, zp_replacement_value %}{% endfor %}{% assign zp_product_snippet = zp_product_snippet | strip %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_product_snippet %}{% assign zp_product_snippet = zp_product_settings_content[1] | strip %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_product_snippet %}{% endif %}{% endfor %}{% if zp_product_link_type == 'cart' %}{% assign zp_redirect_product_url = routes.cart_url %}{% endif %}{% assign zp_part = nil %}{% assign zp_entity_products = nil %}{% assign zp_product_snippet = nil %}{% assign zp_product_settings_content = nil %}{% assign zp_product_settings_keys = nil %}{% endfor %}{% if zp_current_entity_content contains '<zpdproduct ' %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_schema_config = zp_current_entity_metafields['config'].value['schema'] %}{% else %}{% assign zp_current_entity_schema_config = zp_current_entity_metafields['config']['schema'] %}{% endif %}{% include 'dynamic-product.zipifypages', renderscope: 'tag' %}{% assign zp_current_entity_schema_config = nil %}{% endif %}{% if zp_current_entity_content contains '<zpdprdct' %}{% include 'dynamic-product.zipifypages', renderscope: 'metafield' %}{% endif %}{% if zp_split_tests_available and zp_alternative_entity_present and zp_split_single_page_render and zp_current_entity_content contains '<zpdprdct' %}{% include 'dynamic-product.zipifypages', renderscope: 'metafield', zp_current_entity_metafields: zp_alternative_entity_metafields %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '<zpprdmrkp></zpprdmrkp>', '' %}{% if zp_current_entity_content contains '<zp_cart_count></zp_cart_count>' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_cart_count></zp_cart_count>', cart.item_count %}{% endif %}{% if zp_current_entity_content contains '<zp_recharge_subscribe_title></zp_recharge_subscribe_title>' or zp_current_entity_content contains '<zp_recharge_onetime_title></zp_recharge_onetime_title>' %}{% assign zp_main_subscription_settings = shop.metafields['subscriptions'] %}{% assign zp_recharge_subscribe_title = '' | append: zp_main_subscription_settings.subscribe_message | strip | default: 'Subscribe & Save' %}{% assign zp_recharge_onetime_title = '' | append: zp_main_subscription_settings.onetime_message | strip | default: 'One-Time Purchase' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_recharge_subscribe_title></zp_recharge_subscribe_title>', zp_recharge_subscribe_title | replace: '<zp_recharge_onetime_title></zp_recharge_onetime_title>', zp_recharge_onetime_title %}{% endif %}{% if zp_current_entity_content contains '<zp_booster_coupons_count></zp_booster_coupons_count>' or zp_current_entity_content contains '__zp_booster_available_block_class__' or zp_current_entity_content contains '__zp_booster_unavailable_block_class__' %}{% assign zp_booster_coupons_left = '' | append: zp_current_entity_metafields['boostercouponsleft'] | strip | default: '0' %}{% assign zp_booster_coupons_left = 0 | plus: zp_booster_coupons_left %}{% if zp_booster_coupons_left > 0 %}{% assign zp_booster_available_block_class = '' %}{% assign zp_booster_unavailable_block_class = 'hidden' %}{% else %}{% assign zp_booster_available_block_class = 'hidden' %}{% assign zp_booster_unavailable_block_class = '' %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_booster_coupons_count></zp_booster_coupons_count>', zp_booster_coupons_left | replace: '__zp_booster_available_block_class__', zp_booster_available_block_class | replace: '__zp_booster_unavailable_block_class__', zp_booster_unavailable_block_class %}{% endif %}{% assign zp_object_settings_data = 'txtblclassescape:--::|~|:txt2classescape:--::|~|:txt1classescape:--::|~|:ttl5blclassescape:--::|~|:ttl4blclassescape:--::|~|:ttl3classescape:--::|~|:ttl3blclassescape:--::|~|:ttl2classescape:--::|~|:ttl2blclassescape:--::|~|:ttl1classescape:--::|~|:ttl1blclassescape:--::|~|:slctvrnturl_escape:--::|~|:selclassescape:--::|~|:qtyescape:--:1:|~|:prdttlclassescape:--::|~|:prdlbtnclassescape:--::|~|:prdimglblttl:--::|~|:prdimglblattr:--::|~|:prdimgclassescape:--::|~|:prdimgaltescape:--::|~|:prddscntdataurl_escape:--::|~|:prdclassescape:--::|~|:prdbtnltpescape:--::|~|:prdbtneltescape:--:_self:|~|:prdbtncpturl_escape:--::|~|:prdbtncpt:--::|~|:prdbtnclassescape:--::|~|:prdbtnalignescape:--::|~|:prdblclassescape:--::|~|:mcontclstclassescape:--::|~|:mcontclassescape:--::|~|:lblvisattr:--::|~|:lbluptxt:--::|~|:lbltxtcont:--::|~|:lblmdltxt:--::|~|:lblblclassescape:--::|~|:imglblttl:--::|~|:imglblattr:--::|~|:imgimgattr:--::|~|:imgblclassescape:--::|~|:img2blclassescape:--::|~|:img2attr:--::|~|:descclassescape:--::|~|:dcrt5classescape:--::|~|:dcrt4classescape:--::|~|:dcrt3classescape:--::|~|:dcrt2classescape:--::|~|:dcrt1classescape:--::|~|:crtmsgclassurl_escape:--::|~|:clstidescape:--::|~|:bvuppclassescape:--::|~|:bvprcsvclassescape:--::|~|:bvcrnrclassescape:--::|~|:brdclassescape:--::|~|:brdblclassescape:--::|~|:brd3blclassescape:--::|~|:brd2blclassescape:--::|~|:brd1blclassescape:--::|~|:bprcttlclassescape:--::|~|:addcrtmsgurl_escape:--:' %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_setting_key in zp_settings_keys %}{% assign zp_replacement_key = 'zps_' | append: zp_setting_key | downcase %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_replacement_key, zp_settings_values[forloop.index0] %}{% endfor %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_entity_product_tags = nil %}{% assign zp_entity_product_tags_views = nil %}{% assign zp_product_settings_keys = nil %}{% assign zp_product_settings_values = nil %}{% assign zp_selected_variants = nil %}{% assign zp_translation = 'layout.customer.account' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'customer.account_fallback' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "My Account" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_account_link_title></zp_account_link_title>', zp_translation %}{% assign zp_translation = 'layout.customer.log_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'customer.log_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Log out" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_logout_link_title></zp_logout_link_title>', zp_translation %}{% assign zp_translation = 'layout.customer.log_in' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'customer.log_in' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Login" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_login_link_title></zp_login_link_title>', zp_translation %}{% assign zp_translation = 'layout.customer.create_account' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'customer.account_fallback' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Register" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_register_link_title></zp_register_link_title>', zp_translation %}{% assign zp_translation = 'layout.cart.title' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'templates.cart.cart' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Cart" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_cart_link_title></zp_cart_link_title>', zp_translation %}{% assign zp_translation = 'general.search.placeholder' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'general.search.search' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Search" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: 'zp_search_placeholder', zp_translation %}{% if zp_shop_metafields['localization'].type == 'json' %}{% assign zp_localization = zp_shop_metafields['localization'].value %}{% else %}{% assign zp_localization = zp_shop_metafields['localization'] %}{% endif %}{% assign zp_tfmemail = zp_localization['form']['email'] | strip | escape | default: "Email Address" | replace: '\', '&#92;' %}{% assign zp_tfmfirst_name = zp_localization['form']['first_name'] | strip | escape | default: "First Name" | replace: '\', '&#92;' %}{% assign zp_tfmlast_name = zp_localization['form']['last_name'] | strip | escape | default: "Last Name" | replace: '\', '&#92;' %}{% assign zp_tfmphone_number = zp_localization['form']['phone_number'] | strip | escape | default: "Phone Number" | replace: '\', '&#92;' %}{% assign zp_tdsfull = zp_localization['days']['full'] | strip | escape | default: "Days" | replace: '\', '&#92;' %}{% assign zp_tdsshort = zp_localization['days']['short'] | strip | escape | default: "Days" | replace: '\', '&#92;' %}{% assign zp_tdsshortest = zp_localization['days']['shortest'] | strip | escape | default: "D" | replace: '\', '&#92;' %}{% assign zp_thsfull = zp_localization['hours']['full'] | strip | escape | default: "Hours" | replace: '\', '&#92;' %}{% assign zp_thsshort = zp_localization['hours']['short'] | strip | escape | default: "Hrs" | replace: '\', '&#92;' %}{% assign zp_thsshortest = zp_localization['hours']['shortest'] | strip | escape | default: "H" | replace: '\', '&#92;' %}{% assign zp_tmsfull = zp_localization['minutes']['full'] | strip | escape | default: "Minutes" | replace: '\', '&#92;' %}{% assign zp_tmsshort = zp_localization['minutes']['short'] | strip | escape | default: "Min" | replace: '\', '&#92;' %}{% assign zp_tmsshortest = zp_localization['minutes']['shortest'] | strip | escape | default: "M" | replace: '\', '&#92;' %}{% assign zp_tssfull = zp_localization['seconds']['full'] | strip | escape | default: "Seconds" | replace: '\', '&#92;' %}{% assign zp_tssshort = zp_localization['seconds']['short'] | strip | escape | default: "Sec" | replace: '\', '&#92;' %}{% assign zp_tssshortest = zp_localization['seconds']['shortest'] | strip | escape | default: "S" | replace: '\', '&#92;' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zptfmemail></zptfmemail>',zp_tfmemail | replace: '[_zptfmemail_]',zp_tfmemail | replace: '<zptfmfirst_name></zptfmfirst_name>',zp_tfmfirst_name | replace: '[_zptfmfirst_name_]',zp_tfmfirst_name | replace: '<zptfmlast_name></zptfmlast_name>',zp_tfmlast_name | replace: '[_zptfmlast_name_]',zp_tfmlast_name | replace: '<zptfmphone_number></zptfmphone_number>',zp_tfmphone_number | replace: '[_zptfmphone_number_]',zp_tfmphone_number | replace: '<zptdsfull></zptdsfull>',zp_tdsfull | replace: '[_zptdsfull_]',zp_tdsfull | replace: '<zptdsshort></zptdsshort>',zp_tdsshort | replace: '[_zptdsshort_]',zp_tdsshort | replace: '<zptdsshortest></zptdsshortest>',zp_tdsshortest | replace: '[_zptdsshortest_]',zp_tdsshortest | replace: '<zpthsfull></zpthsfull>',zp_thsfull | replace: '[_zpthsfull_]',zp_thsfull | replace: '<zpthsshort></zpthsshort>',zp_thsshort | replace: '[_zpthsshort_]',zp_thsshort | replace: '<zpthsshortest></zpthsshortest>',zp_thsshortest | replace: '[_zpthsshortest_]',zp_thsshortest | replace: '<zptmsfull></zptmsfull>',zp_tmsfull | replace: '[_zptmsfull_]',zp_tmsfull | replace: '<zptmsshort></zptmsshort>',zp_tmsshort | replace: '[_zptmsshort_]',zp_tmsshort | replace: '<zptmsshortest></zptmsshortest>',zp_tmsshortest | replace: '[_zptmsshortest_]',zp_tmsshortest | replace: '<zptssfull></zptssfull>',zp_tssfull | replace: '[_zptssfull_]',zp_tssfull | replace: '<zptssshort></zptssshort>',zp_tssshort | replace: '[_zptssshort_]',zp_tssshort | replace: '<zptssshortest></zptssshortest>',zp_tssshortest | replace: '[_zptssshortest_]',zp_tssshortest %}{% assign zp_tfmemail = '' %}{% assign zp_tfmfirst_name = '' %}{% assign zp_tfmlast_name = '' %}{% assign zp_tfmphone_number = '' %}{% assign zp_tdsfull = '' %}{% assign zp_tdsshort = '' %}{% assign zp_tdsshortest = '' %}{% assign zp_thsfull = '' %}{% assign zp_thsshort = '' %}{% assign zp_thsshortest = '' %}{% assign zp_tmsfull = '' %}{% assign zp_tmsshort = '' %}{% assign zp_tmsshortest = '' %}{% assign zp_tssfull = '' %}{% assign zp_tssshort = '' %}{% assign zp_tssshortest = '' %}{% assign zp_localization = nil %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_judgeme_settings = zp_current_entity_metafields['config'].value['judgeme'] %}{% else %}{% assign zp_judgeme_settings = zp_current_entity_metafields['config']['judgeme'] %}{% endif %}{% for zp_judgeme_data in zp_judgeme_settings %}{% assign zp_tag_config = zp_judgeme_data | last %}{% assign zp_judgeme_product = all_products[zp_tag_config.handle] %}{% assign zp_judgeme_product_id_size = '' | append: zp_judgeme_product.id | size %}{% if zp_judgeme_product_id_size > 0 %}{% assign zp_judgeme_widget_content = '' | append: zp_judgeme_product.metafields.judgeme.widget | strip | replace: 'itemprop', 'data-itemprop' | replace: 'itemscope', 'data-itemscope' %}{% assign zp_judgeme_product_title = zp_judgeme_product.title | escape %}{% assign zp_judgeme_content = '<div style="clear:both"></div><div id="judgeme_product_reviews" class="jdgm-widget jdgm-review-widget" data-product-title="' | append: zp_judgeme_product_title | append: '" data-id="' | append: zp_judgeme_product.id | append: '">' | append: zp_judgeme_widget_content | append: '</div>' %}{% else %}{% assign zp_judgeme_content = '' %}{% endif %}{% assign zp_tag_id = zp_judgeme_data | first %}{% assign zp_judgeme_tag_name = '<zpjudgeme' | append: zp_tag_id | append: '></zpjudgeme' | append: zp_tag_id | append: '>' %}{% capture zp_uniq_judgeme_tag_name %}<zpjudgeme_{{ zp_tag_id }}></zpjudgeme_{{ zp_tag_id }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_judgeme_tag_name, zp_judgeme_content | replace: zp_uniq_judgeme_tag_name, zp_judgeme_content %}{% endfor %}{% assign zp_judgeme_product = nil %}{% assign zp_judgeme_product_title = nil %}{% assign zp_judgeme_widget_content = nil %}{% if zp_split_single_page_render %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_judgeme_settings = zp_alternative_entity_metafields['config'].value['judgeme'] %}{% else %}{% assign zp_judgeme_settings = zp_alternative_entity_metafields['config']['judgeme'] %}{% endif %}{% for zp_judgeme_data in zp_judgeme_settings %}{% assign zp_tag_config = zp_judgeme_data | last %}{% assign zp_judgeme_product = all_products[zp_tag_config.handle] %}{% assign zp_judgeme_product_id_size = '' | append: zp_judgeme_product.id | size %}{% if zp_judgeme_product_id_size > 0 %}{% assign zp_judgeme_widget_content = '' | append: zp_judgeme_product.metafields.judgeme.widget | strip | replace: 'itemprop', 'data-itemprop' | replace: 'itemscope', 'data-itemscope' %}{% assign zp_judgeme_product_title = zp_judgeme_product.title | escape %}{% assign zp_judgeme_content = '<div style="clear:both"></div><div id="judgeme_product_reviews" class="jdgm-widget jdgm-review-widget" data-product-title="' | append: zp_judgeme_product_title | append: '" data-id="' | append: zp_judgeme_product.id | append: '">' | append: zp_judgeme_widget_content | append: '</div>' %}{% else %}{% assign zp_judgeme_content = '' %}{% endif %}{% assign zp_tag_id = zp_judgeme_data | first %}{% assign zp_judgeme_tag_name = '<zpjudgeme' | append: zp_tag_id | append: '></zpjudgeme' | append: zp_tag_id | append: '>' %}{% capture zp_uniq_judgeme_tag_name %}<zpjudgeme_{{ zp_tag_id }}></zpjudgeme_{{ zp_tag_id }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_judgeme_tag_name, zp_judgeme_content | replace: zp_uniq_judgeme_tag_name, zp_judgeme_content %}{% endfor %}{% assign zp_judgeme_product = nil %}{% assign zp_judgeme_product_title = nil %}{% assign zp_judgeme_widget_content = nil %}{% endif %}{% assign zp_judgeme_settings = nil %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_opinew_settings = zp_current_entity_metafields['config'].value['opinew'] %}{% else %}{% assign zp_opinew_settings = zp_current_entity_metafields['config']['opinew'] %}{% endif %}{% for zp_opinew_data in zp_opinew_settings %}{% assign zp_tag_config = zp_opinew_data | last %}{% assign zp_opinew_product = all_products[zp_tag_config.handle] %}{% assign zp_opinew_product_id_size = '' | append: zp_opinew_product.id | size %}{% if zp_opinew_product_id_size > 0 %}{% capture zp_opinew_content %}<div id="opinew-reviews-product-page-code"><span id="opinew-plugin" data-server-address="https://www.opinew.com" data-opw-prodreviews="{{ zp_opinew_product.metafields.opinew_metafields['product_plugin'] }}" data-opinew-shop-id="{{ shop.id }}" data-shop-url="{{ shop.domain }}" data-platform-product-id="{{ zp_opinew_product.id }}"><span id="opinew_product_plugin_app"></span></span></div>{% endcapture %}{% else %}{% assign zp_opinew_content = '' %}{% endif %}{% assign zp_tag_id = zp_opinew_data | first %}{% assign zp_opinew_tag_name = '<zpopinew' | append: zp_tag_id | append: '></zpopinew' | append: zp_tag_id | append: '>' %}{% capture zp_uniq_opinew_tag_name %}<zpopinew_{{ zp_tag_id }}></zpopinew_{{ zp_tag_id }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_opinew_tag_name, zp_opinew_content | replace: zp_uniq_opinew_tag_name, zp_opinew_content %}{% endfor %}{% if zp_split_single_page_render %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_opinew_settings = zp_alternative_entity_metafields['config'].value['opinew'] %}{% else %}{% assign zp_opinew_settings = zp_alternative_entity_metafields['config']['opinew'] %}{% endif %}{% for zp_opinew_data in zp_opinew_settings %}{% assign zp_tag_config = zp_opinew_data | last %}{% assign zp_opinew_product = all_products[zp_tag_config.handle] %}{% assign zp_opinew_product_id_size = '' | append: zp_opinew_product.id | size %}{% if zp_opinew_product_id_size > 0 %}{% capture zp_opinew_content %}<div id="opinew-reviews-product-page-code"><span id="opinew-plugin" data-server-address="https://www.opinew.com" data-opw-prodreviews="{{ zp_opinew_product.metafields.opinew_metafields['product_plugin'] }}" data-opinew-shop-id="{{ shop.id }}" data-shop-url="{{ shop.domain }}" data-platform-product-id="{{ zp_opinew_product.id }}"><span id="opinew_product_plugin_app"></span></span></div>{% endcapture %}{% else %}{% assign zp_opinew_content = '' %}{% endif %}{% assign zp_tag_id = zp_opinew_data | first %}{% assign zp_opinew_tag_name = '<zpopinew' | append: zp_tag_id | append: '></zpopinew' | append: zp_tag_id | append: '>' %}{% capture zp_uniq_opinew_tag_name %}<zpopinew_{{ zp_tag_id }}></zpopinew_{{ zp_tag_id }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_opinew_tag_name, zp_opinew_content | replace: zp_uniq_opinew_tag_name, zp_opinew_content %}{% endfor %}{% endif %}{% assign zp_opinew_settings = nil %}{% if routes.root_url != '/' %}{% assign zp_rtlclurl = routes.root_url %}{% assign zp_rtindexrplc = '="' | append: zp_rtlclurl | append: '/"' %}{% assign zp_rtindexqrplc = '="' | append: zp_rtlclurl | append: '/?' %}{% assign zp_rtindexhrplc = '="' | append: zp_rtlclurl | append: '/#' %}{% assign zp_rtpagesrplc = '="' | append: zp_rtlclurl | append: '/pages/' %}{% assign zp_rtcartrplc = '="' | append: zp_rtlclurl | append: '/cart"' %}{% assign zp_rtcartqrplc = '="' | append: zp_rtlclurl | append: '/cart?' %}{% assign zp_rtcartprplc = '="' | append: zp_rtlclurl | append: '/cart/' %}{% assign zp_rtproductsrplc = '="' | append: zp_rtlclurl | append: '/products/' %}{% assign zp_rtcollectionsrplc = '="' | append: zp_rtlclurl | append: '/collections/' %}{% assign zp_rtaccountrplc = '="' | append: zp_rtlclurl | append: '/account"' %}{% assign zp_rtaccountlgnrplc = '="' | append: zp_rtlclurl | append: '/account/login"' %}{% assign zp_rtaccountrgstrrplc = '="' | append: zp_rtlclurl | append: '/account/register"' %}{% assign zp_rtaccountlgtrplc = '="' | append: zp_rtlclurl | append: '/account/logout"' %}{% assign zp_rtsearchrplc = '="' | append: zp_rtlclurl | append: '/search"' %}{% assign zp_rtsearchqrplc = '="' | append: zp_rtlclurl | append: '/search?' %}{% assign zp_rtcheckoutrplc = '="' | append: zp_rtlclurl | append: '/checkout"' %}{% assign zp_rtcheckoutqrplc = '="' | append: zp_rtlclurl | append: '/checkout?' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '="/"', zp_rtindexrplc | replace: '="/?', zp_rtindexqrplc | replace: '="/#', zp_rtindexhrplc | replace: '="/pages/', zp_rtpagesrplc | replace: '="/cart"', zp_rtcartrplc | replace: '="/cart?', zp_rtcartqrplc | replace: '="/cart/', zp_rtcartprplc | replace: '="/products/', zp_rtproductsrplc | replace: '="/collections/', zp_rtcollectionsrplc | replace: '="/account"', zp_rtaccountrplc | replace: '="/account/login"', zp_rtaccountlgnrplc | replace: '="/account/register"', zp_rtaccountrgstrrplc | replace: '="/account/logout"', zp_rtaccountlgtrplc | replace: '="/search"', zp_rtsearchrplc | replace: '="/search?', zp_rtsearchqrplc | replace: '="/checkout"', zp_rtcheckoutrplc | replace: '="/checkout?', zp_rtcheckoutqrplc %}{% assign zp_rtlclurlsch = shop.domain %}{% assign zp_rtlclurl = zp_rtlclurlsch | append: routes.root_url %}{% assign zp_rtindexrplc = '="https://' | append: zp_rtlclurl | append: '/"' %}{% assign zp_rtindexsch = '="https://' | append: zp_rtlclurlsch | append: '/"' %}{% assign zp_rtindexerplc = '="https://' | append: zp_rtlclurl | append: '"' %}{% assign zp_rtindexesch = '="https://' | append: zp_rtlclurlsch | append: '"' %}{% assign zp_rtindexqrplc = '="https://' | append: zp_rtlclurl | append: '/?' %}{% assign zp_rtindexqsch = '="https://' | append: zp_rtlclurlsch | append: '/?' %}{% assign zp_rtindexhrplc = '="https://' | append: zp_rtlclurl | append: '/#' %}{% assign zp_rtindexhsch = '="https://' | append: zp_rtlclurlsch | append: '/#' %}{% assign zp_rtpagesrplc = '="https://' | append: zp_rtlclurl | append: '/pages/' %}{% assign zp_rtpagessch = '="https://' | append: zp_rtlclurlsch | append: '/pages/' %}{% assign zp_rtcartrplc = '="https://' | append: zp_rtlclurl | append: '/cart"' %}{% assign zp_rtcartsch = '="https://' | append: zp_rtlclurlsch | append: '/cart"' %}{% assign zp_rtcartqrplc = '="https://' | append: zp_rtlclurl | append: '/cart?' %}{% assign zp_rtcartqsch = '="https://' | append: zp_rtlclurlsch | append: '/cart?' %}{% assign zp_rtcartprplc = '="https://' | append: zp_rtlclurl | append: '/cart/' %}{% assign zp_rtcartpsch = '="https://' | append: zp_rtlclurlsch | append: '/cart/' %}{% assign zp_rtproductsrplc = '="https://' | append: zp_rtlclurl | append: '/products/' %}{% assign zp_rtproductssch = '="https://' | append: zp_rtlclurlsch | append: '/products/' %}{% assign zp_rtcollectionsrplc = '="https://' | append: zp_rtlclurl | append: '/collections/' %}{% assign zp_rtcollectionssch = '="https://' | append: zp_rtlclurlsch | append: '/collections/' %}{% assign zp_rtaccountrplc = '="https://' | append: zp_rtlclurl | append: '/account"' %}{% assign zp_rtaccountsch = '="https://' | append: zp_rtlclurlsch | append: '/account"' %}{% assign zp_rtaccountlgnrplc = '="https://' | append: zp_rtlclurl | append: '/account/login"' %}{% assign zp_rtaccountlgnsch = '="https://' | append: zp_rtlclurlsch | append: '/account/login"' %}{% assign zp_rtaccountrgstrrplc = '="https://' | append: zp_rtlclurl | append: '/account/register"' %}{% assign zp_rtaccountrgstrsch = '="https://' | append: zp_rtlclurlsch | append: '/account/register"' %}{% assign zp_rtaccountlgtrplc = '="https://' | append: zp_rtlclurl | append: '/account/logout"' %}{% assign zp_rtaccountlgtsch = '="https://' | append: zp_rtlclurlsch | append: '/account/logout"' %}{% assign zp_rtsearchrplc = '="https://' | append: zp_rtlclurl | append: '/search"' %}{% assign zp_rtsearchsch = '="https://' | append: zp_rtlclurlsch | append: '/search"' %}{% assign zp_rtsearchqrplc = '="https://' | append: zp_rtlclurl | append: '/search?' %}{% assign zp_rtsearchqsch = '="https://' | append: zp_rtlclurlsch | append: '/search?' %}{% assign zp_rtcheckoutrplc = '="https://' | append: zp_rtlclurl | append: '/checkout"' %}{% assign zp_rtcheckoutsch = '="https://' | append: zp_rtlclurlsch | append: '/checkout"' %}{% assign zp_rtcheckoutqrplc = '="https://' | append: zp_rtlclurl | append: '/checkout?' %}{% assign zp_rtcheckoutqsch = '="https://' | append: zp_rtlclurlsch | append: '/checkout?' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_rtindexsch, zp_rtindexrplc | replace: zp_rtindexesch, zp_rtindexerplc | replace: zp_rtindexqsch, zp_rtindexqrplc | replace: zp_rtindexhsch, zp_rtindexhrplc | replace: zp_rtpagessch, zp_rtpagesrplc | replace: zp_rtcartsch, zp_rtcartrplc | replace: zp_rtcartqsch, zp_rtcartqrplc | replace: zp_rtcartpsch, zp_rtcartprplc | replace: zp_rtproductssch, zp_rtproductsrplc | replace: zp_rtcollectionssch, zp_rtcollectionsrplc | replace: zp_rtaccountsch, zp_rtaccountrplc | replace: zp_rtaccountlgnsch, zp_rtaccountlgnrplc | replace: zp_rtaccountrgstrsch, zp_rtaccountrgstrrplc | replace: zp_rtaccountlgtsch, zp_rtaccountlgtrplc | replace: zp_rtsearchsch, zp_rtsearchrplc | replace: zp_rtsearchqsch, zp_rtsearchqrplc | replace: zp_rtcheckoutsch, zp_rtcheckoutrplc | replace: zp_rtcheckoutqsch, zp_rtcheckoutqrplc %}{% endif %}{% assign product = nil %}{% if zp_app_integrations contains 'productoptionsbybold' %}{% assign zp_intgrt_wboldprdopts = true %}{% else %}{% assign zp_intgrt_wboldprdopts = false %}{% endif %}{% if zp_intgrt_wboldprdopts and zp_incl_boldcmnsnpt %}{% capture zp_bold_common_snippet %} {% render 'bold-common' %} {% endcapture %}{% assign zp_bold_common_snippet_separator = '</script>' %}{% assign zp_bold_common_snippet_parts = zp_bold_common_snippet | split: zp_bold_common_snippet_separator %}{% assign zp_bold_common_snippet_scripts_content = '' %}{% assign zp_bold_common_snippet_parts_size = zp_bold_common_snippet_parts | size | minus: 1 %}{% for zp_bold_common_snippet_parts_idx in (1..zp_bold_common_snippet_parts_size) %}{% if forloop.first == true %}{% assign zp_bold_common_snippet_join_string = '' %}{% else %}{% assign zp_bold_common_snippet_join_string = zp_bold_common_snippet_separator %}{% endif %}{% assign zp_bold_common_snippet_scripts_content = zp_bold_common_snippet_scripts_content | append: zp_bold_common_snippet_join_string | append: zp_bold_common_snippet_parts[zp_bold_common_snippet_parts_idx] %}{% endfor %}{% assign zp_bold_common_snippet_init_script_content = zp_bold_common_snippet_parts | first | append: zp_bold_common_snippet_separator %}{% assign zp_bold_common_snippet_parts = nil %}{% assign zp_bold_common_included = true %}{% else %}{% assign zp_bold_common_snippet_init_script_content = '' %}{% assign zp_bold_common_snippet_scripts_content = '' %}{% assign zp_bold_common_included = false %}{% endif %}{% assign zp_no_image_url = 'no-image.gif' | img_url: '1080x' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '"zps_medimurl"', zp_no_image_url | replace: '"zps_medimgsrc"', zp_no_image_url | replace: '"zps_medsurl"', zp_no_image_url | replace: 'zps_medheig', '1024' | replace: 'zps_medwid', '1024' | replace: 'zps_medalt', 'no-image' | replace: 'zps_medvid', '' | replace: 'zps_medpos', '1' %}{% assign product = zp_original_product %}{% unless zp_alternative_entity_present == true %}{% assign zp_wrapper_classes = "zp " | append: zp_entity_attributes_class | append: '-' | append: zp_current_entity.id %}{% if shop.customer_accounts_enabled %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-customer-accounts' %}{% endif %}{% if shop.customer_accounts_enabled and customer %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-account-authorized' %}{% endif %}{% if zp_alternative_entity_present == true %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-split-test' %}{% endif %}{% if zp_entity_custom_template and zp_entity_fixed_layout != true %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-store-header-footer' %}{% endif %}{% if zp_entity_with_default_styles %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-default-styles-text' %}{% endif %}{% if zp_entity_fixed_layout %}{% assign zp_entity_layout_wrapper_class = ' zpa-fixed-layout' %}{% elsif zp_entity_custom_template != true %}{% assign zp_entity_layout_wrapper_class = ' zpa-wide-layout' %}{% else %}{% assign zp_entity_layout_wrapper_class = '' %}{% endif %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: zp_entity_layout_wrapper_class %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-' | append: zp_entity_marker | append: '-template' %}{% endunless %}<div class="{{ zp_wrapper_classes }}{% assign zp_wrapper_classes = nil %}">{% if zp_bold_common_included %}{{ zp_bold_common_snippet_init_script_content }}{% assign zp_bold_common_snippet_init_script_content = "" %}{% endif %}{{ zp_current_entity_content | strip }}{% assign zp_current_entity_content = nil %}{% if zp_bold_common_included %}{{ zp_bold_common_snippet_scripts_content | strip }}{% unless zp_bold_common_snippet_scripts_content contains '.js' %}{% capture zp_bold_common_snippet_scripts_content %}{% render 'sc-includes' %}{% endcapture %}{% unless zp_bold_common_snippet_scripts_content contains 'Could not find asset snippets/sc-includes.liquid' %}{{ zp_bold_common_snippet_scripts_content | strip }}{% endunless %}{% endunless %}{% assign zp_bold_common_snippet_scripts_content = '' %}{% endif %}</div><div style="display:none;"><div id="form-popup"></div></div>{% if zp_app_integrations contains 'bestcurrencyconverter' %}<select class="single-option-selector noreplace" id="zp-integration-option-selector" style="display:none;" aria-hidden="true"></select>{% endif %}{% endif %}
{% schema %}
{
  "name": "ZP ProductFooterContent",
  "tag": "section",
  "class": "zpa-published-page-holder",
  "templates": ["product"]
}
{% endschema %}