{% comment %}
  ** Footer - promotional - static **
{% endcomment %}

{% assign id = section.id %}
{% comment %} Advanced {% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{%- assign locale_selector = false -%}
{%- assign currency_selector = false -%}

{%- if section.settings.show_currency_selector and shop.enabled_currencies.size > 1 or localization.available_countries.size > 1 -%}
  {%- assign currency_selector = true -%}
{%- endif -%}

{%- if section.settings.show_locale_selector and shop.published_locales.size > 1 -%}
  {%- assign locale_selector = true -%}
{%- endif -%}

{% comment %} Section specific CSS {% endcomment %}
{% capture section_css -%}
  .footer__promo-container {
    background-color: {{ section.settings.promo_background }};
    color: {{ section.settings.promo_text }};
  }
{%- endcapture -%}
{% style %}
  {% render 'css-loop',
          css: section_css,
          id: id
%}
  {% render 'css-loop',
          css: custom_css,
          id: id
%}
{% endstyle %}


{% comment %} HTML markup {% endcomment %}
<footer class="footer__container {{ css_class }}">
  <div class="container footer__promo has-no-side-gutter is-flex-{{ section.settings.promo_position }}">
    <div class="footer__content {% if section.settings.enable_promo %} two-thirds {% else %} one-whole {% endif %} column medium-down--one-whole has-padding-top has-padding-bottom">
      <div class="footer__wrap container equal-columns--outside-trim">
        {% for block in section.blocks %}
          <div
            id="shopify-section-{{ block.id }}"
            class="footer__block block__{{ block.id }} block__{{ block.type | downcase | replace: '_', '-' }}
                      one-whole
                      column"
            {{ block.shopify_attributes }}>
            {% if block.type == 'logo' %}
              {% comment %} Logo {% endcomment %}
              <div class="is-flex is-justify-{{ block.settings.alignment }}">
                <a
                  class="footer__logo-wrapper is-{{ block.settings.size }}"
                  href="{{ routes.root_url }}"
                  title="{{ shop.name }}">
                  {% if block.settings.logo %}
                    {%
                      render 'image-element'
                      , image: block.settings.logo
                      , alt: block.settings.logo.alt
                      , additional_classes: 'footer__logo'
                      , focal_point: block.settings.logo.presentation.focal_point
                      ,
                    %}
                  {% else %}
                    <h2 class="footer__heading text-align-center">{{ shop.name }}</h2>
                  {% endif %}
                </a>
              </div>

            {% elsif block.type == 'link_list' %}
              {% comment %} Menu {% endcomment %}
              <div class="container">
                <div class="one-third medium-down--one-whole column link-list__block">
                  {% render 'footer__menu'
                    , menu_link: block.settings.menu_1 %}
                </div>
                {% if block.settings.menu_2 != blank %}
                  <div class="one-third medium-down--one-whole column link-list__block">
                    {% render 'footer__menu'
                      , menu_link: block.settings.menu_2 %}
                  </div>
                {% endif %}
                {% if block.settings.menu_3 != blank %}
                  <div class="one-third medium-down--one-whole column link-list__block">
                    {% render 'footer__menu'
                      , menu_link: block.settings.menu_3 %}
                  </div>
                {% endif %}
              </div>

            {% elsif block.type == 'text' %}
              {% comment %} Text {% endcomment %}
              <p class="footer__heading">{{ block.settings.title }}</p>
              {{ block.settings.content }}

            {% elsif block.type == 'social' %}
              {% comment %} Social media {% endcomment %}
              {% render 'social-icons' %}

            {% elsif block.type == 'html' %}

              {{ block.settings.html_content }}

              {% elsif block.type == 'newsletter' %}
              {% comment %} Newsletter {% endcomment %}
              {% if block.settings.newsletter_title != blank %}
                <h3 class="footer__heading">
                  {{ block.settings.newsletter_title }}
                </h3>
              {% endif %}

              {% if block.settings.newsletter_richtext != blank %}
                <div class="content">
                  {{ block.settings.newsletter_richtext }}
                </div>
              {% endif %}

              {%
                render 'newsletter-form'
                , type: 'block'
                , display_first_name: block.settings.display_first_name
                , display_last_name: block.settings.display_last_name
                , id: 'footer'
                ,
              %}
            {% endif %}
          </div>
        {% endfor %}
        <section class="footer__extra-content has-padding-top has-padding-bottom">
          <div class="sub-footer container">
            {%- if shop.features.follow_on_shop? and section.settings.show_follow_on_shop -%}
              <div class="footer__follow-on-shop one-whole column">
                {{ shop | login_button: action: 'follow' }}
              </div>
            {%- endif -%}

            {% if locale_selector or currency_selector or settings.show_multiple_currencies %}
              <div class="footer-menu__disclosure footer-promotional__disclosure">
                {% render 'localization-switcher'
                  , additional_classes: 'footer-menu__disclosure is-hidden-mobile-only'
                  , id: 'footer__selector-form'
                  , currency_selector: currency_selector
                  , locale_selector: locale_selector
                  , show_currency: section.settings.show_currency_selector
                %}
                {%
                  render 'localization-switcher'
                  , id: 'footer__selector-form--mobile'
                  , additional_classes: 'selectors-form--mobile is-hidden-desktop-only'
                  , currency_selector: currency_selector
                  , locale_selector: locale_selector
                  , show_currency: settings.show_currency_selector
                %}
              </div>
            {% endif %}

            <div class="footer__credits one-whole column">
              <p>&copy; {{ "now" | date: "%Y" }} {{ shop.name | link_to: routes.root_url }}.</p>
              {{ section.settings.copyright_text }}
              {% if section.settings.display_designed_by %}
                <p>{{ 'layout.general.designer_credits_html' | t }}</p>
              {% endif %}
              {% if section.settings.display_shopify %}
                <p>{{ powered_by_link }}</p>
              {% endif %}
            </div>
            {% if section.settings.display_payment_methods %}
              <div class="footer__payment-methods payment-methods one-whole column">
                {% for type in shop.enabled_payment_types %}
                  {{ type | payment_type_svg_tag: class: 'payment-icon' }}
                {% endfor %}
              </div>
            {% endif %}
          </div>
        </section>
      </div>
    </div>
    {% if section.settings.enable_promo %}
      <aside class="one-third  is-align-center column medium-down--one-whole footer__promo-container card-content">
        <div class="footer__promo-image">
          {% if section.settings.promo_link %}
            <a href="{{ section.settings.promo_link }}">
          {% endif %}
          {% if section.settings.promo_image %}
            {%
              render 'image-element'
              , image: section.settings.promo_image
              , alt: section.settings.promo_image.alt
              , focal_point: section.settings.promo_image.presentation.focal_point
              ,
            %}
          {% else %}
            {{ 'collection-1' | placeholder_svg_tag: 'placeholder-svg' }}
          {% endif %}
          {% if section.settings.promo_link %}
            </a>
          {% endif %}
        </div>
        <div class="footer__promo-content container text-align-center">
          <div class="one-whole column">
            {% if section.settings.richtext != blank %}
              <div class="has-padding-top">
                {{ section.settings.richtext }}
              </div>
            {% endif %}

            {% if section.settings.button_label != blank %}
              <div class="has-padding-top">
                {% render 'button'
                  , label: section.settings.button_label
                  , href: section.settings.promo_link
                  , type: "link"
                  , style: section.settings.button_style
                %}
              </div>
            {% endif %}

          </div>
        </div>
      </aside>
    {% endif %}
  </div>
  {% if shop.metafields.cuddleclones.footer_script.value %}
    {{ shop.metafields.cuddleclones.footer_script.value }}
  {% endif %}
</footer>

{% schema %}
  {
    "name": "Footer",
    "class": "footer footer--promotional",
    "max_blocks": 7,
    "settings": [
      {
        "type": "richtext",
        "id": "copyright_text",
        "label": "Copyright text"
      },
      {
        "type": "checkbox",
        "id": "display_designed_by",
        "label": "Show theme designer credits",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "display_shopify",
        "label": "Show Powered by Shopify",
        "default": true
      },
      {
        "type": "header",
        "content": "Follow on Shop",
        "info": "Display follow button for your storefront on the Shop app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/follow-on-shop)"
      }, {
        "type": "checkbox",
        "id": "show_follow_on_shop",
        "label": "Enable Follow on Shop",
        "default": true
      }, {
        "type": "header",
        "content": "Language selector",
        "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
      }, {
        "type": "checkbox",
        "id": "show_locale_selector",
        "label": "Enable language selector",
        "default": true
      }, {
        "type": "header",
        "content": "Country selector",
        "info": "To add a country, go to your [payment settings.](/admin/settings/payments)"
      }, {
        "type": "checkbox",
        "id": "show_currency_selector",
        "label": "Enable country selector",
        "default": true
      }, {
        "type": "header",
        "content": "Payment icons"
      }, {
        "type": "checkbox",
        "id": "display_payment_methods",
        "label": "Show payment method icons",
        "default": true
      }, {
        "type": "header",
        "content": "Promotion"
      }, {
        "type": "checkbox",
        "id": "enable_promo",
        "label": "Enable promotion",
        "default": true
      }, {
        "type": "color",
        "id": "promo_background",
        "label": "Background",
        "default": "#EEEEEE"
      }, {
        "type": "color",
        "id": "promo_text",
        "label": "Text",
        "default": "#000000"
      }, {
        "type": "select",
        "id": "promo_position",
        "label": "Position",
        "options": [
          {
            "value": "row-reverse",
            "label": "Left"
          }, {
            "value": "row",
            "label": "Right"
          }
        ],
        "default": "row-reverse"
      }, {
        "type": "image_picker",
        "id": "promo_image",
        "label": "Image",
        "info": "800 x 500px recommended"
      }, {
        "type": "richtext",
        "id": "richtext",
        "label": "Text",
        "default": "<p>Use this area for promotional information.</p>"
      }, {
        "type": "url",
        "id": "promo_link",
        "label": "Link"
      }, {
        "type": "text",
        "id": "button_label",
        "label": "Button label",
        "default": "Shop now"
      }, {
        "type": "select",
        "id": "button_style",
        "label": "Button Style",
        "options": [
          {
            "value": "button--primary",
            "label": "Primary"
          }, {
            "value": "button--secondary",
            "label": "Secondary"
          }, {
            "value": "button--link-style",
            "label": "Link style"
          }
        ],
        "default": "button--primary"
      }, {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      }, {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      }, {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "html",
        "name": "Custom HTML",
        "settings": [
          {
            "type": "textarea",
            "id": "html_content",
            "label": "HTML",
            "default": "<h2 class='footer__heading'>Your own custom HTML</h2>"
          }
        ]
      },
      {
        "type": "logo",
        "name": "Logo",
        "settings": [
          {
            "type": "image_picker",
            "id": "logo",
            "label": "Logo",
            "info": "500 x 200px recommended"
          }, {
            "type": "select",
            "id": "size",
            "label": "Logo size",
            "default": "small",
            "options": [
              {
                "value": "small",
                "label": "Small"
              }, {
                "value": "medium",
                "label": "Medium"
              }, {
                "value": "large",
                "label": "Large"
              }
            ]
          }, {
            "type": "select",
            "id": "alignment",
            "label": "Alignment",
            "options": [
              {
                "value": "start",
                "label": "Left"
              }, {
                "value": "center",
                "label": "Center"
              }, {
                "value": "end",
                "label": "Right"
              }
            ],
            "default": "start"
          }
        ]
      },
      {
        "type": "link_list",
        "name": "Menus",
        "settings": [
          {
            "type": "link_list",
            "id": "menu_1",
            "label": "Menu 1",
            "info": "This menu won't show drop-down items.",
            "default": "footer"
          }, {
            "type": "link_list",
            "id": "menu_2",
            "label": "Menu 2",
            "info": "This menu won't show drop-down items."
          }, {
            "type": "link_list",
            "id": "menu_3",
            "label": "Menu 3",
            "info": "This menu won't show drop-down items."
          }
        ]
      },
      {
        "type": "newsletter",
        "name": "Newsletter",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "newsletter_title",
            "label": "Heading",
            "default": "Subscribe"
          }, {
            "type": "richtext",
            "id": "newsletter_richtext",
            "label": "Text",
            "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
          }, {
            "type": "checkbox",
            "id": "display_first_name",
            "label": "Show first name"
          }, {
            "type": "checkbox",
            "id": "display_last_name",
            "label": "Show last name"
          }
        ]
      }, {
        "type": "text",
        "name": "Text",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Title"
          }, {
            "type": "richtext",
            "id": "content",
            "label": "Text",
            "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
          }
        ]
      }, {
        "type": "social",
        "name": "Social icons",
        "limit": 1,
        "settings": []
      }
    ]
  }
{% endschema %}