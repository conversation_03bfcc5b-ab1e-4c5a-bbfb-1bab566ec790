<style>
  .portrait_product_section {
    display: flex;
  }
  .portrait_product__images {
    display: flex;
    justify-content: center;
    padding: 50px 20px;
  }
  .product_image {
    height: 559px;
    width: 559px;
  }
  .portrait_product__information {
    padding: 50px 20px;
  }
  .product_heading {
    font-size: 34px;
    font-weight: 700;
  }
  .star_checked {
    color: #5461C9;
  }
  .reviews_number {
    color: #5461C9;
  }
  .price_text {
    font-size: 22px;
    font-weight: 500;
  }
  .question_label {
    font-size: 20px;
    font-weight: 400;
    margin: 10px 0 40px;
  }
  .option_image {
    width: 147px;
    height: 147px;
    border-radius: 10px;
    cursor: pointer;
  }
  .option_image:hover {
    border: 3px solid #5461C9;
  }
  .option_products_container {
    display: flex;
    gap: 20px;
  }
  .option_image_label {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
  }
  .next_step_button_container {
    margin: 20px 0;
  }
  .next_step_button {
    width: 100%;
    height: 50px;
    color: white;
    background: #986195;
    border: 1px solid;
    font-size: 22px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
  }
  .next_step_button:hover {
    opacity: 0.8;
  }
  .satisfy_container {
    margin: 20px 0;
  }
  .satisfy_button {
    width: 100%;
    height: 50px;
    color: black;
    background: rgba(241, 241, 241, 0.76);
    border: 1px solid rgba(241, 241, 241, 0.76);
    font-size: 22px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }
  .reviews_container {
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 3px;
  }
  .unchecked_star {
    width: 15px;
    height: 16px;
    margin-bottom: 2px;
  }
  @media (max-width: 800px) {
    .portrait_product_section {
      display: flex;
      flex-direction: column;
    }
    .one-half {
      width: 100%;
    }
    .product_image {
      height: 334px;
      width: 334px;
    }
    .product_heading {
      font-size: 26px;
      display: flex;
      justify-content: center;
    }
    .satisfy_button {
      width: 100%;
    }
    .next_step_button {
      width: 100%;
    }
    .portrait_product__information {
      padding: 0;
    }
    .option_image {
      width: 110px;
      height: 110px;
    }

    .reviews_container,
    .price_container,
    .question_container {
      display: flex;
      justify-content: center;
    }
    .option_products_container {
      gap: 10px;
      margin: 0 18px;
      justify-content: center;
    }
    .next_step_button_container,
    .satisfy_container {
      margin: 20px 18px;
    }
  }
</style>
{% assign productDetails = page.metafields.cuddleclones.pet_portrait_customizer.value %}
<section class="section section__wrapper">
  <div class="portrait_product_section">
    <div class="portrait_product__images one-half">
      {% render 'portrait__images'
        , images: productDetails.media
      %}
    </div>
    <div class="portrait_product__information one-half">
      <h1 class="product_heading">{{ productDetails.main_heading }}</h1>
      <div>
        <div class="reviews_container">
          {% assign numStars = productDetails.total_stars | round %}
          {% assign emptyStars = 5 | minus: numStars %}
          {% if numStars > 0 and numStars <= 5 %}
            {% for i in (1..numStars) %}
              <span class="fa fa-star star_checked"></span>
            {% endfor %}
            {% for i in (1..emptyStars) %}
              <img
                class="unchecked_star"
                src="{{ 'star-uncheck.png' | asset_url }}"
                alt="uncheck">
            {% endfor %}
          {% else %}
            {% for i in (1..5) %}
              <span class="fa fa-star star_checked"></span>
            {% endfor %}
          {% endif %}
          <span class="reviews_number">
            ({{ productDetails.total_reviews }} Reviews)
          </span>
        </div>
        <div class="price_container">
          <p class="price_text">From
            <span>&nbsp;${{ productDetails.start_price }}</span>
          </p>
        </div>
        <div class="question_container">
          <p class="question_label">{{ productDetails.question_label }}</p>
        </div>
        <div class="option_products_container">
          {% for product in productDetails.media %}
            <div>
              <img
                class="option_image"
                src="{{product.img_url}}"
                alt="{{product.handle}}">
              <p class="option_image_label">{{ product.label }}</p>
            </div>
          {% endfor %}
        </div>
        <div class="next_step_button_container">
          <button class="next_step_button">Next Step</button>
        </div>
        <div class="satisfy_container">
          <button class="satisfy_button" disabled>
            <span style="display: flex;">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="30"
                height="30"
                viewBox="0 0 41 41"
                fill="none">
                <path d="M20.5 3.41669C17.1212 3.41669 13.8183 4.41861 11.009 6.29575C8.19965 8.17289 6.01004 10.8409 4.71704 13.9625C3.42404 17.0841 3.08574 20.519 3.7449 23.8328C4.40406 27.1467 6.03109 30.1906 8.42024 32.5798C10.8094 34.9689 13.8533 36.5959 17.1672 37.2551C20.481 37.9143 23.9159 37.576 27.0375 36.283C30.1591 34.99 32.8271 32.8004 34.7043 29.991C36.5814 27.1817 37.5833 23.8788 37.5833 20.5C37.5833 18.2566 37.1414 16.0352 36.2829 13.9625C35.4244 11.8899 34.1661 10.0066 32.5797 8.42028C30.9934 6.83395 29.1101 5.5756 27.0375 4.71708C24.9648 3.85856 22.7434 3.41669 20.5 3.41669ZM27.8458 16.4171L20.0387 26.6671C19.8796 26.8738 19.6752 27.0414 19.4413 27.1569C19.2073 27.2724 18.95 27.3327 18.6891 27.3334C18.4297 27.3348 18.1733 27.277 17.9394 27.1645C17.7056 27.0521 17.5004 26.8878 17.3396 26.6842L13.1712 21.3713C13.0333 21.194 12.9316 20.9914 12.8719 20.7748C12.8123 20.5583 12.7959 20.3321 12.8236 20.1092C12.8514 19.8864 12.9228 19.6711 13.0337 19.4758C13.1446 19.2806 13.293 19.109 13.4702 18.9711C13.8281 18.6924 14.2821 18.5674 14.7322 18.6234C14.9551 18.6512 15.1703 18.7226 15.3656 18.8335C15.5609 18.9445 15.7324 19.0928 15.8704 19.27L18.655 22.8234L25.1125 14.2817C25.2493 14.1022 25.4202 13.9515 25.6153 13.838C25.8104 13.7246 26.026 13.6507 26.2496 13.6205C26.4733 13.5904 26.7007 13.6046 26.9189 13.6623C27.1371 13.7201 27.3418 13.8203 27.5212 13.9571C27.7007 14.094 27.8515 14.2648 27.9649 14.4599C28.0784 14.655 28.1523 14.8706 28.1824 15.0943C28.2125 15.3179 28.1983 15.5453 28.1406 15.7635C28.0828 15.9817 27.9827 16.1864 27.8458 16.3659V16.4171Z" fill="#5461C9" />
              </svg>
            </span>
            <span>{{ productDetails.satisfication_note }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</section>
<script>
  const product={{ productDetails  | json}};

  let setHandle="";
  
  const optionImages=document.querySelectorAll(".option_products_container img");
  optionImages.forEach((img,index)=>{
      img.addEventListener("click",function(){
          optionImages.forEach((img) => img.style.border = "none");
          img.style.border="3px solid #5461C9";
          setHandle=img.alt;
          showSlidesOnSelectingProduct(index+1);
          validateNextButton();
      });
  });
  

  function showSlidesOnSelectingProduct(n) {
    let slideIndexMain = n;
    let i;
    let slidesContainer = document.querySelector(".customGalleryImageContainer");

    let thumbnailsContainer = document.querySelector(".thumbnailRow");

    let slides = slidesContainer.querySelectorAll(".mySlides");

    let dots = thumbnailsContainer.querySelectorAll(".demo");

    if (n > slides.length) {slideIndexMain = 1}
    if (n < 1) {slideIndexMain = slides.length}
    for (i = 0; i < slides.length; i++) {
        slides[i].style.display = "none";
    }
    for (i = 0; i < dots.length; i++) {
      dots[i].className = dots[i].className.replace(" active", "");
    }
    slides[slideIndexMain - 1].style.display = "block";
    dots[slideIndexMain - 1].className += " active";
  }

  const nextButton=document.querySelector(".next_step_button");
  nextButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
  nextButton.setAttribute("disabled", "true");

  function validateNextButton(){ 
    if(setHandle===""){
      nextButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
      nextButton.setAttribute("disabled", "true");
    }
    else{
      nextButton.style.cssText = "cursor:pointer;opacity:1;pointer-events:auto";
      nextButton.removeAttribute("disabled");
    }
  }

  nextButton.addEventListener("click",function(){
    window.location.href = `{{ shop.url }}/products/${setHandle}`;
  })
</script>

{% schema %}
  {
    "name": "Section name",
    "settings": []
  }
{% endschema %}