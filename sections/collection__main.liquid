{% comment %}
** Collection template with sidebar **
{% endcomment %}

{% assign id = section.id %}
{% comment %}Layout{% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% assign animation = section.settings.animation | default: 'none' %}
{% comment %} Advanced {% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% comment %} CSS {% endcomment %}
{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  .sidebar-section {
    {% if section.settings.sidebar_position == 'right' %}
      order: 1;
    {% endif %}
  }

  {% if section.settings.collection_breadcrumb == false %}
    .breadcrumb__container {
      display: none;
    }
  {% endif %}

  {% if section.settings.pagination_type != 'basic_pagination' %}
    .breadcrumb__page-count {
      display: none;
    }
  {% endif %}

  {% if section.settings.collection_tags == false %}
    .collection__tag-filter {
      display: none;
    }
  {% endif %}

  {% if section.settings.collection_sort == false %}
    .collection__sort-by-filter {
      display: none;
    }
  {% endif %}

  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

<section class="section
        {{ css_class }}
        is-width-{{ width }}"
        {% if animation != "none" %}
          data-scroll-class="{{ animation }}"
        {% endif %}>
  <div class="container {% if settings.heading_divider_style == 'long' %}has-heading-divider-below {% endif %}">
    <div class="collection__breadcrumb two-fifths medium-down--one-whole column has-padding-bottom">
      {% comment %} Collection breadcrumb {% endcomment %}
      <div class="breadcrumb__container">
        {% render 'breadcrumb', context: 'collection' %}
      </div>
    </div>
    <div class="collection__spacer three-fifths column hide-when-banner-enabled"></div>
    <div class="two-fifths medium-down--one-whole column has-no-side-gutter hide-when-banner-enabled">
      {% comment %} Collection title {% endcomment %}
      {% render 'heading',
              title: collection.title,
              heading_tag: 'h1',
              context: 'collection',
              text_alignment: 'left'
      %}
    </div>
    <div class="collection__filters three-fifths medium-down--one-whole column">
      {% comment %} Collection filters {% endcomment %}
      {% render 'collection__filters' %}
    </div>
    {% if settings.heading_divider_width != 0 and settings.heading_divider_style == 'long' %}
      <div class="heading-divider-below heading-wrapper one-whole column hide-when-banner-enabled">
        <div class="heading-divider
                    heading-divider--{{ settings.heading_divider_style }}"
              {% if settings.heading_divider_animation != "none" %}
                data-scroll-class="{{ settings.heading_divider_animation }}"
              {% endif %}>
        </div>
      </div>
    {% endif %}
  </div>

  {% comment %} Collection description {% endcomment %}
  {% if collection.description != blank %}
    <div class="container">
      <div class="one-whole column">
        <div class="collection__description content has-padding-bottom">
          {{ collection.description }}
        </div>
      </div>
    </div>
  {% endif %}

  {% comment %} Collection sidebar and main content area {% endcomment %}
  <div class="container collection__content">

    {% if section.blocks.size > 0 and section.settings.show_sidebar %}
      <aside class="sidebar-section
                    one-fourth
                    medium-down--one-whole
                    column">

        {% liquid
          assign has_faceted_filtering_block = false
          assign faceted_filtering_blocks_count = section.blocks | where: "type", "faceted_filtering" | size
          if faceted_filtering_blocks_count > 0
            assign has_faceted_filtering_block = true
          endif
          assign legacy_filter_block_types = 'tag_list,tag_filter,type_list,vendor_list' | split: ','
        %}

        {% for block in section.blocks %}
          {% if block.type == 'faceted_filtering' %}
            {% render 'sidebar__faceted-filter',
                      block: block,
                      collection: collection %}
          {% else %}
            {%- comment -%} Non-faceted filter blocks {%- endcomment -%}
            <div id="shopify-section-{{ block.id }}"
                class="sidebar__block
                      block__{{ block.type | downcase | replace: '_', '-' }}
                      has-padding-top
                      has-padding-bottom
                      {% if settings.toggle_sidebar %}sidebar-toggle-active{% endif %}
                      {% if has_faceted_filtering_block and legacy_filter_block_types contains block.type %}is-hidden{% endif %}"
                      {{ block.shopify_attributes }}>
              {% if block.type == 'collection_list' %}
                {% comment %} Tag list {% endcomment %}
                {% render 'sidebar__collection-list', block: block %}

              {% elsif block.type == 'featured_promo' %}
                {% comment %} Featured promo {% endcomment %}
                {% render 'sidebar__featured-promo',
                        block: block,
                        id: block.id
                %}

              {% elsif block.type == 'html' %}
                {% comment %} Custom HTML {% endcomment %}
                {% render 'sidebar__html', block: block %}

              {% elsif block.type == 'menu' %}
                {% comment %} Menu {% endcomment %}
                {% render 'sidebar__menu', menu: block.settings.menu %}

              {% elsif block.type == 'newsletter' %}
                {% comment %} Newsletter {% endcomment %}
                {% render 'sidebar__newsletter', block: block %}

              {% elsif block.type == 'page' %}
                {% comment %} Page {% endcomment %}
                {% render 'sidebar__page', page: block.settings.page %}

              {% elsif block.type == 'search' %}
                {% comment %} Search {% endcomment %}
                {%
                  render 'sidebar__search',
                  block: block,
                %}

              {% elsif block.type == 'tag_filter' and has_faceted_filtering_block == false %}
                {% comment %} Tag filter {% endcomment %}
                {% render 'sidebar__tag-filter', block: block %}

              {% elsif block.type == 'tag_list' and has_faceted_filtering_block == false %}
                {% comment %} Tag list {% endcomment %}
                {% render 'sidebar__tag-list', block: block %}

              {% elsif block.type == 'text' %}
                {% comment %} Text {% endcomment %}
                {% render 'sidebar__text', block: block %}

              {% elsif block.type == 'type_list' and has_faceted_filtering_block == false %}
                {% comment %} Type list {% endcomment %}
                {% render 'sidebar__type-list', block: block %}

              {% elsif block.type == 'vendor_list' and has_faceted_filtering_block == false %}
                {% comment %} Vendor list {% endcomment %}
                {% render 'sidebar__vendor-list', block: block %}

              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </aside>
    {% endif %}

    {% paginate collection.products by section.settings.pagination_limit %}

    <div
      class="
        collection__main
        {% if section.blocks.size > 0 and section.settings.show_sidebar %}
          three-fourths
          medium-down--one-whole
          column
          equal-columns--outside-trim
        {% endif %}
      "
      data-collection-main
    >
      {% if section.settings.show_promo_banner %}
        <div class="collection-promo-banner container">
          <div class="banner__wrapper
                      one-whole
                      column
                      dark-overlay-{{ section.settings.image_darken }}
                      is-{{ section.settings.banner_height }}
                      has-margin-bottom">
            {% if section.settings.image != blank %}
              {%
                render 'image-element',
                image: section.settings.image,
                alt: section.settings.image.alt,
                focal_point: section.settings.image.presentation.focal_point,
              %}
            {% else %}
              {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
            {% endif %}
            <div class="banner__content card-content">
              <div class="banner__text text-align-center">
                <h1 class="banner__heading title">{{ section.settings.promo_title }}</h1>
                {% if section.settings.promo_text != blank %}
                  <div class="banner__subheading subtitle">{{ section.settings.promo_text }}</div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      {% endif %}

      {% if collection.products.size == 0 %}
        <div class="container container--no-products-in-collection text-align-center has-padding-bottom has-padding-top">
          <div class="one-whole column">
            <p>{{ 'collections.general.no_matches' | t }}</p>
          </div>
        </div>
      {% else %}
        <div class="collection-matrix__wrapper">
          <div class="collection__loading-icon button is-loading is-loading--icon-only"></div>
          <div class="container collection-matrix" {% if section.settings.pagination_type != 'basic_pagination' %}data-load-more--grid {% endif %}>
            {%- assign products_per_row = section.settings.products_per_row -%}
            {%- assign mobile_products_per_row = section.settings.mobile_products_per_row -%}
            {% render 'product-loop',
                    products: products,
                    products_per_row: products_per_row,
                    mobile_products_per_row: mobile_products_per_row,
                    limit: section.settings.pagination_limit,
                    align_height: section.settings.align_height,
                    height: section.settings.collection_height
            %}
          </div>
        </div>
      {% endif %}

      <div class="container container--pagination">
        <div class="one-whole column text-align-center">
          {%- assign load_more_text = 'collections.general.pagination_button' | t -%}
          {% render 'pagination',
                  paginate: paginate,
                  pagination_type: section.settings.pagination_type,
                  load_more_text: load_more_text
          %}
        </div>
      </div>
    </div>

    {% comment %} JavaScript {% endcomment %}
    <script
      type="application/json"
      data-section-id="{{ section.id }}"
      data-section-data
    >
      {
        "enable_filter": {{ section.settings.collection_tags | json }},
        "enable_sorting": {{ section.settings.collection_sort | json }},
        "enable_breadcrumb": {{ section.settings.collection_breadcrumb | json}},
        "pagination_type": {{ section.settings.pagination_type | json }},
        "number_of_pages": {{ paginate.pages | json }}
      }
    </script>

    {% endpaginate %}
  </div>
</section>


<script data-theme-editor-load-script src="{{ 'z__jsSidebar.js' | asset_url }}"></script>
<script src="{{ 'z__jsCollection.js' | asset_url }}"></script>
{% schema %}
{
  "name": "Collection",
  "class": "collection-main jsCollection jsSidebar",
  "settings": [
    {
      "type": "checkbox",
      "id": "collection_breadcrumb",
      "label": "Show breadcrumbs",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "collection_tags",
      "label": "Show product tag filter",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "collection_sort",
      "label": "Show sort by filter",
      "default": true
    },
    {
      "type": "header",
      "content": "Sidebar"
    },
    {
      "type": "checkbox",
      "id": "show_sidebar",
      "label": "Show sidebar",
      "info": "Add blocks for sidebar content.",
      "default": false
    },
    {
      "type": "radio",
      "id": "sidebar_position",
      "label": "Sidebar position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "header",
      "content": "Promotional banner"
    },
    {
      "type": "checkbox",
      "id": "show_promo_banner",
      "label": "Show promotional banner",
      "default": false
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "1800 x 800px recommended"
    },
    {
      "type": "checkbox",
      "id": "image_darken",
      "label": "Darken banner image",
      "default": true
    },
    {
      "type": "text",
      "id": "promo_title",
      "label": "Heading",
      "default": "Special sale on now!"
    },
    {
      "type": "richtext",
      "id": "promo_text",
      "label": "Text",
      "default": "<p>Description of promotion with lots of details.</p>"
    },
    {
      "type": "select",
      "id": "banner_height",
      "label": "Banner height",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "header",
      "content": "Product thumbnails"
    },
    {
      "type": "checkbox",
      "id": "align_height",
      "label": "Align to height",
      "default": false
    },
    {
      "type": "range",
      "id": "collection_height",
      "label": "Product image height",
      "min": 150,
      "max": 400,
      "step": 10,
      "default": 200,
      "unit": "px",
      "info": "Applied when 'Align to height' is enabled. [Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022543913)"
    },
    {
      "type": "range",
      "id": "products_per_row",
      "label": "Products per row",
      "min": 2,
      "max": 7,
      "step": 1,
      "default": 3
    },
    {
      "type": "select",
      "id": "mobile_products_per_row",
      "label": "Products per row on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ]
    },
    {
      "type": "range",
      "id": "pagination_limit",
      "label": "Products per page",
      "min": 2,
      "max": 50,
      "step": 1,
      "default": 48
    },
    {
      "type": "select",
      "id": "pagination_type",
      "label": "Pagination type",
      "options": [
        {
          "value": "infinite_scroll",
          "label": "Infinite scroll"
        },
        {
          "value": "infinite_load_more",
          "label": "Infinite load more button"
        },
        {
          "value": "load_more",
          "label": "Load more button"
        },
        {
          "value": "basic_pagination",
          "label": "Page links"
        }
      ],
      "default": "basic_pagination"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "standard",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ]
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ],
  "blocks": [
    {
      "type": "collection_list",
      "name": "Collection list",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Collections"
        }
      ]
    },
    {
      "type": "html",
      "name": "Custom HTML",
      "settings": [
        {
          "type": "textarea",
          "id": "html_content",
          "label": "HTML",
          "default": "<div class='container is-flex is-justify-center'><h2 class='title'>Your own custom HTML</h2></div>"
        }
      ]
    },
    {
      "type": "faceted_filtering",
      "name": "Faceted filtering",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "[Learn how](https://help.shopify.com/en/manual/online-store/themes/os20/customize/filters) to configure filters."
        },
        {
          "type": "paragraph",
          "content": "Using this block will hide any tag filter, tag list, type list, and vendor list blocks."
        }
      ]
    },
    {
      "type": "featured_promo",
      "name": "Featured promotion",
      "settings": [
        {
          "type": "color",
          "id": "promo_background",
          "label": "Background",
          "default": "#EEEEEE"
        },
        {
          "type": "color",
          "id": "promo_text",
          "label": "Text",
          "default": "#000000"
        },
        {
          "type": "image_picker",
          "id": "promo_image",
          "label": "Image"
        },
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Text",
          "default": "<p>Use this area for promotional information.</p>"
        },
        {
          "type": "url",
          "id": "promo_link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Shop now"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "value": "button--link-style",
              "label": "Link style"
            }
          ],
          "default": "button--primary"
        }
      ]
    },
    {
      "type": "menu",
      "name": "Menu",
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "info": "This menu won't show drop-down items."
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "newsletter_title",
          "label": "Heading",
          "default": "Subscribe"
        },
        {
          "type": "richtext",
          "id": "newsletter_richtext",
          "label": "Text",
          "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
        },
        {
          "type": "checkbox",
          "id": "display_first_name",
          "label": "Show first name"
        },
        {
          "type": "checkbox",
          "id": "display_last_name",
          "label": "Show last name"
        }
      ]
    },
    {
      "type": "page",
      "name": "Page",
      "settings": [
        {
          "type": "page",
          "id": "page",
          "label": "Page"
        }
      ]
    },
    {
      "type": "search",
      "name": "Search form",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Search"
        }
      ]
    },
    {
      "type": "tag_filter",
      "name": "Tag filter",
      "limit": 5,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Filter by"
        },
        {
          "type": "textarea",
          "id": "filter_tags",
          "label": "Filter tags",
          "info": "Add a comma-separated list of product tags. Only the tags found in the collection will be displayed as filters. [Learn more](https://help.outofthesandbox.com/hc/en-us/articles/115008642047)"
        },
        {
          "type": "checkbox",
          "id": "enable_filter_swatches",
          "label": "Display color swatches",
          "default": false
        }
      ]
    },
    {
      "type": "tag_list",
      "name": "Tag list",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Category"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Text area can be used for details about blog authors or general information.</p>"
        }
      ]
    },
    {
      "type": "type_list",
      "name": "Type list",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Types"
        }
      ]
    },
    {
      "type": "vendor_list",
      "name": "Vendor list",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Vendors"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "featured_promo"
      },
      {
        "type": "menu"
      },
      {
        "type": "text"
      }
    ]
  }
}

{% endschema %}
