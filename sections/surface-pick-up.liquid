<template data-html="surface-pick-up-embed">
  {% comment %}Inject @pixelunion/shopify-surface-pick-up/surface-pick-up-embed begin{% endcomment %}
  {% assign pick_up_availabilities = product_variant.store_availabilities | where: 'pick_up_enabled', true %}
  {% if product_variant.available and pick_up_availabilities.size > 0 %}
    {% assign closest_location = pick_up_availabilities.first %}
  
    <div class="surface-pick-up-embed surface-pick-up-embed--{% if closest_location.available %}available{% else %}unavailable{% endif %}">
      {% if closest_location.available %}
        <svg class="surface-pick-up-embed__in-stock-icon surface-pick-up-embed__svg-placeholder" width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg" focusable=”false” aria-hidden=”true”>
          <path d="M4.33346 10.5625L3.80311 11.0928L4.33344 11.6232L4.86379 11.0928L4.33346 10.5625ZM0.191824 7.48134L3.80311 11.0928L4.8638 10.0322L1.25251 6.4207L0.191824 7.48134ZM4.86379 11.0928L12.9888 2.96783L11.9281 1.90717L3.80313 10.0322L4.86379 11.0928Z"/>
        </svg>
      {% else %}
        <svg class="surface-pick-up-embed__out-of-stock-icon surface-pick-up-embed__svg-placeholder" width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg" focusable=”false” aria-hidden=”true”>
          <path d="M1.46967 2.53033L5.96967 7.03033L7.03033 5.96967L2.53033 1.46967L1.46967 2.53033ZM5.96967 7.03033L10.4697 11.5303L11.5303 10.4697L7.03033 5.96967L5.96967 7.03033ZM7.03033 7.03033L11.5303 2.53033L10.4697 1.46967L5.96967 5.96967L7.03033 7.03033ZM10.4697 1.46967L1.46967 10.4697L2.53033 11.5303L11.5303 2.53033L10.4697 1.46967Z"/>
        </svg>
      {% endif %}
  
      <div class="surface-pick-up-embed__location-info">
        {% if closest_location.available %}
          <h3 class="surface-pick-up-embed__location-availability">{{ 'store_availability.general.available_for_pick_up_at_html' | t: location_name: closest_location.location.name }}</h3>
          <small class="surface-pick-up-embed__location-pick-up-time">{{ closest_location.pick_up_time }}</small>
        {% else %}
          <h3 class="surface-pick-up-embed__location-availability">{{ 'store_availability.general.unavailable_for_pick_up_at_html' | t: location_name: closest_location.location.name }}</h3>
        {% endif %}
      </div>
  
      <button class="surface-pick-up-embed__modal-btn" type="button" aria-haspopup=”dialog” data-surface-pick-up-embed-modal-btn>
        {% if pick_up_availabilities.size > 1 %}
          {{ 'store_availability.general.check_other_stores' | t }}
        {% else %}
          {{ 'store_availability.general.view_store_info' | t }}
        {% endif %}
      </button>
    </div>
  {% endif %}
  {% comment %}Inject @pixelunion/shopify-surface-pick-up/surface-pick-up-embed end{% endcomment %}

</template>

<template data-html="surface-pick-up-items">
  {% assign pick_up_availabilities = product_variant.store_availabilities | where: 'pick_up_enabled', true %}
  {% if product_variant.available and pick_up_availabilities.size > 0 %}
    {%- assign closest_location_country_code = pick_up_availabilities.first.location.address.country_code -%}
    {%- assign imperialSystemCountries = 'US,MM,LR'  | split: ',' -%}
    {%- if imperialSystemCountries contains closest_location_country_code -%}
      {%- assign unit_system = 'imperial' -%}
    {%- else -%}
      {%- assign unit_system = 'metric' -%}
    {%- endif -%}
    <ul class="surface-pick-up-items" role="list">
      {% for location in pick_up_availabilities %}
        {% comment %}Inject @pixelunion/shopify-surface-pick-up/surface-pick-up-item begin{% endcomment %}
        <li
          class="surface-pick-up-item surface-pick-up-item--{% if location.available %}available{% else %}unavailable{% endif %}"
          data-surface-pick-up-item
        >
          <div class="surface-pick-up-item__header">
            <h3 class="surface-pick-up-item__pick-up-location">{{ location.location.name }}</h3>
            <p
              class="surface-pick-up-item__pick-up-distance"
            >
              <span
                data-distance
                data-latitude="{{ location.location.latitude }}"
                data-longitude="{{ location.location.longitude }}"
              >
              </span>
              <span
                data-distance-unit="{{ unit_system }}"
              >
                {%- if unit_system == 'imperial' -%}
                  {{ 'store_availability.general.miles' | t }}
                {%- else -%}
                  {{ 'store_availability.general.kilometers' | t }}
                {%- endif -%}
              </span>
            </p>
          </div>
        
          {% if location.available %}
            <svg class="surface-pick-up-item__in-stock-icon surface-pick-up-item__svg-placeholder" width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg" focusable=”false” aria-hidden=”true”>
              <path d="M4.33346 10.5625L3.80311 11.0928L4.33344 11.6232L4.86379 11.0928L4.33346 10.5625ZM0.191824 7.48134L3.80311 11.0928L4.8638 10.0322L1.25251 6.4207L0.191824 7.48134ZM4.86379 11.0928L12.9888 2.96783L11.9281 1.90717L3.80313 10.0322L4.86379 11.0928Z"/>
            </svg>
          {% else %}
            <svg class="surface-pick-up-item__out-of-stock-icon surface-pick-up-item__svg-placeholder" width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg" focusable=”false” aria-hidden=”true”>
              <path d="M1.46967 2.53033L5.96967 7.03033L7.03033 5.96967L2.53033 1.46967L1.46967 2.53033ZM5.96967 7.03033L10.4697 11.5303L11.5303 10.4697L7.03033 5.96967L5.96967 7.03033ZM7.03033 7.03033L11.5303 2.53033L10.4697 1.46967L5.96967 5.96967L7.03033 7.03033ZM10.4697 1.46967L1.46967 10.4697L2.53033 11.5303L11.5303 2.53033L10.4697 1.46967Z"/>
            </svg>
          {% endif %}
        
          <div class="surface-pick-up-item__availability">
            {% if location.available %}
              {% assign pick_up_time = location.pick_up_time | downcase %}
              {{ 'store_availability.general.available_for_pick_up_at_time_html' | t: pick_up_time: pick_up_time }}
            {% else %}
              {{ 'store_availability.general.unavailable_for_pick_up_at_time_html' | t }}
            {% endif %}
          </div>
        
          <address class="surface-pick-up-item__address-info">
            {{ location.location.address | format_address }}
            {%- if location.location.phone.size > 0 %}
              <a href="tel:{{ location.location.phone }}">{{ location.location.phone }}</a>
            {%- endif -%}
          </address>
        </li>
        {% comment %}Inject @pixelunion/shopify-surface-pick-up/surface-pick-up-item end{% endcomment %}

      {% endfor %}
    </ul>
  {% endif %}
</template>

{% schema %}
{
  "name": "Surface pick up",
  "settings": [],
  "presets": []
}

{% endschema %}