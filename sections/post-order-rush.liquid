{% comment %}
** Page - static **
- Main content area
{% endcomment %}

{% assign id = section.id %}
{% comment %}Layout{% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% comment %}Advanced{% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% style %}

  .section__wrapper {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' -%}
      width: 100%;
      max-width: none;
    {%- endif %}
  }


  {% render 'css-loop',
          css: section_css,
          id: id
  %}
  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}


<section class="section__wrapper section">
    <main class="{{ css_class }} is-width-{{ width }}">
        <div class="container page__container">
            {% unless template contains 'page.banner' %}
            {% render 'heading',
                    title: 'Purchase RUSH for previously placed orders',
                    heading_tag: 'h1',
                    context: 'page',
                    text_alignment: 'left'
            %}
            {% endunless %}
        </div>
        <div class="page__content content">
                {% assign rush_product = all_products[section.settings.rush_product] %}
                {% assign rush_product_in_cart = false %}
                <div class="post-order-rush-container">
                <div class="post-order-rush-image">
                    <img src="{{ rush_product.featured_image | img_url: '680x' }}" alt="{{ rush_product.featured_image.alt }}">
                </div>
                <div class="post-order-rush-details">
                    <div id="not-eligible-for-rush" style="display: none;"></div>
                    <div id="eligible-for-rush" style="display: none;">
                    <h2>{{ rush_product.title }}</h2>
                    <div class="rush-description">{{ rush_product.content }}</div>

                    <p>PURCHASE RUSH FOR ORDER <b id="eligible-for-rush-order-name"></b></p>
                    {% for item in cart.items %}
                      {% if item.product_id == rush_product.id and item.properties['_original_order_name'] %}{% assign rush_product_in_cart = true %}{% endif %}
                    {% endfor %}
                    <div id="eligible-for-rush-original-in-cart" {% if rush_product_in_cart %}style="display: block"{% else %}style="display: none"{% endif %}>RUSH product added to cart.</div>
                      {% unless rush_product_in_cart %}
                        <form method="post" action="/cart/add" enctype="multipart/form-data" id="product_form_{{rush_product.id}}">
                          <input type="hidden" name="properties[_rush_product]" id="eligible-for-rush-original-order-property" value="true">
                          <input type="hidden" name="properties[_original_order_name]" id="eligible-for-rush-original-order-property-hidden" value="">
                          <div class="cart-rush--options">
                            {% for variant in rush_product.variants %}
                              {% assign variant_meta = variant.metafields.cuddleclones.highlighted.value %}
                              <div class="cart-rush--option {% if forloop.last == true %}last-option{% endif %}" {% if variant_meta !='' %}
                                style="display:{{variant_meta.display}}; color:{{variant_meta.color}}; font-weight: {{variant_meta.font-weight}}"
                                {% endif %}>
                                <span class="cart-rush--variant"><input type="radio" name="id" value="{{variant.id}}" {% if has_rush_added and rush_variant == variant.id %}checked{%endif%}></span><label>Delivers In: {{variant.title}}</label><span class="cart-rush--price" data-variant-price="{{variant.price | money_without_currency }}">{{variant.price | money }}</span>
                              </div>
                              {% if section.settings.rush_disclaimer != blank %}
                                {% if forloop.last == true %}
                                  <div class="rush-disclaimer {% if has_rush_added and rush_variant == variant.id %}{% else %}hide{%endif%}">
                                    {{ section.settings.rush_disclaimer }}
                                  </div>
                                {% endif %}
                              {% endif %}
                            {% endfor %}
                          </div>
                          <button id="add-to-cart-button" type="button" name="add" class="button ajax-submit action_button button--add-to-cart action_button--secondary" data-label="Add to cart" data-add-to-cart-trigger=""> <span class="text">Add to cart</span> <svg x="0px" y="0px" width="32px" height="32px" viewBox="0 0 32 32" class="checkmark"> <path fill="none" stroke-width="2" stroke-linecap="square" stroke-miterlimit="10" d="M9,17l3.9,3.9c0.1,0.1,0.2,0.1,0.3,0L23,11"></path></svg></button>
                        </form>
                      {% endunless %}
                    </div>
                </div>
            </div>        
        </div>
    </main>
</section>
<script type="text/javascript">
    const queryString = window.location.search;

    const urlParams = new URLSearchParams(queryString);

    const order_name = urlParams.get('order_name');

    const notEligibleForRush = document.getElementById('not-eligible-for-rush');

    const omsDetails = {{shop.metafields.cuddleclones.api_details.value |  json}};
    
    if(order_name == null) {  
      notEligibleForRush.innerText = "There was no previous order selected for RUSH post purchase.";
      notEligibleForRush.style = "block";
    } else {
      const headers = {
        'api-token': omsDetails[0]?.api_token,
        'ngrok-skip-browser-warning': 6024,
      }
      
      const url = new URL(`${omsDetails[0]?.url}shopify_api/order_missing_rush`);

      let params = {
        "order_number": order_name,
      };
      Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

      
      fetch(url, {
        method: "GET",
        headers,
      })
      .then(response => response.json())
      .then(data => {
        if(data && data.rush_needed) {
          var eligibleForRushContainer = document.getElementById('eligible-for-rush');
          var eligibleForRushOrderName = document.getElementById('eligible-for-rush-order-name');
          var eligibleForRushOriginalOrderProperty = document.getElementById('eligible-for-rush-original-order-property');
          var eligibleForRushOriginalOrderPropertyHidden = document.getElementById('eligible-for-rush-original-order-property-hidden');
          eligibleForRushContainer.style.display = "block";
          eligibleForRushOrderName.innerText = order_name;
          eligibleForRushOriginalOrderProperty.value = order_name;
          eligibleForRushOriginalOrderPropertyHidden.value = order_name;
        } else {
          notEligibleForRush.innerText = "The selected order is not eligible for RUSH post purchase.";
          notEligibleForRush.style = "block";
        }
      });
    }
</script>

{% schema %}
  {
    "name": "Post Order Page",
    "class": "page-main has-sidebar-option",
    "settings": [
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Settings"
      },
      {
        "type": "product",
        "id": "rush_product",
        "label": "RUSH product"
      },
      {
        "type": "text",
        "id": "rush_disclaimer",
        "label": "RUSH disclaimer",
        "default": "Rush applies to plush products only. Other products may ship separately.",
        "info": "If this is not empty, it will show up when the last RUSH option is selected."
      }
    ]
  }
{% endschema %}
