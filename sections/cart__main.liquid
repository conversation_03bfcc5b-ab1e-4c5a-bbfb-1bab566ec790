{% comment %}
** Cart page - main content area
{% endcomment %}

{% liquid
  assign id = section.id

  # Layout
  assign width = section.settings.width
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign animation = section.settings.animation | default: 'none'

  # Advanced
  assign css_class = section.settings.css_class
  assign custom_css = section.settings.custom_css

  # Continue shopping link
  if section.settings.continue_shopping_link == blank
    if cart.items.first.product.collections != blank
      assign continue_shopping_link = cart.items.first.product.collections.last.url
    else
      assign continue_shopping_link = routes.all_products_collection_url
    endif
  else
    assign continue_shopping_link = section.settings.continue_shopping_link
  endif
%}

{% comment %} CSS {% endcomment %}
{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  .cart__product-option-name {
    font-weight: bold;
  }

  {%
    render 'css-loop',
    css: custom_css,
    id: id
  %}
{% endstyle %}

{% comment %} HTML markup {% endcomment %}
<section class="section
        {{ css_class }}
        is-width-{{ width }}"
        {% if animation != "none" %}
          data-scroll-class="{{ animation }}"
        {% endif %}>
  <div class="container cart__heading-container">
    {% capture heading_title %}{{ 'cart.general.title' | t }}{% endcapture %}
    {%
      render 'heading',
      title: heading_title,
      heading_tag: 'h1',
      context: 'cart-page',
      text_alignment: 'left'
    %}
  </div>

  <div
    class="
      container
      cart__empty-cart-message
      {% if cart.item_count > 0 %}
        is-hidden
      {% endif %}
    "
  >
    <div
      class="
        column
        one-whole
        text-align-center
        has-padding-top
        has-padding-bottom
      "
    >
      <h2 class="quote">
        {{- 'cart.general.continue_browsing_html' | t -}}
      </h2>

      <a
        class="
          action_button
          continue-button
          add_to_cart
        "
        href="{{ continue_shopping_link }}"
      >
        {{- 'cart.general.continue_shopping_link_html' | t -}}
      </a>
    </div>
  </div>

  <div class="cart__form {% if cart.item_count == 0 %}is-hidden{% endif %}"
      data-current-discount="{{ cart.total_discount }}">
    <form action="{{ routes.cart_url }}" method="post" id="cart_form" class="{% if cart.item_count == 0 %}visually-hidden{% endif %} quantity-box-enabled-{{ section.settings.display_product_quantity_cart }}">

      <div class="container medium-down--hide">
        <div class="cart__headings column">
          <div class="cart__product-title">
            <h3>{{'cart.general.products' | t }}</h3>
          </div>

          <div class="cart__price-title {% if section.settings.display_product_quantity_cart == false %}no-quantity-box{% endif %}">
            <h3>{{'cart.general.price' | t }}</h3>
          </div>

          {% if section.settings.display_product_quantity_cart %}
            <div class="cart__quantity-title">
              <h3>{{'cart.general.quantity' | t }}</h3>
            </div>
          {% endif %}

          <div class="cart__total-title {% if section.settings.display_product_quantity_cart == false %}no-quantity-box{% endif %}">
            <h3>{{'cart.general.total' | t }}</h3>
          </div>

        </div>
      </div>

      <div class="container">
        <div class="cart__item-list one-whole column medium-down--one-whole">
          {% assign total_saving = 0 %}
          {%- assign has_rush_products = false -%}
          {%- assign has_rush_products_pdp = false -%}
          {%- assign has_rush_added = false -%}
          {% assign has_rush_added_pdp = false %}
          {% for item in cart.items %}
            {% if item.variant.compare_at_price > item.variant.price %}
              {% assign saving = item.variant.compare_at_price | minus: item.variant.price | times: item.quantity %}
              {% assign total_saving = saving | plus: total_saving %}
            {% endif %}
          {% if item.product.type == "RUSH" %} {% comment %}This is basically to detect if the cart contains the RUSH product {% endcomment %}
            {% assign has_rush_added = true %}
            {% assign rush_line = forloop.index %}
            {% assign rush_variant = item.variant_id %}
            {% assign rush_variant_price = item.original_line_price %}
          {% endif %}
          {% if item.product.type == "RUSH PDP" %} {% comment %}This is basically to detect if the cart contains the RUSH product {% endcomment %}
            {% assign has_rush_added_pdp = true %}
            {% assign rush_line_pdp = forloop.index %}
            {% assign rush_variant_pdp = item.variant_id %}
            {% assign rush_variant_price_pdp = item.original_line_price %}
          {% endif %} 
            
        {% unless item.product.type=="RUSH" %} 
              <div class="cart__card container is-align-center" data-cart-item="{{ item.key }}" data-variant-id="{{ item.id }}" data-line-item="{{ forloop.index }}">
              
              <div class="cart__image">
                  <a href="{{ item.url }}" title="{{ item.title | escape }}" class="cart_page_image">
                    {%
                      render 'image-element',
                      image: item.image,
                      alt: item.title,
                      object_fit: true,
                      max_height: 150,
                    %}
                  </a>
                </div>
                <div class="cart__info">
                  <div class="cart__description">
                    <div class="cart__text">
                      <p class="item__title">
                        <a href="{{ item.url }}">{{ item.product.title }}</a>
                      </p>

                      {%- if item.product.has_only_default_variant == false
                        or item.properties.size != 0
                        or item.selling_plan_allocation != null
                      -%}
                        <ul class="cart__product-options">
                          {%- if item.product.has_only_default_variant == false -%}
                            {%- for option in item.options_with_values -%}
                              <li class="cart__product-option">
                                <span class="cart__product-option-name">
                                  {{- option.name -}}:
                                </span>
                                <span class="cart__product-option-value">
                                  {{- option.value -}}
                                </span>
                              </li>
                            {%- endfor -%}
                          {%- endif -%}

                          {%- for property in item.properties -%}
                            
                            {% unless property.first contains "_option_" %}
                              {% assign startsWith = property.first | truncate: 1, '' %}
                              {%- if property.first == "_rush" or property.first == "_rush_product" or property.first == "_accessories_product" -%}
                              {%- assign has_rush_products = true -%}
                              {% elsif property.first == "_rush_pdp" or property.first == "_rush_product_pdp"%}
                              {%- assign has_rush_products_pdp = true -%}
                              {%- endif -%}
                              {% comment %} <p>{{has_rush_products}}</p> {% endcomment %}
                          
                              {% if item.product.type == 'Plush' %}
                              {% if property.first contains '_pet_type' %}
                              <p>
                                <span class="cart__product-option-name">
                                  Pet Profile:
                                </span>
                                <span class="cart__product-option-value">
                                  {{- property.last | capitalize -}}
                                </span>
                              </p>
                              {% elsif  property.first contains 'mouth'%}
                                <p>
                                <span class="cart__product-option-name">
                                  Mouth Postition:
                                </span>
                                <span class="cart__product-option-value">
                                  {{- property.last | capitalize | replace: "-", " " -}}
                                </span>
                              </p>
                              {% elsif  property.first contains 'tail'%}
                                <p>
                                <span class="cart__product-option-name">
                                  Tail Postition:
                                </span>
                                <span class="cart__product-option-value">
                                  {{- property.last | capitalize | replace: "-", " " -}}
                                </span>
                              </p>
                              {% elsif  property.first contains 'body'%}
                                <p>
                                <span class="cart__product-option-name">
                                  Body Postition:
                                </span>
                                <span class="cart__product-option-value">
                                  {{- property.last | capitalize | replace: "-", " " -}}
                                </span>
                              </p>
                            {% endif %}

                              {% else %}
                              {%- assign property_first_char = property.first | slice: 0 -%}
                              {%- if property.last != blank and property_first_char != '_' -%}
                              <li class="cart__product-option">
                                <span class="cart__product-option-name">
                                  {{- property.first |  capitalize -}}:
                                </span>

                                <span class="cart__product-option-value">
                                  {%- if property.last contains '/uploads/' -%}
                                    <a
                                      class="lightbox"
                                      href="{{ property.last }}"
                                      target="_blank"
                                    >
                                      {{- 'cart.general.uploaded_file' | t -}}
                                    </a>
                                  {%- else -%}
                                    {{- property.last -}}
                                  {%- endif -%}
                                </span>
                              </li>
                              {%- endif -%}
                              {% endif %}
                            {%endunless%}
                          {%- endfor -%}
                          {%- for property in item.properties -%}
                            {% if property.first contains "_image_url"  %}   
                              <p>
                                <span class="cart__product-option-name">
                                  Your Pet Image(s):
                                </span>
                              </p>
                              {% break %}                         
                            {%endif%}
                          {%- endfor -%}
                          <div style="display: flex;flex-wrap:wrap;justify-content:flex-start">
                          {%- for property in item.properties -%}
                            {% if property.first contains "_image_url"  %}   
                              <img src="{{property.last}}" alt="Cropped Image" width="50" height="50" style="margin: 10px;">
                              {%endif%}
                            {%- endfor -%}
                            </div>

                          {%- if item.selling_plan_allocation.selling_plan -%}
                            <li class="cart__product-option">
                              {{- item.selling_plan_allocation.selling_plan.name -}}
                            </li>
                          {%- endif -%}
                        </ul>
                      {%- endif -%}
                    </div>
                  </div>

                  <div class="cart__price">
                    {% assign regular_price = item.original_price %}
                    {% if regular_price < item.variant.compare_at_price %}
                        {% assign regular_price = item.variant.compare_at_price %}
                    {% endif %}
                    {% assign final_price = item.original_price %}
                    {% assign type = item.product.type | downcase %}
                    {% if type != 'rush' %}
                        {% assign total_regular_price = regular_price | times: item.quantity %}
                        {% assign subtotal_before_product_discounts = subtotal_before_product_discounts | plus: total_regular_price %}

                        {% assign total_final_price = final_price | times: item.quantity %}
                        {% assign subtotal_after_product_discounts = subtotal_after_product_discounts | plus: total_final_price %}
                    {% endif %}
                    {% if item.line_level_discount_allocations.size < 1 %}
                      <p>
                        {% if item.original_price > 0 %}
                          <span class="money {% if item.original_price < item.variant.compare_at_price %}sale{% endif %}">
                            {%
                              render 'price-element',
                              price: item.original_price
                            %}
                          </span>
                          {% if item.original_price < item.variant.compare_at_price %}
                            <span class="money compare-at-price">
                              {%
                                render 'price-element',
                                price: item.variant.compare_at_price
                              %}
                            </span>
                          {% endif %}
                        {% else %}
                          {{ settings.free_price_text }}
                        {% endif %}
                      </p>
                    {% endif %}

                    {% comment %} Discounts for line items {% endcomment %}
                    {% if item.line_level_discount_allocations.size > 0 %}
                      <p class="sale">
                        <span class="money">
                          {%
                            render 'price-element',
                            price: item.final_price
                          %}
                        </span>
                      </p>
                      {% if item.original_line_price > item.final_line_price %}
                        <s class="original-price">
                          <span class="money">
                            {%
                              render 'price-element',
                              price: item.original_price
                            %}
                          </span>
                        </s>
                      {% endif %}
                      {% for discount_allocation in item.line_level_discount_allocations %}
                        <div class="line-item-discount__container">
                          <p>{{ discount_allocation.discount_application.title }}</p>
                        </div>
                      {% endfor %}
                    {% endif %}

                    {%
                      render 'unit-price',
                      item: item,
                      class: 'cart__unit-price'
                    %}
                  </div>

                  {% if section.settings.display_product_quantity_cart %}
                    
                      <div class="cart__quantity">
                        <div class="purchase-details__quantity product-quantity-box" data-line-item-key={{ forloop.index }}>
                          {% unless item.product.tags contains 'hide' %}
                            {%
                            render 'quantity-box',
                            item: item,
                            size: 'is-medium',
                            variant: item.variant
                            %}
                          {% endunless %}
                        </div>
                          <p class="cart__quantity-warning quantity-warning">
                            </div>
                          
                    
                  {% endif %}

                  <div class="cart__total">
                    <p>
                      {% if item.final_line_price > 0 %}
                        <span class="money">
                          {%
                            render 'price-element',
                            price: item.final_line_price
                          %}
                        </span>
                      {% else %}
                        {{ settings.free_price_text }}
                      {% endif %}
                    </p>
                  </div>
                </div>
                <div class="cart__remove">
                  <a class="cart__remove-btn is-inline-flex is-align-center is-justify-center ajax-cart__delete" data-cart-page-delete data-cart-item-key="{{ item.key }}" data-line-item-key={{ forloop.index }} href="{{ routes.cart_change_url }}?line={{ forloop.index }}&amp;quantity=0" title="{{ 'cart.general.remove' | t }}" >
                    <button class="close" aria-label="close">
                      {%
                        render 'icon',
                        name: 'x'
                      %}
                    </button>
                  </a>
                </div>
              </div>
              {% endunless %}
          {% endfor %}

          <div class="cart__cost-summary container is-flex-column">
            <div class="column eight offset-by-eight medium-down--one-whole">
              {%- if cart.cart_level_discount_applications != blank -%}
                <div class="cart__discounts cart__row">
                  {%- for discount_application in cart.cart_level_discount_applications -%}
                    <div class="cart__row-description">
                      <p class="cart__discount-title">{{- discount_application.title -}}</p>
                    </div>
                    <p class="cart__discount">
                      <span class="money">
                        -
                        {%
                          render 'price-element',
                          price: discount_application.total_allocated_amount
                        %}
                      </span>
                    </p>
                  {%- endfor -%}
                </div>
              {%- endif -%}     
              {% if subtotal_after_product_discounts < subtotal_before_product_discounts %}
                <div class="cart__product-subtotal cart__row">
                  <div class="cart__row-description">
                      <p>{{ 'layout.general.product_subtotal' | t }}</p>
                  </div>
                  <div>
                      <span class="money">{% render 'price-element', price: subtotal_before_product_discounts %}</span>
                  </div>
              </div> 
            {% endif %}
              {% if section.settings.display_savings and total_saving > 0 %}
                <div class="cart__total-savings cart__row">
                  <div class="cart__row-description">
                    <p class="cart">{{ 'layout.general.savings' | t }}:</p>
                  </div>
                  <p class="cart__total-discount">
                    <span class="money">
                      {% assign total_savings = total_saving | plus: cart.total_discount %}
                      {%
                        render 'price-element',
                        price: total_savings
                      %}
                    </span>
                  </p>
                </div>
                
              {% endif %}
             
              {% comment %} {% if has_rush_products or has_rush_products_pdp %}
                {% if cart.total_discount > 0 %}
                  <div class="cart__subtotal-container cart__row">
                      <div class="cart__row-description">
                      <p class="h3">{{ 'cart.general.subtotal' | t }}:</p>
                      </div>
                      <p class="h3 cart__subtotal">
                      <span class="money">
                          {% assign subtotal_without_rush = cart.total_price %}
                          {% if has_rush_added %}
                              {% assign subtotal_without_rush = subtotal_without_rush | minus: rush_variant_price %}
                          {% endif %}
                          {% if has_rush_added %}
                              {% assign subtotal_without_rush = subtotal_without_rush | minus: rush_variant_price_pdp %}
                          {% endif %}
                          {% render 'price-element', price: subtotal_without_rush %}
                      </span>
                      </p>
                  </div>
                {% endif %}
                {% comment %} <p>====={{has_rush_added}}</p> {% endcomment %}
                {% if has_rush_products %}
                  <div class="cart__row">
                      {% include 'cart__rush' with cart: cart %}
                  </div>
                {% elsif has_rush_products_pdp %}
                  <div class="cart__row">
                      {% include 'cart__rush_pdp' with cart: cart %}
                  </div>
                {% endif %}
              {% endif %} {% endcomment %}
              
              <div class="cart__subtotal-container cart__row">
                <div class="cart__row-description">
                  <p class="h3">{{ 'cart.general.subtotal' | t }}:</p>
                </div>
                <p class="h3 cart__subtotal">
                  <span class="money">
                    {%
                      render 'price-element',
                      price: cart.total_price
                    %}
                  </span>
                </p>
              </div>
            </div>
          </div>

          <div class="container has-no-side-gutter has-padding-top">
            <div class="two-thirds column medium-down--one-whole has-padding-bottom">
              {% if settings.display_special_instructions %}
                <span class="cart__notes">
                    <div class="field">
                      <label
                        for="note"
                        {% if settings.use_placeholders %}class="is-sr-only"{% endif %}
                      >
                        {{ 'cart.general.note' | t: shop_name: shop.name }}:
                      </label>
                      <div class="control">
                        <textarea
                          class="textarea"
                          id="note"
                          name="note"
                          rows="3"
                          {% if settings.use_placeholders %}placeholder="{{ 'cart.general.note_detailed' | t: shop_name: shop.name }}"{% endif %}
                        >
                          {{- cart.note -}}
                        </textarea>
                      </div>
                    </div>
                </span>
              {% endif %}
            </div>
            <div class="one-third column medium-down--one-whole">
              <div class="cart__price-breakdown {% if settings.display_tos_checkbox %}js-tos-enabled{% endif %}">
                  {% if section.settings.cart_message != blank %}
                    <div class="cart__cart-message is-flex is-justify-end">
                      {{ section.settings.cart_message }}
                    </div>
                  {% endif %}

                  <div class="cart__checkout-container">
                    <div class="is-flex is-align-right cart__taxes-shipping text-align-right">
                      {%- capture taxes_shipping_checkout -%}
                        {%- if cart.taxes_included and shop.shipping_policy.body != blank -%}
                          {{ 'cart.general.taxes_included_and_shipping_policy_html' | t: link: shop.shipping_policy.url }}
                        {%- elsif cart.taxes_included -%}
                          {{ 'cart.general.taxes_included_but_shipping_at_checkout' | t }}
                        {%- elsif shop.shipping_policy.body != blank -%}
                          {{ 'cart.general.taxes_and_shipping_policy_at_checkout_html' | t: link: shop.shipping_policy.url }}
                        {%- else -%}
                          {{ 'cart.general.tax_and_shipping' | t }}
                        {%- endif -%}
                      {%- endcapture -%}

                      <p class="cart__taxes-shipping-message has-padding-bottom">{{ taxes_shipping_checkout }}</p>
                    </div>
                    <div class="is-flex is-align-center cart__checkout-elements">
                      {% if settings.display_tos_checkbox %}
                        <div class="cart__tos">
                          <div class="field">
                            <label class="tos_label" for="cart_agree">
                              <input type="checkbox" class="tos_agree" id="cart_agree" required />
                              {{ 'cart.general.agree_to_terms_html' | t }}
                            </label>
                            {% if settings.tos_page != blank %}
                              <div class="cart__view-terms-container">
                                <a href="{{ pages[settings.tos_page].url }}" class="text-align-right" target="_blank"  rel="noopener">{{ 'cart.general.view_terms' | t }}</a>
                              </div>
                            {% endif %}
                          </div>
                        </div>
                      {% endif %}
                    </div>
                    <div class="is-flex is-align-center is-justify-end cart__buttons">
                      <div class="cart__checkout">
                        <button type="submit" class="button add_to_cart checkout" id="checkout" name="checkout" value="{{ 'cart.general.checkout' | t }}">
                          {% if settings.show_lock_icon %}
                            {%
                              render 'icon',
                              name: 'lock'
                            %}
                          {% endif %}
                          {{ 'cart.general.checkout' | t }}
                        </button>
                      </div>
                    </div>
                    {% comment %} ============================================================ {% endcomment %}
                    {% comment %} <div class="is-flex is-align-center is-justify-end">
                      {% if content_for_additional_checkout_buttons %}
                        <div class="additional-checkout-buttons">
                          {{ content_for_additional_checkout_buttons }}
                        </div>
                      {% endif %}
                    </div> {% endcomment %}
                    {% comment %} ============================================================= {% endcomment %}
                    <div class="is-flex is-align-center is-justify-end">
                      {% if cart.item_count > 0 %}
                        <div class="cart__featured-links">
                          <a href="{{ continue_shopping_link }}">{{ 'cart.general.continue_shopping_link_html' | t }}</a>
                        </div>
                      {% endif %}
                    </div>
                  </div>

              </div>
            </div>
          </div>

        </div>
      </div>
    </form>
  </div>

  {% if settings.enable_shipping_calculator == true %}
    <div class="container">
      <div class="one-whole column">
        {% render 'shipping-calculator' %}
      </div>
    </div>
  {% endif %}

</section>
<script>
  // Script to remove extra rush products from cart
var cart = {
    items: [
      {% for item in cart.items %}
        {
          product_id: {{ item.product.id }},
          variant_id: {{ item.variant.id }},
          product_type: "{{ item.product.type }}",
          key: "{{ item.key }}",
          quantity: {{ item.quantity }},
          tags: [{% for tag in item.product.tags %}"{{ tag }}"{% unless forloop.last %}, {% endunless %}{% endfor %}]
        }
        {% unless forloop.last %},{% endunless %}
      {% endfor %}
    ]
  };
var rushProducts = {};
var maxRushItems = 1;
var uniqueRushProducts = {};

cart.items.forEach(function(item) {
  if (item.product_type && item.product_type.toUpperCase() === 'RUSH') {
    if (!rushProducts[item.product_id]) {
      rushProducts[item.product_id] = {
        totalQuantity: 0,
        variants: {}
      };
    }
    rushProducts[item.product_id].totalQuantity += item.quantity;
    rushProducts[item.product_id].variants[item.variant_id] = item;
  }
});

for (var productId in rushProducts) {
  var product = rushProducts[productId];
  if (Object.keys(uniqueRushProducts).length < maxRushItems) {
    for (var variantId in product.variants) {
      uniqueRushProducts[variantId] = product.variants[variantId];
      break;
    }
  }
}
for (var productId in rushProducts) {
  var product = rushProducts[productId];
  for (var variantId in product.variants) {
    if (!uniqueRushProducts[variantId]) {
      var variantToRemove = product.variants[variantId];
      fetch('/cart/change.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          quantity: 0,
          id: variantToRemove.key
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        console.log('Extra variant removed successfully.');
        window.location.reload();
      })
      .catch(error => {
        console.error('Error removing extra variant from cart:', error);
      });
    } else if (product.variants[variantId].quantity > 1) {
      var variantToRemove = product.variants[variantId];
      fetch('/cart/change.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          quantity: variantToRemove - 1,
          id: variantToRemove.key
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        console.log('Extra variant removed successfully.');
        window.location.reload();
      })
      .catch(error => {
        console.error('Error removing extra variant from cart:', error);
      });
    }
  }
}



var bulkProducts = {};
cart.items.forEach(function(item) {
    if(item.tags.includes('oneRush')) {
    if (!bulkProducts[item.product_id]) {
      bulkProducts[item.product_id] = {
        variants: {}
      };
    }
    bulkProducts[item.product_id].variants[item.variant_id] = item;
}
});

for (var productId in bulkProducts) {
  var product = bulkProducts[productId];

  for (var variantId in product.variants) {
    var variantItem = product.variants[variantId];
    
    if (variantItem.quantity > 1) {
      fetch('/cart/change.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          quantity: 1,
          id: variantItem.key
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        console.log('Extra Rush Quantity removed successfully.');
        // window.location.reload();
        location.replace(location.href);

      })
      .catch(error => {
        console.error('Error Removing Extra Rush Quantity:', error);
      });
    }
  }
}
</script>

<script>
  async function getCart() {
    const res = await fetch('/cart.js');
    return res.json();
  }
 
  function getGroupAddons(items, uniqueKey,addonHandles) {
    return items.filter(item =>
      item.properties &&
      item.properties._unique_key === uniqueKey &&
      addonHandles.includes(item.handle)
    );
  }

  function overrideRemoveFromCart() {
    if (!window.PXUTheme?.jsCart) return;

    const originalRemove = window.PXUTheme.jsCart.removeFromCart;

    window.PXUTheme.jsCart.removeFromCart = async function (lineID) {
      const cart = await getCart();
      const items = cart.items;
      const index = lineID - 1;
      const item = items[index];
      if (!item) return originalRemove.call(this, lineID);

      if (item.handle === 'zipper-pouch') {
        const uniqueKey = item.properties?._unique_key;
        const groupAddons = getGroupAddons(items, uniqueKey, ['heartbeat-box', 'weighted-insert']);
        if (groupAddons.length > 0) {
          alert("Zipper Pouch cannot be removed because it's required by Heartbeat Box or Weighted Insert.");
          return; // prevent zipper_pouch deletion
        }
      }

      return originalRemove.call(this, lineID); // allow deletion for other products
    };
  }

  document.addEventListener('DOMContentLoaded', overrideRemoveFromCart);
</script>

{% comment %}JavaScript{% endcomment %}
<script
  type="application/json"
  data-section-id="{{ section.id }}"
  data-section-data
>
  {
    "shipping": {
      "multiple_rates": {{ 'cart.shipping_calculator.multiple_rates' | t: number_of_rates: '*number_of_rates*', address: '*address*', rate: '*rate*' | json }},
      "one_rate": {{ 'cart.shipping_calculator.one_rate' | t: address: '*address*' | json }},
      "no_rates": {{ 'cart.shipping_calculator.no_rates' | t | json }},
      "rate_value": {{ 'cart.shipping_calculator.rate_value' | t: rate_title: '*rate_title*', rate: '*rate*'  | json }}
    }
  }
</script>
<script src="{{ 'z__jsCart.js' | asset_url }}" defer></script>

{% schema %}

{
  "name": "Cart",
  "class": "cart-section jsCart jsShippingCalculator",
  "settings": [
    {
      "type": "product",
      "id": "rush_product",
      "label": "RUSH product"
    },
    {
      "type": "product",
      "id": "rush_product_pdp",
      "label": "RUSH product pdp"
    },
    {
      "type": "text",
      "id": "rush_disclaimer",
      "label": "RUSH disclaimer",
      "default": "Rush applies to plush products only. Other products may ship separately. Does not include shipping time or costs.",
      "info": "If this is not empty, it will show up when the last RUSH option is selected."
    },
    {
      "type": "checkbox",
      "id": "display_product_quantity_cart",
      "label": "Show quantity box",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "display_savings",
      "label": "Show total savings",
      "default": true
    },
    {
      "type": "richtext",
      "id": "cart_message",
      "label": "Cart message"
    },
    {
      "type": "header",
      "content": "Continue shopping button"
    },
    {
      "type": "url",
      "id": "continue_shopping_link",
      "label": "Link"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "standard",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ]
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ]
}

{% endschema %}
