{% comment %}
** Page content with sidebar **
{% endcomment %}

{% assign id = section.id %}
{% comment %}Layout{% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% assign animation = section.settings.animation | default: 'none' %}
{% comment %}Advanced{% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  .sidebar-section {
    {% if section.settings.sidebar_position == 'right' %}
      order: 1;
    {% endif %}
  }

  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

<section class="section
        {{ css_class }}
        is-width-{{ width }}"
        {% if animation != "none" %}
          data-scroll-class="{{ animation }}"
        {% endif %}>
  <div class="container">

    {% if section.blocks.size > 0 %}
      {% comment %} HTML markup {% endcomment %}
      <aside class="sidebar-section
                    one-fourth
                    medium-down--one-whole
                    column
                    has-margin-top">
        {% for block in section.blocks %}
          <div id="shopify-section-{{ block.id }}"
              class="sidebar__block block__{{ block.id }} block__{{ block.type | downcase | replace: '_', '-' }}
                      has-padding-top has-padding-bottom
                      {% if settings.toggle_sidebar %}sidebar-toggle-active{% endif %}"
              {{ block.shopify_attributes }}>

            {% if block.type == 'featured_promo' %}
              {% comment %} Featured promo {% endcomment %}
              {% render 'sidebar__featured-promo',
                      block: block,
                      id: block.id
              %}

            {% elsif block.type == 'html' %}
              {% comment %} Custom HTML {% endcomment %}
              {% render 'sidebar__html', block: block %}

            {% elsif block.type == 'menu' %}
              {% comment %} Menu {% endcomment %}
              {% render 'sidebar__menu', menu: block.settings.menu %}

            {% elsif block.type == 'newsletter' %}
              {% comment %} Newsletter {% endcomment %}
              {% render 'sidebar__newsletter', block: block %}

            {% elsif block.type == 'page' %}
              {% comment %} Page {% endcomment %}
              {% render 'sidebar__page', page: block.settings.page %}

            {% elsif block.type == 'search' %}
              {% comment %} Search {% endcomment %}
              {%
                render 'sidebar__search',
                block: block,
              %}

            {% elsif block.type == 'text' %}
              {% comment %} Text {% endcomment %}
              {% render 'sidebar__text', block: block %}

            {% endif %}
          </div>
        {% endfor %}
      </aside>
    {% endif %}

    <script data-theme-editor-load-script src="{{ 'z__jsSidebar.js' | asset_url }}"></script>

    <main class="three-fourths medium-down--one-whole column has-margin-top equal-columns--outside-trim">
      <div class="container">
          {%- if section.settings.show_heading != blank -%}
            {% render 'heading',
                    title: page.title,
                    heading_tag: 'h1',
                    context: 'page',
                    text_alignment: section.settings.heading_alignment
            %}
          {%- endif -%}
      </div>
      <div class="page__content content container {% if settings.table_styles_enabled %} table is-striped is-bordered {% endif %}">
        {% render 'page-multi-column', content: page.content %}
      </div>
    </main>

  </div>
</section>

{% schema %}

{
  "name": "Page content with sidebar",
  "class": "sidebar-section page-sidebar jsSidebar",
  "settings": [
    {
      "type": "radio",
      "id": "sidebar_position",
      "label": "Sidebar position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "checkbox",
      "id": "show_heading",
      "label": "Show page title",
      "default": true
    },
    {
      "type": "select",
      "id": "heading_alignment",
      "label": "Heading alignment",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ]
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "standard",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ]
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ],
  "blocks": [
    {
      "type": "html",
      "name": "Custom HTML",
      "settings": [
        {
          "type": "textarea",
          "id": "html_content",
          "label": "HTML",
          "default": "<div class='container is-flex is-justify-center'><h2 class='title'>Your own custom HTML</h2></div>"
        }
      ]
    },
    {
      "type": "featured_promo",
      "name": "Featured promotion",
      "settings": [
        {
          "type": "color",
          "id": "promo_background",
          "label": "Background",
          "default": "#EEEEEE"
        },
        {
          "type": "color",
          "id": "promo_text",
          "label": "Text",
          "default": "#000000"
        },
        {
          "type": "image_picker",
          "id": "promo_image",
          "label": "Image"
        },
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Text",
          "default": "<p>Use this area for promotional information.</p>"
        },
        {
          "type": "url",
          "id": "promo_link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Shop now"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button Style",
          "options": [
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "value": "button--link-style",
              "label": "Link style"
            }
          ],
          "default": "button--primary"
        }
      ]
    },
    {
      "type": "menu",
      "name": "Menu",
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "info": "This menu won't show drop-down items."
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "newsletter_title",
          "label": "Heading",
          "default": "Subscribe"
        },
        {
          "type": "richtext",
          "id": "newsletter_richtext",
          "label": "Text",
          "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
        },
        {
          "type": "checkbox",
          "id": "display_first_name",
          "label": "Show first name"
        },
        {
          "type": "checkbox",
          "id": "display_last_name",
          "label": "Show last name"
        }
      ]
    },
    {
      "type": "page",
      "name": "Page",
      "settings": [
        {
          "type": "page",
          "id": "page",
          "label": "Page"
        }
      ]
    },
    {
      "type": "search",
      "name": "Search form",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Search"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Text area can be used for details about blog authors or general information.</p>"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "featured_promo"
      },
      {
        "type": "menu"
      },
      {
        "type": "text"
      }
    ]
  }
}

{% endschema %}
