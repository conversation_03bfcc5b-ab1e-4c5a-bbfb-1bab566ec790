{% comment %} Product description - split {% endcomment %}

{% assign id = section.id %}
{% comment %} Layout {% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% assign animation = section.settings.animation | default: 'none' %}
{% comment %} Advanced {% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% comment %} Section specific CSS {% endcomment %}
{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

{% comment %} HTML markup {% endcomment %}
{% if product.description contains "<!-- split -->"  %}
<section class="section
              {{ section.settings.css_class }}
              is-width-{{ section.settings.width }}
              {% if background_alpha != 0 or gradient_alpha != 0  %}
                has-background
              {% endif %}
              has-padding-top
              has-padding-bottom
              "
        {% if section.settings.animation != "none" %}
          data-scroll-class="{{ section.settings.animation }}"
        {% endif %}>
  <div class="container
              has-limit
              content
              ">
      <div class="one-whole column">
        <div class="product-description-split">
          <div class="description content">
            {{ product.description | split: '<!-- split -->' | last }}
          </div>
        </div>
      </div>
  </div>
</section>
{% endif %}

{% schema %}

{
  "name": "Description split",
  "class": "product-template product-split-description",
  "templates": [
    "product"
  ],
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ],
      "default": "standard"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "select",
      "id": "animation",
      "label": "Animation",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "fadeIn",
          "label": "Fade in"
        },
        {
          "value": "fadeInDown",
          "label": "Fade in down"
        },
        {
          "value": "fadeInLeft",
          "label": "Fade in left"
        },
        {
          "value": "fadeInRight",
          "label": "Fade in right"
        },
        {
          "value": "slideInLeft",
          "label": "Slide in left"
        },
        {
          "value": "slideInRight",
          "label": "Slide in right"
        },
        {
          "value": "zoomIn",
          "label": "Zoom in"
        }
      ]
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ],
  "presets": [{
    "name": "Description <!-- split -->",
    "category": "Product",
    "settings": {
    }
  }]
}

{% endschema %}
