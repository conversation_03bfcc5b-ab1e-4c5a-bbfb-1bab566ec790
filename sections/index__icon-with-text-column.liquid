{% comment %}
** Icon with text column **
{% endcomment %}

{% liquid
  # Is the color set to transparent?
  assign text_alpha = section.settings.text_color | color_extract: 'alpha'
  assign icon_alpha = section.settings.icon_color | color_extract: 'alpha'
  assign background_alpha = section.settings.background | color_extract: 'alpha'
  assign gradient_alpha = section.settings.gradient | color_extract: 'alpha'
%}

{% comment %} Section specific CSS {% endcomment %}
{% capture section_css -%}
  .icon-with-text-column__column {
    background-image: linear-gradient({{ section.settings.gradient_rotation }}deg, rgba(255,255,255,0), {{ section.settings.gradient }});
    background-color: {%- if background_alpha != 0 -%}{{ section.settings.background }}{%- endif -%};
  }

  .icon-column__title {
    color: {%- if text_alpha != 0 -%}{{ section.settings.text_color }}{%- endif -%};
  }

  .icon {
    fill: {%- if icon_alpha != 0 -%}{{ section.settings.icon_color }}{%- endif -%};
  }

  .icon-column__text {
    color: {%- if text_alpha != 0 -%}{{ section.settings.text_color }}{%- endif -%};
  }
{%- endcapture -%}

{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-right: {{ section.settings.padding_right }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    padding-left: {{ section.settings.padding_left }}px;

    {% if section.settings.width == 'wide' %}
      width: 100%;
    {% elsif section.settings.width == 'half' %}
      width: 50%;
    {% endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section_css,
    id: section.id,
  %}

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ section.settings.width }}
    {% if section.settings.show_gutter == false %}
      has-no-side-gutter
      has-background
    {% else %}
      has-gutter-enabled
    {% endif %}
    {% if section.blocks.size > section.settings.icons_per_row %}
      has-multirow-blocks
    {% endif %}
    {% if section.settings.width == 'wide' %}
      equal-columns--outside-trim
    {% endif %}
  "
  {% if section.settings.animation != "none" %}
    data-scroll-class="{{ section.settings.animation }}"
  {% endif %}
>
  <div class="container is-justify-center">
    {% for block in section.blocks %}
      <div
        class="
          icon-with-text-column__column
          {% if section.settings.show_gutter %}
            has-gutter
            has-gutter--mobile
          {% endif %}
          column
          has-padding
          {% render 'column-width', value: section.settings.icons_per_row %}
          text-align-{{ section.settings.align_text }}
          {% if section.settings.mobile_text_alignment != 'none' %}
            text-align--mobile-{{ section.settings.mobile_text_alignment }}
          {% endif %}
          medium-down--one-whole
        "
      >
        {%- capture icon_size -%}
          is-{{ section.settings.icon_size }}
          {% if section.settings.mobile_icon_size != 'none' %}
            is--mobile-{{ section.settings.mobile_icon_size }}
          {% endif %}
        {%- endcapture -%}

        {%- assign icon = block.settings.icon_label | downcase -%}

        {%
          render 'icon',
          name: icon,
          icon_class: icon_size,
        %}

        {% if block.settings.title != blank or block.settings.text != blank %}
          <div class="icon-column__text">
            <h3 class="icon-column__title title">
              {{- block.settings.title -}}
            </h3>
            {{ block.settings.text }}
          </div>
        {% endif %}

        {% if block.settings.button_label != blank %}
          <div class="icon-column__action has-padding-top">
            {%
              render 'button',
              label: block.settings.button_label,
              href: block.settings.link,
              style: section.settings.button_style,
              type: "link",
            %}
          </div>
        {% endif %}
      </div>
    {% endfor %}
  </div>
</section>

{% schema %}
  {
    "name": "Text columns with icons",
    "class": "icon-with-text-column",
    "max_blocks": 20,
    "settings": [
      {
        "type": "range",
        "id": "icons_per_row",
        "label": "Items per row",
        "min": 1,
        "max": 8,
        "default": 4
      },
      {
        "type": "text_alignment",
        "id": "align_text",
        "label": "Text alignment",
        "default": "center"
      },
      {
        "type": "select",
        "id": "icon_size",
        "label": "Icon size",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ],
        "default": "medium"
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "options": [
          {
            "value": "button--primary",
            "label": "Primary"
          },
          {
            "value": "button--secondary",
            "label": "Secondary"
          },
          {
            "value": "button--link-style",
            "label": "Link style"
          }
        ],
        "default": "button--primary"
      },
      {
        "type": "header",
        "content": "Colors"
      },
      {
        "type": "color",
        "id": "icon_color",
        "label": "Icons",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color",
        "id": "background",
        "label": "Background",
        "default": "#CCCCCC"
      },
      {
        "type": "color",
        "id": "gradient",
        "label": "Background gradient",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "range",
        "id": "gradient_rotation",
        "label": "Gradient rotation",
        "min": 0,
        "max": 180,
        "step": 10,
        "default": 0,
        "unit": "deg"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "half",
            "label": "Half"
          },
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "show_gutter",
        "label": "Show gutter",
        "default": true
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_left",
        "label": "Left spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_right",
        "label": "Right spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          },
          {
            "value": "zoomIn",
            "label": "Zoom in"
          }
        ]
      },
      {
        "type": "header",
        "content": "Mobile text"
      },
      {
        "type": "select",
        "id": "mobile_text_alignment",
        "label": "Mobile text alignment",
        "options": [
          {
            "value": "none",
            "label": "Same as desktop"
          },
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "none"
      },
      {
        "type": "select",
        "id": "mobile_icon_size",
        "label": "Mobile icon size",
        "options": [
          {
            "value": "none",
            "label": "Same as desktop"
          },
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ],
        "default": "none"
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "logo_image",
        "name": "Icon column",
        "settings": [
          {
            "type": "text",
            "id": "icon_label",
            "label": "Icon",
            "placeholder": "Enter icon name",
            "info": "[Icon list](https://help.outofthesandbox.com/hc/en-us/articles/360021570294)",
            "default": "truck"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Icon with text"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Describe the feature, service or product that this icon represents.</p>"
          },
          {
            "type": "text",
            "id": "button_label",
            "label": "Button label",
            "default": "Learn more"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Text columns with icons",
        "category": "Text",
        "blocks": [
          {
            "type": "logo_image",
            "settings": {
              "icon_label": "truck"
            }
          },
          {
            "type": "logo_image",
            "settings": {
              "icon_label": "bag"
            }
          },
          {
            "type": "logo_image",
            "settings": {
              "icon_label": "tag"
            }
          },
          {
            "type": "logo_image",
            "settings": {
              "icon_label": "star"
            }
          }
        ]
      }
    ],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
