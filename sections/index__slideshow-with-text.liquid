{% comment %}
** Slideshow with text **
{% endcomment %}

{% liquid
  assign blocks = section.blocks

  # Is the color set to transparent?
  assign background_alpha = section.settings.background_color | color_extract: 'alpha'
  assign text_alpha = section.settings.text_color | color_extract: 'alpha'
%}

{% comment %} Section specific CSS {% endcomment %}
{%- capture section_css -%}
  .text-slideshow-column {
    background: {% if background_alpha != 0 %}{{ section.settings.background_color }}{% else %}{{ settings.shop_bg_color }}{% endif %};
  }

  .text-slideshow__content .banner__subheading p,
  .text-slideshow__content .title {
    color: {% if text_alpha != 0 %}{{ section.settings.text_color }}{% else %}{{ settings.regular_color }}{% endif %};
  }

  .flickity-page-dots .dot {
    background: {% if text_alpha != 0 %}{{ section.settings.text_color }}{% else %}{{ settings.regular_color }}{% endif %};
  }
{%- endcapture -%}

{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;

    {% if section.settings.width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section_css,
    id: section.id,
  %}

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

{% if blocks.size > 0 %}
  <section
    class="
      section
      {{ section.settings.css_class }}
      is-width-{{ section.settings.width }}
      {% if section.settings.show_gutter == false %}
        has-no-side-gutter
        has-background
      {% else %}
        has-gutter-enabled
      {% endif %}
      {% if section.settings.width == 'wide' %}
        equal-columns--outside-trim
      {% endif %}
    "
  >
    <div class="slideshow-with-text__container container">
      <div
        class="
          image-slideshow-column
          three-fifths
          medium-down--one-whole
          column
          image-slideshow-position--{{ section.settings.image_position }}
        "
      >
        <div
          class="
            image-slideshow
            image-slideshow--{{ section.settings.image_transition }}
          "
          data-image-slideshow
        >
          {% for block in blocks %}
            {% liquid
              assign image = block.settings.image
              assign mobile_image = block.settings.mobile_image | default: block.settings.image
            %}

            <div
              class="image-slideshow__slide"
              id="shopify-section-{{ block.id }}"
              data-slide-index={{ forloop.index0 }} {{ block.shopify_attributes }}
            >
              {% if block.settings.button_label == blank and block.settings.button_link != blank %}
                <a href="{{ block.settings.button_link }}">
              {% endif %}

              {% if image != blank %}
                {%
                  render 'image-element',
                  image: image,
                  alt: image.alt,
                  stretch_width: true,
                  additional_wrapper_classes: 'is-hidden-mobile-only',
                  focal_point: image.presentation.focal_point,
                %}
              {% else %}
                {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg is-hidden-mobile-only' }}
              {% endif %}

              {% if mobile_image != blank %}
                {%
                  render 'image-element',
                  image: mobile_image,
                  alt: mobile_image.alt,
                  stretch_width: true,
                  additional_wrapper_classes: 'is-hidden-desktop-only',
                  focal_point: mobile_image.presentation.focal_point,
                %}
              {% else %}
                {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg is-hidden-desktop-only' }}
              {% endif %}

              {% if block.settings.button_label == blank and block.settings.button_link != blank %}
                </a>
              {% endif %}
            </div>
          {% endfor %}
        </div>
      </div>

      <div
        class="
          text-slideshow-column
          two-fifths
          medium-down--one-whole
          column
          is-align-center
          is-flex-wrap
        "
      >
        <div
          class="
            text-slideshow
            {% if show_dots == false %}
              flickity-page-dots--hidden
            {% endif %}
          "
          data-text-slideshow
          data-slide-index={{ forloop.index0 }} {{ block.shopify_attributes }}
        >
          {% for block in blocks %}
            <div
              class="
                text-slideshow__slide
                text-slideshow__slide--{{ block.id }}
              "
              id="shopify-section-{{ block.id }}"
            >
              <div
                class="
                  text-slideshow__content
                  text-align-{{ section.settings.text_alignment }}
                  {% if section.settings.mobile_text_alignment != 'none' %}
                    text-align--mobile-{{ section.settings.mobile_text_alignment }}
                  {% endif %}
                "
              >
                {% if block.settings.pretext != blank %}
                  <div
                    class="
                      text-slideshow__heading
                      banner__subheading
                      is-hidden-mobile-only
                    "
                  >
                    {{- block.settings.pretext -}}
                  </div>
                {% endif %}

                {% if block.settings.pretext != blank or block.settings.mobile_pretext != blank %}
                  <div
                    class="
                      text-slideshow__heading
                      banner__subheading
                      is-hidden-desktop-only
                    "
                  >
                    {{- block.settings.mobile_pretext | default: block.settings.pretext -}}
                  </div>
                {% endif %}

                {% if block.settings.title != blank %}
                  <h2
                    class="
                      text-slideshow__heading
                      banner__heading
                      title
                      is-hidden-mobile-only
                    "
                  >
                    {{- block.settings.title -}}
                  </h2>
                {% endif %}

                {% if block.settings.title != blank or block.settings.mobile_title != blank %}
                  <h2
                    class="
                      text-slideshow__heading
                      banner__heading
                      title
                      is-hidden-desktop-only
                    "
                  >
                    {{- block.settings.mobile_title | default: block.settings.title -}}
                  </h2>
                {% endif %}

                {% if block.settings.text != blank %}
                  <div
                    class="
                      text-slideshow__heading
                      banner__subheading
                      subtitle
                      content
                      is-hidden-mobile-only
                    "
                  >
                    {{- block.settings.text -}}
                  </div>
                {% endif %}

                {% if block.settings.text != blank or block.settings.mobile_text != blank %}
                  <div
                    class="
                      text-slideshow__heading
                      banner__subheading
                      subtitle
                      content
                      is-hidden-desktop-only
                    "
                  >
                    {{- block.settings.mobile_text | default: block.settings.text -}}
                  </div>
                {% endif %}

                {% if block.settings.button_label != blank %}
                  {%
                    render 'button',
                    label: block.settings.button_label,
                    href: block.settings.button_link,
                    style: section.settings.button_style,
                    type: "link",
                  %}
                {% endif %}
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </section>
{% else %}
  <div
    class="
      container
      has-padding-top
      has-padding-bottom
      text-align-center
    "
  >
    <div class="one-whole column">
      <p>{{ 'homepage.onboarding.no_content' | t }}</p>
    </div>
  </div>
{% endif %}

<script
  type="application/json"
  data-section-id="{{ section.id }}"
  data-section-data
>
  {
    "text_transition": {{ section.settings.text_transition | json }},
    "image_transition": {{ section.settings.image_transition | json }},
    "image_slideshow_speed": {{ section.settings.image_slideshow_speed | json }},
    "show_arrows": {{ section.settings.show_arrows | json }},
    "show_nav_buttons": {{ section.settings.show_nav_buttons | json }},
    "number_of_slides": {{ blocks.size | json }}
  }
</script>
<script data-theme-editor-load-script src="{{ 'z__jsSlideshowWithText.js' | asset_url }}"></script>

{% schema %}
  {
    "name": "Slideshow with text",
    "class": "jsSlideshowWithText slideshow-with-text",
    "settings": [
      {
        "type": "range",
        "id": "image_slideshow_speed",
        "label": "Change image every",
        "info": "Set to 0 to disable autoplay.",
        "min": 0,
        "max": 12,
        "step": 1,
        "default": 8,
        "unit": "sec"
      },
      {
        "type": "checkbox",
        "id": "show_arrows",
        "label": "Show arrows",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_nav_buttons",
        "label": "Show navigation dots",
        "default": true
      },
      {
        "type": "header",
        "content": "Images"
      },
      {
        "type": "select",
        "id": "image_position",
        "label": "Image position",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "right"
      },
      {
        "type": "select",
        "id": "image_transition",
        "label": "Image transition",
        "options": [
          {
            "value": "fade",
            "label": "Fade"
          },
          {
            "value": "slide",
            "label": "Slide"
          }
        ],
        "default": "slide"
      },
      {
        "type": "header",
        "content": "Text"
      },
      {
        "type": "color",
        "id": "background_color",
        "label": "Background color",
        "default": "#EEEEEE"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "text_alignment",
        "id": "text_alignment",
        "label": "Text alignment",
        "default": "center"
      },
      {
        "type": "select",
        "id": "text_transition",
        "label": "Text transition",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "zoomIn",
            "label": "Zoom in"
          }
        ],
        "default": "fadeIn"
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "options": [
          {
            "value": "button--primary",
            "label": "Primary"
          },
          {
            "value": "button--secondary",
            "label": "Secondary"
          },
          {
            "value": "button--link-style",
            "label": "Link style"
          }
        ],
        "default": "button--primary"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ],
        "default": "standard"
      },
      {
        "type": "checkbox",
        "id": "show_gutter",
        "label": "Show gutter",
        "default": true
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Mobile text"
      },
      {
        "type": "select",
        "id": "mobile_text_alignment",
        "label": "Mobile text alignment",
        "options": [
          {
            "value": "none",
            "label": "Same as desktop"
          },
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "none"
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "image",
        "name": "Slide",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image",
            "info": "1600 x 1000px recommended"
          },
          {
            "type": "image_picker",
            "id": "mobile_image",
            "label": "Mobile image",
            "info": "Optional. Desktop image will show on mobile by default."
          },
          {
            "type": "richtext",
            "id": "pretext",
            "label": "Preheading",
            "default": "<p>Preheading</p>"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Your headline"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Each slide can have its own text and button link. Use it to describe the product, collection, page, etc. that the image depicts.</p>"
          },
          {
            "type": "text",
            "id": "button_label",
            "label": "Button label"
          },
          {
            "type": "url",
            "id": "button_link",
            "label": "Slide link"
          },
          {
            "type": "header",
            "content": "Mobile text"
          },
          {
            "type": "richtext",
            "id": "mobile_pretext",
            "label": "Mobile preheading"
          },
          {
            "type": "text",
            "id": "mobile_title",
            "label": "Mobile heading"
          },
          {
            "type": "richtext",
            "id": "mobile_text",
            "label": "Mobile text"
          }
        ]
      }
    ],
    "presets": [{
      "name": "Slideshow with text",
      "category": "Image",
      "settings": {
      },
      "blocks": [
          {
            "type": "image",
            "settings": {
              "pretext": "<p>Add a preheader</p>",
              "title": "Slideshow with Text",
              "text": "<p>Each slide can have its own text and button link. Use it to describe the product, collection, page, etc. that the image depicts.</p>",
              "button_label": "Click here"
            }
          },
          {
            "type": "image",
            "settings": {
              "pretext": "<p>Add a preheader</p>",
              "title": "Second slide",
              "text": "<p>Each slide can use different text to accompany the image.</p>",
              "button_label": "Link to product"
            }
          }
        ]
      }
    ],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
