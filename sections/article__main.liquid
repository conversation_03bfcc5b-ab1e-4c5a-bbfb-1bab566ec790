{% liquid
  assign id = section.id
  assign width = section.settings.width
  assign animation = section.settings.animation | default: 'none'
%}

{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  .sidebar-section {
    {% if section.settings.sidebar_position == 'right' %}
      order: 1;
    {% endif %}
  }

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ width }}
  "
  {% if animation != "none" %}
    data-scroll-class="{{ animation }}"
  {% endif %}
>
  {% if section.settings.show_breadcrumb %}
    <div class="container breadcrumb__container">
      <div
        class="
          one-whole
          column
          is-flex
          is-justify-space-between
          is-flex-column-reverse-mobile
          breadcrumb
          is-{{ settings.breadcrumb_size }}
          is-{{ settings.breadcrumb_capitalization }}
        "
      >
        <div class="breadcrumb__wrapper">
          {%
            render 'breadcrumb',
            context: 'article',
          %}
        </div>

        <div class="page-navigation-arrows has-padding-bottom">
          {% if blog.previous_article %}
            <a
              class="page-navigation__previous"
              href="{{ blog.previous_article }}"
              title="{{ 'blogs.article.previous_article_html' | t }}"
            >
              {{- 'blogs.article.previous_article_html' | t -}}
            </a>
          {% endif %}

          {% if blog.next_article and blog.previous_article %}
            <span class="page-navigation__divider"> | </span>
          {% endif %}

          {% if blog.next_article %}
            <a
              class="page-navigation__next"
              href="{{ blog.next_article }}"
              title="{{ 'blogs.article.next_article_html' | t }}"
            >
              {{- 'blogs.article.next_article_html' | t -}}
            </a>
          {% endif %}
        </div>
      </div>
    </div>
  {% endif %}

  <div class="container">
    {% if section.blocks.size > 0 and section.settings.show_sidebar %}
      <aside
        class="
          sidebar-section
          one-fourth
          medium-down--one-whole
          column
          has-margin-top
        "
      >
        {% for block in section.blocks %}
          <div
            class="
              sidebar__block
              block__{{ block.type | downcase | replace: '_', '-' }}
              has-padding-top
              has-padding-bottom
              {% if settings.toggle_sidebar %}sidebar-toggle-active{% endif %}
            "
            id="shopify-section-{{ block.id }}"
            {{ block.shopify_attributes }}
          >
            {% case block.type %}
              {% when 'post_tags' %}
                {%
                  render 'sidebar__blog-post-tags',
                  block: block,
                %}

              {% when 'featured_promo' %}
                {%
                  render 'sidebar__featured-promo',
                  block: block,
                  id: block.id,
                %}

              {% when 'html' %}
                {%
                  render 'sidebar__html',
                  block: block,
                %}

              {% when 'menu' %}
                {%
                  render 'sidebar__menu',
                  menu: block.settings.menu,
                %}

              {% when 'newsletter' %}
                {%
                  render 'sidebar__newsletter',
                  block: block,
                %}

              {% when 'page' %}
                {%
                  render 'sidebar__page',
                  page: block.settings.page,
                %}

              {% when 'recent_posts' %}
                {%
                  render 'sidebar__recent-posts',
                  block: block,
                  blog_handle: block.settings.blog,
                %}

              {% when 'search' %}
                {%
                  render 'sidebar__search',
                  block: block,
                  resource_types: 'article',
                %}

              {% when 'text' %}
                {%
                  render 'sidebar__text',
                  block: block,
                %}
            {% endcase %}
          </div>
        {% endfor %}
      </aside>
    {% endif %}

    <section
      class="
        {% if section.blocks.size > 0 and section.settings.show_sidebar %}
          three-fourths
          medium-down--one-whole
          column equal-columns--outside-trim
          has-margin-top
        {% endif %}
      "
    >
      {% paginate article.comments by 100 %}
        <div class="container hide-when-banner-enabled">
          {%
            render 'heading',
            title: article.title,
            heading_tag: 'h1',
            context: 'article',
            text_alignment: 'left',
          %}
        </div>

        <div class="container blog-meta">
          <div
            class="
              one-whole
              column
              meta-info
              is-small
            "
          >
            {%
              render 'meta-info-list',
              blog_author: section.settings.blog_author,
              blog_date: section.settings.blog_date,
              blog_read_time: section.settings.read_time,
              blog_comment_count: section.settings.blog_comment_count,
            %}

            {% if section.settings.show_tags and article.tags.size > 0 %}
              <ul
                class="
                  meta-tag-list
                  tags
                  has-padding-top
                "
              >
                {% for tag in article.tags %}
                  <li class="tag tag--{{ settings.tag_style}}">
                    <a
                      href="{{ shop.url}}/blogs/{{ blog.handle }}/tagged/{{ tag | handleize }}"
                      title="{{ blog.title }} {{ 'blogs.general.tagged' | t }} {{ tag | escape }}"
                    >
                      {{- tag -}}
                    </a>
                  </li>
                {% endfor %}
              </ul>
            {% endif %}
          </div>
        </div>

        <div
          class="
            container
            article-content
            has-padding-top
            has-padding-bottom
          "
        >
          <div
            class="
              one-whole
              column
              content
            "
          >
            {% if article.image and section.settings.show_featured_image %}
              <p>
                {%
                  render 'image-element',
                  image: article.image,
                  alt: article.image.alt,
                  stretch_width: true,
                %}
              </p>
            {% endif %}

            {{ article.content }}
          </div>
        </div>

        <div
          class="
            container
            author-share-wrap
            is-flex
            is-justify-space-between
            has-padding-top
            has-padding-bottom
          "
          {{ block.shopify_attributes }}
        >
          <div
            class="
              blog-author
              one-half
              column
              medium-down--one-whole
            "
          >
            {% if block.settings.blog_author %}
              <div class="media">
                <div class="media-left">
                  <figure class="image">
                    <img src="//1.gravatar.com/avatar/{{ article.user.email | md5 }}?s=160&r=G" alt="{{ article.author }}" />
                  </figure>
                </div>

                <div class="media-content">
                  <p class="comment__author title is-5">
                    {{- article.author -}}
                  </p>
                  <p class="subtitle">
                    {{- article.user.bio -}}
                  </p>
                </div>
              </div>
            {% endif %}
          </div>

          {% if block.settings.display_social_buttons %}
            <div
              class="
                blog-share
                one-half
                column
                medium-down--one-whole
              "
            >
              {%
                render 'social-share-buttons',
                context: 'article',
              %}
            </div>
          {% endif %}
        </div>

        <div class="clear" id="comments" {{ block.shopify_attributes }}></div>

        <div class="container comment-section">
          {% if settings.disqus_enabled %}
            <div id="disqus_thread"></div>
            <script>
                var disqus_config = function () {
                    this.page.url = "{{ canonical_url }}";
                    this.page.identifier = "{{ article.id }}";
                };
                (function() {
                    var d = document, s = d.createElement('script');
                    s.src = '//{{ settings.disqus_shortname }}.disqus.com/embed.js';
                    s.setAttribute('data-timestamp', +new Date());
                    (d.head || d.body).appendChild(s);
                })();
            </script>
            <noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript" rel="nofollow">comments powered by Disqus.</a></noscript>
          {% elsif blog.comments_enabled? %}
            {% if article.comments_count > 0 %}
              <div
                class="
                  one-whole
                  column
                  comment-section--title
                  has-margin-bottom
                  has-margin-top
                "
              >
                <h4 class="title">
                  {{- 'blogs.comments.response_count' | t: count: article.comments.size -}}
                </h4>
              </div>

            <div
              class="
                one-whole
                column
                comment-section--cards
              "
            >
              {% for comment in article.comments %}
                <div id="comment-{{ comment.id }}" class="card has-margin-bottom">
                  <div class="card-content" data-email="{{ comment.email }}">
                    <div class="media">
                      <div class="media-left">
                        <figure class="image">
                          <img src="//1.gravatar.com/avatar/{{ comment.email | md5 }}?s=160&r=G" alt="{{ comment.author }}" />
                        </figure>
                      </div>

                      <div class="media-content">
                        <p class="title">
                          {{- comment.author -}}
                        </p>

                        <p class="subtitle has-text-grey-lighter">
                          <small>
                            {{- comment.created_at | date: format: "month_day_year" -}}
                          </small>
                        </p>

                        <div class="content">
                          {{- comment.content -}}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% endif %}

          <div
            class="
              one-whole
              column
              text-align-center
            "
          >
            {%
              render 'pagination',
              paginate: paginate,
            %}
          </div>

          <div class="clear" id="new-comment"></div>

          <div
            class="
              one-whole
              column
              new-comment-title
              has-margin-bottom
              has-margin-top
            "
          >
            <h4 class="title">
              {{- 'blogs.comments.title' | t -}}
            </h4>
          </div>

          {% if blog.moderated? %}
            <div class="one-whole column">
              <em>{{ 'blogs.comments.moderated' | t }}</em>
            </div>
          {% endif %}

          <div
            class="
              one-whole
              column
              new-comment-form
            "
          >
            {% form article %}
              {% if form.posted_successfully? %}
                {% if blog.moderated? %}
                  <p class="help">
                    {{- 'blogs.comments.success_moderated' | t -}}
                  </p>
                {% else %}
                  <p class="help is-success">
                    {{- 'blogs.comments.success' | t -}}
                  </p>
                {% endif %}
              {% endif %}

              {% if form.errors %}
                <p class="help is-danger">
                  {{- 'general.forms.post_error' | t -}}
                </p>
              {% endif %}

              <div class="field is-horizontal">
                <div class="field-body">
                  <div class="field">
                    <label class="label">
                      {{ 'blogs.comments.name' | t }} <span class="required-field-indicator">*</span>
                    </label>

                    <div class="control">
                      <input
                        class="input"
                        id="comment_author"
                        name="comment[author]"
                        required="required"
                        type="text"
                        placeholder="e.g Alex Smith"
                      >
                    </div>
                  </div>

                  <div class="field">
                    <label class="label">
                      {{ 'blogs.comments.email' | t }} <span class="required-field-indicator">*</span>
                    </label>

                    <div class="control">
                      <input
                        class="input"
                        id="comment_email"
                        name="comment[email]"
                        required="required"
                        type="email"
                        placeholder="e.g. <EMAIL>"
                      >
                    </div>
                  </div>
                </div>
              </div>

              <div class="field is-horizontal">
                <div class="field-body">
                  <div class="field">
                    <label class="label" for="comment_body">
                      {{ 'blogs.comments.comment' | t }} <span class="required-field-indicator">*</span>
                    </label>

                    <div class="control">
                      <textarea
                        class="textarea"
                        id="comment_body"
                        name="comment[body]"
                        rows="5"
                        required="required"
                      >
                        {{- form.body -}}
                      </textarea>
                    </div>
                  </div>
                </div>
              </div>

              <div class="field is-horizontal">
                <div class="field-body">
                  <div class="field">
                    <div class="control">
                      <input
                        class="button"
                        id="comment-submit"
                        type="submit"
                        value="{{ 'blogs.comments.post' | t }}"
                      >
                    </div>
                  </div>
                </div>
              </div>
            {% endform %}
          </div>
        {% endif %}
      </div>

      {% if blog.articles_count > 1 %}
        <div
          class="
            container
            related-blogs
            has-padding-bottom
          "
          {{ block.shopify_attributes }}
        >
          <div
            class="
              one-whole
              column
              related-blogs--title
              has-margin-bottom
              has-margin-top
            "
          >
            <h4 class="title">
              {{- 'blogs.article.additional_articles' | t: title: blog.title -}}
            </h4>
          </div>

          <div
            class="
              one-whole
              column
              related-blogs--content
              equal-columns--outside-trim
              field
              is-grouped
              is-flex-wrap
            "
          >
            {% assign article_found = false %}
            {% for a in blog.articles limit: 4 %}
              {% liquid
                assign skip_article = false

                if article_found == false and forloop.last
                  assign skip_article = true
                endif

                if article.id == a.id
                  assign article_found = true
                  assign skip_article = true
                endif
              %}

              {% unless skip_article %}
                <div
                  class="
                    one-third
                    columns
                    article
                    blog-card
                    card
                    show-border-{{ block.settings.show_border }}
                    medium-down--one-whole
                  "
                >
                  {% if a.image %}
                    <div class="card-image blog-card__image">
                      <figure class="image">
                        <a href="{{ a.url }}" title="{{ a.title | escape }}">
                          {%
                            render 'image-element',
                            image: a.image,
                            alt: a.image.alt,
                            stretch_width: true,
                          %}
                        </a>
                      </figure>
                    </div>
                  {% endif %}

                  <div class="card-content blog-card__content">
                    <div class="media">
                      <div class="media-content">
                        <h4 class="title">
                          <a href="{{ a.url }}" title="{{ a.title | escape }}">
                            {{- a.title -}}
                          </a>
                        </h4>
                      </div>
                    </div>

                    {% if a.excerpt != blank and block.settings.blog_show_excerpt %}
                      {% liquid
                        assign postexcerpt = a.excerpt | size
                        if postexcerpt > 150
                          assign excerptlength = 'lg'
                        elsif postexcerpt <= 150
                          assign excerptlength = 'sm'
                        endif
                      %}

                      <div
                        class="
                          excerpt
                          excerpt-length-{{ excerptlength }}
                          has-margin-bottom
                        "
                      >
                        {{ a.excerpt }}
                        <span class="truncation-fade"></span>
                      </div>
                    {% endif %}

                    <div class="meta-info is-small">
                      {% if block.settings.blog_show_tags %}
                        {% if a.tags.size > 0 %}
                          <ul class="meta-tag-list tags">
                            {% for tag in a.tags %}
                              <li class="tag tag--{{ settings.tag_style}}">
                                <a
                                  href="{{ shop.url}}/blogs/{{ blog.handle }}/tagged/{{ tag | handleize }}"
                                  title="{{ blog.title | escape }} {{ 'blogs.general.tagged' | t }} {{ tag | escape }}"
                                >
                                  {{- tag -}}
                                </a>
                              </li>
                            {% endfor %}
                          </ul>
                        {% endif %}
                      {% endif %}

                      {%
                        render 'meta-info-list',
                        article: a,
                        blog_author: block.settings.blog_author,
                        blog_date: block.settings.blog_date,
                        blog_read_time: block.settings.read_time,
                        blog_comment_count: block.settings.blog_comment_count,
                      %}
                    </div>
                  </div>

                  {% if block.settings.button_type != 'none' %}
                    <div class="blog-card__read-more buttons">
                      {%
                        render 'button',
                        label: block.settings.button_label,
                        href: a.url,
                        type: 'link',
                        style: block.settings.button_type,
                      %}
                    </div>
                  {% endif %}
                </div>
              {% endunless %}
            {% endfor %}
          </div>
        </div>
      {% endif %}
    {% endpaginate %}
    </section>
  </div>
</section>

<script data-theme-editor-load-script src="{{ 'z__jsSidebar.js' | asset_url }}"></script>

{% schema %}
  {
    "name": "Blog post",
    "class": "article-main has-sidebar-option jsSidebar",
    "settings": [
      {
        "type": "checkbox",
        "id": "show_breadcrumb",
        "label": "Show breadcrumbs",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_author",
        "label": "Show author",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_date",
        "label": "Show date",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "read_time",
        "label": "Show estimated read time",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_comment_count",
        "label": "Show comment count",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_tags",
        "label": "Show tags",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "show_featured_image",
        "label": "Show featured image",
        "default": false
      },
      {
        "type": "header",
        "content": "Sidebar"
      },
      {
        "type": "checkbox",
        "id": "show_sidebar",
        "label": "Show sidebar",
        "info": "Add blocks for sidebar content.",
        "default": false
      },
      {
        "type": "radio",
        "id": "sidebar_position",
        "label": "Sidebar position",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "left"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "post_tags",
        "name": "Blog post tags",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Blog post tags"
          },
          {
            "type": "blog",
            "id": "blog",
            "label": "Blog"
          }
        ]
      },
      {
        "type": "html",
        "name": "Custom HTML",
        "settings": [
          {
            "type": "textarea",
            "id": "html_content",
            "label": "HTML",
            "default": "<div class='container is-flex is-justify-center'><h2 class='title'>Your own custom HTML</h2></div>"
          }
        ]
      },
      {
        "type": "featured_promo",
        "name": "Featured promotion",
        "settings": [
          {
            "type": "color",
            "id": "promo_background",
            "label": "Background",
            "default": "#EEEEEE"
          },
          {
            "type": "color",
            "id": "promo_text",
            "label": "Text",
            "default": "#000000"
          },
          {
            "type": "image_picker",
            "id": "promo_image",
            "label": "Image"
          },
          {
            "type": "richtext",
            "id": "richtext",
            "label": "Text",
            "default": "<p>Use this area for promotional information.</p>"
          },
          {
            "type": "url",
            "id": "promo_link",
            "label": "Link"
          },
          {
            "type": "text",
            "id": "button_label",
            "label": "Button label",
            "default": "Shop now"
          },
          {
            "type": "select",
            "id": "button_style",
            "label": "Button Style",
            "options": [
              {
                "value": "button--primary",
                "label": "Primary"
              },
              {
                "value": "button--secondary",
                "label": "Secondary"
              },
              {
                "value": "button--link-style",
                "label": "Link style"
              }
            ],
            "default": "button--primary"
          }
        ]
      },
      {
        "type": "menu",
        "name": "Menu",
        "settings": [
          {
            "type": "link_list",
            "id": "menu",
            "label": "Menu",
            "info": "This menu won't show drop-down items."
          }
        ]
      },
      {
        "type": "newsletter",
        "name": "Newsletter",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "newsletter_title",
            "label": "Heading",
            "default": "Subscribe"
          },
          {
            "type": "richtext",
            "id": "newsletter_richtext",
            "label": "Text",
            "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
          },
          {
            "type": "checkbox",
            "id": "display_first_name",
            "label": "Show first name"
          },
          {
            "type": "checkbox",
            "id": "display_last_name",
            "label": "Show last name"
          }
        ]
      },
      {
        "type": "page",
        "name": "Page",
        "settings": [
          {
            "type": "page",
            "id": "page",
            "label": "Page"
          }
        ]
      },
      {
        "type": "recent_posts",
        "name": "Recent blog posts",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Recent posts"
          },
          {
            "type": "blog",
            "id": "blog",
            "label": "Blog"
          },
          {
            "type": "range",
            "id": "blog_post_count",
            "label": "Blog posts",
            "min": 2,
            "max": 10,
            "step": 1,
            "default": 10
          }
        ]
      },
      {
        "type": "search",
        "name": "Search form",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Search"
          }
        ]
      },
      {
        "type": "text",
        "name": "Text",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Heading"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Text area can be used for details about blog authors or general information.</p>"
          }
        ]
      }
    ],
    "default": {
      "blocks": [
        {
          "type": "featured_promo"
        },
        {
          "type": "menu"
        },
        {
          "type": "text"
        }
      ]
    }
  }
{% endschema %}
