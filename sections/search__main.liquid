{% comment %}
** Search page and results - main content area
{% endcomment %}

{%- liquid
  assign id = section.id
  # Layout
  assign width = section.settings.width
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign animation = section.settings.animation | default: 'none'
  # Advanced
  assign css_class = section.settings.css_class
  assign custom_css = section.settings.custom_css
-%}

{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
      max-width: 95%;
    {% endif %}
  }

  .sidebar-section {
    {% if section.settings.sidebar_position == 'right' %}
      order: 1;
    {% endif %}
  }

  {% if section.settings.search_breadcrumb == false %}
    .breadcrumb__container {
      display: none;
    }
  {% endif %}

  {% if section.settings.pagination_type != 'basic_pagination' %}
    .breadcrumb__page-count {
      display: none !important;
    }
  {% endif %}

  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

{% comment %} HTML markup {% endcomment %}
<section class="section
        {{ css_class }}
        is-width-{{ width }}"
        {% if animation != "none" %}
          data-scroll-class="{{ animation }}"
        {% endif %}>
  <div class="container breadcrumb__container">
    <div class="one-whole column has-padding-bottom">
      <div class="breadcrumb__wrapper">
        {% render 'breadcrumb', context: 'search' %}
      </div>
    </div>
  </div>

  {% comment %} Search title {% endcomment %}
  <header class="container hide-when-banner-enabled">
    {% capture heading_title %}{{ 'general.search.title' | t }}{% endcapture %}
    {% render 'heading',
            title: heading_title,
            heading_tag: 'h1',
            context: 'search-page',
            text_alignment: 'left'
    %}
  </header>

  {% comment %} Search sidebar and main content area {% endcomment %}
  <div class="container search__content">

    {% if section.blocks.size > 0 and section.settings.show_sidebar %}
      <aside class="sidebar-section
                    one-fourth
                    medium-down--one-whole
                    column
                    has-margin-top">
        {% for block in section.blocks %}
          <div id="shopify-section-{{ block.id }}"
              class="sidebar__block
                    block__{{ block.type | downcase | replace: '_', '-' }}
                    has-padding-top
                    has-padding-bottom
                    {% if settings.toggle_sidebar %}sidebar-toggle-active{% endif %}"
                    {{ block.shopify_attributes }}>

            {% if block.type == 'featured_promo' %}
              {% comment %} Featured promo {% endcomment %}
              {% render 'sidebar__featured-promo',
                      block: block,
                      id: block.id
              %}

            {% elsif block.type == 'menu' %}
              {% comment %} Menu {% endcomment %}
              {% render 'sidebar__menu', menu: block.settings.menu %}

            {% elsif block.type == 'newsletter' %}
              {% comment %} Newsletter {% endcomment %}
              {% render 'sidebar__newsletter', block: block %}

            {% elsif block.type == 'page' %}
              {% comment %} Page {% endcomment %}
              {% render 'sidebar__page', page: block.settings.page %}

            {% elsif block.type == 'text' %}
              {% comment %} Text {% endcomment %}
              {% render 'sidebar__text', block: block %}

            {% endif %}
          </div>
        {% endfor %}
      </aside>
    {% endif %}

    {%- assign search_pagination = section.settings.pagination_limit -%}
    {% paginate search.results by search_pagination %}

      {%- liquid
        assign product_count = 0
        assign page_or_article_count = 0

        for item in search.results
          if item.object_type == 'product'
            assign product_count = product_count | plus: 1
          else
            assign page_or_article_count = page_or_article_count | plus: 1
          endif
        endfor
      -%}

    <main
      class="
        search__main
        {% if section.blocks.size > 0 and section.settings.show_sidebar %}
          three-fourths
          medium-down--one-whole
          column
          has-margin-top
          equal-columns--outside-trim
        {% endif %}
      "
    >
      {% if search.performed %}
        <div class="container">
          <div class="one-whole column">
            <h3 class="search__results-count has-padding-bottom">
              {% if search.results_count == 0 %}
                {{ 'general.search.results_count.zero' | t: count: search.results_count, search_terms: search.terms }}
              {% elsif search.results_count == 1 %}
                {{ 'general.search.results_count.one' | t: count: search.results_count, search_terms: search.terms }}
              {% else %}
                {{ 'general.search.results_count.other' | t: count: search.results_count, search_terms: search.terms }}
              {% endif %}
            </h3>
          </div>
        </div>

        {% if search.results == empty %}
          {% comment %} No results {% endcomment %}
          <div class="search__no-results container">
            <p
              class="
                one-whole
                column
                search__no-results__text
              "
            >
              {{ 'general.search.no_results_html' | t: terms: search.terms }}
            </p>
          </div>
        {% endif %}

        <div class="container">
          <div class="one-whole column has-padding-bottom" data-autocomplete-{{ settings.enable_autocomplete }}>
            <form class="search-form" action="{{ routes.search_url }}">
              <div class="search__fields">
                <label for="q" class="visually-hidden">{{ settings.search_placeholder }}</label>
                <div class="field">
                  <div class="control has-icons-left is-relative">
                    <input class="input" type="text" id="q" name="q" placeholder="{{ settings.search_placeholder }}" value="{{ search.terms }}" x-webkit-speech autocapitalize="off" autocomplete="off" autocorrect="off" data-q/>
                    <button type="submit" name="search" value="submit" class="visually-hidden">
                      {%
                        render 'icon',
                        name: 'search',
                        class: 'icon is-left',
                      %}
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        {% if search.results != empty %}
          {% comment %}Show all search results (including pages){% endcomment %}
          {% if page_or_article_count > 0 %}
            <div class="search__results-list" {% if section.settings.pagination_type != 'basic_pagination' %}data-load-more--grid {% endif %}>
              {% for item in search.results %}
                <div class="search__item container has-padding-bottom" {% if section.settings.pagination_type != 'basic_pagination' %}data-load-more--grid-item{% endif %}>
                  {%- assign featured_image = false -%}

                  {% if item.object_type == 'article' or item.object_type == 'page' %}
                    {%- assign featured_image = true -%}
                    {%- if product_count > 0 -%}
                      <div class="one-fourth column search-result__image-container small-down--one-whole">
                        <a href="{{ item.url }}" title="{{ item.title | escape }}">
                          {%- if item.image.src != blank -%}
                            {%
                              render 'image-element',
                              image: item.image.src,
                              alt: item.image.alt,
                            %}
                          {%- endif -%}
                        </a>
                      </div>
                    {%- endif -%}
                  {% else %}
                    {%- assign featured_image = true -%}
                    <div class="one-fourth column search-result__image-container small-down--one-whole">
                      <a href="{{ item.url }}" title="{{ item.title | escape }}">
                        {%- if item.featured_image != blank -%}
                          {%
                            render 'image-element',
                            image: item.featured_image,
                            alt: item.featured_image.alt,
                          %}
                        {%- else -%}
                          {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                        {%- endif -%}
                      </a>
                    </div>
                  {% endif %}

                  <div class="search-result__description {% if featured_image %}three-fourths{% else %}one-whole{% endif %} small-down--one-whole column">
                    <h3 class="search-result__title">
                      <a href="{{ item.url }}" title="{{ item.title | escape }}">{{ item.title }}</a>
                    </h3>

                    {% if item.price %}
                      <div class="info has-padding-top">
                        <span class="price {% if item.compare_at_price > item.price %}sale{% endif %}">
                          {% if item.available %}
                            {% if item.price_varies and item.price_min > 0 %}
                              <small><em>{{ 'products.general.from' | t }}</em></small>
                            {% endif %}
                            {% if item.price_min > 0 %}
                              <span class="money">
                                {% render 'price-element', price: item.price_min %}
                              </span>
                            {% else %}
                              {{ settings.free_price_text }}
                            {% endif %}
                            {% if item.compare_at_price > item.price %}
                              <span class="compare-at-price">
                                <span class="money">
                                  {%
                                    render 'price-element',
                                    price: item.compare_at_price,
                                  %}
                                </span>
                              </span>
                            {% endif %}
                          {% else %}
                            <span class="sold-out">{{ 'products.product.sold_out' | t }}</span>
                          {% endif %}
                        </span>
                      </div>
                    {% endif %}

                    <div class="has-padding-top">
                      {% if item.object_type == 'article' %}
                        <div class="blog_meta">
                          {% if section.settings.blog_author %}
                            <p>{{ 'blogs.article.by_author' | t: author: item.author }}</p>
                          {% endif %}

                          {% if section.settings.blog_date %}
                            <p>{{ item.published_at | date: format: "month_day_year" }}</p>
                          {% endif %}
                        </div>
                      {% endif %}

                      {% if item.excerpt %}
                        <div class="excerpt">{{ item.excerpt | strip_html | truncate: 500 }}</div>
                      {% else %}
                        <p>
                          {{ item.content | strip_html | truncatewords: 40 | highlight: search.terms | replace: 'Description', '' | replace: 'Specs', '' | replace: 'Shipping', '' | replace: 'Size', '' }}
                        </p>
                      {% endif %}
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
            <div class="container">
              <div class="one-whole column text-align-center has-margin-bottom">
                {% render 'pagination',
                        paginate: paginate,
                        pagination_type: section.settings.pagination_type
                %}
              </div>
            </div>
          {% else %}
            <div class="container" {% if section.settings.pagination_type != 'basic_pagination' %}data-load-more--grid {% endif %}>
              {%- assign products = search.results -%}
              {%- assign products_per_row = section.settings.products_per_row -%}
              {%- assign mobile_products_per_row = section.settings.mobile_products_per_row -%}
              {%- assign limit = section.settings.pagination_limit -%}
              {% render 'product-loop',
                      limit: limit,
                      products: products,
                      products_per_row: products_per_row,
                      mobile_products_per_row: mobile_products_per_row,
                      align_height: section.settings.align_height,
                      height: section.settings.collection_height
              %}
            </div>
            <div class="container">
              <div class="one-whole column text-align-center has-margin-bottom">
                {% render 'pagination', paginate: paginate %}
              </div>
            </div>
          {% endif %}
        {% endif %}

      {% else %}
        {% comment %} Default view (no search results) {% endcomment %}

        <div class="container">
          <div class="one-whole column" data-autocomplete-{{ settings.enable_autocomplete }}>
            <div class="search__text-content content">
              {% if section.settings.text %}
                {{ section.settings.text }}
              {% endif %}
            </div>

            <form class="search-form" action="{{ routes.search_url }}">
              <div class="search__fields">
                <label for="q" class="visually-hidden">{{ settings.search_placeholder }}</label>
                <div class="field">
                  <div class="control has-icons-left is-relative">
                    <input class="input" type="text" name="q" placeholder="{{ settings.search_placeholder }}" value="{{ search.terms }}" x-webkit-speech autocapitalize="off" autocomplete="off" autocorrect="off" data-q/>
                    <button type="submit" name="search" value="submit" class="visually-hidden">
                      {%
                        render 'icon',
                        name: 'search',
                        class: 'icon is-left',
                      %}
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

      {% endif %}

    </main>

    {% comment %} JavaScript {% endcomment %}
    <script
      type="application/json"
      data-section-id="{{ section.id }}"
      data-section-data
    >
      {
        "enable_breadcrumb": {{ section.settings.search_breadcrumb | json }},
        "pagination_type": {{ section.settings.pagination_type | json }},
        "number_of_pages": {{ paginate.pages | json }}
      }
    </script>

    {% endpaginate %}
  </div>
</section>

<script data-theme-editor-load-script src="{{ 'z__jsSidebar.js' | asset_url }}"></script>
<script src="{{ 'z__jsSearch.js' | asset_url }}"></script>

{% schema %}
{
  "name": "Search",
  "class": "search-main has-sidebar-option jsSearch jsSidebar",
  "settings": [
    {
      "type": "checkbox",
      "id": "search_breadcrumb",
      "label": "Show breadcrumbs",
      "default": true
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text",
      "default": "<p>Find what you seek.</p>"
    },
    {
      "type": "header",
      "content": "Sidebar"
    },
    {
      "type": "checkbox",
      "id": "show_sidebar",
      "label": "Show sidebar",
      "info": "Add blocks for sidebar content.",
      "default": false
    },
    {
      "type": "radio",
      "id": "sidebar_position",
      "label": "Sidebar position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "header",
      "content": "Search results"
    },
    {
      "type": "range",
      "id": "products_per_row",
      "label": "Results per row",
      "info": "Used for 'Products only' search results",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 3
    },
    {
      "type": "select",
      "id": "mobile_products_per_row",
      "label": "Results per row on mobile",
      "info": "Used for 'Products only' search results",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "2"
    },
    {
      "type": "range",
      "id": "pagination_limit",
      "label": "Results per page",
      "min": 2,
      "max": 50,
      "step": 1,
      "default": 48
    },
    {
      "type": "select",
      "id": "pagination_type",
      "label": "Pagination type",
      "options": [
        {
          "value": "infinite_scroll",
          "label": "Infinite scroll"
        },
        {
          "value": "infinite_load_more",
          "label": "Infinite load more button"
        },
        {
          "value": "load_more",
          "label": "Load more button"
        },
        {
          "value": "basic_pagination",
          "label": "Page links"
        }
      ],
      "default": "basic_pagination"
    },
    {
      "type": "header",
      "content": "Product thumbnails"
    },
    {
      "type": "checkbox",
      "id": "align_height",
      "label": "Align to height"
    },
    {
      "type": "range",
      "id": "collection_height",
      "label": "Product image height",
      "min": 150,
      "max": 400,
      "step": 10,
      "default": 200,
      "unit": "px",
      "info": "Applied when 'Align to height' is enabled. [Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022543913)"
    },
    {
      "type": "header",
      "content": "Blog posts"
    },
    {
      "type": "checkbox",
      "id": "blog_author",
      "label": "Show author"
    },
    {
      "type": "checkbox",
      "id": "blog_date",
      "label": "Show date",
      "default": true
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "standard",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ]
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ],
  "blocks": [
    {
      "type": "featured_promo",
      "name": "Featured promotion",
      "settings": [
        {
          "type": "color",
          "id": "promo_background",
          "label": "Background",
          "default": "#EEEEEE"
        },
        {
          "type": "color",
          "id": "promo_text",
          "label": "Text",
          "default": "#000000"
        },
        {
          "type": "image_picker",
          "id": "promo_image",
          "label": "Image"
        },
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Text",
          "default": "<p>Use this area for promotional information.</p>"
        },
        {
          "type": "url",
          "id": "promo_link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Shop now"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "value": "button--link-style",
              "label": "Link style"
            }
          ],
          "default": "button--primary"
        }
      ]
    },
    {
      "type": "menu",
      "name": "Menu",
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "info": "This menu won't show drop-down items."
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "newsletter_title",
          "label": "Heading",
          "default": "Subscribe"
        },
        {
          "type": "richtext",
          "id": "newsletter_richtext",
          "label": "Text",
          "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
        },
        {
          "type": "checkbox",
          "id": "display_first_name",
          "label": "Show first name"
        },
        {
          "type": "checkbox",
          "id": "display_last_name",
          "label": "Show last name"
        }
      ]
    },
    {
      "type": "page",
      "name": "Page",
      "settings": [
        {
          "type": "page",
          "id": "page",
          "label": "Page"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Text area can be used for details about blog authors or general information.</p>"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "featured_promo"
      },
      {
        "type": "menu"
      },
      {
        "type": "text"
      }
    ]
  }
}

{% endschema %}
