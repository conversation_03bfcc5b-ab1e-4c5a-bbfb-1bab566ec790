{% comment %}
** Page content **
{% endcomment %}

{% assign id = section.id %}
{% comment %}Layout{% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% assign animation = section.settings.animation | default: 'none' %}
{% comment %}Advanced{% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

<section class="section
        {{ css_class }}
        is-width-{{ width }}"
        {% if animation != "none" %}
          data-scroll-class="{{ animation }}"
        {% endif %}>
  <div class="container">
    {%- if section.settings.show_heading != blank -%}
      {% render 'heading',
              title: page.title,
              heading_tag: 'h1',
              context: 'page',
              text_alignment: section.settings.heading_alignment
      %}
    {%- endif -%}
    <div class="page__content content container {% if settings.table_styles_enabled %} table is-striped is-bordered {% endif %}">
      {% render 'page-multi-column', content: page.content %}
    </div>
  </div>
</section>

{% schema %}
  {
    "name": "Page content",
    "class": "page-main",
    "templates": ["page"],
    "settings": [
      {
        "type": "checkbox",
        "id": "show_heading",
        "label": "Show page title",
        "default": true
      },
      {
        "type": "select",
        "id": "heading_alignment",
        "label": "Heading alignment",
        "default": "left",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          }
        ]
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced"
      },
      {
        "type": "paragraph",
        "content": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ]
  }
{% endschema %}
