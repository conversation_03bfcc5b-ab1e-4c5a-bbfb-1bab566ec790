{% comment %}
** Footer - centered - static **
{% endcomment %}

{% liquid
  assign id = section.id

  assign css_class = section.settings.css_class
  assign custom_css = section.settings.custom_css

  assign locale_selector = false
  assign currency_selector = false
  assign social_icons = false

  if section.settings.show_currency_selector and shop.enabled_currencies.size > 1 or localization.available_countries.size > 1
    assign currency_selector = true
  endif

  if section.settings.show_locale_selector and shop.published_locales.size > 1
    assign locale_selector = true
  endif
%}

{% style %}
  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

{% comment %} HTML markup {% endcomment %}
<footer class="footer__container {{ css_class }}">
  <section class="footer__content
                  section
                  is-width-wide
                  has-background
                  has-padding-top
                  has-padding-bottom">
    <div class="container is-flex-column is-align-center has-limit">
      {% for block in section.blocks %}
        <div id="shopify-section-{{ block.id }}" class=" footer__block block__{{ block.id }} block__{{ block.type | downcase | replace: '_', '-' }}
                    has-padding-bottom
                    text-align-center"
                    {{ block.shopify_attributes }}>
          {% if block.type == 'logo' %}
            {% comment %} Logo {% endcomment %}
            <a class="footer__logo-wrapper is-{{ block.settings.size }}" href="{{ routes.root_url }}" title="{{ shop.name }}">
              {% if block.settings.logo %}
                  {%
                    render 'image-element',
                    image: block.settings.logo,
                    alt: block.settings.logo.alt,
                    additional_classes: 'footer__logo',
                    focal_point: block.settings.logo.presentation.focal_point,
                  %}
              {% else %}
                <h2 class="footer__heading text-align-center">{{ shop.name }}</h2>
              {% endif %}
            </a>
          {% elsif block.type == 'link_list' %}
            {% comment %} Menu {% endcomment %}
            {% render 'footer__menu', menu_link: block.settings.menu %}

          {% elsif block.type == 'newsletter' %}
            {% comment %} Newsletter {% endcomment %}
            {% if block.settings.newsletter_title != blank %}
              <h3 class="footer__heading">
                {{ block.settings.newsletter_title }}
              </h3>
            {% endif %}

            {% if block.settings.newsletter_richtext != blank %}
              <div class="content">
                {{ block.settings.newsletter_richtext }}
              </div>
            {% endif %}

            {%
              render 'newsletter-form',
              type: 'block',
              display_first_name: block.settings.display_first_name,
              display_last_name: block.settings.display_last_name,
              id: 'footer',
            %}
          {% elsif block.type == 'html' %}

            {{ block.settings.html_content }}

          {% elsif block.type == 'text' %}
            {% comment %} Text {% endcomment %}
              <h3 class="footer__heading">{{ block.settings.title }}</h3>
              {{ block.settings.content }}

          {% elsif block.type == 'social' %}
            {% comment %} Social and Follow on Shop {% endcomment %}
            {% render 'social-icons' %}
            {%- if shop.features.follow_on_shop? and section.settings.show_follow_on_shop -%}
              <div class="footer__follow-on-shop one-whole column">
                {{ shop | login_button: action: 'follow' }}
              </div>
            {%- endif -%}
            {% assign social_icons = true %}

          {% endif %}
        </div>
      {% endfor %}
    </div>
  </section>
  <section class="footer__extra-content section">
    <div class="sub-footer container has-limit text-align-center has-padding-top has-padding-bottom">
      {% if locale_selector or currency_selector or settings.show_multiple_currencies %}
        <div class="footer-menu__disclosure footer-centered__disclosure">
          {% render 'localization-switcher',
            additional_classes: 'footer-menu__disclosure is-hidden-mobile-only',
            id: 'footer__selector-form',
            currency_selector: currency_selector,
            locale_selector: locale_selector,
            show_currency: section.settings.show_currency_selector
          %}
          {%
            render 'localization-switcher',
            id: 'footer__selector-form--mobile',
            additional_classes: 'selectors-form--mobile is-hidden-desktop-only',
            currency_selector: currency_selector,
            locale_selector: locale_selector,
            show_currency: settings.show_currency_selector
          %}
        </div>
      {% endif %}

      {%- if social_icons == false and shop.features.follow_on_shop? and section.settings.show_follow_on_shop -%}
        <div class="footer__follow-on-shop one-whole column">
          {{ shop | login_button: action: 'follow' }}
        </div>
      {%- endif -%}

      <div class="footer__credits one-whole column">
        <p>&copy; {{ "now" | date: "%Y" }} {{ shop.name | link_to: routes.root_url }}.</p>
        {{ section.settings.copyright_text }}
        {% if section.settings.display_designed_by %}
          <p>{{ 'layout.general.designer_credits_html' | t }}</p>
        {% endif %}
        {% if section.settings.display_shopify %}
          <p>{{ powered_by_link }}</p>
        {% endif %}
      </div>

      {% if section.settings.display_payment_methods %}
        <div class="footer__payment-methods payment-methods one-whole column">
          {% for type in shop.enabled_payment_types %}
            {{ type | payment_type_svg_tag: class:'payment-icon' }}
          {% endfor %}
        </div>
      {% endif %}
      </div>
    </div>
  </section>
  {% if shop.metafields.cuddleclones.footer_script.value %}
  {{ shop.metafields.cuddleclones.footer_script.value }}
{% endif %}
</footer>

{% schema %}
  {
    "name": "Footer",
    "class": "footer footer--centered",
    "max_blocks": 5,
    "settings": [
      {
        "type": "richtext",
        "id": "copyright_text",
        "label": "Copyright text"
      },
      {
        "type": "checkbox",
        "id": "display_designed_by",
        "label": "Show theme designer credits",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "display_shopify",
        "label": "Show Powered by Shopify",
        "default": true
      },
      {
        "type": "header",
        "content": "Follow on Shop",
        "info": "Display follow button for your storefront on the Shop app. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/follow-on-shop)"
      },
      {
        "type": "checkbox",
        "id": "show_follow_on_shop",
        "label": "Enable Follow on Shop",
        "default": true
      },
      {
        "type": "header",
        "content": "Language selector",
        "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
      },
      {
        "type": "checkbox",
        "id": "show_locale_selector",
        "label": "Enable language selector",
        "default": true
      },
      {
        "type": "header",
        "content": "Country selector",
        "info": "To add a country, go to your [payment settings.](/admin/settings/payments)"
      },
      {
        "type": "checkbox",
        "id": "show_currency_selector",
        "label": "Enable country selector",
        "default": true
      },
      {
        "type": "header",
        "content": "Payment icons"
      },
      {
        "type": "checkbox",
        "id": "display_payment_methods",
        "label": "Show payment method icons",
        "default": true
      },
      {
        "type": "header",
        "content": "Advanced"
      },
      {
        "type": "paragraph",
        "content": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "html",
        "name": "Custom HTML",
        "settings": [
          {
            "type": "textarea",
            "id": "html_content",
            "label": "HTML",
            "default": "<h2 class='footer__heading'>Your own custom HTML</h2>"
          }
        ]
      },
      {
        "type": "logo",
        "name": "Logo",
        "settings": [
          {
            "type": "image_picker",
            "id": "logo",
            "label": "Logo",
            "info": "500 x 200px recommended"
          },
          {
            "type": "select",
            "id": "size",
            "label": "Size",
            "default": "small",
            "options": [
              {
                "value": "small",
                "label": "Small"
              },
              {
                "value": "medium",
                "label": "Medium"
              },
              {
                "value": "large",
                "label": "Large"
              }
            ]
          }
        ]
      },
      {
        "type": "link_list",
        "name": "Menu",
        "settings": [
          {
            "type": "link_list",
            "id": "menu",
            "label": "Menu",
            "info": "This menu won't show drop-down items.",
            "default": "footer"
          }
        ]
      },
      {
        "type": "newsletter",
        "name": "Newsletter",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "newsletter_title",
            "label": "Heading",
            "default": "Subscribe"
          },
          {
            "type": "richtext",
            "id": "newsletter_richtext",
            "label": "Text",
            "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
          },
          {
            "type": "checkbox",
            "id": "display_first_name",
            "label": "Show first name"
          },
          {
            "type": "checkbox",
            "id": "display_last_name",
            "label": "Show last name"
          }
        ]
      },
      {
        "type": "text",
        "name": "Text",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Title"
          },
          {
            "type": "richtext",
            "id": "content",
            "label": "Text",
            "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
          }
        ]
      },
      {
        "type": "social",
        "name": "Social icons",
        "limit": 1,
        "settings": [
        ]
      }
    ]
  }
{% endschema %}
