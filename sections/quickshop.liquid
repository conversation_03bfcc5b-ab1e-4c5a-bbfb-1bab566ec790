<section class="quickshop-container" data-html="content">
  {% style %}
    {%
      render 'css-loop',
      css: section.settings.custom_css,
      id: section.id,
    %}
  {% endstyle %}

  {% liquid
    assign selected_variant = product.selected_variant

    if product.variants.size == 1 or settings.select_first_available_variant
      assign selected_variant = product.selected_or_first_available_variant
    endif
  %}

  <div class="product-{{ product.id }} {{ section.settings.css_class }}">
    {%
      render 'product',
      product: product,
      is_product_modal: true,
      sold_out_options: settings.sold_out_options,
      selected_variant: selected_variant,
      width: width,
      css_class: css_class,
      display_thumbnails: section.settings.display_thumbnails,
      enable_product_lightbox: section.settings.enable_product_lightbox,
      enable_shopify_product_badges: section.settings.enable_shopify_product_badges,
      enable_thumbnail_slider: section.settings.enable_thumbnail_slider,
      enable_zoom: section.settings.enable_zoom,
      gallery_arrows: section.settings.gallery_arrows,
      product_height: section.settings.product_height,
      product_images_position: section.settings.product_images_position,
      set_product_height: section.settings.set_product_height,
      slideshow_transition: section.settings.slideshow_transition,
      stickers_enabled: settings.stickers_enabled,
      tag_style: settings.tag_style,
      thumbnail_position: section.settings.thumbnail_position,
      video_looping: section.settings.video_looping,
    %}
  </div>

  {%- for block in section.blocks -%}
    {%- if block.type == 'price' -%}
      {%- assign display_savings = block.settings.display_savings | json -%}
    {%- endif -%}
  {%- endfor -%}

  {% comment %} JavaScript {% endcomment %}
  <script
    type="application/json"
    data-section-id="{{ section.id }}"
    data-section-data
  >
    {
      "display_savings": {{ display_savings }},
      "gallery_arrows": {{ section.settings.gallery_arrows | json }},
      "thumbnail_arrows": {{ section.settings.gallery_arrows | json }},
      "enable_zoom": {{ section.settings.enable_zoom | json }},
      "enable_product_lightbox": {{ section.settings.enable_product_lightbox | json }},
      "enable_thumbnail_slider": {{ section.settings.enable_thumbnail_slider | json }},
      "slideshow_speed": {{ section.settings.slideshow_speed | json }},
      "slideshow_transition": {{ section.settings.slideshow_transition | json }},
      "thumbnails_enabled": {{ section.settings.display_thumbnails | json }},
      "thumbnail_position": {{ section.settings.thumbnail_position | json }},
      "product_media_amount": {{ product.media.size }},
      "template": "classic"
    }
  </script>
</section>

<script src="{{ 'z__jsProduct.js' | asset_url }}"></script>

{% comment %} Shopify-XR {% endcomment %}
{% if product.media %}
  <script>
    window.ShopifyXR=window.ShopifyXR||function(){(ShopifyXR.q=ShopifyXR.q||[]).push(arguments)}
      {% assign models = product.media | where: 'media_type', 'model' | json %}
      ShopifyXR('addModels', {{ models }});
  </script>
{% endif %}

{% schema %}

{
  "name": "Product",
  "class": "quickshop-template jsProduct",
  "settings": [
    {
      "type": "header",
      "content": "Media",
      "info": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "radio",
      "id": "product_images_position",
      "label": "Media position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "checkbox",
      "id": "set_product_height",
      "label": "Set height of product media",
      "default": false
    },
    {
      "type": "range",
      "id": "product_height",
      "label": "Product media height",
      "min": 200,
      "max": 800,
      "step": 10,
      "default": 500,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "video_looping",
      "label": "Enable video looping",
      "default": false
    },
    {
      "type": "header",
      "content": "Product gallery"
    },
    {
      "type": "checkbox",
      "id": "gallery_arrows",
      "label": "Show arrows",
      "info": "Only applies to desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_zoom",
      "label": "Magnify on hover",
      "default": true
    },
    {
      "type": "range",
      "id": "slideshow_speed",
      "label": "Gallery speed",
      "min": 0,
      "max": 6,
      "unit": "sec",
      "default": 0,
      "info": "Set to 0 to disable autoplay."
    },
    {
      "type": "select",
      "id": "slideshow_transition",
      "label": "Gallery transition",
      "options": [
        {
          "value": "slide",
          "label": "Slide"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ],
      "default": "slide"
    },
    {
      "type": "checkbox",
      "id": "display_thumbnails",
      "label": "Show thumbnails",
      "default": true
    },
    {
      "type": "select",
      "id": "thumbnail_position",
      "label": "Thumbnails position",
      "options": [
        {
          "value": "left-thumbnails",
          "label": "Left of main image"
        },
        {
          "value": "right-thumbnails",
          "label": "Right of main image"
        },
        {
          "value": "bottom-thumbnails",
          "label": "Below main image"
        }
      ],
      "default": "bottom-thumbnails"
    },
    {
      "type": "checkbox",
      "id": "enable_thumbnail_slider",
      "label": "Enable thumbnail slider",
      "default": true
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "title",
      "name": "Title",
      "limit": 1
    },
    {
      "type": "vendor",
      "name": "Vendor",
      "limit": 1
    },
    {
      "type": "price",
      "name": "Price",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "display_savings",
          "label": "Show price savings",
          "default": true
        }
      ]
    },
    {
      "type": "rating",
      "name": "Product rating",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To display a rating, add a product rating app. [Learn more](https://apps.shopify.com/product-reviews)"
        }
      ]
    },
    {
      "type": "sku",
      "name": "SKU",
      "limit": 1
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Text block</p>"
        }
      ]
    },
    {
      "type": "description",
      "name": "Description",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "truncate_words",
          "label": "Truncate description",
          "info": "Removes HTML when enabled",
          "default": false
        },
        {
          "type": "range",
          "id": "truncate_words_limit",
          "label": "Description truncated words",
          "min": 1,
          "max": 50,
          "default": 20
        }
      ]
    },
    {
      "type": "form",
      "name": "Form",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Dynamic Checkout Button"
        },
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https:\/\/help.shopify.com\/manual\/using-themes\/change-the-layout\/dynamic-checkout)",
          "default": true
        },
        {
          "type": "paragraph",
          "content": "Customize additional form features for the product in Theme settings > Product form."
        }
      ]
    },
    {
      "type": "product-links",
      "name": "Product links",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_collections",
          "label": "Show collections",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_types",
          "label": "Show types",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_tags",
          "label": "Show tags",
          "default": true
        }
      ]
    },
    {
      "type": "share",
      "name": "Share",
      "limit": 1
    },
    {
      "type": "size-chart",
      "name": "Size chart",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Label",
          "default": "Size chart"
        },
        {
          "type": "page",
          "id": "size_chart",
          "label": "Size chart",
          "info": "[Learn more](https:\/\/help.outofthesandbox.com\/hc\/en-us\/articles\/115006910707-Using-the-Size-Chart-Sections-themes-)"
        }
      ]
    }
  ],
  "default": {
    "settings": {

    }
  }
}

{% endschema %}
