{%- comment -%}
  Updated: 01/25/2024
  This file is system-generated and should not be modified. We reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost
{%- endcomment -%}{% assign contentprt = 'header' %}{% if zp_override_template_name != blank %}{% assign zp_entity_marker = zp_override_template_name %}{% else %}{% assign zp_entity_marker = template.name %}{% endif %}{% if zp_override_template_suffix != blank %}{% assign zp_template_suffix = zp_override_template_suffix %}{% else %}{% assign zp_template_suffix = template.suffix %}{% endif %}{% assign zp_product_template_suffix = zp_template_suffix | remove: '-' %}{% assign zp_shop_dir_name = shop.permanent_domain | sha1 %}{% assign zp_collect_entity_content_parts = false %}{% assign zp_split_tests_available = false %}{% assign zp_use_json_entity_settings = false %}{% assign zp_use_only_custom_template = false %}{% assign zp_shop_metafields_loaded = false %}{% assign zp_current_entity_metafields_loaded = false %}{% assign zp_use_product_markup = false %}{% assign zp_use_current_entity_markup = false %}{% assign zp_use_native_entity_description = false %}{% assign zp_use_native_og_images = false %}{% assign zp_og_entity_type = 'website' %}{% assign zp_is_product_page = false %}{% assign zp_current_entity_index_state = nil %}{% if zp_template_suffix contains 'zipifypages' %}{% assign zp_enable_content_parsing = true %}{% else %}{% assign zp_enable_content_parsing = false %}{% endif %}{% if zp_enable_content_parsing and zp_entity_marker == 'article' %}{% assign zp_current_entity = article %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'aac' %}{% assign zp_scripts_metafield_key = 'articlescripts' %}{% assign zp_entity_data_metafield_key = 'articledata' %}{% assign zp_entity_styles_folder = 'articles' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_use_product_markup = true %}{% elsif zp_enable_content_parsing and zp_entity_marker == 'blog' %}{% assign zp_current_entity = blog %}{% assign zp_current_entity_content = '' | append: zp_current_entity.metafields['zipifypagesblogparts']['blogheaderfooter'] %}{% assign zp_entity_attributes_class = 'bac' %}{% assign zp_scripts_metafield_key = 'blogscripts' %}{% assign zp_entity_data_metafield_key = 'blogdata' %}{% assign zp_entity_styles_folder = 'blogs' %}{% assign zp_use_product_markup = true %}{% elsif zp_enable_content_parsing and zp_entity_marker == 'page' %}{% assign zp_current_entity = page %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'pac' %}{% assign zp_scripts_metafield_key = 'pagescripts' %}{% assign zp_entity_data_metafield_key = 'pagedata' %}{% assign zp_entity_styles_folder = 'pages' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_split_tests_available = true %}{% assign zp_use_product_markup = true %}{% elsif zp_entity_marker == 'index' %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% assign zp_shop_metafields_loaded = true %}{% assign zp_index_page = zp_shop_metafields['indexpage'] %}{% assign zp_index_page_handle = zp_shop_metafields['indexpagehandle'] %}{% if zp_index_page.type == 'page_reference' %}{% assign zp_current_entity = zp_index_page.value %}{% elsif zp_index_page_handle.size > 0 %}{% assign zp_current_entity = pages[zp_index_page_handle] %}{% else %}{% assign zp_current_entity = nil %}{% endif %}{% assign zp_current_entity_id_size = '' | append: zp_current_entity.id | size %}{% if zp_current_entity_id_size > 0 %}{% assign zp_entity_marker = 'page' %}{% assign zp_enable_content_parsing = true %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'pac' %}{% assign zp_scripts_metafield_key = 'pagescripts' %}{% assign zp_entity_data_metafield_key = 'pagedata' %}{% assign zp_entity_styles_folder = 'pages' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_split_tests_available = true %}{% assign zp_use_product_markup = true %}{% else %}{% assign zp_current_entity = nil %}{% assign zp_enable_content_parsing = false %}{% endif %}{% elsif zp_entity_marker == 'product' %}{% assign zp_current_entity = product %}{% assign zp_entity_attributes_class = 'ppac' %}{% assign zp_scripts_metafield_key = 'productpagescripts' %}{% assign zp_entity_data_metafield_key = 'productpagedata' %}{% assign zp_entity_styles_folder = 'product_pages' %}{% assign zp_use_viewport_meta = false %}{% assign zp_use_json_entity_settings = true %}{% assign zp_is_product_page = true %}{% if zp_template_suffix contains 'zipifypages-' %}{% assign zp_current_entity_metafields = shop.metafields[zp_product_template_suffix] %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_index_state = zp_current_entity_metafields['config'].value['page_index_state'] %}{% else %}{% assign zp_current_entity_index_state = zp_current_entity_metafields['config']['page_index_state'] %}{% endif %}{% if zp_use_meta_tags == true %}{% assign zp_use_meta_tags = true %}{% elsif zp_use_meta_tags == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_meta_tags = true %}{% else %}{% assign zp_use_meta_tags = false %}{% endif %}{% if zp_use_open_graph_tags == true %}{% assign zp_use_open_graph_tags = true %}{% elsif zp_use_open_graph_tags == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_open_graph_tags = true %}{% else %}{% assign zp_use_open_graph_tags = false %}{% endif %}{% if zp_use_favicon == true %}{% assign zp_use_favicon = true %}{% elsif zp_use_favicon == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_favicon = true %}{% else %}{% assign zp_use_favicon = false %}{% endif %}{% assign zp_display_content_for_header = false %}{% assign zp_use_native_entity_description = true %}{% assign zp_use_native_og_images = true %}{% assign zp_og_entity_type = 'product' %}{% assign zp_use_current_entity_markup = true %}{% assign zp_product_page_with_zp_layout = true %}{% else %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% assign zp_use_meta_tags = false %}{% assign zp_use_open_graph_tags = false %}{% assign zp_use_favicon = false %}{% assign zp_display_content_for_header = false %}{% assign zp_use_only_custom_template = true %}{% assign zp_product_page_with_zp_layout = false %}{% endif %}{% assign zp_current_entity_metafields_loaded = true %}{% assign zp_current_entity_data = zp_current_entity_metafields['config'] | default: zp_current_entity_metafields[zp_entity_data_metafield_key] | default: 'noassigneddata' %}{% if zp_current_entity_data == 'noassigneddata' %}{% assign zp_enable_content_parsing = false %}{% else %}{% assign zp_enable_content_parsing = true %}{% assign zp_split_tests_available = true %}{% endif %}{% assign zp_current_entity_data = nil %}{% else %}{% assign zp_current_entity = nil %}{% assign zp_current_entity_content = '' %}{% assign zp_use_meta_tags = false %}{% assign zp_use_open_graph_tags = false %}{% assign zp_use_viewport_meta = false %}{% assign zp_use_favicon = false %}{% assign zp_display_content_for_header = false %}{% endif %}{% if zp_enable_content_parsing %}{% unless zp_shop_metafields_loaded %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% endunless %}{% unless zp_current_entity_metafields_loaded %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% endunless %}{% if zp_current_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% if zp_use_only_custom_template %}{% assign zp_entity_custom_template = true %}{% elsif zp_current_entity.template_suffix == 'custom.zipifypages' %}{% assign zp_entity_custom_template = true %}{% elsif zp_current_entity_index_state == 'custom' %}{% assign zp_entity_custom_template = true %}{% else %}{% assign zp_entity_custom_template = false %}{% endif %}{% endif %}{% if zp_collect_entity_content_parts and zp_current_entity_content contains ':|zpendofcontent|:' %}{% assign zp_entity_with_multiparts = true %}{% else %}{% assign zp_entity_with_multiparts = false %}{% endif %}{% assign zp_gdpr_enabled = false %}{% assign zp_alternative_entity_present = false %}{% assign zp_split_single_page_render = false %}{% assign zp_split_test_redirect = false %}{% assign zp_split_test_view_type_redirect = false %}{% if zp_entity_with_multiparts %}{% assign zp_current_entity_content = '' | append: zp_current_entity_content | split: ':|zpendofcontent|:' | first %}{% endif %}{% if zp_enable_content_parsing and zp_split_tests_available %}{% if zp_product_page_with_zp_layout %}{% assign zp_current_entity_object_metafields = zp_current_entity_main_metafields %}{% else %}{% assign zp_current_entity_object_metafields = zp_current_entity_metafields %}{% endif %}{% if zp_current_entity_object_metafields['splittest'] != blank %}{% if zp_current_entity_object_metafields['splittest'].type == 'json' %}{% assign zp_split_test_data = zp_current_entity_object_metafields['splittest'].value %}{% assign zp_split_test_type = zp_split_test_data.type %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data.alternative_handle %}{% assign zp_split_dataset = zp_split_test_data.dataset | json %}{% assign zp_split_dataset = '' | append: zp_split_dataset %}{% assign zp_split_single_page_render = '' | append: zp_split_test_data.single_page_render %}{% assign zp_split_token = zp_split_test_data.token%}{% else %}{% assign zp_split_test_data = '' | append: zp_current_entity_object_metafields['splittest'] | split: ':|~|:' %}{% assign zp_split_test_type = zp_split_test_data[0] %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data[1] %}{% assign zp_split_dataset = zp_split_test_data[2] %}{% assign zp_split_single_page_render = '' | append: zp_split_test_data[3] %}{% assign zp_split_token = '' | append: zp_split_test_data[4] %}{% endif %}{% if zp_split_test_type == 'mainlayout' %}{% if zp_entity_marker == 'article' %}{% assign zp_alternative_entity = articles[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.content %}{% elsif zp_entity_marker == 'blog' %}{% assign zp_alternative_entity = blogs[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.metafields['zipifypagesblogparts']['blogheaderfooter'] %}{% elsif zp_entity_marker == 'page' %}{% assign zp_alternative_entity = pages[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.content %}{% else %}{% assign zp_alternative_entity = nil %}{% assign zp_alternative_entity_content = '' %}{% endif %}{% assign zp_alternative_entity_id_size = '' | append: zp_alternative_entity.id | size %}{% if zp_alternative_entity_id_size > 0 %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity_content | split: ':|zpendofcontent|:' | first %}{% assign zp_alternative_entity_present = true %}{% if zp_split_single_page_render == 'true' %}{% assign zp_split_single_page_render = true %}{% else %}{% assign zp_split_single_page_render = false %}{% endif %}{% assign zp_alternative_entity_title = '' | append: zp_alternative_entity.title %}{% assign zp_alternative_entity_metafields = zp_alternative_entity.metafields['zipifypages'] %}{% if zp_alternative_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_alternative_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_alternative_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% assign zp_entity_scripts = '' | append: zp_alternative_entity_metafields[zp_scripts_metafield_key] | split: '|;|~|;|' %}{% assign zp_alternative_header_scripts = '' | append: zp_entity_scripts[0] | strip %}{% assign zp_entity_scripts = '' %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_alternative_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_alternative_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data contains '"gdpr_enabled":true' %}{% assign zp_gdpr_enabled = true %}{% elsif zp_blocks_data contains '"gdpr_enabled":false' %}{% assign zp_gdpr_enabled = false %}{% elsif zp_blocks_data contains '"gdpr_status":"enable_with_checkbox"' or zp_blocks_data contains '"gdpr_status":"enable_without_checkbox"' %}{% assign zp_gdpr_enabled = true %}{% endif %}{% endif %}{% assign zp_alternative_entity_content_wrapper_class = "zp " | append: zp_entity_attributes_class | append: '-' | append: zp_alternative_entity.id %}{% if zp_alternative_entity.template_suffix == 'custom.zipifypages' %}{% assign zp_alternative_entity_custom_template = true %}{% else %}{% assign zp_alternative_entity_custom_template = false %}{% endif %}{% elsif zp_split_test_type == 'redirect' or zp_split_test_type == 'redirecttarget' %}{% assign zp_split_test_redirect = true %}{% elsif zp_split_test_type == 'viewtyperedirect' %}{% assign zp_split_test_view_type_redirect = true %}{% endif %}{% endif %}{% endif %}{% if zp_enable_content_parsing %}{% assign zp_entity_title = page_title | remove: ':|~|:' | remove: ':--:' | append: entity_title_suffix | strip %}{% if zp_gdpr_enabled == false %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_current_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_current_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data contains '"gdpr_enabled":true' %}{% assign zp_gdpr_enabled = true %}{% elsif zp_blocks_data contains '"gdpr_enabled":false' %}{% assign zp_gdpr_enabled = false %}{% elsif zp_blocks_data contains '"gdpr_status":"enable_with_checkbox"' or zp_blocks_data contains '"gdpr_status":"enable_without_checkbox"' %}{% assign zp_gdpr_enabled = true %}{% endif %}{% endif %}{% assign zp_blocks_data = '' %}{% endif %}{% if zp_enable_content_parsing %}<svg style="display:none;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"> <defs> <symbol id="btn-close" viewBox="0 0 48 48"> <path d="M38 12.83L35.17 10 24 21.17 12.83 10 10 12.83 21.17 24 10 35.17 12.83 38 24 26.83 35.17 38 38 35.17 26.83 24z"/> <path d="M0 0h48v48H0z" fill="none"/> </symbol> <symbol id="minusIcon" viewBox="0 0 12 12"> <path fill="currentColor" d="M11.14 4.8H.86c-.48 0-.86.36-.86.8v.8c0 .**********.8h10.28c.48 0 .86-.36.86-.8v-.8c0-.44-.38-.8-.86-.8z"/> </symbol> <symbol id="plusIcon" viewBox="0 0 12 12"> <path fill="currentColor" d="M11.1 4.7H7.3V.9c0-.5-.4-.9-.9-.9h-.8c-.5 0-.9.4-.9.9v3.9H.9c-.5-.1-.9.3-.9.8v.9c0 .5.4.9.9.9h3.9v3.9c0 .5.4.9.9.9h.9c.5 0 .9-.4.9-.9v-4h3.9c.5 0 .9-.4.9-.9v-.8c-.3-.5-.7-.9-1.2-.9z"/> </symbol> <symbol id="arrowIcon" viewBox="0 0 15 9"> <path fill="currentColor" transform="matrix(0 1 1 0 3.13 -3.13)" d="M10.75 11.88a1.1 1.1 0 0 1-.8-.35L3.13 4.37l6.82-7.15a1.1 1.1 0 0 1 1.6 0c.44.46.44 1.21 0 1.68L6.31 4.37l5.22 5.48c.45.47.43 1.17 0 1.68-.13.15-.4.27-.8.35z"/> </symbol> <symbol id="quote-mark-left" viewBox="0 0 123.961 123.961"> <path d="M49.8 29.032c3.1-1.3 4.4-5 3-8l-4.9-10.3c-1.4-2.899-4.8-4.2-7.8-2.899-8.5 3.6-15.8 8.3-21.6 14C11.4 28.532 6.6 36.232 4 44.732c-2.6 8.601-4 20.3-4 35.2v30.7c0 3.3 2.7 6 6 6h39.3c3.3 0 6-2.7 6-6v-39.3c0-3.301-2.7-6-6-6H26.5c.2-10.101 2.6-18.2 7-24.301 3.6-4.898 9-8.898 16.3-11.999zM120.4 29.032c3.1-1.3 4.399-5 3-8l-4.9-10.199c-1.4-2.9-4.8-4.2-7.8-2.9-8.4 3.6-15.601 8.3-21.5 13.9-7.101 6.8-12 14.5-14.601 23-2.6 8.399-3.899 20.1-3.899 35.1v30.7c0 3.3 2.7 6 6 6H116c3.3 0 6-2.7 6-6v-39.3c0-3.301-2.7-6-6-6H97.1c.2-10.101 2.601-18.2 7-24.301 3.6-4.899 9-8.899 16.3-12z"/> </symbol> <symbol id="quote-mark-right" viewBox="0 0 975.036 975.036"> <path d="M925.036 57.197h-304c-27.6 0-50 22.4-50 50v304c0 27.601 22.4 50 50 50h145.5c-1.9 79.601-20.4 143.3-55.4 191.2-27.6 37.8-69.399 69.1-125.3 93.8-25.7 11.3-36.8 41.7-24.8 67.101l36 76c11.6 24.399 40.3 35.1 65.1 24.399 66.2-28.6 122.101-64.8 167.7-108.8 55.601-53.7 93.7-114.3 114.3-181.9 20.601-67.6 30.9-159.8 30.9-276.8v-239c0-27.599-22.401-50-50-50zM106.036 913.497c65.4-28.5 121-64.699 166.9-108.6 56.1-53.7 94.4-114.1 115-181.2 20.6-67.1 30.899-159.6 30.899-277.5v-239c0-27.6-22.399-50-50-50h-304c-27.6 0-50 22.4-50 50v304c0 27.601 22.4 50 50 50h145.5c-1.9 79.601-20.4 143.3-55.4 191.2-27.6 37.8-69.4 69.1-125.3 93.8-25.7 11.3-36.8 41.7-24.8 67.101l35.9 75.8c11.601 24.399 40.501 35.2 65.301 24.399z"/> </symbol> <symbol id="small-shopify-cart" viewBox="0 0 510 510"> <path d="M153,408c-28.05,0-51,22.95-51,51s22.95,51,51,51s51-22.95,51-51S181.05,408,153,408z M0,0v51h51l91.8,193.8L107.1,306 c-2.55,7.65-5.1,17.85-5.1,25.5c0,28.05,22.95,51,51,51h306v-51H163.2c-2.55,0-5.1-2.55-5.1-5.1v-2.551l22.95-43.35h188.7 c20.4,0,35.7-10.2,43.35-25.5L504.9,89.25c5.1-5.1,5.1-7.65,5.1-12.75c0-15.3-10.2-25.5-25.5-25.5H107.1L84.15,0H0z M408,408 c-28.05,0-51,22.95-51,51s22.95,51,51,51s51-22.95,51-51S436.05,408,408,408z" fill="#888888"/> </symbol> <symbol id="shopify-cart-2" viewBox="0 0 25 25"> <path d="M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"/> <path d="M0 0h24v24H0z" fill="none"/> </symbol> <symbol id="navbarIconClose" viewBox="0 0 17 17"> <path d="M10.6 8.5l5.9-5.9c.6-.6.6-1.5 0-2.1-.6-.6-1.5-.6-2.1 0L8.5 6.4 2.6.4C2-.1 1-.1.4.4-.1 1-.1 2 .4 2.6l5.9 5.9-5.9 5.9c-.6.6-.6 1.5 0 2.1.6.6 1.5.6 2.1 0l5.9-5.9 5.9 5.9c.6.6 1.5.6 2.1 0 .6-.6.6-1.5 0-2.1l-5.8-5.9z"/> </symbol> <symbol id="navbar-icon" viewBox="0 0 20 18"> <path d="M.7 17.9h18.6c.4 0 .7-.3.7-.7v-1.8c0-.4-.3-.7-.7-.7H.7c-.4 0-.7.3-.7.7v1.8c0 .4.3.7.7.7M.7 10.5h18.6c.4 0 .7-.3.7-.7V8.1c0-.4-.3-.7-.7-.7H.7c-.4 0-.7.3-.7.7v1.8c0 .3.3.6.7.6M.7 3.2h18.6c.4 0 .7-.3.7-.7V.7c0-.4-.3-.7-.7-.7H.7C.3 0 0 .3 0 .7v1.8c0 .3.3.7.7.7"/> </symbol> <symbol id="shopify-cart" viewBox="0 0 20 20"> <path d="M17.6 16.1c0-.1-.1-.2-.1-.2l.2-1c.1-.6-.3-1.2-.8-1.2H7.6l-.2-1.3h10.2c.4 0 .7-.3.8-.7l1.6-8c.1-.6-.3-1.2-.8-1.2H5.5L5.2.8C5.1.3 4.8 0 4.4 0H.8C.4 0 0 .4 0 1v.6c0 .5.4.9.8.9h2.4L5.7 16c-.8.3-1.3 1.1-1.3 1.9 0 1.2.9 2.1 2.1 2.1h.2c1.1-.1 1.9-1 1.9-2.1 0-.7-.3-1.2-.8-1.6h7.5c-.3.3-.6.6-.7 1-.1.2-.1.4-.1.6 0 1.1.8 2 1.9 2.1h.4c1.1-.1 1.9-1 1.9-2.1 0-.8-.5-1.5-1.1-1.8z"/> </symbol> <symbol id="user-icon" viewBox="0 0 18 18"> <path d="M13.5 11h-1.9c-.8.4-1.6.6-2.6.6s-1.8-.2-2.6-.6H4.5C2 11 0 13.1 0 15.7v.6c0 1 .8 1.8 1.7 1.8h14.6c.9 0 1.7-.8 1.7-1.8v-.6c0-2.6-2-4.7-4.5-4.7M9 10c2.8 0 5-2.2 5-5s-2.2-5-5-5-5 2.2-5 5 2.2 5 5 5"/> </symbol> <symbol id="search-icon" viewBox="0 0 20 20"> <path d="M19.7 17.3l-3.9-3.9c-.2-.2-.4-.3-.7-.3h-.6c1.1-1.4 1.7-3.1 1.7-5 0-4.5-3.6-8.1-8.1-8.1S0 3.6 0 8.1s3.6 8.1 8.1 8.1c1.9 0 3.6-.6 5-1.7v.6c0 .2.1.5.3.7l3.9 3.9c.4.4 1 .4 1.3 0l1.1-1.1c.4-.4.4-.9 0-1.3zM8.1 13.1c-2.8 0-5-2.2-5-5s2.2-5 5-5 5 2.2 5 5-2.2 5-5 5z"/> </symbol> <symbol id="shop-icon" viewBox="0 0 20 18"> <path d="M19.6 3.7L17.5.5c-.3-.3-.6-.5-.9-.5H3.4c-.4 0-.7.2-.9.5L.4 3.7c-1.1 1.7-.1 4 1.9 4.2h.4c1 0 1.8-.4 2.4-1C5.7 7.6 6.6 8 7.5 8c1 0 1.8-.4 2.4-1 .6.6 1.4 1 2.4 1s1.8-.4 2.4-1c.6.6 1.4 1 2.4 1h.4c2.1-.3 3.1-2.6 2.1-4.3zM16.2 8.9V14H3.7V8.9c-.3.1-.7.1-.9.1h-.6c-.2 0-.3-.1-.5-.1V17c-.1.5.4 1 1 1h14.6c.6 0 1-.5 1-1V8.9c-.2.1-.3.1-.5.1h-.7c-.3 0-.6-.1-.9-.1z"/> </symbol> <symbol id="icon-droplet-first-twitter" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M18.679,9.229c-0.436,0.219-0.945,0.364-1.453,0.437c0.508-0.291,0.945-0.8,1.09-1.381 c-0.51,0.29-1.018,0.509-1.6,0.581c-0.436-0.509-1.09-0.8-1.889-0.8c-1.383,0-2.545,1.163-2.545,2.545c0,0.218,0,0.363,0.073,0.581 c-2.108-0.072-3.998-1.09-5.234-2.689c-0.218,0.363-0.363,0.8-0.363,1.309c0,0.872,0.437,1.672,1.163,2.108 c-0.437,0-0.8-0.146-1.163-0.291l0,0c0,1.235,0.872,2.253,2.035,2.472c-0.218,0.072-0.436,0.072-0.654,0.072 c-0.146,0-0.29,0-0.509-0.072c0.291,1.018,1.236,1.744,2.399,1.744c-0.873,0.654-1.963,1.091-3.126,1.091c-0.218,0-0.437,0-0.582,0 c1.091,0.727,2.472,1.163,3.926,1.163c4.653,0,7.196-3.853,7.196-7.197c0-0.145,0-0.218,0-0.363 C17.88,10.174,18.315,9.737,18.679,9.229"></path> </symbol> <symbol id="icon-droplet-first-pinterest" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M12.5,6.247c-3.417,0-6.18,2.763-6.18,6.18c0,2.544,1.526,4.726,3.708,5.67c0-0.437,0-0.944,0.072-1.453 c0.146-0.51,0.8-3.345,0.8-3.345s-0.219-0.362-0.219-0.944c0-0.944,0.51-1.6,1.163-1.6c0.581,0,0.8,0.437,0.8,0.944 c0,0.582-0.363,1.382-0.581,2.181c-0.146,0.654,0.362,1.163,0.944,1.163c1.163,0,1.962-1.526,1.962-3.271 c0-1.383-0.943-2.399-2.544-2.399c-1.89,0-3.053,1.382-3.053,2.98c0,0.509,0.146,0.944,0.437,1.235 c0.146,0.146,0.146,0.218,0.072,0.363c0,0.146-0.072,0.363-0.146,0.509c-0.073,0.146-0.146,0.218-0.291,0.146 c-0.872-0.363-1.235-1.31-1.235-2.326c0-1.745,1.454-3.854,4.435-3.854c2.325,0,3.926,1.744,3.926,3.563 c0,2.397-1.381,4.216-3.344,4.216c-0.654,0-1.31-0.363-1.526-0.799c0,0-0.363,1.453-0.438,1.672 c-0.146,0.509-0.362,0.944-0.653,1.308c0.582,0.146,1.163,0.22,1.745,0.22c3.416,0,6.179-2.764,6.179-6.18 C18.679,9.01,15.916,6.247,12.5,6.247"></path> </symbol> <symbol id="icon-droplet-first-facebook" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M10.191,10.675h1.31V9.44c0-0.582,0-1.381,0.436-1.891c0.438-0.582,1.019-0.944,1.963-0.944 c1.601,0,2.326,0.218,2.326,0.218l-0.291,1.891c0,0-0.509-0.146-1.018-0.146s-0.945,0.146-0.945,0.654v1.452h2.036l-0.146,1.817 h-1.891v6.397h-2.398v-6.397h-1.309v-1.817H10.191L10.191,10.675z"></path> </symbol> <symbol id="icon-droplet-first-googleplus" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M9.592,11.481v2.034c0,0,1.963,0,2.763,0c-0.437,1.311-1.091,2.035-2.763,2.035s-2.98-1.381-2.98-2.979 c0-1.673,1.31-2.98,2.98-2.98c0.872,0,1.454,0.29,1.963,0.728c0.437-0.438,0.363-0.509,1.455-1.454 c-0.874-0.8-2.109-1.31-3.417-1.31c-2.762,0-5.088,2.255-5.088,5.09s2.253,5.089,5.088,5.089c4.216,0,5.234-3.635,4.871-6.106 H9.592V11.481L9.592,11.481z"></path> <path class="inner-figure" fill="currentColor" d="M 18.752,11.554 L 18.752,9.811 17.516,9.811 17.516,11.554 15.699,11.554 15.699,12.79 17.516,12.79 17.516,14.607 18.752,14.607 18.752,12.79 20.496,12.79 20.496,11.554 z"></path> </symbol> <symbol id="icon-droplet-first-youtube" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M15.043,12.5l-4.072,2.398v-4.725L15.043,12.5z M18.822,14.681v-4.434c0,0,0-2.108-2.107-2.108H8.646 c0,0-2.107,0-2.107,2.108v4.434c0,0,0,2.107,2.107,2.107h8.069C16.641,16.788,18.822,16.788,18.822,14.681"></path> </symbol> <symbol id="icon-droplet-first-youtube2" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M15.045,8.284v2.326c-0.072,0.071-0.146,0.146-0.219,0.146c-0.072,0.072-0.146,0.072-0.146,0.072 c-0.072,0-0.072,0-0.145-0.072c0-0.073-0.072-0.073-0.072-0.146V8.21h-0.654v2.617c0,0.218,0.072,0.291,0.146,0.437 c0.072,0.072,0.217,0.146,0.363,0.146c0.145,0,0.217,0,0.361-0.073c0.146-0.072,0.219-0.146,0.363-0.291v0.364h0.654V8.285 L15.045,8.284L15.045,8.284z M13.082,8.429c-0.146-0.146-0.364-0.219-0.654-0.219c-0.291,0-0.509,0.073-0.728,0.219 c-0.146,0.146-0.29,0.363-0.29,0.581v1.601c0,0.29,0.071,0.509,0.218,0.653c0.146,0.146,0.363,0.219,0.654,0.219 s0.509-0.072,0.653-0.219c0.146-0.146,0.218-0.363,0.218-0.653V9.01C13.301,8.792,13.229,8.574,13.082,8.429z M12.646,10.682 c0,0.073,0,0.146-0.071,0.219c-0.073,0.072-0.146,0.072-0.22,0.072c-0.071,0-0.146,0-0.218-0.072s-0.072-0.146-0.072-0.219V9.01 c0-0.071,0-0.146,0.072-0.146c0.072-0.072,0.146-0.072,0.218-0.072c0.073,0,0.146,0,0.22,0.072c0.071,0.073,0.071,0.073,0.071,0.146 V10.682z M10.683,7.193l-0.436,1.672h-0.073L9.739,7.193H9.01l0.872,2.544v1.673h0.729V9.665l0.8-2.472H10.683L10.683,7.193z M15.627,14.607c0.072,0.072,0.072,0.146,0.072,0.219v0.291h-0.51v-0.291c0-0.146,0-0.219,0.072-0.219 c0.072-0.072,0.072-0.072,0.219-0.072C15.555,14.535,15.627,14.535,15.627,14.607z M13.736,14.607 c-0.072-0.072-0.072-0.072-0.146-0.072c-0.072,0-0.072,0-0.145,0c-0.074,0-0.074,0.072-0.146,0.072v1.744 c0.072,0.072,0.072,0.072,0.146,0.072c0.07,0,0.07,0,0.145,0c0.072,0,0.146,0,0.146-0.072s0.072-0.072,0.072-0.219V14.68 C13.736,14.68,13.736,14.607,13.736,14.607z M13.736,14.607c-0.072-0.072-0.072-0.072-0.146-0.072c-0.072,0-0.072,0-0.145,0 c-0.074,0-0.074,0.072-0.146,0.072v1.744c0.072,0.072,0.072,0.072,0.146,0.072c0.07,0,0.07,0,0.145,0c0.072,0,0.146,0,0.146-0.072 s0.072-0.072,0.072-0.219V14.68C13.736,14.68,13.736,14.607,13.736,14.607z M16.936,12.427h-8.87c-0.943,0-1.743,0.799-1.743,1.745 v1.816c0,0.945,0.8,1.746,1.743,1.746h8.87c0.945,0,1.744-0.801,1.744-1.746v-1.816C18.68,13.227,17.881,12.427,16.936,12.427z M10.465,13.662H9.811v3.271H9.158v-3.271H8.503v-0.58h1.963L10.465,13.662L10.465,13.662z M12.283,16.934h-0.582v-0.361 c-0.072,0.145-0.219,0.217-0.29,0.289c-0.146,0.072-0.22,0.072-0.291,0.072c-0.146,0-0.218-0.072-0.291-0.145 c-0.072-0.074-0.072-0.219-0.072-0.363V14.1h0.581v2.182c0,0.072,0,0.145,0.073,0.145c0,0,0.071,0.072,0.146,0.072 c0.072,0,0.072,0,0.146-0.072c0.072-0.072,0.146-0.072,0.146-0.145v-2.109h0.582v2.764h-0.146V16.934z M14.316,16.352 c0,0.219-0.07,0.363-0.145,0.438c-0.072,0.072-0.219,0.146-0.363,0.146s-0.219,0-0.291-0.074c-0.072-0.072-0.146-0.072-0.219-0.217 v0.217h-0.582v-3.854h0.582v1.236c0.072-0.072,0.146-0.146,0.219-0.219s0.146-0.072,0.291-0.072s0.291,0.072,0.438,0.146 c0.07,0.146,0.145,0.289,0.145,0.51v1.744h-0.074V16.352z M16.281,15.553h-1.092v0.51c0,0.145,0,0.219,0.072,0.291 s0.072,0.072,0.219,0.072c0.072,0,0.146,0,0.219-0.072s0.072-0.146,0.072-0.291v-0.146h0.58v0.146c0,0.291-0.07,0.51-0.217,0.652 c-0.146,0.146-0.363,0.221-0.654,0.221s-0.438-0.074-0.582-0.221c-0.145-0.145-0.219-0.361-0.219-0.652v-1.236 c0-0.217,0.074-0.438,0.219-0.58c0.146-0.146,0.363-0.219,0.582-0.219c0.291,0,0.438,0.072,0.582,0.219 c0.145,0.145,0.219,0.363,0.219,0.58V15.553L16.281,15.553z M13.518,14.535c-0.07,0-0.07,0-0.145,0 c-0.072,0-0.072,0.072-0.146,0.072v1.744c0.072,0.072,0.072,0.072,0.146,0.072c0.072,0,0.072,0,0.145,0c0.074,0,0.146,0,0.146-0.072 s0.074-0.072,0.074-0.219V14.68c0-0.072,0-0.146-0.074-0.219C13.664,14.535,13.592,14.535,13.518,14.535z M13.736,14.607 c-0.072-0.072-0.072-0.072-0.146-0.072c-0.072,0-0.072,0-0.145,0c-0.074,0-0.074,0.072-0.146,0.072v1.744 c0.072,0.072,0.072,0.072,0.146,0.072c0.07,0,0.07,0,0.145,0c0.072,0,0.146,0,0.146-0.072s0.072-0.072,0.072-0.219V14.68 C13.736,14.68,13.736,14.607,13.736,14.607z M13.736,14.607c-0.072-0.072-0.072-0.072-0.146-0.072c-0.072,0-0.072,0-0.145,0 c-0.074,0-0.074,0.072-0.146,0.072v1.744c0.072,0.072,0.072,0.072,0.146,0.072c0.07,0,0.07,0,0.145,0c0.072,0,0.146,0,0.146-0.072 s0.072-0.072,0.072-0.219V14.68C13.736,14.68,13.736,14.607,13.736,14.607z"></path> </symbol> <symbol id="icon-droplet-first-tumblr" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M12.789,6.321v3.126h2.907v1.963h-2.907v3.198c0,0.727,0.072,1.164,0.146,1.381 c0.072,0.219,0.218,0.363,0.437,0.51c0.291,0.146,0.582,0.217,0.945,0.217c0.652,0,1.234-0.217,1.889-0.58v1.963 c-0.508,0.219-1.018,0.436-1.453,0.51c-0.436,0.07-0.873,0.145-1.381,0.145c-0.582,0-1.09-0.072-1.527-0.219 c-0.436-0.146-0.8-0.363-1.09-0.58c-0.291-0.291-0.509-0.582-0.654-0.873s-0.146-0.727-0.146-1.309V11.41H8.5V9.666 C9.009,9.52,9.373,9.302,9.736,9.011s0.653-0.653,0.8-1.091c0.219-0.436,0.364-0.943,0.437-1.599L12.789,6.321L12.789,6.321z"></path> </symbol> <symbol id="icon-droplet-first-apple" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M17.949,15.335c-0.291,0.654-0.436,0.945-0.799,1.526c-0.51,0.8-1.236,1.817-2.182,1.817 c-0.799,0-1.018-0.509-2.108-0.509c-1.09,0-1.309,0.509-2.107,0.509c-0.945,0-1.6-0.872-2.108-1.672 c-1.454-2.253-1.6-4.798-0.727-6.179c0.654-1.018,1.672-1.526,2.616-1.526c0.945,0,1.6,0.509,2.399,0.509 c0.8,0,1.236-0.509,2.398-0.509c0.873,0,1.746,0.436,2.4,1.235C15.551,11.555,15.842,14.535,17.949,15.335L17.949,15.335z M14.389,8.284c0.436-0.509,0.727-1.236,0.58-2.036c-0.654,0.073-1.453,0.437-1.889,1.019c-0.437,0.509-0.728,1.235-0.655,1.962 C13.225,9.302,13.951,8.865,14.389,8.284L14.389,8.284z"></path> </symbol> <symbol id="icon-droplet-first-behance" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M11.981,9.364c0.221,0.297,0.369,0.742,0.369,1.185c0,0.445-0.148,0.89-0.369,1.112 c-0.147,0.148-0.295,0.296-0.59,0.444c0.369,0.148,0.664,0.371,0.885,0.667c0.221,0.297,0.295,0.667,0.295,1.111 c0,0.445-0.147,0.889-0.37,1.259c-0.147,0.223-0.294,0.444-0.517,0.593c-0.221,0.222-0.516,0.297-0.885,0.37 c-0.369,0.075-0.737,0.075-1.107,0.075H6.226v-7.63h3.763C10.948,8.549,11.612,8.846,11.981,9.364z M7.775,9.883v1.704h1.918 c0.37,0,0.591-0.074,0.812-0.222c0.222-0.148,0.295-0.37,0.295-0.667c0-0.37-0.147-0.592-0.443-0.74 c-0.221-0.075-0.517-0.148-0.885-0.148H7.775V9.883z M7.775,12.92v2.073h1.918c0.37,0,0.591-0.073,0.812-0.147 c0.369-0.147,0.517-0.518,0.517-0.963c0-0.37-0.147-0.667-0.517-0.814c-0.221-0.074-0.442-0.148-0.812-0.148H7.775z M17.293,10.698 c0.37,0.148,0.739,0.445,0.96,0.815c0.222,0.37,0.369,0.74,0.443,1.185c0.073,0.296,0.073,0.667,0.073,1.112h-4.206 c0,0.592,0.222,0.962,0.591,1.185c0.221,0.147,0.517,0.222,0.812,0.222c0.369,0,0.59-0.074,0.811-0.296 c0.148-0.074,0.223-0.222,0.296-0.37h1.55c-0.074,0.37-0.222,0.666-0.59,1.036c-0.517,0.594-1.256,0.815-2.141,0.815 c-0.737,0-1.401-0.222-1.992-0.741c-0.59-0.444-0.886-1.259-0.886-2.296c0-0.963,0.296-1.778,0.813-2.296s1.181-0.814,2.065-0.814 C16.482,10.401,16.925,10.476,17.293,10.698z M15.08,11.958c-0.222,0.222-0.369,0.519-0.369,0.889h2.582 c0-0.37-0.146-0.667-0.368-0.889c-0.222-0.223-0.517-0.296-0.886-0.296C15.597,11.661,15.302,11.735,15.08,11.958z M17.589,8.921 h-3.32v0.962h3.32V8.921z"></path> </symbol> <symbol id="icon-droplet-first-vimeo" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M18.68,9.882c-0.074,1.236-0.873,2.835-2.545,4.944c-1.674,2.18-3.125,3.27-4.289,3.27 c-0.729,0-1.31-0.652-1.817-1.963c-0.364-1.234-0.654-2.471-1.019-3.634c-0.363-1.309-0.8-1.963-1.162-1.963 c-0.073,0-0.438,0.219-0.945,0.582l-0.582-0.729c0.582-0.509,1.236-1.09,1.817-1.599C8.938,8.063,9.592,7.7,9.955,7.7 C10.9,7.627,11.481,8.282,11.7,9.663c0.218,1.526,0.437,2.472,0.509,2.835c0.291,1.237,0.581,1.889,0.946,1.889 c0.289,0,0.652-0.436,1.162-1.234c0.51-0.799,0.799-1.454,0.799-1.89c0.074-0.728-0.217-1.018-0.799-1.018 c-0.291,0-0.582,0.072-0.945,0.218c0.582-1.963,1.744-2.908,3.49-2.908C18.098,7.484,18.75,8.284,18.68,9.882z"></path> </symbol> <symbol id="icon-droplet-first-instagram" viewBox="0 0 32 32"> <circle cx="16.1" cy="16" r="16" fill="#FFF"/> <circle style="fill: #C4C4C4; fill: var(--icon-background)" cx="16.1" cy="16" r="3"/> <path style="fill: #C4C4C4; fill: var(--icon-background)" d="M23.1 10.7c-.3-.8-.9-1.4-1.7-1.7-.5-.2-1.1-.3-1.7-.3h-7.2c-.6 0-1.1.1-1.7.3-.8.3-1.4.9-1.7 1.7-.2.5-.3 1.1-.3 1.7v7.2c0 .6.1 1.1.3 1.7.3.8.9 1.4 1.7 1.7.5.2 1.1.3 1.7.3h7.2c.6 0 1.1-.1 1.7-.3.8-.3 1.4-.9 1.7-1.7.2-.5.3-1.1.3-1.7V16v-3.6c0-.6-.1-1.2-.3-1.7zm-7 9.9a4.65 4.65 0 01-3.3-7.9 4.65 4.65 0 017.9 3.3c0 2.5-2.1 4.6-4.6 4.6zm4.8-8.3c-.6 0-1.1-.5-1.1-1.1s.5-1.1 1.1-1.1c.6 0 1.1.5 1.1 1.1s-.5 1.1-1.1 1.1z"/> <path style="fill: #C4C4C4; fill: var(--icon-background)" d="M16.1 0C11.7 0 7.7 1.8 4.8 4.7a15.95 15.95 0 000 22.6 15.95 15.95 0 0022.6 0c2.9-2.9 4.7-6.9 4.7-11.3 0-8.8-7.2-16-16-16zm9 19.7c0 .8-.2 1.5-.4 2.2-.5 1.2-1.4 2.2-2.6 2.6-.7.3-1.4.4-2.2.4h-7.4c-.8 0-1.5-.2-2.2-.4-1.2-.5-2.2-1.4-2.6-2.6-.3-.7-.4-1.4-.4-2.2V16v-3.7c0-.8.2-1.5.4-2.2.5-1.2 1.4-2.2 2.6-2.6.7-.3 1.4-.4 2.2-.4h3.7c2.5 0 2.8 0 3.7.1.8 0 1.5.2 2.2.4 1.2.5 2.2 1.4 2.6 2.6.3.7.4 1.4.4 2.2V19.7z"/> </symbol> <symbol id="icon-droplet-first-flickr" viewBox="0 0 33 33"> <rect x=".5" y=".5" width="32" height="32" rx="16" style="fill: #C4C4C4; fill: var(--icon-circle-color)"/> <path style="fill: #FFF; fill: var(--icon-border-color)" d="M16.5 33A16.5 16.5 0 1133 16.5 16.52 16.52 0 0116.5 33zm0-32A15.5 15.5 0 1032 16.5 15.52 15.52 0 0016.5 1z"/> <path style="fill: #FFF; fill: var(--right-dot)" d="M17.77 16.5a4.64 4.64 0 104.64-4.64 4.64 4.64 0 00-4.64 4.64z"/> <path style="fill: #FFF; fill: var(--left-dot)" d="M5.95 16.5a4.64 4.64 0 104.64-4.64 4.64 4.64 0 00-4.64 4.64z"/> </symbol> <symbol id="icon-droplet-first-rss" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M9.154,15.819c0.29,0.291,0.509,0.728,0.509,1.163c0,0.437-0.219,0.872-0.509,1.163l0,0 c-0.291,0.291-0.728,0.509-1.163,0.509c-0.437,0-0.873-0.218-1.163-0.509c-0.291-0.291-0.51-0.727-0.51-1.163 c0-0.436,0.219-0.872,0.51-1.163l0,0c0.29-0.291,0.727-0.509,1.163-0.509C8.426,15.311,8.863,15.528,9.154,15.819z M9.154,15.819 L9.154,15.819L9.154,15.819L9.154,15.819z M6.828,18.146L6.828,18.146L6.828,18.146L6.828,18.146z M6.318,10.44v2.326 c1.527,0,2.98,0.581,4.071,1.672c1.091,1.09,1.672,2.544,1.672,4.07l0,0l0,0h2.398c0-2.253-0.943-4.288-2.398-5.742 C10.607,11.386,8.572,10.44,6.318,10.44z M6.318,6.297v2.326c5.525,0,9.959,4.507,9.959,10.031h2.398 c0-3.416-1.381-6.47-3.635-8.723C12.861,7.678,9.735,6.297,6.318,6.297z"></path> </symbol> <symbol id="icon-droplet-first-blogger" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M18.605,11.41l-0.072-0.22l-0.146-0.146c-0.218-0.146-1.381,0-1.672-0.29 c-0.218-0.219-0.218-0.582-0.291-1.091c-0.146-0.945-0.218-1.019-0.362-1.31c-0.51-1.163-2.035-2.035-2.98-2.107h-2.763 c-2.182,0-3.925,1.745-3.925,3.926v4.58c0,2.107,1.743,3.926,3.925,3.926h4.508c2.181,0,3.925-1.744,3.925-3.926v-3.125 L18.605,11.41z M10.246,9.519h2.181c0.438,0,0.729,0.363,0.729,0.729c0,0.436-0.364,0.727-0.729,0.727h-2.181 c-0.437,0-0.727-0.363-0.727-0.727C9.52,9.81,9.883,9.519,10.246,9.519z M14.681,15.48h-4.435c-0.437,0-0.727-0.363-0.727-0.727 c0-0.438,0.362-0.729,0.727-0.729h4.435c0.437,0,0.728,0.363,0.728,0.729C15.407,15.117,15.116,15.48,14.681,15.48z"></path> </symbol> <symbol id="icon-droplet-first-dribbble" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M18.533,11.24c-0.072-0.364-0.217-0.8-0.363-1.163c-0.145-0.364-0.363-0.728-0.582-1.018 c-0.217-0.364-0.508-0.654-0.727-0.945c-0.291-0.291-0.582-0.509-0.945-0.728c-0.363-0.218-0.654-0.436-1.018-0.581 S14.1,6.515,13.736,6.442c-0.438-0.073-0.8-0.146-1.236-0.146c-0.437,0-0.873,0.072-1.236,0.146c-0.363,0.072-0.8,0.218-1.163,0.363 S9.375,7.169,9.083,7.387C8.72,7.605,8.429,7.896,8.138,8.114C7.848,8.405,7.629,8.695,7.412,9.06 C7.194,9.423,6.975,9.713,6.83,10.077c-0.145,0.363-0.29,0.799-0.363,1.163c-0.072,0.436-0.146,0.799-0.146,1.235 s0.073,0.872,0.146,1.236c0.073,0.363,0.219,0.799,0.363,1.163c0.146,0.363,0.364,0.727,0.582,1.018 c0.218,0.363,0.509,0.654,0.727,0.944c0.291,0.291,0.582,0.509,0.945,0.728c0.363,0.218,0.654,0.436,1.018,0.581 s0.8,0.291,1.163,0.363c0.437,0.073,0.8,0.146,1.236,0.146c0.436,0,0.873-0.072,1.236-0.146c0.363-0.072,0.799-0.218,1.162-0.363 s0.727-0.363,1.018-0.581c0.363-0.219,0.654-0.509,0.945-0.728c0.291-0.29,0.51-0.581,0.727-0.944 c0.219-0.364,0.438-0.654,0.582-1.018c0.146-0.364,0.291-0.8,0.363-1.163c0.072-0.437,0.146-0.8,0.146-1.236 S18.68,11.604,18.533,11.24z M17.807,12.403c-0.072,0-1.891-0.437-3.707-0.146c-0.072-0.073-0.072-0.146-0.146-0.291 c-0.145-0.291-0.217-0.509-0.363-0.8c2.109-0.872,2.908-2.107,2.908-2.107C17.297,10.004,17.734,11.167,17.807,12.403z M15.99,8.478 c0,0-0.801,1.163-2.764,1.89c-0.872-1.672-1.889-2.98-1.962-3.053c0.363-0.072,0.8-0.146,1.236-0.146 C13.809,7.169,15.045,7.678,15.99,8.478z M11.264,7.314L11.264,7.314L11.264,7.314L11.264,7.314z M10.247,7.678 c0.072,0.073,1.018,1.454,1.963,3.054c-2.544,0.654-4.726,0.654-4.87,0.654C7.703,9.713,8.792,8.405,10.247,7.678z M7.194,12.476 c0-0.072,0-0.146,0-0.146c0.072,0,2.689,0.073,5.451-0.727c0.146,0.291,0.291,0.581,0.437,0.872c-0.072,0-0.146,0.073-0.218,0.073 c-2.835,0.944-4.289,3.489-4.289,3.489l0,0C7.703,15.02,7.194,13.784,7.194,12.476z M12.5,17.71c-1.309,0-2.472-0.437-3.345-1.236 c0.073,0.073,0.146,0.073,0.146,0.073s1.018-2.182,4.144-3.271l0,0c0.727,1.963,1.018,3.562,1.162,3.998 C13.953,17.564,13.227,17.71,12.5,17.71z M15.48,16.837c-0.072-0.29-0.363-1.89-1.018-3.779c1.672-0.291,3.199,0.218,3.271,0.218 C17.516,14.802,16.645,16.038,15.48,16.837z"></path> </symbol> <symbol id="icon-droplet-first-skype" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M18.316,13.809c0.072-0.437,0.145-0.8,0.145-1.235c0-3.271-2.617-5.889-5.888-5.889 c-0.363,0-0.654,0-1.018,0.073c-0.509-0.364-1.163-0.51-1.817-0.51c-1.89,0-3.417,1.527-3.417,3.417 c0,0.654,0.146,1.235,0.437,1.745c-0.072,0.363-0.146,0.799-0.146,1.163c0,3.271,2.617,5.888,5.889,5.888 c0.363,0,0.726,0,1.089-0.072c0.51,0.29,1.018,0.436,1.6,0.436c1.891,0,3.416-1.526,3.416-3.416 C18.68,14.826,18.605,14.245,18.316,13.809z M15.625,15.335c-0.289,0.363-0.652,0.728-1.162,0.945s-1.164,0.291-1.817,0.291 c-0.873,0-1.527-0.146-2.108-0.437c-0.363-0.218-0.728-0.509-0.945-0.872s-0.363-0.728-0.363-1.091c0-0.218,0.072-0.436,0.218-0.581 s0.363-0.219,0.582-0.219c0.218,0,0.363,0.073,0.509,0.146c0.146,0.146,0.218,0.291,0.363,0.509 c0.072,0.219,0.218,0.437,0.363,0.582s0.291,0.291,0.509,0.363c0.219,0.072,0.509,0.146,0.873,0.146 c0.508,0,0.872-0.073,1.163-0.291s0.436-0.437,0.436-0.727c0-0.219-0.072-0.437-0.217-0.582c-0.146-0.146-0.363-0.291-0.654-0.363 s-0.655-0.146-1.091-0.291c-0.581-0.146-1.163-0.29-1.526-0.436c-0.436-0.146-0.8-0.437-1.018-0.728 c-0.218-0.29-0.363-0.727-0.363-1.163c0-0.436,0.146-0.872,0.437-1.163c0.29-0.363,0.653-0.581,1.163-0.8 c0.509-0.145,1.09-0.29,1.744-0.29c0.508,0,1.018,0.072,1.381,0.146c0.363,0.145,0.727,0.29,0.945,0.509 c0.291,0.218,0.436,0.436,0.58,0.654c0.146,0.218,0.219,0.436,0.219,0.654c0,0.218-0.072,0.436-0.219,0.581 c-0.145,0.146-0.363,0.218-0.58,0.218c-0.219,0-0.363-0.072-0.51-0.146c-0.145-0.072-0.217-0.218-0.363-0.436 c-0.145-0.291-0.291-0.509-0.508-0.654c-0.219-0.146-0.51-0.218-1.018-0.218c-0.437,0-0.8,0.072-1.019,0.218 c-0.218,0.146-0.363,0.363-0.363,0.581c0,0.146,0.073,0.219,0.146,0.364c0.073,0.072,0.218,0.218,0.363,0.29 c0.146,0.073,0.291,0.146,0.509,0.219c0.146,0.072,0.437,0.146,0.8,0.218c0.508,0.072,0.945,0.218,1.309,0.363 s0.727,0.291,1.018,0.437c0.291,0.218,0.508,0.436,0.654,0.727c0.145,0.291,0.217,0.654,0.217,1.018 C15.99,14.535,15.916,14.972,15.625,15.335z"></path> </symbol> <symbol id="icon-droplet-first-yahoo" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M16.207,10.61c0.074,0,0.146-0.073,0.291-0.073h0.873l0.145-0.218l0,0l0.363-0.509l0,0V9.737h-4.797 l0.219,0.728h1.309L12.355,12.5c-0.436-0.654-1.526-1.963-2.253-2.98h1.672V8.938V8.793l0,0V8.72H6.249v0.728h1.6 c0.654,0.509,3.053,3.489,3.126,3.853c0.072,0.291,0.072,1.963-0.073,2.108c-0.218,0.218-1.308,0.218-1.599,0.218l-0.073,0.509 c0.509,0,2.036-0.072,2.545-0.072c0.944,0,2.617,0,2.835,0v-0.51c-0.219-0.072-1.527,0-1.746-0.072 c-0.072-0.291-0.072-2.035-0.072-2.181C13.154,12.863,15.918,10.683,16.207,10.61z"></path> <path class="inner-figure" fill="currentColor" d="M17.08,14.754l0.727,0.072l0.873-3.344c-0.146,0-1.453-0.146-1.6-0.146V14.754z"></path> <path class="inner-figure" fill="currentColor" d="M 16.936,15.335 L 16.936,16.135 17.299,16.135 17.734,16.207 17.807,15.408 17.371,15.335 z"></path> </symbol> <symbol id="icon-droplet-first-windows" viewBox="0 0 32 32"> <path style="fill: #C4C4C4; fill: var(--windows-background-color);" d="M16 0C11.6 0 7.6 1.8 4.7 4.7a15.95 15.95 0 000 22.6 15.95 15.95 0 0022.6 0C30.2 24.4 32 20.4 32 16c0-8.8-7.2-16-16-16zM7.5 22.1v-5.8h6.9V23l-6.9-.9zm7-6.5H7.6V9.8l7-1v6.8h-.1zm10 8.9l-9.2-1.3v-6.7h9.2v8zm0-8.9l-9.2.1V8.8l9.2-1.3v8.1z" fill="#c4c4c4"/> <path fill="#FFF" d="M7.5 16.3h6.9V23l-6.9-.9zM7.6 9.8l7-1v6.8h-7zM24.5 7.5v8.1l-9.2.1V8.8zM15.3 16.5h9.2v8l-9.2-1.3z"/> </symbol> <symbol id="icon-droplet-first-appstore" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M11.409,12.282l1.019-1.817c0.072-0.146,0-0.291-0.073-0.363l-0.727-0.437 c-0.146-0.072-0.291,0-0.363,0.072L10.102,11.7l0,0l-1.236,2.181l0,0l-1.018,1.816c-0.072,0.146,0,0.291,0.072,0.365l0.728,0.436 c0.146,0.072,0.291,0,0.363-0.072l1.454-2.545l0,0L11.409,12.282z"></path> <path class="inner-figure" fill="currentColor" d="M18.388,11.7h-2.544c0.291,0.582,0.582,1.091,0.727,1.455c0.146,0.291,0.219,0.436,0.291,0.508 c0.073,0.074,0.073,0.146,0.073,0.219h1.381c0.146,0,0.291-0.145,0.291-0.291v-1.671C18.679,11.845,18.533,11.7,18.388,11.7z"></path> <path class="inner-figure" fill="currentColor" d="M12.282,11.7l-1.236,2.181h3.417c-0.219-0.51-0.582-1.164-1.018-2.181H12.282z"></path> <path class="inner-figure" fill="currentColor" d="M9.447,11.7H6.539c-0.146,0-0.291,0.146-0.291,0.291v1.671c0,0.146,0.146,0.291,0.291,0.291h1.672 L9.447,11.7z"></path> <path class="inner-figure" fill="currentColor" d="M8.356,16.861L7.63,16.426c-0.146-0.074-0.291,0-0.291,0.145l-0.146,1.09c0,0.146,0.072,0.219,0.218,0.146 l0.945-0.654C8.502,17.08,8.502,16.934,8.356,16.861z"></path> <path class="inner-figure" fill="currentColor" d="M16.498,13.953L16.498,13.953L16.498,13.953c-0.072-0.145-0.291-0.508-0.509-0.945l0,0 c0,0,0-0.072-0.072-0.072c0,0,0-0.073-0.073-0.073v-0.072c0-0.073-0.072-0.073-0.072-0.146c0,0,0,0,0-0.072 c0-0.073-0.073-0.073-0.073-0.146c0,0,0,0,0-0.073c-0.509-1.018-1.163-2.181-1.599-2.835c-0.728-1.09-1.672-2.98-1.963-2.835 c-0.363,0.218,0.8,2.544,1.163,3.489c0.363,0.872,1.89,4.29,2.181,4.506c0.291,0.146,0.436,0.074,0.581,0 C16.353,14.535,16.644,14.244,16.498,13.953z"></path> <path class="inner-figure" fill="currentColor" d="M17.37,15.697l-0.363-0.654c-0.072-0.145-0.291-0.217-0.437-0.145l-0.436,0.219 c-0.146,0.072-0.218,0.291-0.073,0.436l0.437,0.727c0.072,0.146,0.291,0.219,0.437,0.072l0.29-0.217 C17.37,16.063,17.443,15.844,17.37,15.697z"></path> <path class="inner-figure" fill="currentColor" d="M17.225,16.715c-0.145,0.074-0.363,0.365-0.072,0.801s1.018,0.436,1.163,0.799 C18.315,18.242,18.533,15.988,17.225,16.715z"></path> </symbol> <symbol id="icon-droplet-first-android" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M14.39,7.411l0.146-0.218l0.146-0.218l0.291-0.437c0.072-0.072,0-0.146,0-0.146 c-0.072-0.072-0.146,0-0.146,0l-0.291,0.509L14.39,7.121l-0.145,0.218c-0.437-0.146-0.945-0.291-1.454-0.291 s-1.018,0.073-1.454,0.291l-0.146-0.218l-0.146-0.219l-0.29-0.509c-0.073-0.072-0.146-0.072-0.146,0 c-0.073,0.073-0.073,0.146,0,0.146l0.29,0.437l0.146,0.218l0.146,0.218C10.174,7.92,9.52,8.792,9.52,9.81h6.688 C16.062,8.719,15.408,7.847,14.39,7.411z M11.265,8.792c-0.219,0-0.364-0.146-0.364-0.363c0-0.219,0.146-0.364,0.364-0.364 c0.218,0,0.363,0.146,0.363,0.364C11.628,8.647,11.482,8.792,11.265,8.792z M14.172,8.792c-0.218,0-0.363-0.146-0.363-0.363 c0-0.219,0.146-0.364,0.363-0.364s0.363,0.146,0.363,0.364C14.535,8.647,14.39,8.792,14.172,8.792z"></path> <path class="inner-figure" fill="currentColor" d="M9.447,10.247L9.447,10.247l-0.073,0.581v0.509v4.216c0,0.436,0.363,0.727,0.728,0.727h0.581 c0,0.072,0,0.146,0,0.219v0.072v0.219v1.234c0,0.365,0.291,0.729,0.728,0.729c0.363,0,0.727-0.291,0.727-0.729v-1.234V16.57v-0.072 c0-0.072,0-0.146,0-0.219h1.309c0,0.072,0,0.146,0,0.219v0.072v0.219v1.234c0,0.365,0.291,0.729,0.727,0.729 c0.363,0,0.727-0.291,0.727-0.729v-1.234V16.57v-0.072c0-0.072,0-0.146,0-0.219h0.582c0.437,0,0.727-0.363,0.727-0.727v-4.216 v-0.509v-0.581h-0.072H9.447z"></path> <path class="inner-figure" fill="currentColor" d="M8.211,10.247c-0.363,0-0.727,0.29-0.727,0.727v3.052c0,0.363,0.291,0.729,0.727,0.729 c0.363,0,0.728-0.291,0.728-0.729v-3.052C8.938,10.61,8.574,10.247,8.211,10.247z"></path> <path class="inner-figure" fill="currentColor" d="M17.298,10.247c-0.363,0-0.727,0.29-0.727,0.727v3.052c0,0.363,0.29,0.729,0.727,0.729 c0.363,0,0.727-0.291,0.727-0.729v-3.052C18.024,10.61,17.661,10.247,17.298,10.247z"></path> </symbol> <symbol id="icon-droplet-first-amazon" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M16.934,16.279c-0.072,0-0.146,0-0.219,0.071c-0.072,0-0.145,0.073-0.219,0.073l-0.072,0.072l-0.145,0.072 l0,0c-1.311,0.51-2.689,0.872-3.999,0.872c-0.073,0-0.073,0-0.146,0c-2.034,0-3.706-0.944-5.38-1.891 c-0.071,0-0.145-0.072-0.145-0.072c-0.073,0-0.146,0-0.219,0.072c-0.072,0.073-0.072,0.146-0.072,0.219s0.072,0.218,0.146,0.218 c1.6,1.382,3.271,2.617,5.598,2.617c0.073,0,0.073,0,0.146,0c1.455,0,3.126-0.509,4.435-1.309l0,0 c0.146-0.072,0.363-0.218,0.51-0.363c0.072-0.073,0.145-0.219,0.145-0.291C17.297,16.425,17.08,16.279,16.934,16.279z"></path> <path class="inner-figure" fill="currentColor" d="M18.678,15.552L18.678,15.552c0-0.145,0-0.218-0.072-0.29l0,0l0,0c-0.072-0.073-0.072-0.073-0.146-0.073 c-0.145-0.071-0.436-0.071-0.727-0.071c-0.219,0-0.438,0-0.654,0.071l0,0l-0.219,0.073l0,0l-0.146,0.072l0,0 c-0.145,0.073-0.289,0.146-0.436,0.218c-0.072,0.073-0.146,0.146-0.146,0.291c0,0.073,0,0.146,0.072,0.219 c0.074,0.072,0.146,0.072,0.219,0.072c0,0,0,0,0.072,0l0,0l0,0c0.146,0,0.291-0.072,0.51-0.072c0.146,0,0.363,0,0.508,0 c0.146,0,0.219,0,0.291,0h0.072l0,0c0,0,0,0,0,0.072c0,0.146-0.072,0.363-0.145,0.581c-0.072,0.219-0.146,0.51-0.219,0.654 c0,0.072,0,0.072,0,0.146s0,0.146,0.072,0.219s0.146,0.072,0.219,0.072l0,0c0.072,0,0.146,0,0.219-0.072 c0.582-0.509,0.799-1.382,0.799-1.891L18.678,15.552z"></path> <path class="inner-figure" fill="currentColor" d="M13.154,9.737c-0.364,0-0.8,0.072-1.164,0.072c-0.654,0.072-1.235,0.218-1.745,0.438 c-1.018,0.436-1.672,1.308-1.672,2.544c0,1.599,1.019,2.397,2.326,2.397c0.437,0,0.8-0.071,1.091-0.146 c0.509-0.146,0.945-0.437,1.455-1.019c0.289,0.438,0.363,0.582,0.871,1.019c0.146,0.073,0.291,0.073,0.363,0 c0.363-0.29,0.871-0.799,1.236-1.019c0.145-0.146,0.145-0.29,0-0.436c-0.291-0.364-0.582-0.728-0.582-1.454V9.663 c0-1.019,0.072-1.963-0.654-2.689c-0.654-0.582-1.6-0.8-2.398-0.8h-0.291C10.61,6.247,9.156,6.828,8.792,8.573 c-0.073,0.218,0.146,0.29,0.218,0.29l1.526,0.219c0.146,0,0.219-0.146,0.291-0.291c0.146-0.582,0.654-0.872,1.163-0.945h0.146 c0.363,0,0.654,0.146,0.872,0.438c0.219,0.362,0.219,0.8,0.219,1.163v0.29L13.154,9.737L13.154,9.737z M12.863,13.008 c-0.218,0.363-0.509,0.654-0.872,0.728c-0.072,0-0.146,0-0.218,0c-0.582,0-0.945-0.437-0.945-1.162c0-0.873,0.509-1.31,1.163-1.454 c0.363-0.073,0.8-0.146,1.164-0.146v0.363C13.154,11.918,13.227,12.427,12.863,13.008z"></path> </symbol> <symbol id="icon-droplet-first-linkedin" viewBox="0 0 34 34"> <path class="circle" d="M17,34c9.4,0,17-7.6,17-17c0-9.4-7.6-17-17-17S0,7.6,0,17C0,26.4,7.6,34,17,34"></path> <path class="inner-figure" fill="currentColor" d="M12.3,24.1H8.9V13.5h3.3V24.1z M10.5,12.2L10.5,12.2c-1.2,0-2-0.8-2-1.8c0-1,0.8-1.8,2-1.8c1.2,0,2,0.8,2,1.8 C12.5,11.4,11.8,12.2,10.5,12.2z M25.5,24.1h-3.8v-5.5c0-1.4-0.6-2.4-1.9-2.4c-1,0-1.5,0.7-1.8,1.3C18,17.7,18,18.1,18,18.4v5.7 h-3.7c0,0,0-9.7,0-10.6H18v1.7c0.2-0.7,1.4-1.8,3.3-1.8c2.4,0,4.2,1.5,4.2,4.8V24.1z"></path> </symbol> <symbol id="icon-droplet-first-forrst" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M17.848,18.242L12.759,6.466c-0.072-0.073-0.146-0.146-0.29-0.146l0,0c-0.146,0-0.219,0.072-0.291,0.146 L7.017,18.242c-0.073,0.072,0,0.219,0,0.291c0.072,0.072,0.146,0.145,0.218,0.145h4.58v-2.471l-1.236-0.873l0.219-0.291l1.018,0.729 v-2.035h1.163v1.307l1.09-0.508l0.146,0.291l-1.235,0.582v0.943l1.962-1.018l0.146,0.291l-2.107,1.09v1.891h4.507 c0.072,0,0.218-0.072,0.218-0.145C17.92,18.461,17.92,18.389,17.848,18.242z"></path> </symbol> <symbol id="icon-droplet-first-cursor" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M 18.68,13.954 L 6.321,13.954 6.321,10.974 7.703,10.974 7.703,12.573 17.297,12.573 17.297,10.974 18.68,10.974 z"></path> </symbol> <symbol id="icon-droplet-first-dropbox" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M 6.463,13.297 L 10.098,15.696 12.643,13.515 8.935,11.262 z"></path> <path class="inner-figure" fill="currentColor" d="M 10.098,6.9 L 6.463,9.299 8.935,11.262 12.643,9.008 z"></path> <path class="inner-figure" fill="currentColor" d="M 18.821,9.299 L 15.187,6.9 12.643,9.008 16.276,11.262 z"></path> <path class="inner-figure" fill="currentColor" d="M 12.643,13.515 L 15.187,15.696 18.821,13.297 16.276,11.262 z"></path> <path class="inner-figure" fill="currentColor" d="M 12.643,14.023 L 10.098,16.132 9.008,15.405 9.008,16.205 12.643,18.386 16.276,16.205 16.276,15.405 15.187,16.132 z"></path> </symbol> <symbol id="icon-droplet-first-lastfm" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M16.644,11.919c-0.072,0-0.218-0.073-0.29-0.073c-0.8-0.218-1.236-0.363-1.236-1.018 c0-0.509,0.364-0.872,0.873-0.872c0.363,0,0.727,0.146,0.944,0.509c0,0,0.073,0.072,0.146,0l0.8-0.363c0,0,0.072,0,0.072-0.073 V9.956c-0.436-0.728-1.018-1.091-1.817-1.091c-1.235,0-2.035,0.728-2.035,1.891s0.8,1.672,2.181,2.181 c0.8,0.291,1.236,0.436,1.236,1.018c0,0.654-0.582,1.163-1.454,1.09c-0.872,0-1.091-0.509-1.454-1.235 c-0.581-1.235-1.163-2.763-1.163-2.763c-0.654-1.453-1.89-2.326-3.416-2.326c-2.036,0-3.708,1.672-3.708,3.708 c0,2.035,1.672,3.707,3.708,3.707c1.09,0,2.181-0.509,2.907-1.381v-0.073l-0.436-1.09l-0.073-0.073c0,0-0.072,0-0.072,0.073 c-0.437,0.872-1.309,1.381-2.254,1.381c-1.381,0-2.544-1.163-2.544-2.544c0-1.382,1.163-2.545,2.544-2.545 c1.018,0,1.963,0.582,2.326,1.527l1.163,2.616l0.146,0.291c0.509,1.236,1.309,1.745,2.472,1.745c1.454,0,2.544-0.945,2.544-2.182 C18.534,12.791,17.88,12.282,16.644,11.919z"></path> </symbol> <symbol id="icon-droplet-first-soundcloud" viewBox="0 0 32 32"> <circle cx="16" cy="16" r="16" fill="#FFF"/> <path style="fill: #C4C4C4; fill: var(--soundcloud-background-color);" d="M16 0C11.6 0 7.6 1.8 4.7 4.7a15.95 15.95 0 000 22.6 15.95 15.95 0 0022.6 0C30.2 24.4 32 20.4 32 16c0-8.8-7.2-16-16-16zM6.1 20.2c0 .2 0 .4-.1.6-.2.1-.3.2-.5.2-.3 0-.5 0-.7-.2s-.3-.4-.3-.7V17c0-.2.1-.4.2-.6.1-.1.3-.2.6-.2.2 0 .4.1.6.2.1.2.2.4.2.6v3.2zm2.4 1.3c0 .2-.1.4-.2.5-.2.1-.4.2-.6.2s-.4 0-.6-.2c-.1-.1-.1-.3-.1-.5v-7.4c0-.2.1-.4.2-.6.1-.2.3-.2.5-.2s.4.1.6.2c.2.1.2.4.2.6v7.4zm2.5.4c0 .2-.1.4-.2.5-.2.1-.4.2-.6.2-.2 0-.4 0-.6-.2-.2-.1-.2-.3-.2-.5v-6.7c0-.2.1-.4.2-.6.1-.2.3-.2.6-.2.2 0 .4.1.6.2.1.2.2.4.2.6v6.7zm2.5 0c0 .4-.3.6-.8.6s-.8-.2-.8-.6V11c0-.3.1-.6.3-.8s.4-.3.7-.2c.4.1.6.4.6 1v10.9zm12.9-.5c-.7.7-1.6 1.1-2.5 1.1h-9.3c-.1 0-.1-.1-.1-.1V10.3c0-.4.1-.6.3-.7.5-.1.9-.2 1.4-.2a6.59 6.59 0 015.3 2.9c.6.9.9 1.9 1 3 1.4-.6 2.9-.3 4 .8 1.4 1.6 1.4 3.9-.1 5.3z" fill="#c4c4c4"/> </symbol> <symbol id="icon-droplet-first-wordpress" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M6.321,12.5c0,2.472,1.454,4.58,3.489,5.598l-2.979-8.069C6.54,10.756,6.321,11.628,6.321,12.5z M16.717,12.209c0-0.799-0.291-1.308-0.51-1.672c-0.291-0.509-0.582-0.944-0.582-1.453c0-0.582,0.438-1.091,1.02-1.091h0.072 c-1.092-1.018-2.545-1.6-4.144-1.6c-2.181,0-4.07,1.091-5.161,2.763c0.146,0,0.291,0,0.363,0c0.654,0,1.672-0.072,1.672-0.072 c0.363,0,0.363,0.436,0.073,0.509c0,0-0.363,0.072-0.728,0.072l2.254,6.688l1.381-4.07l-0.944-2.617 c-0.364,0-0.654-0.072-0.654-0.072c-0.364,0-0.291-0.509,0.072-0.509c0,0,1.018,0.072,1.6,0.072c0.654,0,1.671-0.072,1.671-0.072 c0.363,0,0.363,0.436,0.072,0.509c0,0-0.363,0.072-0.727,0.072l2.254,6.615l0.582-2.035C16.57,13.372,16.717,12.718,16.717,12.209z M12.646,13.009l-1.891,5.38c0.582,0.145,1.163,0.218,1.745,0.218c0.726,0,1.38-0.146,2.035-0.363c0,0,0-0.073-0.072-0.073 L12.646,13.009z M17.951,9.52c0,0.219,0.074,0.437,0.074,0.654c0,0.654-0.146,1.309-0.438,2.181l-1.889,5.452 c1.816-1.09,3.053-3.053,3.053-5.307C18.68,11.41,18.461,10.392,17.951,9.52z"></path> </symbol> <symbol id="icon-droplet-first-digg" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M8.139,9.011v1.672h-1.89v3.78h3.053V9.084H8.139V9.011z M8.139,13.373H7.484V11.7h0.654V13.373z"></path> <path class="inner-figure" fill="currentColor" d="M11.846,10.61v3.78h1.891v0.582h-1.891v1.018h2.544h0.51v-5.38H11.846z M13.736,13.373h-0.654V11.7h0.654 V13.373z"></path> <path class="inner-figure" fill="currentColor" d="M15.553,10.61v3.78h1.891v0.582h-1.891v1.018h2.545h0.509v-5.38H15.553z M17.443,13.373h-0.654V11.7h0.654 V13.373z"></path> <path class="inner-figure" fill="currentColor" d="M 9.956 10.61 L 11.191 10.61 11.191 14.39 9.956 14.39 z "></path> <path class="inner-figure" fill="currentColor" d="M 9.956 9.084 L 11.191 9.084 11.191 10.102 9.956 10.102 z "></path> </symbol> <symbol id="icon-droplet-first-stumbleupon" viewBox="0 0 32 32"> <circle cx="16" cy="16" r="16" fill="#FFF"/> <path style="fill: #C4C4C4; fill: var(--stumbleupon-background-color);" d="M16 0C7.2 0 0 7.2 0 16s7.2 16 16 16 16-7.2 16-16S24.8 0 16 0zm-.1 11.9a1 1 0 00-1 1v5.9c0 2.2-1.8 4.1-4 4.1s-4-1.8-4-4.1v-2.7H10v2.6a1 1 0 002 0v-5.8c0-2.2 1.8-4.1 4-4.1s4.1 1.8 4.1 4.1v1.2l-1.8.6-1.3-.6v-1.2c-.2-.5-.6-1-1.1-1zm9.1 6.9c0 2.2-1.8 4-4.1 4-2.2 0-4-1.8-4-4.1v-2.6l1.3.6 1.8-.6v2.6a1 1 0 002 0v-2.6h3v2.7z" fill="#c4c4c4"/> </symbol> <symbol id="icon-droplet-first-ebay" viewBox="0 0 33 33"> <rect x=".5" y=".5" width="32" height="32" rx="16" style="fill: #C4C4C4; fill: var(--icon-circle-color)"/> <path d="M16.5 33A16.5 16.5 0 1133 16.5 16.52 16.52 0 0116.5 33zm0-32a15.5 15.5 0 000 31 15.5 15.5 0 000-31z" style="fill: #FFF; fill: var(--icon-border-color)"/> <path style="fill: #FFF; fill: var(--letter-e)" d="M7.6 13.68c-1.69 0-3.1.75-3.1 3 0 1.79.95 2.91 3.15 2.91 2.58 0 2.75-1.77 2.75-1.77H9.15a1.51 1.51 0 01-1.58.95 1.75 1.75 0 01-1.83-1.8h4.8v-.65a2.62 2.62 0 00-2.94-2.64zm-.04.84a1.57 1.57 0 011.7 1.62h-3.5a1.68 1.68 0 011.8-1.62z"/> <path style="fill: #FFF; fill: var(--letter-b)" d="M10.53 11.5v6.96c0 .4-.03.95-.03.95h1.2s.04-.4.04-.76a2.53 2.53 0 002.2.96 2.77 2.77 0 002.84-2.97 2.76 2.76 0 00-2.84-2.94 2.4 2.4 0 00-2.18.94V11.5zm3.1 3.06a1.9 1.9 0 011.88 2.08 1.9 1.9 0 11-3.75 0 1.9 1.9 0 011.88-2.08z"/> <path style="fill: #FFF; fill: var(--letter-a)" d="M19.75 13.68c-2.54 0-2.7 1.45-2.7 1.68h1.26s.07-.84 1.36-.84c.83 0 1.48.4 1.48 1.16v.27h-1.48c-1.98 0-3.02.6-3.02 1.82 0 1.2.96 1.85 2.27 1.85a2.59 2.59 0 002.34-1.02c0 .4.03.8.03.8h1.13s-.04-.49-.04-.8v-2.74c0-1.8-1.4-2.18-2.63-2.18zm1.4 3.1v.36a1.7 1.7 0 01-1.94 1.65c-.9 0-1.3-.47-1.3-1.02 0-.99 1.32-1 3.24-1z"/> <path style="fill: #FFF; fill: var(--letter-y)" d="M21.7 13.9h1.42l2.05 4.27 2.04-4.26h1.29l-3.72 7.59h-1.35l1.07-2.12-2.8-5.47z"/> </symbol> <symbol id="icon-droplet-first-pandora" viewBox="0 0 25 25"> <circle class="circle" fill="inherit" cx="12.5" cy="12.5" r="12.5"></circle> <path class="inner-figure" fill="currentColor" d="M18.679,9.375c0-0.509-0.146-0.944-0.363-1.381c-0.654-1.091-1.89-1.6-3.053-1.6H9.084L6.321,18.607h3.635 l0.872-4.072h2.617h0.072c1.454,0,2.835-0.58,3.78-1.525c0.581-0.654,1.091-1.454,1.309-2.472 C18.679,10.103,18.752,9.739,18.679,9.375z M15.189,10.538c-0.072,0.437-0.363,0.8-0.654,1.091 c-0.146,0.072-0.218,0.218-0.363,0.291c-0.363,0.218-0.727,0.363-1.09,0.363H11.41l0.727-3.126l0.072-0.218h1.817 c0.291,0,0.582,0.072,0.8,0.218C15.189,9.448,15.335,9.957,15.189,10.538z"></path> </symbol> <symbol id="icon-droplet-first-deviantart" viewBox="0 0 32 32"> <path style="fill: #C4C4C4; fill: var(--background-color);" d="M31.9 14.4c-.1-1.1-.3-2.1-.6-3.1-.1-.2-.2-.5-.2-.8C29.5 6.1 26 2.6 21.6 1c-.2-.1-.5-.2-.8-.2-1-.3-2-.5-3.1-.6C17.1 0 16.5 0 16 0s-1.1 0-1.6.1c-1.1.1-2.1.3-3.1.6-.3.1-.5.2-.8.3A16 16 0 001 10.5c-.1.2-.2.5-.2.8-.3 1-.5 2-.6 3.1-.2.5-.2 1.1-.2 1.6s0 1.1.1 1.6c.1 1.1.3 2.1.6 3.1.1.3.2.5.3.8a16 16 0 009.5 9.5c.2.1.5.2.8.2 1 .3 2 .5 3.1.6.5.2 1.1.2 1.6.2s1.1 0 1.6-.1c1.1-.1 2.1-.3 3.1-.6.2-.1.5-.2.8-.2a16 16 0 009.5-9.5c.1-.2.2-.5.2-.8.3-1 .5-2 .6-3.1 0-.5.1-1.1.1-1.6s.1-1.2 0-1.7zM22 9.6l-3.5 7 .3.4H22v5h-5.9l-.5.4-1.7 3.3-.4.3H10v-3.6l3.5-7-.2-.4H10v-5h5.9l.5-.4 1.7-3.3.4-.3H22v3.6z" fill="#c4c4c4"/> <path fill="#FFF" d="M22 22h-5.9l-.5.4-1.7 3.3-.4.3H10v-3.6l3.5-7-.2-.4H10v-5h5.9l.5-.4 1.7-3.3.4-.3H22v3.6l-3.5 7 .3.4H22z"/> </symbol> <symbol id="icon-droplet-first-googledrive" viewBox="0 0 33 33"> <rect x=".5" y=".5" width="32" height="32" rx="16" style="fill: #C4C4C4; fill: var(--icon-circle-color)"/> <path d="M16.5 33A16.5 16.5 0 1133 16.5 16.52 16.52 0 0116.5 33zm0-32A15.5 15.5 0 1032 16.5 15.52 15.52 0 0016.5 1z" style="fill: #FFF; fill: var(--icon-border-color)"/> <path style="fill: #FFF; fill: var(--left-line)" d="M13.35 8h6.3l6.39 10.74h-6.3L13.35 8z"/> <path style="fill: #FFF; fill: var(--right-line)" d="M6.5 19.63L9.65 25l6.1-10.29-3.23-5.37-6.02 10.3z"/> <path style="fill: #FFF; fill: var(--bottom-line)" d="M11.22 25l3.15-5.37H26.5L23.35 25H11.22z"/> </symbol> <symbol id="icon-droplet-first-del_icio_us" viewBox="0 0 32 32"> <rect width="32" height="32" rx="16" style="fill: #C4C4C4; fill: var(--icon-circle-color)"/> <path d="M8 8h8v8H8z" style="fill: #E5E5E5; fill: var(--icon-border-color)"/> <path d="M8 16h8v8H8z" style="fill: #FFF; fill: var(--right-top-icon)"/> <path d="M16 16h8v8h-8z" style="fill: #E5E5E5; fill: var(--right-bottom-icon)"/> <path d="M16 8h8v8h-8z" style="fill: #FFF; fill: var(--left-bottom-icon)"/> </symbol> <symbol id="icon-droplet-first-tiktok" viewBox="0 0 32 32"> <rect width="32" height="32" rx="16" style="fill: #C4C4C4; fill: var(--icon-circle-color)"/> <path style="fill: #FFF; fill: var(--logo-red-shadow)" d="M20.34 13.22A8.08 8.08 0 0025 14.69v-3.3a4.8 4.8 0 01-.98-.1v2.6a8.08 8.08 0 01-4.66-1.47v6.7a6.13 6.13 0 01-6.18 6.08 6.23 6.23 0 01-3.44-1.03A6.22 6.22 0 0014.16 26a6.13 6.13 0 006.18-6.07v-6.71zm1.2-3.32a4.54 4.54 0 01-1.2-2.68V6.8h-.93a4.6 4.6 0 002.14 3.1zM11.9 21.6a2.74 2.74 0 01-.58-1.68 2.8 2.8 0 012.83-2.78 2.88 2.88 0 01.86.13V13.9a6.35 6.35 0 00-.98-.06v2.62a2.88 2.88 0 00-.86-.13 2.8 2.8 0 00-2.83 2.78 2.77 2.77 0 001.56 2.48z" fill-rule="evenodd"/> <path style="fill: #FFF; fill: var(--main-logo)" d="M19.36 12.42a8.08 8.08 0 004.66 1.47v-2.6a4.7 4.7 0 01-2.47-1.39 4.6 4.6 0 01-2.14-3.1h-2.43v13.13a2.8 2.8 0 01-2.83 2.76 2.84 2.84 0 01-2.25-1.1 2.77 2.77 0 01-1.56-2.47 2.8 2.8 0 012.83-2.78 2.88 2.88 0 01.86.13v-2.62a6.13 6.13 0 00-6.05 6.08 6 6 0 001.76 4.24 6.23 6.23 0 003.44 1.03 6.13 6.13 0 006.18-6.07z" fill-rule="evenodd"/> <path style="fill: #FFF; fill: var(--logo-blue-shadow)" d="M24.02 11.3v-.7a4.72 4.72 0 01-2.47-.7 4.7 4.7 0 002.47 1.4zm-4.6-4.5l-.06-.38V6H16v13.13a2.8 2.8 0 01-2.83 2.76 2.85 2.85 0 01-1.27-.3 2.84 2.84 0 002.25 1.1 2.8 2.8 0 002.83-2.76V6.8zm-5.4 7.05v-.74a6.33 6.33 0 00-.84-.06A6.13 6.13 0 007 19.13a6.04 6.04 0 002.74 5.04 6 6 0 01-1.76-4.24 6.13 6.13 0 016.05-6.08z" fill-rule="evenodd"/> </symbol> <symbol id="icon-droplet-first-twitch" viewBox="0 0 32 32"> <path d="M16 0a15.98 15.98 0 1016 16A15.95 15.95 0 0016 0z"/> <path d="M8.27 6.5L7 9.76V23.1h4.5v2.41h2.53l2.4-2.41h3.65L25 18.13V6.5zM23.3 17.28L20.5 20.1H16l-2.4 2.41v-2.4H9.82V8.2h13.5zm-2.81-5.82v4.96h-1.69v-4.96zm-4.5 0v4.96h-1.69v-4.96z" fill="#fff" fill-rule="evenodd"/> </symbol> <symbol id="icon-droplet-first-telegram" viewBox="0 0 32 32"> <path d="M16 0C11.8 0 7.7 1.7 4.7 4.7S0 11.8 0 16s1.7 8.3 4.7 11.3S11.8 32 16 32s8.3-1.7 11.3-4.7S32 20.2 32 16s-1.7-8.3-4.7-11.3S20.2 0 16 0z"/> <path fill="#fff" d="m24 8.8-3 15.3s-.1.7-1 .7c-.5 0-.7-.2-.7-.2l-6.5-5.4-3.2-1.6-4.1-1.1s-.7-.2-.7-.8c0-.5.7-.7.7-.7l17.1-6.8s.5-.2.9-.2c.2 0 .5.1.5.4v.4z"/> <path fill="#d9d9d9" d="m15.2 21.2-2.7 2.7s-.1.1-.3.1H12l.8-4.8 2.4 2z"/> <path fill="#e6e6e6" d="M20.7 11.4c-.1-.2-.4-.2-.6-.1L9.6 17.6s1.7 4.7 1.9 5.5c.3.8.5.9.5.9l.8-4.8 7.9-7.3c.1-.1.2-.4 0-.5z"/> </symbol> <symbol id="iconDecrease" viewBox="0 0 14 2"> <path d="M0 0h14v2H0z"/> </symbol> <symbol id="iconIncrease" viewBox="0 0 14 14"> <path d="M0 6h14v2H0z"/> <path d="M6 0h2v14H6z"/> </symbol> <symbol id="reviewStars" viewBox="0 0 35 35"> <path fill="currentColor" d="M17.5 0l-4.131 13.369H0l10.816 8.262L6.684 35 17.5 26.738 28.315 35l-4.131-13.369L35 13.369H21.631L17.5 0z"/> <path d="M17.5 0v26.738L6.684 35l4.132-13.369L0 13.369h13.369L17.5 0z"/> </symbol> <symbol id="carouselArrowIcon" viewBox="0 0 40 40"> <path fill="#000" fill-rule="evenodd" d="M27.123 36a2.435 2.435 0 0 1-1.752-.741L10.4 19.999 25.371 4.74a2.442 2.442 0 0 1 3.503 0 2.558 2.558 0 0 1 0 3.57L17.412 20l11.462 11.688a2.558 2.558 0 0 1 0 3.57 2.435 2.435 0 0 1-1.751.742Z" clip-rule="evenodd"/> <path fill="#fff" d="m25.371 35.259.359-.349-.002-.001-.357.35ZM10.4 19.999l-.357-.35L9.7 20l.343.35.357-.35ZM25.371 4.74l.357.35-.357-.35Zm3.503 3.57-.357-.35.357.35ZM17.412 20l-.357-.35-.344.35.344.35.357-.35Zm11.462 11.688-.357.35.357-.35Zm0 3.57-.357-.35-.001.002.358.349Zm-1.751.242a1.935 1.935 0 0 1-1.393-.59l-.717.698c.58.595 1.345.892 2.11.892v-1Zm-1.395-.591-14.971-15.26-.714.7 14.972 15.26.713-.7Zm-14.971-14.56L25.728 5.09l-.713-.7-14.972 15.26.714.7ZM25.728 5.09a1.942 1.942 0 0 1 2.789 0l.714-.7a2.942 2.942 0 0 0-4.216 0l.713.7Zm2.789 0a2.058 2.058 0 0 1 0 2.87l.714.701a3.058 3.058 0 0 0 0-4.27l-.714.7Zm0 2.87L17.055 19.65l.714.7L29.23 8.66l-.714-.7ZM17.055 20.35l11.462 11.688.714-.7L17.769 19.65l-.714.7Zm11.462 11.688a2.058 2.058 0 0 1 0 2.87l.714.701a3.058 3.058 0 0 0 0-4.27l-.714.7Zm-.001 2.872c-.385.395-.889.59-1.393.59v1c.764 0 1.529-.297 2.11-.892l-.717-.698Z"/> </symbol> <symbol id="elementLoader" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid"> <circle cx="50" cy="50" r="21.9" fill="none" stroke="#3aaa35" stroke-width="2"> <animate attributeName="r" calcMode="spline" values="0;40" keyTimes="0;1" dur="2" keySplines="0 0.2 0.8 1" begin="-1s" repeatCount="indefinite"/> <animate attributeName="opacity" calcMode="spline" values="1;0" keyTimes="0;1" dur="2" keySplines="0.2 0 0.8 1" begin="-1s" repeatCount="indefinite"/> </circle> <circle cx="50" cy="50" r="38.55" fill="none" stroke="#e0e0e0" stroke-width="2"> <animate attributeName="r" calcMode="spline" values="0;40" keyTimes="0;1" dur="2" keySplines="0 0.2 0.8 1" begin="0s" repeatCount="indefinite"/> <animate attributeName="opacity" calcMode="spline" values="1;0" keyTimes="0;1" dur="2" keySplines="0.2 0 0.8 1" begin="0s" repeatCount="indefinite"/> </circle> </symbol> <symbol id="iconVideoPrev" viewBox="0 0 10 12"> <path d="M.5.88v10.24L9.03 6 .5.88z"/> </symbol> <symbol id="buyButtonLoader" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid"> <circle cx="50" cy="50" r="46" fill="none" stroke="currentColor" stroke-dasharray="226.1946710584651 77.39822368615503" stroke-width="8"/> </symbol> <symbol id="zpIconArrow" viewBox="0 0 40 40"> <path fill="#000" fill-rule="evenodd" d="M12.877 4c.635 0 1.27.246 1.752.741L29.6 20.001 14.629 35.26a2.442 2.442 0 0 1-3.503 0 2.558 2.558 0 0 1 0-3.57L22.588 20 11.126 8.312a2.558 2.558 0 0 1 0-3.57A2.435 2.435 0 0 1 12.877 4Z" clip-rule="evenodd"/> <path fill="#fff" d="m14.629 4.741-.359.349.002.001.357-.35ZM29.6 20.001l.357.35.343-.35-.343-.35-.357.35ZM14.629 35.26l-.357-.35.357.35Zm-3.503-3.57.357.35-.357-.35ZM22.588 20l.357.35.344-.35-.344-.35-.357.35ZM11.126 8.312l.357-.35-.357.35Zm0-3.57.357.35.001-.002-.358-.349Zm1.751-.242c.505 0 1.009.195 1.393.59l.717-.698a2.935 2.935 0 0 0-2.11-.892v1Zm1.395.591 14.971 15.26.714-.7L14.985 4.39l-.713.7Zm14.971 14.56L14.272 34.91l.713.7 14.972-15.26-.714-.7ZM14.272 34.91a1.941 1.941 0 0 1-2.789 0l-.714.7a2.942 2.942 0 0 0 4.216 0l-.713-.7Zm-2.789 0a2.058 2.058 0 0 1 0-2.87l-.714-.701a3.058 3.058 0 0 0 0 4.27l.714-.7Zm0-2.87 11.462-11.69-.713-.7-11.463 11.69.714.7Zm11.462-12.39L11.483 7.962l-.714.7L22.232 20.35l.713-.7ZM11.483 7.962a2.058 2.058 0 0 1 0-2.87l-.714-.701a3.058 3.058 0 0 0 0 4.27l.714-.7Zm.001-2.872c.385-.395.889-.59 1.393-.59v-1c-.764 0-1.529.297-2.109.892l.716.698Z"/> </symbol> <symbol id="videoPlaceholderIcon" viewBox="0 0 110 110"> <path fill-rule="evenodd" d="M55 110a55 55 0 1155-55 55.06 55.06 0 01-55 55M55 8.4A46.6 46.6 0 10101.6 55 46.65 46.65 0 0055 8.4"/> <path fill-rule="evenodd" d="M77.39 56.08L41.22 76.97V35.2l36.17 20.88z"/> </symbol> <symbol viewBox="0 0 10 6" id="navbarChevronDown"> <path fill-rule="evenodd" clip-rule="evenodd" d="M1.6 0L0.666667 0.888889L5.33333 5.33333L10 0.888889L9.06667 0L5.33333 3.55551L1.6 0Z" fill="currentColor"/> </symbol> </defs> </svg>{% if zp_current_entity_metafields['config'].type == 'json' and zp_current_entity_metafields['config'].value['page_last_sync_date'] != blank %}{% assign zp_object_settings = zp_current_entity_metafields['config'].value %}{% elsif zp_current_entity_metafields[zp_entity_data_metafield_key].type == 'json' %}{% assign zp_object_settings = zp_current_entity_metafields[zp_entity_data_metafield_key].value %}{% else %}{% assign zp_object_settings = zp_current_entity_metafields[zp_entity_data_metafield_key] %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['page_with_default_settings'] %}{% if zp_setting_value == 'true' %}{% assign zp_entity_with_default_styles = true %}{% else %}{% assign zp_entity_with_default_styles = false %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['page_fixed_layout'] %}{% if zp_setting_value == 'true' %}{% assign zp_entity_fixed_layout = true %}{% else %}{% assign zp_entity_fixed_layout = false %}{% endif %}{% assign zp_setting_value = nil %}{% if contentprt == 'footer' %}{% assign zp_current_entity_content = '' | append: zp_current_entity_metafields['footercontent'] %}{% assign zp_product_selector_suffix = contentprt %}{% elsif contentprt == 'header' %}{% assign zp_current_entity_content = '' | append: zp_current_entity_metafields['headercontent'] %}{% assign zp_product_selector_suffix = contentprt %}{% elsif contentprt == 'full' %}{% if zp_product_page_entity_tag.size > 0 %}{% assign zp_current_entity_content = '' | append: zp_product_page_entity_tag %}{% else %}{% assign zp_current_entity_content = '' | append: zp_product_page_entity_content %}{% endif %}{% assign zp_product_page_entity_content = nil %}{% assign zp_product_page_entity_tag = nil %}{% assign zp_product_selector_suffix = contentprt %}{% endif %}{% assign zp_current_entity_icons = zp_current_entity_metafields['icons'] %}{% unless zp_current_entity_icons == blank %}<svg style="display:none!important" xmlns="http://www.w3.org/2000/svg"><defs>{{ zp_current_entity_icons }}</defs></svg>{% endunless %}{% if zp_alternative_entity_present %}{% assign zp_current_entity_icons = zp_alternative_entity_metafields['icons'] %}{% unless zp_current_entity_icons == blank %}<svg style="display:none!important" xmlns="http://www.w3.org/2000/svg"><defs>{{ zp_current_entity_icons }}</defs></svg>{% endunless %}{% endif %}{% assign zp_current_entity_icons = nil %}{% assign zp_app_integrations = '' | append: zp_shop_metafields['appintegrations'] | split: ',' %}{% assign zp_original_product = product %}{% if zp_app_integrations contains 'recharge' %}{% assign zp_integrate_with_recharge = true %}{% else %}{% assign zp_integrate_with_recharge = false %}{% endif %}{% assign zp_shopify_options_selector = false %}{% assign zp_unit_price_enabled = false %}{% assign zp_settings_keys = zp_shop_metafields['blockssettings'] %}{% if zp_settings_keys != blank %}{% assign zp_use_json_blocks_settings = true %}{% if zp_settings_keys.type == 'json' %}{% assign zp_settings_keys = zp_settings_keys.value %}{% endif %}{% else %}{% assign zp_use_json_blocks_settings = false %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% assign zp_object_settings_data = '' | append: zp_shop_metafields['buyboxessettings'] | strip %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% endif %}{% for zp_setting_key_data in zp_settings_keys %}{% if zp_use_json_blocks_settings %}{% assign zp_setting_key = zp_setting_key_data | first %}{% assign zp_setting_value = zp_setting_key_data | last | strip %}{% else %}{% assign zp_setting_key = zp_setting_key_data %}{% assign zp_setting_value = '' | append: zp_settings_values[forloop.index0] | strip %}{% endif %}{% if zp_setting_key == 'show_unavailable_variants' %}{% if zp_setting_value == 'true' %}{% assign zp_shopify_options_selector = true %}{% endif %}{% elsif zp_setting_key == 'show_unit_price' %}{% if zp_setting_value == 'true' %}{% assign zp_unit_price_enabled = true %}{% endif %}{% endif %}{% endfor %}{% if zp_app_integrations contains 'bestcurrencyconverter' %}{% assign zp_amount_variants = 'amount|amount_no_decimals|amount_with_comma_separator|amount_no_decimals_with_comma_separator|amount_with_space_separator|amount_no_decimals_with_space_separator|amount_with_apostrophe_separator|amount_with_period_and_space_separator' | split: '|' %}{% assign zp_amount_format_template = shop.money_format %}{% assign zp_open_curly_bracket = '{' %}{% assign zp_closed_curly_bracket = '}' %}{% assign zp_amount_template = '[#!amount!#]' %}{% for zp_amount_variant in zp_amount_variants %}{% assign zp_format_parts = zp_amount_format_template | split: zp_amount_variant %}{% assign zp_amount_format_template = '' %}{% assign zp_is_start_init = false %}{% for zp_part in zp_format_parts %}{% if zp_part.size >= 2 %}{% assign zp_tmp = zp_part | strip | split: '' %}{% assign zp_first_symbol = zp_tmp[0] %}{% assign zp_second_symbol = zp_tmp[1] %}{% if zp_is_start_init == true and zp_first_symbol == zp_closed_curly_bracket and zp_second_symbol == zp_closed_curly_bracket %}{% assign zp_is_start_init = false %}{% assign zp_amount_format_template = zp_amount_format_template | rstrip %}{% assign zp_start_slice = 0 %}{% assign zp_end_slice = zp_amount_format_template.size | minus: 2 %}{% if zp_end_slice < 0 %}{% assign zp_end_slice = 0 %}{% endif %}{% assign zp_amount_format_template = zp_amount_format_template | slice: zp_start_slice, zp_end_slice %}{% assign zp_tmp_part = zp_part | lstrip %}{% assign zp_start_slice = 2 %}{% assign zp_end_slice = zp_tmp_part.size | minus: zp_start_slice %}{% if zp_end_slice < 0 %}{% assign zp_end_slice = 0 %}{% endif %}{% assign zp_tmp_part = zp_tmp_part | slice: zp_start_slice, zp_end_slice %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_amount_template | append: zp_tmp_part %}{% else %}{% assign zp_is_start_init = false %}{% if zp_amount_format_template.size > 0 %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_amount_variant %}{% endif %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_part %}{% endif %}{% assign zp_last_index = zp_tmp.size | minus: 1 %}{% assign zp_second_last_index = zp_tmp.size | minus: 2 %}{% if zp_last_index >= 0 %}{% assign zp_last_symbol = zp_tmp[zp_last_index] %}{% else %}{% assign zp_last_symbol = '' %}{% endif %}{% if zp_second_last_index >= 0 %}{% assign zp_second_last_symbol = zp_tmp[zp_second_last_index] %}{% else %}{% assign zp_second_last_symbol = '' %}{% endif %}{% if zp_last_symbol == zp_open_curly_bracket and zp_second_last_symbol == zp_open_curly_bracket %}{% assign zp_is_start_init = true %}{% endif %}{% if zp_is_start_init == false and zp_last_symbol == zp_open_curly_bracket and zp_second_last_symbol == zp_open_curly_bracket %}{% assign zp_is_start_init = true %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_part %}{% endif %}{% else %}{% assign zp_is_start_init = false %}{% assign zp_amount_format_template = zp_amount_format_template | append: zp_part | append: zp_amount_variant %}{% endif %}{% endfor %}{% endfor %}{% assign zp_amount_variants = '' %}{% assign zp_format_parts = '' %}{% endif %}{%- if zp_integrate_with_recharge %}<style>[data-zp-add-to-cart-form] .rc-container-wrapper{display:none!important}</style>{% endif -%}{% if zp_entity_with_multiparts %}{% for zp_part in zp_current_entity.metafields['zipifypagesparts'] %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_part[1] %}{% endfor %}{% endif %}{% assign zp_gb_ids = zp_current_entity_metafields['config'].value['globalblocks'] %}{% for zp_gb_id in zp_gb_ids %}{% capture zp_gb_content %}{% assign zp_gb_snippet_name = 'gb-' | append: zp_gb_id | append: '.zipifypages' %}{% include zp_gb_snippet_name %}{% endcapture %}{% capture zp_gb_tag_name %}<zp_gb_{{ zp_gb_id }}></zp_gb_{{ zp_gb_id }}>{% endcapture %}{% assign zp_gb_error = 'Could not find asset snippets/' | append: zp_gb_snippet_name | append: '.liquid' %}{% if zp_gb_content contains zp_gb_error %}{% assign zp_gb_content = '' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_gb_tag_name, zp_gb_content %}{% else %}{% assign zp_gb_styles =zp_gb_content | split: '<!--ZP_SEPARATOR-->' | first %}{% assign zp_gb_content = zp_gb_content | remove_first: zp_gb_styles | remove: '<!--ZP_SEPARATOR-->' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_gb_tag_name, zp_gb_content | prepend: zp_gb_styles %}{% endif %}{% endfor %}{% if zp_split_single_page_render == true %}{% assign zp_gb_ids = zp_alternative_entity_metafields['config'].value['globalblocks'] %}{% for zp_gb_id in zp_gb_ids %}{% capture zp_gb_content %}{% assign zp_gb_snippet_name = 'gb-' | append: zp_gb_id | append: '.zipifypages' %}{% include zp_gb_snippet_name %}{% endcapture %}{% capture zp_gb_tag_name %}<zp_gb_{{ zp_gb_id }}></zp_gb_{{ zp_gb_id }}>{% endcapture %}{% assign zp_gb_error = 'Could not find asset snippets/' | append: zp_gb_snippet_name | append: '.liquid' %}{% if zp_gb_content contains zp_gb_error %}{% assign zp_gb_content = '' %}{% assign zp_alternative_entity_content = zp_alternative_entity_content | replace: zp_gb_tag_name, zp_gb_content %}{% else %}{% assign zp_gb_styles =zp_gb_content | split: '<!--ZP_SEPARATOR-->' | first %}{% assign zp_gb_content = zp_gb_content | remove_first: zp_gb_styles | remove: '<!--ZP_SEPARATOR-->' %}{% assign zp_alternative_entity_content = zp_alternative_entity_content | replace: zp_gb_tag_name, zp_gb_content | prepend: zp_gb_styles %}{% endif %}{% endfor %}{% endif %}{% assign zp_split_parts_separator = '' %}{% if zp_split_single_page_render == true %}{% assign zp_split_parts_start = '<zp__script type="text/template" id="zp-main-content">' %}{% assign zp_split_parts_end = '</zp__script>' %}{% assign zp_split_parts_separator = '</zp__script><zp__script type="text/template" id="zp-alternative-content">' %}{% assign zp_replacement_key = '</script' %}{% assign zp_replacement_value = '<\/script' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_replacement_key, zp_replacement_value %}{% assign zp_current_entity_content = zp_split_parts_start | append: zp_current_entity_content | append: zp_split_parts_separator %}{% for zp_part in zp_alternative_entity.metafields['zipifypagesparts'] %}{% assign zp_alternative_entity_content = zp_alternative_entity_content | append: zp_part[1] %}{% endfor %}{% assign zp_booster_coupons_left = '' | append: zp_alternative_entity_metafields['boostercouponsleft'] | strip | default: '0' %}{% assign zp_booster_coupons_left = 0 | plus: zp_booster_coupons_left %}{% if zp_booster_coupons_left > 0 %}{% assign zp_booster_available_block_class = '' %}{% assign zp_booster_unavailable_block_class = 'hidden' %}{% else %}{% assign zp_booster_available_block_class = 'hidden' %}{% assign zp_booster_unavailable_block_class = '' %}{% endif %}{% assign zp_alternative_entity_content = zp_alternative_entity_content | replace: '<zp_booster_coupons_count></zp_booster_coupons_count>', zp_booster_coupons_left | replace: '__zp_booster_available_block_class__', zp_booster_available_block_class | replace: '__zp_booster_unavailable_block_class__', zp_booster_unavailable_block_class | replace: zp_replacement_key, zp_replacement_value %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_alternative_entity_content %}{% assign zp_alternative_entity_content = '' %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_split_parts_end %}{% endif %}{% assign zp_entity_product_tags = 'zp_rch_product6,zp_rch_product5,zp_rch_product4,zp_rch_product3,zp_rch_product2,zp_rch_product1,zp_bvo_product3,zp_bvo_product2,zp_bvo_product1,zp_bvh_product3,zp_bvh_product2,zp_bvh_product1,zp_bv_product3,zp_bv_product2,zp_bv_product1,zp_product3,zp_product2,zp_product1,zp_ofrbox_product,zp_product' | split: ',' %}{% assign zp_entity_product_tags_views = 'recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,recharge-subscription-view.zipifypages,best-value-offer-view.zipifypages,best-value-offer-view.zipifypages,best-value-offer-view.zipifypages,best-value-horizontal-view.zipifypages,best-value-horizontal-view.zipifypages,best-value-horizontal-view.zipifypages,best-value-view.zipifypages,best-value-view.zipifypages,best-value-view.zipifypages,three-products-view.zipifypages,three-products-view.zipifypages,three-products-view.zipifypages,offer-box-view.zipifypages,product-view.zipifypages' | split: ',' %}{% assign zp_product_iterator = 0 %}{% assign zp_cross_sell_product_iterator = 0 %}{% if zp_product_selector_suffix == nil %}{% assign zp_product_selector_suffix = '' %}{% endif %}{% assign zp_main_product_selector = 'zpproductselector' | append: zp_product_selector_suffix %}{% assign zp_main_product_selector_suffix = '-wrapper' %}{% assign zp_opened_cross_sell_tag = '<zpdcsproduct ' %}{% assign zp_closed_cross_sell_tag = '</zpdcsproduct>' %}{% assign zp_shw_vrntsslctr_lnktps = 'cart,checkout,cart_current_page,cart_external' | split: ',' %}{% assign zp_include_bold_snippets_link_types = 'cart,checkout,cart_current_page,cart_external' | split: ',' %}{% assign zp_incl_boldcmnsnpt = false %}{% if zp_use_product_markup %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '</zp_rch_product6>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product6>' | replace: '</zp_rch_product5>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product5>' | replace: '</zp_rch_product4>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product4>' | replace: '</zp_rch_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product3>' | replace: '</zp_rch_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product2>' | replace: '</zp_rch_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_rch_product1>' | replace: '</zp_bvo_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvo_product3>' | replace: '</zp_bvo_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvo_product2>' | replace: '</zp_bvo_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvo_product1>' | replace: '</zp_bvh_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvh_product3>' | replace: '</zp_bvh_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvh_product2>' | replace: '</zp_bvh_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bvh_product1>' | replace: '</zp_bv_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bv_product3>' | replace: '</zp_bv_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bv_product2>' | replace: '</zp_bv_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_bv_product1>' | replace: '</zp_product3>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_product3>' | replace: '</zp_product2>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_product2>' | replace: '</zp_product1>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_product1>' | replace: '</zp_ofrbox_product>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_ofrbox_product>' | replace: '</zp_product>', '<zpprdtmpmrkp></zpprdtmpmrkp></zp_product>' | replace: '</zpdproduct>', '<zpprdtmpmrkp></zpprdtmpmrkp></zpdproduct>' | replace: '</zpdprdct', '<zpprdtmpmrkp></zpprdtmpmrkp></zpdprdct' | replace_first: '<zpprdtmpmrkp></zpprdtmpmrkp>', '<zpprdmrkp></zpprdmrkp>' | remove: '<zpprdtmpmrkp></zpprdtmpmrkp>' %}{% endif %}{% for zp_entity_product_tag in zp_entity_product_tags %}{% assign zp_opened_product_tag = '<' | append: zp_entity_product_tag | append: '>' %}{% unless zp_current_entity_content contains zp_opened_product_tag %}{% continue %}{% endunless %}{% assign zp_entity_product_view = zp_entity_product_tags_views[forloop.index0] %}{% assign zp_closed_product_tag = '</' | append: zp_entity_product_tag | append: '>' %}{% assign zp_entity_products = zp_current_entity_content | split: zp_opened_product_tag %}{% assign zp_current_entity_content = '' %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% for zp_part in zp_entity_products %}{% assign zp_product_settings_content = zp_part | append: ' ' | split: zp_closed_product_tag %}{% if zp_product_settings_content.size < 2 %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_part %}{% else %}{% assign zp_object_settings_data = zp_product_settings_content[0] %}{% if zp_object_settings_data contains '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_object_settings_data = zp_object_settings_data | remove: '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_display_product_markup = true %}{% else %}{% assign zp_display_product_markup = false %}{% endif %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% assign zp_product_settings_keys = zp_settings_keys %}{% assign zp_product_settings_values = zp_settings_values %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_product_image_position = '' %}{% assign zp_product_image = '' %}{% assign zp_product_image_alt = nil %}{% assign zp_product_image_custom_srcset = '' %}{% assign zp_product_image_attributes = '' %}{% assign zp_product_link_type = 'cart' %}{% assign zp_redirect_product_url = routes.cart_url %}{% assign zp_selected_variants = '' %}{% assign zp_product_discount_data = '' %}{% assign zp_product_custom_title = '' %}{% assign zp_product_custom_description = '' %}{% assign zp_three_products_blocks_count = '3' %}{% assign zp_product_element_id = nil %}{% assign zp_product_type = 'base' %}{% assign zp_bv_lbluptxt_value = '' %}{% assign zp_product_image_type = 'product' %}{% assign zp_product_unit_price_tag_included = false %}{% assign zp_product_image_lazy_load = false %}{% assign zp_product_calculate_bundle_price = false %}{% assign zp_product_default_quantity = '1' %}{% assign zp_product_sold_out_view_enabled = false %}{% assign zp_show_product_image_label = false %}{% assign zp_include_product_button_main_classes = true %}{% assign zp_include_product_button_alignment_classes = true %}{% assign zp_bv_product_static_compare_price = '' %}{% assign zp_bv_product_static_price = '' %}{% assign zp_bv_product_header_title = '' %}{% assign zp_bv_product_additional_description = '' %}{% assign zp_bv_product_description = '' %}{% assign zp_bv_product_additional_header_title = '' %}{% assign zp_product_first_description = '' %}{% assign zp_product_second_description = '' %}{% assign zp_product_static_body_html = '' %}{% assign zp_product_static_title = '' %}{% for zp_setting_key in zp_product_settings_keys %}{% assign zp_prd_stng_vl = '' | append: zp_product_settings_values[forloop.index0] %}{% if zp_setting_key == 'handle' %}{% assign zp_product_handle = zp_prd_stng_vl %}{% elsif zp_setting_key == 'imgpos' %}{% assign zp_product_image_position = zp_prd_stng_vl %}{% elsif zp_setting_key == 'slctvrnt' %}{% assign zp_selected_variants = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdimgsrcset' %}{% assign zp_product_image_custom_srcset = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'prdimg' %}{% assign zp_product_image = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdimgalt' %}{% assign zp_product_image_alt = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdurl' %}{% assign zp_redirect_product_url = zp_prd_stng_vl | replace: '&amp;', '&' | url_escape | replace: '%23', '#' | replace: '%25', '%' %}{% elsif zp_setting_key == 'prdbtnltp' %}{% assign zp_product_link_type = zp_prd_stng_vl %}{% elsif zp_setting_key == 'shwqty' %}{% if zp_prd_stng_vl == 'true' or zp_prd_stng_vl == '1' %}{% assign zp_product_show_quantity = true %}{% else %}{% assign zp_product_show_quantity = false %}{% endif %}{% elsif zp_setting_key == 'prddscntdata' %}{% assign zp_product_discount_data = zp_prd_stng_vl | split: ':' %}{% elsif zp_setting_key == 'prdtype' %}{% assign zp_product_type = zp_prd_stng_vl %}{% elsif zp_setting_key == 'ttl1cont' %}{% assign zp_bv_product_additional_header_title = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'ttl2cont' %}{% assign zp_bv_product_header_title = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'ttl3cont' %}{% assign zp_bv_product_static_compare_price = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'ttl4cont' %}{% assign zp_bv_product_static_price = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'ttl5cont' %}{% assign zp_bv_product_additional_description = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'txtcont' %}{% assign zp_bv_product_description = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'txt1cont' %}{% assign zp_product_first_description = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'txt2cont' %}{% assign zp_product_second_description = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'blckscnt' %}{% assign zp_three_products_blocks_count = zp_prd_stng_vl %}{% elsif zp_setting_key == 'lbluptxt' %}{% assign zp_bv_lbluptxt_value = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdimgtp' %}{% assign zp_product_image_type = zp_prd_stng_vl %}{% elsif zp_setting_key == 'prdbdhtml' %}{% assign zp_product_custom_description = zp_prd_stng_vl %}{% assign zp_product_static_body_html = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'prdttl' %}{% assign zp_product_custom_title = zp_prd_stng_vl %}{% assign zp_product_static_title = zp_prd_stng_vl | strip %}{% elsif zp_setting_key == 'imgimgattr' %}{% assign zp_product_image_attributes = zp_prd_stng_vl %}{% elsif zp_setting_key == 'shwimglbl' %}{% if zp_prd_stng_vl == 'true' %}{% assign zp_show_product_image_label = true %}{% endif %}{% elsif zp_setting_key == 'eid' %}{% assign zp_product_element_id = zp_prd_stng_vl %}{% elsif zp_setting_key == 'qty' %}{% assign zp_product_default_quantity = zp_prd_stng_vl %}{% elsif zp_setting_key == 'unprcincl' %}{% if zp_prd_stng_vl == '1' %}{% assign zp_product_unit_price_tag_included = true %}{% endif %}{% elsif zp_setting_key == 'lzld' %}{% if zp_prd_stng_vl == '1' %}{% assign zp_product_image_lazy_load = true %}{% endif %}{% elsif zp_setting_key == 'sldtv' %}{% if zp_prd_stng_vl == '1' %}{% assign zp_product_sold_out_view_enabled = true %}{% endif %}{% elsif zp_setting_key == 'bndlprc' %}{% if zp_prd_stng_vl == '1' %}{% assign zp_product_calculate_bundle_price = true %}{% endif %}{% elsif zp_setting_key == 'prdbtninmncl' %}{% if zp_prd_stng_vl == '0' %}{% assign zp_include_product_button_main_classes = false %}{% endif %}{% elsif zp_setting_key == 'prdbtninalcl' %}{% if zp_prd_stng_vl == '0' %}{% assign zp_include_product_button_alignment_classes = false %}{% endif %}{% endif %}{% endfor %}{% assign zp_selected_variants = zp_selected_variants | split: ',' %}{% assign product = all_products[zp_product_handle] %}{% assign zp_product_id_size = '' | append: product.id | size %}{% assign zp_product_found = true %}{% if zp_product_id_size < 1 %}{% assign product = nil %}{% assign zp_product_found = false %}{% endif %}{% if zp_product_link_types != blank %}{% assign zp_shw_vrntsslctr = false %}{% for zp_link_type in zp_product_link_types %}{% if zp_shw_vrntsslctr_lnktps contains zp_link_type %}{% assign zp_shw_vrntsslctr = true %}{% break %}{% endif %}{% endfor %}{% elsif zp_shw_vrntsslctr_lnktps contains zp_product_link_type %}{% assign zp_shw_vrntsslctr = true %}{% else %}{% assign zp_shw_vrntsslctr = false %}{% endif %}{% assign zp_show_variants_selectors = zp_shw_vrntsslctr %}{% assign zp_enable_subscription_widget = zp_shw_vrntsslctr %}{% if zp_shw_vrntsslctr %}{% assign zp_incl_boldsnpts = true %}{% else %}{% assign zp_incl_boldsnpts = false %}{% endif %}{% if zp_incl_boldsnpts and zp_app_integrations contains 'productoptionsbybold' %}{% assign zp_intgrt_wboldprdopts = true %}{% else %}{% assign zp_intgrt_wboldprdopts = false %}{% endif %}{% if zp_incl_boldsnpts and zp_app_integrations contains 'quantitybreaksbybold' %}{% assign zp_intgrt_wboldqtbrk = true %}{% else %}{% assign zp_intgrt_wboldqtbrk = false %}{% endif %}{% if zp_app_integrations contains 'subscriptionsbybold' %}{% assign zp_incl_boldsbsrpns = true %}{% else %}{% assign zp_incl_boldsbsrpns = false %}{% endif %}{% capture zp_product_snippet %}{% assign zp_product_iterator = zp_product_iterator | plus: 1 %}{% assign zp_product_selector = zp_main_product_selector | append: zp_product_iterator %}{% assign zp_product_wrapper_selector = zp_product_selector | append: zp_main_product_selector_suffix %}{% include zp_entity_product_view with product %}{% endcapture %}{% assign zp_object_settings_data = 'txtblattr:--:txtblclass:##:class:##::|~|:ttl5blattr:--:ttl5blclass:##:class:##::|~|:ttl4blattr:--:ttl4blclass:##:class:##::|~|:ttl3blattr:--:ttl3blclass:##:class:##::|~|:ttl2blattr:--:ttl2blclass:##:class:##::|~|:prdttlcss:--:prdttlclass:##:class:##::|~|:prdlbtnattr:--:prdlbtnclass:##:class:##::|~|:prdattr:--:prdclass:##:class:##::|~|:prdbtnattr:--:prdbtnclass:##:class:##::|~|:prdblattr:--:prdblclass:##:class:##::|~|:mcontclstpadd:--:mcontclstclass:##:class:##::|~|:mcontattr:--:mcontclass:##:class:##::|~|:lblblattr:--:lblblclass:##:class:##::|~|:imgblattr:--:imgblclass:##:class:##::|~|:img2blattr:--:img2blclass:##:class:##::|~|:descfnts:--:descclass:##:class:##::|~|:bvuppclr:--:bvuppclass:##:class:##::|~|:bvprcsv:--:bvprcsvclass:##:class:##::|~|:bvcrnrattr:--:bvcrnrclass:##:class:##::|~|:bprcttlcss:--:bprcttlclass:##:class:##:' %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_setting_key in zp_settings_keys %}{% assign zp_prd_stng_vl = nil %}{% for zp_prd_stng_k in zp_product_settings_keys %}{% assign zp_product_key = zp_prd_stng_k | downcase %}{% assign zp_replacement_key = zp_setting_key | downcase %}{% if zp_product_key == zp_replacement_key %}{% assign zp_prd_stng_vl = '' | append: zp_product_settings_values[forloop.index0] | strip %}{% break %}{% endif %}{% endfor %}{% assign zp_replace_data = zp_settings_values[forloop.index0] | split: ':##:' %}{% assign zp_replacement_key = 'zps_' | append: zp_replace_data[0] | downcase %}{% assign zp_find_attr_sq = '' | append: zp_replace_data[1] | strip | append: "='" %}{% assign zp_find_attr_dq = '' | append: zp_replace_data[1] | strip | append: '="' %}{% assign zp_replacement_default = '' | append: zp_replace_data[2] | strip %}{% unless zp_prd_stng_vl == nil %}{% assign zp_replacement_value = zp_prd_stng_vl | split: zp_find_attr_sq %}{% assign zp_replacement_value = zp_replacement_value[1] | split: "'" | first | strip %}{% if zp_replacement_value.size < 1 %}{% assign zp_replacement_value = zp_prd_stng_vl | split: zp_find_attr_dq %}{% assign zp_replacement_value = zp_replacement_value[1] | split: '"' | first | strip %}{% endif %}{% if zp_replacement_value.size < 1 %}{% assign zp_replacement_value = zp_replacement_default %}{% endif %}{% assign zp_replacement_key_url_escape = zp_replacement_key | append: 'url_escape' | downcase %}{% assign zp_replacement_value_url_escape = zp_replacement_value | url_escape %}{% assign zp_replacement_key_escape = zp_replacement_key | append: 'escape' | downcase %}{% assign zp_replacement_value_escape = zp_replacement_value | escape %}{% assign zp_product_snippet = zp_product_snippet | replace: zp_replacement_key_url_escape, zp_replacement_value_url_escape | replace: zp_replacement_key_escape, zp_replacement_value_escape | replace: zp_replacement_key, zp_replacement_value %}{% endunless %}{% endfor %}{% assign zp_replace_data = '' %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% for zp_setting_key in zp_product_settings_keys %}{% assign zp_replacement_key = 'zps_' | append: zp_setting_key | downcase %}{% assign zp_replacement_value = '' | append: zp_product_settings_values[forloop.index0] %}{% if zp_replacement_key == 'zps_prdbtncpt' %}{% assign zp_replacement_value = zp_replacement_value | replace: '\', '&bsol;' %}{% endif %}{% assign zp_replacement_key_url_escape = zp_replacement_key | append: 'url_escape' | downcase %}{% assign zp_replacement_value_url_escape = zp_replacement_value | url_escape %}{% assign zp_replacement_key_escape = zp_replacement_key | append: 'escape' | downcase %}{% assign zp_replacement_value_escape = zp_replacement_value | escape %}{% assign zp_product_snippet = zp_product_snippet | replace: zp_replacement_key_url_escape, zp_replacement_value_url_escape | replace: zp_replacement_key_escape, zp_replacement_value_escape | replace: zp_replacement_key, zp_replacement_value %}{% endfor %}{% assign zp_product_snippet = zp_product_snippet | strip %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_product_snippet %}{% assign zp_product_snippet = zp_product_settings_content[1] | strip %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_product_snippet %}{% endif %}{% endfor %}{% if zp_product_link_type == 'cart' %}{% assign zp_redirect_product_url = routes.cart_url %}{% endif %}{% assign zp_part = nil %}{% assign zp_entity_products = nil %}{% assign zp_product_snippet = nil %}{% assign zp_product_settings_content = nil %}{% assign zp_product_settings_keys = nil %}{% endfor %}{% if zp_current_entity_content contains '<zpdproduct ' %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_schema_config = zp_current_entity_metafields['config'].value['schema'] %}{% else %}{% assign zp_current_entity_schema_config = zp_current_entity_metafields['config']['schema'] %}{% endif %}{% include 'dynamic-product.zipifypages', renderscope: 'tag' %}{% assign zp_current_entity_schema_config = nil %}{% endif %}{% if zp_current_entity_content contains '<zpdprdct' %}{% include 'dynamic-product.zipifypages', renderscope: 'metafield' %}{% endif %}{% if zp_split_tests_available and zp_alternative_entity_present and zp_split_single_page_render and zp_current_entity_content contains '<zpdprdct' %}{% include 'dynamic-product.zipifypages', renderscope: 'metafield', zp_current_entity_metafields: zp_alternative_entity_metafields %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '<zpprdmrkp></zpprdmrkp>', '' %}{% if zp_current_entity_content contains '<zp_cart_count></zp_cart_count>' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_cart_count></zp_cart_count>', cart.item_count %}{% endif %}{% if zp_current_entity_content contains '<zp_recharge_subscribe_title></zp_recharge_subscribe_title>' or zp_current_entity_content contains '<zp_recharge_onetime_title></zp_recharge_onetime_title>' %}{% assign zp_main_subscription_settings = shop.metafields['subscriptions'] %}{% assign zp_recharge_subscribe_title = '' | append: zp_main_subscription_settings.subscribe_message | strip | default: 'Subscribe & Save' %}{% assign zp_recharge_onetime_title = '' | append: zp_main_subscription_settings.onetime_message | strip | default: 'One-Time Purchase' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_recharge_subscribe_title></zp_recharge_subscribe_title>', zp_recharge_subscribe_title | replace: '<zp_recharge_onetime_title></zp_recharge_onetime_title>', zp_recharge_onetime_title %}{% endif %}{% if zp_current_entity_content contains '<zp_booster_coupons_count></zp_booster_coupons_count>' or zp_current_entity_content contains '__zp_booster_available_block_class__' or zp_current_entity_content contains '__zp_booster_unavailable_block_class__' %}{% assign zp_booster_coupons_left = '' | append: zp_current_entity_metafields['boostercouponsleft'] | strip | default: '0' %}{% assign zp_booster_coupons_left = 0 | plus: zp_booster_coupons_left %}{% if zp_booster_coupons_left > 0 %}{% assign zp_booster_available_block_class = '' %}{% assign zp_booster_unavailable_block_class = 'hidden' %}{% else %}{% assign zp_booster_available_block_class = 'hidden' %}{% assign zp_booster_unavailable_block_class = '' %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_booster_coupons_count></zp_booster_coupons_count>', zp_booster_coupons_left | replace: '__zp_booster_available_block_class__', zp_booster_available_block_class | replace: '__zp_booster_unavailable_block_class__', zp_booster_unavailable_block_class %}{% endif %}{% assign zp_object_settings_data = 'txtblclassescape:--::|~|:txt2classescape:--::|~|:txt1classescape:--::|~|:ttl5blclassescape:--::|~|:ttl4blclassescape:--::|~|:ttl3classescape:--::|~|:ttl3blclassescape:--::|~|:ttl2classescape:--::|~|:ttl2blclassescape:--::|~|:ttl1classescape:--::|~|:ttl1blclassescape:--::|~|:slctvrnturl_escape:--::|~|:selclassescape:--::|~|:qtyescape:--:1:|~|:prdttlclassescape:--::|~|:prdlbtnclassescape:--::|~|:prdimglblttl:--::|~|:prdimglblattr:--::|~|:prdimgclassescape:--::|~|:prdimgaltescape:--::|~|:prddscntdataurl_escape:--::|~|:prdclassescape:--::|~|:prdbtnltpescape:--::|~|:prdbtneltescape:--:_self:|~|:prdbtncpturl_escape:--::|~|:prdbtncpt:--::|~|:prdbtnclassescape:--::|~|:prdbtnalignescape:--::|~|:prdblclassescape:--::|~|:mcontclstclassescape:--::|~|:mcontclassescape:--::|~|:lblvisattr:--::|~|:lbluptxt:--::|~|:lbltxtcont:--::|~|:lblmdltxt:--::|~|:lblblclassescape:--::|~|:imglblttl:--::|~|:imglblattr:--::|~|:imgimgattr:--::|~|:imgblclassescape:--::|~|:img2blclassescape:--::|~|:img2attr:--::|~|:descclassescape:--::|~|:dcrt5classescape:--::|~|:dcrt4classescape:--::|~|:dcrt3classescape:--::|~|:dcrt2classescape:--::|~|:dcrt1classescape:--::|~|:crtmsgclassurl_escape:--::|~|:clstidescape:--::|~|:bvuppclassescape:--::|~|:bvprcsvclassescape:--::|~|:bvcrnrclassescape:--::|~|:brdclassescape:--::|~|:brdblclassescape:--::|~|:brd3blclassescape:--::|~|:brd2blclassescape:--::|~|:brd1blclassescape:--::|~|:bprcttlclassescape:--::|~|:addcrtmsgurl_escape:--:' %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_setting_key in zp_settings_keys %}{% assign zp_replacement_key = 'zps_' | append: zp_setting_key | downcase %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_replacement_key, zp_settings_values[forloop.index0] %}{% endfor %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_entity_product_tags = nil %}{% assign zp_entity_product_tags_views = nil %}{% assign zp_product_settings_keys = nil %}{% assign zp_product_settings_values = nil %}{% assign zp_selected_variants = nil %}{% assign zp_translation = 'layout.customer.account' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'customer.account_fallback' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "My Account" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_account_link_title></zp_account_link_title>', zp_translation %}{% assign zp_translation = 'layout.customer.log_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'customer.log_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Log out" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_logout_link_title></zp_logout_link_title>', zp_translation %}{% assign zp_translation = 'layout.customer.log_in' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'customer.log_in' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Login" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_login_link_title></zp_login_link_title>', zp_translation %}{% assign zp_translation = 'layout.customer.create_account' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'customer.account_fallback' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Register" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_register_link_title></zp_register_link_title>', zp_translation %}{% assign zp_translation = 'layout.cart.title' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'templates.cart.cart' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Cart" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zp_cart_link_title></zp_cart_link_title>', zp_translation %}{% assign zp_translation = 'general.search.placeholder' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'general.search.search' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Search" %}{% endif %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: 'zp_search_placeholder', zp_translation %}{% if zp_shop_metafields['localization'].type == 'json' %}{% assign zp_localization = zp_shop_metafields['localization'].value %}{% else %}{% assign zp_localization = zp_shop_metafields['localization'] %}{% endif %}{% assign zp_tfmemail = zp_localization['form']['email'] | strip | escape | default: "Email Address" | replace: '\', '&#92;' %}{% assign zp_tfmfirst_name = zp_localization['form']['first_name'] | strip | escape | default: "First Name" | replace: '\', '&#92;' %}{% assign zp_tfmlast_name = zp_localization['form']['last_name'] | strip | escape | default: "Last Name" | replace: '\', '&#92;' %}{% assign zp_tfmphone_number = zp_localization['form']['phone_number'] | strip | escape | default: "Phone Number" | replace: '\', '&#92;' %}{% assign zp_tdsfull = zp_localization['days']['full'] | strip | escape | default: "Days" | replace: '\', '&#92;' %}{% assign zp_tdsshort = zp_localization['days']['short'] | strip | escape | default: "Days" | replace: '\', '&#92;' %}{% assign zp_tdsshortest = zp_localization['days']['shortest'] | strip | escape | default: "D" | replace: '\', '&#92;' %}{% assign zp_thsfull = zp_localization['hours']['full'] | strip | escape | default: "Hours" | replace: '\', '&#92;' %}{% assign zp_thsshort = zp_localization['hours']['short'] | strip | escape | default: "Hrs" | replace: '\', '&#92;' %}{% assign zp_thsshortest = zp_localization['hours']['shortest'] | strip | escape | default: "H" | replace: '\', '&#92;' %}{% assign zp_tmsfull = zp_localization['minutes']['full'] | strip | escape | default: "Minutes" | replace: '\', '&#92;' %}{% assign zp_tmsshort = zp_localization['minutes']['short'] | strip | escape | default: "Min" | replace: '\', '&#92;' %}{% assign zp_tmsshortest = zp_localization['minutes']['shortest'] | strip | escape | default: "M" | replace: '\', '&#92;' %}{% assign zp_tssfull = zp_localization['seconds']['full'] | strip | escape | default: "Seconds" | replace: '\', '&#92;' %}{% assign zp_tssshort = zp_localization['seconds']['short'] | strip | escape | default: "Sec" | replace: '\', '&#92;' %}{% assign zp_tssshortest = zp_localization['seconds']['shortest'] | strip | escape | default: "S" | replace: '\', '&#92;' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '<zptfmemail></zptfmemail>',zp_tfmemail | replace: '[_zptfmemail_]',zp_tfmemail | replace: '<zptfmfirst_name></zptfmfirst_name>',zp_tfmfirst_name | replace: '[_zptfmfirst_name_]',zp_tfmfirst_name | replace: '<zptfmlast_name></zptfmlast_name>',zp_tfmlast_name | replace: '[_zptfmlast_name_]',zp_tfmlast_name | replace: '<zptfmphone_number></zptfmphone_number>',zp_tfmphone_number | replace: '[_zptfmphone_number_]',zp_tfmphone_number | replace: '<zptdsfull></zptdsfull>',zp_tdsfull | replace: '[_zptdsfull_]',zp_tdsfull | replace: '<zptdsshort></zptdsshort>',zp_tdsshort | replace: '[_zptdsshort_]',zp_tdsshort | replace: '<zptdsshortest></zptdsshortest>',zp_tdsshortest | replace: '[_zptdsshortest_]',zp_tdsshortest | replace: '<zpthsfull></zpthsfull>',zp_thsfull | replace: '[_zpthsfull_]',zp_thsfull | replace: '<zpthsshort></zpthsshort>',zp_thsshort | replace: '[_zpthsshort_]',zp_thsshort | replace: '<zpthsshortest></zpthsshortest>',zp_thsshortest | replace: '[_zpthsshortest_]',zp_thsshortest | replace: '<zptmsfull></zptmsfull>',zp_tmsfull | replace: '[_zptmsfull_]',zp_tmsfull | replace: '<zptmsshort></zptmsshort>',zp_tmsshort | replace: '[_zptmsshort_]',zp_tmsshort | replace: '<zptmsshortest></zptmsshortest>',zp_tmsshortest | replace: '[_zptmsshortest_]',zp_tmsshortest | replace: '<zptssfull></zptssfull>',zp_tssfull | replace: '[_zptssfull_]',zp_tssfull | replace: '<zptssshort></zptssshort>',zp_tssshort | replace: '[_zptssshort_]',zp_tssshort | replace: '<zptssshortest></zptssshortest>',zp_tssshortest | replace: '[_zptssshortest_]',zp_tssshortest %}{% assign zp_tfmemail = '' %}{% assign zp_tfmfirst_name = '' %}{% assign zp_tfmlast_name = '' %}{% assign zp_tfmphone_number = '' %}{% assign zp_tdsfull = '' %}{% assign zp_tdsshort = '' %}{% assign zp_tdsshortest = '' %}{% assign zp_thsfull = '' %}{% assign zp_thsshort = '' %}{% assign zp_thsshortest = '' %}{% assign zp_tmsfull = '' %}{% assign zp_tmsshort = '' %}{% assign zp_tmsshortest = '' %}{% assign zp_tssfull = '' %}{% assign zp_tssshort = '' %}{% assign zp_tssshortest = '' %}{% assign zp_localization = nil %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_judgeme_settings = zp_current_entity_metafields['config'].value['judgeme'] %}{% else %}{% assign zp_judgeme_settings = zp_current_entity_metafields['config']['judgeme'] %}{% endif %}{% for zp_judgeme_data in zp_judgeme_settings %}{% assign zp_tag_config = zp_judgeme_data | last %}{% assign zp_judgeme_product = all_products[zp_tag_config.handle] %}{% assign zp_judgeme_product_id_size = '' | append: zp_judgeme_product.id | size %}{% if zp_judgeme_product_id_size > 0 %}{% assign zp_judgeme_widget_content = '' | append: zp_judgeme_product.metafields.judgeme.widget | strip | replace: 'itemprop', 'data-itemprop' | replace: 'itemscope', 'data-itemscope' %}{% assign zp_judgeme_product_title = zp_judgeme_product.title | escape %}{% assign zp_judgeme_content = '<div style="clear:both"></div><div id="judgeme_product_reviews" class="jdgm-widget jdgm-review-widget" data-product-title="' | append: zp_judgeme_product_title | append: '" data-id="' | append: zp_judgeme_product.id | append: '">' | append: zp_judgeme_widget_content | append: '</div>' %}{% else %}{% assign zp_judgeme_content = '' %}{% endif %}{% assign zp_tag_id = zp_judgeme_data | first %}{% assign zp_judgeme_tag_name = '<zpjudgeme' | append: zp_tag_id | append: '></zpjudgeme' | append: zp_tag_id | append: '>' %}{% capture zp_uniq_judgeme_tag_name %}<zpjudgeme_{{ zp_tag_id }}></zpjudgeme_{{ zp_tag_id }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_judgeme_tag_name, zp_judgeme_content | replace: zp_uniq_judgeme_tag_name, zp_judgeme_content %}{% endfor %}{% assign zp_judgeme_product = nil %}{% assign zp_judgeme_product_title = nil %}{% assign zp_judgeme_widget_content = nil %}{% if zp_split_single_page_render %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_judgeme_settings = zp_alternative_entity_metafields['config'].value['judgeme'] %}{% else %}{% assign zp_judgeme_settings = zp_alternative_entity_metafields['config']['judgeme'] %}{% endif %}{% for zp_judgeme_data in zp_judgeme_settings %}{% assign zp_tag_config = zp_judgeme_data | last %}{% assign zp_judgeme_product = all_products[zp_tag_config.handle] %}{% assign zp_judgeme_product_id_size = '' | append: zp_judgeme_product.id | size %}{% if zp_judgeme_product_id_size > 0 %}{% assign zp_judgeme_widget_content = '' | append: zp_judgeme_product.metafields.judgeme.widget | strip | replace: 'itemprop', 'data-itemprop' | replace: 'itemscope', 'data-itemscope' %}{% assign zp_judgeme_product_title = zp_judgeme_product.title | escape %}{% assign zp_judgeme_content = '<div style="clear:both"></div><div id="judgeme_product_reviews" class="jdgm-widget jdgm-review-widget" data-product-title="' | append: zp_judgeme_product_title | append: '" data-id="' | append: zp_judgeme_product.id | append: '">' | append: zp_judgeme_widget_content | append: '</div>' %}{% else %}{% assign zp_judgeme_content = '' %}{% endif %}{% assign zp_tag_id = zp_judgeme_data | first %}{% assign zp_judgeme_tag_name = '<zpjudgeme' | append: zp_tag_id | append: '></zpjudgeme' | append: zp_tag_id | append: '>' %}{% capture zp_uniq_judgeme_tag_name %}<zpjudgeme_{{ zp_tag_id }}></zpjudgeme_{{ zp_tag_id }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_judgeme_tag_name, zp_judgeme_content | replace: zp_uniq_judgeme_tag_name, zp_judgeme_content %}{% endfor %}{% assign zp_judgeme_product = nil %}{% assign zp_judgeme_product_title = nil %}{% assign zp_judgeme_widget_content = nil %}{% endif %}{% assign zp_judgeme_settings = nil %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_opinew_settings = zp_current_entity_metafields['config'].value['opinew'] %}{% else %}{% assign zp_opinew_settings = zp_current_entity_metafields['config']['opinew'] %}{% endif %}{% for zp_opinew_data in zp_opinew_settings %}{% assign zp_tag_config = zp_opinew_data | last %}{% assign zp_opinew_product = all_products[zp_tag_config.handle] %}{% assign zp_opinew_product_id_size = '' | append: zp_opinew_product.id | size %}{% if zp_opinew_product_id_size > 0 %}{% capture zp_opinew_content %}<div id="opinew-reviews-product-page-code"><span id="opinew-plugin" data-server-address="https://www.opinew.com" data-opw-prodreviews="{{ zp_opinew_product.metafields.opinew_metafields['product_plugin'] }}" data-opinew-shop-id="{{ shop.id }}" data-shop-url="{{ shop.domain }}" data-platform-product-id="{{ zp_opinew_product.id }}"><span id="opinew_product_plugin_app"></span></span></div>{% endcapture %}{% else %}{% assign zp_opinew_content = '' %}{% endif %}{% assign zp_tag_id = zp_opinew_data | first %}{% assign zp_opinew_tag_name = '<zpopinew' | append: zp_tag_id | append: '></zpopinew' | append: zp_tag_id | append: '>' %}{% capture zp_uniq_opinew_tag_name %}<zpopinew_{{ zp_tag_id }}></zpopinew_{{ zp_tag_id }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_opinew_tag_name, zp_opinew_content | replace: zp_uniq_opinew_tag_name, zp_opinew_content %}{% endfor %}{% if zp_split_single_page_render %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_opinew_settings = zp_alternative_entity_metafields['config'].value['opinew'] %}{% else %}{% assign zp_opinew_settings = zp_alternative_entity_metafields['config']['opinew'] %}{% endif %}{% for zp_opinew_data in zp_opinew_settings %}{% assign zp_tag_config = zp_opinew_data | last %}{% assign zp_opinew_product = all_products[zp_tag_config.handle] %}{% assign zp_opinew_product_id_size = '' | append: zp_opinew_product.id | size %}{% if zp_opinew_product_id_size > 0 %}{% capture zp_opinew_content %}<div id="opinew-reviews-product-page-code"><span id="opinew-plugin" data-server-address="https://www.opinew.com" data-opw-prodreviews="{{ zp_opinew_product.metafields.opinew_metafields['product_plugin'] }}" data-opinew-shop-id="{{ shop.id }}" data-shop-url="{{ shop.domain }}" data-platform-product-id="{{ zp_opinew_product.id }}"><span id="opinew_product_plugin_app"></span></span></div>{% endcapture %}{% else %}{% assign zp_opinew_content = '' %}{% endif %}{% assign zp_tag_id = zp_opinew_data | first %}{% assign zp_opinew_tag_name = '<zpopinew' | append: zp_tag_id | append: '></zpopinew' | append: zp_tag_id | append: '>' %}{% capture zp_uniq_opinew_tag_name %}<zpopinew_{{ zp_tag_id }}></zpopinew_{{ zp_tag_id }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_opinew_tag_name, zp_opinew_content | replace: zp_uniq_opinew_tag_name, zp_opinew_content %}{% endfor %}{% endif %}{% assign zp_opinew_settings = nil %}{% if routes.root_url != '/' %}{% assign zp_rtlclurl = routes.root_url %}{% assign zp_rtindexrplc = '="' | append: zp_rtlclurl | append: '/"' %}{% assign zp_rtindexqrplc = '="' | append: zp_rtlclurl | append: '/?' %}{% assign zp_rtindexhrplc = '="' | append: zp_rtlclurl | append: '/#' %}{% assign zp_rtpagesrplc = '="' | append: zp_rtlclurl | append: '/pages/' %}{% assign zp_rtcartrplc = '="' | append: zp_rtlclurl | append: '/cart"' %}{% assign zp_rtcartqrplc = '="' | append: zp_rtlclurl | append: '/cart?' %}{% assign zp_rtcartprplc = '="' | append: zp_rtlclurl | append: '/cart/' %}{% assign zp_rtproductsrplc = '="' | append: zp_rtlclurl | append: '/products/' %}{% assign zp_rtcollectionsrplc = '="' | append: zp_rtlclurl | append: '/collections/' %}{% assign zp_rtaccountrplc = '="' | append: zp_rtlclurl | append: '/account"' %}{% assign zp_rtaccountlgnrplc = '="' | append: zp_rtlclurl | append: '/account/login"' %}{% assign zp_rtaccountrgstrrplc = '="' | append: zp_rtlclurl | append: '/account/register"' %}{% assign zp_rtaccountlgtrplc = '="' | append: zp_rtlclurl | append: '/account/logout"' %}{% assign zp_rtsearchrplc = '="' | append: zp_rtlclurl | append: '/search"' %}{% assign zp_rtsearchqrplc = '="' | append: zp_rtlclurl | append: '/search?' %}{% assign zp_rtcheckoutrplc = '="' | append: zp_rtlclurl | append: '/checkout"' %}{% assign zp_rtcheckoutqrplc = '="' | append: zp_rtlclurl | append: '/checkout?' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '="/"', zp_rtindexrplc | replace: '="/?', zp_rtindexqrplc | replace: '="/#', zp_rtindexhrplc | replace: '="/pages/', zp_rtpagesrplc | replace: '="/cart"', zp_rtcartrplc | replace: '="/cart?', zp_rtcartqrplc | replace: '="/cart/', zp_rtcartprplc | replace: '="/products/', zp_rtproductsrplc | replace: '="/collections/', zp_rtcollectionsrplc | replace: '="/account"', zp_rtaccountrplc | replace: '="/account/login"', zp_rtaccountlgnrplc | replace: '="/account/register"', zp_rtaccountrgstrrplc | replace: '="/account/logout"', zp_rtaccountlgtrplc | replace: '="/search"', zp_rtsearchrplc | replace: '="/search?', zp_rtsearchqrplc | replace: '="/checkout"', zp_rtcheckoutrplc | replace: '="/checkout?', zp_rtcheckoutqrplc %}{% assign zp_rtlclurlsch = shop.domain %}{% assign zp_rtlclurl = zp_rtlclurlsch | append: routes.root_url %}{% assign zp_rtindexrplc = '="https://' | append: zp_rtlclurl | append: '/"' %}{% assign zp_rtindexsch = '="https://' | append: zp_rtlclurlsch | append: '/"' %}{% assign zp_rtindexerplc = '="https://' | append: zp_rtlclurl | append: '"' %}{% assign zp_rtindexesch = '="https://' | append: zp_rtlclurlsch | append: '"' %}{% assign zp_rtindexqrplc = '="https://' | append: zp_rtlclurl | append: '/?' %}{% assign zp_rtindexqsch = '="https://' | append: zp_rtlclurlsch | append: '/?' %}{% assign zp_rtindexhrplc = '="https://' | append: zp_rtlclurl | append: '/#' %}{% assign zp_rtindexhsch = '="https://' | append: zp_rtlclurlsch | append: '/#' %}{% assign zp_rtpagesrplc = '="https://' | append: zp_rtlclurl | append: '/pages/' %}{% assign zp_rtpagessch = '="https://' | append: zp_rtlclurlsch | append: '/pages/' %}{% assign zp_rtcartrplc = '="https://' | append: zp_rtlclurl | append: '/cart"' %}{% assign zp_rtcartsch = '="https://' | append: zp_rtlclurlsch | append: '/cart"' %}{% assign zp_rtcartqrplc = '="https://' | append: zp_rtlclurl | append: '/cart?' %}{% assign zp_rtcartqsch = '="https://' | append: zp_rtlclurlsch | append: '/cart?' %}{% assign zp_rtcartprplc = '="https://' | append: zp_rtlclurl | append: '/cart/' %}{% assign zp_rtcartpsch = '="https://' | append: zp_rtlclurlsch | append: '/cart/' %}{% assign zp_rtproductsrplc = '="https://' | append: zp_rtlclurl | append: '/products/' %}{% assign zp_rtproductssch = '="https://' | append: zp_rtlclurlsch | append: '/products/' %}{% assign zp_rtcollectionsrplc = '="https://' | append: zp_rtlclurl | append: '/collections/' %}{% assign zp_rtcollectionssch = '="https://' | append: zp_rtlclurlsch | append: '/collections/' %}{% assign zp_rtaccountrplc = '="https://' | append: zp_rtlclurl | append: '/account"' %}{% assign zp_rtaccountsch = '="https://' | append: zp_rtlclurlsch | append: '/account"' %}{% assign zp_rtaccountlgnrplc = '="https://' | append: zp_rtlclurl | append: '/account/login"' %}{% assign zp_rtaccountlgnsch = '="https://' | append: zp_rtlclurlsch | append: '/account/login"' %}{% assign zp_rtaccountrgstrrplc = '="https://' | append: zp_rtlclurl | append: '/account/register"' %}{% assign zp_rtaccountrgstrsch = '="https://' | append: zp_rtlclurlsch | append: '/account/register"' %}{% assign zp_rtaccountlgtrplc = '="https://' | append: zp_rtlclurl | append: '/account/logout"' %}{% assign zp_rtaccountlgtsch = '="https://' | append: zp_rtlclurlsch | append: '/account/logout"' %}{% assign zp_rtsearchrplc = '="https://' | append: zp_rtlclurl | append: '/search"' %}{% assign zp_rtsearchsch = '="https://' | append: zp_rtlclurlsch | append: '/search"' %}{% assign zp_rtsearchqrplc = '="https://' | append: zp_rtlclurl | append: '/search?' %}{% assign zp_rtsearchqsch = '="https://' | append: zp_rtlclurlsch | append: '/search?' %}{% assign zp_rtcheckoutrplc = '="https://' | append: zp_rtlclurl | append: '/checkout"' %}{% assign zp_rtcheckoutsch = '="https://' | append: zp_rtlclurlsch | append: '/checkout"' %}{% assign zp_rtcheckoutqrplc = '="https://' | append: zp_rtlclurl | append: '/checkout?' %}{% assign zp_rtcheckoutqsch = '="https://' | append: zp_rtlclurlsch | append: '/checkout?' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_rtindexsch, zp_rtindexrplc | replace: zp_rtindexesch, zp_rtindexerplc | replace: zp_rtindexqsch, zp_rtindexqrplc | replace: zp_rtindexhsch, zp_rtindexhrplc | replace: zp_rtpagessch, zp_rtpagesrplc | replace: zp_rtcartsch, zp_rtcartrplc | replace: zp_rtcartqsch, zp_rtcartqrplc | replace: zp_rtcartpsch, zp_rtcartprplc | replace: zp_rtproductssch, zp_rtproductsrplc | replace: zp_rtcollectionssch, zp_rtcollectionsrplc | replace: zp_rtaccountsch, zp_rtaccountrplc | replace: zp_rtaccountlgnsch, zp_rtaccountlgnrplc | replace: zp_rtaccountrgstrsch, zp_rtaccountrgstrrplc | replace: zp_rtaccountlgtsch, zp_rtaccountlgtrplc | replace: zp_rtsearchsch, zp_rtsearchrplc | replace: zp_rtsearchqsch, zp_rtsearchqrplc | replace: zp_rtcheckoutsch, zp_rtcheckoutrplc | replace: zp_rtcheckoutqsch, zp_rtcheckoutqrplc %}{% endif %}{% assign product = nil %}{% if zp_app_integrations contains 'productoptionsbybold' %}{% assign zp_intgrt_wboldprdopts = true %}{% else %}{% assign zp_intgrt_wboldprdopts = false %}{% endif %}{% if zp_intgrt_wboldprdopts and zp_incl_boldcmnsnpt %}{% capture zp_bold_common_snippet %} {% render 'bold-common' %} {% endcapture %}{% assign zp_bold_common_snippet_separator = '</script>' %}{% assign zp_bold_common_snippet_parts = zp_bold_common_snippet | split: zp_bold_common_snippet_separator %}{% assign zp_bold_common_snippet_scripts_content = '' %}{% assign zp_bold_common_snippet_parts_size = zp_bold_common_snippet_parts | size | minus: 1 %}{% for zp_bold_common_snippet_parts_idx in (1..zp_bold_common_snippet_parts_size) %}{% if forloop.first == true %}{% assign zp_bold_common_snippet_join_string = '' %}{% else %}{% assign zp_bold_common_snippet_join_string = zp_bold_common_snippet_separator %}{% endif %}{% assign zp_bold_common_snippet_scripts_content = zp_bold_common_snippet_scripts_content | append: zp_bold_common_snippet_join_string | append: zp_bold_common_snippet_parts[zp_bold_common_snippet_parts_idx] %}{% endfor %}{% assign zp_bold_common_snippet_init_script_content = zp_bold_common_snippet_parts | first | append: zp_bold_common_snippet_separator %}{% assign zp_bold_common_snippet_parts = nil %}{% assign zp_bold_common_included = true %}{% else %}{% assign zp_bold_common_snippet_init_script_content = '' %}{% assign zp_bold_common_snippet_scripts_content = '' %}{% assign zp_bold_common_included = false %}{% endif %}{% assign zp_no_image_url = 'no-image.gif' | img_url: '1080x' %}{% assign zp_current_entity_content = zp_current_entity_content | replace: '"zps_medimurl"', zp_no_image_url | replace: '"zps_medimgsrc"', zp_no_image_url | replace: '"zps_medsurl"', zp_no_image_url | replace: 'zps_medheig', '1024' | replace: 'zps_medwid', '1024' | replace: 'zps_medalt', 'no-image' | replace: 'zps_medvid', '' | replace: 'zps_medpos', '1' %}{% assign product = zp_original_product %}{% unless zp_alternative_entity_present == true %}{% assign zp_wrapper_classes = "zp " | append: zp_entity_attributes_class | append: '-' | append: zp_current_entity.id %}{% if shop.customer_accounts_enabled %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-customer-accounts' %}{% endif %}{% if shop.customer_accounts_enabled and customer %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-account-authorized' %}{% endif %}{% if zp_alternative_entity_present == true %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-split-test' %}{% endif %}{% if zp_entity_custom_template and zp_entity_fixed_layout != true %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-store-header-footer' %}{% endif %}{% if zp_entity_with_default_styles %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-default-styles-text' %}{% endif %}{% if zp_entity_fixed_layout %}{% assign zp_entity_layout_wrapper_class = ' zpa-fixed-layout' %}{% elsif zp_entity_custom_template != true %}{% assign zp_entity_layout_wrapper_class = ' zpa-wide-layout' %}{% else %}{% assign zp_entity_layout_wrapper_class = '' %}{% endif %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: zp_entity_layout_wrapper_class %}{% assign zp_wrapper_classes = zp_wrapper_classes | append: ' zpa-' | append: zp_entity_marker | append: '-template' %}{% endunless %}<div class="{{ zp_wrapper_classes }}{% assign zp_wrapper_classes = nil %}">{% if zp_bold_common_included %}{{ zp_bold_common_snippet_init_script_content }}{% assign zp_bold_common_snippet_init_script_content = "" %}{% endif %}{{ zp_current_entity_content | strip }}{% assign zp_current_entity_content = nil %}{% if zp_bold_common_included %}{{ zp_bold_common_snippet_scripts_content | strip }}{% unless zp_bold_common_snippet_scripts_content contains '.js' %}{% capture zp_bold_common_snippet_scripts_content %}{% render 'sc-includes' %}{% endcapture %}{% unless zp_bold_common_snippet_scripts_content contains 'Could not find asset snippets/sc-includes.liquid' %}{{ zp_bold_common_snippet_scripts_content | strip }}{% endunless %}{% endunless %}{% assign zp_bold_common_snippet_scripts_content = '' %}{% endif %}</div><div style="display:none;"><div id="form-popup"></div></div>{% if zp_app_integrations contains 'bestcurrencyconverter' %}<select class="single-option-selector noreplace" id="zp-integration-option-selector" style="display:none;" aria-hidden="true"></select>{% endif %}<div id="zpaProductSection"></div><script>window.ZipifyPages.mainProduct={id:{{ product.id | default: 'null' }}}</script>{% endif %}
{% schema %}
{
  "name": "ZP ProductHeaderContent",
  "tag": "section",
  "class": "zpa-published-page-holder",
  "templates": ["product"]
}
{% endschema %}