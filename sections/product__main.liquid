{% comment %}
  ** Product - main content area **
  - Product template
{% endcomment %}

{% assign id = section.id %}
{% comment %} Layout {% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}{% assign animation = section.settings.animation | default: 'none' %}
{% comment %} Advanced {% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% comment %} Section specific CSS {% endcomment %}
{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  .section--has-sidebar-option.has-sidebar-enabled {
    max-width: 900px;
    {% if width == 'wide' -%}
      width: 100%;
      max-width: 95%;
    {%- endif %}
  }

  .section--has-sidebar-option.has-sidebar-disabled {
    width: 100%;
    max-width: none;
  }

  /* free product style */
  .create-pet1 {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 10000;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);

    input,
    select {
      display: block;
      width: 100%;
      height: 40px;
      margin: 20px 0;
      padding: 0 10px;
      border: 1px solid #dedede;
      background-color: transparent;

       &:focus {
        outline: none;
      }
    }
  }

  .create-pet__form1 {
    background: #fff;
    max-width: 400px;
    width: calc(100% - 60px);
    padding: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow-y: auto;
  }

  .create-pet__form1::-webkit-scrollbar {
    width: 12px;
    /* Width of the entire scrollbar */
  }
  .create-pet__form1::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* Background of the scrollbar track */
    border-radius: 16px;
    /* Rounded corners of the track */
    margin: 10px;
  }

  .create-pet__form1::-webkit-scrollbar-thumb {
    background-color: #888;
    /* Color of the scrollbar thumb */
    border-radius: 16px;
    /* Rounded corners of the thumb */
    border: 3px solid #f1f1f1;
    /* Adds space around the thumb for padding */
  }

  .create-pet__form1::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* Color of the thumb on hover */
    cursor: pointer;
  }


  .free-product1 {
    max-width: 450px !important;
  }
  .gift-button1 {
    cursor: pointer;

  }
  .create-pet__error1 {
    margin: -15px 0 0;
    padding: 0;
    color: red;
  }

  .create-pet__form1 {
    background: #fff;
    max-width: 400px;
    width: calc(100% - 60px);
    padding: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .free-product1 {
    max-width: 900px !important;
    max-height: 700px;
    height: 100%;
    border: 1px solid black;
    border-radius: 15px;
    text-align: center;
  }
  .gift-button1 {
    cursor: pointer;

  }
  .create-pet__error1 {
    margin: -15px 0 0;
    padding: 0;
    color: red;
  }
  .popUpContainer {
    display: flex;
  }

  #free_product_title {
    font-weight: 700 !important;
    margin: 10px 0;
    font-size: 22px;
  }

  .freeProductName {
    font-weight: bold;
  }

  .productImageContainer {
    display: flex;
    width: 50%;
    justify-content: center;
    align-items: center;
  }

  .productImage {
    margin-top: 20%;
    width: 400px;
    height: 400px;
    object-fit: fill;
    border-radius: 10px;
  }

  .productDetailsContainer {
    width: 50%;
    padding-top: 35px;
  }

  .productName {
    text-align: left;
    margin-left: 25px;
    font-weight: 700;
    font-size: 22px;
    margin-bottom: 10px;
  }

  .productPriceContainer {
    text-align: left;
    margin: 10px 25px;
  }

  .productOriginalPrice {
    font-weight: 500;
    font-size: 22px;
  }

  .productComparePrice {
    font-size: 22px;
    font-weight: 500;
    color: rgb(0, 0, 0, 0.32);
    text-decoration: line-through;
  }

  .productReviews {
    text-align: left;
    margin-left: 25px;
  }

  .selector-wrapper-free-product {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: baseline;
    margin-bottom: 10px;
    margin-left: 25px;
  }

  .selector-wrapper-free-product label {
    font-weight: 700;
    color: #363636;
    font-size: 1rem;
  }

  .selector-wrapper-free-product label:after {
    content: ":";
  }

  .selectFreeVariant select {
    margin: 8px 0;
  }

  .selectFreeVariant:not(.is-multiple) {
    height: unset;
  }

  .selectFreeVariant select {
    border: 1px solid black;
    border-radius: 8px;
    font-size: 17px;
  }

  .selectFreeVariant {
    position: relative;
    display: inline-block;
    width: auto;
    max-width: 100%;
    vertical-align: top;
  }

  .close-button-free-product-popup {
    display: flex;
    position: relative;
    top: -70px;
    left: 100%;
    cursor: pointer;
    background-color: #5461c8;
    border: 1px solid #5461c8;
    border-radius: 50%;
    font-size: 20px;
    padding: 2px;
    margin: 0;
    line-height: 1;
    color: white;
    width: 30px;
    height: 30px;
    justify-content: center;
  }

  .close-button-uploader-free-product {
    display: none;
    position: relative;
    top: -8px;
    left: 100%;
    cursor: pointer;
    background-color: #5461c8;
    border: 1px solid #5461c8;
    border-radius: 50%;
    font-size: 16px;
    padding: 2px;
    margin: 0;
    line-height: 1;
    color: white;
  }

  .close-button-uploader-free-product:hover {
    color: white;
    border: 1px solid #5461c8;
    padding: 2px;
  }

  .dragDropText {
    font-weight: unset;
    cursor: pointer;
  }

  .productUploaderHeading {
    font-size: 20px;
    font-weight: 400;
    text-align: left;
    margin-left: 25px;
    margin-bottom: 10px;
    display:flex;
  }

  .uploadOuterFree {
    margin: 10px 25px;
  }

  .uploadOuterFree .dragBox label {
    border: 3px solid #5461c8;
    border-radius: 16px;
    padding: 8px 20px;
    color: #5461c8;
  }
  .uploadOuterFree .dragBox strong {
    margin: 0 20px;
    font-weight: 500;
  }
  .uploadOuterFree .dragBoxFree label {
    border: 3px solid #5461c8;
    border-radius: 10px;
    padding: 0 20px;
    color: white;
    background-color: #5461C8;
  }
  .uploadOuterFree .dragBoxFree strong {
    margin: 0 20px;
    font-weight: 500;
  }

  .swatches-label-free-product {
    text-align: left;
    margin-left: 25px;
    font-weight: 700;
  }

  .customRadioInputs {
    width: 0 !important;
  }

  .dragBoxFree {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 10px 0;
    margin: 0 auto;
    position: relative;
    text-align: center;
    font-weight: 500;
    color: #000;
    transition: transform 0.3s;
    border-radius: 10px;
    background: #d9d9d9;
    font-size: 22px;
    cursor: pointer;
    input[type="file"] {
      position: absolute;
      border-radius: 20px;
      height: 100%;
      width: 100%;
      opacity: 0;
      top: 0;
      left: 0;
      padding: 0;
    }
  }

  .add-to-cart-button-free-product {
    background-color: #5461c8;
    padding: 10px 20px;
    max-width: 315px;
    width: 100%;
    border: 1px solid #5461c8;
    border-radius: 24px;
    color: #fff;
    font-size: 20px;
    font-family: Inter;
    font-weight: 600;
    letter-spacing: 3px;
    margin-top: 30px;
    cursor: pointer;
  }

  .close-button-free-product-popup {
    left: 95%;
  }

  @media (max-width: 800px) {
    .productUploaderHeading{
       margin-left:0px;
    }
    .productReviews {
      margin: 10px 0;
      text-align: center;
    }
    .close-button-free-product-popup {
      left: 45%;
    }
    .selector-wrapper-free-product {
      flex-direction: column;
    }
    .popUpContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .productImage {
      width: 226px;
      height: 222px;
    }
    .productName {
      text-align: center;
      margin: 0;
      font-size: 18px;
    }
    .productPriceContainer {
      text-align: center;
    }
    .orText {
      display: none;
    }
    .dragBoxText {
      display: none;
    }
    .dragDropText {
      display: none;
    }
    .dragBoxFree {
      display: flex;
      flex-direction: column;
    }
    .uploadOuterFree {
      margin: 0;
    }

    .dragBoxFree {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding: 10px;
      margin: 0 auto;
      position: relative;
      text-align: center;
      font-weight: 500;
      transition: transform 0.3s;
      font-size: 22px;
      border: 0;
      background: #d9d9d9;
      box-shadow: none;
      input[type="file"] {
        position: absolute;
        border-radius: 20px;
        height: 100%;
        width: 100%;
        opacity: 0;
        top: 0;
        left: 0;
        padding: 0;
      }
    }
  }
  @media (max-width: 480px) {
    .close-button-free-product-popup {
      top: -50px;
      left: 45%;
    }

    .productDetailsContainer {
      width: 100% !important;
    }
    .productReviews {
      margin: 10px 0;
      text-align: center;
    }
    .selector-wrapper-free-product {
      align-items: center;
      margin-left: 0;
      gap: 6px;
      margin-bottom: 25px;
    }
    .swatches-label-free-product {
      margin-left: 0;
      margin-bottom: 15px;
      text-align: center;
    }
    .productImageContainer {
      width: 85%;
    }
    .create-pet__form1 {
      padding: 20px;
    }
  }


  {% render 'css-loop',
          css: section.settings.custom_css,
          id: section.id
%}
{% endstyle %}

{% liquid
  assign selected_variant = product.selected_variant

  if product.variants.size == 1 or settings.select_first_available_variant
    assign selected_variant = product.selected_or_first_available_variant
  endif

  for block in section.blocks
    if block.type == 'price'
      assign display_savings = block.settings.display_savings | json
    endif
    if block.type == 'complementary_products'
      assign product_recommendation_limit = block.settings.product_recommendation_limit
    endif
  endfor
%}

<section
  class="section
        {{ css_class }}
        is-width-{{ width }}"
  {% if animation != "none" %}
  data-scroll-class="{{ animation }}"
  {% endif %}>
  <div class="pet-product__confirm" id="pet-product__confirm ">
    <div
      class="create-pet1"
      id="free_product_div"
      data-product_id="{{ product.id }}"
      style="visibility: hidden;">
      <input
        type="hidden"
        class="freeProductVariants"
        value="">
      <input
        type="hidden"
        class="freeProductKeys"
        value="">
        <input type="hidden" class="apiKey" value>
      <form
        method="POST"
        action="/cart/add"
        class="create-pet__form1 free-product1 freeProductForm">
        <input
          type="hidden"
          id="freeFormId"
          value=""
          name="id">
        <input
          type="hidden"
          id="freeUniqueId"
          value=""
          name="properties[_unique_key]">
        <input
          type="hidden"
          id="freeFormQuantity"
          value="1"
          name="quantity">
        <h3 id="free_product_title">Free Gift Unlocked!</h3>
        <div class="popUpContainer">
          <button
            class="close-button-free-product-popup"
            type="button"
            onclick="closeFreeProductDiv(this)">✖</button>
          <div class="productImageContainer"><img class="productImage" alt="Product Image"></div>
          <div class="productDetailsContainer">
            <h1 class="productName">""</h1>
            <div class="productReviews"></div>
            <div class="productPriceContainer">
              <span class="productOriginalPrice">FREE</span>
              <span class="productComparePrice"></span>
            </div>
            <div class="variantsContainer"></div>
            <div>
              <label class="productUploaderHeading" for="free-uploadFile-1">Upload Image:</label>
              <div class="free-image-uploader" id="free-image-uploader-1">
                <button class="close-button-uploader-free-product">✖</button>
                <div class="selected-image-free-1 selected-image-container-free"></div>
                <input
                  type="hidden"
                  name="properties[_image_url]"
                  id="hiddenInputFree"
                  value="">
                <div class="uploadOuterFree">
                  <span class="dragBoxFree">
                    <span class="dragDropText">Drag + Drop</span>
                    <input
                      onclick="this.value=null;"
                      type="file"
                      class="image_uploader_free"
                      onChange="dragNdropFreeProduct(event, 'free-image-uploader-1',1)"
                      ondragover="drag()"
                      ondrop="drop()"
                      id="free-uploadFile-1" />
                    <span class="orText">
                      <strong>or</strong>
                    </span>
                    <label for="uploadFileFree" class="btn btn-primary">
                      Select File
                    </label>
                  </span>
                </div>
              </div>
            </div>
            <div>
              <button
                type="submit"
                class="add-to-cart-button-free-product"
                id="add_to_cart_free_product">ADD TO CART</button>
            </div>
          </div>
        </div>
      {% comment %} <button type="submit" class="gift-button1"><a id="free_product_href" href="" style="text-decoration: none; color: white;" >Customize FREE Gift Now ⟶</a></button> {% endcomment %}
      </form>
    </div>
  </div>

  <div class="product-{{ product.id }}">
    {%
      render 'product'
      , product: product
      , sold_out_options: settings.sold_out_options
      , selected_variant: selected_variant
      , width: width
      , css_class: css_class
      , display_thumbnails: section.settings.display_thumbnails
      , enable_product_lightbox: section.settings.enable_product_lightbox
      , enable_shopify_product_badges: section.settings.enable_shopify_product_badges
      , enable_thumbnail_slider: section.settings.enable_thumbnail_slider
      , enable_zoom: section.settings.enable_zoom
      , gallery_arrows: section.settings.gallery_arrows
      , product_height: section.settings.product_height
      , product_images_position: section.settings.product_images_position
      , set_product_height: section.settings.set_product_height
      , slideshow_transition: section.settings.slideshow_transition
      , stickers_enabled: settings.stickers_enabled
      , tag_style: settings.tag_style
      , thumbnail_position: section.settings.thumbnail_position
      , video_looping: section.settings.video_looping
      ,
    %}
  </div>
</section>

{% comment %} JavaScript {% endcomment %}
<script
  type="application/json"
  data-section-id="{{ section.id }}"
  data-section-data>
  {
    "product": {{ product | json }},
    "section_id": {{ section.id | json }},
    "product_recommendation_limit": {{ product_recommendation_limit | json }},
    "display_savings": {{ display_savings }},
    "gallery_arrows": {{ section.settings.gallery_arrows | json }},
    "thumbnail_arrows": {{ section.settings.gallery_arrows | json }},
    "enable_zoom": {{ section.settings.enable_zoom | json }},
    "enable_product_lightbox": {{ section.settings.enable_product_lightbox | json }},
    "enable_thumbnail_slider": {{ section.settings.enable_thumbnail_slider | json }},
    "slideshow_speed": {{ section.settings.slideshow_speed | json }},
    "slideshow_transition": {{ section.settings.slideshow_transition | json }},
    "thumbnails_enabled": {{ section.settings.display_thumbnails | json }},
    "thumbnail_position": {{ section.settings.thumbnail_position | json }},
    "product_media_amount": {{ product.media.size }},
    "template": "classic"
  }
</script>

<script src="{{ 'z__jsProduct.js' | asset_url }}"></script>

{% comment %} Shopify-XR {% endcomment %}
{% if product.media %}
  <script>
    window.ShopifyXR=window.ShopifyXR||function(){(ShopifyXR.q=ShopifyXR.q||[]).push(arguments)}
      {% assign models = product.media | where: 'media_type', 'model' | json %}
      ShopifyXR('addModels', {{ models }});
  </script>
{% endif %}

<script>

  let omsDetails={{shop.metafields.cuddleclones.api_details.value|json}};
  document.querySelector(".apiKey").value=omsDetails[0].api_token;
  function closeFreeProductDiv() {
    document.getElementById("free_product_div").style.visibility = "hidden";
    window.location.href = "{{ shop.url }}/cart";
  }

  const cartButton = document.getElementById("add_to_cart_free_product");
  cartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
  cartButton.setAttribute("disabled", "true");

  async function dragNdropFreeProduct(event, containerId, number){
      var files = event.target.files;

      if (files.length > 1) {
        alert("Please select only one image file.");
        return;
      }

      var file = files[0];
      var fileName = URL.createObjectURL(files[0]);

      if (!file.type.startsWith("image/")) {
        alert("Please select only image files.");
        return;
      }

      if (file.type !== "image/jpg" && file.type !== "image/png" && file.type !== "image/bmp" && file.type !== "image/jpeg") {
        alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
        return;
      }
      if (files[0].size > 15 * 1024 * 1024) {
        // Convert MB to bytes
        alert("Please select images smaller than 15MB.");
        return;
      }

      let uploadedImageName;
      var previewImg = document.createElement("img");
      previewImg.src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif";


      const imageContainer=document.querySelector(".selected-image-container-free");
      document.querySelector(".close-button-uploader-free-product").style.display="block";
      document.querySelector(".free-image-uploader").style.padding="20px";

      previewImg.setAttribute("alt", "Image Free Product Uploader");
      previewImg.setAttribute("id", "previewImageFreeProduct");
      imageContainer.appendChild(previewImg);

      document.querySelector(".uploadOuterFree").style.display = "none";

      let hiddenInputFree=document.getElementById("hiddenInputFree");
      // const cartButton = document.getElementById("add_to_cart_free_product");


      const formData = new FormData();
      formData.append('image', files[0]); 

      try {
        const response = await fetch(`${api_details[0].url}/image_uploader/print_image`, {
          method: "POST",
          body: formData,
          headers: {
            'api-token': omsDetails[0].api_token,
          },
        })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          previewImg.src = data.imageUrl;

          hiddenInputFree.value=data.imageUrl;

          cartButton.style.cssText = "cursor:pointer;opacity:1;pointer-events:auto";
          cartButton.removeAttribute("disabled");

          const containerWidth = 250;
          const containerHeight = 250;
          const imageAspectRatio = previewImg.naturalWidth / previewImg.naturalHeight;

          let imageWidth, imageHeight;
          if (imageAspectRatio >= 1) {
            // Landscape or square image
            imageWidth = containerWidth;
            imageHeight = containerWidth / imageAspectRatio;
          } else {
            // Portrait image
            imageHeight = containerHeight;
            imageWidth = containerHeight * imageAspectRatio;
          }

          // Set image dimensions and quality
          previewImg.style.width = `${imageWidth}px`;
          previewImg.style.height = `${imageHeight}px`;
          previewImg.style.objectFit = "contain"; // Maintain aspect ratio and fill container

          const imageUrl = data.imageUrl;
          const parts = imageUrl.split('/');
          uploadedImageName = parts[parts?.length - 1];
        })
        .catch((error) => {
          console.error("Error uploading new image:", error);
        });           
      } catch (error) {
          console.log('Error:', error);
      }

      document.querySelector(".close-button-uploader-free-product").addEventListener("click",function(event){
        event.preventDefault();
        if (uploadedImageName) {
          imageContainer.innerHTML="";
          document.querySelector(".uploadOuterFree").style.display = "block";
          document.querySelector(".close-button-uploader-free-product").style.display="none";
          document.getElementById("hiddenInputFree").value="";
          document.querySelector(".free-image-uploader").style.padding="0px";
          const cartButton = document.getElementById("add_to_cart_free_product");
          cartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
          cartButton.setAttribute("disabled", "true");
        }
      });
    }

    

  function extractVariantAndAdd(){
    let variantsString=document.querySelector(".freeProductVariants").value;
    let variantsData=JSON.parse(variantsString);
    let nativeKeys = document.querySelector(".freeProductKeys").value;
    let nativeKeysData=JSON.parse(nativeKeys);
    let concatenatedValue="";
    nativeKeysData.forEach((key,index)=>{
      let inputValue=document.querySelector(`.${key}HiddenInput`).value;
      concatenatedValue+=inputValue+" / ";
    });
    if (concatenatedValue.endsWith(" / ")) {
      concatenatedValue = concatenatedValue.slice(0, -3);
    }
    let foundVariant = variantsData.find(variant => variant.title === concatenatedValue);
    if(foundVariant){
      document.querySelector("#freeFormId").value=foundVariant.id;
    }else{
      document.querySelector("#freeFormId").value=variantsData[0].id;
    }

  }

  document.getElementById('add_to_cart_free_product').addEventListener('click', function(event){
    event.preventDefault();

    extractVariantAndAdd();

    document.querySelector('.freeProductForm').submit();
  });
</script>

{% schema %}

  {
    "name": "Product information",
    "class": "product-template product-main has-sidebar-option jsProduct section--has-sidebar-option",
    "settings": [
      {
        "type": "header",
        "content": "Media",
        "info": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
      },

      {
        "type": "image_picker",
        "id": "new_pet_bowl_img",
        "label": "Pet Bowl Image"
      },
      {
        "type": "text",
        "id": "new_pet_bowl_text",
        "label": "Pet Shelter Text"
      },
      {
        "type": "radio",
        "id": "product_images_position",
        "label": "Media position",
        "options": [
          {
            "value": "left",
            "label": "Left"
          }, {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "left"
      }, {
        "type": "checkbox",
        "id": "set_product_height",
        "label": "Set height of product media",
        "default": false
      }, {
        "type": "range",
        "id": "product_height",
        "label": "Product media height",
        "min": 200,
        "max": 800,
        "step": 10,
        "default": 500,
        "unit": "px"
      }, {
        "type": "checkbox",
        "id": "video_looping",
        "label": "Enable video looping",
        "default": false
      }, {
        "type": "header",
        "content": "Product gallery"
      }, {
        "type": "checkbox",
        "id": "gallery_arrows",
        "label": "Show arrows",
        "info": "Only applies to desktop",
        "default": true
      }, {
        "type": "checkbox",
        "id": "enable_zoom",
        "label": "Magnify on hover",
        "default": true
      }, {
        "type": "checkbox",
        "id": "enable_product_lightbox",
        "label": "Enable lightbox",
        "default": true
      }, {
        "type": "range",
        "id": "slideshow_speed",
        "label": "Gallery speed",
        "min": 0,
        "max": 6,
        "unit": "sec",
        "default": 0,
        "info": "Set to 0 to disable autoplay."
      }, {
        "type": "select",
        "id": "slideshow_transition",
        "label": "Gallery transition",
        "options": [
          {
            "value": "slide",
            "label": "Slide"
          }, {
            "value": "fade",
            "label": "Fade"
          }
        ],
        "default": "slide"
      }, {
        "type": "checkbox",
        "id": "display_thumbnails",
        "label": "Show thumbnails",
        "default": true
      }, {
        "type": "select",
        "id": "thumbnail_position",
        "label": "Thumbnails position",
        "options": [
          {
            "value": "left-thumbnails",
            "label": "Left of main image"
          }, {
            "value": "right-thumbnails",
            "label": "Right of main image"
          }, {
            "value": "bottom-thumbnails",
            "label": "Below main image"
          }
        ],
        "default": "bottom-thumbnails"
      }, {
        "type": "checkbox",
        "id": "enable_thumbnail_slider",
        "label": "Enable thumbnail slider",
        "default": true
      }, {
        "type": "header",
        "content": "Layout"
      }, {
        "type": "select",
        "id": "width",
        "label": "Width",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          }, {
            "value": "wide",
            "label": "Wide"
          }
        ],
        "default": "standard"
      }, {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      }, {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      }, {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          }, {
            "value": "fadeInRight",
            "label": "Fade in right"
          }, {
            "value": "slideInLeft",
            "label": "Slide in left"
          }, {
            "value": "slideInRight",
            "label": "Slide in right"
          }
        ]
      }, {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      }, {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      }, {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "complementary_products",
        "name": "Complementary products",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "To select complementary products, add the Search & Discovery app. [Learn more](https:\/\/shopify.dev\/themes\/product-merchandising\/recommendations)"
          }, {
            "type": "text",
            "id": "heading",
            "label": "Heading",
            "default": "Pairs well with"
          }, {
            "type": "range",
            "id": "product_recommendation_limit",
            "label": "Maximum products to show",
            "min": 1,
            "max": 10,
            "default": 5
          }, {
            "type": "range",
            "id": "products_per_slide",
            "label": "Number of products per page",
            "min": 1,
            "max": 3,
            "default": 2
          }
        ]
      },
      {
        "name": "Shop pay",
        "type": "shop_pay",
        "limit": 1,
        "settings": [
          {
            "type": "inline_richtext",
            "id": "shop_pay_text",
            "label": "Text",
            "default": "4 interest-free installments"
          },
          {
            "type": "image_picker",
            "id": "shop_pay_logo_image",
            "label": "Shop Pay Logo"
          }
        ]
      },
      {
        "type": "title",
        "name": "Title",
        "limit": 1
      },
      {
        "type": "vendor",
        "name": "Vendor",
        "limit": 1
      }, {
        "type": "shelter_pet",
        "name": "Pet Shelter",
        "limit": 1
      }, {
        "type": "price",
        "name": "Price",
        "limit": 1,
        "settings": [
          {
            "type": "checkbox",
            "id": "display_savings",
            "label": "Show price savings",
            "default": true
          }
        ]
      }, {
        "type": "rating",
        "name": "Product rating",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "To display a rating, add a product rating app. [Learn more](https://apps.shopify.com/product-reviews)"
          }
        ]
      }, {
        "type": "sku",
        "name": "SKU",
        "limit": 1
      }, {
        "type": "text",
        "name": "Text",
        "settings": [
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Text block</p>"
          }
        ]
      }, {
        "type": "description",
        "name": "Description",
        "limit": 1
      }, {
        "type": "form",
        "name": "Form",
        "limit": 1,
        "settings": [
          {
            "type": "header",
            "content": "Dynamic Checkout Button"
          }, {
            "type": "checkbox",
            "id": "show_payment_button",
            "label": "Show dynamic checkout button",
            "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https:\/\/help.shopify.com\/manual\/using-themes\/change-the-layout\/dynamic-checkout)",
            "default": true
          }, {
            "type": "checkbox",
            "id": "show_gift_card_recipient_form",
            "label": "t:sections.product.blocks.form.show_gift_card_recipient_form.label",
            "info": "t:sections.product.blocks.form.show_gift_card_recipient_form.info",
            "default": false
          }, {
            "type": "paragraph",
            "content": "Customize additional form features for the product in Theme settings > Product form."
          }
        ]
      }, {
        "type": "product-links",
        "name": "Product links",
        "limit": 1,
        "settings": [
          {
            "type": "checkbox",
            "id": "show_collections",
            "label": "Show collections",
            "default": true
          }, {
            "type": "checkbox",
            "id": "show_types",
            "label": "Show types",
            "default": true
          }, {
            "type": "checkbox",
            "id": "show_tags",
            "label": "Show tags",
            "default": true
          }
        ]
      }, {
        "type": "share",
        "name": "Share",
        "limit": 1
      }, {
        "type": "size-chart",
        "name": "Size chart",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Label",
            "default": "Size chart"
          }, {
            "type": "page",
            "id": "size_chart",
            "label": "Size chart",
            "info": "[Learn more](https:\/\/help.outofthesandbox.com\/hc\/en-us\/articles\/115006910707-Using-the-Size-Chart-Sections-themes-)"
          }
        ]
      }
    ],
    "default": {
      "settings": {}
    }
  }

{% endschema %}