{% comment %}
** Rich text **
{% endcomment %}

{% liquid
  assign heading_font = section.settings.heading_font
  assign heading_color = section.settings.heading_color
  assign heading_alignment = section.settings.heading_alignment
  assign heading_size = section.settings.heading_size
  assign text_font = section.settings.text_font
  assign text_color = section.settings.text_color
  assign text_alignment = section.settings.text_alignment
  assign text_size = section.settings.text_size

  # Is the color set to transparent?
  assign heading_alpha = heading_color | color_extract: 'alpha'
  assign text_alpha = text_color | color_extract: 'alpha'
  assign background_alpha = section.settings.background | color_extract: 'alpha'
  assign gradient_alpha = section.settings.gradient | color_extract: 'alpha'
%}

{%- capture heading_classes -%}
  rich-text__heading
  title
  text-align-{{ heading_alignment }}
  {% if section.settings.mobile_alignment != 'none' %}
    text-align--mobile-{{ section.settings.mobile_alignment }}
  {% endif %}
  {% if heading_size > 50 %}
    mobile-shrink-text
  {% endif %}
{%- endcapture -%}

{%- capture text_classes -%}
  rich-text__text
  content
  has-columns--{{ section.settings.columns }}
  text-align-{{ text_alignment }}
  {% if section.settings.mobile_alignment != 'none' %}
    text-align--mobile-{{ section.settings.mobile_alignment }}
  {% endif %}
  {% if text_size > 35 %}
    mobile-shrink-text
  {% endif %}
{%- endcapture -%}

{% comment %} Section specific CSS {% endcomment %}
{%- capture section_css -%}
  .section {
    background-image: linear-gradient({{ section.settings.gradient_rotation }}deg, rgba(255,255,255,0), {{ section.settings.gradient }});
    background-color: {%- if background_alpha != 0 -%}{{ section.settings.background }}{%- endif -%};
  }

  .rich-text__heading {
    font-size: {{ heading_size }}px;
    color: {%- if heading_alpha != 0 -%}{{ heading_color }}{%- else -%}{{ settings.heading_color }}{%- endif -%};
    font-family: {{ heading_font.family }}, {{ heading_font.fallback_families }};
    font-weight: {{ heading_font.weight }};
    font-style: {{ heading_font.style }};
  }

  .rich-text__text {
    font-size: {{ text_size }}px;
    color: {%- if text_alpha != 0 -%}{{ text_color }}{%- else -%}{{ settings.regular_color }}{%- endif -%};
    font-family: {{ text_font.family }}, {{ text_font.fallback_families }};
    font-weight: {{ text_font.weight }};
    font-style: {{ text_font.style }};
  }
{%- endcapture -%}

{% style %}
  {{ heading_font | font_face }}
  {{ text_font | font_face }}

  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-right: {{ section.settings.padding_right }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    padding-left: {{ section.settings.padding_left }}px;

    {% if section.settings.width == 'wide' %}
      width: 100%;
    {% elsif section.settings.width == 'half' %}
      width: 50%;
    {% endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section_css,
    id: section.id,
  %}

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ section.settings.width }}
    {% if background_alpha != 0 or gradient_alpha != 0  %}
      has-background
    {% endif %}
  "
  {% if section.settings.animation != "none" %}
    data-scroll-class="{{ section.settings.animation }}"
  {% endif %}
>
  <div class="container has-limit">
    <div class="columns one-whole">
      <div class="rich-text__content card-content">
        {% if section.settings.title != blank %}
          <h2 class="{{ heading_classes }} is-hidden-mobile-only">
            {{- section.settings.title -}}
          </h2>
        {% endif %}

        {% if section.settings.title != blank or section.settings.mobile_title != blank %}
          <h2 class="{{ heading_classes }} is-hidden-desktop-only">
            {{- section.settings.mobile_title | default: section.settings.title -}}
          </h2>
        {% endif %}

        {% if section.settings.text != blank %}
          <div class="{{ text_classes }} is-hidden-mobile-only">
            {{- section.settings.text -}}
          </div>
        {% endif %}

        {% if section.settings.text != blank or section.settings.mobile_text != blank %}
          <div class="{{ text_classes }} is-hidden-desktop-only">
            {{- section.settings.mobile_text | default: section.settings.text -}}
          </div>
        {% endif %}

        {% if section.settings.button_label != blank %}
          <div class="buttons is-{{ section.settings.button_alignment }}">
            {%
              render 'button',
              label: section.settings.button_label,
              href: section.settings.button_link,
              style: section.settings.button_style,
              type: "link",
            %}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</section>

{% schema %}
  {
    "name": "Rich text",
    "class": "rich-text",
    "settings": [
      {
        "type": "header",
        "content": "Heading"
      },
      {
        "type": "text",
        "id": "title",
        "label": "Heading",
        "default": "Rich text"
      },
      {
        "type": "font_picker",
        "id": "heading_font",
        "label": "Font",
        "default": "open_sans_n4"
      },
      {
        "type": "select",
        "id": "heading_alignment",
        "label": "Heading alignment",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "justify",
            "label": "Justify"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "range",
        "id": "heading_size",
        "label": "Heading base size",
        "min": 20,
        "max": 120,
        "default": 30,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Text"
      },
      {
        "type": "richtext",
        "id": "text",
        "label": "Text",
        "default": "<p>Use this section to create some callout text on your page, or add more details about your shop, services, promotions, etc. If you have a large amount&nbsp;of text, you can opt to have it display in <strong>two columns<\/strong> instead so that it's much easier to read.<\/p>\n<p><\/p>\n<p>This section also includes&nbsp;the option to add a button&nbsp;in primary or secondary style, and also control the colors, including the option for a gradient background. Since this is rich text, you can also <em>format it as you wish<\/em> and&nbsp;<a href=\"\/collections\/all\" title=\"All Products\">include links<\/a>.<\/p>"
      },
      {
        "type": "font_picker",
        "id": "text_font",
        "label": "Font",
        "default": "open_sans_n4"
      },
      {
        "type": "select",
        "id": "text_alignment",
        "label": "Text alignment",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "justify",
            "label": "Justify"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "left"
      },
      {
        "type": "range",
        "id": "text_size",
        "label": "Text base size",
        "min": 8,
        "max": 40,
        "default": 14,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "columns",
        "label": "Number of text columns",
        "info": "Only applies to desktop.",
        "min": 1,
        "max": 3,
        "default": 2
      },
      {
        "type": "header",
        "content": "Button"
      },
      {
        "type": "text",
        "id": "button_label",
        "label": "Button label"
      },
      {
        "type": "url",
        "id": "button_link",
        "label": "Button link"
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "options": [
          {
            "value": "button--primary",
            "label": "Primary"
          },
          {
            "value": "button--secondary",
            "label": "Secondary"
          },
          {
            "value": "button--link-style",
            "label": "Link style"
          }
        ],
        "default": "button--primary"
      },
      {
        "type": "select",
        "id": "button_alignment",
        "label": "Button alignment",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "header",
        "content": "Colors"
      },
      {
        "type": "color",
        "id": "heading_color",
        "label": "Heading",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color",
        "id": "background",
        "label": "Background",
        "default": "#EEEEEE"
      },
      {
        "type": "color",
        "id": "gradient",
        "label": "Background gradient",
        "default": "#C0C0C0"
      },
      {
        "type": "range",
        "id": "gradient_rotation",
        "label": "Gradient rotation",
        "min": 0,
        "max": 180,
        "step": 10,
        "default": 180,
        "unit": "deg"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "wide",
        "options": [
          {
            "value": "half",
            "label": "Half"
          },
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_left",
        "label": "Left spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_right",
        "label": "Right spacing",
        "default": 0,
        "min": 0,
        "max": 80,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          },
          {
            "value": "zoomIn",
            "label": "Zoom in"
          }
        ]
      },
      {
        "type": "header",
        "content": "Mobile text"
      },
      {
        "type": "text",
        "id": "mobile_title",
        "label": "Mobile heading"
      },
      {
        "type": "richtext",
        "id": "mobile_text",
        "label": "Mobile text"
      },
      {
        "type": "select",
        "id": "mobile_alignment",
        "label": "Mobile alignment",
        "info": "Applies to heading, text, and button.",
        "options": [
          {
            "value": "none",
            "label": "Same as desktop"
          },
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "justify",
            "label": "Justify"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "none"
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced"
      },
      {
        "type": "paragraph",
        "content": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "presets": [
      {
        "name": "Rich text",
        "category": "Text",
        "settings": {

        }
      }
    ],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
