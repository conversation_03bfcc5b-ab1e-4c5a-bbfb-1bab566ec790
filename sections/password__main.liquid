{% comment %}
** Password page - main content area **
- Template
{% endcomment %}

{% assign id = section.id %}
{% comment %}Layout{% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% comment %} Advanced {% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% comment %} CSS {% endcomment %}
{% style %}
  #password-page-background {
    {% if section.settings.password-page-background == blank %}
      background: {{ section.settings.password_page_background_color }};
    {% else %}
      background: url({{ section.settings.password-page-background | img_url: '2000x' }}) center center;
    {% endif %}

    {% if section.settings.pagework_page_bg_stretched %}
      background-repeat: no-repeat;
      background-attachment: fixed;
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
    {% endif %}
  }

  .password-page-message {
    border-top: 1px solid {{ section.settings.password_page_text_color }};
    border-bottom: 1px solid {{ section.settings.password_page_text_color }};
  }

  .password-page-follow,
  .password-page-message,
  .password-social .social_icons a,
  .password-social .social_icons a:visited,
  .password-social .social_icons a:active,
  .password-footer,
  #password-page-background,
  #password-container h1,
  #open-me a {
    color: {{ section.settings.password_page_text_color }};
  }

  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' -%}
      width: 100%;
    {%- endif %}
  }

  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

<section class="section is-width-{{ width }} {{ css_class }}">
  <div id="password-container" class="text-align-center container is-justify-center is-align-center">
    <div class="one-half column password-page">

      {% if section.settings.custom_logo != nil %}
        <div class="one-whole column has-margin-bottom has-margin-top">
          <img class="password-logo" src="{{ section.settings.custom_logo | img_url: '720x' }}" alt="{{ shop.name }}" />
        </div>
      {% else %}
        <div class="one-whole column has-margin-bottom has-margin-top text-align-center">
            <h1 class="title password-title">{{ shop.name }}</h1>
        </div>

      {% endif %}

      <div class="one-whole column has-margin-bottom">
        <h2 class="password-page-message">{{ 'general.password_page.opening_soon' | t }}</h2>
      </div>

      {% unless shop.password_message == blank %}
      <div class="one-whole column has-margin-bottom">
        <p class="password-message">{{ shop.password_message }}</p>
      </div>
      {% endunless %}

      <div class="one-whole column has-margin-bottom">
        <div class="controls">
          <div id="open-me">
            <a href="#">{{ 'general.password_page.login_form_heading' | t }}</a>
          </div>
        </div>
      </div>

      <div class="password-page__newsletter one-whole column has-margin-bottom">
        {% if section.settings.newsletter_title %}
          <h3 class="newsletter__title title">{{ section.settings.newsletter_title }}</h3>
        {% endif %}

        {% if section.settings.newsletter_richtext != blank %}
          {{ section.settings.newsletter_richtext }}
        {% endif %}

        {% if section.settings.show_signup_form %}
          {%
            render 'newsletter-form',
            type: 'section',
            display_first_name: section.settings.display_first_name,
            display_last_name: section.settings.display_last_name,
            id: 'password',
          %}
        {% endif %}
      </div>

      {% if section.settings.social_media %}
        <div class="password-page__social-media one-whole column has-margin-bottom is-flex is-flex-column is-flex-wrap is-align-center">
          {% render 'social-icons' %}
        </div>
      {% endif %}


      <div class="one-whole column">
        <div class="password-footer text-is-small has-margin-bottom">

          {{ section.settings.copyright_text }}

          <div class="powered">
            <p>&copy; {{ "now" | date: "%Y" }} {{ shop.name }}. {% if section.settings.display_designed_by %}{{ 'layout.general.designer_credits_html' | t }}{% endif %}</p>
          </div>

          {% if section.settings.display_shopify %}
            <div class="shopify">
              <p>{{ 'general.password_page.powered_by_shopify_html' | t }}</p>
            </div>
          {% endif %}
        </div>
      </div>

    </div>
  </div>

  <div class="overlay overlay-data container is-justify-center is-align-center text-align-center">
    <button id="close-me" class="modal-close close password-modal__close is-large" aria-label="close">
      {% render 'icon', name: 'x' %}
    </button>

    <div class="modal-container one-third column medium-down--one-whole">
      {% form 'storefront_password' %}
        {{ form.errors | default_errors }}
        {% if form.errors.count > 0 %}
          <script>
            $( document ).ready(function() {
              $( ".overlay" ).addClass('overlay-open');
            });
          </script>
        {% endif %}
        <label for="password">{{ 'general.password_page.login_form_password_label' | t }} </label>

        <div class="modal-container__password">
          <input type="password" name="password" id="password" autofocus placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}"/>
          <input class="button button--primary sign_up" name="commit" type="submit" value="{{ 'general.password_page.login_form_submit' | t }}" />
        </div>
      {% endform %}

      <div id="owner">{{ 'general.password_page.admin_link_html' | t }} {{ 'general.password_page.or' | t }} <a href="/admin/online_store/preferences?tutorial=unlock">{{ 'general.password_page.change_your_password_settings' | t }}</a></div>
    </div>

  </div>
</section>

{% comment %} JavaScript {% endcomment %}
<script src="{{ 'z__jsPassword.js' | asset_url }}"></script>

{% schema %}
{
  "name": "Password page",
  "class": "password-page jsPassword",
  "settings": [
    {
      "type": "image_picker",
      "id": "custom_logo",
      "label": "Logo",
      "info": "720px wide recommended"
    },
    {
      "type": "image_picker",
      "id": "password-page-background",
      "label": "Background image",
      "info": "1800 x 1000px recommended"
    },
    {
      "type": "checkbox",
      "id": "pagework_page_bg_stretched",
      "label": "Stretch background to fill the password page",
      "default": true
    },
    {
      "type": "richtext",
      "id": "copyright_text",
      "label": "Copyright text"
    },
    {
      "type": "checkbox",
      "id": "display_designed_by",
      "label": "Show theme designer credits",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "display_shopify",
      "label": "Show Powered by Shopify",
      "default": true
    },
    {
      "type": "header",
      "content": "Social media"
    },
    {
      "type": "checkbox",
      "id": "social_media",
      "label": "Show social icons",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "password_page_background_color",
      "label": "Background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "password_page_text_color",
      "label": "Text",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Newsletter"
    },
    {
      "type": "text",
      "id": "newsletter_title",
      "label": "Heading",
      "default": "Subscribe"
    },
    {
      "type": "richtext",
      "id": "newsletter_richtext",
      "label": "Text",
      "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
    },
    {
      "type": "checkbox",
      "id": "show_signup_form",
      "label": "Show signup form",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "display_first_name",
      "label": "Show first name"
    },
    {
      "type": "checkbox",
      "id": "display_last_name",
      "label": "Show last name"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ]
}
{% endschema %}
