{% comment %}
** Collections - subcollection view **
- Features a subset of collections and products on a page
{% endcomment %}

{% assign id = section.id %}
{% assign overlay_background_opacity = section.settings.overlay_background_opacity %}
{% assign overlay_background = section.settings.overlay_background %}
{% assign overlay_text = section.settings.overlay_text %}
{% comment %}Layout{% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% assign animation = section.settings.animation | default: 'none' %}
{% comment %} Buttons {% endcomment %}
{% assign button_style = section.settings.button_style %}
{% comment %} Advanced {% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% comment %} Is the color set to transparent? {% endcomment %}
{% assign overlay_alpha = overlay_background | color_extract: 'alpha' %}
{% assign overlay_text_alpha = overlay_text | color_extract: 'alpha' %}
{% comment %} Opacity level {% endcomment %}
{% if overlay_alpha != 0 %}
  {% assign overlay_background_alpha = overlay_background_opacity | divided_by: 100.00 %}
{% endif %}

{% if section.settings.align_height %}
  {% assign collection_height = section.settings.collection_height %}
{% endif %}

{% assign menu = linklists[collection.handle] %}

{%- capture section_css -%}
  .collection-thumbnail-overlay {
    background-color: {%- if overlay_alpha != 0 -%}{{ overlay_background | color_modify: 'alpha', overlay_background_alpha }}{% endif %}
  }

  .collection-info__caption span {
    color: {%- if overlay_text_alpha != 0 -%}{{ overlay_text }}{% endif %}
  }

  {%- if section.settings.align_height -%}
    .placeholder-svg {
      height: {{ collection_height }}px;
      max-height: {{ collection_height }}px;
    }
  {%- endif -%}
{%- endcapture -%}

{% comment %} CSS {% endcomment %}
{%- style -%}
  #shopify-section-{{ section.id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {%- if width == 'wide' -%}
      width: 100%;
      max-width: none;
    {%- endif -%}
  }

  {% render 'css-loop',
          css: section_css,
          id: id
  %}
  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{%- endstyle -%}

  {% comment %} HTML markup {% endcomment %}
<section class="section
        {{ css_class }}
        is-width-{{ width }}"
        {% if animation != "none" %}
          data-scroll-class="{{ animation }}"
        {% endif %}>
  <div class="list-collection-wrapper container">
    {%- assign menu = linklists[collection.handle] -%}
    {% for link in menu.links %}
      {%- assign collection_linklist = link.object -%}
      {% capture collection_title %}{{ collection_linklist.title | escape }}{% endcapture %}
      <div class="
        {% render 'column-width', value: section.settings.collections_per_row  %}
        column thumbnail
        {% if section.settings.align_height %}list-collection--align-height{% endif %}
        list-collection__thumbnail medium-down--one-half
        {% if section.settings.mobile_collections_per_row == '1' %}small-down--one-whole{% else %}small-down--one-half{% endif %}">
        <div class="product-wrap">
          <div class="collection-thumbnail-overlay"></div>

          {% if collection_linklist.image != blank %}
            {%- assign collection_image = collection_linklist.image -%}
            {%- assign collection_image_alt = collection_linklist.image.alt | escape -%}
          {% else collection_linklist.empty? == false %}
            {%- assign collection_image = collection_linklist.products.first.featured_image -%}
            {%- assign collection_image_alt = collection_linklist.products.first.featured_image.alt | escape -%}
          {% endif %}

          {% if collection_image != blank %}
            <div class="thumbnail image__container has-image-crop">
              {%
                render 'image-element',
                image: collection_image.src,
                alt: collection_image_alt,
                image_crop: true,
                max_height: collection_height,
              %}
            </div>
          {% else %}
            {% capture num %}{% cycle "1", "2", "3", "4", "5", "6" %}{% endcapture %}
            {{ 'collection-' | append: num | placeholder_svg_tag: 'placeholder-svg' }}
          {% endif %}
          <a class="collection-info__caption" href="{{ collection_linklist.url }}">
            {% if section.settings.collection_title_below_image == blank %}
              <div class="collection-info__caption-wrapper">
                <span class="title">
                  {{ collection_linklist.title }}
                </span>
                {%- assign view_products = 'collections.general.view_all' | t -%}
                {% render 'button',
                        label: view_products,
                        href: collection_linklist.url,
                        style: section.settings.button_style
                %}
              </div>
            {% endif %}
          </a>
        </div>
        {% if section.settings.collection_title_below_image != blank %}
          <a class="collection-info__caption-wrapper collection-info__caption--below-image" href="{{ collection_linklist.url }}">
            <span class="title">
              {{ collection_linklist.title }}
            </span>
            {%- assign view_products = 'collections.general.view_all' | t -%}
            {% render 'button',
                    label: view_products,
                    href: collection_linklist.url,
                    style: section.settings.button_style
            %}
          </a>
        {% endif %}
      </div>
    {% endfor %}
  </div>
</section>

{% schema %}

{
  "name": "Sub collections",
  "class": "sub-collection-main",
  "settings": [
    {
      "type": "range",
      "id": "collections_per_row",
      "label": "Collections per row",
      "min": 2,
      "max": 4,
      "step": 1,
      "default": 3
    },
    {
      "type": "select",
      "id": "mobile_collections_per_row",
      "label": "Collections per row on mobile",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "align_height",
      "label": "Align to height",
      "default": false
    },
    {
      "type": "range",
      "id": "collection_height",
      "label": "Collection image height",
      "min": 200,
      "max": 400,
      "step": 10,
      "default": 400,
      "unit": "px",
      "info": "Applied when 'Align to height' is also enabled. [Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360024344614)"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "default": "button--secondary",
      "options": [
        {
          "value": "button--primary",
          "label": "Primary"
        },
        {
          "value": "button--secondary",
          "label": "Secondary"
        },
        {
          "value": "button--link-style",
          "label": "Link"
        }
      ]
    },
    {
      "type": "color",
      "id": "overlay_background",
      "label": "Overlay",
      "default": "#939393"
    },
    {
      "type": "range",
      "id": "overlay_background_opacity",
      "label": "Overlay opacity",
      "min": 0,
      "max": 100,
      "step": 10,
      "default": 70,
      "unit": "%"
    },
    {
      "type": "color",
      "id": "overlay_text",
      "label": "Text",
      "default": "#FFFFFF"
    },
    {
      "type": "checkbox",
      "id": "collection_title_below_image",
      "label": "Show collection title below image",
      "default": false
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "standard",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ]
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 0,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ]
}

{% endschema %}
