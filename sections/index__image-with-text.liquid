{% comment %}
** Image with text **
{% endcomment %}

{%- liquid
  assign blocks = section.blocks
  assign mobile_text_position = section.settings.mobile_text_position
  assign image_block_count = 0
  assign text_block_count = 0
  assign first_image_block_found = false

  for block in blocks
    if block.type == 'image'
      assign image_block_count = image_block_count | plus: 1
    elsif block.type == 'text'
      assign text_block_count = text_block_count | plus: 1
    endif
  endfor
-%}

{% comment %} Section specific CSS {% endcomment %}
{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    {% if section.settings.width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.mobile_padding_top }}px;
      padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
    }
  }

  {% render 'css-loop',
          css: section.settings.custom_css,
          id: section.id
  %}
{% endstyle %}

{% comment %} HTML markup {% endcomment %}
<section class="section
                {{ section.settings.css_class }}
                is-width-{{ section.settings.width }}
                {% if section.settings.show_gutter == false %}
                  has-no-side-gutter
                  has-background
                {% else %}
                  has-gutter-enabled
                {% endif %}
                {% if section.settings.width == 'wide' %}
                  equal-columns--outside-trim
                {% endif %}
                "
                {% if section.settings.animation != "none" %}
                  data-scroll-class="{{ section.settings.animation }}"
                {% endif %}>
  <div class="container
              ">
    {% if blocks.size > 0 %}
      {% for block in blocks %}

        {% if block.type != 'image' %}

          {%- liquid
            # Content settings
            assign title = block.settings.title
            assign heading_size = block.settings.heading_size
            assign text = block.settings.text
            assign alignment = block.settings.alignment
            assign mobile_title = block.settings.mobile_title
            assign mobile_heading_size = block.settings.mobile_heading_size
            assign mobile_text = block.settings.mobile_text
            assign mobile_alignment = block.settings.mobile_alignment

            if mobile_heading_size == 'same_as_desktop'
              assign mobile_heading_size = heading_size
            endif

            if mobile_alignment == 'same_as_desktop'
              assign mobile_alignment = alignment
            endif

            # Is the color set to transparent?
            assign background_alpha = block.settings.background | color_extract: 'alpha'
            assign text_alpha = block.settings.text_color | color_extract: 'alpha'
          -%}

          {% comment %} Block specific CSS {% endcomment %}
          {%- capture block_css -%}
            .image-with-text__heading {
              color: {%- if text_alpha != 0 -%}{{ block.settings.text_color }}{%- else -%}{{ settings.heading_color }}{%- endif -%};
            }

            .image-with-text__text {
              color: {%- if text_alpha != 0 -%}{{ block.settings.text_color }}{%- else -%}{{ settings.regular_color }}{%- endif -%};
            }
          {%- endcapture -%}

          {% style %}
            #shopify-section-{{ block.id }} {
                background-color: {%- if background_alpha != 0 -%}{{ block.settings.background }}{% endif %};
              }
            {% render 'css-loop',
                    css: block_css,
                    id: block.id
            %}
          {% endstyle %}

          <div
            class="
              image-with-text__text-column
              image-with-text__column
              image-with-text__alignment-{{ alignment }}
              image-with-text__mobile-alignment-{{ mobile_alignment }}
              {% render 'column-width', value: forloop.length %}
              medium-down--one-whole
              column
              has-padding-left
              has-padding-right
              {% if background_alpha != 0 %}has-background{% endif %}
              is-flex
              is-flex-wrap
              is-align-{{ block.settings.vertical_position }}
            "
            {% if mobile_text_position == 'top' or mobile_text_position == 'middle' and image_block_count == 2 %}
              style="--text-flex-order: -1;"
            {% endif %}
            {% if type != 'block' %}
              {{ block.shopify_attributes }} id="shopify-section-{{ block.id }}"
            {% endif %}
          >
            <div class="image-with-text__wrapper">
              {% if title != blank %}
                <h3
                  class="
                    image-with-text__heading
                    text-align-{{ alignment }}
                    is-{{ heading_size }}
                    is-hidden-mobile-only
                  "
                >
                  {{ title }}
                </h3>
              {% endif %}

              {%- if mobile_title != blank or title != blank -%}
                <h3
                  class="
                    image-with-text__heading
                    text-align-{{ mobile_alignment }}
                    is-{{ mobile_heading_size }}
                    is-hidden-desktop-only
                  "
                >
                  {%- if mobile_title != blank -%}
                    {{- mobile_title -}}
                  {%- else -%}
                    {{- title -}}
                  {%- endif -%}
                </h3>
              {%- endif -%}

              {% if text != blank %}
                <div
                  class="
                    image-with-text__text
                    text-align-{{ alignment }}
                    content
                    is-hidden-mobile-only
                  "
                >
                  {{ text }}
                </div>
              {% endif %}

              {%- if mobile_text != blank or text != blank -%}
                <div
                  class="
                    image-with-text__text
                    text-align-{{ mobile_alignment }}
                    content
                    is-hidden-desktop-only
                  "
                >
                  {%- if mobile_text != blank -%}
                    {{- mobile_text -}}
                  {%- else -%}
                    {{- text -}}
                  {%- endif -%}
                </div>
              {%- endif -%}

              {% if block.settings.button_label != blank %}
                <div
                  class="
                    image-with-text__button
                    image-with-text__button--text-align-{{ alignment }}
                    image-with-text__button--mobile-text-align-{{ mobile_alignment }}
                    buttons
                  "
                >
                  {%
                    render 'button',
                    label: block.settings.button_label,
                    href: block.settings.link,
                    style: block.settings.button_style,
                    type: "link",
                  %}
                </div>
              {% endif %}
            </div>
          </div>

        {% elsif block.type == 'image' %}
          {%- liquid
            # Content settings
            assign image = block.settings.image
            assign mobile_image = block.settings.mobile_image

            assign image_additional_wrapper_classes = ''
            assign image_placeholder_svg_tags = 'placeholder-svg'

            if mobile_image != blank
              assign image_additional_wrapper_classes = 'is-hidden-mobile-only'
              assign image_placeholder_svg_tags = image_placeholder_svg_tags | append: ' is-hidden-mobile-only'
            endif
          -%}

          <div
            class="
              image-with-text__image-column
              image-with-text__column
              {% if forloop.length == 1 %}
                one-whole
              {% elsif forloop.length == 2 %}
                one-half
              {% elsif forloop.length == 3 %}
                one-third
              {% endif %}
              medium-down--one-whole
              column
              is-flex
              is-align-center
            "
            {% if mobile_text_position == 'middle' and text_block_count > 0 and first_image_block_found == false and image_block_count == 2 and forloop.index0 <= 1 %}
              {% assign first_image_block_found = true %}
              style="--image-flex-order: -2;"
            {% elsif mobile_text_position == 'bottom' and text_block_count > 0 %}
              style="--image-flex-order: -1;"
            {% endif %}
            {% if type != 'block' %}
              {{ block.shopify_attributes }}
              id="shopify-section-{{ block.id }}"
            {% endif %}
          >
            {% if block.settings.image_link %}
              <a class="image-with-text__link" href="{{ block.settings.image_link }}">
            {% endif %}
              {% if image != blank %}
                {%
                  render 'image-element',
                  image: image,
                  alt: image.alt,
                  stretch_width: true,
                  additional_classes: 'image-with-text__image',
                  additional_wrapper_classes: image_additional_wrapper_classes,
                  focal_point: image.presentation.focal_point,
                %}
              {% else %}
                {{ 'image' | placeholder_svg_tag: image_placeholder_svg_tags }}
              {% endif %}

              {%- if mobile_image != blank -%}
                {%
                  render 'image-element',
                  image: mobile_image,
                  alt: mobile_image.alt,
                  stretch_width: true,
                  additional_classes: 'image-with-text__image',
                  additional_wrapper_classes: 'is-hidden-desktop-only',
                  focal_point: mobile_image.presentation.focal_point,
                %}
              {%- endif -%}
            {% if block.settings.image_link %}
              </a>
            {% endif %}
          </div>
        {% endif %}

    {% endfor %}
  {% endif %}

  </div>
</section>

{% schema %}
{
  "name": "Image with text",
  "class": "image-with-text image-with-text-1",
  "max_blocks": 3,
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "standard",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "show_gutter",
      "label": "Show gutter",
      "default": true
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 0,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "default": 0,
      "min": 0,
      "max": 80,
      "unit": "px"
    },
    {
      "type": "select",
      "id": "animation",
      "label": "Animation",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "fadeIn",
          "label": "Fade in"
        },
        {
          "value": "fadeInDown",
          "label": "Fade in down"
        },
        {
          "value": "fadeInLeft",
          "label": "Fade in left"
        },
        {
          "value": "fadeInRight",
          "label": "Fade in right"
        },
        {
          "value": "slideInLeft",
          "label": "Slide in left"
        },
        {
          "value": "slideInRight",
          "label": "Slide in right"
        },
        {
          "value": "zoomIn",
          "label": "Zoom in"
        }
      ]
    },
    {
      "type": "header",
      "content": "Mobile text"
    },
    {
      "type": "select",
      "id": "mobile_text_position",
      "label": "Mobile text position",
      "options": [
        {
          "value": "same_as_desktop",
          "label": "Same as desktop"
        },
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "middle",
          "label": "Middle"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "same_as_desktop",
      "info": "Only applies when both image and text blocks are shown."
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "range",
      "id": "mobile_padding_top",
      "label": "Mobile top spacing",
      "min": 0,
      "max": 80,
      "step": 1,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "mobile_padding_bottom",
      "label": "Mobile bottom spacing",
      "min": 0,
      "max": 80,
      "step": 1,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1024 x 1024px recommended"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile image",
          "info": "Optional. Desktop image will show on mobile by default."
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "Link"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading text here"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "Heading base size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "regular",
              "label": "Regular"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "regular"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        },
        {
          "type": "text_alignment",
          "id": "alignment",
          "label": "Text alignment"
        },
        {
          "type": "select",
          "id": "vertical_position",
          "label": "Vertical text position",
          "options": [
            {
              "value": "start",
              "label": "Top"
            },
            {
              "value": "center",
              "label": "Middle"
            },
            {
              "value": "end",
              "label": "Bottom"
            }
          ],
          "default": "center"
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "View all"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Button link"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "value": "button--link-style",
              "label": "Link style"
            }
          ],
          "default": "button--primary"
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "header",
          "content": "Mobile text"
        },
        {
          "type": "text",
          "id": "mobile_title",
          "label": "Mobile heading"
        },
        {
          "type": "select",
          "id": "mobile_heading_size",
          "label": "Mobile heading base size",
          "options": [
            {
              "value": "same_as_desktop",
              "label": "Same as desktop"
            },
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "regular",
              "label": "Regular"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "same_as_desktop"
        },
        {
          "type": "richtext",
          "id": "mobile_text",
          "label": "Mobile text"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "label": "Mobile text alignment",
          "options": [
            {
              "value": "same_as_desktop",
              "label": "Same as desktop"
            },
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "same_as_desktop"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Image with text",
      "category": "Image",
      "settings": {
          "padding_top": 20,
          "padding_bottom": 20
      },
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "text"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": [
      "*"
    ]
  }
}
{% endschema %}
