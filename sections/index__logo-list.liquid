{% comment %}
** <PERSON><PERSON> list **
{% endcomment %}

{% liquid
  # Is the color set to transparent?
  assign background_alpha = section.settings.background | color_extract: 'alpha'
  assign gradient_alpha = section.settings.gradient | color_extract: 'alpha'
%}

{% comment %} Section specific CSS {% endcomment %}
{% capture section_css -%}
  .section {
    background-image: linear-gradient({{ section.settings.gradient_rotation }}deg, rgba(255,255,255,0), {{ section.settings.gradient }});
    background-color: {%- if background_alpha != 0 -%}{{ section.settings.background }}{%- endif -%};
  }
{%- endcapture -%}

{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-right: {{ section.settings.padding_right }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    padding-left: {{ section.settings.padding_left }}px;

    {% if section.settings.width == 'wide' %}
      width: 100%;
    {% elsif section.settings.width == 'half'  %}
      width: 50%;
    {% endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section_css,
    id: section.id,
  %}

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ section.settings.width }}
    {% if background_alpha != 0 or gradient_alpha != 0  %}
      has-background
    {% endif %}
  "
  {% if section.settings.animation != "none" %}
    data-scroll-class="{{ section.settings.animation }}"
  {% endif %}
>
  <div
    class="
      container
      has-column-padding-bottom
      is-justify-center
      is-align-center
      small-down--has-limit
      is-{{ section.settings.logo_size }}
      logo-list__wrapper
    "
  >
    {% if section.blocks.size > 0 %}
      {% for block in section.blocks %}
        {% comment %}Add number to id for each loop{% endcomment %}
        {% capture image %}image{{ index }}{% endcapture %}
        {% capture link %}link{{ index }}{% endcapture %}

        {% assign image = block.settings[image] %}

        <div
          class="
            logo-list__item
            small-down--one-half
            column
            {% if section.settings.logos_per_row == 1 %}
              one-whole
            {% elsif section.settings.logos_per_row == 2 %}
              one-half
            {% elsif section.settings.logos_per_row == 3 %}
              one-third
            {% elsif section.settings.logos_per_row == 4 %}
              one-fourth
            {% elsif section.settings.logos_per_row == 5 %}
              one-fifth
            {% elsif section.settings.logos_per_row == 6 %}
              one-sixth
            {% elsif section.settings.logos_per_row == 7 %}
              one-seventh
            {% elsif section.settings.logos_per_row == 8 %}
              one-eighth
            {% endif %}
          "
          {% if type != 'block' %}
            {{ block.shopify_attributes }}
          {% endif %}
        >
          {% if block.settings[link] != blank %}
            <a href="{{ block.settings[link] }}" class="logo-list__link">
          {% endif %}

          {% if image != blank %}
            {%
              render 'image-element',
              image: image,
              alt: image.alt,
              focal_point: image.presentation.focal_point,
            %}
          {% else %}
            {{ 'logo' | placeholder_svg_tag: 'placeholder-svg' }}
          {% endif %}

          {% if block.settings[link] != blank %}
            </a>
          {% endif %}
        </div>
      {% endfor %}
    {% endif %}
  </div>
</section>

{% schema %}
  {
    "name": "Logo list",
    "class": "logo-list",
    "max_blocks": 20,
    "settings": [
      {
        "type": "range",
        "id": "logos_per_row",
        "label": "Logos per row",
        "min": 1,
        "max": 8,
        "default": 5
      },
      {
        "type": "select",
        "id": "logo_size",
        "label": "Logo size",
        "info": "Applies to desktop only.",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ],
        "default": "medium"
      },
      {
        "type": "header",
        "content": "Background"
      },
      {
        "type": "color",
        "id": "background",
        "label": "Background",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color",
        "id": "gradient",
        "label": "Background gradient",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "range",
        "id": "gradient_rotation",
        "label": "Gradient rotation",
        "min": 0,
        "max": 180,
        "step": 10,
        "default": 0,
        "unit": "deg"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "half",
            "label": "Half"
          },
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_left",
        "label": "Left spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_right",
        "label": "Right spacing",
        "default": 0,
        "min": 0,
        "max": 80,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          },
          {
            "value": "zoomIn",
            "label": "Zoom in"
          }
        ]
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced"
      },
      {
        "type": "paragraph",
        "content": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "logo_image",
        "name": "Logo",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image",
            "info": "800 x 800px recommended"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Logo list",
        "category": "Image",
        "blocks": [
          {
            "type": "logo_image"
          },
          {
            "type": "logo_image"
          },
          {
            "type": "logo_image"
          },
          {
            "type": "logo_image"
          },
          {
            "type": "logo_image"
          }
        ]
      }
    ],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
