{% comment %}
** Featured promotions **
{% endcomment %}

{% liquid
  assign blocks = section.blocks

  # Is the color set to transparent?
  assign overlay_alpha = section.settings.overlay_background | color_extract: 'alpha'
  assign overlay_text_alpha = section.settings.overlay_text | color_extract: 'alpha'

  # Opacity level
  if overlay_alpha != 0
    assign overlay_background_alpha = section.settings.overlay_background_opacity | divided_by: 100.00
  endif
%}

{%- capture section_css -%}
  .featured-promotions__content {
    border-radius: {{ section.settings.border_radius }}px;
  }

  .featured-promotions__overlay {
    border-radius: {{ section.settings.border_radius }}px;
    color: {% if overlay_text_alpha != 0 %}{{ section.settings.overlay_text }}{% else %}{{ settings.regular_color }}{% endif %};
    border-color: {% if overlay_text_alpha != 0 %}{{ section.settings.overlay_text }}{% else %}{{ settings.regular_color }}{% endif %};
  }

  .featured-promotions__overlay .featured-promotions__link {
    color: {{ section.settings.overlay_text }};
  }

  .has-color-override,
  .has-color-override strong {
    color: {% if overlay_text_alpha != 0 %}{{ section.settings.overlay_text }}{% else %}{{ settings.regular_color }}{% endif %};
    border-color: {% if overlay_text_alpha != 0 %}{{ section.settings.overlay_text }}{% else %}{{ settings.regular_color }}{% endif %};
  }

  .featured-promotions__content:before {
    background-color: {% if overlay_alpha != 0 %}{{ section.settings.overlay_background | color_modify: 'alpha', overlay_background_alpha }};{% endif %}
  }
{%- endcapture -%}

{% comment %} Section specific CSS {% endcomment %}
{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;

    {% if section.settings.width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section_css,
    id: section.id,
  %}

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ section.settings.width }}
    {% if section.settings.show_gutter == false %}
      has-no-side-gutter
      has-background
    {% else %}
      has-gutter-enabled
    {% endif %}
    {% if section.settings.width == 'wide' %}
      equal-columns--outside-trim
    {% endif %}
  "
  {% if section.settings.animation != "none" %}
    data-scroll-class="{{ section.settings.animation }}"
  {% endif %}
>
  {% if blocks.size > 0 %}
    {% if blocks.size > 3 %}
      <div class="container">
        <div
          class="
            featured-promotions__nav-wrapper
            one-whole
            column
            is-flex
          "
        >
          <div class="featured-promotions__nav featured-promotions__nav--prev">
            {%
              render 'icon',
              name: 'left-arrow',
            %}
          </div>

          <div class="featured-promotions__nav featured-promotions__nav--next ">
            {%
              render 'icon',
              name: 'right-arrow',
            %}
          </div>
        </div>
      </div>
    {% endif %}

    <div
      class="
        featured-promotions__wrapper
        {% if blocks.size <= 3 %}
          container
        {% endif %}
      "
      {% if blocks.size > 3 %}
        data-featured-promotions-slider
      {% endif %}
    >
      {% for block in blocks %}
        <div
          class="
            featured-promotions__block
            featured-promotions__block--{{ forloop.length }}
            {% if block.settings.link %}
              featured-promotions__block--has-link
            {% endif %}
            {% if section.settings.crop_images %}
              has-image-crop
            {% endif %}
            medium-down--one-whole
            {% if blocks.size == 1 %}
              one-whole
            {% elsif blocks.size == 2 %}
              one-half
            {% else %}
              one-third
            {% endif %}
            column
            has-color-override
          "
          id="shopify-section-{{ block.id }}"
          data-promo-index={{ forloop.index0 }}
          {{ block.shopify_attributes }}
        >
          {% liquid
            assign image = block.settings.image
            assign mobile_image = block.settings.mobile_image
            assign placeholder_svg_tag = 'placeholder-svg'

            if mobile_image != blank
              assign is_hidden_mobile_only = 'is-hidden-mobile-only'
              assign placeholder_svg_tag = 'placeholder-svg is-hidden-mobile-only'
            endif

            # Is the color set to transparent?
            assign block_overlay_alpha = block.settings.overlay_background | color_extract: 'alpha'
            assign block_overlay_text_alpha = block.settings.overlay_text | color_extract: 'alpha'

            # Opacity level
            if block_overlay_alpha != 0
              assign block_overlay_background_alpha = block.settings.overlay_background_opacity | divided_by: 100.00
            endif
          %}

          {% comment %} Block specific CSS {% endcomment %}
          {% capture block_css -%}
            .has-color-override,
            .has-color-override strong {
              color: {%- if block_overlay_text_alpha != 0 -%}{{ block.settings.overlay_text }}{% endif %};
              border-color: {%- if block_overlay_text_alpha != 0 -%}{{ block.settings.overlay_text }}{% endif %};
            }

            .featured-promotions__content:before {
              background-color: {%- if block_overlay_alpha != 0 -%}{{ block.settings.overlay_background | color_modify: 'alpha', block_overlay_background_alpha }}{% endif %};
            }

            .featured-promotions__overlay {
              color: {%- if block_overlay_text_alpha != 0 -%}{{ block.settings.overlay_text }}{% endif %};
              border-color: {%- if block_overlay_text_alpha != 0 -%}{{ block.settings.overlay_text }}{% endif %};
            }

            .featured-promotions__overlay .featured-promotions__link {
              color: {%- if block_overlay_text_alpha != 0 -%}{{ block.settings.overlay_text }}{% endif %};
            }
          {%- endcapture -%}

          {% style %}
            {%
              render 'css-loop',
              css: block_css,
              id: block.id,
            %}
          {% endstyle %}

          <div class="featured-promotions__content">
            {% if image != blank %}
              {%
                render 'image-element',
                image: image,
                alt: image.alt,
                additional_wrapper_classes: is_hidden_mobile_only,
                focal_point: image.presentation.focal_point,
              %}
            {% else %}
              {{ 'collection-1' | placeholder_svg_tag: placeholder_svg_tag }}
            {% endif %}

            {% if mobile_image != blank %}
              {%
                render 'image-element',
                image: mobile_image,
                alt: mobile_image.alt,
                additional_wrapper_classes: 'is-hidden-desktop-only',
                focal_point: mobile_image.presentation.focal_point,
              %}
            {% endif %}

           {% if block.settings.link %}
              <a href="{{ block.settings.link }}" class="featured-promotions__link"  title="{{ block.settings.image.alt }}">
            {% endif %}
              <div class="featured-promotions__overlay {% if section.settings.show_border %}has-border{% endif %}">
                {% if block.settings.icon_label != blank %}
                  {%
                    render 'icon',
                    name: block.settings.icon_label | downcase
                  %}
                {% endif %}

                {% if block.settings.title != blank %}
                  <h3 class="featured-promotions__title has-color-override">
                    {{- block.settings.title -}}
                  </h3>
                {% endif %}

                {% if block.settings.text != blank %}
                  <div
                    class="
                      subtitle
                      featured-promotions__subtitle
                      has-color-override
                    "
                  >
                    {{- block.settings.text -}}
                  </div>
                {% endif %}
              </div>
            {% if block.settings.link %}
              </a>
            {% endif %}
          </div>
        </div>
      {% endfor %}
    </div>
  {% endif %}
</section>

{% comment %}JavaScript{% endcomment %}
<script data-theme-editor-load-script src="{{ 'z__jsFeaturedPromos.js' | asset_url }}"></script>

{% schema %}
  {
    "name": "Featured promotions",
    "class": "featured-promotions jsFeaturedPromos",
    "max_blocks": 21,
    "settings": [
      {
        "type": "range",
        "id": "border_radius",
        "label": "Corner radius",
        "min": 0,
        "max": 25,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "checkbox",
        "id": "crop_images",
        "label": "Crop images",
        "default": true
      },
      {
        "type": "header",
        "content": "Overlay"
      },
      {
        "type": "checkbox",
        "id": "show_border",
        "label": "Show border",
        "default": true
      },
      {
        "type": "color",
        "id": "overlay_text",
        "label": "Text color",
        "default": "#FFFFFF"
      },
      {
        "type": "color",
        "id": "overlay_background",
        "label": "Overlay color",
        "default": "#939393"
      },
      {
        "type": "range",
        "id": "overlay_background_opacity",
        "label": "Overlay opacity",
        "min": 0,
        "max": 100,
        "step": 10,
        "default": 70,
        "unit": "%"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "show_gutter",
        "label": "Show gutter",
        "default": true
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          },
          {
            "value": "zoomIn",
            "label": "Zoom in"
          }
        ]
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "image",
        "name": "Promotion",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image",
            "info": "800 x 600px recommended"
          },
          {
            "type": "image_picker",
            "id": "mobile_image",
            "label": "Mobile image",
            "info": "Optional. Desktop image will show on mobile by default."
          },
          {
            "type": "text",
            "id": "icon_label",
            "label": "Icon",
            "placeholder": "Enter icon name",
            "info": "[Icon list](https://help.outofthesandbox.com/hc/en-us/articles/360021570294)",
            "default": "truck"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Special deal"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Promotion description details.</p>"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link"
          },
          {
            "type": "header",
            "content": "Overlay"
          },
          {
            "type": "color",
            "id": "overlay_text",
            "label": "Text color",
            "default": "#FFFFFF",
            "default": "rgba(0,0,0,0)"
          },
          {
            "type": "color",
            "id": "overlay_background",
            "label": "Overlay color",
            "default": "rgba(0,0,0,0)"
          },
          {
            "type": "range",
            "id": "overlay_background_opacity",
            "label": "Overlay opacity",
            "min": 0,
            "max": 100,
            "step": 10,
            "default": 70,
            "unit": "%"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Featured promotions",
        "category": "Image",
        "blocks": [
          {
            "type": "image",
            "settings": {
              "icon_label": "truck"
            }
          },
          {
            "type": "image",
            "settings": {
              "icon_label": "bag"
            }
          },
          {
            "type": "image",
            "settings": {
              "icon_label": "tag"
            }
          }
        ]
      }
    ],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
