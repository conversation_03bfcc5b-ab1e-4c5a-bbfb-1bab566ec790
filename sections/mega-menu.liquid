{% comment %}
** Mega menu - static **
{% endcomment %}

{% assign id = section.id %}

<section class="mega-menu__section mega-menu__{{ id }}">
  {% include 'include-mega-menu', id: id %}

  <script
    type="application/json"
    data-section-id="{{ id }}"
    data-section-data
  >
    {
      "parent_link": {{ section.settings.parent_link | handleize | json }},
      "section_id": {{ id | json }}
    }
  </script>
  <script src="{{ 'z__jsMegaMenu.js' | asset_url }}"></script>
</section>

{% schema %}
  {
    "name": "Mega menu",
    "class": "mega-menu-section jsMegaMenu",
    "max_blocks": 6,
    "settings": [
      {
        "type": "header",
        "content": "Mega menu",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360021253094)"
      },
      {
        "type": "text",
        "id": "parent_link",
        "label": "Mega menu trigger",
        "default": "catalog",
        "info": "Add the menu item that you want to turn into a mega menu."
      },
      {
        "type": "color",
        "id": "mega_menu_background_color",
        "label": "Background color",
        "default": "#f6f6f6"
      },
      {
        "type": "color",
        "id": "mega_menu_text_color",
        "label": "Text color",
        "default": "#404040"
      },
      {
        "type": "checkbox",
        "id": "enable_mega_menu_border",
        "label": "Show borders",
        "default": false
      },
      {
        "type": "header",
        "content": "Bottom bar"
      },
      {
        "type": "checkbox",
        "id": "show_mega_bottom_bar",
        "label": "Show bottom bar",
        "default": true
      },
      {
        "type": "text",
        "id": "mega_bottom_bar_icon",
        "label": "Icon",
        "placeholder": "Enter icon name",
        "info": "[Icon list](https://help.outofthesandbox.com/hc/en-us/articles/360021570294)"
      },
      {
        "type": "text",
        "id": "mega_bottom_bar_text",
        "label": "Text",
        "default": "Add your deal, information or promotional text"
      },
      {
        "type": "url",
        "id": "mega_bottom_bar_link",
        "label": "Link"
      },
      {
        "type": "select",
        "id": "size",
        "label": "Base size",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "regular",
            "label": "Regular"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ],
        "default": "regular"
      },
      {
        "type": "color",
        "id": "bottom_banner_bg",
        "label": "Background color",
        "default": "#007ace"
      },
      {
        "type": "color",
        "id": "bottom_banner_text",
        "label": "Text color",
        "default": "#efefef"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "html",
        "name": "Custom HTML",
        "settings": [
          {
            "type": "textarea",
            "id": "html_content",
            "label": "HTML",
            "default": "<div class='container is-flex is-justify-center'><h2 class='title'>Your own custom HTML</h2></div>"
          }
        ]
      },
      {
        "type": "menu",
        "name": "Menu",
        "settings": [
          {
            "type": "link_list",
            "id": "menu_1",
            "label": "Menu",
            "info": "This menu won't show drop-down items."
          },
          {
            "type": "url",
            "id": "menu_1_link",
            "label": "Menu heading link"
          }
        ]
      },
      {
        "type": "image",
        "name": "Image",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image",
            "info": "600 x 600px recommended"
          },
          {
            "type": "url",
            "id": "image_link",
            "label": "Image link"
          },
          {
            "type": "richtext",
            "id": "image_caption",
            "label": "Text",
            "default": "<p>Image caption appears here</p>"
          }
        ]
      },
      {
        "type": "mixed",
        "name": "Mixed",
        "settings": [
          {
            "type": "header",
            "content": "Top content"
          },
          {
            "type": "text",
            "id": "richtext_top",
            "label": "Heading",
            "default": "Your headline"
          },
          {
            "type": "image_picker",
            "id": "image_top",
            "label": "Image",
            "info": "600 x 600px recommended"
          },
          {
            "type": "url",
            "id": "image_link_top",
            "label": "Image link"
          },
          {
            "type": "richtext",
            "id": "image_caption_top",
            "label": "Text"
          },
          {
            "type": "header",
            "content": "Navigation"
          },
          {
            "type": "link_list",
            "id": "menu_1",
            "label": "Menu 1",
            "info": "This menu won't show drop-down items."
          },
          {
            "type": "url",
            "id": "menu_1_link",
            "label": "Menu 1 heading link",
            "info": "Add a link to the title of the menu."
          },
          {
            "type": "link_list",
            "id": "menu_2",
            "label": "Menu 2",
            "info": "This menu won't show drop-down items."
          },
          {
            "type": "url",
            "id": "menu_2_link",
            "label": "Menu 2 heading link",
            "info": "Add a link to the title of the menu."
          },
          {
            "type": "link_list",
            "id": "menu_3",
            "label": "Menu 3",
            "info": "This menu won't show drop-down items."
          },
          {
            "type": "url",
            "id": "menu_3_link",
            "label": "Menu 3 heading link",
            "info": "Add a link to the title of the menu."
          },
          {
            "type": "link_list",
            "id": "menu_4",
            "label": "Menu 4",
            "info": "This menu won't show drop-down items."
          },
          {
            "type": "url",
            "id": "menu_4_link",
            "label": "Menu 4 heading link",
            "info": "Add a link to the title of the menu."
          },
          {
            "type": "header",
            "content": "Bottom content"
          },
          {
            "type": "image_picker",
            "id": "image_bottom",
            "label": "Image",
            "info": "600 x 600px recommended"
          },
          {
            "type": "url",
            "id": "image_link_bottom",
            "label": "Image link"
          },
          {
            "type": "richtext",
            "id": "richtext_bottom",
            "label": "Text"
          }
        ]
      },
      {
        "type": "featured_product",
        "name": "Featured product",
        "settings": [
          {
            "type": "product",
            "id": "featured_product",
            "label": "Product"
          },
          {
            "type": "richtext",
            "id": "richtext_bottom",
            "label": "Text"
          }
        ]
      },
      {
        "type": "featured_promo",
        "name": "Featured promotion",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image",
            "info": "600 x 600px recommended"
          },
          {
            "type": "richtext",
            "id": "richtext",
            "label": "Text",
            "default": "<p>Add description and links to your promotion</p>"
          },
          {
            "type": "color",
            "id": "bgr_color",
            "label": "Background color",
            "default": "#EEEEEE"
          },
          {
            "type": "color",
            "id": "text_color",
            "label": "Text color",
            "default": "#000000"
          },
          {
            "type": "header",
            "content": "Button"
          },
          {
            "type": "text",
            "id": "button_label",
            "label": "Button label",
            "default": "Shop now"
          },
          {
            "type": "url",
            "id": "promo_link",
            "label": "Button link"
          },
          {
            "type": "select",
            "id": "button_style",
            "label": "Button style",
            "options": [
              {
                "value": "button--primary",
                "label": "Primary"
              },
              {
                "value": "button--secondary",
                "label": "Secondary"
              },
              {
                "value": "button--link-style",
                "label": "Link style"
              }
            ],
            "default": "button--primary"
          }
        ]
      },
      {
        "type": "empty_column",
        "name": "Empty column"
      }
    ],
    "presets": [
      {
        "name": "Mega menu",
        "category": "Mega menu",
        "blocks": [
          {
            "type": "menu"
          },
          {
            "type": "featured_promo"
          },
          {
            "type": "mixed"
          },
          {
            "type": "empty_column"
          },
          {
            "type": "image"
          },
          {
            "type": "featured_product"
          }
        ]
      }
    ],
    "enabled_on": {
      "groups": [
        "header"
      ]
    }
  }
{% endschema %}
