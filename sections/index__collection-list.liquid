{% comment %}
** Collection list **
{% endcomment %}

{% liquid
  assign blocks = section.blocks
  assign use_mobile_slider = false

  if section.settings.mobile_layout == 'slider' and blocks.size > 1
    assign use_mobile_slider = true
  endif

  # Is the color set to transparent?
  assign overlay_alpha = section.settings.overlay_background | color_extract: 'alpha'
  assign overlay_text_alpha = section.settings.overlay_text | color_extract: 'alpha'

  # Opacity level
  if overlay_alpha != 0
    assign overlay_background_alpha = section.settings.overlay_background_opacity | divided_by: 100.00
  endif

  if section.settings.align_height
    assign collection_height = section.settings.collection_height
  endif
%}

{%- capture section_css -%}
  {% if section.settings.align_height != blank %}
    .placeholder-svg {
      height: {{ collection_height }}px;
      max-height: {{ collection_height }}px;
    }
  {% endif %}

  .collection-thumbnail-overlay {
    background-color: {%- if overlay_alpha != 0 -%}{{ section.settings.overlay_background | color_modify: 'alpha', overlay_background_alpha }}{% endif %};
  }

  .collection-info__caption span {
    color: {%- if overlay_text_alpha != 0 -%}{{ section.settings.overlay_text }}{% endif %}
  }
{%- endcapture -%}

{% comment %} Section specific CSS {% endcomment %}
{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;

    {% if section.settings.width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section_css,
    id: section.id,
  %}

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ section.settings.width }}
    {% if section.settings.show_gutter == false %}
      has-no-side-gutter
      has-background
    {% else %}
      has-gutter-enabled
    {% endif %}
    {% if blocks.size > section.settings.collections_per_row %}
      has-multirow-blocks
    {% endif %}
  "
  {% if section.settings.animation != "none" %}
    data-scroll-class="{{ section.settings.animation }}"
  {% endif %}
>
  {% if use_mobile_slider and section.settings.show_arrows %}
    <div class="container">
      <div class="one-whole column">
        <div class="collection-list__nav-wrapper">
          {%
            render 'icon',
            name: 'left-arrow',
            icon_class: 'collection-list__nav collection-list__nav--prev',
          %}
          {%
            render 'icon',
            name: 'right-arrow',
            icon_class: 'collection-list__nav collection-list__nav--next',
          %}
        </div>
      </div>
    </div>
  {% endif %}

  <div
    class="
      collection-list__wrapper
      {% if use_mobile_slider %}
        collection-list__wrapper--page-dots-{{ section.settings.show_navigation_dots }}
      {% endif %}
      container
    "
    data-collection-list-wrapper
  >
    {% if blocks.size > 0 %}
      {% for block in blocks %}
        {% liquid
          assign collection = collections[block.settings.feature_collection]
          assign image = block.settings.image
          assign title = block.settings.title
          assign button_label = block.settings.button_label

          if image != blank
            assign collection_image = image
            assign collection_image_alt = image.alt | escape
          elsif collection.image != blank
            assign collection_image = collection.image
            assign collection_image_alt = collection.image.alt | escape
          else
            assign collection_image = collection.products.first.featured_image
            assign collection_image_alt = collection.products.first.featured_image.alt | escape
          endif
        %}

        <div
          class="
            collection-list__thumbnail
            column
            {% render 'column-width', value: section.settings.collections_per_row %}
            thumbnail
            {% if section.settings.align_height %}
              list-collection--align-height
            {% endif %}
            {% if section.settings.show_gutter == true %}
              has-gutter
              has-gutter--mobile
            {% endif %}
            medium-down--one-whole
          "
        >
          <div class="product-wrap enable-zoom-{{ section.settings.enable_zoom }}">
            <div class="collection-thumbnail-overlay"></div>

            {% if collection_image != blank %}
              <div class="thumbnail image__container has-image-crop">
                {%
                  render 'image-element',
                  image: collection_image.src,
                  alt: collection_image_alt,
                  image_crop: true,
                  max_height: collection_height,
                %}
              </div>
            {% else %}
              {% capture num %}{% cycle "1", "2", "3", "4", "5", "6" %}{% endcapture %}
              {{ 'collection-' | append: num | placeholder_svg_tag: 'placeholder-svg' }}
            {% endif %}

            <a class="collection-info__caption" href="{{ collection.url }}">
              {% if section.settings.collection_title_below_image == blank %}
                <div class="collection-info__caption-wrapper">
                  <span class="title">
                    {% if title != blank %}
                      {{- title -}}
                    {% elsif collection != blank %}
                      {{- collection.title -}}
                    {% else %}
                      {{- "homepage.onboarding.collection_title" | t -}}
                    {% endif %}
                  </span>

                  {%
                    render 'button',
                    label: button_label,
                    href: collection.url,
                    style: section.settings.button_style,
                  %}
                </div>
              {% endif %}
            </a>
          </div>

          {% if section.settings.collection_title_below_image != blank %}
            <a
              class="
                collection-list__caption-wrapper
                collection-list__caption-wrapper--below-image
              "
              href="{{ collection.url }}"
            >
              <span class="title">
                {% if title != blank %}
                  {{- title -}}
                {% elsif collection != blank %}
                  {{- collection.title -}}
                {% else %}
                  {{- "homepage.onboarding.collection_title" | t -}}
                {% endif %}
              </span>

              {%
                render 'button',
                label: button_label,
                href: collection.url,
                style: section.settings.button_style,
              %}
            </a>
          {% endif %}
        </div>
      {% endfor %}
    {% endif %}
  </div>
</section>

<script
  type="application/json"
  data-section-id="{{ section.id }}"
  data-section-data
>
  {
    "use_mobile_slider": {{ use_mobile_slider | json }},
    "show_navigation_dots": {{ section.settings.show_navigation_dots | json }}
  }
</script>
<script data-theme-editor-load-script src="{{ 'z__jsCollectionList.js' | asset_url }}"></script>

{% schema %}
  {
    "name": "Collection list",
    "class": "collection-list jsCollectionList",
    "max_blocks": 8,
    "settings": [
      {
        "type": "range",
        "id": "collections_per_row",
        "label": "Collections per row",
        "min": 2,
        "max": 5,
        "step": 1,
        "default": 3
      },
      {
        "type": "checkbox",
        "id": "align_height",
        "label": "Align to height",
        "default": false
      },
      {
        "type": "range",
        "id": "collection_height",
        "label": "Collection image height",
        "min": 200,
        "max": 400,
        "step": 10,
        "default": 400,
        "unit": "px",
        "info": "Applied when 'Align to height' is also enabled. [Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360024344614)"
      },
      {
        "type": "checkbox",
        "id": "enable_zoom",
        "label": "Magnify image on hover",
        "default": true
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "default": "button--secondary",
        "options": [
          {
            "value": "button--primary",
            "label": "Primary"
          },
          {
            "value": "button--secondary",
            "label": "Secondary"
          },
          {
            "value": "button--link-style",
            "label": "Link"
          }
        ]
      },
      {
        "type": "header",
        "content": "Overlay"
      },
      {
        "type": "color",
        "id": "overlay_text",
        "label": "Text color",
        "default": "#FFFFFF"
      },
      {
        "type": "color",
        "id": "overlay_background",
        "label": "Overlay color",
        "default": "#939393"
      },
      {
        "type": "range",
        "id": "overlay_background_opacity",
        "label": "Overlay opacity",
        "min": 0,
        "max": 100,
        "step": 10,
        "default": 70,
        "unit": "%"
      },
      {
        "type": "checkbox",
        "id": "collection_title_below_image",
        "label": "Show collection title below image",
        "default": false
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "show_gutter",
        "label": "Show gutter",
        "default": true
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          },
          {
            "value": "zoomIn",
            "label": "Zoom in"
          }
        ]
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "radio",
        "id": "mobile_layout",
        "label": "Mobile layout",
        "options": [
          {
            "value": "slider",
            "label": "Slider"
          },
          {
            "value": "stacked",
            "label": "Stacked"
          }
        ],
        "default": "stacked"
      },
      {
        "type": "checkbox",
        "id": "show_arrows",
        "label": "Show arrows on mobile",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_navigation_dots",
        "label": "Show navigation dots on mobile",
        "default": true
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "collection",
        "name": "Collection",
        "settings": [
          {
            "type": "collection",
            "id": "feature_collection",
            "label": "Collection"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image",
            "info": "The collection's featured image will be used as a fallback."
          },
          {
            "type": "text",
            "id": "title",
            "label": "Custom heading"
          },
          {
            "type": "text",
            "id": "button_label",
            "label": "Button label",
            "default": "View products"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Collection list",
        "category": "Collection",
        "settings": {},
        "blocks": [
          {
            "type": "collection"
          },
          {
            "type": "collection"
          },
          {
            "type": "collection"
          }
        ]
      }
    ],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
