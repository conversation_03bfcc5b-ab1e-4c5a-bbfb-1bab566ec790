{% comment %}
** Product sidebar **
- Complimentary section to be used with product templates

{% endcomment %}

{% if section.blocks.size > 0 %}

{% assign id = section.id %}
{% comment %} Layout {% endcomment %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% comment %} Advanced {% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% comment %} Section specific CSS {% endcomment %}
{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    flex: 1 0 25%;
    max-width: 300px;
    padding-right: 40px;
  }

  .product-main.has-sidebar-option {
    flex: 1 0 75%;
  }

  .product-main.has-sidebar-option + #shopify-section-{{ id }} {
    padding-left: 40px;
  }

  .product-main.has-sidebar-option .section {
    width: 100%;
  }

  @media only screen and (max-width: 1200px){
    #shopify-section-{{ id }} {
      padding-left: 2%;
      padding-right: 2%;
    }

    .product-main.has-sidebar-option {
      padding-left: 2%;
      padding-right: 2%;
    }
  }

  @media only screen and (max-width: 798px){
    #shopify-section-{{ id }} {
      flex: 1 0 100%;
      max-width: 100%;
      padding-left: 0;
      padding-right: 0;
    }
  }

  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

{% comment %} HTML markup {% endcomment %}
<div class="section is-width-wide product-sidebar--mobile-{{ settings.mobile_sidebar_position }} container {{ css_class }}" data-product-sidebar>
  <aside class="one-whole
                column">
    {% for block in section.blocks %}
      <div id="shopify-section-{{ block.id }}"
            class="sidebar__block block__{{ block.type | downcase | replace: '_', '-' }}
                  has-padding-top has-padding-bottom
                  {% if settings.toggle_sidebar %}sidebar-toggle-active{% endif %}"
            {{ block.shopify_attributes }}>

        {% if block.type == 'collection_list' %}
          {% comment %} Tag list {% endcomment %}
          {% render 'sidebar__collection-list', block: block %}

        {% elsif block.type == 'featured_promo' %}
          {% comment %} Featured promo {% endcomment %}
          {% render 'sidebar__featured-promo',
                  block: block,
                  id: block.id
          %}

        {% elsif block.type == 'menu' %}
          {% comment %} Menu {% endcomment %}
          {% render 'sidebar__menu', menu: block.settings.menu %}

        {% elsif block.type == 'newsletter' %}
          {% comment %} Newsletter {% endcomment %}
          {% render 'sidebar__newsletter', block: block %}

        {% elsif block.type == 'page' %}
          {% comment %} Page {% endcomment %}
          {% render 'sidebar__page', page: block.settings.page %}

        {% elsif block.type == 'search' %}
          {% comment %} Search {% endcomment %}
          {%
            render 'sidebar__search',
            block: block,
          %}

        {% elsif block.type == 'tag_list' %}
          {% comment %} Tag list {% endcomment %}
          {% render 'sidebar__tag-list', block: block %}

        {% elsif block.type == 'text' %}
          {% comment %} Text {% endcomment %}
          {% render 'sidebar__text', block: block %}

        {% elsif block.type == 'type_list' %}
          {% comment %} Type list {% endcomment %}
          {% render 'sidebar__type-list', block: block %}

        {% elsif block.type == 'vendor_list' %}
          {% comment %} Vendor list {% endcomment %}
          {% render 'sidebar__vendor-list', block: block %}

        {% endif %}
      </div>
    {% endfor %}
  </aside>
</div>

{% endif %}

<script data-theme-editor-load-script src="{{ 'z__jsSidebar.js' | asset_url }}"></script>

{% schema %}

{
  "name": "Product sidebar",
  "class": "product-sidebar sidebar-section jsSidebar",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ],
  "blocks": [
    {
      "type": "collection_list",
      "name": "Collection list",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Collections"
        }
      ]
    },
    {
      "type": "featured_promo",
      "name": "Featured promotion",
      "settings": [
        {
          "type": "color",
          "id": "promo_background",
          "label": "Background",
          "default": "#EEEEEE"
        },
        {
          "type": "color",
          "id": "promo_text",
          "label": "Text",
          "default": "#000000"
        },
        {
          "type": "image_picker",
          "id": "promo_image",
          "label": "Image"
        },
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Text",
          "default": "<p>Use this area for promotional information.</p>"
        },
        {
          "type": "url",
          "id": "promo_link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Shop now"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button Style",
          "options": [
            {
              "value": "button--primary",
              "label": "Primary"
            },
            {
              "value": "button--secondary",
              "label": "Secondary"
            },
            {
              "value": "button--link-style",
              "label": "Link style"
            }
          ],
          "default": "button--primary"
        }
      ]
    },
    {
      "type": "menu",
      "name": "Menu",
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "info": "This menu won't show drop-down items."
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "newsletter_title",
          "label": "Heading",
          "default": "Subscribe"
        },
        {
          "type": "richtext",
          "id": "newsletter_richtext",
          "label": "Text",
          "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
        },
        {
          "type": "checkbox",
          "id": "display_first_name",
          "label": "Show first name"
        },
        {
          "type": "checkbox",
          "id": "display_last_name",
          "label": "Show last name"
        }
      ]
    },
    {
      "type": "page",
      "name": "Page",
      "settings": [
        {
          "type": "page",
          "id": "page",
          "label": "Page"
        }
      ]
    },
    {
      "type": "search",
      "name": "Search form",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Search"
        }
      ]
    },
    {
      "type": "tag_list",
      "name": "Tag list",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Category"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Text area can be used for details about blog authors or general information.</p>"
        }
      ]
    },
    {
      "type": "type_list",
      "name": "Type list",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Types"
        }
      ]
    },
    {
      "type": "vendor_list",
      "name": "Vendor list",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Vendors"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "featured_promo"
      },
      {
        "type": "menu"
      },
      {
        "type": "text"
      }
    ]
  }
}

{% endschema %}
