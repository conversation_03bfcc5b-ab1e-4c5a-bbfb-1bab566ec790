{% comment %}
** Blog posts **
{% endcomment %}

{% liquid
  assign title = section.settings.title
  assign use_mobile_slider = false
  assign blog = blogs[section.settings.blog_widget_select]

  if section.settings.mobile_layout == 'slider' and blog.articles.size > 1 or section.settings.mobile_layout == 'slider' and blog.empty?
    assign use_mobile_slider = true
  endif
%}

{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;

    {% if section.settings.width == 'wide' -%}
      width: 100%;
    {%- endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ section.settings.width }}
  "
  {% if section.settings.animation != "none" %}
    data-scroll-class="{{ section.settings.animation }}"
  {% endif %}
>
  {% if title != blank %}
    <div class="container">
      {%
        render 'heading',
        title: title,
        heading_tag: 'h2',
        context: 'featured-blog',
        text_alignment: 'center',
        url: blog.url,
      %}
    </div>
  {% endif %}

  {% if use_mobile_slider and section.settings.show_arrows %}
    <div class="container">
      <div class="one-whole column">
        <div class="blog-posts__nav-wrapper">
          {%
            render 'icon',
            name: 'left-arrow',
            icon_class: 'blog-posts__nav blog-posts__nav--prev',
          %}
          {%
            render 'icon',
            name: 'right-arrow',
            icon_class: 'blog-posts__nav blog-posts__nav--next',
          %}
        </div>
      </div>
    </div>
  {% endif %}

  <div
    class="
      blog-posts__wrapper
      blog-posts__wrapper--page-dots-{{ section.settings.show_navigation_dots }}
      container
    "
    data-blog-posts-wrapper
  >
    {% if blog != blank %}
      {% for article in blog.articles limit: section.settings.home_page_articles %}
        <div
          class="
            featured-article
            blog-card
            {% render 'column-width', value: section.settings.home_page_articles %}
            columns
            article
            card
            show-border-{{ section.settings.show-border }}
            medium-down--one-whole
            has-margin-bottom
          "
        >
          {% if article.image != blank %}
            <div class="card-image blog-card__image">
              <figure class="image">
                <a
                  class="blog-card__link"
                  href="{{ article.url }}"
                  title="{{ article.title | escape }}"
                >
                  {%
                    render 'image-element',
                    image: article.image,
                    alt: article.image.alt,
                    additional_classes: 'blog-card__image',
                  %}
                </a>
              </figure>
            </div>
          {% endif %}

          <div class="card-content blog-card__content">
            <div class="media">
              <div class="media-content">
                <h3 class="title">
                  <a
                    class="featured-article--link"
                    href="{{ article.url }}"
                    title="{{ article.title | escape }}"
                  >
                    {{- article.title -}}
                  </a>
                </h3>
              </div>
            </div>

            {% if article.excerpt != blank and section.settings.blog_show_excerpt %}
              {% assign postexcerpt = article.excerpt | size %}

              {% if postexcerpt > 150 %}
                {% assign excerptlength = 'lg' %}
              {% elsif postexcerpt <= 150 %}
                {% assign excerptlength = 'sm' %}
              {% endif %}

              <div
                class="
                  excerpt
                  excerpt-length-{{ excerptlength }}
                  has-margin-bottom
                "
              >
                {{ article.excerpt }}
                <span class="truncation-fade"></span>
              </div>
            {% endif %}

            <div class="meta-info is-small">
              {% if section.settings.blog_show_tags %}
                {% if article.tags.size > 0 %}
                  <ul class="meta-tag-list tags">
                  {% for tag in article.tags %}
                    <li class="tag tag--{{ settings.tag_style}}">
                      <a
                        href="{{ shop.url}}/blogs/{{ blog.handle }}/tagged/{{ tag | handleize }}"
                        title="{{ blog.title | escape }} {{ 'blogs.general.tagged' | t }} {{ tag | escape }}"
                      >
                        {{- tag -}}
                      </a>
                    </li>
                  {% endfor %}
                  </ul>
                {% endif %}
              {% endif %}

              {%
                render 'meta-info-list',
                article: article,
                blog_author: section.settings.blog_author,
                blog_date: section.settings.blog_date,
                blog_read_time: section.settings.read_time,
                blog_comment_count: section.settings.blog_comment_count,
              %}
            </div>
          </div>

          {% if section.settings.button_type != 'none' %}
            <div class="blog-card__read-more">
              {%
                render 'button',
                label: section.settings.button_label,
                href: article.url,
                type: "link",
                style: section.settings.button_type,
              %}
            </div>
          {% endif %}
        </div>
      {% endfor %}
    {% else %}
      {% for i in (1..section.settings.home_page_articles) %}
        <div
          class="
            featured-article
            blog-card
            {% render 'column-width', value: section.settings.home_page_articles %}
            columns
            article
            card
            show-border-{{ section.settings.show-border }}
            medium-down--one-whole
            has-margin-bottom
          "
        >
          <div class="card-image blog-card__image">
            <figure class="image">
              <a href="{{ article.url }}">
                {{- 'image' | placeholder_svg_tag: 'placeholder-svg' -}}
              </a>
            </figure>
          </div>

          <div class="card-content blog-card__content">
            <div class="media">
              <div class="media-content">
                <h4 class="title">
                  <a class="featured-article--link" href="#">
                    {{- 'homepage.onboarding.blogpost_title' | t -}}
                  </a>
                </h4>
              </div>
            </div>

            {% if section.settings.blog_show_excerpt %}
              <div class="excerpt has-margin-bottom">
                {{ 'homepage.onboarding.blog_excerpt' | t }}
                <span class="truncation-fade"></span>
              </div>
            {% endif %}

            <div class="meta-info is-small">
              {% if section.settings.blog_show_tags %}
                <ul class="meta-tag-list tags">
                  {% for i in (1..3) %}
                    <li class="tag tag--{{ settings.tag_style }}">
                      <a href="#">tag{{i}}</a>
                    </li>
                  {% endfor %}
                </ul>
              {% endif %}

              {%
                render 'meta-info-list',
                article: article,
                blog_author: section.settings.blog_author,
                blog_date: section.settings.blog_date,
                blog_read_time: section.settings.read_time,
                blog_comment_count: section.settings.blog_comment_count,
              %}
            </div>
          </div>

          {% if section.settings.button_type != 'none' %}
            <div class="blog-card__read-more">
              {%
                render 'button',
                label: section.settings.button_label,
                href: a.url,
                type: "link",
                style: section.settings.button_type,
              %}
            </div>
          {% endif %}
        </div>
      {% endfor %}
    {% endif %}
  </div>
</section>

<script
  type="application/json"
  data-section-id="{{ section.id }}"
  data-section-data
>
  {
    "use_mobile_slider": {{ use_mobile_slider | json }},
    "show_navigation_dots": {{ section.settings.show_navigation_dots | json }}
  }
</script>
<script data-theme-editor-load-script src="{{ 'z__jsFeaturedBlogPosts.js' | asset_url }}"></script>

{% schema %}
  {
    "name": "Blog posts",
    "class": "blog-posts blog-posts--1 jsFeaturedBlogPosts",
    "settings": [
      {
        "type": "text",
        "id": "title",
        "label": "Heading",
        "default": "Recent blog posts"
      },
      {
        "type": "blog",
        "id": "blog_widget_select",
        "label": "Blog"
      },
      {
        "type": "range",
        "id": "home_page_articles",
        "label": "Posts to show",
        "min": 2,
        "max": 4,
        "default": 3
      },
      {
        "type": "checkbox",
        "id": "blog_show_excerpt",
        "label": "Show excerpts",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_show_tags",
        "label": "Show tags",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_author",
        "label": "Show author",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_date",
        "label": "Show date",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "read_time",
        "label": "Show estimated read time",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_comment_count",
        "label": "Show comment count",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "show-border",
        "label": "Show border",
        "default": true
      },
      {
        "type": "text",
        "id": "button_label",
        "label": "Button label",
        "default": "Read more"
      },
      {
        "type": "select",
        "id": "button_type",
        "label": "Button style",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "button--primary",
            "label": "Primary"
          },
          {
            "value": "button--secondary",
            "label": "Secondary"
          },
          {
            "value": "button--link-style",
            "label": "Link style"
          }
        ],
        "default": "button--primary"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "wide",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "default": 0,
        "min": 0,
        "max": 80,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          }
        ]
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "radio",
        "id": "mobile_layout",
        "label": "Mobile layout",
        "options": [
          {
            "value": "slider",
            "label": "Slider"
          },
          {
            "value": "stacked",
            "label": "Stacked"
          }
        ],
        "default": "stacked"
      },
      {
        "type": "checkbox",
        "id": "show_arrows",
        "label": "Show arrows on mobile",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_navigation_dots",
        "label": "Show navigation dots on mobile",
        "default": true
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "presets": [{
      "name": "Blog posts",
      "category": "Blog",
      "settings": {
        "blog_widget_select": ""
      }
    }],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
