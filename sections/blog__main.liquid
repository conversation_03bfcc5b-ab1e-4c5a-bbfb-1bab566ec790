{% liquid
  assign id = section.id
  assign width = section.settings.width
  assign animation = section.settings.animation | default: 'none'
%}

{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  .sidebar-section {
    {% if section.settings.sidebar_position == 'right' %}
      order: 1;
    {% endif %}
  }

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ width }}
  "
  {% if animation != "none" %}
    data-scroll-class="{{ animation }}"
  {% endif %}
>
  <div class="container">
    {% if section.blocks.size > 0 and section.settings.show_sidebar %}
      <aside
        class="
          sidebar-section
          one-fourth
          medium-down--one-whole
          column
        "
      >
        {% for block in section.blocks %}
          <div
            class="
              sidebar__block
              block__{{ block.type | downcase | replace: '_', '-' }}      block__{{ block.id }}
              has-padding-top has-padding-bottom
              {% if settings.toggle_sidebar %}sidebar-toggle-active{% endif %}
            "
            id="shopify-section-{{ block.id }}"
            {{ block.shopify_attributes }}
          >
            {% case block.type %}
              {% when 'post_tags' %}
                {%
                  render 'sidebar__blog-post-tags',
                  block: block,
                %}

              {% when 'html' %}
                {%
                  render 'sidebar__html',
                  block: block,
                %}

              {% when 'featured_promo' %}
                {%
                  render 'sidebar__featured-promo',
                  block: block,
                  id: block.id,
                %}

              {% when 'menu' %}
                {%
                  render 'sidebar__menu',
                  menu: block.settings.menu,
                %}

              {% when 'newsletter' %}
                {%
                  render 'sidebar__newsletter',
                  block: block,
                %}

              {% when 'page' %}
                {%
                  render 'sidebar__page',
                  page: block.settings.page,
                %}

              {% when 'recent_posts' %}
                {%
                  render 'sidebar__recent-posts',
                  block: block,
                  blog_handle: block.settings.blog,
                %}

              {% when 'search' %}
                {%
                  render 'sidebar__search',
                  block: block,
                  resource_types: 'article',
                %}

              {% when 'text' %}
                {%
                  render 'sidebar__text',
                  block: block,
                %}
            {% endcase %}
          </div>
        {% endfor %}
      </aside>
    {% endif %}

    {% paginate blog.articles by section.settings.blog_posts_per_page %}
      <section
        class="
          {% if section.blocks.size > 0 and section.settings.show_sidebar %}
            three-fourths
            medium-down--one-whole
            column
            equal-columns--outside-trim
          {% endif %}
        "
      >
        <div
          class="
            container
            {% if settings.heading_divider_style == 'long' %}
              has-heading-divider-below
            {% endif %}
          "
        >
          <div
            class="
              one-half
              medium-down--one-whole
              column
              has-no-side-gutter
            "
          >
            <div class="hide-when-banner-enabled">
              {%
                render 'heading',
                title: blog.title,
                heading_tag: 'h1',
                context: 'blog',
                text_alignment: 'left',
              %}
            </div>
          </div>

          {% if section.settings.blog_tags %}
            <div
              class="
                blog__filter
                one-half
                medium-down--one-whole
                column
              "
            >
              <div
                class="
                  field
                  is-grouped
                  is-flex-wrap
                "
              >
                <div class="select">
                  <select
                    class="select"
                    id="blog_filter"
                    name="tag_filter"
                  >
                    <option {% unless current_tags %}selected="selected"{% endunless %} value="{{ blog.url }}">
                      {{- 'blogs.general.view_all' | t -}}
                    </option>

                    {% for tag in blog.all_tags %}
                      <option
                        {% if current_tags contains tag %}
                          selected="selected"
                        {% endif %}
                        value="/blogs/{{ blog.handle }}/tagged/{{ tag | handleize }}"
                      >
                        {{- tag -}}
                      </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
          {% endif %}

          {% if settings.heading_divider_width != 0 and settings.heading_divider_style == 'long' %}
            <div
              class="
                heading-divider-below
                heading-wrapper
                one-whole
                column
                hide-when-banner-enabled
              "
            >
              <div
                class="
                  heading-divider
                  heading-divider--{{ settings.heading_divider_style }}
                "
                {% if settings.heading_divider_animation != "none" %}
                  data-scroll-class="{{ settings.heading_divider_animation }}"
                {% endif %}
              >
              </div>
            </div>
          {% endif %}
        </div>

        <div
          class="container"
          {% if section.settings.pagination_type != 'basic_pagination' %}
            data-load-more--grid
          {% endif %}
        >
          {% if blog.articles.size == 0 %}
            <div
              class="
                blog-page__no-content
                one-whole
                column
              "
            >
              <div class="excerpt">
                {{- 'homepage.onboarding.blog_excerpt' | t -}}
              </div>
            </div>
          {% endif %}

          {% for article in blog.articles %}
            <div
              class="
                featured-article
                blog-card
                {% render 'column-width', value: section.settings.blog_posts_per_row %}
                columns
                article
                card
                show-border-{{ section.settings.show_border }}
                medium-down--one-half
                small-down--one-whole
                has-margin-bottom
                {% if section.settings.blog_posts_per_row == 1 %}
                  is-horizontal
                {% endif %}
              "
              {% if section.settings.pagination_type != 'basic_pagination' %}
                data-load-more--grid-item
              {% endif %}
            >
              {% if article.image %}
                <div class="card-image blog-card__image">
                  <figure class="image">
                    <a href="{{ article.url }}" title="{{ article.title | escape }}">
                      {%
                        render 'image-element',
                        image: article.image,
                        alt: article.image.alt,
                        stretch_width: true,
                      %}
                    </a>
                  </figure>
                </div>
              {% endif %}

              <div class="card-content blog-card__content">
                <div class="media">
                  <div class="media-content">
                    <h4 class="title">
                      <a class="featured-article--link" href="{{ article.url }}">
                        {{- article.title -}}
                      </a>
                    </h4>
                  </div>
                </div>

                {% if article.excerpt != blank and section.settings.blog_show_excerpt %}
                  {% liquid
                    assign postexcerpt = article.excerpt | size
                    if postexcerpt > 150
                      assign excerptlength = 'lg'
                    elsif postexcerpt <= 150
                      assign excerptlength = 'sm'
                    endif
                  %}

                  <div
                    class="
                      excerpt
                      excerpt-length-{{ excerptlength }}
                      has-margin-bottom
                    "
                  >
                    {{ article.excerpt }}
                    <span class="truncation-fade"></span>
                  </div>
                {% endif %}

                <div class="meta-info is-small">
                  {% if section.settings.blog_show_tags %}
                    {% if article.tags.size > 0 %}
                      <ul class="meta-tag-list tags">
                        {% for tag in article.tags %}
                          <li class="tag tag--{{ settings.tag_style }}">
                            <a
                              href="{{ shop.url}}/blogs/{{ blog.handle }}/tagged/{{ tag | handleize }}"
                              title="{{ blog.title | escape }} {{ 'blogs.general.tagged' | t }} {{ tag | escape }}"
                            >
                              {{- tag -}}
                            </a>
                          </li>
                        {% endfor %}
                      </ul>
                    {% endif %}
                  {% endif %}

                  {%
                    render 'meta-info-list',
                    article: article,
                    blog_author: section.settings.blog_author,
                    blog_date: section.settings.blog_date,
                    blog_read_time: section.settings.read_time,
                    blog_comment_count: section.settings.blog_comment_count,
                  %}
                </div>

                {% if section.settings.button_type != 'none' and section.settings.blog_posts_per_row == 1 %}
                  <div class="blog-card__read-more buttons">
                    {%
                      render 'button',
                      label: section.settings.button_label,
                      href: article.url,
                      type: "link",
                      style: section.settings.button_type,
                    %}
                  </div>
                {% endif %}
              </div>

              {% if section.settings.button_type != 'none' and section.settings.blog_posts_per_row != 1 %}
                <div class="blog-card__read-more buttons">
                  {%
                    render 'button',
                    label: section.settings.button_label,
                    href: article.url,
                    type: "link",
                    style: section.settings.button_type,
                  %}
                </div>
              {% endif %}
            </div>
          {% endfor %}
        </div>

        <div class="container">
          <div
            class="
              one-whole
              column
              text-align-center
            "
          >
            {% assign load_more_text = 'blogs.general.pagination_button' | t %}
            {%
              render 'pagination',
              paginate: paginate,
              pagination_type: section.settings.pagination_type,
              load_more_text: load_more_text,
            %}
          </div>
        </div>
      </section>
    {% endpaginate %}
  </div>
</section>

<script
  type="application/json"
  data-section-id="{{ section.id }}"
  data-section-data
>
  {
    "enable_filter": {{ section.settings.blog_tags | json }}
  }
</script>

<script data-theme-editor-load-script src="{{ 'z__jsSidebar.js' | asset_url }}"></script>
<script src="{{ 'z__jsBlog.js' | asset_url }}"></script>

{% schema %}
  {
    "name": "Blog",
    "class": "blog-main has-sidebar-option jsBlog jsSidebar",
    "settings": [
      {
        "type": "checkbox",
        "id": "blog_tags",
        "label": "Show tag filter"
      },
      {
        "type": "range",
        "id": "blog_posts_per_row",
        "label": "Posts per row",
        "min": 1,
        "max": 4,
        "step": 1,
        "default": 3
      },
      {
        "type": "range",
        "id": "blog_posts_per_page",
        "label": "Posts per page",
        "min": 1,
        "max": 50,
        "step": 1,
        "default": 12
      },
      {
        "type": "select",
        "id": "pagination_type",
        "label": "Pagination type",
        "options": [
          {
            "value": "infinite_scroll",
            "label": "Infinite scroll"
          },
          {
            "value": "infinite_load_more",
            "label": "Infinite load more button"
          },
          {
            "value": "load_more",
            "label": "Load more button"
          },
          {
            "value": "basic_pagination",
            "label": "Page links"
          }
        ],
        "default": "basic_pagination"
      },
      {
        "type": "header",
        "content": "Sidebar"
      },
      {
        "type": "checkbox",
        "id": "show_sidebar",
        "label": "Show sidebar",
        "info": "Add blocks for sidebar content.",
        "default": false
      },
      {
        "type": "radio",
        "id": "sidebar_position",
        "label": "Sidebar position",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "left"
      },
      {
        "type": "header",
        "content": "Blog posts"
      },
      {
        "type": "checkbox",
        "id": "blog_author",
        "label": "Show author"
      },
      {
        "type": "checkbox",
        "id": "blog_date",
        "label": "Show date",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "read_time",
        "label": "Show estimated read time",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_comment_count",
        "label": "Show comment count",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_show_tags",
        "label": "Show tags",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "blog_show_excerpt",
        "label": "Show excerpts",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_border",
        "label": "Show border",
        "default": true
      },
      {
        "type": "text",
        "id": "button_label",
        "label": "Button label",
        "default": "Read more"
      },
      {
        "type": "select",
        "id": "button_type",
        "label": "Button style",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "button--primary",
            "label": "Primary"
          },
          {
            "value": "button--secondary",
            "label": "Secondary"
          },
          {
            "value": "button--link-style",
            "label": "Link style"
          }
        ],
        "default": "button--primary"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          }
        ]
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "post_tags",
        "name": "Blog post tags",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Blog post tags"
          },
          {
            "type": "blog",
            "id": "blog",
            "label": "Blog"
          }
        ]
      },
      {
        "type": "html",
        "name": "Custom HTML",
        "settings": [
          {
            "type": "textarea",
            "id": "html_content",
            "label": "HTML",
            "default": "<div class='container is-flex is-justify-center'><h2 class='title'>Your own custom HTML</h2></div>"
          }
        ]
      },
      {
        "type": "featured_promo",
        "name": "Featured promotion",
        "settings": [
          {
            "type": "color",
            "id": "promo_background",
            "label": "Background",
            "default": "#EEEEEE"
          },
          {
            "type": "color",
            "id": "promo_text",
            "label": "Text",
            "default": "#000000"
          },
          {
            "type": "image_picker",
            "id": "promo_image",
            "label": "Image"
          },
          {
            "type": "richtext",
            "id": "richtext",
            "label": "Text",
            "default": "<p>Use this area for promotional information.</p>"
          },
          {
            "type": "url",
            "id": "promo_link",
            "label": "Link"
          },
          {
            "type": "text",
            "id": "button_label",
            "label": "Button label",
            "default": "Shop now"
          },
          {
            "type": "select",
            "id": "button_style",
            "label": "Button style",
            "options": [
              {
                "value": "button--primary",
                "label": "Primary"
              },
              {
                "value": "button--secondary",
                "label": "Secondary"
              },
              {
                "value": "button--link-style",
                "label": "Link style"
              }
            ],
            "default": "button--primary"
          }
        ]
      },
      {
        "type": "menu",
        "name": "Menu",
        "settings": [
          {
            "type": "link_list",
            "id": "menu",
            "label": "Menu",
            "info": "This menu won't show drop-down items."
          }
        ]
      },
      {
        "type": "newsletter",
        "name": "Newsletter",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "newsletter_title",
            "label": "Heading",
            "default": "Subscribe"
          },
          {
            "type": "richtext",
            "id": "newsletter_richtext",
            "label": "Text",
            "default": "<p>Sign up to get the latest on sales, new releases and more …</p>"
          },
          {
            "type": "checkbox",
            "id": "display_first_name",
            "label": "Show first name"
          },
          {
            "type": "checkbox",
            "id": "display_last_name",
            "label": "Show last name"
          }
        ]
      },
      {
        "type": "page",
        "name": "Page",
        "settings": [
          {
            "type": "page",
            "id": "page",
            "label": "Page"
          }
        ]
      },
      {
        "type": "recent_posts",
        "name": "Recent blog posts",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Recent posts"
          },
          {
            "type": "blog",
            "id": "blog",
            "label": "Blog"
          },
          {
            "type": "range",
            "id": "blog_post_count",
            "label": "Blog posts",
            "min": 2,
            "max": 10,
            "step": 1,
            "default": 10
          }
        ]
      },
      {
        "type": "search",
        "name": "Search form",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Search"
          }
        ]
      },
      {
        "type": "text",
        "name": "Text",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Heading"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Text area can be used for details about blog authors or general information.</p>"
          }
        ]
      }
    ],
    "default": {
      "blocks": [
        {
          "type": "featured_promo"
        },
        {
          "type": "menu"
        },
        {
          "type": "text"
        }
      ]
    }
  }
{% endschema %}
