{% comment %}
** Heading **
{% endcomment %}

{% liquid
  assign preheading = section.settings.preheading
  assign title = section.settings.title
  assign subheading = section.settings.subheading

  # Is the color set to transparent?
  assign heading_alpha = section.settings.heading_color | color_extract: 'alpha'
  assign preheading_alpha = section.settings.preheading_color | color_extract: 'alpha'
  assign subheading_alpha = section.settings.subheading_color | color_extract: 'alpha'
  assign background_alpha = section.settings.background | color_extract: 'alpha'
  assign gradient_alpha = section.settings.gradient | color_extract: 'alpha'
%}

{% comment %} Section specific CSS {% endcomment %}
{% capture section_css -%}
  .section {
    background-image: linear-gradient({{ section.settings.gradient_rotation }}deg, rgba(255,255,255,0), {{ section.settings.gradient }});
    background-color: {%- if background_alpha != 0 -%}{{ section.settings.background }}{%- endif -%};
  }

  .heading-section__heading {
    color: {%- if heading_alpha != 0 -%}{{ section.settings.heading_color }}{%- else -%}{{ settings.heading_color }}{%- endif -%};
  }

  .heading-section__preheading {
    color: {%- if preheading_alpha != 0 -%}{{ section.settings.preheading_color }}{%- else -%}{{ settings.heading_color }}{%- endif -%};
  }

  .heading-section__subheading {
    color: {%- if subheading_alpha != 0 -%}{{ section.settings.subheading_color }}{%- else -%}{{ settings.heading_color }}{%- endif -%};
  }
{%- endcapture -%}

{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-right: {{ section.settings.padding_right }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    padding-left: {{ section.settings.padding_left }}px;

    {% if section.settings.width == 'wide' -%}
      width: 100%;
    {%- elsif section.settings.width == 'half' -%}
      width: 50%;
    {%- endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section_css,
    id: section.id,
  %}

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

{% if title != blank or subheading != blank or preheading != blank %}
  <section
    class="
      section
      {{ section.settings.css_class }}
      is-width-{{ section.settings.width }}
      {% if background_alpha != 0 or gradient_alpha != 0  %}
        has-background
      {% endif %}
    "
    data-section-id="{{ section.id }}"
    {% if section.settings.animation != "none" %}
      data-scroll-class="{{ section.settings.animation }}"
    {% endif %}
  >
    <div class="container">
      <div class="one-whole column">
        <div
          class="
            heading-section__content
            is-flex
            is-flex-column
            is-align-{{ section.settings.heading_alignment }}
            {% if section.settings.mobile_text_alignment != 'none' %}
              is-align--mobile-{{ section.settings.mobile_text_alignment }}
            {% endif %}
            text-align-{{ section.settings.heading_alignment }}
            vertical-spacing-{{ section.settings.vertical_spacing }}
          "
        >
          {% if preheading != blank %}
            <div class="heading-section__preheading subtitle">
              {{- preheading -}}
            </div>
          {% endif %}

          {% if title != blank %}
            <h2 class="heading-section__heading title">
              {{- title -}}
            </h2>
          {% endif %}

          {% if subheading != blank %}
            <div class="heading-section__subheading subtitle">
              {{- subheading -}}
            </div>
          {% endif %}

          {% if settings.display_heading_divider == true %}
            <div
              class="
                heading-divider
                heading-divider--{{ settings.heading_divider_style }}
              "
              {% if settings.heading_divider_animation != "none" %}
                data-scroll-class="{{ settings.heading_divider_animation }}"
              {% endif %}
            >
            </div>
          {% endif %}
      </div>
    </div>
  </section>
{% endif %}

{% schema %}
  {
    "name": "Heading",
    "class": "heading-section",
    "settings": [
      {
        "type": "richtext",
        "id": "preheading",
        "label": "Preheading",
        "default": "<p>Add a preheading</p>"
      },
      {
        "type": "text",
        "id": "title",
        "label": "Heading",
        "default": "Section heading"
      },
      {
        "type": "richtext",
        "id": "subheading",
        "label": "Subheading",
        "default": "<p>Additional text or description of section</p>"
      },
      {
        "type": "select",
        "id": "heading_alignment",
        "label": "Text alignment",
        "options": [
          {
            "value": "start",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "end",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "select",
        "id": "vertical_spacing",
        "label": "Vertical text spacing",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ],
        "default": "medium"
      },
      {
        "type": "header",
        "content": "Text colors"
      },
      {
        "type": "color",
        "id": "preheading_color",
        "label": "Preheading",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color",
        "id": "heading_color",
        "label": "Heading",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color",
        "id": "subheading_color",
        "label": "Subheading",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "header",
        "content": "Background"
      },
      {
        "type": "color",
        "id": "background",
        "label": "Background",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color",
        "id": "gradient",
        "label": "Background gradient",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "range",
        "id": "gradient_rotation",
        "label": "Gradient rotation",
        "min": 0,
        "max": 180,
        "step": 10,
        "default": 0,
        "unit": "deg"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "half",
            "label": "Half"
          },
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_left",
        "label": "Left spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_right",
        "label": "Right spacing",
        "default": 0,
        "min": 0,
        "max": 80,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          },
          {
            "value": "zoomIn",
            "label": "Zoom in"
          }
        ]
      },
      {
        "type": "header",
        "content": "Mobile text"
      },
      {
        "type": "select",
        "id": "mobile_text_alignment",
        "label": "Mobile text alignment",
        "options": [
          {
            "value": "none",
            "label": "Same as desktop"
          },
          {
            "value": "start",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "end",
            "label": "Right"
          }
        ],
        "default": "none"
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced"
      },
      {
        "type": "paragraph",
        "content": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "presets": [
      {
        "name": "Heading",
        "category": "Layout",
        "settings": {

        }
      }
    ],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
