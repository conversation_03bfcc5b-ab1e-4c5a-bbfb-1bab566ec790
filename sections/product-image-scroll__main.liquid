{% assign section_id = section.id %}
{% assign width = section.settings.width %}

{% style %}
  #shopify-section-{{ section_id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section_id,
  %}
{% endstyle %}

{% liquid
  assign collection_handles = product.collections | map: 'handle'
  assign selected_variant = product.selected_variant

  if product.variants.size == 1 or settings.select_first_available_variant
    assign selected_variant = product.selected_or_first_available_variant
  endif

  assign product_tags = product.tags | join: ' '
%}

<section
  class="
    section
    is-width-{{ width }}
    {{ section.settings.css_class }}
  "
>
  <div class="product-{{ product.id }} has-padding-top">
    <div
      class="
        product_section
        js-product_section
        container
        is-justify-space-between
        {% if section.settings.product_images_position == 'right' %}
          is-flex-row-reverse
        {% endif %}
      "
    >
      <div
        class="
          product__images
          one-half
          column
          medium-down--one-whole
        "
      >
        {%
          render 'product__images',
          product: product,
          display_thumbnails: section.settings.display_thumbnails,
          enable_thumbnail_slider: section.settings.enable_thumbnail_slider,
          product_height: section.settings.product_height,
          set_product_height: section.settings.set_product_height,
          video_looping: section.settings.video_looping,
          slideshow_transition: section.settings.slideshow_transition,
          enable_product_lightbox: section.settings.enable_product_lightbox,
          enable_zoom: section.settings.enable_zoom,
        %}
      </div>

      <div
        class="
          product__information
          has-product-sticker
          one-half
          column
          medium-down--one-whole
        "
      >
        <div class="sticky-product-scroll">
          {% if settings.stickers_enabled %}
            {%
              render 'product-thumbnail__sticker',
              context: 'product',
              collection_handles: collection_handles,
            %}
          {% endif %}

          {% for block in section.blocks %}
            <div
              class="
                product-block
                product-block--{{ block.type | downcase | replace: '_', '-' }}
                {% if forloop.first == true %}
                  product-block--first
                {% endif %}
              "
              {{ block.shopify_attributes }}
            >
              {% case block.type %}
                {% when 'complementary_products' %}
                  {%
                    render 'complementary-products',
                    product: product,
                    block: block,
                  %}

                {% when 'vendor' %}
                  <p class="vendor">
                    <span class="vendor">
                      {{ product.vendor | link_to_vendor }}
                    </span>
                  </p>

                {% when 'sku' %}
                  <p class="sku">
                    {{ selected_variant.sku }}
                  </p>

                {% when 'title' %}
                  <h1 class="product-title title">
                    {% if section_id == 'quickshop' %}
                      <a href="{{ product.url }}">
                        {{ product.title }}
                      </a>
                    {% else %}
                      {{ product.title }}
                    {% endif %}
                  </h1>

                {% when 'size-chart' %}
                  {% for tag in product.tags %}
                    {% if tag contains 'meta-size-chart-' %}
                      {% assign meta_size_chart = true %}
                    {% endif %}
                  {% endfor %}

                  {% if settings.size_chart != blank or block.settings.size_chart != blank or meta_size_chart %}
                    <a
                      class="product__size-chart"
                      href="javascript:;"
                      data-fancybox
                      data-src="#size-chart{{ product.id }}"
                      data-type="inline"
                    >
                      {{ block.settings.title | escape }}
                    </a>

                    {%
                      render 'popup-size-chart',
                      block: block,
                      product_id: product.id,
                    %}
                  {% endif %}

                {% when 'price' %}
                  <div class="modal_price subtitle" data-display-savings="{{ block.settings.display_savings }}">
                    {% comment %}Inject @pixelunion/shopify-price-ui/price-ui begin{% endcomment %}
                    <div class="price-ui price-ui--loading" data-price-ui>
                      <noscript>
                        <style>
                          .price-ui--loading {
                            display: block !important;
                            opacity: 1 !important;
                          }
                        </style>
                      </noscript>
                      {% assign compare_at_price = false %}
                    
                      {% if product.compare_at_price and product.compare_at_price != product.price %}
                        {% if product.compare_at_price_varies %}
                          {%- capture price_min -%}
                            {%-
                              render 'price-ui-templates',
                              template: 'price-min',
                              value: product.compare_at_price_min,
                            -%}
                          {%- endcapture -%}
                    
                          {%- capture price_max -%}
                            {%-
                              render 'price-ui-templates',
                              template: 'price-max',
                              value: product.compare_at_price_max,
                            -%}
                          {%- endcapture -%}
                    
                          {%- assign compare_at_price = 'product.price.range_html' | t: price_min: price_min, price_max: price_max -%}
                        {% else %}
                          {%- capture compare_at_price -%}
                            {%-
                              render 'price-ui-templates',
                              template: 'price',
                              value: product.compare_at_price,
                            -%}
                          {%- endcapture -%}
                        {% endif %}
                      {% endif %}
                    
                      {% if product.price_varies %}
                        {%- capture price_min -%}
                          {%-
                            render 'price-ui-templates',
                            template: 'price-min',
                            value: product.price_min,
                          -%}
                        {%- endcapture -%}
                    
                        {%- capture price_max -%}
                          {%-
                            render 'price-ui-templates',
                            template: 'price-max',
                            value: product.price_max,
                          -%}
                        {%- endcapture -%}
                    
                        {%- assign price = 'product.price.range_html' | t: price_min: price_min, price_max: price_max -%}
                      {% else %}
                        {%- capture price -%}
                          {%-
                            render 'price-ui-templates',
                            template: 'price',
                            value: product.price,
                          -%}
                        {%- endcapture -%}
                      {% endif %}
                    
                      {%-
                        render 'price-ui-templates',
                        template: 'price-ui',
                        compare_at_price: compare_at_price,
                        price: price,
                        unit_pricing: false,
                      -%}
                    </div>
                    {% comment %}Inject @pixelunion/shopify-price-ui/price-ui end{% endcomment %}

                  </div>

                {% when 'description' %}
                  {% if product.description != blank %}
                    <div
                      class="
                        description
                        content
                        has-padding-top
                      "
                    >
                      {{ product.description | split: '<!-- split -->' | first }}
                    </div>
                  {% endif %}

                {% when 'form' %}
                  <div class="product-form-container has-padding-top">
                    {%
                      render 'product__notify-me-form',
                      product: product,
                    %}

                    {% unless collection_handles contains 'coming-soon' %}
                      {%
                        render 'product__form',
                        context: 'product',
                        product: product,
                        sold_out_options: sold_out_options,
                        selected_variant: selected_variant,
                        show_payment_button: block.settings.show_payment_button,
                        show_recipient_form: block.settings.show_gift_card_recipient_form,
                        collection_handles: collection_handles,
                      %}
                    {% endunless %}
                  </div>

                {% when 'product-links' %}
                  {% if block.settings.show_collections or block.settings.show_tags or block.settings.show_type %}
                    <div class="product__classification-links has-padding-top">
                      {% if block.settings.show_collections %}
                        <p class="product__collections-list tags">
                          <span class="product__classification-title">
                            {{ 'products.product.collections' | t }}:
                          </span>

                          {% for col in product.collections %}
                            <span class="tag tag--{{ tag_style }}">
                              <a href="{{ col.url }}" title="{{ col.title }}">
                                {{ col.title }}
                              </a>
                            </span>
                          {% endfor %}
                        </p>
                      {% endif %}

                      {% if block.settings.show_type %}
                        <p class="product__type-list tags">
                          <span class="product__classification-title">
                            {{ 'products.product.product_types' | t }}:
                          </span>
                          <span class="tag tag--{{ tag_style }}">
                            {{ product.type | link_to_type }}
                          </span>
                        </p>
                      {% endif %}

                      {% if block.settings.show_tags and product.tags.size > 0 %}
                        <p class="product__tags-list tags">
                          <span class="product__classification-title">{{ 'products.product.tags' | t }}:</span>
                          {% for tag in product.tags %}
                            {% unless tag contains 'meta-' %}
                              <span class="tag tag--{{ tag_style }}">
                                <a
                                  href="{{ routes.collections_url }}/{% if collection %}{{ collection.handle }}{% else %}all{% endif %}/{{ tag | handle }}"
                                  title="{{ 'products.product.products_tagged' | t: tag: tag }}"
                                >
                                  {{ tag }}
                                </a>
                              </span>
                            {% endunless %}
                          {% endfor %}
                        </p>
                      {% endif %}
                    </div>
                  {% endif %}

                {% when 'rating' %}
                  {% if product.metafields.reviews.rating.value != blank %}
                    <div class="product__rating rating">
                      {%
                        render 'rating-stars',
                        value: product.metafields.reviews.rating.value.rating,
                        scale_max: product.metafields.reviews.rating.value.scale_max,
                      %}

                      <p class="rating__text">
                        <span aria-hidden="true">
                          {{ product.metafields.reviews.rating.value }} / {{ product.metafields.reviews.rating.value.scale_max }}
                        </span>
                      </p>

                      <p class="rating__count">
                        <span aria-hidden="true">
                          {{ product.metafields.reviews.rating_count }}
                          {% if product.metafields.reviews.rating_count > 1 %}
                            {{ "general.accessibility.star_reviews_text" | t }}
                          {% else %}
                            {{ "general.accessibility.star_review_text" | t }}
                          {% endif %}
                        </span>
                      </p>
                    </div>
                  {% endif %}

                {% when 'share' %}
                  <div class="product__social-share has-padding-top">
                    {% render 'social-share-buttons' %}
                  </div>

                {% when 'text' %}
                  <div class="product-text">
                    {{ block.settings.text }}
                  </div>

                {% when '@app' %}
                  <div class="product-app">
                    {% render block %}
                  </div>
              {% endcase %}
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
</section>

{% comment %} Shopify-XR {% endcomment %}
{% if product.media %}
  <script>
    window.ShopifyXR=window.ShopifyXR||function(){(ShopifyXR.q=ShopifyXR.q||[]).push(arguments)}
      {% assign models = product.media | where: 'media_type', 'model' | json %}
      ShopifyXR('addModels', {{ models }});
  </script>
{% endif %}

{% liquid
  for block in section.blocks
    if block.type == 'complementary_products'
      assign product_recommendation_limit = block.settings.product_recommendation_limit
      break
    endif
  endfor
%}

<script
  type="application/json"
  data-section-id="{{ section_id }}"
  data-section-data
>
  {
    "product": {{ product | json }},
    "section_id": {{ section.id | json }},
    "product_recommendation_limit": {{ product_recommendation_limit | json }},
    "display_savings": {{ section.settings.display_savings | json }},
    "enable_zoom": {{ section.settings.enable_zoom | json }},
    "enable_product_lightbox": {{ section.settings.enable_product_lightbox | json }},
    "enable_thumbnail_slider": {{ section.settings.enable_thumbnail_slider | json }},
    "slideshow_speed": {{ section.settings.slideshow_speed | json }},
    "slideshow_transition": {{ section.settings.slideshow_transition | json }},
    "thumbnails_enabled": {{ section.settings.display_thumbnails | json }},
    "product_media_amount": {{ product.media.size }},
    "template": "image-scroll"
  }
</script>
<script src="{{ 'z__jsProduct.js' | asset_url }}"></script>

{% schema %}
  {
    "name": "Product - image scroll",
    "max_blocks": 10,
    "class": "product-template product-main has-sidebar-option jsProduct product-template--image-scroll",
    "settings": [
      {
        "type": "header",
        "content": "Product gallery"
      },
      {
        "type": "checkbox",
        "id": "enable_zoom",
        "label": "Magnify on hover",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "enable_product_lightbox",
        "label": "Enable lightbox",
        "default": true
      },
      {
        "type": "header",
        "content": "Mobile Product Gallery"
      },
      {
        "type": "checkbox",
        "id": "display_thumbnails",
        "label": "Show thumbnails",
        "default": true
      },
      {
        "type": "range",
        "id": "slideshow_speed",
        "label": "Gallery speed",
        "min": 0,
        "max": 6,
        "unit": "sec",
        "default": 0,
        "info": "Set to 0 to disable autoplay."
      },
      {
        "type": "select",
        "id": "slideshow_transition",
        "label": "Gallery transition",
        "options": [
          {
            "value": "slide",
            "label": "Slide"
          },
          {
            "value": "fade",
            "label": "Fade"
          }
        ],
        "default": "slide"
      },
      {
        "type": "checkbox",
        "id": "enable_thumbnail_slider",
        "label": "Enable thumbnail slider",
        "default": true
      },
      {
        "type": "header",
        "content": "Media",
        "info": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
      },
      {
        "type": "radio",
        "id": "product_images_position",
        "label": "Media position",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "left"
      },
      {
        "type": "checkbox",
        "id": "set_product_height",
        "label": "Set height of product media",
        "default": false
      },
      {
        "type": "range",
        "id": "product_height",
        "label": "Product media height",
        "min": 200,
        "max": 800,
        "step": 10,
        "default": 500,
        "unit": "px"
      },
      {
        "type": "checkbox",
        "id": "video_looping",
        "label": "Enable video looping",
        "default": false
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 20,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "complementary_products",
        "name": "Complementary products",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "To select complementary products, add the Search & Discovery app. [Learn more](https:\/\/shopify.dev\/themes\/product-merchandising\/recommendations)"
          },
          {
            "type": "text",
            "id": "heading",
            "label": "Heading",
            "default": "Pairs well with"
          },
          {
            "type": "range",
            "id": "product_recommendation_limit",
            "label": "Maximum products to show",
            "min": 1,
            "max": 10,
            "default": 5
          },
          {
            "type": "range",
            "id": "products_per_slide",
            "label": "Number of products per page",
            "min": 1,
            "max": 3,
            "default": 2
          }
        ]
      },
      {
        "type": "title",
        "name": "Title",
        "limit": 1
      },
      {
        "type": "vendor",
        "name": "Vendor",
        "limit": 1
      },
      {
        "type": "price",
        "name": "Price",
        "limit": 1,
        "settings": [
          {
            "type": "checkbox",
            "id": "display_savings",
            "label": "Show price savings",
            "default": true
          }
        ]
      },
      {
        "type": "rating",
        "name": "Product rating",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "To display a rating, add a product rating app. [Learn more](https://apps.shopify.com/product-reviews)"
          }
        ]
      },
      {
        "type": "sku",
        "name": "SKU",
        "limit": 1
      },
      {
        "type": "text",
        "name": "Text",
        "settings": [
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Text block</p>"
          }
        ]
      },
      {
        "type": "description",
        "name": "Description",
        "limit": 1
      },
      {
        "type": "form",
        "name": "Form",
        "limit": 1,
        "settings": [
          {
            "type": "header",
            "content": "Dynamic Checkout Button"
          },
          {
            "type": "checkbox",
            "id": "show_payment_button",
            "label": "Show dynamic checkout button",
            "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https:\/\/help.shopify.com\/manual\/using-themes\/change-the-layout\/dynamic-checkout)",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_gift_card_recipient_form",
            "label": "t:sections.product.blocks.form.show_gift_card_recipient_form.label",
            "info": "t:sections.product.blocks.form.show_gift_card_recipient_form.info",
            "default": false
          },
          {
            "type": "paragraph",
            "content": "Customize additional form features for the product in Theme settings > Product form."
          }
        ]
      },
      {
        "type": "product-links",
        "name": "Product links",
        "limit": 1,
        "settings": [
          {
            "type": "checkbox",
            "id": "show_collections",
            "label": "Show collections",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_types",
            "label": "Show types",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_tags",
            "label": "Show tags",
            "default": true
          }
        ]
      },
      {
        "type": "share",
        "name": "Share",
        "limit": 1
      },
      {
        "type": "size-chart",
        "name": "Size chart",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Label",
            "default": "Size chart"
          },
          {
            "type": "page",
            "id": "size_chart",
            "label": "Size chart",
            "info": "[Learn more](https:\/\/help.outofthesandbox.com\/hc\/en-us\/articles\/115006910707-Using-the-Size-Chart-Sections-themes-)"
          }
        ]
      }
    ],
    "default": {
      "settings": {

      }
    }
  }
{% endschema %}