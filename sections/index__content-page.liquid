{% comment %}
** Content page **
{% endcomment %}

{% liquid
  assign content_page = pages[section.settings.content_page]

  # Is the color set to transparent?
  assign background_alpha = section.settings.background | color_extract: 'alpha'
  assign gradient_alpha = section.settings.gradient | color_extract: 'alpha'
%}

{% comment %} Section specific CSS {% endcomment %}
{% capture section_css -%}
  .section {
    background-image: linear-gradient({{ section.settings.gradient_rotation }}deg, rgba(255,255,255,0), {{ section.settings.gradient }});
    background-color: {%- if background_alpha != 0 -%}{{ section.settings.background }}{%- endif -%};
  }
{%- endcapture -%}

{% style %}
  #shopify-section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top }}px;
    padding-right: {{ section.settings.padding_right }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    padding-left: {{ section.settings.padding_left }}px;

    {% if section.settings.width == 'wide' -%}
      width: 100%;
    {%- elsif section.settings.width == 'half' -%}
      width: 50%;
    {%- endif %}
  }

  @media only screen and (max-width: 798px) {
    #shopify-section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
    }
  }

  {%
    render 'css-loop',
    css: section_css,
    id: section.id,
  %}

  {%
    render 'css-loop',
    css: section.settings.custom_css,
    id: section.id,
  %}
{% endstyle %}

<section
  class="
    section
    {{ section.settings.css_class }}
    is-width-{{ section.settings.width }}
    {% if background_alpha != 0 or gradient_alpha != 0  %}
      has-background
    {% endif %}
    has-padding-top
    has-padding-bottom
  "
  {% if section.settings.animation != "none" %}
    data-scroll-class="{{ section.settings.animation }}"
  {% endif %}
>
  <div
    class="
      container
      has-limit
      content
    "
  >
    {% if content_page != blank %}
      {%
        render 'page-multi-column',
        content: content_page.content,
      %}
    {% else %}
      <div class="one-whole column">
        <p class="text-align-center">
          {{- 'homepage.onboarding.no_content' | t -}}
        </p>
      </div>
    {% endif %}
  </div>
</section>

{% schema %}
  {
    "name": "Page",
    "class": "content-page",
    "settings": [
      {
        "type": "page",
        "id": "content_page",
        "label": "Content page"
      },
      {
        "type": "header",
        "content": "Background"
      },
      {
        "type": "color",
        "id": "background",
        "label": "Background",
        "default": "#EEEEEE"
      },
      {
        "type": "color",
        "id": "gradient",
        "label": "Gradient",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "range",
        "id": "gradient_rotation",
        "label": "Gradient rotation",
        "min": 0,
        "max": 180,
        "step": 10,
        "default": 0,
        "unit": "deg"
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "width",
        "label": "Width",
        "default": "standard",
        "options": [
          {
            "value": "half",
            "label": "Half"
          },
          {
            "value": "standard",
            "label": "Standard"
          },
          {
            "value": "wide",
            "label": "Wide"
          }
        ]
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Top spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Bottom spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_left",
        "label": "Left spacing",
        "min": 0,
        "max": 80,
        "default": 0,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_right",
        "label": "Right spacing",
        "default": 0,
        "min": 0,
        "max": 80,
        "unit": "px"
      },
      {
        "type": "select",
        "id": "animation",
        "label": "Animation",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "fadeIn",
            "label": "Fade in"
          },
          {
            "value": "fadeInDown",
            "label": "Fade in down"
          },
          {
            "value": "fadeInLeft",
            "label": "Fade in left"
          },
          {
            "value": "fadeInRight",
            "label": "Fade in right"
          },
          {
            "value": "slideInLeft",
            "label": "Slide in left"
          },
          {
            "value": "slideInRight",
            "label": "Slide in right"
          },
          {
            "value": "zoomIn",
            "label": "Zoom in"
          }
        ]
      },
      {
        "type": "header",
        "content": "Mobile layout"
      },
      {
        "type": "range",
        "id": "padding_top_mobile",
        "label": "Mobile top spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding_bottom_mobile",
        "label": "Mobile bottom spacing",
        "min": 0,
        "max": 80,
        "default": 40,
        "unit": "px"
      },
      {
        "type": "header",
        "content": "Advanced",
        "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
      },
      {
        "type": "text",
        "id": "css_class",
        "label": "CSS Class"
      },
      {
        "type": "textarea",
        "id": "custom_css",
        "label": "Custom CSS"
      }
    ],
    "presets": [{
      "name": "Page",
      "category": "Text",
      "settings": {
      }
    }],
    "disabled_on": {
      "groups": [
        "*"
      ]
    }
  }
{% endschema %}
