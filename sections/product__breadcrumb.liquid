{% comment %} Product breadcrumb {% endcomment %}

{% assign id = section.id %}
{% comment %} Layout {% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% assign animation = section.settings.animation | default: 'none' %}
{% comment %} Advanced {% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% comment %} Section specific CSS {% endcomment %}
{% style %}
  #shopify-section-{{ id }} {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
    {% if width == 'wide' %}
      width: 100%;
    {% endif %}
  }

  {% render 'css-loop',
          css: section.settings.custom_css,
          id: section.id
  %}
{% endstyle %}

{% comment %} HTML markup {% endcomment %}
<section class="section
        {{ css_class }}
        is-width-{{ width }}"
        {% if animation != "none" %}
          data-scroll-class="{{ animation }}"
        {% endif %}>
  <div class="container">
    <div class="one-whole column is-flex is-justify-space-between is-flex-column-reverse-mobile is-align-center">

      <div class="breadcrumb__container">
        {% render 'breadcrumb', context: 'product' %}
      </div>

      {% if collection.previous_product or collection.next_product %}
        <div class="page-navigation-arrows has-padding-top has-padding-bottom is-{{ settings.breadcrumb_capitalization }}">
          {% if collection.previous_product %}
            <a href="{{ collection.previous_product }}" title="{{ 'products.general.previous_product_html' | t }}" class="page-navigation__next">
              {{ 'products.general.previous_product_html' | t }}
            </a>
          {% endif %}
          {% if collection.previous_product and collection.next_product %}
            <span class="page-navigation__divider"> | </span>
          {% endif %}
          {% if collection.next_product %}
            <a href="{{collection.next_product}}" title="{{ 'products.general.next_product_html' | t }}" class="page-navigation__next">
              {{ 'products.general.next_product_html' | t }}
            </a>
          {% endif %}
        </div>
      {% endif %}

    </div>
  </div>
</section>

{% schema %}

{
  "name": "Breadcrumb",
  "class": "product-template product-breadcrumb",
  "templates": [
    "product"
  ],
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ],
      "default": "standard"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 20,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced",
      "info": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/360022329373)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ],
  "default": {
    "settings": {

    }
  }
}

{% endschema %}
