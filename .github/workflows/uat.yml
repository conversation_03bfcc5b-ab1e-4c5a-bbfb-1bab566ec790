name: "Deploy to UAT"

on:
  push:
    branches: [ shopify-uat ]
  pull_request:
    branches: [ shopify-uat ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Shopify
        uses: ./.github/actions/shopify
        env:
          SHOPIFY_PASSWORD: ${{ secrets.UAT_SHOPIFY_PASSWORD }}
          SHOPIFY_STORE_URL: ${{ secrets.UAT_SHOPIFY_STORE_URL }}
          SHOPIFY_THEME_ID: ${{ secrets.UAT_SHOPIFY_THEME_ID }}
          THEME_PATH: ./
        with:
          args: >
            --ignored-file=config/settings_data.json
            --ignored-file=config/settings_schema.json
            --ignored-file=templates/index.json
            --ignored-file=sections/header-group-classic.json
            --allow-live
