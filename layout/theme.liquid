{% capture content %}
<!doctype html>
<html class="no-js no-touch" lang="{{ shop.locale }}">
<head>
  <script async src="https://sdk.postscript.io/sdk.bundle.js?shopId=194751"></script>

<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" />
<link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    />
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script>
    window.Store = window.Store || {};
    window.Store.id = {{ shop.id }};
  </script>
  <meta charset="utf-8">
  <meta http-equiv="cleartype" content="on">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.shop_bg_color }}">
  <link rel="canonical" href="{{ canonical_url }}">
  {% if product.tags contains 'redirect' %}
    <meta name="robots" content="noindex,nofollow">
    <script>
      window.location.href = "{{ shop.url }}";
    </script>
    
    {% else %}
      <meta name="robots" content="index,follow">
  {% endif %}

  {%- if current_tags -%}{%- assign meta_tags = current_tags | join: ', ' -%}{%- endif -%}
  <title>{%- if template.name == "index" -%}{{ page_title }}{%- else -%}{{ page_title }}{% if current_tags %} {{ 'general.meta.tagged_html' | t: tags: meta_tags }}{%- endif -%}{% if current_page != 1 %} {{ 'general.meta.page' | t: page_number: current_page }}{% endif %}{% unless page_title contains shop.name %} - {{ shop.name }}{% endunless %}{% endif %}</title>

  <!-- DNS prefetches -->
  <link rel="dns-prefetch" href="https://cdn.shopify.com">
  <link rel="dns-prefetch" href="https://fonts.shopify.com">
  <link rel="dns-prefetch" href="https://monorail-edge.shopifysvc.com">
  <link rel="dns-prefetch" href="https://ajax.googleapis.com">

  <!-- Preconnects -->
  <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
  <link rel="preconnect" href="https://fonts.shopify.com" crossorigin>
  <link rel="preconnect" href="https://monorail-edge.shopifysvc.com">
  <link rel="preconnect" href="https://ajax.googleapis.com">

  <!-- Preloads -->
  <!-- Preload CSS -->
  <link rel="preload" href="{{ 'fancybox.css' | asset_url }}" as="style">
  <link rel="preload" href="{{ 'styles.css' | asset_url }}" as="style">

  <!-- Preload JS -->
  <link rel="preload" href="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js" as="script">
  <link rel="preload" href="{{ 'vendors.js' | asset_url }}" as="script">
  <link rel="preload" href="{{ 'utilities.js' | asset_url }}" as="script">
  <link rel="preload" href="{{ 'app.js' | asset_url }}" as="script">

  {% if settings.show_multiple_currencies or shop.enabled_currencies.size > 1 or localization.available_countries.size > 1 %}
    <link rel="preload" href="/services/javascripts/currencies.js" as="script">
    <link rel="preload" href="{{ 'currencyConversion.js' | asset_url }}" as="script">
  {% endif %}

  {% if settings.enable_shipping_calculator and template.name == 'cart' %}
    <link rel="preload" href="{{ 'shopify_common.js' | shopify_asset_url }}" as="script">
    <link rel="preload" href="{{ 'api.jquery.js' | shopify_asset_url }}" as="script">
  {% endif %}

  {%- if template.directory == 'customers' -%}
  <link rel="preload" href="{{ 'shopify_common.js' | shopify_asset_url }}" as="script">
  <link rel="preload" href="{{ 'customer_area.js' | shopify_asset_url }}" as="script">
  {%- endif -%}

  {% if settings.disqus_enabled %}
    {% if template.name == "blog" or template.name == "article" or template.name == "index" %}
      <link rel="preload" href="//{{ settings.disqus_shortname }}.disqus.com/count.js" as="script">
    {% endif %}
  {% endif %}
  <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css"
    />
    <!-- Cropper CSS -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/cropper/2.3.4/cropper.min.css"
    />
     <!-- Cropper JS -->
     <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/0.8.1/cropper.min.js"></script>

  <!-- CSS for Flex -->
  <link rel="stylesheet" href="{{ 'fancybox.css' | asset_url }}">
  <link rel="stylesheet" href="{{ 'styles.css' | asset_url }}">

  {% comment %}Inject theme-object begin{% endcomment %}
  <script>
    window.PXUTheme = window.PXUTheme || {};
    window.PXUTheme.version = '5.1.0';
    window.PXUTheme.name = 'Flex';
  </script>
  {% comment %}Inject theme-object end{% endcomment %}


  <script>
    {% render 'js-variables' %}
  </script>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js" defer></script>
  <script src="{{ 'vendors.js' | asset_url }}" defer></script>
  <script src="{{ 'utilities.js' | asset_url }}" defer></script>
  <script src="{{ 'app.js' | asset_url }}" defer></script>

  {%- if settings.show_multiple_currencies or shop.enabled_currencies.size > 1 or localization.available_countries.size > 1 -%}
    <script type="text/javascript" src="/services/javascripts/currencies.js"></script>
    <script src="{{ 'currencyConversion.js' | asset_url }}" defer></script>
  {%- endif -%}



  {%- if settings.enable_shipping_calculator and template.name == "cart" -%}
    <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer></script>
    <script src="{{ 'api.jquery.js' | shopify_asset_url }}" defer></script>
    <script src="{{ '/services/javascripts/countries.js' }}"></script>
  {%- endif -%}

  {%- if template.directory == 'customers' -%}
    <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer></script>
    <script src="{{ 'customer_area.js' | shopify_asset_url }}" defer></script>
  {%- endif -%}

  {% if settings.disqus_enabled %}
    {% if template.name == "blog" or template.name == "article" or template.name == "index" %}
      <script id="dsq-count-scr" src="//{{ settings.disqus_shortname }}.disqus.com/count.js" async></script>
    {% endif %}
  {% endif %}

  <noscript>
    <style>
      /* Insert styles for styles when JS is not loaded */

      .noscript {
        display: block;
      }

    </style>
  </noscript>
  <script>
    document.documentElement.className=document.documentElement.className.replace(/\bno-js\b/,'js');
    if(window.Shopify&&window.Shopify.designMode)document.documentElement.className+=' in-theme-editor';
    if(('ontouchstart' in window)||window.DocumentTouch&&document instanceof DocumentTouch)document.documentElement.className=document.documentElement.className.replace(/\bno-touch\b/,'has-touch');
  </script>

  {%- if page_description -%}
    <meta name="description" content="{{ page_description | escape }}{% if current_tags %} {{ 'general.meta.tagged_html' | t: tags: meta_tags | escape }}.{% endif %}{% if current_page != 1 %} {{ 'general.meta.page' | t: page_number: current_page }}.{% endif %}" />
  {%- endif -%}

  {%- if settings.favicon != nil -%}
    <link rel="shortcut icon" type="image/x-icon" href="{{ settings.favicon | img_url: '180x180' }}">
    <link rel="apple-touch-icon" href="{{ settings.favicon | img_url: '180x180' }}"/>
    <link rel="apple-touch-icon" sizes="57x57" href="{{ settings.favicon | img_url: '57x57' }}"/>
    <link rel="apple-touch-icon" sizes="60x60" href="{{ settings.favicon | img_url: '60x60' }}"/>
    <link rel="apple-touch-icon" sizes="72x72" href="{{ settings.favicon | img_url: '72x72' }}"/>
    <link rel="apple-touch-icon" sizes="76x76" href="{{ settings.favicon | img_url: '76x76' }}"/>
    <link rel="apple-touch-icon" sizes="114x114" href="{{ settings.favicon | img_url: '114x114' }}"/>
    <link rel="apple-touch-icon" sizes="180x180" href="{{ settings.favicon | img_url: '180x180' }}"/>
    <link rel="apple-touch-icon" sizes="228x228" href="{{ settings.favicon | img_url: '228x228' }}"/>
  {%- else -%}
    <link rel="shortcut icon" type="image/x-icon" href="{{ 'favicon.png' | asset_url }}">
  {%- endif -%}

  {%- render 'header-scripts.zipifypages', renderctx: 'thm' -%}{{ content_for_header }}

  {% render 'social-meta-info' %}
  {% if collection.previous_product %}<link rel="prev" href="{{ collection.previous_product }}">{% endif %}
  {% if collection.next_product %}<link rel="next" href="{{ collection.next_product }}">{% endif %}

  {% if template.name == 'customers' %}
    {{ "shopify_common.js" | shopify_asset_url | script_tag }}
    {{ "customer_area.js"  | shopify_asset_url | script_tag }}
  {% endif %}

  <script id="elevar-dl-listener-config" type="application/json">
    {"data_layer_listener_script": "https://shopify-gtm-suite.getelevar.com/shops/c30d8dc0e1dd4e019c56446164b392a703019b67/events.js", "ss_url": null, "signing_key": null, "myshopify_domain": null}
  </script>
  <script>
    (() => { const configElement = document.getElementById("elevar-dl-listener-config"); if (!configElement) { console.error("Elevar: DLL Config element not found"); return; } const config = JSON.parse(configElement.textContent); const script = document.createElement("script"); script.type = "text/javascript"; script.src = config.data_layer_listener_script; script.onerror = function () { console.error("Elevar: DLL JS script failed to load"); }; script.onload = function () { if (!window.ElevarGtmSuiteListener) { console.error("Elevar: `ElevarGtmSuiteListener` is not defined"); return; } window.ElevarGtmSuiteListener.handlers.listen({ ssUrl: config.ss_url, signingKey: config.signing_key, myshopifyDomain: config.myshopify_domain }); }; const headScripts = document.head.getElementsByTagName("script"); if (headScripts[0]) { document.head.insertBefore(script, headScripts[0]); } else { document.head.appendChild(script); } })(); 
  </script>

  {% if shop.metafields.cuddleclones.GoogleTagManager != "" %}
    {{ shop.metafields.cuddleclones.GoogleTagManager }}
  {% endif %}

  {% if shop.metafields.cuddleclones.GoogleAPI != "" %}
    {{ shop.metafields.cuddleclones.GoogleAPI }}
  {% endif %}

  {% if shop.metafields.cuddleclones.all_page_script != "" %}
    {{ shop.metafields.cuddleclones.all_page_script }}
  {% endif %}

  {% if shop.metafields.cuddleclones.shareasale_script != "" %}
    {{ shop.metafields.cuddleclones.shareasale_script  }}
  {% endif %}

   <!-- PostPilot SiteMatch Pixel Script CWA 7-5-23 START -->
   <script type='text/javascript'>
   var script = document.createElement('script');
   script.src = 'https://xp2023-pix.s3.amazonaws.com/px_pBSUM.js';
   document.getElementsByTagName('head')[0].appendChild(script);
 </script>
 <!-- PostPilot SiteMatch Pixel Script CWA 7-5-23 END -->

 <!-- START VWO Async SmartCode CWA 17/1/2024-->
  <link rel="preconnect" href="https://dev.visualwebsiteoptimizer.com" />
  <script type='text/javascript' id='vwoCode'>
    window._vwo_code || (function() {
    var account_id=833284,
    version=2.0,
    settings_tolerance=2000,
    hide_element='body',
    hide_element_style = 'opacity:0 !important;filter:alpha(opacity=0) !important;background:none !important',
    /* DO NOT EDIT BELOW THIS LINE */
    f=false,w=window,d=document,v=d.querySelector('#vwoCode'),cK='_vwo_'+account_id+'_settings',cc={};try{var c=JSON.parse(localStorage.getItem('_vwo_'+account_id+'_config'));cc=c&&typeof c==='object'?c:{}}catch(e){}var stT=cc.stT==='session'?w.sessionStorage:w.localStorage;code={use_existing_jquery:function(){return typeof use_existing_jquery!=='undefined'?use_existing_jquery:undefined},library_tolerance:function(){return typeof library_tolerance!=='undefined'?library_tolerance:undefined},settings_tolerance:function(){return cc.sT||settings_tolerance},hide_element_style:function(){return'{'+(cc.hES||hide_element_style)+'}'},hide_element:function(){return typeof cc.hE==='string'?cc.hE:hide_element},getVersion:function(){return version},finish:function(){if(!f){f=true;var e=d.getElementById('_vis_opt_path_hides');if(e)e.parentNode.removeChild(e)}},finished:function(){return f},load:function(e){var t=this.getSettings(),n=d.createElement('script'),i=this;if(t){n.textContent=t;d.getElementsByTagName('head')[0].appendChild(n);if(!w.VWO||VWO.caE){stT.removeItem(cK);i.load(e)}}else{n.fetchPriority='high';n.src=e;n.type='text/javascript';n.onerror=function(){_vwo_code.finish()};d.getElementsByTagName('head')[0].appendChild(n)}},getSettings:function(){try{var e=stT.getItem(cK);if(!e){return}e=JSON.parse(e);if(Date.now()>e.e){stT.removeItem(cK);return}return e.s}catch(e){return}},init:function(){if(d.URL.indexOf('__vwo_disable__')>-1)return;var e=this.settings_tolerance();w._vwo_settings_timer=setTimeout(function(){_vwo_code.finish();stT.removeItem(cK)},e);var t=d.currentScript,n=d.createElement('style'),i=this.hide_element(),r=t&&!t.async&&i?i+this.hide_element_style():'',c=d.getElementsByTagName('head')[0];n.setAttribute('id','_vis_opt_path_hides');v&&n.setAttribute('nonce',v.nonce);n.setAttribute('type','text/css');if(n.styleSheet)n.styleSheet.cssText=r;else n.appendChild(d.createTextNode(r));c.appendChild(n);this.load('https://dev.visualwebsiteoptimizer.com/j.php?a='+account_id+'&u='+encodeURIComponent(d.URL)+'&vn='+version)}};w._vwo_code=code;code.init();})();
  </script>
  <!-- END VWO Async SmartCode CWA 17/1/2024 -->

  <!--intelligems Script Start-->
  <script>
    window.Shopify = window.Shopify || {theme: {id: {{ theme.id }}, role: '{{ theme.role }}' } };
    window._template = {
        directory: "{{ template.directory }}",
        name: "{{ template.name }}",
        suffix: "{{ template.suffix }}"
    }
  </script> 
  <script src="https://cdn.intelligems.io/cde708c5ba8a.js"></script>
   <!--intelligems Script Ended-->
 
 
</head>

{%- capture money_format_output -%}
  {%- if settings.currency_format == 'money_with_currency_format' -%}
    {{- shop.money_with_currency_format | strip_html -}}
  {%- else -%}
    {{- shop.money_format | strip_html -}}
  {%- endif -%}
{%- endcapture -%}
<body
  class="
    {%- if template.name == '404' -%}
      error-404
    {% else -%}
      {{ template | replace: '.', '-' | handle }}
    {%- endif -%}
    {% if settings.header_layout == 'vertical' -%}
      has-vertical-header
    {%- endif -%}
  "
  data-money-format="{{ money_format_output }}"
  data-shop-url="{{ shop.url }}"
  data-current-lang="{{ request.locale.iso_code }}"
  data-site-header-layout="{{ settings.header_layout }}"
>
  <div
    class="
      site-overlay
      site-overlay--hidden
    "
    aria-hidden="true"
    data-site-overlay
  ></div>

  {% comment %}Inject icon-star-symbol begin{% endcomment %}
  <svg
    class="icon-star-reference"
    aria-hidden="true"
    focusable="false"
    role="presentation"
    xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="3 3 17 17" fill="none"
  >
    <symbol id="icon-star">
      <rect class="icon-star-background" width="20" height="20" fill="currentColor"/>
      <path d="M10 3L12.163 7.60778L17 8.35121L13.5 11.9359L14.326 17L10 14.6078L5.674 17L6.5 11.9359L3 8.35121L7.837 7.60778L10 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    </symbol>
    <clipPath id="icon-star-clip">
      <path d="M10 3L12.163 7.60778L17 8.35121L13.5 11.9359L14.326 17L10 14.6078L5.674 17L6.5 11.9359L3 8.35121L7.837 7.60778L10 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </clipPath>
  </svg>
  {% comment %}Inject icon-star-symbol end{% endcomment %}

  {% comment %}Inject css-variables begin{% endcomment %}
  {% style %}
    :root {
      --color-body-text: {{ settings.regular_color }};
      --color-body: {{ settings.shop_bg_color }};
      --color-bg: {{ settings.shop_bg_color }};
    }
  {% endstyle %}
  {% comment %}Inject css-variables end{% endcomment %}

  {% comment %}Inject @pixelunion/shopify-price-ui/price-ui-globals begin{% endcomment %}
  {%- capture price -%}{%- render 'price-ui-templates', template: 'price' -%}{%- endcapture -%}
  {%- capture price_percent -%}{%- render 'price-ui-templates', template: 'price-percent' -%}{%- endcapture -%}
  {%- capture price_min -%}{%- render 'price-ui-templates', template: 'price-min' -%}{%- endcapture -%}
  {%- capture price_max -%}{%- render 'price-ui-templates', template: 'price-max' -%}{%- endcapture -%}
  {%- capture unit_quantity -%}{%- render 'price-ui-templates', template: 'unit-quantity' -%}{%- endcapture -%}
  {%- capture unit_price -%}{%- render 'price-ui-templates', template: 'unit-price' -%}{%- endcapture -%}
  {%- capture unit_measurement -%}{%- render 'price-ui-templates', template: 'unit-measurement' -%}{%- endcapture -%}
  
  <template id="price-ui">{%- render 'price-ui-templates', template: 'price-ui' -%}</template>
  <template id="price-ui-badge">{%- render 'price-ui-templates', template: 'price-ui-badge' -%}</template>
  
  <template id="price-ui__price">{{ price }}</template>
  <template id="price-ui__price-range">{{- 'product.price.range_html' | t: price_min: price_min, price_max: price_max -}}</template>
  <template id="price-ui__unit-pricing">{{- 'product.price.unit_pricing_html' | t: unit_quantity: unit_quantity, unit_price: unit_price, unit_measurement: unit_measurement -}}</template>
  <template id="price-ui-badge__percent-savings-range">{{- 'product.badge.sale_percentage_range_html' | t: price_percent: price_percent -}}</template>
  <template id="price-ui-badge__percent-savings">{{- 'product.badge.sale_percentage_single_html' | t: price_percent: price_percent -}}</template>
  <template id="price-ui-badge__price-savings-range">{{- 'product.badge.sale_money_range_html' | t: price: price -}}</template>
  <template id="price-ui-badge__price-savings">{{- 'product.badge.sale_money_single_html' | t: price: price -}}</template>
  <template id="price-ui-badge__on-sale">{{- 'product.badge.sale' | t -}}</template>
  <template id="price-ui-badge__sold-out">{{- 'product.badge.sold_out' | t -}}</template>
  <template id="price-ui-badge__in-stock">{{- 'product.badge.in_stock' | t -}}</template>
  {% comment %}Inject @pixelunion/shopify-price-ui/price-ui-globals end{% endcomment %}


  {% case settings.header_layout %}
    {% when 'classic' %}
      {% sections 'header-group-classic' %}
    {% when 'centered' %}
      {% sections 'header-group-centered' %}
    {% when 'vertical' %}
      {% sections 'header-group-vertical' %}
    {% else %}
      {% sections 'header-group-search-focus' %}
  {% endcase %}

  <div style="--background-color: {{ settings.shop_bg_color }}">
    {%
      render 'age-gate',
      id: 'page',
      sections: content_for_layout,
    %}
  </div>

  {%- if settings.cart_action == 'drawer' -%}
    {% render 'ajax-cart' %}
  {%- endif -%}

  {% if settings.header_layout == "vertical" %}
    <div class="is-beside-vertical-header" data-is-beside-vertical-header>
  {% endif %}

  <div id="template-{{ template | replace: '.', '-' | handle }}" data-check-for-order="true">
    <div class="dynamic-sections">
      {{ content_for_layout }}
    </div>
  </div>

  {% case settings.footer_layout %}
    {% when 'centered' %}
      {% sections 'footer-group-centered' %}
    {% when 'classic' %}
      {% sections 'footer-group-classic' %}
    {% when 'promotional' %}
      {% sections 'footer-group-promotional' %}
  {% endcase %}

  {% if settings.header_layout == "vertical" %}
    </div>
  {% endif %}

  {% if settings.show_fixed_message %}
    {% section 'fixed-message' %}
  {% endif %}

  {% if settings.enable_quickshop %}
    {% render 'quick-shop' %}
  {% endif %}

  {% if settings.search_display_style == 'popup' %}
    {% render 'search-popup' %}
  {% endif %}

  {% if settings.show_popup %}
    {% section 'popup' %}
  {% endif %}

  {% render 'mobile-search' %}

  {% render 'structured-data' %}
{%- render 'page-footer.zipifypages', renderctx: 'thm', ocuapp: oneclickupsellapp -%}
{% comment %} <script>
  let shopUrl="{{shop.url}}";
  if( shopUrl=='https://cuddleclones-dev.myshopify.com'){
    document.addEventListener('DOMContentLoaded', function() {
        const element = document.querySelector('.navbar-link');
        if (element) {
            element.style.display = 'none';
        }
    });
}
</script> {% endcomment %}
<script>
  window.addEventListener("load", function() {
    setTimeout(() => {
      var localeElement = document.querySelector(".locale-selectors__container");
      if(localeElement){
        let countryRegionLabel=localeElement?.querySelector("#country_code_label");
        if(countryRegionLabel){
          const countryRegionLabelChildSpan=countryRegionLabel.querySelector("span");
          countryRegionLabelChildSpan.style.backgroundColor="#8275c8";
        }
      }
     
      let languageLabel=localeElement?.querySelector("#locale_code_label");
      if(languageLabel){
        const languageLabelChildSpan=languageLabel.querySelector("span");
        languageLabelChildSpan.style.backgroundColor="#8275c8";
      }
      
      if (localeElement) {
        localeElement.style.display = "flex";
        localeElement.style.justifyContent = "center";
        localeElement.parentElement.style.width = "100%";
      }

      if (window.innerWidth <= 480) {
        const styleElement = document.querySelector('#country_code');
        
        if (styleElement) {
          styleElement.style.width = "250px";
          styleElement.style.setProperty("width", "250px", "important"); // Set width with !important
        }
        var reOrderElement = document.querySelector('.os-step__info-item');
        consle.log(reOrderElement);
        if (reOrderElement) {
          reOrderElement.style.display = none;
      }
      }
    }, 1500); 
    const judgemeReviewsNumber=document.querySelectorAll(".jdgm-histogram__frequency");
    judgemeReviewsNumber.forEach(review=>{
      review.style.color="#6a696b";
    });
    const judgemeReviewTimestamp=document.querySelectorAll(".jdgm-rev__timestamp");
    judgemeReviewTimestamp.forEach(timestamp=>{
      timestamp.style.color="#6a696b";
    });
  });
</script>
   <!-- Widget : Gift with Purchase -->
  <!-- <div data-rebuy-id="153563"></div> -->
  <!-- End Widget : Gift with Purchase -->

</body>
</html>
{% endcapture %}
{% assign content = content | replace_first: '<!doctype', '!doctype' | split: ' <' %}
{%- for html in content -%}
  {%- assign htmlblock = html | strip | prepend: ' <' -%}{{ htmlblock | replace: ' </', '</' }}{%- endfor -%}

<script>
    var searchResults=document.querySelector("#searchresults");

    if (searchResults) {
      searchResults.style.margin = "20px 100px";

      var formElement = searchResults.querySelector("form");
      if(formElement){
        var inputs=formElement.querySelectorAll("input");
        inputs[0].style.cssText="padding:10px; border-radius:10px; width:50%;";
        inputs[1].style.cssText="padding:10px; border-radius:10px;";
      }  
        
      var olElement = searchResults.querySelector("ol");
      if (olElement) {
        olElement.style.padding = "30px";
        var liElements = olElement.querySelectorAll("li");
        liElements.forEach(function(li) {
          li.style.padding = "35px 20px";
        });
      }
        
    }
</script>

<script>
  function handlePlushPostScript(){
    let plush_product={{product|json}};
    if(hostname == productionDomain){
      window.postscript.event('page_view', {
        "shop_id": "194751", // Postscript Shop ID
        "url": "{{shop.metafields.cuddleclones.store_url}}/products/{{plush_product.handle}}", // absolute URL of the current page
        "search_params": { "variant": plush_product.variants[0].id }, 
        "page_type": "product",
        "referrer": "{{shop.metafields.cuddleclones.store_url}}/collections/all", // absolute URL of the referring page
        "resource": { // information about the product
          "category": "Uncategorized", 
          "name": plush_product.title, 
          "price_in_cents": plush_product.price, 
          "resource_id": plush_product.id, 
          "resource_type": "product", 
          "sku": null, 
          "variant_id": plush_product.variants[0].id, 
          "vendor": plush_product.vendor 
        }
      });
    }
  }
</script>