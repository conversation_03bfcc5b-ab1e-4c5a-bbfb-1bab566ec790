{% capture content %}
<!doctype html>
<html lang="{{ shop.locale }}">
<head>
  <script>
    window.Store = window.Store || {};
    window.Store.id = {{ shop.id }};
  </script>
  <meta charset="utf-8">
  <meta http-equiv="cleartype" content="on">
  <meta name="robots" content="index,follow">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.shop_bg_color }}">
  <link rel="canonical" href="{{ canonical_url }}">

  {%- if current_tags -%}{%- assign meta_tags = current_tags | join: ', ' -%}{%- endif -%}
  <title>{%- if template contains "index" -%}{{ page_title }}{%- else -%}{{ page_title }}{% if current_tags %} {{ 'general.meta.tagged_html' | t: tags: meta_tags }}{%- endif -%}{% if current_page != 1 %} {{ 'general.meta.page' | t: page_number: current_page }}{% endif %}{% unless page_title contains shop.name %} - {{ shop.name }}{% endunless %}{% endif %}</title>

  <!-- DNS prefetches -->
  <link rel="dns-prefetch" href="https://cdn.shopify.com">
  <link rel="dns-prefetch" href="https://fonts.shopify.com">
  <link rel="dns-prefetch" href="https://monorail-edge.shopifysvc.com">
  <link rel="dns-prefetch" href="https://ajax.googleapis.com">

  <!-- Preconnects -->
  <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
  <link rel="preconnect" href="https://fonts.shopify.com" crossorigin>
  <link rel="preconnect" href="https://monorail-edge.shopifysvc.com">
  <link rel="preconnect" href="https://ajax.googleapis.com">

  <!-- Preloads -->
  <!-- Preload CSS -->
  <link rel="preload" href="{{ 'fancybox.css' | asset_url }}" as="style">
  <link rel="preload" href="{{ 'styles.css' | asset_url }}" as="style">

  <!-- Preload JS -->
  <link rel="preload" href="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js" as="script">
  <link rel="preload" href="{{ 'vendors.js' | asset_url }}" as="script">
  <link rel="preload" href="{{ 'utilities.js' | asset_url }}" as="script">
  <link rel="preload" href="{{ 'app.js' | asset_url }}" as="script">

  {% if settings.show_multiple_currencies or shop.enabled_currencies.size > 1 or localization.available_countries.size > 1 %}
    <link rel="preload" href="/services/javascripts/currencies.js" as="script">
    <link rel="preload" href="{{ 'currencyConversion.js' | asset_url }}" as="script">
  {% endif %}

  {% if settings.enable_shipping_calculator and template contains 'cart' %}
    <link rel="preload" href="{{ 'shopify_common.js' | shopify_asset_url }}" as="script">
    <link rel="preload" href="{{ 'api.jquery.js' | shopify_asset_url }}" as="script">
    <link rel="preload" href="{{ 'countries.js' | shopify_asset_url }}" as="script">
  {% endif %}

  {%- if template.directory == 'customers' -%}
  <link rel="preload" href="{{ 'shopify_common.js' | shopify_asset_url }}" as="script">
  <link rel="preload" href="{{ 'customer_area.js' | shopify_asset_url }}" as="script">
  {%- endif -%}

  {% if settings.disqus_enabled %}
    {% if template contains "blog" or template contains "article" or template contains "index" %}
      <link rel="preload" href="//{{ settings.disqus_shortname }}.disqus.com/count.js" as="script">
    {% endif %}
  {% endif %}

  <!-- CSS for Flex -->
  <link rel="stylesheet" href="{{ 'fancybox.css' | asset_url }}">
  <link rel="stylesheet" href="{{ 'styles.css' | asset_url }}">

  {% comment %}Inject theme-object begin{% endcomment %}
  <script>
    window.PXUTheme = window.PXUTheme || {};
    window.PXUTheme.version = '5.1.0';
    window.PXUTheme.name = 'Flex';
  </script>
  {% comment %}Inject theme-object end{% endcomment %}


  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js" defer></script>
  <script src="{{ 'vendors.js' | asset_url }}" defer></script>
  <script src="{{ 'utilities.js' | asset_url }}" defer></script>
  <script src="{{ 'app.js' | asset_url }}" defer></script>

  {%- if settings.show_multiple_currencies or shop.enabled_currencies.size > 1 or localization.available_countries.size > 1 -%}
    <script type="text/javascript" src="/services/javascripts/currencies.js"></script>
    <script src="{{ 'currencyConversion.js' | asset_url }}" defer></script>
  {%- endif -%}

  <script>
    {% render 'js-variables' %}
  </script>

  {%- if settings.enable_shipping_calculator and template contains "cart" -%}
    <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer></script>
    <script src="{{ 'api.jquery.js' | shopify_asset_url }}" defer></script>
    <script src="{{ 'countries.js' | shopify_asset_url }}" defer></script>
  {%- endif -%}

  {%- if template.directory == 'customers' -%}
    <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer></script>
    <script src="{{ 'customer_area.js' | shopify_asset_url }}" defer></script>
  {%- endif -%}

  {% if settings.disqus_enabled %}
    {% if template contains "blog" or template contains "article" or template contains "index" %}
      <script id="dsq-count-scr" src="//{{ settings.disqus_shortname }}.disqus.com/count.js" async></script>
    {% endif %}
  {% endif %}

  <noscript>
    <style>
      /* Insert styles for styles when JS is not loaded */

      .noscript {
        display: block;
      }

    </style>
  </noscript>

  {%- if page_description -%}
    <meta name="description" content="{{ page_description | escape }}{% if current_tags %} {{ 'general.meta.tagged_html' | t: tags: meta_tags | escape }}.{% endif %}{% if current_page != 1 %} {{ 'general.meta.page' | t: page_number: current_page }}.{% endif %}" />
  {%- endif -%}

  {%- if settings.favicon != nil -%}
    <link rel="shortcut icon" type="image/x-icon" href="{{ settings.favicon | img_url: '180x180' }}">
    <link rel="apple-touch-icon" href="{{ settings.favicon | img_url: '180x180' }}"/>
    <link rel="apple-touch-icon" sizes="57x57" href="{{ settings.favicon | img_url: '57x57' }}"/>
    <link rel="apple-touch-icon" sizes="60x60" href="{{ settings.favicon | img_url: '60x60' }}"/>
    <link rel="apple-touch-icon" sizes="72x72" href="{{ settings.favicon | img_url: '72x72' }}"/>
    <link rel="apple-touch-icon" sizes="76x76" href="{{ settings.favicon | img_url: '76x76' }}"/>
    <link rel="apple-touch-icon" sizes="114x114" href="{{ settings.favicon | img_url: '114x114' }}"/>
    <link rel="apple-touch-icon" sizes="180x180" href="{{ settings.favicon | img_url: '180x180' }}"/>
    <link rel="apple-touch-icon" sizes="228x228" href="{{ settings.favicon | img_url: '228x228' }}"/>
  {%- else -%}
    <link rel="shortcut icon" type="image/x-icon" href="{{ 'favicon.png' | asset_url }}">
  {%- endif -%}

  {{ content_for_header }}

  {% render 'social-meta-info' %}
  {% if collection.previous_product %}<link rel="prev" href="{{ collection.previous_product }}">{% endif %}
  {% if collection.next_product %}<link rel="next" href="{{ collection.next_product }}">{% endif %}

  {% if template contains 'customers' %}
    {{ "shopify_common.js" | shopify_asset_url | script_tag }}
    {{ "customer_area.js"  | shopify_asset_url | script_tag }}
  {% endif %}
</head>

{%- capture money_format_output -%}
  {%- if settings.currency_format == 'money_with_currency_format' -%}
    {{- shop.money_with_currency_format | strip_html -}}
  {%- else -%}
    {{- shop.money_format | strip_html -}}
  {%- endif -%}
{%- endcapture -%}
<body
  class="
    {%- if template.name == '404' -%}
      error-404
    {% else -%}
      {{ template | replace: '.', '-' | handle }}
    {%- endif -%}
    {% if settings.header_layout == 'vertical' -%}
      has-vertical-header
    {%- endif -%}
  "
  data-money-format="{{ money_format_output }}"
  data-shop-url="{{ shop.url }}"
  data-current-lang="{{ request.locale.iso_code }}"
>
  {% comment %}Inject icon-star-symbol begin{% endcomment %}
  <svg
    class="icon-star-reference"
    aria-hidden="true"
    focusable="false"
    role="presentation"
    xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="3 3 17 17" fill="none"
  >
    <symbol id="icon-star">
      <rect class="icon-star-background" width="20" height="20" fill="currentColor"/>
      <path d="M10 3L12.163 7.60778L17 8.35121L13.5 11.9359L14.326 17L10 14.6078L5.674 17L6.5 11.9359L3 8.35121L7.837 7.60778L10 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    </symbol>
    <clipPath id="icon-star-clip">
      <path d="M10 3L12.163 7.60778L17 8.35121L13.5 11.9359L14.326 17L10 14.6078L5.674 17L6.5 11.9359L3 8.35121L7.837 7.60778L10 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </clipPath>
  </svg>
  {% comment %}Inject icon-star-symbol end{% endcomment %}

  {% comment %}Inject css-variables begin{% endcomment %}
  {% style %}
    :root {
      --color-body-text: {{ settings.regular_color }};
      --color-body: {{ settings.shop_bg_color }};
      --color-bg: {{ settings.shop_bg_color }};
    }
  {% endstyle %}
  {% comment %}Inject css-variables end{% endcomment %}

  {% comment %}Inject @pixelunion/shopify-price-ui/price-ui-globals begin{% endcomment %}
  {%- capture price -%}{%- render 'price-ui-templates', template: 'price' -%}{%- endcapture -%}
  {%- capture price_percent -%}{%- render 'price-ui-templates', template: 'price-percent' -%}{%- endcapture -%}
  {%- capture price_min -%}{%- render 'price-ui-templates', template: 'price-min' -%}{%- endcapture -%}
  {%- capture price_max -%}{%- render 'price-ui-templates', template: 'price-max' -%}{%- endcapture -%}
  {%- capture unit_quantity -%}{%- render 'price-ui-templates', template: 'unit-quantity' -%}{%- endcapture -%}
  {%- capture unit_price -%}{%- render 'price-ui-templates', template: 'unit-price' -%}{%- endcapture -%}
  {%- capture unit_measurement -%}{%- render 'price-ui-templates', template: 'unit-measurement' -%}{%- endcapture -%}
  
  <template id="price-ui">{%- render 'price-ui-templates', template: 'price-ui' -%}</template>
  <template id="price-ui-badge">{%- render 'price-ui-templates', template: 'price-ui-badge' -%}</template>
  
  <template id="price-ui__price">{{ price }}</template>
  <template id="price-ui__price-range">{{- 'product.price.range_html' | t: price_min: price_min, price_max: price_max -}}</template>
  <template id="price-ui__unit-pricing">{{- 'product.price.unit_pricing_html' | t: unit_quantity: unit_quantity, unit_price: unit_price, unit_measurement: unit_measurement -}}</template>
  <template id="price-ui-badge__percent-savings-range">{{- 'product.badge.sale_percentage_range_html' | t: price_percent: price_percent -}}</template>
  <template id="price-ui-badge__percent-savings">{{- 'product.badge.sale_percentage_single_html' | t: price_percent: price_percent -}}</template>
  <template id="price-ui-badge__price-savings-range">{{- 'product.badge.sale_money_range_html' | t: price: price -}}</template>
  <template id="price-ui-badge__price-savings">{{- 'product.badge.sale_money_single_html' | t: price: price -}}</template>
  <template id="price-ui-badge__on-sale">{{- 'product.badge.sale' | t -}}</template>
  <template id="price-ui-badge__sold-out">{{- 'product.badge.sold_out' | t -}}</template>
  <template id="price-ui-badge__in-stock">{{- 'product.badge.in_stock' | t -}}</template>
  {% comment %}Inject @pixelunion/shopify-price-ui/price-ui-globals end{% endcomment %}


  {{ content_for_layout }}

</body>
</html>
{% endcapture %}
{% assign content = content | replace_first: '<!doctype', '!doctype' | split: ' <' %}
{%- for html in content -%}{%- assign htmlblock = html | strip | prepend: ' <' -%}{{htmlblock | replace: ' </', '</'}}{%- endfor -%}