{% comment %}
  @param item {Object}
    Required to display unit prices, 'item' or 'line_item'
  @param class {String}
    CSS class names to add to unit price elements
{% endcomment %}

{%- capture total_quantity -%}
  <span class="{{ class | append: '-total-quantity' }}" data-total-quantity>{{ item.unit_price_measurement.quantity_value }}{{ item.unit_price_measurement.quantity_unit }}</span>
{%- endcapture -%}

{%- capture unit_price -%}
  {%- if item.unit_price == 0 -%}
    <span class="{{ class | append: '-amount' }}" data-unit-price-amount>{{ settings.free_price_text }}</span>
  {%- else -%}
    <span class="{{ class | append: '-amount money' }}" data-unit-price-amount>{{ item.unit_price | money }}</span>
  {%- endif -%}
{%- endcapture -%}

{%- capture unit_measure -%}
  <span class="{{ class | append: '-measure' }}" data-unit-price-measure>{%- if item.unit_price_measurement.reference_value != 1 -%}{{ item.unit_price_measurement.reference_value }}{%- endif -%}{{ item.unit_price_measurement.reference_unit }}</span>
{%- endcapture -%}

{%- if item.unit_price_measurement -%}
  <p class="{{ class }}">{{ 'products.item.price.price_per_unit_html' | t: total_quantity: total_quantity, unit_price: unit_price, unit_measure: unit_measure | strip_newlines }}</p>
{%- endif -%}
