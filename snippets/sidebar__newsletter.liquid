{% if block.settings.newsletter_title != blank %}
  <div class="sidebar-block__toggle" data-has-toggle-option>
    <h3 class="sidebar-block__heading" {% if settings.toggle_sidebar %}data-sidebar-block__toggle="closed" aria-label="toggle"{% endif %}>
      {{ block.settings.newsletter_title }}
      {% if settings.toggle_sidebar %}
        <button class="sidebar-block__toggle-icon icon-style--{{ settings.toggle_icon_style }}">
          {% if settings.toggle_icon_style == 'plus_and_minus' %}
            {% render 'icon',
                    name: 'plus',
                    icon_class: 'icon--active'
            %}
            {% render 'icon', name: 'minus' %}
          {% else %}
            {% render 'icon',
                    name: 'down-caret',
                    icon_class: 'icon--active'
            %}
          {% endif %}
        </button>
      {% endif %}
    </h3>
  </div>
{% endif %}
<div class="sidebar-block__content" {% if settings.toggle_sidebar %}data-sidebar-block__content--collapsible{% endif %}>
  <div class="sidebar-block__text content">
    {{ block.settings.newsletter_richtext }}
  </div>
  {%
    render 'newsletter-form',
    type: 'block',
    display_first_name: block.settings.display_first_name,
    display_last_name: block.settings.display_last_name,
    id: 'sidebar',
  %}
</div>
