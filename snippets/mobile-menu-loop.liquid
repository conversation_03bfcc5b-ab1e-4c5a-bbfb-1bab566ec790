{% for link in main_menu.links %}

  <li class="mobile-menu__list-item" data-mobile-menu-navlink-handle="{{ link.title | handleize }}">
    {% if link.links == blank %}
      <div class="mobile-menu__first-level has-no-submenu" data-mobile-menu-has-toggle="false">
        <input class="visually-hidden submenu__input" type="checkbox" id="mobile-submenu-{{ link.title | handleize }}">
        <label class="submenu__label {% if link.url == 'http://' or link.url == '' or link.url == '/' or link.url == 'https://' or link.url == '#' %}has-no-link{% endif %}" for="mobile-submenu-{{ link.title | handleize }}">
          <a class="mobile-menu__item {% if link.active %}is-active{% endif %}" href="{{ link.url }}" >
            {{ link.title }}
          </a>
        </label>
      </div>
    {% else %}
      <div class="mobile-menu__first-level has-submenu" data-mobile-menu-has-toggle="true">
        <input class="visually-hidden submenu__input" type="checkbox" id="mobile-submenu-{{ forloop.index }}">
        <label class="submenu__label" for="mobile-submenu-{{ forloop.index }}">
          <a {% unless link.url == 'http://' or link.url == '' or link.url == '/' or link.url == 'https://' or link.url == '#' %}href="{{ link.url }}"{% endunless %} class="mobile-menu-link {% if link.active %}is-active{% endif %}" >
            {{ link.title }}
          </a>
          <span class="close-dropdown"></span>
        </label>
        <ul class="mobile-submenu__list mobile-menu__submenu has-dropdown" data-mobile-submenu-first-level-list>
          {% assign currentIndex = forloop.index %}
          {% for sub_link in link.links %}
            <li class="mobile-menu__submenu-list-item">
              {% if sub_link.links == blank %}
                <a class="mobile-menu__item" href="{{ sub_link.url }}">{{ sub_link.title }}</a>
              {% else %}
                <div class="mobile-menu__second-level">
                  <input class="visually-hidden submenu__input" type="checkbox" id="mobile-sub-submenu-{{ forloop.index }}-{{ currentIndex }}">
                  <label class="submenu__label" for="mobile-sub-submenu-{{ forloop.index }}-{{ currentIndex }}">
                    <a {% unless sub_link.url == 'http://' or sub_link.url == '' or sub_link.url == '/' or sub_link.url == 'https://' or sub_link.url == '#' %}href="{{ sub_link.url }}"{% endunless %} class="mobile-menu-link {% if sub_link.active %}is-active{% endif %}">
                      {{ sub_link.title }}
                    </a>
                  <span class="close-sub-dropdown"></span>
                  </label>
                  <ul class="mobile-sub-submenu__list mobile-menu__submenu" data-mobile-submenu-second-level-list>
                    {% for sub-sub_link in sub_link.links %}
                      <li class="mobile-menu__submenu-list-item">
                        <a class="mobile-menu__item" href="{{ sub-sub_link.url }}">{{ sub-sub_link.title }}</a>
                      </li>
                    {% endfor %}
                  </ul>
                </div>
              {% endif %}
            </li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}
  </li>
{% endfor %}
