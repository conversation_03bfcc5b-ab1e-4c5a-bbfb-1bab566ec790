{% capture product_media_loop %}
  {% for media in product.media %}
    {% case media.media_type %}
      {% when 'image' %}
        {{ media | img_url: "master" }}^{{ media.alt | escape }}^{{ media.id }}^{{ media.width }}^{{ media.media_type }}
      {% when 'video' %}
        {% capture video_code %}
        <div class="product-gallery__video" data-html5-video data-media-type="{{ media.media_type }}">
          {{ media | video_tag: controls: true }}
        </div>
        {% endcapture %}
        {{ video_code | url_escape }}^{{ media.media_type}}^{{ media.id }}^{{ media.width }}^{{ media.media_type }}
      {% when 'external_video' %}
        {% capture external_video_code %}
        <div class="product-gallery__video" data-youtube-video data-media-type="{{ media.media_type }}">
          {{ media | external_video_tag }}
        </div>
        {% endcapture %}
        {{ external_video_code | url_escape }}^{{ media.media_type}}^{{ media.id }}^{{ media.width }}^{{ media.media_type }}
      {% when 'model' %}
        {% capture model_code %}
        <div class="product-gallery__model is-relative" data-media-type="{{ media.media_type }}">
          {{ media | model_viewer_tag: reveal: 'interaction', toggleable: true, image_size: '800x800' }}
        </div>
        {% endcapture %}
        {{ model_code | url_escape }}^{{ media.media_type}}^{{ media.id }}^{{ media.width }}^{{ media.media_type }}
      {% else %}
        {{ media | img_url: "master" }}^{{ media.alt | escape }}^{{ media.id }}^{{ media.width }}^{{ media.media_type }}
    {% endcase %}
  {% unless forloop.last %}
  ~{% endunless %}
  {% endfor %}
{% endcapture %}

{% assign collection_handles = product.collections | map: 'handle' %}
{% assign variant = product.selected_or_first_available_variant %}
{% capture thumbnail_paths_alts %}{% if product.media %}{% for media in product.media %}{{ media | img_url: "master" }}^{{ media.media_type | escape }}^{{ media.id }}^{{ media.width }}{% unless forloop.last %}~{% endunless %}{% endfor %}{% else %}{% for image in product.images %}{{ image | img_url: "master" }}^{{ image.alt | escape }}^{{ image.id }}^{{ image.width }}{% unless forloop.last %}~{% endunless %}{% endfor %}{% endif %}{% endcapture %}
{% capture image_paths_alts %}{% if product.media %}{{ product_media_loop }}{% else %}{% for image in product.images %}{{ image | img_url: "master" }}^{{ image.alt | escape }}^{{ image.id }}^{{ image.width }}{% unless forloop.last %}~{% endunless %}{% endfor %}{% endif %}{% endcapture %}

<div 
  class="
    quick-shop__buttons 
    animated 
    fadeInUp
  "
>
  <span 
    class="
      quick_shop 
      button 
      action_button 
      {{ settings.quickshop_button_style }} 
      js-quick-shop-link
    "
    data-id="{{ product.id }}"
    data-url="{{ product.url }}"
  >
    {{ 'collections.general.quick_shop' | t }}
  </span>
</div>
