{% comment %}
Required values
menu_link: linklist
______________
Optional values
linklist_class: <string>
link_setting: <string>
{% endcomment %}

<div class="footer__menu is-justify-space-between {{ linklist_class }}">
{% if menu_link != blank %}
  {% assign menu = linklists[menu_link] %}
  <p class="footer__heading">
    {% if link_setting != blank %}<a href="{{ link_setting }}">{% endif %}
      {{ menu.title }}
    {% if link_setting != blank %}</a>{% endif %}
  </p>
  <ul>
    {% for link in menu.links %}
      <li class="footer__menu-link">
        <a href="{{ link.url }}" class="{% if link.active %}is-active{% endif %}">
          {{ link.title }}
        </a>
      </li>
    {% endfor %}
  </ul>
{% else %}
  <p class="footer__heading">{{ 'homepage.onboarding.menu_title' | t }}</p>
  <p>{{ 'homepage.onboarding.no_content' | t }}</p>
{% endif %}
</div>
