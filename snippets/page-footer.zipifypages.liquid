{%- comment -%}
  Updated: 09/07/2024
  This file is system-generated and should not be modified. We reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost
{%- endcomment -%}{% assign zp_available_render_context = 'tpl,thm,idxtpl,lsctn' | split: ',' %}{% if zp_available_render_context contains renderctx %}{% if zp_override_template_name != blank %}{% assign zp_entity_marker = zp_override_template_name %}{% else %}{% assign zp_entity_marker = template.name %}{% endif %}{% if zp_override_template_suffix != blank %}{% assign zp_template_suffix = zp_override_template_suffix %}{% else %}{% assign zp_template_suffix = template.suffix %}{% endif %}{% assign zp_product_template_suffix = zp_template_suffix | remove: '-' %}{% assign zp_shop_dir_name = shop.permanent_domain | sha1 %}{% assign zp_collect_entity_content_parts = false %}{% assign zp_split_tests_available = false %}{% assign zp_use_json_entity_settings = false %}{% assign zp_use_only_custom_template = false %}{% assign zp_shop_metafields_loaded = false %}{% assign zp_current_entity_metafields_loaded = false %}{% assign zp_use_product_markup = false %}{% assign zp_use_current_entity_markup = false %}{% assign zp_use_native_entity_description = false %}{% assign zp_use_native_og_images = false %}{% assign zp_og_entity_type = 'website' %}{% assign zp_is_product_page = false %}{% assign zp_current_entity_index_state = nil %}{% if zp_template_suffix contains 'zipifypages' %}{% assign zp_enable_content_parsing = true %}{% else %}{% assign zp_enable_content_parsing = false %}{% endif %}{% if zp_enable_content_parsing and zp_entity_marker == 'article' %}{% assign zp_current_entity = article %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'aac' %}{% assign zp_scripts_metafield_key = 'articlescripts' %}{% assign zp_entity_data_metafield_key = 'articledata' %}{% assign zp_entity_styles_folder = 'articles' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_use_product_markup = true %}{% elsif zp_enable_content_parsing and zp_entity_marker == 'blog' %}{% assign zp_current_entity = blog %}{% assign zp_current_entity_content = '' | append: zp_current_entity.metafields['zipifypagesblogparts']['blogheaderfooter'] %}{% assign zp_entity_attributes_class = 'bac' %}{% assign zp_scripts_metafield_key = 'blogscripts' %}{% assign zp_entity_data_metafield_key = 'blogdata' %}{% assign zp_entity_styles_folder = 'blogs' %}{% assign zp_use_product_markup = true %}{% elsif zp_enable_content_parsing and zp_entity_marker == 'page' %}{% assign zp_current_entity = page %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'pac' %}{% assign zp_scripts_metafield_key = 'pagescripts' %}{% assign zp_entity_data_metafield_key = 'pagedata' %}{% assign zp_entity_styles_folder = 'pages' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_split_tests_available = true %}{% assign zp_use_product_markup = true %}{% elsif zp_entity_marker == 'index' %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% assign zp_shop_metafields_loaded = true %}{% assign zp_index_page = zp_shop_metafields['indexpage'] %}{% assign zp_index_page_handle = zp_shop_metafields['indexpagehandle'] %}{% if zp_index_page.type == 'page_reference' %}{% assign zp_current_entity = zp_index_page.value %}{% elsif zp_index_page_handle.size > 0 %}{% assign zp_current_entity = pages[zp_index_page_handle] %}{% else %}{% assign zp_current_entity = nil %}{% endif %}{% assign zp_current_entity_id_size = '' | append: zp_current_entity.id | size %}{% if zp_current_entity_id_size > 0 %}{% assign zp_entity_marker = 'page' %}{% assign zp_enable_content_parsing = true %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'pac' %}{% assign zp_scripts_metafield_key = 'pagescripts' %}{% assign zp_entity_data_metafield_key = 'pagedata' %}{% assign zp_entity_styles_folder = 'pages' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_split_tests_available = true %}{% assign zp_use_product_markup = true %}{% else %}{% assign zp_current_entity = nil %}{% assign zp_enable_content_parsing = false %}{% endif %}{% elsif zp_entity_marker == 'product' %}{% assign zp_current_entity = product %}{% assign zp_entity_attributes_class = 'ppac' %}{% assign zp_scripts_metafield_key = 'productpagescripts' %}{% assign zp_entity_data_metafield_key = 'productpagedata' %}{% assign zp_entity_styles_folder = 'product_pages' %}{% assign zp_use_viewport_meta = false %}{% assign zp_use_json_entity_settings = true %}{% assign zp_is_product_page = true %}{% if zp_template_suffix contains 'zipifypages-' %}{% assign zp_current_entity_metafields = shop.metafields[zp_product_template_suffix] %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_index_state = zp_current_entity_metafields['config'].value['page_index_state'] %}{% else %}{% assign zp_current_entity_index_state = zp_current_entity_metafields['config']['page_index_state'] %}{% endif %}{% if zp_use_meta_tags == true %}{% assign zp_use_meta_tags = true %}{% elsif zp_use_meta_tags == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_meta_tags = true %}{% else %}{% assign zp_use_meta_tags = false %}{% endif %}{% if zp_use_open_graph_tags == true %}{% assign zp_use_open_graph_tags = true %}{% elsif zp_use_open_graph_tags == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_open_graph_tags = true %}{% else %}{% assign zp_use_open_graph_tags = false %}{% endif %}{% if zp_use_favicon == true %}{% assign zp_use_favicon = true %}{% elsif zp_use_favicon == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_favicon = true %}{% else %}{% assign zp_use_favicon = false %}{% endif %}{% assign zp_display_content_for_header = false %}{% assign zp_use_native_entity_description = true %}{% assign zp_use_native_og_images = true %}{% assign zp_og_entity_type = 'product' %}{% assign zp_use_current_entity_markup = true %}{% assign zp_product_page_with_zp_layout = true %}{% else %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% assign zp_use_meta_tags = false %}{% assign zp_use_open_graph_tags = false %}{% assign zp_use_favicon = false %}{% assign zp_display_content_for_header = false %}{% assign zp_use_only_custom_template = true %}{% assign zp_product_page_with_zp_layout = false %}{% endif %}{% assign zp_current_entity_metafields_loaded = true %}{% assign zp_current_entity_data = zp_current_entity_metafields['config'] | default: zp_current_entity_metafields[zp_entity_data_metafield_key] | default: 'noassigneddata' %}{% if zp_current_entity_data == 'noassigneddata' %}{% assign zp_enable_content_parsing = false %}{% else %}{% assign zp_enable_content_parsing = true %}{% assign zp_split_tests_available = true %}{% endif %}{% assign zp_current_entity_data = nil %}{% else %}{% assign zp_current_entity = nil %}{% assign zp_current_entity_content = '' %}{% assign zp_use_meta_tags = false %}{% assign zp_use_open_graph_tags = false %}{% assign zp_use_viewport_meta = false %}{% assign zp_use_favicon = false %}{% assign zp_display_content_for_header = false %}{% endif %}{% if zp_enable_content_parsing %}{% unless zp_shop_metafields_loaded %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% endunless %}{% unless zp_current_entity_metafields_loaded %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% endunless %}{% if zp_current_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% if zp_use_only_custom_template %}{% assign zp_entity_custom_template = true %}{% elsif zp_current_entity.template_suffix == 'custom.zipifypages' %}{% assign zp_entity_custom_template = true %}{% elsif zp_current_entity_index_state == 'custom' %}{% assign zp_entity_custom_template = true %}{% else %}{% assign zp_entity_custom_template = false %}{% endif %}{% endif %}{% if zp_collect_entity_content_parts and zp_current_entity_content contains ':|zpendofcontent|:' %}{% assign zp_entity_with_multiparts = true %}{% else %}{% assign zp_entity_with_multiparts = false %}{% endif %}{% assign zp_gdpr_enabled = false %}{% assign zp_alternative_entity_present = false %}{% assign zp_split_single_page_render = false %}{% assign zp_split_test_redirect = false %}{% assign zp_split_test_view_type_redirect = false %}{% if zp_entity_with_multiparts %}{% assign zp_current_entity_content = '' | append: zp_current_entity_content | split: ':|zpendofcontent|:' | first %}{% endif %}{% if zp_enable_content_parsing and zp_split_tests_available %}{% if zp_product_page_with_zp_layout %}{% assign zp_current_entity_object_metafields = zp_current_entity_main_metafields %}{% else %}{% assign zp_current_entity_object_metafields = zp_current_entity_metafields %}{% endif %}{% if zp_current_entity_object_metafields['splittest'] != blank %}{% if zp_current_entity_object_metafields['splittest'].type == 'json' %}{% assign zp_split_test_data = zp_current_entity_object_metafields['splittest'].value %}{% assign zp_split_test_type = zp_split_test_data.type %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data.alternative_handle %}{% assign zp_split_dataset = zp_split_test_data.dataset | json %}{% assign zp_split_dataset = '' | append: zp_split_dataset %}{% assign zp_split_single_page_render = '' | append: zp_split_test_data.single_page_render %}{% assign zp_split_token = zp_split_test_data.token%}{% else %}{% assign zp_split_test_data = '' | append: zp_current_entity_object_metafields['splittest'] | split: ':|~|:' %}{% assign zp_split_test_type = zp_split_test_data[0] %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data[1] %}{% assign zp_split_dataset = zp_split_test_data[2] %}{% assign zp_split_single_page_render = '' | append: zp_split_test_data[3] %}{% assign zp_split_token = '' | append: zp_split_test_data[4] %}{% endif %}{% if zp_split_test_type == 'mainlayout' %}{% if zp_entity_marker == 'article' %}{% assign zp_alternative_entity = articles[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.content %}{% elsif zp_entity_marker == 'blog' %}{% assign zp_alternative_entity = blogs[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.metafields['zipifypagesblogparts']['blogheaderfooter'] %}{% elsif zp_entity_marker == 'page' %}{% assign zp_alternative_entity = pages[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.content %}{% else %}{% assign zp_alternative_entity = nil %}{% assign zp_alternative_entity_content = '' %}{% endif %}{% assign zp_alternative_entity_id_size = '' | append: zp_alternative_entity.id | size %}{% if zp_alternative_entity_id_size > 0 %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity_content | split: ':|zpendofcontent|:' | first %}{% assign zp_alternative_entity_present = true %}{% if zp_split_single_page_render == 'true' %}{% assign zp_split_single_page_render = true %}{% else %}{% assign zp_split_single_page_render = false %}{% endif %}{% assign zp_alternative_entity_title = '' | append: zp_alternative_entity.title %}{% assign zp_alternative_entity_metafields = zp_alternative_entity.metafields['zipifypages'] %}{% if zp_alternative_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_alternative_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_alternative_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% assign zp_entity_scripts = '' | append: zp_alternative_entity_metafields[zp_scripts_metafield_key] | split: '|;|~|;|' %}{% assign zp_alternative_header_scripts = '' | append: zp_entity_scripts[0] | strip %}{% assign zp_entity_scripts = '' %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_alternative_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_alternative_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data contains '"gdpr_enabled":true' %}{% assign zp_gdpr_enabled = true %}{% elsif zp_blocks_data contains '"gdpr_enabled":false' %}{% assign zp_gdpr_enabled = false %}{% elsif zp_blocks_data contains '"gdpr_status":"enable_with_checkbox"' or zp_blocks_data contains '"gdpr_status":"enable_without_checkbox"' %}{% assign zp_gdpr_enabled = true %}{% endif %}{% endif %}{% assign zp_alternative_entity_content_wrapper_class = "zp " | append: zp_entity_attributes_class | append: '-' | append: zp_alternative_entity.id %}{% if zp_alternative_entity.template_suffix == 'custom.zipifypages' %}{% assign zp_alternative_entity_custom_template = true %}{% else %}{% assign zp_alternative_entity_custom_template = false %}{% endif %}{% elsif zp_split_test_type == 'redirect' or zp_split_test_type == 'redirecttarget' %}{% assign zp_split_test_redirect = true %}{% elsif zp_split_test_type == 'viewtyperedirect' %}{% assign zp_split_test_view_type_redirect = true %}{% endif %}{% endif %}{% endif %}{% if zp_enable_content_parsing %}{% assign zp_entity_title = page_title | remove: ':|~|:' | remove: ':--:' | append: entity_title_suffix | strip %}{% if zp_gdpr_enabled == false %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_current_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_current_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data contains '"gdpr_enabled":true' %}{% assign zp_gdpr_enabled = true %}{% elsif zp_blocks_data contains '"gdpr_enabled":false' %}{% assign zp_gdpr_enabled = false %}{% elsif zp_blocks_data contains '"gdpr_status":"enable_with_checkbox"' or zp_blocks_data contains '"gdpr_status":"enable_without_checkbox"' %}{% assign zp_gdpr_enabled = true %}{% endif %}{% endif %}{% assign zp_blocks_data = '' %}{% endif %}{% endif %}{% if zp_enable_content_parsing %}{% assign zp_show_back_to_top_button = false %}{% assign zp_load_loox_scripts = true %}{% if zp_current_entity_metafields['config'].type == 'json' and zp_current_entity_metafields['config'].value['page_last_sync_date'] != blank %}{% assign zp_use_config_entity_settings = true %}{% else %}{% assign zp_use_config_entity_settings = false %}{% endif %}{% if zp_use_json_entity_settings or zp_use_config_entity_settings %}{% if zp_use_config_entity_settings %}{% assign zp_object_settings = zp_current_entity_metafields['config'].value %}{% assign zp_entity_data_settings = zp_object_settings['page_settings_data'] | json %}{% elsif zp_current_entity_metafields[zp_entity_data_metafield_key].type == 'json' %}{% assign zp_object_settings = zp_current_entity_metafields[zp_entity_data_metafield_key].value %}{% assign zp_entity_data_settings = zp_object_settings['page_settings_data'] | default: '' %}{% else %}{% assign zp_object_settings = zp_current_entity_metafields[zp_entity_data_metafield_key] %}{% assign zp_entity_data_settings = zp_object_settings['page_settings_data'] | default: '' %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['enable_back_to_top_button'] %}{% if zp_setting_value == 'true' %}{% assign zp_show_back_to_top_button = true %}{% else %}{% assign zp_show_back_to_top_button = false %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['enable_loox'] %}{% if zp_setting_value == 'true' %}{% assign zp_load_loox_scripts = true %}{% else %}{% assign zp_load_loox_scripts = false %}{% endif %}{% assign zp_setting_value = nil %}{% else %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% assign zp_object_settings_data = '' | append: zp_current_entity_metafields[zp_entity_data_metafield_key] | strip %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_setting_key in zp_settings_keys %}{% assign zp_setting_value = '' | append: zp_settings_values[forloop.index0] | strip %}{% if 'page_settings_data' == zp_setting_key %}{% assign zp_entity_data_settings = zp_setting_value %}{% elsif 'enable_back_to_top_button' == zp_setting_key %}{% if zp_setting_value == 'true' %}{% assign zp_show_back_to_top_button = true %}{% endif %}{% elsif 'enable_loox' == zp_setting_key %}{% unless zp_setting_value == 'true' %}{% assign zp_load_loox_scripts = false %}{% endunless %}{% endif %}{% endfor %}{% endif %}{% if zp_alternative_entity_present == true %}{% assign zp_alternative_data_settings = '' %}{% if zp_alternative_entity_metafields['config'].type == 'json' and zp_alternative_entity_metafields['config'].value['page_last_sync_date'] != blank %}{% assign zp_object_settings = zp_alternative_entity_metafields['config'].value %}{% assign zp_alternative_data_settings = zp_object_settings['page_settings_data'] | json %}{% else %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% assign zp_object_settings_data = '' | append: zp_alternative_entity_metafields[zp_entity_data_metafield_key] | strip %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_setting_key in zp_settings_keys %}{% assign zp_setting_value = '' | append: zp_settings_values[forloop.index0] | strip %}{% if 'page_settings_data' == zp_setting_key %}{% assign zp_alternative_data_settings = zp_setting_value %}{% break %}{% endif %}{% endfor %}{% endif %}{% endif %}{% if zp_alternative_entity_present == true %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_alternative_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_alternative_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data != 'nobuttons' %}{% if routes.root_url != '/' %}{% assign zp_rtlclurl = routes.root_url %}{% assign zp_rtindexrplc = '":"' | append: zp_rtlclurl | append: '/"' %}{% assign zp_rtindexqrplc = '":"' | append: zp_rtlclurl | append: '/?' %}{% assign zp_rtindexhrplc = '":"' | append: zp_rtlclurl | append: '/#' %}{% assign zp_rtpagesrplc = '":"' | append: zp_rtlclurl | append: '/pages/' %}{% assign zp_rtcartrplc = '":"' | append: zp_rtlclurl | append: '/cart"' %}{% assign zp_rtcartqrplc = '":"' | append: zp_rtlclurl | append: '/cart?' %}{% assign zp_rtcartprplc = '":"' | append: zp_rtlclurl | append: '/cart/' %}{% assign zp_rtproductsrplc = '":"' | append: zp_rtlclurl | append: '/products/' %}{% assign zp_rtcollectionsrplc = '":"' | append: zp_rtlclurl | append: '/collections/' %}{% assign zp_rtcheckoutrplc = '":"' | append: zp_rtlclurl | append: '/checkout"' %}{% assign zp_rtcheckoutqrplc = '":"' | append: zp_rtlclurl | append: '/checkout?' %}{% assign zp_blocks_data = zp_blocks_data | replace: '":"/"', zp_rtindexrplc | replace: '":"/?', zp_rtindexqrplc | replace: '":"/#', zp_rtindexhrplc | replace: '":"/pages/', zp_rtpagesrplc | replace: '":"/cart"', zp_rtcartrplc | replace: '":"/cart?', zp_rtcartqrplc | replace: '":"/cart/', zp_rtcartprplc | replace: '":"/products/', zp_rtproductsrplc | replace: '":"/collections/', zp_rtcollectionsrplc | replace: '":"/checkout"', zp_rtcheckoutrplc | replace: '":"/checkout?', zp_rtcheckoutqrplc %}{% assign zp_rtlclurlsch = shop.domain %}{% assign zp_rtlclurl = zp_rtlclurlsch | append: routes.root_url %}{% assign zp_rtindexrplc = '":"https://' | append: zp_rtlclurl | append: '/"' %}{% assign zp_rtindexsch = '":"https://' | append: zp_rtlclurlsch | append: '/"' %}{% assign zp_rtindexerplc = '":"https://' | append: zp_rtlclurl | append: '"' %}{% assign zp_rtindexesch = '":"https://' | append: zp_rtlclurlsch | append: '"' %}{% assign zp_rtindexqrplc = '":"https://' | append: zp_rtlclurl | append: '/?' %}{% assign zp_rtindexqsch = '":"https://' | append: zp_rtlclurlsch | append: '/?' %}{% assign zp_rtindexhrplc = '":"https://' | append: zp_rtlclurl | append: '/#' %}{% assign zp_rtindexhsch = '":"https://' | append: zp_rtlclurlsch | append: '/#' %}{% assign zp_rtpagesrplc = '":"https://' | append: zp_rtlclurl | append: '/pages/' %}{% assign zp_rtpagessch = '":"https://' | append: zp_rtlclurlsch | append: '/pages/' %}{% assign zp_rtcartrplc = '":"https://' | append: zp_rtlclurl | append: '/cart"' %}{% assign zp_rtcartsch = '":"https://' | append: zp_rtlclurlsch | append: '/cart"' %}{% assign zp_rtcartqrplc = '":"https://' | append: zp_rtlclurl | append: '/cart?' %}{% assign zp_rtcartqsch = '":"https://' | append: zp_rtlclurlsch | append: '/cart?' %}{% assign zp_rtcartprplc = '":"https://' | append: zp_rtlclurl | append: '/cart/' %}{% assign zp_rtcartpsch = '":"https://' | append: zp_rtlclurlsch | append: '/cart/' %}{% assign zp_rtproductsrplc = '":"https://' | append: zp_rtlclurl | append: '/products/' %}{% assign zp_rtproductssch = '":"https://' | append: zp_rtlclurlsch | append: '/products/' %}{% assign zp_rtcollectionsrplc = '":"https://' | append: zp_rtlclurl | append: '/collections/' %}{% assign zp_rtcollectionssch = '":"https://' | append: zp_rtlclurlsch | append: '/collections/' %}{% assign zp_rtaccountrplc = '":"https://' | append: zp_rtlclurl | append: '/account"' %}{% assign zp_rtaccountsch = '":"https://' | append: zp_rtlclurlsch | append: '/account"' %}{% assign zp_rtaccountlgnrplc = '":"https://' | append: zp_rtlclurl | append: '/account/login"' %}{% assign zp_rtaccountlgnsch = '":"https://' | append: zp_rtlclurlsch | append: '/account/login"' %}{% assign zp_rtaccountrgstrrplc = '":"https://' | append: zp_rtlclurl | append: '/account/register"' %}{% assign zp_rtaccountrgstrsch = '":"https://' | append: zp_rtlclurlsch | append: '/account/register"' %}{% assign zp_rtaccountlgtrplc = '":"https://' | append: zp_rtlclurl | append: '/account/logout"' %}{% assign zp_rtaccountlgtsch = '":"https://' | append: zp_rtlclurlsch | append: '/account/logout"' %}{% assign zp_rtsearchrplc = '":"https://' | append: zp_rtlclurl | append: '/search"' %}{% assign zp_rtsearchsch = '":"https://' | append: zp_rtlclurlsch | append: '/search"' %}{% assign zp_rtsearchqrplc = '":"https://' | append: zp_rtlclurl | append: '/search?' %}{% assign zp_rtsearchqsch = '":"https://' | append: zp_rtlclurlsch | append: '/search?' %}{% assign zp_rtcheckoutrplc = '":"https://' | append: zp_rtlclurl | append: '/checkout"' %}{% assign zp_rtcheckoutsch = '":"https://' | append: zp_rtlclurlsch | append: '/checkout"' %}{% assign zp_rtcheckoutqrplc = '":"https://' | append: zp_rtlclurl | append: '/checkout?' %}{% assign zp_rtcheckoutqsch = '":"https://' | append: zp_rtlclurlsch | append: '/checkout?' %}{% assign zp_blocks_data = zp_blocks_data | replace: zp_rtindexsch, zp_rtindexrplc | replace: zp_rtindexesch, zp_rtindexerplc | replace: zp_rtindexqsch, zp_rtindexqrplc | replace: zp_rtindexhsch, zp_rtindexhrplc | replace: zp_rtpagessch, zp_rtpagesrplc | replace: zp_rtcartsch, zp_rtcartrplc | replace: zp_rtcartqsch, zp_rtcartqrplc | replace: zp_rtcartpsch, zp_rtcartprplc | replace: zp_rtproductssch, zp_rtproductsrplc | replace: zp_rtcollectionssch, zp_rtcollectionsrplc | replace: zp_rtaccountsch, zp_rtaccountrplc | replace: zp_rtaccountlgnsch, zp_rtaccountlgnrplc | replace: zp_rtaccountrgstrsch, zp_rtaccountrgstrrplc | replace: zp_rtaccountlgtsch, zp_rtaccountlgtrplc | replace: zp_rtsearchsch, zp_rtsearchrplc | replace: zp_rtsearchqsch, zp_rtsearchqrplc | replace: zp_rtcheckoutsch, zp_rtcheckoutrplc | replace: zp_rtcheckoutqsch, zp_rtcheckoutqrplc %}{% endif %}<script type="template/json" id="zp-alternative-blocks-json-data">{{ zp_blocks_data }}</script>{% endif %}{% assign zp_blocks_data = '' | append: zp_alternative_entity_metafields['boosterpage'] | strip | default: 'noboosterpage' | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' %}{% if zp_blocks_data != 'noboosterpage' %}<script type="template/json" id="zp-alternative-boosterpage-json-data">{{ zp_blocks_data }}</script>{% endif %}{% assign zp_blocks_data = '' | append: zp_alternative_entity_metafields['boostercouponsleft'] | strip | default: 'noboostercouponsleft' %}{% if zp_blocks_data != 'noboostercouponsleft' %}<script type="template/json" id="zp-alternative-boostercouponsleft-json-data">{"couponsLeftCount":{{ zp_blocks_data }}}</script>{% endif %}{% endif %}{% assign zp_alternative_entity = nil %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_current_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_current_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data != 'nobuttons' %}{% if routes.root_url != '/' %}{% assign zp_rtlclurl = routes.root_url %}{% assign zp_rtindexrplc = '":"' | append: zp_rtlclurl | append: '/"' %}{% assign zp_rtindexqrplc = '":"' | append: zp_rtlclurl | append: '/?' %}{% assign zp_rtindexhrplc = '":"' | append: zp_rtlclurl | append: '/#' %}{% assign zp_rtpagesrplc = '":"' | append: zp_rtlclurl | append: '/pages/' %}{% assign zp_rtcartrplc = '":"' | append: zp_rtlclurl | append: '/cart"' %}{% assign zp_rtcartqrplc = '":"' | append: zp_rtlclurl | append: '/cart?' %}{% assign zp_rtcartprplc = '":"' | append: zp_rtlclurl | append: '/cart/' %}{% assign zp_rtproductsrplc = '":"' | append: zp_rtlclurl | append: '/products/' %}{% assign zp_rtcollectionsrplc = '":"' | append: zp_rtlclurl | append: '/collections/' %}{% assign zp_rtcheckoutrplc = '":"' | append: zp_rtlclurl | append: '/checkout"' %}{% assign zp_rtcheckoutqrplc = '":"' | append: zp_rtlclurl | append: '/checkout?' %}{% assign zp_blocks_data = zp_blocks_data | replace: '":"/"', zp_rtindexrplc | replace: '":"/?', zp_rtindexqrplc | replace: '":"/#', zp_rtindexhrplc | replace: '":"/pages/', zp_rtpagesrplc | replace: '":"/cart"', zp_rtcartrplc | replace: '":"/cart?', zp_rtcartqrplc | replace: '":"/cart/', zp_rtcartprplc | replace: '":"/products/', zp_rtproductsrplc | replace: '":"/collections/', zp_rtcollectionsrplc | replace: '":"/checkout"', zp_rtcheckoutrplc | replace: '":"/checkout?', zp_rtcheckoutqrplc %}{% assign zp_rtlclurlsch = shop.domain %}{% assign zp_rtlclurl = zp_rtlclurlsch | append: routes.root_url %}{% assign zp_rtindexrplc = '":"https://' | append: zp_rtlclurl | append: '/"' %}{% assign zp_rtindexsch = '":"https://' | append: zp_rtlclurlsch | append: '/"' %}{% assign zp_rtindexerplc = '":"https://' | append: zp_rtlclurl | append: '"' %}{% assign zp_rtindexesch = '":"https://' | append: zp_rtlclurlsch | append: '"' %}{% assign zp_rtindexqrplc = '":"https://' | append: zp_rtlclurl | append: '/?' %}{% assign zp_rtindexqsch = '":"https://' | append: zp_rtlclurlsch | append: '/?' %}{% assign zp_rtindexhrplc = '":"https://' | append: zp_rtlclurl | append: '/#' %}{% assign zp_rtindexhsch = '":"https://' | append: zp_rtlclurlsch | append: '/#' %}{% assign zp_rtpagesrplc = '":"https://' | append: zp_rtlclurl | append: '/pages/' %}{% assign zp_rtpagessch = '":"https://' | append: zp_rtlclurlsch | append: '/pages/' %}{% assign zp_rtcartrplc = '":"https://' | append: zp_rtlclurl | append: '/cart"' %}{% assign zp_rtcartsch = '":"https://' | append: zp_rtlclurlsch | append: '/cart"' %}{% assign zp_rtcartqrplc = '":"https://' | append: zp_rtlclurl | append: '/cart?' %}{% assign zp_rtcartqsch = '":"https://' | append: zp_rtlclurlsch | append: '/cart?' %}{% assign zp_rtcartprplc = '":"https://' | append: zp_rtlclurl | append: '/cart/' %}{% assign zp_rtcartpsch = '":"https://' | append: zp_rtlclurlsch | append: '/cart/' %}{% assign zp_rtproductsrplc = '":"https://' | append: zp_rtlclurl | append: '/products/' %}{% assign zp_rtproductssch = '":"https://' | append: zp_rtlclurlsch | append: '/products/' %}{% assign zp_rtcollectionsrplc = '":"https://' | append: zp_rtlclurl | append: '/collections/' %}{% assign zp_rtcollectionssch = '":"https://' | append: zp_rtlclurlsch | append: '/collections/' %}{% assign zp_rtaccountrplc = '":"https://' | append: zp_rtlclurl | append: '/account"' %}{% assign zp_rtaccountsch = '":"https://' | append: zp_rtlclurlsch | append: '/account"' %}{% assign zp_rtaccountlgnrplc = '":"https://' | append: zp_rtlclurl | append: '/account/login"' %}{% assign zp_rtaccountlgnsch = '":"https://' | append: zp_rtlclurlsch | append: '/account/login"' %}{% assign zp_rtaccountrgstrrplc = '":"https://' | append: zp_rtlclurl | append: '/account/register"' %}{% assign zp_rtaccountrgstrsch = '":"https://' | append: zp_rtlclurlsch | append: '/account/register"' %}{% assign zp_rtaccountlgtrplc = '":"https://' | append: zp_rtlclurl | append: '/account/logout"' %}{% assign zp_rtaccountlgtsch = '":"https://' | append: zp_rtlclurlsch | append: '/account/logout"' %}{% assign zp_rtsearchrplc = '":"https://' | append: zp_rtlclurl | append: '/search"' %}{% assign zp_rtsearchsch = '":"https://' | append: zp_rtlclurlsch | append: '/search"' %}{% assign zp_rtsearchqrplc = '":"https://' | append: zp_rtlclurl | append: '/search?' %}{% assign zp_rtsearchqsch = '":"https://' | append: zp_rtlclurlsch | append: '/search?' %}{% assign zp_rtcheckoutrplc = '":"https://' | append: zp_rtlclurl | append: '/checkout"' %}{% assign zp_rtcheckoutsch = '":"https://' | append: zp_rtlclurlsch | append: '/checkout"' %}{% assign zp_rtcheckoutqrplc = '":"https://' | append: zp_rtlclurl | append: '/checkout?' %}{% assign zp_rtcheckoutqsch = '":"https://' | append: zp_rtlclurlsch | append: '/checkout?' %}{% assign zp_blocks_data = zp_blocks_data | replace: zp_rtindexsch, zp_rtindexrplc | replace: zp_rtindexesch, zp_rtindexerplc | replace: zp_rtindexqsch, zp_rtindexqrplc | replace: zp_rtindexhsch, zp_rtindexhrplc | replace: zp_rtpagessch, zp_rtpagesrplc | replace: zp_rtcartsch, zp_rtcartrplc | replace: zp_rtcartqsch, zp_rtcartqrplc | replace: zp_rtcartpsch, zp_rtcartprplc | replace: zp_rtproductssch, zp_rtproductsrplc | replace: zp_rtcollectionssch, zp_rtcollectionsrplc | replace: zp_rtaccountsch, zp_rtaccountrplc | replace: zp_rtaccountlgnsch, zp_rtaccountlgnrplc | replace: zp_rtaccountrgstrsch, zp_rtaccountrgstrrplc | replace: zp_rtaccountlgtsch, zp_rtaccountlgtrplc | replace: zp_rtsearchsch, zp_rtsearchrplc | replace: zp_rtsearchqsch, zp_rtsearchqrplc | replace: zp_rtcheckoutsch, zp_rtcheckoutrplc | replace: zp_rtcheckoutqsch, zp_rtcheckoutqrplc %}{% endif %}{% if zp_alternative_entity_present == true %}<script type="template/json" id="zp-main-blocks-json-data">{{ zp_blocks_data }}</script>{% else %}<script type="template/json" id="zp-blocks-json-data">{{ zp_blocks_data }}</script>{% endif %}{% endif %}{% assign zp_blocks_data = '' | append: zp_current_entity_metafields['boosterpage'] | strip | default: 'noboosterpage' | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' %}{% if zp_blocks_data != 'noboosterpage' %}{% if zp_alternative_entity_present == true %}<script type="template/json" id="zp-main-boosterpage-json-data">{{ zp_blocks_data }}</script>{% else %}<script type="template/json" id="zp-boosterpage-json-data">{{ zp_blocks_data }}</script>{% endif %}{% endif %}{% assign zp_blocks_data = '' | append: zp_current_entity_metafields['boostercouponsleft'] | strip | default: 'noboostercouponsleft' %}{% if zp_blocks_data != 'noboostercouponsleft' %}{% if zp_alternative_entity_present == true %}<script type="template/json" id="zp-main-boostercouponsleft-json-data">{"couponsLeftCount":{{ zp_blocks_data }}}</script>{% else %}<script type="template/json" id="zp-boostercouponsleft-json-data">{"couponsLeftCount":{{ zp_blocks_data }}}</script>{% endif %}{% endif %}{% assign zp_blocks_data = nil %}{% if zp_alternative_entity_present == true %}<script>!function(){var t=!1;window.ZipifyPages.SplitTest.onEntityStateLoaded(function(){if(!t){t=!0;var e=document.querySelector("#zp-"+this.currentEntity()+"-blocks-json-data");if(e){var n=document.createElement("script");n.setAttribute("id","zp-blocks-json-data"),n.setAttribute("type","template/json"),n.innerHTML=e.innerHTML,document.getElementsByTagName("body")[0].appendChild(n)}}})}();</script><script>!function(){var t=!1;window.ZipifyPages.SplitTest.onEntityStateLoaded(function(){if(!t){t=!0;var e=document.querySelector("#zp-"+this.currentEntity()+"-boosterpage-json-data");if(e){var n=document.createElement("script");n.setAttribute("id","zp-boosterpage-json-data"),n.setAttribute("type","template/json"),n.innerHTML=e.innerHTML,document.getElementsByTagName("body")[0].appendChild(n)}}})}();</script><script>!function(){var t=!1;window.ZipifyPages.SplitTest.onEntityStateLoaded(function(){if(!t){t=!0;var e=document.querySelector("#zp-"+this.currentEntity()+"-boostercouponsleft-json-data");if(e){var n=document.createElement("script");n.setAttribute("id","zp-boostercouponsleft-json-data"),n.setAttribute("type","template/json"),n.innerHTML=e.innerHTML,document.getElementsByTagName("body")[0].appendChild(n)}}})}();</script>{% endif %}{% if zp_entity_data_settings.size > 0 %}{% if zp_alternative_entity_present == true %}<script type="template/json" id="zp-main-entity-settings">{{ zp_entity_data_settings }}</script>{% else %}<script type="template/json" id="zp-entity-settings">{{ zp_entity_data_settings }}</script>{% endif %}{% endif %}{% assign zp_entity_data_settings = nil %}{% if zp_alternative_entity_present == true and zp_alternative_data_settings.size > 0 %}<script type="template/json" id="zp-alternative-entity-settings">{{ zp_alternative_data_settings }}</script>{% endif %}{% assign zp_alternative_data_settings = nil %}{% if zp_alternative_entity_present == true %}<script>!function(){var t=!1;window.ZipifyPages.SplitTest.onEntityStateLoaded(function(){if(!t){t=!0;var e=document.querySelector("#zp-"+this.currentEntity()+"-entity-settings");if(e){var n=document.createElement("script");n.setAttribute("id","zp-entity-settings"),n.setAttribute("type","template/json"),n.innerHTML=e.innerHTML,document.getElementsByTagName("body")[0].appendChild(n)}}})}();</script>{% endif %}{% if zp_gdpr_enabled == true %}<script>!function(){function t(){var t=(new Date).valueOf(),e=function(){var t;try{t=JSON.parse(localStorage.getItem("zp-geoip"))}catch(t){console.error(t)}return t}(),n=e&&e.expires>t;return e&&e.location&&n}function e(){var t;try{t=JSON.parse(this.responseText)}catch(t){console.error(t)}a({location:t,status:"loaded",expires:(new Date).valueOf()+864e5})}function n(){a({location:null,status:"failed"})}function o(){a({status:"loading"})}function a(e){try{localStorage.setItem("zp-geoip",JSON.stringify(e))}catch(t){window.__ZP_GEOIP_STATUS__=e,console.error(t)}}var r=!1;function i(){if(!r){r=!0;var t=new XMLHttpRequest;t.open("GET","https://gip.zipify.com/json/",!0),t.timeout=2e3,t.addEventListener("load",e),t.addEventListener("error",n),t.addEventListener("timeout",n),t.addEventListener("loadstart",o),t.send()}}t()||(a({status:"initialized"}),document.addEventListener("DOMContentLoaded",i))}();</script>{% endif %}<script src="https://cdn03.zipify.com/javascript/page-published-scripts.min.js" type="module"></script>{% assign zp_entity_scripts = '' | append: zp_current_entity_metafields[zp_scripts_metafield_key] | split: '|;|~|;|' %}{% assign zp_entity_footer_scripts = '' | append: zp_entity_scripts[1] | strip %}{% assign zp_entity_scripts = '' %}{% if zp_entity_footer_scripts.size > 0 %}{% if zp_alternative_entity_present == true %}{% assign zp_replacement_key = '</script' %}{% assign zp_replacement_value = '<\/script' %}<script type="text/template" id="zp-main-footer-scripts">{{ zp_entity_footer_scripts | replace: zp_replacement_key, zp_replacement_value }}</script>{% else %}{{ zp_entity_footer_scripts }}{% endif %}{% endif %}{% assign zp_entity_footer_scripts = nil %}{% if zp_alternative_entity_present == true %}{% assign zp_entity_scripts = '' | append: zp_alternative_entity_metafields[zp_scripts_metafield_key] | split: '|;|~|;|' %}{% assign zp_alternative_footer_scripts = '' | append: zp_entity_scripts[1] | strip %}{% assign zp_entity_scripts = '' %}{% if zp_alternative_footer_scripts.size > 0 %}{% assign zp_replacement_key = '</script' %}{% assign zp_replacement_value = '<\/script' %}<script type="text/template" id="zp-alternative-footer-scripts">{{ zp_alternative_footer_scripts | replace: zp_replacement_key, zp_replacement_value }}</script>{% endif %}{% assign zp_alternative_footer_scripts = nil %}<script>!function(){var t=!1;window.ZipifyPages.SplitTest.onEntityContentInjected(function(){if(!t){t=!0;var e=document.querySelector("#zp-"+this.currentEntity()+"-footer-scripts");if(e){var n=document.getElementsByTagName("body")[0];this.prepareAndAppendHTML(e.innerHTML,n)}}})}();</script>{% endif %}{% if zp_shop_metafields['localization'].type == 'json' %}{% assign zp_localization = zp_shop_metafields['localization'].value %}{% else %}{% assign zp_localization = zp_shop_metafields['localization'] %}{% endif %}<script id="published-popup-template" type="text/template"><form class="zpa-crm-popup-form" id="zpa-crm-form" action="/subscribe" method="post" data-zpa-crm-form><header class="zpa-crm-popup-header"><button class="zpa-popup-btn-close" type="button"aria-label="Close Navigation" data-close-btn><svg class="zpa-popup-btn-close-icon" width="34" height="34" data-name="btn-close"><use href="#btn-close"></use></svg></button></header><div class="zpa-crm-popup-body" data-popup-content></div></form></script> <script id="crm-button-popup" type="text/template">{! if (progress_image_option == 1)!}<div class="zpa-crm-popup-progressbar-wrap"><img class="zpa-crm-popup-img-progress zpa-img-fluid zpa-center-block" src="{!= progress_image.src !}"></div>{! !}<h1 class="zpa-crm-popup-title" style="color:{!= form_name_color !}; font-family:{!= form_name_font_family !}; font-size:{!= form_name_font_size !};">{!= form_name !}</h1><div class="zpa-crm-popup-form-container">{! if (form_image_option == 1) !}<div class="zpa-crm-popup-image-box"><img class="zpa-crm-popup-img-asside zpa-img-fluid zpa-center-block" style="border-radius:{!= form_image_shape !};" src="{!= form_image.src !}"></div>{! !}<div class="zpa-crm-popup-form-box"><div class="zpa-crm-popup-field-wrap">{! if (first_name_option == 1) !}<label class="zpa-crm-popup-field-wrap zpa-crm-popup-field-first-name zpa-text-field zpa-offset-bottom-xs"><input class="zpa-text-field__input" type="text" name="first_name" data-user-fname data-zp-input><span class="zpa-text-field__label">{{ zp_localization['form']['first_name'] | strip | escape | default: "First Name" | replace: '\', '&#92;' }}</span></label>{! !}{! if (last_name_option == 1) !}<label class="zpa-crm-popup-field-wrap zpa-crm-popup-field-last-name zpa-text-field zpa-offset-bottom-xs"><input class="zpa-text-field__input" type="text" name="last_name" data-user-lname data-zp-input><span class="zpa-text-field__label">{{ zp_localization['form']['last_name'] | strip | escape | default: "Last Name" | replace: '\', '&#92;' }}</span></label>{! !}</div><label class="zpa-text-field"><input class="zpa-text-field__input" type="email" name="email" required data-user-email data-zp-input><span class="zpa-text-field__label">{{ zp_localization['form']['email'] | strip | escape | default: "Email Address" | replace: '\', '&#92;' }}</span></label>{! if (phone_option == 1) !}<label class="zpa-text-field zpa-crm-popup-field-phone"><input class="zpa-text-field__input" name="phone" type="tel" data-user-phone data-zp-input><span class="zpa-text-field__label">{{ zp_localization['form']['phone_number'] | strip | escape | default: "Phone Number" | replace: '\', '&#92;' }}</span></label>{! !}</div></div>{! if (gdpr_status != 'disable') !}<div>{! if (gdpr_status == 'enable_with_checkbox') !}<label class="zpa-control zpa-control--top zpa-control-checkbox zpa-offset-bottom-xs hidden" data-gdpr-checkbox-section><input class="zpa-control__field" type="checkbox" name="gdpr" data-gdpr-checkbox><span class="zpa-control__indicator zpa-offset-right-xs"></span><span class="zpa-fsize-xs" data-gdpr-checkbox-text></span></label>{! !}{! if (gdpr_status == 'enable_without_checkbox') !}<p class="zpa-fsize-xs zpa-text-center zpa-offset-top-none zpa-offset-bottom-xs hidden" data-gdpr-message-section></p>{! !}</div>{! !}<button class="zpa-crm-button-checkout zpa-btn-custom" type="submit" style="color:{!= checkout_button_color !}; background-color: {!= checkout_button_background_color !}" data-checkout-btn data-zpa-submit-button>{!= checkout_button_caption !}</button>{! if (comment_text_status == 1) !}<p class="zpa-crm-popup-description" style="color: {!= comment_text_font_color !}" data-comment-text-status>{!= comment_text !}</p>{! !}{! if (banner_image_option == 1)!}<img class="zpa-crm-popup-img-footer zpa-img-fluid zpa-center-block" src="{!= banner_image.src !}">{! !}</script>{% assign zp_localization = nil %}{% if zp_show_back_to_top_button %}<button type="button" title="Back To Top" class="zpa-button--back-to-top" data-back-to-top-widget><svg viewBox="0 0 24 24"><path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"></path></svg></button>{% endif %}{% assign zp_app_integrations = '' | append: zp_shop_metafields['appintegrations'] | split: ',' %}{% unless ocuapp %}{% if zp_app_integrations contains 'oneclickupsellnative' %}{% capture zp_integrations_include %}{% render 'oneclickupsellapp-extend' %}{% endcapture %}{% unless zp_integrations_include contains 'Could not find asset snippets/oneclickupsellapp-extend.liquid' %}{{ zp_integrations_include | strip }}{% endunless %}{% assign zp_integrations_include = '' %}{% endif %}{% if zp_app_integrations contains 'oneclickupsellnative' %}{% capture zp_integrations_include %}{% render 'oneclickupsellapp-theme' %}{% endcapture %}{% unless zp_integrations_include contains 'Could not find asset snippets/oneclickupsellapp-theme.liquid' %}{{ zp_integrations_include | strip }}{% endunless %}{% assign zp_integrations_include = '' %}{% endif %}{% endunless %}{% if zp_load_loox_scripts and shop.metafields.loox %}{{ shop.metafields.loox['global_html_body'] }}{% endif %}{% capture zp_replace_integration %}<zp_additional_integration></zp_additional_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_additional_integration>', '' | replace: '</zp_additional_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{% endif %}