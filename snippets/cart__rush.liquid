{% assign rush_product = all_products[section.settings.rush_product] %}
{% comment %} <p>{{ has_rush_added }}</p> {% endcomment %}
{% if rush_product.metafields.cuddleclones.rush_cart_texts %}
  {% assign rushCreationLabel = rush_product.metafields.cuddleclones.rush_cart_texts.value %}
{% endif %}

{% comment %} <p>======={{ rushCreationLabel }}</p> {% endcomment %}

<div class="cart-rush">
  <div class="cart-rush--option">
    <span class="cart-rush--variant"><input
        type="radio"
        name="cart-rush-variant"
        value="none"
        {% unless has_rush_added %}
        checked{% endunless %}><i>
        <!-- fake input -->
      </i>
    </span>
    <label>{{ rushCreationLabel.rush_creation_label }}: 8 Weeks</label>
    <span class="cart-rush--price" data-variant-price="0"></span>
  </div>
  {% for variant in rush_product.variants %}
    {% assign variant_meta = variant.metafields.cuddleclones.highlighted.value %}
    <div
      class="cart-rush--option {% if forloop.last == true %}last-option{% endif %}"
      {% if variant_meta !='' %}
      style="display:{{variant_meta.display}}; color:{{variant_meta.color}}; font-weight: {{variant_meta.font-weight}}"
      {% endif %}>
      <span class="cart-rush--variant">
        <input
          type="radio"
          name="cart-rush-variant"
          value="{{variant.id}}"
          {% if has_rush_added and rush_variant==variant.id %}
          checked{%endif%} />
        <i>
          <!-- fake input -- -->
        </i>
      </span>
      <label>{{ rushCreationLabel.rush_creation_label }}:
        {{ variant.title }}</label>
      <span class="cart-rush--price" data-variant-price="{{variant.price | money_without_currency }}">
        {% if variant.compare_at_price > variant.price %}
          <s>{{ variant.compare_at_price | money }}</s>
        {% endif %}
        {{ variant.price | money }}
      </span>
    </div>
  {% endfor %}
  {% if section.settings.rush_disclaimer != blank %}
    <div class="rush-disclaimer {% if has_rush_added %}{% else %}hide{% endif %}">{{ rushCreationLabel.rush_disclaimer_text }}</div>
  {% endif %}
</div>