{%- comment -%}
  Updated: 01/22/2025
  This file is system-generated and should not be modified. We reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost
  Please, read through this article to make sure that your customizations will be preserved: https://help.zipify.com/en/articles/877075-zipify-pages-advanced-work-with-buy-box-blocks
{%- endcomment -%}{% assign zp_rcprewso = false %}{% if renderscope == 'metafield' %}{% assign zp_rprfrmf = true %}{% assign zp_rprfrtg = false %}{% else %}{% assign zp_rprfrmf = false %}{% assign zp_rprfrtg = true %}{% endif %}{% assign zp_dynamic_product_section_excluded = true %}{% assign zp_dynamic_product_section_markup_excluded = true %}{% if zp_rprfrmf %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_entity_products = zp_current_entity_metafields['config'].value['products'] %}{% else %}{% assign zp_entity_products = zp_current_entity_metafields['config']['products'] %}{% endif %}{% if zp_product_page_with_zp_layout %}{% for zp_part in zp_entity_products %}{% assign zp_current_product_settings = zp_part | last %}{% if zp_current_product_settings['vtp'] == 'dynamicproductsection' %}{% assign zp_dynamic_product_section_excluded = false %}{% break %}{% endif %}{% endfor %}{% endif %}{% else %}{% assign zp_entity_products = zp_current_entity_content | split: '<zpdproduct ' %}{% assign zp_current_entity_content = '' %}{% endif %}{% if zp_product_image_size == nil %}{% assign zp_current_product_image_size = '1080x' %}{% else %}{% assign zp_current_product_image_size = zp_product_image_size %}{% endif %}{% assign zp_empty_image_src = 'no-image.gif' | img_url: zp_current_product_image_size %} 
{%- for zp_part in zp_entity_products -%}{% if zp_rprfrmf %}{% assign zp_pblkmkr = zp_part | first %}{% assign zp_current_product_settings = zp_part | last %}{% assign zp_product_content_parts_size = 2 %}{% else %}{% assign zp_product_content = zp_part | append: ' ' | split: '</zpdproduct>' %}{% assign zp_product_content_parts_size = zp_product_content | size %}{% endif %}{%- if zp_product_content_parts_size < 2 -%}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_part %}{%- else -%}{% assign zp_pshcren = false %}{% assign zp_display_product_markup = false %}{% assign zp_product_as_current_entity = false %}{% assign zp_product_handle = '' %}{% assign zp_phndents = '' | split: ',' %}{% assign zp_selected_variants = '' %}{% assign zp_selected_variants_list = '' %}{% assign zp_product_default_quantity = '1' %}{% assign zp_product_show_quantity = true %}{% assign zp_product_link_type = 'cart' %}{% assign zp_product_link_types = nil %}{% assign zp_redirect_product_url = routes.cart_url %}{% assign zp_product_type = 'base' %}{% assign zp_patcm = 'Product has been added to cart' %}{% assign zp_pbtnlt = '_self' %}{% assign zp_product_image_type = 'product' %}{% assign zp_product_discount_data = '' %}{% assign zp_product_block_id = '' %}{% assign zp_product_element_id = nil %}{% assign zp_bpelid = nil %}{% assign zp_patccl = '' %}{% assign zp_pmsgwcl = '' %}{% assign zp_pqslcl = '' %}{% assign zp_pvrntslv = 'vt' %}{% assign zp_product_image_sizes_data = '' %}{% assign zp_product_form_classes = '' %}{% assign zp_product_view_type = 'dynamicbuybox' %}{% assign zp_pstprcnt = '' %}{% assign zp_pstcpprcnt = '' %}{% assign zp_pprvimg = false %}{% assign zp_product_load_main_image = true %}{% assign zp_product_show_quantity_label = true %}{% assign zp_pvrntslcl = nil %}{% assign zp_pshwvrntsllbl = nil %}{% assign zp_pvrntslofcl = nil %}{% assign zp_product_unit_price_tag_included = false %}{% assign zp_product_image_lazy_load = false %}{% assign zp_product_calculate_bundle_price = false %}{% assign zp_ugpimgcnt = false %}{% assign zp_product_sold_out_view_enabled = false %}{% assign zp_upextppr = false %}{% assign zp_pvrntslwrcl = '' %}{% assign zp_pvrntslwrclen = false %}{% assign zp_popstng = nil %}{% assign zp_product_reviews_source = nil %}{% assign zp_product_reviews_data = nil %}{% assign zp_pshcrid = nil %}{% assign zp_pshcrmfid = nil %}{% assign zp_pshcritid = nil %}{% assign zp_epimgln = true %}{% assign zp_shpwtginc = false %}{% assign zp_pfpmtcp = '' %}{% assign zp_bndblen = false %}{% assign zp_csblen = false %}{% assign zp_cscfgk = 'crossSell' %}{% assign zp_pbtnsjsndt = nil %}{% assign zp_pvrnjsndt = nil %}{% assign zp_pimgjsndt = nil %}{% assign zp_pimgdt = nil %}{% assign zp_prdlpsbcren = false %}{% assign zp_pcrdt = nil %}{% assign zp_pcintrcsbcr = false %}{% assign zp_phnditr = 0 %}{% assign zp_pgrtpl = '' %}{% assign zp_pgrid = nil %}{% assign zp_uspgrt = false %}{% assign zp_pgrdprscnt = '' %}{% assign zp_pgrdclhnd = '' %}{% assign zp_pgrdclen = false %}{% assign zp_pgrdcice = false %}{% assign zp_pgrdclprpg = 10 %}{% assign zp_pbtnstgs = nil %}{% assign zp_product_title = 'Product Title' %}{% assign zp_pttpra = zp_product_title %}{% assign zp_product_description = 'Product Description' %}{% if zp_rprfrmf %}{% assign zp_product_settings_keys = zp_current_product_settings %}{% capture zp_dynamic_product_block_closed_tag %}</zpdprdct_{{ zp_pblkmkr }}>{% endcapture %}{% assign zp_dynamic_product_block_schema_marker = '<zpprdmrkp></zpprdmrkp>' | append: zp_dynamic_product_block_closed_tag %}{% if zp_current_entity_content contains zp_dynamic_product_block_schema_marker %}{% assign zp_display_product_markup = true %}{% else %}{% assign zp_display_product_markup = false %}{% endif %}{% else %}{% if zp_product_content[0] contains '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_product_content[0] = zp_product_content[0] | remove: '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_display_product_markup = true %}{% else %}{% assign zp_display_product_markup = false %}{% endif %}{% assign zp_product_content_parts = zp_product_content[0] | append: ' ' | split: '>' %}{% assign zp_product_settings = zp_product_content_parts[0] %}{% assign zp_product_settings_tmp = zp_product_settings | append: '>' %}{% assign zp_product_content_parts = zp_product_content_parts | join: '>' | remove_first: zp_product_settings_tmp %}{% assign zp_product_settings = ' ' | append: zp_product_settings | split: ' data-' %}{% assign zp_product_settings_keys = '' %}{% assign zp_product_settings_values = '' %}{% for zp_product_setting in zp_product_settings %}{% assign zp_product_setting_parts = zp_product_setting | split: '=' %}{% if zp_product_setting_parts.size < 2 %}{% continue %}{% endif %}{% assign zp_prstnk = '' | append: zp_product_setting_parts[0] | strip %}{% if zp_prstnk.size < 1 %}{% continue %}{% endif %}{% assign zp_product_settings_keys = zp_product_settings_keys | append: zp_prstnk | append: '__zp1502150400__' %}{% assign zp_product_setting_size = zp_product_setting | size %}{% assign zp_product_setting_key_size = '' | append: zp_product_setting_parts[0] | size | plus: 1 %}{% assign zp_prstnvl = '' | append: zp_product_setting | slice: zp_product_setting_key_size, zp_product_setting_size | strip %}{% assign zp_product_setting_value_last_index = zp_prstnvl | size | minus: 1 %}{% assign zp_product_setting_value_first_letter = zp_prstnvl | first %}{% if zp_product_setting_value_first_letter == '"' or zp_product_setting_value_first_letter == "'" %}{% assign zp_prstnvl = zp_prstnvl | slice: 1, zp_product_setting_value_last_index %}{% endif %}{% assign zp_product_setting_value_last_index = zp_prstnvl | size | minus: 1 %}{% assign zp_product_setting_value_last_letter = zp_prstnvl | last %}{% if zp_product_setting_value_last_letter == '"' or zp_product_setting_value_last_letter == "'" %}{% assign zp_prstnvl = zp_prstnvl | slice: 0, zp_product_setting_value_last_index %}{% endif %}{% assign zp_product_settings_values = zp_product_settings_values | append: zp_prstnvl | append: '__zp1502150400__' %}{% endfor %}{% assign zp_product_settings_keys = zp_product_settings_keys | split: '__zp1502150400__' %}{% assign zp_product_settings_values = zp_product_settings_values | split: '__zp1502150400__' %}{% assign zp_product_settings = nil %}{% assign zp_product_settings_tmp = nil %}{% assign zp_product_setting_parts = nil %}{% endif %}{% for zp_product_setting_key_data in zp_product_settings_keys %}{% if zp_rprfrmf %}{% assign zp_prstnk = zp_product_setting_key_data | first %}{% assign zp_unstringified_product_setting_keys = 'btns,srtgs,vrnts,imgs,pcrls' | split: ',' %}{% if zp_unstringified_product_setting_keys contains zp_prstnk %}{% assign zp_prstnvl = zp_product_setting_key_data | last %}{% else %}{% assign zp_prstnvl = zp_product_setting_key_data | last | strip %}{% endif %}{% assign zp_unstringified_product_setting_keys = nil %}{% else %}{% assign zp_prstnk = zp_product_setting_key_data %}{% assign zp_prstnvl = '' | append: zp_product_settings_values[forloop.index0] | strip %}{% endif %}{% case zp_prstnk %}{% when 'handle' %}{% assign zp_phndents = zp_prstnvl | split: ':|~|:' %}{% when 'slctvrnt' %}{% assign zp_selected_variants = zp_prstnvl %}{% assign zp_selected_variants_list = zp_prstnvl %}{% when 'qty' %}{% assign zp_product_default_quantity = zp_prstnvl %}{% when 'shwqty' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_product_show_quantity = true %}{% else %}{% assign zp_product_show_quantity = false %}{% endif %}{% when 'btnltp' %}{% assign zp_product_link_type = zp_prstnvl | replace: '\', '&bsol;' %}{% when 'btns' %}{% assign zp_pbtnstgs = zp_prstnvl %}{% assign zp_plnktpsstr = '' %}{% for zp_pstgv in zp_pbtnstgs %}{% assign zp_plnktpsstr = zp_plnktpsstr | append: zp_pstgv['tp'] | append: ',' %}{% endfor %}{% assign zp_product_link_types = zp_plnktpsstr | split: ',' %}{% when 'dcbe' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_use_product_form_action = true %}{% endif %}{% when 'url' %}{% assign zp_redirect_product_url = zp_prstnvl | replace: '&amp;', '&' | url_escape | replace: '%23', '#' | replace: '%25', '%' %}{% when 'btncpt' %}{% assign zp_product_button_caption = zp_prstnvl | replace: '\', '&bsol;' %}{% when 'stkbtncpt' %}{% assign zp_product_sticky_button_caption = zp_prstnvl %}{% when 'type' %}{% assign zp_product_type = zp_prstnvl %}{% when 'addcrtmsg' %}{% assign zp_patcm = zp_prstnvl | replace: '\', '&bsol;' | url_escape %}{% when 'btnelt' %}{% assign zp_pbtnlt = zp_prstnvl %}{% when 'imgtp' %}{% assign zp_product_image_type = zp_prstnvl %}{% when 'dscntdt' %}{% assign zp_product_discount_data = zp_prstnvl %}{% when 'bid' %}{% assign zp_product_block_id = zp_prstnvl %}{% when 'eid' %}{% assign zp_product_element_id = zp_prstnvl %}{% assign zp_bpelid = zp_prstnvl %}{% when 'gid' %}{% assign zp_pgrid = zp_prstnvl %}{% when 'crtmsgcls' %}{% assign zp_patccl = zp_prstnvl | url_escape %}{% when 'msgwrpcl' %}{% assign zp_pmsgwcl = zp_prstnvl | url_escape %}{% when 'vrntcls' %}{% assign zp_pvrntslcl = zp_prstnvl | url_escape %}{% when 'vrntoffcls' %}{% assign zp_pvrntslofcl = zp_prstnvl | url_escape %}{% when 'vrntwcls' %}{% assign zp_pvrntslwrcl = zp_prstnvl | url_escape %}{% when 'qtycls' %}{% assign zp_pqslcl = zp_prstnvl %}{% when 'vrntsv' %}{% assign zp_pvrntslv = zp_prstnvl %}{% when 'imgszs' %}{% assign zp_product_image_sizes_data = zp_prstnvl %}{% when 'frmcls' %}{% assign zp_product_form_classes = zp_prstnvl %}{% when 'vtp' %}{% assign zp_product_view_type = zp_prstnvl %}{% when 'stcprc' %}{% assign zp_pstprcnt = zp_prstnvl %}{% when 'stccprprc' %}{% assign zp_pstcpprcnt = zp_prstnvl %}{% when 'optst' %}{% assign zp_popstng = zp_prstnvl %}{% when 'srtgs' %}{% assign zp_srtjsndt = '' %}{% assign zp_srtaispr = false %}{% for zp_pstgv in zp_prstnvl %}{% if zp_pstgv['rtgsrc'] != blank %}{% if zp_srtaispr %}{% assign zp_srtispr = ',' %}{% else %}{% assign zp_srtispr = '' %}{% endif %}{% assign zp_srtjsndt = zp_srtjsndt | append: zp_srtispr | append: '{' %}{% assign zp_srtaispr = true %}{% assign zp_srtaipspr = false %}{% for zp_pstgnv in zp_pstgv %}{% if zp_srtaipspr %}{% assign zp_srtipspr = ',' %}{% else %}{% assign zp_srtipspr = '' %}{% endif %}{% assign zp_srtaipspr = true %}{% assign zp_srstgntk = zp_pstgnv | first | json %}{% assign zp_srstgntv = zp_pstgnv | last | json %}{% assign zp_srtjsndt = zp_srtjsndt | append: zp_srtipspr | append: zp_srstgntk | append: ':' | append: zp_srstgntv %}{% endfor %}{% assign zp_srtjsndt = zp_srtjsndt | append: '}' %}{% endif %}{% endfor %}{% assign zp_product_reviews_data = '[' | append: zp_srtjsndt | append: ']' %}{% when 'rtgsrc' %}{% assign zp_product_reviews_source = zp_prstnvl %}{% when 'imgc' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_ugpimgcnt = true %}{% endif %}{% when 'imgprld' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_pprvimg = true %}{% endif %}{% when 'mimgld' %}{% if zp_prstnvl == 'false' or zp_prstnvl == '0' %}{% assign zp_product_load_main_image = false %}{% endif %}{% when 'shwqtylb' %}{% if zp_prstnvl == 'false' or zp_prstnvl == '0' %}{% assign zp_product_show_quantity_label = false %}{% endif %}{% when 'shwvrntlb' %}{% if zp_prstnvl == 'false' or zp_prstnvl == '0' %}{% assign zp_pshwvrntsllbl = false %}{% else %}{% assign zp_pshwvrntsllbl = true %}{% endif %}{% when 'enpimglg' %}{% if zp_prstnvl == 'false' or zp_prstnvl == '0' %}{% assign zp_epimgln = false %}{% endif %}{% when 'crntentt' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_product_as_current_entity = true %}{% assign zp_phndents = '_cprdct_' | split: ',' %}{% endif %}{% when 'unprcincl' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_product_unit_price_tag_included = true %}{% endif %}{% when 'lzld' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_product_image_lazy_load = true %}{% endif %}{% when 'sldtv' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_product_sold_out_view_enabled = true %}{% endif %}{% when 'bndlprc' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_product_calculate_bundle_price = true %}{% endif %}{% when 'extpupd' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_upextppr = true %}{% endif %}{% when 'spwincl' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_shpwtginc = true %}{% endif %}{% when 'spcen' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_pshcren = true %}{% endif %}{% when 'rcsbcr' %}{% if zp_prstnvl == 'true' %}{% assign zp_pcintrcsbcr = true %}{% endif %}{% when 'spcid' %}{% assign zp_pshcrid = zp_prstnvl %}{% when 'spcpid' %}{% assign zp_pshcrmfid = zp_prstnvl %}{% when 'spsrcs' %}{% assign zp_pcrshsrs = zp_prstnvl %}{% when 'dfttl' %}{% assign zp_product_title = zp_prstnvl %}{% when 'dfbd' %}{% assign zp_product_description = zp_prstnvl %}{% when 'crsslls' %}{% assign zp_csblen = true %}{% when 'bndl' %}{% assign zp_cscfgk = 'bundle' %}{% assign zp_bndblen = true %}{% assign zp_phndents = '_bundle_' | split: ',' %}{% when 'vrnts' %}{% assign zp_pvrnjsndt = zp_prstnvl | json %}{% when 'imgs' %}{% assign zp_pimgjsndt = zp_prstnvl | json %}{% assign zp_pimgdt = zp_prstnvl %}{% when 'pcrls' %}{% assign zp_pcrdt = zp_prstnvl %}{% when 'chandle' %}{% assign zp_pgrdclhnd = zp_prstnvl %}{% assign zp_pgrdclen = true %}{% when 'cicrent' %}{% assign zp_pgrdcice = true %}{% assign zp_pgrdclen = true %}{% when 'cppp' %}{% assign zp_pgrdclprpg = 0 | plus: zp_prstnvl %}{% endcase %}{% endfor %}{% if zp_product_link_types != blank %}{% for zp_link_type in zp_product_link_types %}{% if zp_shw_vrntsslctr_lnktps contains zp_link_type %}{% assign zp_product_link_type = zp_link_type %}{% break %}{% endif %}{% endfor %}{% endif %}{% if zp_product_link_type == 'cart' %}{% assign zp_redirect_product_url = routes.cart_url %}{% endif %}{% if zp_pvrntslwrcl.size > 0 %}{% assign zp_pvrntslwrcl = 'zp ' | append: zp_pvrntslwrcl %}{% assign zp_pvrntslwrclen = true %}{% endif %}{% if zp_rprfrtg and zp_popstng != empty %}{% assign zp_popstng = zp_popstng | replace: '&quot;', '"' %}{% endif %}{% if zp_pgrid != blank %}{% assign zp_pprsngtp = 'multiproduct' %}{% capture zp_pgrtplotg %}<zppgrt_{{ zp_pgrid }}>{% endcapture %}{% capture zp_pgrtplctg %}</zppgrt_{{ zp_pgrid }}>{% endcapture %}{% assign zp_pgrtplprt = zp_current_entity_content | split: zp_pgrtplotg %}{% if zp_pgrtplprt.size > 1 %}{% assign zp_pgrtplprt = zp_pgrtplprt | last | split: zp_pgrtplctg %}{% if zp_pgrtplprt.size > 1 %}{% assign zp_pgrtpl = zp_pgrtplprt | first %}{% assign zp_pgrtplprt = nil %}{% if zp_pgrtpl.size > 0 %}{% assign zp_uspgrt = true %}{% assign zp_tentcnt = zp_current_entity_content %}{% assign zp_current_entity_content = zp_pgrtpl %}{% else %}{% continue %}{% endif %}{% else %}{% continue %}{% endif %}{% else %}{% continue %}{% endif %}{% else %}{% assign zp_pprsngtp = 'product' %}{% endif %}{% if zp_pgrdclen %}{% if zp_pgrdcice %}{% assign zp_pgrdcl = zp_current_entity %}{% else %}{% assign zp_pgrdcl = collections[zp_pgrdclhnd] %}{% endif %}{% assign zp_pgrdclhndsz = '' | append: zp_pgrdcl.id %}{% if zp_pgrdclhndsz.size > 0 %}{% paginate zp_pgrdcl.products by zp_pgrdclprpg %}{% assign zp_phndents = zp_pgrdcl.products %}{% endpaginate %}{% endif %}{% endif %}{% for zp_phndent in zp_phndents %}{% assign zp_first_selected_variant = nil %}{% assign zp_first_available_variant = nil %}{% assign zp_current_selected_variant = nil %}{% assign zp_product_schema_variant = nil %}{% assign zp_any_product_variant_available = false %}{% assign zp_current_product_price = nil %}{% assign zp_product_init_script = '' %}{% assign zp_product_additional_integrations = '' %}{% assign zp_pfoptg = '' %}{% assign zp_pfcstg = '' %}{% assign zp_unpcls = ' hidden' %}{% assign zp_unpbwrcls = '' %}{% assign zp_unplnbcls = ' hidden' %}{% assign zp_unavailable_discount_class = '' %}{% assign zp_pvrntcntr = '' %}{% assign zp_pqtycntr = '' %}{% assign zp_product_button_type = 'button' %}{% assign zp_pbtnintgr = '' %}{% assign zp_pbtnmsg = '' %}{% assign zp_product_image = zp_empty_image_src %}{% assign zp_product_image_data_attrs = '' %}{% assign zp_product_wrapper_selector = '' %}{% assign zp_formatted_product_price = nil %}{% assign zp_formatted_product_compare_at_price = nil %}{% assign zp_unqtycls = ' hidden' %}{% assign zp_product_button_text = 'products.product.unavailable' | t %}{% if zp_product_button_text contains 'Translation missing' or zp_product_button_text contains 'translation missing' %}{% assign zp_product_button_text = "Unavailable" %}{% endif %}{% assign zp_product_sticky_button_text = 'products.product.unavailable' | t %}{% if zp_product_sticky_button_text contains 'Translation missing' or zp_product_sticky_button_text contains 'translation missing' %}{% assign zp_product_sticky_button_text = "Unavailable" %}{% endif %}{% assign zp_sale_price = nil %}{% assign zp_product_sale_price = nil %}{% assign zp_product_saved_amount = nil %}{% assign zp_product_discount_amount = nil %}{% assign zp_formatted_product_sale_price = nil %}{% assign zp_formatted_product_saved_amount = nil %}{% assign zp_formatted_product_discount_amount = nil %}{% assign zp_product_discount_amount_with_suffix = nil %}{% assign zp_pimgldcnt = '' %}{% assign zp_current_product_custom_variants = nil %}{% assign zp_product_unit_price = '' %}{% assign zp_pupwrcl = ' hidden' %}{% assign zp_acspct = 0 %}{% assign zp_uncswrcl = ' hidden' %}{% assign zp_puplbl = '' %}{% assign zp_pupblk = '' %}{% assign zp_unpfptrmscl = 'hidden' %}{% assign zp_product_review_wrapper_classes = 'disabled hidden' %}{% assign zp_product_review_count = nil %}{% assign zp_product_review_rating = nil %}{% assign zp_product_review_rating_text = '' %}{% assign zp_cscfg = nil %}{% assign zp_ucprart = false %}{% assign zp_prdscdfwcl = 'hidden' %}{% if zp_use_current_entity_markup %}{% assign zp_product_custom_title = zp_current_entity.title %}{% assign zp_product_custom_description = zp_current_entity.description %}{% assign zp_product_custom_image = zp_current_product_settings['schm']['img'] %}{% else %}{% if zp_rprfrmf %}{% assign zp_crpshcfg = zp_current_product_settings['schm'] %}{% else %}{% assign zp_crpshcfg = zp_current_entity_schema_config[zp_product_block_id] %}{% endif %}{% assign zp_product_custom_title = zp_crpshcfg['title'] %}{% assign zp_product_custom_description = zp_crpshcfg['descr'] %}{% assign zp_product_custom_image = zp_crpshcfg['img'] %}{% assign zp_crpshcfg = nil %}{% endif %}{% if zp_product_view_type == 'dynamicproductsection' %}{% assign zp_use_product_variant_from_url = true %}{% assign zp_product_entity_type = 'main' %}{% else %}{% assign zp_use_product_variant_from_url = false %}{% assign zp_product_entity_type = 'alt' %}{% endif %}{% if zp_product_link_types != blank %}{% assign zp_shw_vrntsslctr = false %}{% for zp_link_type in zp_product_link_types %}{% if zp_shw_vrntsslctr_lnktps contains zp_link_type %}{% assign zp_shw_vrntsslctr = true %}{% break %}{% endif %}{% endfor %}{% elsif zp_shw_vrntsslctr_lnktps contains zp_product_link_type %}{% assign zp_shw_vrntsslctr = true %}{% else %}{% assign zp_shw_vrntsslctr = false %}{% endif %}{% assign zp_show_variants_selectors = zp_shw_vrntsslctr %}{% assign zp_enable_subscription_widget = zp_shw_vrntsslctr %}{% assign zp_dshpwgt = true %}{% assign zp_dshpwgtbpr = false %}{% assign zp_product_iterator = zp_product_iterator | plus: 1 %}{% assign zp_product_selector = zp_main_product_selector | append: zp_product_iterator %}{% assign zp_product_wrapper_selector = zp_product_selector | append: zp_main_product_selector_suffix %}{% assign zp_main_product_available = true %}{% if zp_shw_vrntsslctr %}{% assign zp_unbndpiwrcl = '' %}{% else %}{% assign zp_unbndpiwrcl = 'hidden' %}{% endif %}{% assign zp_adpinstg = '' %}{% assign zp_padclcnt = '' %}{% assign zp_pshcrmtpl = nil %}{% assign zp_pshcrttpl = nil %}{% assign zp_pshcrm = '' %}{% assign zp_pshcrt = '' %}{% assign zp_pshcrjsn = '' %}{% assign zp_lpscrwgc = '' %}{% assign zp_aptscrwgc = '' %}{% assign zp_slscrwgc = '' %}{% assign zp_rcscrwgc = '' %}{% if zp_integrate_with_recharge or zp_pcintrcsbcr %}{% assign zp_pscrprrf = true %}{% else %}{% assign zp_pscrprrf = false %}{% endif %}{% if zp_product_as_current_entity or zp_pgrdclen %}{% assign zp_phndlsz = 1 %}{% elsif zp_bndblen %}{% assign zp_phndlsz = 0 %}{% else %}{% assign zp_phndlsz = zp_phndent.size %}{% endif %}{% assign zp_phnditr = zp_phnditr | plus: 1 %}{% if zp_pprsngtp == 'multiproduct' and zp_product_element_id != blank %}{% assign zp_product_element_id = '' | append: zp_bpelid | append: '_' | append: zp_phnditr %}{% assign zp_pshcrmfid = zp_product_element_id %}{% assign zp_pshcritid = zp_product_element_id %}{% endif %}{% assign zp_pphpr = '#' %}{% assign zp_purpr = '#' %}{% assign zp_pttpr = '' %}{% assign zp_pprid = nil %}{% assign zp_product_title = 'products.product.unavailable' | t %}{% if zp_product_title contains 'Translation missing' or zp_product_title contains 'translation missing' %}{% assign zp_product_title = "Unavailable" %}{% endif %}{% assign zp_product_description = 'products.product.unavailable' | t %}{% if zp_product_description contains 'Translation missing' or zp_product_description contains 'translation missing' %}{% assign zp_product_description = "Unavailable" %}{% endif %}{% assign zp_asmpbdpf = false %}{% assign zp_ansmpbndprav = false %}{% if zp_phndlsz < 1 and zp_rprfrmf %}{% assign zp_cscntprts = zp_current_product_settings['bndl'] %}{% for zp_cscntprt in zp_cscntprts %}{% assign zp_bundle_product_settings = zp_cscntprt | last %}{% assign zp_bundle_product_handle = zp_bundle_product_settings['handle'] | strip %}{% if zp_bundle_product_handle.size < 1 %}{% continue %}{% endif %}{% assign zp_bundle_product = all_products[zp_bundle_product_handle] %}{% assign zp_bdphs = '' | append: zp_bundle_product.id | size %}{% if zp_bdphs > 0 %}{% assign zp_asmpbdpf = true %}{% endif %}{% if zp_bundle_product.available == true %}{% assign zp_bundle_product_use_product_variant_from_url = false %}{% assign zp_bundle_product_selected_variants = zp_bundle_product_settings['slctvrnt'] | strip %}{% assign zp_selected_variants = zp_bundle_product_selected_variants %}{% assign zp_selected_variants_list = zp_bundle_product_selected_variants %}{% if zp_bundle_product_product_custom_variants == blank %}{% assign zp_bundle_product_product_custom_variants = zp_bundle_product.variants %}{% endif %}{% if zp_bundle_product_selected_variants.size < 1 %}{% assign zp_bundle_product_selected_variants = zp_bundle_product_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% endif %}{% if zp_bundle_product_use_product_variant_from_url %}{% assign zp_url_selected_variant_id = '' | append: zp_bundle_product.selected_variant.id %}{% else %}{% assign zp_url_selected_variant_id = '' %}{% endif %}{% assign zp_filtered_selected_variants = '' %}{% assign zp_bundle_product_first_selected_variant = nil %}{% assign zp_current_selected_variant = nil %}{% assign zp_bundle_product_any_product_variant_available = false %}{% for zp_prd_variant in zp_bundle_product_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_bundle_product_use_product_variant_from_url and zp_url_selected_variant_id == zp_variant_id and zp_bundle_product_selected_variants contains zp_url_selected_variant_id %}{% assign zp_bundle_product_first_selected_variant = zp_prd_variant %}{% assign zp_current_selected_variant = zp_prd_variant %}{% endif %}{% if zp_bundle_product_selected_variants contains zp_variant_id %}{% assign zp_filtered_selected_variants = zp_filtered_selected_variants | append: zp_variant_id | append: ',' %}{% if zp_bundle_product_first_selected_variant == nil %}{% assign zp_bundle_product_first_selected_variant = zp_prd_variant %}{% endif %}{% if zp_prd_variant.available %}{% assign zp_bundle_product_any_product_variant_available = true %}{% endif %}{% endif %}{% endfor %}{% assign zp_bundle_product_selected_variants = zp_filtered_selected_variants | split: ',' %}{% if zp_bundle_product_selected_variants.size < 1 %}{% assign zp_bundle_product_selected_variants = zp_bundle_product_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% assign zp_bundle_product_any_product_variant_available = zp_bundle_product.available %}{% if zp_bundle_product_use_product_variant_from_url and zp_bundle_product_selected_variants contains zp_url_selected_variant_id %}{% assign zp_bundle_product_first_selected_variant = zp_bundle_product.selected_variant %}{% assign zp_current_selected_variant = zp_bundle_product.selected_variant %}{% else %}{% assign zp_bundle_product_first_selected_variant = zp_bundle_product_product_custom_variants | first %}{% endif %}{% endif %}{% unless zp_bundle_product_first_selected_variant.available %}{% assign zp_bundle_product_first_available_variant = nil %}{% for zp_prd_variant in zp_bundle_product_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_bundle_product_selected_variants contains zp_variant_id and zp_prd_variant.available %}{% assign zp_bundle_product_first_available_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% unless zp_bundle_product_first_available_variant == nil %}{% assign zp_bundle_product_first_selected_variant = zp_bundle_product_first_available_variant %}{% endunless %}{% assign zp_bundle_product_first_available_variant = nil %}{% endunless %}{% assign zp_variant_id = nil %}{% unless zp_bundle_product_any_product_variant_available %}{% continue %}{% endunless %}{% assign zp_ansmpbndprav = true %}{% assign zp_prstnvl = zp_bundle_product_settings['bndlprc'] | strip %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_product_calculate_bundle_price = true %}{% endif %}{% assign zp_product_discount_data = zp_bundle_product_settings['dscntdt'] | strip %}{% assign zp_popstng = zp_bundle_product_settings['optst'] | strip %}{% assign zp_product_default_quantity = zp_bundle_product_settings['qty'] | strip %}{% assign zp_product_sold_out_view_enabled = false %}{% assign zp_prstnvl = zp_bundle_product_settings['unprcincl'] | strip %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_product_unit_price_tag_included = true %}{% endif %}{% assign zp_product_show_quantity = false %}{% assign zp_ucprart = true %}{% break %}{% endif %}{% endfor %}{% endif %}{% if zp_ansmpbndprav %}{% assign zp_smpbndlprdctwprprcls = '' %}{% else %}{% assign zp_smpbndlprdctwprprcls = 'hidden' %}{% endif %}{% assign zp_cross_sell_product = nil %}{% assign zp_cshndl = '' %}{% assign zp_csslcvrns = '' %}{% assign zp_csslvrnlst = '' %}{% assign zp_csblcid = '' %}{% assign zp_csimgsz = nil %}{% assign zp_csimgszjson = '[]' %}{% assign zp_csshwvrnlbl = nil %}{% assign zp_csvrntslvw = 'vt' %}{% assign zp_csvrntslofscls = nil %}{% assign zp_csdsctam = nil %}{% assign zp_cssvam = nil %}{% assign zp_crcspr = nil %}{% assign zp_crcscapr = nil %}{% assign zp_csslpr = nil %}{% assign zp_fmcspr = nil %}{% assign zp_csimg = 'no-image.gif' | img_url: '280x' %}{% assign zp_fmcsslpr = nil %}{% assign zp_fmcscapr = nil %}{% assign zp_fmcsdsctsvam = nil %}{% assign zp_fmcsdsctam = nil %}{% assign zp_csdsctamwsfx = nil %}{% assign zp_uncscls = ' hidden' %}{% assign zp_csunvbdsctcl = ' hidden' %}{% assign zp_uncsqtycls = ' hidden' %}{% assign zp_csupwrprcls = ' hidden' %}{% assign zp_csimgdatr = nil %}{% assign zp_csvrntcntr = nil %}{% assign zp_csqtycntr = nil %}{% assign zp_csmsgs = nil %}{% assign zp_cross_sell_first_selected_variant = nil %}{% assign zp_current_cross_sell_custom_variants = nil %}{% assign zp_cselid = nil %}{% assign zp_csupblck = nil %}{% assign zp_csuplbl = '' %}{% assign zp_csatnlclscnt = '' %}{% assign zp_csavlbl = false %}{% assign zp_csldmnimg = true %}{% assign zp_csshwqtylbl = true %}{% assign zp_csdftqty = '1' %}{% assign zp_csimgtp = 'product' %}{% assign zp_csdsctdt = '' %}{% assign zp_csshwqty = true %}{% assign zp_cross_sell_view_type = 'crosssellproduct' %}{% assign zp_csprdfnd = false %}{% assign zp_csuntprtginc = false %}{% assign zp_csoptstgs = nil %}{% assign zp_csvrntslctrcls = nil %}{% assign zp_csimglzld = false %}{% assign zp_crvsrc = nil %}{% assign zp_csrvdt = nil %}{% assign zp_csrvwrpcl = 'disabled hidden' %}{% assign zp_csrvrtg = nil %}{% assign zp_csrvcnt = nil %}{% assign zp_csrvrtngtxt = '' %}{% assign zp_csshcrlen = false %}{% assign zp_csshcrlid = nil %}{% assign zp_csshcrlmfid = nil %}{% assign zp_csshcrlmdtpl = nil %}{% assign zp_csshcrltmbtpl = nil %}{% assign zp_csshcrlmds = '' %}{% assign zp_csshcrltmb = '' %}{% assign zp_csshcrljsn = '' %}{% assign zp_csvrntjsndt = nil %}{% assign zp_csimgsjsndt = nil %}{% assign zp_csimgsdt = nil %}{% assign zp_cspttl = 'products.product.unavailable' | t %}{% if zp_cspttl contains 'Translation missing' or zp_cspttl contains 'translation missing' %}{% assign zp_cspttl = "Unavailable" %}{% endif %}{% assign zp_cspdsc = 'products.product.unavailable' | t %}{% if zp_cspdsc contains 'Translation missing' or zp_cspdsc contains 'translation missing' %}{% assign zp_cspdsc = "Unavailable" %}{% endif %}{%- if zp_phndlsz > 0 or zp_ansmpbndprav -%}{% if zp_product_as_current_entity %}{% assign product = zp_current_entity %}{% elsif zp_pgrdclen %}{% assign product = zp_phndent %}{% elsif zp_ansmpbndprav %}{% assign product = zp_bundle_product %}{% assign zp_main_product_available = false %}{% else %}{% assign product = all_products[zp_phndent] %}{% endif %}{% assign zp_product_id_size = '' | append: product.id | size %}{% assign zp_product_found = true %}{% if zp_product_id_size < 1 %}{% assign product = nil %}{% assign zp_product_found = false %}{% endif -%}{% if zp_product_found %}{% assign zp_product_handle = product.handle %}{% assign zp_product_title = product.title %}{% assign zp_pttpra = zp_product_title | escape %}{% assign zp_product_description = product.description %}{% assign zp_pqtyicrtr = 'products.product.quantity.increase' | t: product: product.title %}{% if zp_pqtyicrtr contains 'Translation missing' or zp_pqtyicrtr contains 'translation missing' %}{% assign zp_pqtyicrtr = "Increase quantity" %}{% endif %}{% assign zp_pqtyinpttr = 'products.product.quantity.input_label' | t: product: product.title %}{% if zp_pqtyinpttr contains 'Translation missing' or zp_pqtyinpttr contains 'translation missing' %}{% assign zp_pqtyinpttr = "Quantity" %}{% endif %}{% assign zp_pqtydcrtr = 'products.product.quantity.decrease' | t: product: product.title %}{% if zp_pqtydcrtr contains 'Translation missing' or zp_pqtydcrtr contains 'translation missing' %}{% assign zp_pqtydcrtr = "Decrease quantity" %}{% endif %}{% assign zp_pqtyaatr = ' name="quantity"' %}{% assign zp_crrpi = '<zplpsbcr_' | append: zp_pblkmkr | append: '>' %}{% if zp_current_entity_content contains zp_crrpi %}{% assign zp_prdlpsbcren = true %}{% capture zp_lpscrwgc %}{% render 'loop-subscriptions', type: 'product-widget', product: product %}{% endcapture %}{% endif %}{% assign zp_aptscrwgc = product | json | escape | prepend: '<div class="appstle_stand_alone_selector" data-product-data="' | append: '"></div>' %}{% assign zp_slscrwgc = product | json | escape | prepend: '<div class="sealsubs-target-element" data-product="' | append: '" data-handle="' | append: product.handle | append: '"></div>' %}{% if zp_pcintrcsbcr %}{% assign zp_rcscrwgc = '<div id="' | append: zp_product_selector | append: '-recharge-wrapper" class="zpa-offset-bottom-xs zpa-recharge-wrapper" data-zp-recharge-product="' | append: product.id | append: '" data-zp-wrapper="' | append: zp_product_selector | append: '"></div>' %}{% endif %}{% assign zp_pphpr = routes.root_url | append: '/products/' | replace: '//', '/' | append: product.handle %}{% assign zp_purpr = shop.url | append: zp_pphpr %}{% assign zp_pttpr = product.title %}{% assign zp_pprid = product.id %}{% capture zp_pphrpr %}zps_pp{{ zp_pprid }}_{% endcapture %}{% capture zp_purrpr %}zps_pu{{ zp_pprid }}_{% endcapture %}{% assign zp_redirect_product_url = zp_redirect_product_url | replace: zp_pphrpr, zp_pphpr | replace: zp_purrpr, zp_purpr %}{% endif -%} 
{%- if zp_main_product_available %}{%- capture zp_replace_integration -%}<zp_product_custom_variants_integration></zp_product_custom_variants_integration>{%- endcapture -%}{% assign zp_replace_integration = '' %}{% endif -%}{% if zp_pbtnstgs != blank %}{% assign zp_pbtnsjsndt = '[' %}{% assign zp_pbtnsaispr = false %}{% for zp_pstgv in zp_pbtnstgs %}{% assign zp_pbtnt = zp_pstgv['tp'] %}{% if zp_pbtnsaispr %}{% assign zp_pbtnsispr = ',' %}{% else %}{% assign zp_pbtnsispr = '' %}{% endif %}{% assign zp_pbtnsaispr = true %}{% assign zp_pbtnsjsndt = zp_pbtnsjsndt | append: zp_pbtnsispr | append: '{' %}{% assign zp_pbtnsaipspr = false %}{% for zp_pstgnv in zp_pstgv %}{% assign zp_pstgntk = zp_pstgnv | first | json %}{% if zp_pstgntk == '"cpt"' %}{% continue %}{% endif %}{% if zp_pbtnsaipspr %}{% assign zp_pbtnsipspr = ',' %}{% else %}{% assign zp_pbtnsipspr = '' %}{% endif %}{% assign zp_pbtnsaipspr = true %}{% if zp_pstgntk == '"url"' and zp_product_found and zp_pbtnt == 'product' %}{% assign zp_pstgntv = routes.root_url | append: '/products/' | replace: '//', '/' | append: product.handle | url_escape | json %}{% else %}{% assign zp_pstgntv = zp_pstgnv | last | url_escape | json %}{% endif %}{% assign zp_pbtnsjsndt = zp_pbtnsjsndt | append: zp_pbtnsipspr | append: zp_pstgntk | append: ':' | append: zp_pstgntv %}{% endfor %}{% assign zp_pbtnsjsndt = zp_pbtnsjsndt | append: '}' %}{% endfor %}{% assign zp_pbtnsjsndt = zp_pbtnsjsndt | append: ']' %}{% endif %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_native_lazyloading_state = zp_current_entity_metafields['config'].value['native_lzld'] %}{% if zp_current_entity_native_lazyloading_state %}{% assign zp_current_entity_use_native_lazyloading = true %}{% else %}{% assign zp_current_entity_use_native_lazyloading = false %}{% endif %}{% else %}{% assign zp_current_entity_use_native_lazyloading = false %}{% endif %}{% if zp_current_product_custom_variants == blank %}{% assign zp_current_product_custom_variants = product.variants %}{% endif %}{% if zp_selected_variants.size < 1 %}{% assign zp_selected_variants = zp_current_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% endif %}{% if zp_use_product_variant_from_url %}{% assign zp_url_selected_variant_id = '' | append: product.selected_variant.id %}{% else %}{% assign zp_url_selected_variant_id = '' %}{% endif %}{% assign zp_filtered_selected_variants = '' %}{% assign zp_first_selected_variant = nil %}{% assign zp_current_selected_variant = nil %}{% assign zp_any_product_variant_available = false %}{% assign zp_basic_variant_ids = '' %}{% assign zp_bold_variant_ids = '' %}{% assign zp_product_min_price = nil %}{% assign zp_product_max_price = nil %}{% for zp_prd_variant in zp_current_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_intgrt_wboldqtbrk and zp_integrate_with_bold_custom_pricing %}{% assign zp_basic_variant_ids = zp_basic_variant_ids | append: zp_variant_id | append: ',' %}{% assign zp_bold_variant_id = zp_prd_variant.metafields.shappify_csp['csp_base'] | default: zp_prd_variant.metafields.shappify_qb['qb_parent'] | default: zp_prd_variant.metafields.shappify_bundle['bundle_parent'] | default: 'notassigned' %}{% assign zp_bold_variant_ids = zp_bold_variant_ids | append: zp_bold_variant_id | append: ',' %}{% endif %}{% if zp_use_product_variant_from_url and zp_url_selected_variant_id == zp_variant_id and zp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_first_selected_variant = zp_prd_variant %}{% assign zp_current_selected_variant = zp_prd_variant %}{% endif %}{% if zp_selected_variants contains zp_variant_id %}{% assign zp_filtered_selected_variants = zp_filtered_selected_variants | append: zp_variant_id | append: ',' %}{% if zp_first_selected_variant == nil %}{% assign zp_first_selected_variant = zp_prd_variant %}{% endif %}{% if zp_prd_variant.available %}{% assign zp_any_product_variant_available = true %}{% endif %}{% if zp_product_min_price == blank or zp_product_min_price > zp_prd_variant.price %}{% assign zp_product_min_price = zp_prd_variant.price %}{% endif %}{% if zp_product_max_price == blank or zp_product_max_price < zp_prd_variant.price %}{% assign zp_product_max_price = zp_prd_variant.price %}{% endif %}{% endif %}{% endfor %}{% assign zp_selected_variants = zp_filtered_selected_variants | split: ',' %}{% if zp_selected_variants.size < 1 %}{% assign zp_selected_variants = zp_current_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% assign zp_any_product_variant_available = product.available %}{% if zp_use_product_variant_from_url and zp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_first_selected_variant = product.selected_variant %}{% assign zp_current_selected_variant = product.selected_variant %}{% else %}{% assign zp_first_selected_variant = zp_current_product_custom_variants | first %}{% endif %}{% endif %}{% unless zp_first_selected_variant.available %}{% assign zp_first_available_variant = nil %}{% for zp_prd_variant in zp_current_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_selected_variants contains zp_variant_id and zp_prd_variant.available %}{% assign zp_first_available_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% unless zp_first_available_variant == nil %}{% assign zp_first_selected_variant = zp_first_available_variant %}{% endunless %}{% assign zp_first_available_variant = nil %}{% endunless %}{% assign zp_variant_id = nil %}{% if zp_intgrt_wboldqtbrk and zp_integrate_with_bold_custom_pricing and zp_main_product_available %}{% assign zp_basic_variant_ids = zp_basic_variant_ids | split: ',' %}{% assign zp_bold_variant_ids = zp_bold_variant_ids | split: ',' %}{% assign zp_bold_variant_id = nil %}{% assign zp_variant_id = '' | append: zp_first_selected_variant.id %}{% for zp_prd_variant in zp_bold_variant_ids %}{% if zp_variant_id == zp_prd_variant %}{% assign zp_bold_variant_id = zp_basic_variant_ids[forloop.index0] %}{% break %}{% endif %}{% endfor %}{% if zp_bold_variant_id and zp_bold_variant_id != zp_variant_id %}{% assign zp_break_iterator = false %}{% for zp_prd_variant in product.variants %}{% if zp_break_iterator %}{% break %}{% endif %}{% assign zp_prd_variant_id = '' | append: zp_prd_variant.id %}{% if zp_prd_variant_id == zp_bold_variant_id %}{% assign zp_break_iterator = true %}{% if zp_intgrt_wboldqtbrk %}{% include 'bold-variant' with zp_prd_variant, hide_action: 'skip' %}{% endif %}{% assign zp_first_selected_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% endif %}{% endif %}{% assign zp_current_product_price = zp_first_selected_variant.price %}{% if zp_pimgdt != blank %}{% assign zp_img_tps = zp_pimgdt | map: 'tp' %}{% assign zp_umltpimgs = true %}{% else %}{% assign zp_img_tps = zp_product_image_type | split: ',' %}{% assign zp_umltpimgs = false %}{% endif %}{% for zp_img_tp in zp_img_tps %}{% assign zp_product_featured_image = product.featured_image %}{% assign zp_product_default_image = zp_product_featured_image | img_url: 'master' %}{% if zp_product_image_sizes_data == blank or zp_product_image_sizes_data == '' %}{% assign zp_product_image_sizes_data = '1080:--:1080x:|~|:936:--:936x:|~|:774:--:774x:|~|:540:--:540x:|~|:350:--:350x' %}{% endif %}{% assign zp_first_selected_variant_image_id = '' | append: zp_first_selected_variant.image.id | strip %}{% if zp_img_tp == 'variant' and zp_shw_vrntsslctr == true and zp_first_selected_variant_image_id.size > 0 %}{% assign zp_product_featured_image = zp_first_selected_variant.image %}{% endif %}{% assign zp_product_image_srcset_available = true %}{% assign zp_product_image_sizes_data_parts = '' | append: zp_product_image_sizes_data | split: ':|~|:' %}{% assign zp_product_image_src_set_string = '' %}{% assign zp_product_image_sizes_list_string = '' %}{% assign zp_product_image_sizes_json_string = '' %}{% assign zp_current_product_image_size = nil %}{% assign zp_first_image_screen_size = 0 %}{% assign zp_last_image_screen_size = 0 %}{% for zp_image_size_data in zp_product_image_sizes_data_parts %}{% assign zp_image_size_data_parts = '' | append: zp_image_size_data | split: ':--:' %}{% assign zp_image_screen_size = zp_image_size_data_parts[0] %}{% assign zp_image_resize_filter = zp_image_size_data_parts[1] %}{% if forloop.first == true %}{% assign zp_first_image_screen_size = 0 | plus: zp_image_screen_size %}{% elsif forloop.last == true %}{% assign zp_last_image_screen_size = 0 | plus: zp_image_screen_size %}{% endif %}{% if zp_image_screen_size.size > 0 and zp_image_resize_filter.size > 0 %}{% assign zp_product_image_url_with_filter = zp_product_featured_image | img_url: zp_image_resize_filter %}{% assign zp_image_screen_size_width = zp_image_screen_size | append: 'w' %}{% assign zp_product_image_src_set_string = zp_product_image_src_set_string | append: zp_product_image_url_with_filter | append: ' ' | append: zp_image_screen_size_width | append: '__zp1502150400__' %}{% assign zp_product_image_sizes_list_string = zp_product_image_sizes_list_string | append: zp_image_screen_size | append: 'px' | append: '__zp1502150400__' %}{% assign zp_image_screen_size_width_json = zp_image_screen_size_width | json %}{% assign zp_image_resize_filter_json = zp_image_resize_filter | json %}{% assign zp_product_image_sizes_json_string = zp_product_image_sizes_json_string | append: '{"filter":' | append: zp_image_resize_filter_json | append: ',"screen":' | append: zp_image_screen_size_width_json | append: '}' | append: '__zp1502150400__' %}{% unless zp_current_product_image_size %}{% assign zp_current_product_image_size = zp_image_resize_filter %}{% endunless %}{% endif %}{% endfor %}{% unless zp_current_product_image_size %}{% assign zp_current_product_image_size = '1080x' %}{% endunless %}{% assign zp_product_image_src_set = zp_product_image_src_set_string | split: '__zp1502150400__' %}{% assign zp_product_image_sizes_json = zp_product_image_sizes_json_string | split: '__zp1502150400__' %}{% if zp_first_image_screen_size > zp_last_image_screen_size %}{% assign zp_product_image_src_set = zp_product_image_src_set | reverse %}{% assign zp_product_image_sizes_json = zp_product_image_sizes_json | reverse %}{% endif %}{% assign zp_product_image_src_set = zp_product_image_src_set | join: ', ' %}{% assign zp_product_image_sizes_list = zp_product_image_sizes_list_string | split: '__zp1502150400__' | join: ',' %}{% assign zp_product_image_sizes_json = zp_product_image_sizes_json | join: ',' | prepend: '[' | append: ']' %}{% assign zp_product_image = zp_product_featured_image | img_url: zp_current_product_image_size %}{% if zp_product_load_main_image == false %}{% assign zp_product_image = '' %}{% endif %}{% if zp_img_tp == 'custom' %}{% assign zp_product_image_lazy_load_available = false %}{% assign zp_product_image_srcset_available = false %}{% assign zp_product_image_srcset_sizes_available = false %}{% else %}{% assign zp_product_image_lazy_load_available = true %}{% assign zp_product_image_srcset_sizes_available = true %}{% endif %}{% capture zp_product_image_data_attrs %}{% if zp_product_image_lazy_load and zp_current_entity_use_native_lazyloading == false %}{% assign zp_product_image_load_attr_prefix = 'data-' %}{% else %}{% assign zp_product_image_load_attr_prefix = '' %}{% endif %}{% if zp_product_image_lazy_load and zp_product_image_lazy_load_available %} data-lowsrc="{{ zp_product_featured_image | img_url: '540x' }}" width="{{ zp_product_featured_image.width }}" height="{{ zp_product_featured_image.height }}" data-parent-fit="cover" data-aspectratio="16/9" {% if zp_current_entity_use_native_lazyloading %}decoding="async" loading="lazy" src="{{ zp_product_featured_image | img_url: 'original' }}"{% endif %}{% endif %}{% if zp_product_image_srcset_sizes_available %} {% if zp_product_image_lazy_load and zp_product_image_lazy_load_available %}data-sizes="auto" sizes="{{ zp_product_image_sizes_list }}"{% else %}sizes="{{ zp_product_image_sizes_list }}"{% endif %}{% endif %}{% if zp_product_image_srcset_available and zp_product_load_main_image %} {{ zp_product_image_load_attr_prefix }}srcset="{{ zp_product_image_src_set }}"{% endif %} data-zp-product-image data-zp-product-image-type="{{ zp_img_tp }}" data-zp-image-id="{{ zp_product_featured_image.id }}" data-zp-default-image="{{ zp_product_default_image }}"{% endcapture %}{% if zp_umltpimgs %}{% capture zp_img_prp %}zps_mprdimgdt_{{ zp_pblkmkr }}_{{ zp_pimgdt[forloop.index0]['id'] }}_{% endcapture %}{% capture zp_img_alprpn %}zps_imgalt_{{ zp_pblkmkr }}_{{ zp_pimgdt[forloop.index0]['id'] }}_{% endcapture %}{% assign zp_img_alprpv = zp_product_featured_image.alt | strip_html | strip_newlines | escape %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_img_prp, zp_product_image_data_attrs | replace: zp_img_alprpn, zp_img_alprpv %}{% endif %}{% endfor %}{% if zp_pprvimg %}{% assign zp_pimgldcnt = '<svg class="hidden" width="215px" height="150px" data-zp-image-loader><use href="#elementLoader"></use></svg>' %}{% endif %}{% if zp_pshcren or zp_pcrdt != blank %}{% if zp_pcrdt != blank %}{% assign zp_pcrldtl = zp_pcrdt %}{% assign zp_pcrlrvr = true %}{% else %}{% assign zp_pcrldtl = '1' | split: ',' %}{% assign zp_pcrlrvr = false %}{% endif %}{% for zp_pcrldti in zp_pcrldtl %}{% if zp_pcrlrvr %}{% assign zp_pshcrid = zp_pcrldti['spcid'] %}{% if zp_pshcritid != blank %}{% assign zp_pshcrmfid = zp_pshcritid %}{% else %}{% assign zp_pshcrmfid = zp_pcrldti['spcpid'] %}{% endif %}{% assign zp_pcrshsrs = '' | append: zp_pcrldti['spsrcs'] %}{% assign zp_pcrlbmkr = zp_pblkmkr | append: '_' | append: zp_pshcrid %}{% else %}{% assign zp_pcrlbmkr = zp_pblkmkr %}{% endif %}{% capture zp_cdvdbgn %}<zppcim_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zppcim_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{% assign zp_product_shopify_carousel_image_template = zp_tpsct[0] %}{% capture zp_cdvdbgn %}<zppcv_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zppcv_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{% assign zp_product_shopify_carousel_video_template = zp_tpsct[0] %}{% capture zp_cdvdbgn %}<zppcti_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zppcti_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{% assign zp_product_shopify_carousel_thumbnail_image_template = zp_tpsct[0] %}{% capture zp_cdvdbgn %}<zppctv_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zppctv_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{% assign zp_product_shopify_carousel_thumbnail_video_template = zp_tpsct[0] %}{% capture zp_pshcrmtpl %}<zpctw_{{ zp_pcrlbmkr }}>{% capture zp_cdvdbgn %}<zpctw_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zpctw_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{{ zp_tpsct[0] }}</zpctw_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_pshcrttpl %}<zpctwt_{{ zp_pcrlbmkr }}>{% capture zp_cdvdbgn %}<zpctwt_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zpctwt_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{{ zp_tpsct[0] }}</zpctwt_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_pshcrm %}{% assign zp_src_sizes_cut = zp_pcrshsrs | split: ',' %}{% if product.media.size > 0 %}{% if zp_first_selected_variant.available and zp_first_selected_variant.featured_media != blank %}{% case zp_first_selected_variant.featured_media.media_type %}{% when 'image' %}{% assign zp_imgsml = zp_first_selected_variant.featured_media | img_url: '100x100' %}{% assign zp_imgorg = zp_first_selected_variant.featured_media | img_url: 'original' %}{% assign zp_imgalt = zp_first_selected_variant.featured_media.alt | escape %}{% capture zp_srcszimgs %}{% for zp_srcszct in zp_src_sizes_cut %}{{ zp_first_selected_variant.featured_media | img_url: zp_srcszct }} {{ zp_srcszct | replace: 'x', 'w' }}{% unless forloop.last %},{% endunless %}{% endfor %}{% endcapture %}{{ zp_product_shopify_carousel_image_template | replace: 'zps_medwid', zp_first_selected_variant.featured_media.width| replace: 'zps_medheig', zp_first_selected_variant.featured_media.height | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medimurl', zp_imgorg | replace: 'zps_medalt', zp_imgalt | replace: 'zps_medimgsrc', zp_srcszimgs }}{% when 'external_video' %}{% assign video = zp_first_selected_variant.featured_media | external_video_tag %}{{ zp_product_shopify_carousel_video_template | replace: 'zps_medvid', video }}{% when 'video' %}{% assign video = zp_first_selected_variant.featured_media | video_tag: controls: true %}{{ zp_product_shopify_carousel_video_template | replace: 'zps_medvid', video }}{% endcase %}{% endif %}{% for zp_product_media in product.media %}{% if zp_product_media.id == zp_first_selected_variant.featured_media.id %}{% continue %}{% endif %}{% case zp_product_media.media_type %}{% when 'image' %}{% assign zp_imgsml = zp_product_media | img_url: '100x100' %}{% assign zp_imgorg = zp_product_media | img_url: 'original' %}{% assign zp_imgalt = zp_product_media.alt | escape %}{% capture zp_srcszimgs %}{% for zp_srcszct in zp_src_sizes_cut %}{{ zp_product_media | img_url: zp_srcszct }} {{ zp_srcszct | replace: 'x', 'w' }}{% unless forloop.last %},{% endunless %}{% endfor %}{% endcapture %}{{ zp_product_shopify_carousel_image_template | replace: 'zps_medwid', zp_product_media.width| replace: 'zps_medheig', zp_product_media.height | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medimurl', zp_imgorg | replace: 'zps_medalt', zp_imgalt | replace: 'zps_medimgsrc', zp_srcszimgs }}{% when 'external_video' %}{% assign video = zp_product_media | external_video_tag %}{{ zp_product_shopify_carousel_video_template | replace: 'zps_medvid', video }}{% when 'video' %}{% assign video = zp_product_media | video_tag: controls: true %}{{ zp_product_shopify_carousel_video_template | replace: 'zps_medvid', video }}{% endcase %}{% endfor %}{% else %}{% assign zp_imgsml = product.media | img_url: '100x100' %}{% assign zp_imgorg = product.media | img_url: 'original' %}{% assign zp_imgalt = product.media.alt | escape %}{% capture zp_srcszimgs %}{% for zp_srcszct in zp_src_sizes_cut %}{{ product.media | img_url: zp_srcszct }} {{ zp_srcszct | replace: 'x', 'w' }}{% unless forloop.last %},{% endunless %}{% endfor %}{% endcapture %}{{ zp_product_shopify_carousel_image_template | replace: 'zps_medwid', product.media.width| replace: 'zps_medheig', product.media.height | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medimurl', zp_imgorg | replace: 'zps_medalt', zp_imgalt | replace: 'zps_medimgsrc', zp_srcszimgs }}{% endif %}{% endcapture %}{% capture zp_pshcrt %}{% assign zp_media_position = 0 %}{% if product.media.size > 0 %}{% if zp_first_selected_variant.available and zp_first_selected_variant.featured_media != blank %}{% case zp_first_selected_variant.featured_media.media_type %}{% when 'image' %}{% assign zp_imgsml = zp_first_selected_variant.featured_media | img_url: '100x100' %}{% assign zp_imgalt = zp_first_selected_variant.featured_media.alt | escape %}{{ zp_product_shopify_carousel_thumbnail_image_template | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medpos', zp_media_position | replace: 'zps_medalt', zp_imgalt }}{% when 'external_video' %}{{ zp_product_shopify_carousel_thumbnail_video_template | replace: 'zps_medpos', zp_media_position }}{%when 'video' %}{{ zp_product_shopify_carousel_thumbnail_video_template | replace: 'zps_medpos', zp_media_position }}{% endcase %}{% assign zp_media_position = zp_media_position | plus: 1 %}{% endif %}{% for zp_product_media in product.media %}{% if zp_product_media.id == zp_first_selected_variant.featured_media.id %}{% continue %}{% endif %}{% case zp_product_media.media_type %}{% when 'image' %}{% assign zp_imgsml = zp_product_media | img_url: '100x100' %}{% assign zp_imgalt = zp_product_media.alt | escape %}{{ zp_product_shopify_carousel_thumbnail_image_template | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medpos', zp_media_position | replace: 'zps_medalt', zp_imgalt }}{% when 'external_video' %}{{ zp_product_shopify_carousel_thumbnail_video_template | replace: 'zps_medpos', zp_media_position }}{%when 'video' %}{{ zp_product_shopify_carousel_thumbnail_video_template | replace: 'zps_medpos', zp_media_position }}{% endcase %}{% assign zp_media_position = zp_media_position | plus: 1 %}{% endfor %}{% else %}{% assign zp_imgsml = product.media | img_url: '100x100' %}{% assign zp_imgalt = product.media.alt | escape %}{{ zp_product_shopify_carousel_thumbnail_image_template | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medpos', zp_media_position | replace: 'zps_medalt', zp_imgalt }}{% endif %}{% endcapture %}{% capture zp_pshcrjsn %}{% assign zp_open_bracket = "{" %}{% assign zp_close_bracket = "}" %}{% assign zp_need_add_comma = false %}<script type="application/json" data-zp-shopify-carousel="{{ zp_pshcrid }}">{{ zp_open_bracket }}"id":{{ '' | append: zp_pshcrid | json }},"mfid":{{ zp_pshcrmfid | json }},"role":"shopify_carousel","media":[{% if product.media.size > 0 %}{% if zp_first_selected_variant.available and zp_first_selected_variant.featured_media != blank %}{% assign zp_need_add_comma = true %}{{ zp_open_bracket }}{% case zp_first_selected_variant.featured_media.media_type %}{% when 'image' %}"id":"{{ zp_first_selected_variant.featured_media.id }}","type":"image","src":"{{ zp_first_selected_variant.featured_media | img_url: 'original' }}","alt":{{ '' | append: zp_first_selected_variant.featured_media.alt | escape | json }}{% when 'external_video' %}"id":"{{ zp_first_selected_variant.featured_media.id }}","type":"video","content":"{{ zp_first_selected_variant.featured_media | external_video_tag | escape }}"{% when 'video' %}"id":"{{ zp_first_selected_variant.featured_media.id }}","type":"video","content":"{{ zp_first_selected_variant.featured_media | video_tag: controls: true | escape }}"{% endcase %}{{ zp_close_bracket }}{% endif %}{% for zp_product_media in product.media %}{% if zp_product_media.id == zp_first_selected_variant.featured_media.id %}{% continue %}{% endif %}{% if zp_need_add_comma %},{% endif %}{{ zp_open_bracket }}{% case zp_product_media.media_type %}{% when 'image' %}"id":"{{ zp_product_media.id }}","type":"image","src":"{{ zp_product_media | img_url: 'original' }}","alt":{{ '' | append: zp_product_media.alt | escape | json }}{% when 'external_video' %}"id":"{{ zp_product_media.id }}","type":"video","content":"{{ zp_product_media | external_video_tag | escape }}"{% when 'video' %}"id":"{{ zp_product_media.id }}","type":"video","content":"{{ zp_product_media | video_tag: controls: true | escape }}"{% endcase %}{{ zp_close_bracket }}{% assign zp_need_add_comma = true %}{% endfor %}{% else %}{{ zp_open_bracket }}"id":"{{ product.media.id }}","type":"image_default","src":"{{ product.media | img_url: 'original' }}","alt":{{ '' | append: product.media.alt | escape | json }}{{ zp_close_bracket }}{% endif %}]{{ zp_close_bracket }}</script>{% endcapture %}{% if zp_pshcrttpl != blank %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_pshcrttpl, zp_pshcrt %}{% endif %}{% if zp_pshcrmtpl != blank %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_pshcrmtpl, zp_pshcrm %}{% endif %}{% capture zp_pcrlbmkr %}<zpprdscj_{{ zp_pcrlbmkr }}></zpprdscj_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_pcrlbmkr, zp_pshcrjsn %}{% endfor %}{% assign zp_pcrldtl = nil %}{% assign zp_pshcritid = nil %}{% endif %}{% if zp_product_redirect_with_params == nil %}{% assign zp_current_product_redirect_with_params = false %}{% else %}{% assign zp_current_product_redirect_with_params = zp_product_redirect_with_params %}{% endif %}{% assign zp_product_json_integration_content = product | json | default: 'null' %}{% if zp_integrate_with_recharge == false and zp_pcintrcsbcr and zp_rcprewso == false %}<style>[data-zp-add-to-cart-form] .rc-container-wrapper{display:none!important}</style>{% assign zp_rcprewso = true %}{% endif %} 
{%- if zp_main_product_available %}{% capture zp_product_soldout_integrations -%}{% capture zp_replace_integration %}<zp_product_soldout_integration></zp_product_soldout_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_soldout_integration>', '' | replace: '</zp_product_soldout_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- endcapture %}{% assign zp_pfcstg = zp_product_soldout_integrations %}{% assign zp_product_soldout_integrations = nil %}{% endif -%}{%- if zp_any_product_variant_available -%}{%- if zp_main_product_available %}{% capture zp_product_form_classes_list -%}{% if zp_intgrt_wboldqtbrk %}shapp_qb_prod{% endif %} {{ zp_product_form_classes }} {{ zp_product_form_custom_classes }}{%- endcapture %}{% assign zp_product_form_classes_list = zp_product_form_classes_list | strip %}{% else %}{% assign zp_product_form_classes_list = '' %}{% endif %}{% if zp_use_product_form_action %}{% assign zp_product_form_action_attr = '/cart/add' %}{% else %}{% assign zp_product_form_action_attr = nil %}{% endif %}{% if zp_use_product_form_id %}{% assign zp_product_form_id_default_val = 'zp_product_form_' | append: zp_product_iterator | append: '_' | append: product.id %}{% assign zp_product_form_id_attr = zp_product_form_id_val | default: zp_product_form_id_default_val %}{% else %}{% assign zp_product_form_id_attr = nil %}{% endif %}{% if zp_enable_subscription_widget %}{% assign zp_disable_subscription_widget_attr = nil %}{% else %}{% assign zp_disable_subscription_widget_attr = '' %}{% endif %}{% if zp_prdlpsbcren %}{% assign zp_prdlpsbcrfat = product.id %}{% else %}{% assign zp_prdlpsbcrfat = nil %}{% endif -%}{%- capture zp_pfoptg -%}{%- form 'product', product, id: zp_product_form_id_attr, action: zp_product_form_action_attr, class: zp_product_form_classes_list, data-zp-add-to-cart-form: '', data-zp-link-type: zp_product_link_type, data-productid: product.id, data-zp-redirect-with-url-params: zp_current_product_redirect_with_params, data-zp-disable-subscrptn-wdgt: zp_disable_subscription_widget_attr, data-loop-product-id: zp_prdlpsbcrfat -%}{% if zp_dshpwgtbpr and zp_product_max_price != blank %}{% if zp_product_max_price < 5000 or zp_product_max_price > 300000 %}{% assign zp_dshpwgt = false %}{% endif %}{% endif %}{% if zp_dshpwgt and zp_shw_vrntsslctr and zp_app_integrations contains 'shoppaywidget' %}{% if zp_shpwtginc %}{% capture zp_pfpmtcp %}{{ form | payment_terms }}{% endcapture %}{% else %}<div class="zpa-offset-bottom-sm">{{ form | payment_terms }}</div>{% endif %}{% if zp_pfpmtcp.size > 0 %}{% assign zp_unpfptrmscl = '' %}{% endif %}{% endif %}{% assign zp_pfcstg = '</form>' %}{% assign zp_product_form_classes = '' %}{% assign zp_product_form_classes_list = '' %}{% assign zp_unpcls = '' %}{% if zp_shw_vrntsslctr and zp_product_show_quantity %}{% assign zp_unqtycls = '' %}{% else %}{% assign zp_unqtycls = ' hidden' %}{% endif %}{% if zp_shw_vrntsslctr %}{% assign zp_incl_boldsnpts = true %}{% else %}{% assign zp_incl_boldsnpts = false %}{% endif %}{% if zp_incl_boldsnpts and zp_app_integrations contains 'productoptionsbybold' %}{% assign zp_intgrt_wboldprdopts = true %}{% else %}{% assign zp_intgrt_wboldprdopts = false %}{% endif %}{% if zp_incl_boldsnpts and zp_app_integrations contains 'quantitybreaksbybold' %}{% assign zp_intgrt_wboldqtbrk = true %}{% else %}{% assign zp_intgrt_wboldqtbrk = false %}{% endif %}{% if zp_app_integrations contains 'subscriptionsbybold' %}{% assign zp_incl_boldsbsrpns = true %}{% else %}{% assign zp_incl_boldsbsrpns = false %}{% endif %}{% assign zp_product_button_type = 'submit' %}{% capture zp_checkout_button %}{{ form | payment_button }}{% endcapture %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_pdstds = zp_current_entity_metafields['config'].value['staticdiscounts'] %}{% else %}{% assign zp_pdstds = zp_current_entity_metafields['config']['staticdiscounts'] %}{% endif %}{% assign zp_cpstdd = zp_pdstds[zp_product_element_id] %}{% assign zp_current_product_static_discount_data = zp_cpstdd %}{%- if zp_rprfrtg and zp_product_content_parts contains zp_opened_cross_sell_tag -%}{% assign zp_product_with_cross_sell_tag = true %}{%- else -%}{% assign zp_product_with_cross_sell_tag = false %}{%- endif -%}{%- if zp_shw_vrntsslctr or zp_bndblen -%}{% if zp_rprfrmf %}{% assign zp_cscntprts = zp_current_product_settings['crsslls'] | default: zp_current_product_settings['bndl'] | default: nil %}{% else %}{% assign zp_cscntprts = zp_product_content_parts | split: zp_opened_cross_sell_tag %}{% assign zp_product_content_parts = '' %}{% endif %}{% for zp_cscntprt in zp_cscntprts %}{% if zp_rprfrmf %}{% assign zp_csblkmkr = zp_cscntprt | first %}{% assign zp_cssttngs = zp_cscntprt | last %}{% assign zp_cscntprtsz = 2 %}{% else %}{% assign zp_cscnt = zp_cscntprt | append: ' ' | split: zp_closed_cross_sell_tag %}{% assign zp_cscntprtsz = zp_cscnt | size %}{% endif %}{% if zp_cscntprtsz < 2 %}{% assign zp_product_content_parts = zp_product_content_parts | append: zp_cscntprt %}{% else %}{% assign zp_cross_sell_product = nil %}{% assign zp_cshndl = '' %}{% assign zp_csslcvrns = '' %}{% assign zp_csslvrnlst = '' %}{% assign zp_csblcid = '' %}{% assign zp_csimgsz = nil %}{% assign zp_csimgszjson = '[]' %}{% assign zp_csshwvrnlbl = nil %}{% assign zp_csvrntslvw = 'vt' %}{% assign zp_csvrntslofscls = nil %}{% assign zp_csdsctam = nil %}{% assign zp_cssvam = nil %}{% assign zp_crcspr = nil %}{% assign zp_crcscapr = nil %}{% assign zp_csslpr = nil %}{% assign zp_fmcspr = nil %}{% assign zp_csimg = 'no-image.gif' | img_url: '280x' %}{% assign zp_fmcsslpr = nil %}{% assign zp_fmcscapr = nil %}{% assign zp_fmcsdsctsvam = nil %}{% assign zp_fmcsdsctam = nil %}{% assign zp_csdsctamwsfx = nil %}{% assign zp_uncscls = ' hidden' %}{% assign zp_csunvbdsctcl = ' hidden' %}{% assign zp_uncsqtycls = ' hidden' %}{% assign zp_csupwrprcls = ' hidden' %}{% assign zp_csimgdatr = nil %}{% assign zp_csvrntcntr = nil %}{% assign zp_csqtycntr = nil %}{% assign zp_csmsgs = nil %}{% assign zp_cross_sell_first_selected_variant = nil %}{% assign zp_current_cross_sell_custom_variants = nil %}{% assign zp_cselid = nil %}{% assign zp_csupblck = nil %}{% assign zp_csuplbl = '' %}{% assign zp_csatnlclscnt = '' %}{% assign zp_csavlbl = false %}{% assign zp_csldmnimg = true %}{% assign zp_csshwqtylbl = true %}{% assign zp_csdftqty = '1' %}{% assign zp_csimgtp = 'product' %}{% assign zp_csdsctdt = '' %}{% assign zp_csshwqty = true %}{% assign zp_cross_sell_view_type = 'crosssellproduct' %}{% assign zp_csprdfnd = false %}{% assign zp_csuntprtginc = false %}{% assign zp_csoptstgs = nil %}{% assign zp_csvrntslctrcls = nil %}{% assign zp_csimglzld = false %}{% assign zp_crvsrc = nil %}{% assign zp_csrvdt = nil %}{% assign zp_csrvwrpcl = 'disabled hidden' %}{% assign zp_csrvrtg = nil %}{% assign zp_csrvcnt = nil %}{% assign zp_csrvrtngtxt = '' %}{% assign zp_csshcrlen = false %}{% assign zp_csshcrlid = nil %}{% assign zp_csshcrlmfid = nil %}{% assign zp_csshcrlmdtpl = nil %}{% assign zp_csshcrltmbtpl = nil %}{% assign zp_csshcrlmds = '' %}{% assign zp_csshcrltmb = '' %}{% assign zp_csshcrljsn = '' %}{% assign zp_csvrntjsndt = nil %}{% assign zp_csimgsjsndt = nil %}{% assign zp_csimgsdt = nil %}{% assign zp_cspttl = 'products.product.unavailable' | t %}{% if zp_cspttl contains 'Translation missing' or zp_cspttl contains 'translation missing' %}{% assign zp_cspttl = "Unavailable" %}{% endif %}{% assign zp_cspdsc = 'products.product.unavailable' | t %}{% if zp_cspdsc contains 'Translation missing' or zp_cspdsc contains 'translation missing' %}{% assign zp_cspdsc = "Unavailable" %}{% endif %}{% assign zp_cross_sell_product_iterator = zp_cross_sell_product_iterator | plus: 1 %}{% if zp_bndblen %}{% assign zp_cross_sell_selector = 'zpbundleproductselector' %}{% else %}{% assign zp_cross_sell_selector = 'zpcrosssellselector' %}{% endif %}{% assign zp_cross_sell_selector = zp_cross_sell_selector | append: zp_product_selector_suffix | append: zp_cross_sell_product_iterator %}{% assign zp_cross_sell_wrapper_selector = zp_cross_sell_selector | append: zp_main_product_selector_suffix %}{% assign zp_cross_sell_empty_wrapper_selector = zp_cross_sell_wrapper_selector | append: '-empty' %}{% if zp_rprfrmf %}{% assign zp_product_settings_keys = zp_cssttngs %}{% else %}{% if zp_cscnt[0] contains '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_cscnt[0] = zp_cscnt[0] | remove: '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_display_cross_sell_markup = true %}{% else %}{% assign zp_display_cross_sell_markup = false %}{% endif %}{% assign zp_cscntprts = zp_cscnt[0] | append: ' ' | split: '>' %}{% assign zp_product_settings = zp_cscntprts[0] %}{% assign zp_product_settings_tmp = zp_product_settings | append: '>' %}{% assign zp_cscntprts = zp_cscntprts | join: '>' | remove_first: zp_product_settings_tmp %}{% assign zp_product_settings = ' ' | append: zp_product_settings | split: ' data-' %}{% assign zp_product_settings_keys = '' %}{% assign zp_product_settings_values = '' %}{% for zp_product_setting in zp_product_settings %}{% assign zp_product_setting_parts = zp_product_setting | split: '=' %}{% if zp_product_setting_parts.size < 2 %}{% continue %}{% endif %}{% assign zp_prstnk = '' | append: zp_product_setting_parts[0] | strip %}{% if zp_prstnk.size < 1 %}{% continue %}{% endif %}{% assign zp_product_settings_keys = zp_product_settings_keys | append: zp_prstnk | append: '__zp1502150400__' %}{% assign zp_product_setting_size = zp_product_setting | size %}{% assign zp_product_setting_key_size = '' | append: zp_product_setting_parts[0] | size | plus: 1 %}{% assign zp_prstnvl = '' | append: zp_product_setting | slice: zp_product_setting_key_size, zp_product_setting_size | strip %}{% assign zp_product_setting_value_last_index = zp_prstnvl | size | minus: 1 %}{% assign zp_product_setting_value_first_letter = zp_prstnvl | first %}{% if zp_product_setting_value_first_letter == '"' or zp_product_setting_value_first_letter == "'" %}{% assign zp_prstnvl = zp_prstnvl | slice: 1, zp_product_setting_value_last_index %}{% endif %}{% assign zp_product_setting_value_last_index = zp_prstnvl | size | minus: 1 %}{% assign zp_product_setting_value_last_letter = zp_prstnvl | last %}{% if zp_product_setting_value_last_letter == '"' or zp_product_setting_value_last_letter == "'" %}{% assign zp_prstnvl = zp_prstnvl | slice: 0, zp_product_setting_value_last_index %}{% endif %}{% assign zp_product_settings_values = zp_product_settings_values | append: zp_prstnvl | append: '__zp1502150400__' %}{% endfor %}{% assign zp_product_settings_keys = zp_product_settings_keys | split: '__zp1502150400__' %}{% assign zp_product_settings_values = zp_product_settings_values | split: '__zp1502150400__' %}{% assign zp_product_settings = nil %}{% assign zp_product_settings_tmp = nil %}{% assign zp_product_setting_parts = nil %}{% endif %}{% for zp_product_setting_key_data in zp_product_settings_keys %}{% if zp_rprfrmf %}{% assign zp_prstnk = zp_product_setting_key_data | first %}{% assign zp_unstringified_product_setting_keys = 'srtgs,vrnts,imgs,pcrls' | split: ',' %}{% if zp_unstringified_product_setting_keys contains zp_prstnk %}{% assign zp_prstnvl = zp_product_setting_key_data | last %}{% else %}{% assign zp_prstnvl = zp_product_setting_key_data | last | strip %}{% endif %}{% assign zp_unstringified_product_setting_keys = nil %}{% else %}{% assign zp_prstnk = zp_product_setting_key_data %}{% assign zp_prstnvl = '' | append: zp_product_settings_values[forloop.index0] | strip %}{% endif %}{% case zp_prstnk %}{% when 'handle' %}{% assign zp_cshndl = zp_prstnvl %}{% when 'slctvrnt' %}{% assign zp_csslcvrns = zp_prstnvl %}{% assign zp_csslvrnlst = zp_prstnvl %}{% when 'qty' %}{% assign zp_csdftqty = zp_prstnvl %}{% when 'bid' %}{% assign zp_csblcid = zp_prstnvl %}{% when 'eid' %}{% assign zp_cselid = zp_prstnvl %}{% when 'imgtp' %}{% assign zp_csimgtp = zp_prstnvl %}{% when 'dscntdt' %}{% assign zp_csdsctdt = zp_prstnvl %}{% when 'srtgs' %}{% assign zp_srtjsndt = '' %}{% assign zp_srtaispr = false %}{% for zp_pstgv in zp_prstnvl %}{% if zp_pstgv['rtgsrc'] != blank %}{% if zp_srtaispr %}{% assign zp_srtispr = ',' %}{% else %}{% assign zp_srtispr = '' %}{% endif %}{% assign zp_srtjsndt = zp_srtjsndt | append: zp_srtispr | append: '{' %}{% assign zp_srtaispr = true %}{% assign zp_srtaipspr = false %}{% for zp_pstgnv in zp_pstgv %}{% if zp_srtaipspr %}{% assign zp_srtipspr = ',' %}{% else %}{% assign zp_srtipspr = '' %}{% endif %}{% assign zp_srtaipspr = true %}{% assign zp_srstgntk = zp_pstgnv | first | json %}{% assign zp_srstgntv = zp_pstgnv | last | json %}{% assign zp_srtjsndt = zp_srtjsndt | append: zp_srtipspr | append: zp_srstgntk | append: ':' | append: zp_srstgntv %}{% endfor %}{% assign zp_srtjsndt = zp_srtjsndt | append: '}' %}{% endif %}{% endfor %}{% assign zp_csrvdt = '[' | append: zp_srtjsndt | append: ']' %}{% when 'rtgsrc' %}{% assign zp_crvsrc = zp_prstnvl %}{% when 'vrntsv' %}{% assign zp_csvrntslvw = zp_prstnvl %}{% when 'vtp' %}{% assign zp_cross_sell_view_type = zp_prstnvl %}{% when 'optst' %}{% assign zp_csoptstgs = zp_prstnvl %}{% when 'vrntcls' %}{% assign zp_csvrntslctrcls = zp_prstnvl | url_escape %}{% when 'vrntoffcls' %}{% assign zp_csvrntslofscls = zp_prstnvl | url_escape %}{% when 'shwqty' %}{% if zp_prstnvl == 'false' or zp_prstnvl == '0' %}{% assign zp_csshwqty = false %}{% endif %}{% when 'mimgld' %}{% if zp_prstnvl == 'false' or zp_prstnvl == '0' %}{% assign zp_csldmnimg = false %}{% endif %}{% when 'shwqtylb' %}{% if zp_prstnvl == 'false' or zp_prstnvl == '0' %}{% assign zp_csshwqtylbl = false %}{% endif %}{% when 'shwvrntlb' %}{% if zp_prstnvl == 'false' or zp_prstnvl == '0' %}{% assign zp_csshwvrnlbl = false %}{% else %}{% assign zp_csshwvrnlbl = true %}{% endif %}{% when 'unprcincl' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_csuntprtginc = true %}{% endif %}{% when 'lzld' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_csimglzld = true %}{% endif %}{% when 'spcen' %}{% if zp_prstnvl == 'true' or zp_prstnvl == '1' %}{% assign zp_csshcrlen = true %}{% endif %}{% when 'spcid' %}{% assign zp_csshcrlid = zp_prstnvl %}{% when 'spcpid' %}{% assign zp_csshcrlmfid = zp_prstnvl %}{% when 'vrnts' %}{% assign zp_csvrntjsndt = zp_prstnvl | json %}{% when 'imgs' %}{% assign zp_csimgsjsndt = zp_prstnvl | json %}{% assign zp_csimgsdt = zp_prstnvl %}{% when 'pcrls' %}{% assign zp_cscrls_dt = zp_prstnvl %}{% endcase %}{% endfor %}{% if zp_rprfrtg and zp_csoptstgs != empty %}{% assign zp_csoptstgs = zp_csoptstgs | replace: '&quot;', '"' %}{% endif %}{% if zp_cshndl.size > 0 %}{% assign zp_cross_sell_product = all_products[zp_cshndl] %}{% assign zp_cross_sell_id = '' | append: zp_cross_sell_product.id %}{% if zp_cross_sell_id.size > 0 %}{% assign zp_csprdfnd = true %}{% assign zp_cspttl = zp_cross_sell_product.title %}{% assign zp_cspttla = zp_cspttl | escape %}{% assign zp_cspdsc = zp_cross_sell_product.description %}{% assign zp_csqtyicrtr = 'products.product.quantity.increase' | t: product: zp_cross_sell_product.title %}{% if zp_csqtyicrtr contains 'Translation missing' or zp_csqtyicrtr contains 'translation missing' %}{% assign zp_csqtyicrtr = "Increase quantity" %}{% endif %}{% assign zp_csqtyinpttr = 'products.product.quantity.input_label' | t: product: zp_cross_sell_product.title %}{% if zp_csqtyinpttr contains 'Translation missing' or zp_csqtyinpttr contains 'translation missing' %}{% assign zp_csqtyinpttr = "Quantity" %}{% endif %}{% assign zp_csqtydcrtr = 'products.product.quantity.decrease' | t: product: zp_cross_sell_product.title %}{% if zp_csqtydcrtr contains 'Translation missing' or zp_csqtydcrtr contains 'translation missing' %}{% assign zp_csqtydcrtr = "Decrease quantity" %}{% endif %}{% if zp_current_cross_sell_custom_variants == blank %}{% assign zp_current_cross_sell_custom_variants = zp_cross_sell_product.variants %}{% endif %}{% if zp_csslcvrns.size < 1 %}{% assign zp_csslcvrns = zp_current_cross_sell_custom_variants | map: 'id' | join: ',' | split: ',' %}{% endif %}{% if zp_cross_sell_use_product_variant_from_url %}{% assign zp_url_selected_variant_id = '' | append: zp_cross_sell_product.selected_variant.id %}{% else %}{% assign zp_url_selected_variant_id = '' %}{% endif %}{% assign zp_filtered_selected_variants = '' %}{% assign zp_cross_sell_first_selected_variant = nil %}{% assign zp_cross_sell_current_selected_variant = nil %}{% assign zp_any_cross_sell_variant_available = false %}{% assign zp_basic_variant_ids = '' %}{% assign zp_bold_variant_ids = '' %}{% assign zp_product_min_price = nil %}{% assign zp_product_max_price = nil %}{% for zp_prd_variant in zp_current_cross_sell_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_intgrt_wboldqtbrk and zp_integrate_with_bold_custom_pricing %}{% assign zp_basic_variant_ids = zp_basic_variant_ids | append: zp_variant_id | append: ',' %}{% assign zp_bold_variant_id = zp_prd_variant.metafields.shappify_csp['csp_base'] | default: zp_prd_variant.metafields.shappify_qb['qb_parent'] | default: zp_prd_variant.metafields.shappify_bundle['bundle_parent'] | default: 'notassigned' %}{% assign zp_bold_variant_ids = zp_bold_variant_ids | append: zp_bold_variant_id | append: ',' %}{% endif %}{% if zp_cross_sell_use_product_variant_from_url and zp_url_selected_variant_id == zp_variant_id and zp_csslcvrns contains zp_url_selected_variant_id %}{% assign zp_cross_sell_first_selected_variant = zp_prd_variant %}{% assign zp_cross_sell_current_selected_variant = zp_prd_variant %}{% endif %}{% if zp_csslcvrns contains zp_variant_id %}{% assign zp_filtered_selected_variants = zp_filtered_selected_variants | append: zp_variant_id | append: ',' %}{% if zp_cross_sell_first_selected_variant == nil %}{% assign zp_cross_sell_first_selected_variant = zp_prd_variant %}{% endif %}{% if zp_prd_variant.available %}{% assign zp_any_cross_sell_variant_available = true %}{% endif %}{% if zp_product_min_price == blank or zp_product_min_price > zp_prd_variant.price %}{% assign zp_product_min_price = zp_prd_variant.price %}{% endif %}{% if zp_product_max_price == blank or zp_product_max_price < zp_prd_variant.price %}{% assign zp_product_max_price = zp_prd_variant.price %}{% endif %}{% endif %}{% endfor %}{% assign zp_csslcvrns = zp_filtered_selected_variants | split: ',' %}{% if zp_csslcvrns.size < 1 %}{% assign zp_csslcvrns = zp_current_cross_sell_custom_variants | map: 'id' | join: ',' | split: ',' %}{% assign zp_any_cross_sell_variant_available = zp_cross_sell_product.available %}{% if zp_cross_sell_use_product_variant_from_url and zp_csslcvrns contains zp_url_selected_variant_id %}{% assign zp_cross_sell_first_selected_variant = zp_cross_sell_product.selected_variant %}{% assign zp_cross_sell_current_selected_variant = zp_cross_sell_product.selected_variant %}{% else %}{% assign zp_cross_sell_first_selected_variant = zp_current_cross_sell_custom_variants | first %}{% endif %}{% endif %}{% unless zp_cross_sell_first_selected_variant.available %}{% assign zp_cross_sell_first_available_variant = nil %}{% for zp_prd_variant in zp_current_cross_sell_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_csslcvrns contains zp_variant_id and zp_prd_variant.available %}{% assign zp_cross_sell_first_available_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% unless zp_cross_sell_first_available_variant == nil %}{% assign zp_cross_sell_first_selected_variant = zp_cross_sell_first_available_variant %}{% endunless %}{% assign zp_cross_sell_first_available_variant = nil %}{% endunless %}{% assign zp_variant_id = nil %}{% if zp_intgrt_wboldqtbrk and zp_integrate_with_bold_custom_pricing%}{% assign zp_basic_variant_ids = zp_basic_variant_ids | split: ',' %}{% assign zp_bold_variant_ids = zp_bold_variant_ids | split: ',' %}{% assign zp_bold_variant_id = nil %}{% assign zp_variant_id = '' | append: zp_cross_sell_first_selected_variant.id %}{% for zp_prd_variant in zp_bold_variant_ids %}{% if zp_variant_id == zp_prd_variant %}{% assign zp_bold_variant_id = zp_basic_variant_ids[forloop.index0] %}{% break %}{% endif %}{% endfor %}{% if zp_bold_variant_id and zp_bold_variant_id != zp_variant_id %}{% assign zp_break_iterator = false %}{% for zp_prd_variant in zp_cross_sell_product.variants %}{% if zp_break_iterator %}{% break %}{% endif %}{% assign zp_prd_variant_id = '' | append: zp_prd_variant.id %}{% if zp_prd_variant_id == zp_bold_variant_id %}{% assign zp_break_iterator = true %}{% if zp_intgrt_wboldqtbrk %}{% include 'bold-variant' with zp_prd_variant, hide_action: 'skip' %}{% endif %}{% assign zp_cross_sell_first_selected_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% endif %}{% endif %}{% assign zp_crcspr = zp_cross_sell_first_selected_variant.price %}{% if zp_csshcrlen or zp_cscrls_dt != blank %}{% if zp_cscrls_dt != blank %}{% assign zp_pcrldtl = zp_cscrls_dt %}{% assign zp_pcrlrvr = true %}{% else %}{% assign zp_pcrldtl = '1' | split: ',' %}{% assign zp_pcrlrvr = false %}{% endif %}{% for zp_pcrldti in zp_pcrldtl %}{% if zp_pcrlrvr %}{% assign zp_csshcrlid = zp_pcrldti['spcid'] %}{% assign zp_csshcrlmfid = zp_pcrldti['spcpid'] %}{% assign zp_pcrshsrs = '' | append: zp_pcrldti['spsrcs'] %}{% assign zp_pcrlbmkr = zp_csblkmkr | append: '_' | append: zp_csshcrlid %}{% else %}{% assign zp_pcrlbmkr = zp_csblkmkr %}{% endif %}{% capture zp_cdvdbgn %}<zpcspcim_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zpcspcim_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{% assign zp_product_shopify_carousel_image_template = zp_tpsct[0] %}{% capture zp_cdvdbgn %}<zpcspcv_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zpcspcv_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{% assign zp_product_shopify_carousel_video_template = zp_tpsct[0] %}{% capture zp_cdvdbgn %}<zpcspcti_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zpcspcti_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{% assign zp_product_shopify_carousel_thumbnail_image_template = zp_tpsct[0] %}{% capture zp_cdvdbgn %}<zpcspctv_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zpcspctv_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{% assign zp_product_shopify_carousel_thumbnail_video_template = zp_tpsct[0] %}{% capture zp_csshcrlmdtpl %}<zpcsctw_{{ zp_pcrlbmkr }}>{% capture zp_cdvdbgn %}<zpcsctw_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zpcsctw_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{{ zp_tpsct[0] }}</zpcsctw_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_csshcrltmbtpl %}<zpcsctwt_{{ zp_pcrlbmkr }}>{% capture zp_cdvdbgn %}<zpcsctwt_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_cdvden %}</zpcsctwt_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_tpfct = zp_current_entity_content | split: zp_cdvdbgn %}{% assign zp_tpsct = zp_tpfct[1] | split: zp_cdvden %}{{ zp_tpsct[0] }}</zpcsctwt_{{ zp_pcrlbmkr }}>{% endcapture %}{% capture zp_csshcrlmds %}{% assign zp_src_sizes_cut = zp_pcrshsrs | split: ',' %}{% if zp_cross_sell_product.media.size > 0 %}{% if zp_cross_sell_first_selected_variant.available and zp_cross_sell_first_selected_variant.featured_media != blank %}{% case zp_cross_sell_first_selected_variant.featured_media.media_type %}{% when 'image' %}{% assign zp_imgsml = zp_cross_sell_first_selected_variant.featured_media | img_url: '100x100' %}{% assign zp_imgorg = zp_cross_sell_first_selected_variant.featured_media | img_url: 'original' %}{% assign zp_imgalt = zp_cross_sell_first_selected_variant.featured_media.alt | escape %}{% capture zp_srcszimgs %}{% for zp_srcszct in zp_src_sizes_cut %}{{ zp_cross_sell_first_selected_variant.featured_media | img_url: zp_srcszct }} {{ zp_srcszct | replace: 'x', 'w' }}{% unless forloop.last %},{% endunless %}{% endfor %}{% endcapture %}{{ zp_product_shopify_carousel_image_template | replace: 'zps_medwid', zp_cross_sell_first_selected_variant.featured_media.width| replace: 'zps_medheig', zp_cross_sell_first_selected_variant.featured_media.height | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medimurl', zp_imgorg | replace: 'zps_medalt', zp_imgalt | replace: 'zps_medimgsrc', zp_srcszimgs }}{% when 'external_video' %}{% assign video = zp_cross_sell_first_selected_variant.featured_media | external_video_tag %}{{ zp_product_shopify_carousel_video_template | replace: 'zps_medvid', video }}{% when 'video' %}{% assign video = zp_cross_sell_first_selected_variant.featured_media | video_tag: controls: true %}{{ zp_product_shopify_carousel_video_template | replace: 'zps_medvid', video }}{% endcase %}{% endif %}{% for zp_product_media in zp_cross_sell_product.media %}{% if zp_product_media.id == zp_cross_sell_first_selected_variant.featured_media.id %}{% continue %}{% endif %}{% case zp_product_media.media_type %}{% when 'image' %}{% assign zp_imgsml = zp_product_media | img_url: '100x100' %}{% assign zp_imgorg = zp_product_media | img_url: 'original' %}{% assign zp_imgalt = zp_product_media.alt | escape %}{% capture zp_srcszimgs %}{% for zp_srcszct in zp_src_sizes_cut %}{{ zp_product_media | img_url: zp_srcszct }} {{ zp_srcszct | replace: 'x', 'w' }}{% unless forloop.last %},{% endunless %}{% endfor %}{% endcapture %}{{ zp_product_shopify_carousel_image_template | replace: 'zps_medwid', zp_product_media.width| replace: 'zps_medheig', zp_product_media.height | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medimurl', zp_imgorg | replace: 'zps_medalt', zp_imgalt | replace: 'zps_medimgsrc', zp_srcszimgs }}{% when 'external_video' %}{% assign video = zp_product_media | external_video_tag %}{{ zp_product_shopify_carousel_video_template | replace: 'zps_medvid', video }}{% when 'video' %}{% assign video = zp_product_media | video_tag: controls: true %}{{ zp_product_shopify_carousel_video_template | replace: 'zps_medvid', video }}{% endcase %}{% endfor %}{% else %}{% assign zp_imgsml = zp_cross_sell_product.media | img_url: '100x100' %}{% assign zp_imgorg = zp_cross_sell_product.media | img_url: 'original' %}{% assign zp_imgalt = zp_cross_sell_product.media.alt | escape %}{% capture zp_srcszimgs %}{% for zp_srcszct in zp_src_sizes_cut %}{{ zp_cross_sell_product.media | img_url: zp_srcszct }} {{ zp_srcszct | replace: 'x', 'w' }}{% unless forloop.last %},{% endunless %}{% endfor %}{% endcapture %}{{ zp_product_shopify_carousel_image_template | replace: 'zps_medwid', zp_cross_sell_product.media.width| replace: 'zps_medheig', zp_cross_sell_product.media.height | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medimurl', zp_imgorg | replace: 'zps_medalt', zp_imgalt | replace: 'zps_medimgsrc', zp_srcszimgs }}{% endif %}{% endcapture %}{% capture zp_csshcrltmb %}{% assign zp_media_position = 0 %}{% if zp_cross_sell_product.media.size > 0 %}{% if zp_cross_sell_first_selected_variant.available and zp_cross_sell_first_selected_variant.featured_media != blank %}{% case zp_cross_sell_first_selected_variant.featured_media.media_type %}{% when 'image' %}{% assign zp_imgsml = zp_cross_sell_first_selected_variant.featured_media | img_url: '100x100' %}{% assign zp_imgalt = zp_cross_sell_first_selected_variant.featured_media.alt | escape %}{{ zp_product_shopify_carousel_thumbnail_image_template | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medpos', zp_media_position | replace: 'zps_medalt', zp_imgalt }}{% when 'external_video' %}{{ zp_product_shopify_carousel_thumbnail_video_template | replace: 'zps_medpos', zp_media_position }}{%when 'video' %}{{ zp_product_shopify_carousel_thumbnail_video_template | replace: 'zps_medpos', zp_media_position }}{% endcase %}{% assign zp_media_position = zp_media_position | plus: 1 %}{% endif %}{% for zp_product_media in zp_cross_sell_product.media %}{% if zp_product_media.id == zp_cross_sell_first_selected_variant.featured_media.id %}{% continue %}{% endif %}{% case zp_product_media.media_type %}{% when 'image' %}{% assign zp_imgsml = zp_product_media | img_url: '100x100' %}{% assign zp_imgalt = zp_product_media.alt | escape %}{{ zp_product_shopify_carousel_thumbnail_image_template | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medpos', zp_media_position | replace: 'zps_medalt', zp_imgalt }}{% when 'external_video' %}{{ zp_product_shopify_carousel_thumbnail_video_template | replace: 'zps_medpos', zp_media_position }}{%when 'video' %}{{ zp_product_shopify_carousel_thumbnail_video_template | replace: 'zps_medpos', zp_media_position }}{% endcase %}{% assign zp_media_position = zp_media_position | plus: 1 %}{% endfor %}{% else %}{% assign zp_imgsml = zp_cross_sell_product.media | img_url: '100x100' %}{% assign zp_imgalt = zp_cross_sell_product.media.alt | escape %}{{ zp_product_shopify_carousel_thumbnail_image_template | replace: 'zps_medsurl', zp_imgsml | replace: 'zps_medpos', zp_media_position | replace: 'zps_medalt', zp_imgalt }}{% endif %}{% endcapture %}{% capture zp_csshcrljsn %}{% assign zp_open_bracket = "{" %}{% assign zp_close_bracket = "}" %}{% assign zp_need_add_comma = false %}<script type="application/json" data-zp-shopify-carousel="{{ zp_csshcrlid }}">{{ zp_open_bracket }}"id":{{ '' | append: zp_csshcrlid | json }},"mfid":{{ zp_csshcrlmfid | json }},"role":"shopify_carousel","media":[{% if zp_cross_sell_product.media.size > 0 %}{% if zp_cross_sell_first_selected_variant.available and zp_cross_sell_first_selected_variant.featured_media != blank %}{% assign zp_need_add_comma = true %}{{ zp_open_bracket }}{% case zp_cross_sell_first_selected_variant.featured_media.media_type %}{% when 'image' %}"id":"{{ zp_cross_sell_first_selected_variant.featured_media.id }}","type":"image","src":"{{ zp_cross_sell_first_selected_variant.featured_media | img_url: 'original' }}","alt":{{ '' | append: zp_cross_sell_first_selected_variant.featured_media.alt | escape | json }}{% when 'external_video' %}"id":"{{ zp_cross_sell_first_selected_variant.featured_media.id }}","type":"video","content":"{{ zp_cross_sell_first_selected_variant.featured_media | external_video_tag | escape }}"{% when 'video' %}"id":"{{ zp_cross_sell_first_selected_variant.featured_media.id }}","type":"video","content":"{{ zp_cross_sell_first_selected_variant.featured_media | video_tag: controls: true | escape }}"{% endcase %}{{ zp_close_bracket }}{% endif %}{% for zp_product_media in zp_cross_sell_product.media %}{% if zp_product_media.id == zp_cross_sell_first_selected_variant.featured_media.id %}{% continue %}{% endif %}{% if zp_need_add_comma %},{% endif %}{{ zp_open_bracket }}{% case zp_product_media.media_type %}{% when 'image' %}"id":"{{ zp_product_media.id }}","type":"image","src":"{{ zp_product_media | img_url: 'original' }}","alt":{{ '' | append: zp_product_media.alt | escape | json }}{% when 'external_video' %}"id":"{{ zp_product_media.id }}","type":"video","content":"{{ zp_product_media | external_video_tag | escape }}"{% when 'video' %}"id":"{{ zp_product_media.id }}","type":"video","content":"{{ zp_product_media | video_tag: controls: true | escape }}"{% endcase %}{{ zp_close_bracket }}{% assign zp_need_add_comma = true %}{% endfor %}{% else %}{{ zp_open_bracket }}"id":"{{ zp_cross_sell_product.media.id }}","type":"image_default","src":"{{ zp_cross_sell_product.media | img_url: 'original' }}","alt":{{ '' | append: zp_cross_sell_product.media.alt | escape | json }}{{ zp_close_bracket }}{% endif %}]{{ zp_close_bracket }}</script>{% endcapture %}{% if zp_csshcrltmbtpl != blank %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_csshcrltmbtpl, zp_csshcrltmb %}{% endif %}{% if zp_csshcrlmdtpl != blank %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_csshcrlmdtpl, zp_csshcrlmds %}{% endif %}{% capture zp_pcrlbmkr %}<zpcsscj_{{ zp_pcrlbmkr }}></zpcsscj_{{ zp_pcrlbmkr }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_pcrlbmkr, zp_csshcrljsn %}{% endfor %}{% assign zp_pcrldtl = nil %}{% endif %}{% if zp_any_cross_sell_variant_available %}{% assign zp_csavlbl = true %}{% assign zp_uncscls = '' %}{% assign zp_csunvbdsctcl = '' %}{% assign zp_acspct = zp_acspct | plus: 1 %}{% if zp_csimgsdt != blank %}{% assign zp_img_tps = zp_csimgsdt | map: 'tp' %}{% assign zp_umltpimgs = true %}{% else %}{% assign zp_img_tps = zp_csimgtp | split: ',' %}{% assign zp_umltpimgs = false %}{% endif %}{% for zp_img_tp in zp_img_tps %}{% assign zp_cross_sell_featured_image = zp_cross_sell_product.featured_image %}{% assign zp_cross_sell_default_image = zp_cross_sell_featured_image | img_url: 'master' %}{% assign zp_first_selected_variant_image_id = '' | append: zp_cross_sell_first_selected_variant.image.id | strip %}{% if zp_img_tp == 'variant' and zp_shw_vrntsslctr == true and zp_first_selected_variant_image_id.size > 0 %}{% assign zp_cross_sell_featured_image = zp_cross_sell_first_selected_variant.image %}{% endif %}{% assign zp_cross_sell_image_srcset_available = true %}{% assign zp_cross_sell_image_sizes_data = '1080:--:280x' %}{% assign zp_product_image_sizes_data_parts = '' | append: zp_cross_sell_image_sizes_data | split: ':|~|:' %}{% assign zp_product_image_src_set_string = '' %}{% assign zp_product_image_sizes_list_string = '' %}{% assign zp_product_image_sizes_json_string = '' %}{% assign zp_csimgsz = nil %}{% assign zp_first_image_screen_size = 0 %}{% assign zp_last_image_screen_size = 0 %}{% for zp_image_size_data in zp_product_image_sizes_data_parts %}{% assign zp_image_size_data_parts = '' | append: zp_image_size_data | split: ':--:' %}{% assign zp_image_screen_size = zp_image_size_data_parts[0] %}{% assign zp_image_resize_filter = zp_image_size_data_parts[1] %}{% if forloop.first == true %}{% assign zp_first_image_screen_size = 0 | plus: zp_image_screen_size %}{% elsif forloop.last == true %}{% assign zp_last_image_screen_size = 0 | plus: zp_image_screen_size %}{% endif %}{% if zp_image_screen_size.size > 0 and zp_image_resize_filter.size > 0 %}{% assign zp_product_image_url_with_filter = zp_cross_sell_featured_image | img_url: zp_image_resize_filter %}{% assign zp_image_screen_size_width = zp_image_screen_size | append: 'w' %}{% assign zp_product_image_src_set_string = zp_product_image_src_set_string | append: zp_product_image_url_with_filter | append: ' ' | append: zp_image_screen_size_width | append: '__zp1502150400__' %}{% assign zp_product_image_sizes_list_string = zp_product_image_sizes_list_string | append: zp_image_screen_size | append: 'px' | append: '__zp1502150400__' %}{% assign zp_image_screen_size_width_json = zp_image_screen_size_width | json %}{% assign zp_image_resize_filter_json = zp_image_resize_filter | json %}{% assign zp_product_image_sizes_json_string = zp_product_image_sizes_json_string | append: '{"filter":' | append: zp_image_resize_filter_json | append: ',"screen":' | append: zp_image_screen_size_width_json | append: '}' | append: '__zp1502150400__' %}{% unless zp_csimgsz %}{% assign zp_csimgsz = zp_image_resize_filter %}{% endunless %}{% endif %}{% endfor %}{% unless zp_csimgsz %}{% assign zp_csimgsz = '1080x' %}{% endunless %}{% assign zp_cross_sell_image_src_set = zp_product_image_src_set_string | split: '__zp1502150400__' %}{% assign zp_csimgszjson = zp_product_image_sizes_json_string | split: '__zp1502150400__' %}{% if zp_first_image_screen_size > zp_last_image_screen_size %}{% assign zp_cross_sell_image_src_set = zp_cross_sell_image_src_set | reverse %}{% assign zp_csimgszjson = zp_csimgszjson | reverse %}{% endif %}{% assign zp_cross_sell_image_src_set = zp_cross_sell_image_src_set | join: ', ' %}{% assign zp_cross_sell_image_sizes_list = zp_product_image_sizes_list_string | split: '__zp1502150400__' | join: ',' %}{% assign zp_csimgszjson = zp_csimgszjson | join: ',' | prepend: '[' | append: ']' %}{% assign zp_csimg = zp_cross_sell_featured_image | img_url: zp_csimgsz %}{% if zp_img_tp == 'custom' %}{% assign zp_cross_sell_image_lazy_load_available = false %}{% assign zp_cross_sell_image_srcset_available = false %}{% assign zp_cross_sell_image_srcset_sizes_available = false %}{% else %}{% assign zp_cross_sell_image_lazy_load_available = true %}{% assign zp_cross_sell_image_srcset_sizes_available = true %}{% endif %}{% capture zp_csimgdatr %}{% if zp_csimglzld and zp_current_entity_use_native_lazyloading == false %}{% assign zp_product_image_load_attr_prefix = 'data-' %}{% else %}{% assign zp_product_image_load_attr_prefix = '' %}{% endif %}{% if zp_csimglzld and zp_cross_sell_image_lazy_load_available %} data-lowsrc="{{ zp_cross_sell_featured_image | img_url: '280x' }}" width="{{ zp_cross_sell_featured_image.width }}" height="{{ zp_cross_sell_featured_image.height }}" data-parent-fit="cover" data-aspectratio="16/9" {% if zp_current_entity_use_native_lazyloading %}decoding="async" loading="lazy" src="{{ zp_cross_sell_featured_image | img_url: 'original' }}"{% endif %}{% endif %}{% if zp_cross_sell_image_srcset_sizes_available %} {% if zp_csimglzld and zp_cross_sell_image_lazy_load_available %}data-sizes="auto" sizes="{{ zp_cross_sell_image_sizes_list }}"{% else %}sizes="{{ zp_cross_sell_image_sizes_list }}"{% endif %}{% endif %}{% if zp_cross_sell_image_srcset_available and zp_csldmnimg %} {{ zp_product_image_load_attr_prefix }}srcset="{{ zp_cross_sell_image_src_set }}"{% endif %} data-zp-{% if zp_bndblen %}bundle-product{% else %}cross-sell{% endif %}-image data-zp-{% if zp_bndblen %}bundle-product{% else %}cross-sell{% endif %}-image-type="{{ zp_img_tp }}" data-zp-image-id="{{ zp_cross_sell_featured_image.id }}" data-zp-default-image="{{ zp_cross_sell_default_image }}"{% endcapture %}{% if zp_umltpimgs %}{% capture zp_img_prp %}zps_mcsimgdt_{{ zp_csblkmkr }}_{{ zp_csimgsdt[forloop.index0]['id'] }}_{% endcapture %}{% capture zp_img_alprpn %}zps_csimgalt_{{ zp_csblkmkr }}_{{ zp_csimgsdt[forloop.index0]['id'] }}_{% endcapture %}{% assign zp_img_alprpv = zp_cross_sell_featured_image.alt | strip_html | strip_newlines | escape %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_img_prp, zp_csimgdatr | replace: zp_img_alprpn, zp_img_alprpv %}{% endif %}{% endfor %}{% if zp_cross_sell_first_selected_variant.compare_at_price > zp_cross_sell_first_selected_variant.price %}{% assign zp_crcscapr = zp_cross_sell_first_selected_variant.compare_at_price %}{% else %}{% assign zp_crcscapr = nil %}{% endif %}{% assign zp_csdsctam_recalculate = true %}{% assign zp_csdsctam_money = false %}{% assign zp_csdsctam_prefix = '' %}{% assign zp_csdsctam_suffix = '' %}{% if zp_csdsctdt contains ':' %}{% assign zp_csdsctdt = zp_csdsctdt | split: ':' %}{% assign zp_split_discount_data = true %}{% else %}{% assign zp_split_discount_data = false %}{% endif %}{% if zp_csdsctdt.size > 1 %}{% assign zp_csdsctam = 0 | plus: zp_csdsctdt[0] | abs %}{% assign zp_product_discount_type = '' | append: zp_csdsctdt[1] %}{% if zp_product_discount_type == 'percentage' or zp_product_discount_type == 'percentage_dynamic' %}{% assign zp_product_discount_amount_parts = '' | append: zp_csdsctam | split: '.' %}{% assign zp_product_discount_decimal_part = '' | append: zp_product_discount_amount_parts[1] | default: '0' %}{% assign zp_product_discount_decimal_part = 0 | plus: zp_product_discount_decimal_part %}{% unless zp_product_discount_decimal_part > 0 %}{% assign zp_csdsctam = zp_product_discount_amount_parts[0] %}{% endunless %}{% assign zp_csdsctam = 0 | plus: zp_csdsctam | at_most: 100 %}{% assign zp_cssvam = zp_crcspr | times: zp_csdsctam | divided_by: 100 | at_most: zp_crcspr %}{% assign zp_csdsctam_recalculate = false %}{% assign zp_csdsctam_suffix = '%' %}{% elsif zp_product_discount_type == 'compare_price' %}{% if zp_crcscapr %}{% assign zp_csdsctam = zp_crcscapr | minus: zp_crcspr %}{% else %}{% assign zp_csdsctam = 0 %}{% assign zp_csunvbdsctcl = 'hidden' %}{% endif %}{% assign zp_cssvam = zp_csdsctam %}{% else %}{% assign zp_csdsctam = zp_csdsctam | times: 100 %}{% assign zp_csdsctam = '' | append: zp_csdsctam | split: '.' | first | default: 0 %}{% assign zp_csdsctam = 0 | plus: zp_csdsctam | at_most: zp_crcspr %}{% assign zp_cssvam = zp_csdsctam %}{% assign zp_csdsctam_money = true %}{% endif %}{% if zp_product_discount_type == 'compare_price' %}{% assign zp_csslpr = zp_crcspr %}{% else %}{% assign zp_csslpr = zp_crcspr | minus: zp_cssvam %}{% endif %}{% assign zp_csdsctam_prefix = '-' %}{% else %}{% assign zp_csslpr = zp_crcspr %}{% assign zp_csunvbdsctcl = 'hidden' %}{% assign zp_csdsctam = 0 %}{% endif %}{% if zp_split_discount_data %}{% assign zp_csdsctdt = zp_csdsctdt | join: ':' %}{% endif %}{% if zp_product_calculate_bundle_price %}{% assign zp_crcspr = zp_crcspr | times: zp_csdftqty %}{% if zp_crcscapr %}{% assign zp_crcscapr = zp_crcscapr | times: zp_csdftqty %}{% endif %}{% assign zp_csslpr = zp_csslpr | times: zp_csdftqty %}{% if zp_cssvam %}{% assign zp_cssvam = zp_cssvam | times: zp_csdftqty %}{% endif %}{% if zp_csdsctam_recalculate %}{% assign zp_csdsctam = zp_csdsctam | times: zp_csdftqty %}{% endif %}{% endif %}{% assign zp_fmcspr = zp_crcspr | money %}{% assign zp_fmcscapr = zp_crcscapr | money %}{% assign zp_fmcsslpr = zp_csslpr | money %}{% if zp_csdsctam_money %}{% assign zp_fmcsdsctam = zp_csdsctam | money %}{% else %}{% assign zp_fmcsdsctam = zp_csdsctam %}{% endif %}{% assign zp_csdsctamwsfx = zp_fmcsdsctam | append: zp_csdsctam_suffix %}{% assign zp_fmcsdsctam = zp_fmcsdsctam | prepend: zp_csdsctam_prefix | append: zp_csdsctam_suffix %}{% if zp_cssvam %}{% assign zp_fmcsdsctsvam = zp_cssvam | money %}{% endif %}{% if zp_bndblen %}{% assign zp_csaflatr = 'bundle-product' %}{% else %}{% assign zp_csaflatr = 'cross-sell' %}{% endif %}{% capture zp_csvrntcntr %}{% if zp_shw_vrntsslctr %}<select id="{{ zp_cross_sell_selector }}" class="hidden noreplace" data-productid="{{ zp_cross_sell_product.id }}" data-zp-{{ zp_csaflatr }}-field="id">{% assign zp_first_selected_variant_id = '' | append: zp_cross_sell_first_selected_variant.id %}{% for zp_cross_sell_variant in zp_current_cross_sell_custom_variants %}{% assign zp_variant_id = '' | append: zp_cross_sell_variant.id %}{% unless zp_csslcvrns contains zp_variant_id %}{% continue %}{% endunless %}{% if zp_cross_sell_product.has_only_default_variant %}{% assign zp_csvnm = zp_cross_sell_product.title %}{% else %}{% assign zp_csvnm = zp_cross_sell_product.title | append: ' - ' | append: zp_cross_sell_variant.title %}{% endif %}{% if zp_cross_sell_variant.available %}{% if zp_first_selected_variant_id == zp_variant_id %}{% assign zp_option_selected_attr = 'selected="selected" ' %}{% else %}{% assign zp_option_selected_attr = '' %}{% endif %}<option {{ zp_option_selected_attr }}value="{{ zp_cross_sell_variant.id }}" data-zp-var-name="{{ zp_csvnm | escape }}">{{ zp_cross_sell_variant.title }} - {{ zp_cross_sell_variant.price | money_with_currency }}</option>{% else %}<option disabled="disabled" value="{{ zp_cross_sell_variant.id }}" data-zp-var-name="{{ zp_csvnm | escape }}">{{ zp_cross_sell_variant.title }} - {% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}</option>{% endif %}{% endfor %}</select>{% endif %}{% endcapture %}{% if zp_unit_price_enabled %}{% if zp_csuntprtginc %}{% if zp_cross_sell_first_selected_variant.unit_price_measurement %}{% assign zp_csupwrprcls = '' %}{% else %}{% assign zp_csupwrprcls = ' hidden' %}{% endif %}{% capture zp_csuplbl %}{% assign zp_translation = 'products.product.unit_price_label' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'products.product.price.unit_price' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Unit Price" %}{% endif %}{% endif %}{{ zp_translation }}{% endcapture %}{% capture zp_csupblck %}<span data-zp-cross-sell-unit-price>{% if zp_cross_sell_first_selected_variant.unit_price_measurement %}{{ zp_cross_sell_first_selected_variant.unit_price | money }}{% endif %}</span><span> / </span><span data-zp-cross-sell-up-base-unit>{% if zp_cross_sell_first_selected_variant.unit_price_measurement %}{% if zp_cross_sell_first_selected_variant.unit_price_measurement.reference_value != 1 %}{{ zp_cross_sell_first_selected_variant.unit_price_measurement.reference_value }}{% endif %}{{ zp_cross_sell_first_selected_variant.unit_price_measurement.reference_unit }}{% endif %}</span>{% endcapture %}{% else %}{% capture zp_csupblck %}{% if zp_unit_price_enabled %}<div class="zpa-offset-bottom-xs{% unless zp_cross_sell_first_selected_variant.unit_price_measurement %} hidden{% endunless %}" data-zp-cross-sell-unit-price-wrapper><span data-zp-cross-sell-unit-price>{% if zp_cross_sell_first_selected_variant.unit_price_measurement %}{{ zp_cross_sell_first_selected_variant.unit_price | money }}{% endif %}</span><span> / </span><span data-zp-cross-sell-up-base-unit>{% if zp_cross_sell_first_selected_variant.unit_price_measurement %}{% if zp_cross_sell_first_selected_variant.unit_price_measurement.reference_value != 1 %}{{ zp_cross_sell_first_selected_variant.unit_price_measurement.reference_value }}{% endif %}{{ zp_cross_sell_first_selected_variant.unit_price_measurement.reference_unit }}{% endif %}</span></div>{% endif %}{% endcapture %}{% assign zp_csvrntcntr = zp_csupblck | append: zp_csvrntcntr %}{% endif %}{% endif %}{% if zp_csshwqty %}{% assign zp_uncsqtycls = '' %}{% else %}{% assign zp_uncsqtycls = 'hidden' %}{% endif %}{% capture zp_csqtycntr %}{% if zp_bndblen %}{% assign zp_csqtyfatr = 'bundle-product' %}{% else %}{% assign zp_csqtyfatr = 'cross-sell' %}{% endif %}{% if zp_csshwqty %}{% unless zp_csshwqtylbl == false %}<label for="{{ zp_cross_sell_selector }}-quantity">{% assign zp_translation = 'products.product.quantity.label' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'products.product.quantity' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Quantity" %}{% endif %}{% endif %}{% assign zp_last_translation_char = zp_translation | strip | split: '' | last %}{% if zp_last_translation_char == ':' %}{{ zp_translation }}{% else %}{{ zp_translation }}:{% endif %}</label>{% endunless %}<div class="zpa-quantity-block" data-zp-{{ zp_csqtyfatr }}-quantity-el data-product-wrapper-id="{{ zp_cselid }}"><button class="zpa-btn-custom zpa-quantity-btn" type="button" title="{% assign zp_translation = 'products.product.quantity.decrease' | t: product: zp_cross_sell_product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Decrease quantity" %}{% endif %}{{ zp_translation }}" data-zp-{{ zp_csqtyfatr }}-decrease-qty><svg class="zpa-quantity-icon" width="14px" height="14px"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#iconDecrease"></use></svg></button><input id="{{ zp_cross_sell_selector }}-quantity" class="zpa-quantity-field xs" value="{{ zp_csdftqty }}" maxlength="3" size="3" type="tel" inputmode="numeric" required pattern="^[1-9][0-9]*" title="{% assign zp_translation = 'products.product.quantity.input_label' | t: product: zp_cross_sell_product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Quantity" %}{% endif %}{{ zp_translation }}" data-zp-{{ zp_csqtyfatr }}-quantity data-zp-{{ zp_csqtyfatr }}-field="quantity"><button class="zpa-btn-custom zpa-quantity-btn" type="button" title="{% assign zp_translation = 'products.product.quantity.increase' | t: product: zp_cross_sell_product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Increase quantity" %}{% endif %}{{ zp_translation }}" data-zp-{{ zp_csqtyfatr }}-increase-qty><svg class="zpa-quantity-icon" width="14px" height="14px"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#iconIncrease"></use></svg></button></div>{% else %}<input id="{{ zp_cross_sell_selector }}-quantity" type="hidden" value="{{ zp_csdftqty }}" data-zp-{{ zp_csqtyfatr }}-quantity data-zp-{{ zp_csqtyfatr }}-field="quantity">{% endif %}{% endcapture %}{% capture zp_csmsgs %}{% if zp_cross_sell_first_selected_variant.available %}{% if zp_bndblen %}{% assign zp_cross_sell_inventory_message_attr = 'bundle-product' %}{% else %}{% assign zp_cross_sell_inventory_message_attr = 'cross-sell' %}{% endif %}<div data-zp-{{ zp_cross_sell_inventory_message_attr }}-inventory-messages class="zpa-{{ zp_csaflatr }}-sold-out__label zpa-text-center zpa-font-b zpa-text-uppercase zpa-offset-top-xs hidden"></div>{% else %}{% if zp_bndblen %}{% assign zp_cross_sell_inventory_message_attr = 'bundle-product' %}{% else %}{% assign zp_cross_sell_inventory_message_attr = 'cross-sell' %}{% endif %}<div data-zp-{{ zp_cross_sell_inventory_message_attr }}-inventory-messages class="zpa-{{ zp_csaflatr }}-sold-out__label zpa-text-center zpa-font-b zpa-text-uppercase zpa-offset-top-xs">{% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}</div>{% endif %}{% if zp_shw_vrntsslctr %}<div class="zpa-product-message-wrap"><span class="zpa-product-message zpa-{% if zp_bndblen %}bundle-product{% else %}cross-sell-product{% endif %}--message hidden" data-zp-{{ zp_csaflatr }}-messages data-zp-success-message="{{ zp_patcm }}"></span></div>{% endif %}{% endcapture %}{% if zp_bndblen %}{% assign zp_cssdd = zp_pdstds[zp_csblkmkr] %}{% if zp_cssdd != blank and zp_cpstdd['only_cross_sell'] == blank %}{% assign zp_csatnlclscnt = zp_csatnlclscnt | append: '<input type="hidden" data-zp-bundle-product-discount-field="discount" value="' | append: zp_cssdd['code'] | append: '">' %}{% endif %}{% endif %}{% else %}{% capture zp_csmsgs %}{% if zp_bndblen %}{% assign zp_cross_sell_inventory_message_attr = 'bundle-product' %}{% else %}{% assign zp_cross_sell_inventory_message_attr = 'cross-sell' %}{% endif %}<div data-zp-{{ zp_cross_sell_inventory_message_attr }}-inventory-messages class="zpa-{{ zp_csaflatr }}-sold-out__label zpa-text-center zpa-font-b zpa-text-uppercase zpa-offset-top-xs">{% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}</div>{% endcapture %}{% endif %}{% capture zp_csjsnincnt %}{% if zp_intgrt_wboldqtbrk %}{% capture zp_product_json %}{% include 'bold-product', product: zp_cross_sell_product, output: 'json' %}{% endcapture %}{% if zp_product_json contains 'Could not find asset ' %}{{ zp_cross_sell_product | json | default: 'null' }}{% else %}{{ zp_product_json | strip | default: 'null' }}{% endif %}{% assign zp_product_json = nil %}{% else %}{{ zp_cross_sell_product | json | default: 'null' }}{% endif %}{% endcapture %}{% capture zp_cscfgpr %}{"product":{{ zp_csjsnincnt }}{% if zp_csslvrnlst != '' %},"selectedVariants":"{{ zp_csslvrnlst | url_escape }}"{% endif %},"discountData":"{{ zp_csdsctdt }}","selector":"{{ zp_cross_sell_selector }}","available":{{ zp_csavlbl }},"blockId":"{{ zp_csblcid | default: '' }}","elementId":"{{ zp_cselid | default: '' }}","imageSize":"{{ '' | append: zp_csimgsz | strip | default: 'master' | url_escape }}","imageSrcSetData":{{ zp_csimgszjson }}{% if zp_csshwvrnlbl != nil %},"showVariantLabel":"{{ zp_csshwvrnlbl }}"{% endif %}{% if zp_csvrntjsndt == blank %},"variantSelectorView":"{{ zp_csvrntslvw }}"{% endif %}{% if zp_csvrntjsndt != blank %},"variantSelectors":{{ zp_csvrntjsndt }}{% endif %}{% if zp_csimgsdt != blank %},"images":{{ zp_csimgsjsndt }}{% endif %},"defaultQty":{{ 0 | plus: zp_csdftqty }}{% if zp_csoptstgs != blank %},"optionsSettings":{{ zp_csoptstgs }}{% endif %}{% if zp_csvrntslctrcls != blank %},"variantLabelClasses":"zp {{ zp_csvrntslctrcls }}"{% endif %}{% if zp_csvrntslofscls != blank %},"variantOffsetClasses":"{{ zp_csvrntslofscls }}"{% endif %}{% if zp_crvsrc != blank %},"reviewsSource":{{ zp_crvsrc | json }}{% endif %}{% if zp_csrvdt != blank %},"starRatings":{{ zp_csrvdt }}{% endif %} }{% endcapture %}{% if zp_cscfg.size > 1 %}{% assign zp_cscfgprspr = ',' %}{% else %}{% assign zp_cscfgprspr = '' %}{% endif %}{% assign zp_cscfg = '' | append: zp_cscfg | append: zp_cscfgprspr | append: zp_cscfgpr %}{% assign zp_cscfgpr = nil %}{% else %}{% assign zp_cross_sell_product = nil %}{% capture zp_csmsgs %}{% if zp_bndblen %}{% assign zp_cross_sell_inventory_message_attr = 'bundle-product' %}{% else %}{% assign zp_cross_sell_inventory_message_attr = 'cross-sell' %}{% endif %}<div data-zp-{{ zp_cross_sell_inventory_message_attr }}-inventory-messages class="zpa-{{ zp_csaflatr }}-sold-out__label zpa-text-center zpa-font-b zpa-text-uppercase zpa-offset-top-xs">{% assign zp_translation = 'products.product.unavailable' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Unavailable" %}{% endif %}{{ zp_translation }}</div>{% endcapture %}{% if zp_bndblen %}{% if zp_cscfg.size > 1 %}{% assign zp_cscfgprspr = ',' %}{% else %}{% assign zp_cscfgprspr = '' %}{% endif %}{% assign zp_cscfg = '' | append: zp_cscfg | append: zp_cscfgprspr | append: '{"product":null,"selector":"' | append: zp_cross_sell_selector | append: '","elementId":"' | append: zp_cselid | append: '"}' %}{% endif %}{% endif %}{% if zp_cssttngs contains 'srtgs' %}{% assign zp_rvwsrc = zp_cssttngs['srtgs'] | map: 'rtgsrc' | uniq %}{% assign zp_cptr_rplc_src = true %}{% else %}{% assign zp_rvwsrc = zp_cssttngs['rtgsrc'] | split: ' ' %}{% assign zp_cptr_rplc_src = false %}{% endif %}{% for zp_rvs_src in zp_rvwsrc %}{% if zp_rvs_src == blank %}{% continue %}{% endif %}{% assign zp_csrvrtg = nil %}{% assign zp_csrvcnt = nil %}{% assign zp_csrvrtngtxt = '' %}{% if zp_cross_sell_product.id > 0 %}{% case zp_rvs_src %}{% when 'yotpo' %}{% assign zp_prvwdt = zp_cross_sell_product.metafields.yotpo %}{% assign zp_prvwcnt = 0 | plus: zp_prvwdt.reviews_count %}{% if zp_prvwcnt > 0 %}{% assign zp_csrvrtg = 0 | plus: zp_prvwdt.reviews_average %}{% assign zp_csrvcnt = 0 | plus: zp_prvwdt.reviews_count %}{% endif %}{% when 'loox' %}{% assign zp_prvwdt = zp_cross_sell_product.metafields.loox %}{% assign zp_prvwcnt = 0 | plus: zp_prvwdt.num_reviews %}{% if zp_prvwcnt > 0 %}{% assign zp_csrvrtg = 0 | plus: zp_prvwdt.avg_rating %}{% assign zp_csrvcnt = 0 | plus: zp_prvwdt.num_reviews %}{% endif %}{% when 'spr' %}{% assign zp_prvwdt = zp_cross_sell_product.metafields.spr.reviews %}{% if zp_prvwdt %}{% assign zp_prvwdt = ' ' | append: zp_prvwdt | append: ' ' %}{% assign zp_prvwrtgdt = zp_prvwdt | split: '"ratingValue":' %}{% assign zp_prvwrtgdt = zp_prvwrtgdt[1] | strip | slice: 0, 100 | remove: '"' | remove: "'" | split: '' %}{% assign zp_prvwavchr = '1234567890.' | split: '' %}{% assign zp_csrvrtg = '' %}{% for zp_prvwrchr in zp_prvwrtgdt %}{% if zp_prvwavchr contains zp_prvwrchr %}{% assign zp_csrvrtg = zp_csrvrtg | append: zp_prvwrchr %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_csrvrtg = 0 | plus: zp_csrvrtg %}{% unless zp_csrvrtg > 0 %}{% assign zp_csrvrtg = nil %}{% endunless %}{% assign zp_prvwrtgdt = zp_prvwdt | split: '"reviewCount":' %}{% if zp_prvwrtgdt.size < 2 %}{% assign zp_prvwrtgdt = zp_prvwdt | split: '"ratingCount":' %}{% endif %}{% assign zp_prvwrtgdt = zp_prvwrtgdt[1] | strip | slice: 0, 100 | remove: '"' | remove: "'" | split: '' %}{% assign zp_prvwavchr = '1234567890' | split: '' %}{% assign zp_csrvcnt = '' %}{% for zp_prvwrchr in zp_prvwrtgdt %}{% if zp_prvwavchr contains zp_prvwrchr %}{% assign zp_csrvcnt = zp_csrvcnt | append: zp_prvwrchr %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_csrvcnt = 0 | plus: zp_csrvcnt %}{% unless zp_csrvcnt > 0 %}{% assign zp_csrvcnt = nil %}{% endunless %}{% endif %}{% when 'opinew' %}{% assign zp_prvwdt = zp_cross_sell_product.metafields.opinew_metafields %}{% assign zp_prvwcnt = 0 | plus: zp_prvwdt.reviews_count %}{% if zp_prvwcnt > 0 %}{% assign zp_csrvrtg = 0 | plus: zp_prvwdt.reviews_average %}{% assign zp_csrvcnt = 0 | plus: zp_prvwdt.reviews_count %}{% endif %}{% when 'judgeme' %}{% assign zp_prvwdt = zp_cross_sell_product.metafields.judgeme.badge %}{% if zp_prvwdt %}{% assign zp_prvwdt = ' ' | append: zp_prvwdt | append: ' ' %}{% assign zp_prvwrtgdt = zp_prvwdt | split: ' data-average-rating=' %}{% assign zp_prvwrtgdt = zp_prvwrtgdt[1] | strip | split: ' ' | first | strip | remove: '"' | remove: "'" | split: '' %}{% assign zp_prvwavchr = '1234567890.' | split: '' %}{% assign zp_csrvrtg = '' %}{% for zp_prvwrchr in zp_prvwrtgdt %}{% if zp_prvwavchr contains zp_prvwrchr %}{% assign zp_csrvrtg = zp_csrvrtg | append: zp_prvwrchr %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_csrvrtg = 0 | plus: zp_csrvrtg %}{% unless zp_csrvrtg > 0 %}{% assign zp_csrvrtg = nil %}{% endunless %}{% assign zp_prvwrtgdt = zp_prvwdt | split: ' data-number-of-reviews=' %}{% assign zp_prvwrtgdt = zp_prvwrtgdt[1] | strip | split: ' ' | first | strip | remove: '"' | remove: "'" | split: '' %}{% assign zp_prvwavchr = '1234567890' | split: '' %}{% assign zp_csrvcnt = '' %}{% for zp_prvwrchr in zp_prvwrtgdt %}{% if zp_prvwavchr contains zp_prvwrchr %}{% assign zp_csrvcnt = zp_csrvcnt | append: zp_prvwrchr %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_csrvcnt = 0 | plus: zp_csrvcnt %}{% unless zp_csrvcnt > 0 %}{% assign zp_csrvcnt = nil %}{% endunless %}{% endif %}{% endcase %}{% endif %}{% assign zp_prvwdt = nil %}{% assign zp_prvwrtgdt = nil %}{% assign zp_prvwcnt = nil %}{% if zp_csrvcnt != empty and zp_csrvcnt > 0 %}{% assign zp_csrvwrpcl = '' %}{% else %}{% assign zp_csrvwrpcl = 'disabled hidden' %}{% endif %}{% if zp_cptr_rplc_src %}{% assign zp_rvwmkr = zp_csblkmkr | append: '_' | append: zp_rvs_src %}{% else %}{% assign zp_rvwmkr = zp_csblkmkr %}{% endif %}{% if zp_csrvrtg != empty and zp_csrvrtg > 0 %}{% assign zp_csrvrtngtxt = '' | append: zp_csrvrtg %}{% if zp_csrvrtngtxt contains '.' %}{% assign zp_prvwrtgdt = zp_csrvrtg | split: '.' %}{% if zp_prvwrtgdt[1].size > 1 %}{% assign zp_prtgdtdcm = '' | append: zp_prvwrtgdt[1] | split: '' %}{% assign zp_csrvrtngtxt = zp_prvwrtgdt[0] | append: '.' | append: zp_prtgdtdcm[0] %}{% endif %}{% assign zp_prvwrtgdt = nil %}{% assign zp_prtgdtdcm = nil %}{% else %}{% assign zp_csrvrtngtxt = zp_csrvrtngtxt | append: '.0' %}{% endif %}{% assign zp_prvwtrtng = zp_csrvrtg %}{% for zp_itridx in (1..5) %}{% if zp_prvwtrtng > 0 and zp_prvwtrtng <= 0.5 %}{% assign zp_csrtstrcl = 'zpa-star-half' %}{% elsif zp_prvwtrtng > 0.5 %}{% assign zp_csrtstrcl = 'zpa-star-full' %}{% else %}{% assign zp_csrtstrcl = '' %}{% endif %}{% assign zp_prtgsitr = 'zps_rvic_' | append: zp_itridx %}{% assign zp_prvwtrtng = zp_prvwtrtng | minus: 1 %}{% if zp_rprfrmf %}{% capture zp_crrpi %}zps_rvic_{{ zp_itridx }}_{{ zp_rvwmkr }}{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_csrtstrcl %}{% else %}{% assign zp_product_content_parts = zp_product_content_parts | replace: zp_prtgsitr, zp_csrtstrcl %}{% endif %}{% endfor %}{% else %}{% for zp_itridx in (1..5) %}{% assign zp_prtgsitr = 'zps_rvic_' | append: zp_itridx %}{% if zp_rprfrmf %}{% capture zp_crrpi %}zps_rvic_{{ zp_itridx }}_{{ zp_rvwmkr }}{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, "" %}{% else %}{% assign zp_product_content_parts = zp_product_content_parts | replace: zp_prtgsitr, "" %}{% endif %}{% endfor %}{% endif %}{% capture zp_crrpi %}<zpcsrvavg_{{ zp_rvwmkr }}></zpcsrvavg_{{ zp_rvwmkr }}>{% endcapture %}{% capture zp_crrpi2 %}<zpcsrvcnt_{{ zp_rvwmkr }}></zpcsrvcnt_{{ zp_rvwmkr }}>{% endcapture %}{% capture zp_crrpi3 %}zps_csunvavlblrv_{{ zp_rvwmkr }}{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_csrvrtngtxt | replace: zp_crrpi2, zp_csrvcnt | replace: zp_crrpi3, zp_csrvwrpcl %}{% endfor %}{% endif %}{% unless zp_csprdfnd %}{% assign zp_cross_sell_wrapper_selector = zp_cross_sell_empty_wrapper_selector %}{% endunless %}{% if zp_rprfrmf %}{% capture zp_cross_sell_variants_tag_name %}<zpcsvrnts_{{ zp_csblkmkr }}></zpcsvrnts_{{ zp_csblkmkr }}>{% endcapture %}{% unless zp_current_entity_content contains zp_cross_sell_variants_tag_name %}{% assign zp_csatnlclscnt = zp_csatnlclscnt | append: zp_csvrntcntr %}{% endunless %}{% capture zp_cross_sell_quantity_tag_name %}<zpcsqtyctrl_{{ zp_csblkmkr }}></zpcsqtyctrl_{{ zp_csblkmkr }}>{% endcapture %}{% unless zp_current_entity_content contains zp_cross_sell_quantity_tag_name %}{% assign zp_csatnlclscnt = zp_csatnlclscnt | append: zp_csqtycntr %}{% endunless %}{% if zp_csatnlclscnt.size > 0 %}{% assign zp_csatnlclscnt = '<div style="display:none!important">' | append: zp_csatnlclscnt | append: '</div>' %}{% endif %}{% capture zp_crrpi %}zp_cross_sell_wrapper_selector_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi2 %}zps_csimgsrc_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi3 %}<zpcsslprc_{{ zp_csblkmkr }}></zpcsslprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi4 %}<zpprscst_{{ zp_csblkmkr }}></zpprscst_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi5 %}zpprscsa_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi6 %}<zpprscsdes_{{ zp_csblkmkr }}></zpprscsdes_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi7 %}<zpcsprc_{{ zp_csblkmkr }}></zpcsprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi8 %}<zpcscompareprc_{{ zp_csblkmkr }}></zpcscompareprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi9 %}<zpcssvdprc_{{ zp_csblkmkr }}></zpcssvdprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi10 %}<zpcsdcsamnt_{{ zp_csblkmkr }}></zpcsdcsamnt_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi11 %}<zpcsildcs_{{ zp_csblkmkr }}></zpcsildcs_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi12 %}zps_csunvavlblprdcl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi13 %}zps_csunavlbldccl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi14 %}zps_csimgdattr_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi15 %}zps_csunvavlblqtycl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi16 %}<zpcsvrnts_{{ zp_csblkmkr }}></zpcsvrnts_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi17 %}<zpcsvrnts_{{ zp_csblkmkr }}></zpcsvrnts_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi18 %}<zpcsqtyctrl_{{ zp_csblkmkr }}></zpcsqtyctrl_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi19 %}<zpcspmsg_{{ zp_csblkmkr }}></zpcspmsg_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi20 %}<zpcsunprc_{{ zp_csblkmkr }}></zpcsunprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi21 %}<zpcsunplb_{{ zp_csblkmkr }}></zpcsunplb_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi22 %}zps_unvavlblcsunp_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi23 %}zps_unvavlblbndilcl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi24 %}<zpcsaccc_{{ zp_csblkmkr }}></zpcsaccc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi25 %}zps_qtyicrtl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi26 %}zps_qtyinttl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi27 %}zps_qtydcrtl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi28 %}zps_elid_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi29 %}zps_qtyattr_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi30 %}<zpcsctw_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi31 %}</zpcsctw_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi32 %}<zpcspcim_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi33 %}</zpcspcim_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi34 %}<zpcspcv_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi35 %}</zpcspcv_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi36 %}<zpcsctwt_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi37 %}</zpcsctwt_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi38 %}<zpcspcti_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi39 %}</zpcspcti_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi40 %}<zpcspctv_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi41 %}</zpcspctv_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi42 %}<zpdcsprdct_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi43 %}</zpdcsprdct_{{ zp_csblkmkr }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_cross_sell_wrapper_selector | replace: zp_crrpi2, zp_csimg | replace: zp_crrpi3, zp_fmcsslpr | replace: zp_crrpi4, zp_cspttl | replace: zp_crrpi5, zp_cspttla | replace: zp_crrpi6, zp_cspdsc | replace: zp_crrpi7, zp_fmcspr | replace: zp_crrpi8, zp_fmcscapr | replace: zp_crrpi9, zp_fmcsdsctsvam | replace: zp_crrpi10, zp_fmcsdsctam | replace: zp_crrpi11, zp_csdsctamwsfx | replace: zp_crrpi12, zp_uncscls | replace: zp_crrpi13, zp_csunvbdsctcl | replace: zp_crrpi14, zp_csimgdatr | replace: zp_crrpi15, zp_uncsqtycls | replace_first: zp_crrpi16, zp_csvrntcntr | replace: zp_crrpi17, '' | replace: zp_crrpi18, zp_csqtycntr | replace: zp_crrpi19, zp_csmsgs | replace: zp_crrpi20, zp_csupblck | replace: zp_crrpi21, zp_csuplbl | replace: zp_crrpi22, zp_csupwrprcls | replace: zp_crrpi23, zp_unbndpiwrcl | replace: zp_crrpi24, zp_csatnlclscnt | replace: zp_crrpi25, zp_csqtyicrtr | replace: zp_crrpi26, zp_csqtyinpttr | replace: zp_crrpi27, zp_csqtydcrtr | replace: zp_crrpi28, zp_cselid | replace: zp_crrpi29, '' | replace: zp_crrpi30, '' | replace: zp_crrpi31, '' | replace: zp_crrpi32, '' | replace: zp_crrpi33, '' | replace: zp_crrpi34, '' | replace: zp_crrpi35, '' | replace: zp_crrpi36, '' | replace: zp_crrpi37, '' | replace: zp_crrpi38, '' | replace: zp_crrpi39, '' | replace: zp_crrpi40, '' | replace: zp_crrpi41, '' | replace: zp_crrpi42, '' | replace: zp_crrpi43, '' %}{% else %}{% assign zp_cscntprts = zp_cscntprts | replace: 'zp_cross_sell_wrapper_selector', zp_cross_sell_wrapper_selector | replace: 'zps_csimgsrc', zp_csimg | replace: '<zpcsslprc></zpcsslprc>', zp_fmcsslpr | replace: '<zpcsprc></zpcsprc>', zp_fmcspr | replace: '<zpcscompareprc></zpcscompareprc>', zp_fmcscapr | replace: '<zpcssvdprc></zpcssvdprc>', zp_fmcsdsctsvam | replace: '<zpcsdcsamnt></zpcsdcsamnt>', zp_fmcsdsctam | replace: 'zps_csunvavlblprdcl', zp_uncscls | replace: 'zps_csunavlbldccl', zp_csunvbdsctcl | replace: 'zps_csimgdattr', zp_csimgdatr | replace: 'zps_csunvavlblqtycl', zp_uncsqtycls | replace_first: '<zpcsvrnts></zpcsvrnts>', zp_csvrntcntr | replace: '<zpcsqtyctrl></zpcsqtyctrl>', zp_csqtycntr | replace: '<zpcspmsg></zpcspmsg>', zp_csmsgs | replace: '<zpcsunprc></zpcsunprc>', zp_csupblck | replace: 'zps_unvavlblcsunp', zp_csupwrprcls | replace: 'zps_unvavlblbndilcl', zp_unbndpiwrcl %}{% assign zp_product_content_parts = zp_product_content_parts | append: zp_cscntprts | append: zp_cscnt[1] %}{% endif %}{% endif %}{% endfor %}{% if zp_acspct > 0 %}{% assign zp_uncswrcl = '' %}{% endif %}{% if zp_cscfg.size > 0 %}{% assign zp_cscfg = '[' | append: zp_cscfg | append: ']' %}{% if zp_bndblen %}{% assign zp_bndbcfg = zp_current_product_settings['bndlc'] | default: nil | json %}{% assign zp_adpinstg = zp_adpinstg | append: ',"bundleConfig":{"rules":' | append: zp_bndbcfg | append: ',"hash":' %}{% assign zp_bndbcfg = zp_current_product_settings['bndlce'] | default: nil | json %}{% assign zp_adpinstg = zp_adpinstg | append: zp_bndbcfg %}{% if zp_current_product_settings['bndlel'] != blank %}{% for zp_bndecgfpr in zp_current_product_settings['bndlel'] %}{% assign zp_bndestg = zp_bndecgfpr | last %}{% assign zp_adpinstg = zp_adpinstg | append: ',"' | append: zp_bndestg['t'] | append: '":' %}{% if zp_bndestg['t'] == 'title' %}{% assign zp_bndbcfg = zp_bndestg['dt'] | default: product.title | strip_html | strip_newlines | strip %}{% else %}{% assign zp_bndbcfg = zp_bndestg['dt'] %}{% endif %}{% assign zp_bndbcfg = zp_bndbcfg | default: nil | json %}{% assign zp_adpinstg = zp_adpinstg | append: zp_bndbcfg %}{% endfor %}{% else %}{% assign zp_bndbcfg = zp_current_product_settings['bndlttl'] | default: '' | strip_html | strip_newlines | strip | json %}{% assign zp_adpinstg = zp_adpinstg | append: ',"title":' | append: zp_bndbcfg %}{% endif %}{% assign zp_adpinstg = zp_adpinstg | append: '}' %}{% assign zp_bndbcfg = nil %}{% endif %}{% endif %}{%- endif -%} 
{%- capture zp_additional_property_integration_content -%}{% capture zp_replace_integration %}<zp_additional_property_integration></zp_additional_property_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_additional_property_integration>', '' | replace: '</zp_additional_property_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- endcapture -%}{% if zp_integrate_with_recharge and zp_enable_subscription_widget and zp_pcintrcsbcr == false %}{% assign zp_recharge_wrapper_content = '<div id="' | append: zp_product_selector | append: '-recharge-wrapper" class="zpa-offset-bottom-xs zpa-recharge-wrapper" data-zp-recharge-product="' | append: product.id | append: '" data-zp-wrapper="' | append: zp_product_selector | append: '"></div>' %}{% assign zp_additional_property_integration_content = zp_additional_property_integration_content | prepend: zp_recharge_wrapper_content %}{% endif %} 
{%- if zp_main_product_available -%}{%- capture zp_variant_integration_content -%}{% capture zp_replace_integration %}<zp_variant_integration></zp_variant_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_variant_integration>', '' | replace: '</zp_variant_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- endcapture -%}{%- else -%}{% assign zp_variant_integration_content = '' %}{%- endif -%}
{%- if zp_main_product_available -%}{%- capture zp_property_integration_content -%}{% if zp_incl_boldsbsrpns %}{% include 'bsub-widget' %}{% endif %}{% if zp_intgrt_wboldprdopts %}<div class="bold_options" data-product-id="{{ product.id }}"></div>{% endif %}{% capture zp_replace_integration %}<zp_property_integration></zp_property_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_property_integration>', '' | replace: '</zp_property_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- endcapture -%}{%- else -%}{% assign zp_property_integration_content = '' %}{%- endif -%}
{%- if zp_main_product_available -%}{%- capture zp_product_json_integration_content -%}{% capture zp_replace_integration %}<zp_product_json_integration>{% if zp_intgrt_wboldqtbrk %}{% capture zp_product_json %}{% include 'bold-product', product: product, output: 'json' %}{% endcapture %}{% if zp_product_json contains 'Could not find asset ' %}{{ product | json | default: 'null' }}{% else %}{{ zp_product_json | strip | default: 'null' }}{% endif %}{% assign zp_product_json = nil %}{% else %}{{ product | json | default: 'null' }}{% endif %}</zp_product_json_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_json_integration>', '' | replace: '</zp_product_json_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- endcapture -%}{%- else -%}{% assign zp_product_json_integration_content = '' %}{%- endif -%}
{%- if zp_main_product_available -%}{%- capture zp_product_footer_integration_content -%}{% capture zp_replace_integration %}<zp_product_footer_integration></zp_product_footer_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_footer_integration>', '' | replace: '</zp_product_footer_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- endcapture -%}{%- else -%}{% assign zp_product_footer_integration_content = '' %}{%- endif -%}{%- if zp_main_product_available %}{% capture zp_pvrntcntr %}{% if zp_shw_vrntsslctr %}{% if zp_pvrntslwrclen %}<div class="{{ zp_pvrntslwrcl }}">{% endif %}{{ zp_additional_property_integration_content }}<select class="hidden noreplace" id="{{ zp_product_selector }}" name="id" data-productid="{{ product.id }}" aria-hidden="true">{% assign zp_first_selected_variant_id = '' | append: zp_first_selected_variant.id %}{% for variant in zp_current_product_custom_variants %}{% assign zp_variant_id = '' | append: variant.id %}{% unless zp_selected_variants contains zp_variant_id %}{% continue %}{% endunless %}{{ zp_variant_integration_content }}{% if zp_intgrt_wboldqtbrk %}{% include 'bold-variant' with variant, hide_action: 'skip' %}{% endif %}{% if variant.available %}{% if zp_first_selected_variant_id == zp_variant_id %}{% assign zp_option_selected_attr = 'selected="selected" ' %}{% else %}{% assign zp_option_selected_attr = '' %}{% endif %}<option {{ zp_option_selected_attr }}value="{{ variant.id }}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% else %}<option disabled="disabled" value="{{ variant.id }}">{{ variant.title }} - {% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}</option>{% endif %}{% endfor %}</select>{{ zp_property_integration_content }}{% if zp_pvrntslwrclen %}</div>{% endif %}{% endif %}{% endcapture %}{% endif -%}{%- capture zp_pqtycntr -%}{% if zp_shw_vrntsslctr %}{% if zp_product_show_quantity %}{% unless zp_product_show_quantity_label == false %}<label class="{% if zp_pvrntslv == 'hz' %}{{ 'zpa-buybox__label zpa-offset-right-sm zpa-buybox__label zp' | append: zp_pqslcl | strip }}{% else %}{{ 'zp' | append: zp_pqslcl | strip }}{% endif %}" for="{{ zp_product_selector }}-quantity">{% assign zp_translation = 'products.product.quantity.label' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'products.product.quantity' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Quantity" %}{% endif %}{% endif %}{% assign zp_last_translation_char = zp_translation | strip | split: '' | last %}{% if zp_last_translation_char == ':' %}{{ zp_translation }}{% else %}{{ zp_translation }}:{% endif %}</label>{% endunless %}<div class="{% if zp_pvrntslv == 'hz' %}zpa-quantity-block{% else %}zpa-quantity-block{% endif %}" data-product-wrapper-id="{{ zp_product_element_id }}" data-zp-quantity-el><button class="zpa-btn-custom zpa-quantity-btn" type="button" title="{% assign zp_translation = 'products.product.quantity.decrease' | t: product: product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Decrease quantity" %}{% endif %}{{ zp_translation }}" data-zp-product-decrease-qty><svg class="zpa-quantity-icon" width="14px" height="14px"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#iconDecrease"></use></svg></button><input id="{{ zp_product_selector }}-quantity" class="zpa-quantity-field xs" value="{{ zp_product_default_quantity }}" maxlength="3" size="3" type="tel" inputmode="numeric" required pattern="^[1-9][0-9]*" name="quantity" title="{% assign zp_translation = 'products.product.quantity.input_label' | t: product: product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Quantity" %}{% endif %}{{ zp_translation }}" data-zp-product-quantity><button class="zpa-btn-custom zpa-quantity-btn" type="button" title="{% assign zp_translation = 'products.product.quantity.increase' | t: product: product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Increase quantity" %}{% endif %}{{ zp_translation }}" data-zp-product-increase-qty><svg class="zpa-quantity-icon" width="14px" height="14px"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#iconIncrease"></use></svg></button></div>{% else %}<input id="{{ zp_product_selector }}-quantity" type="hidden" name="quantity" value="{{ zp_product_default_quantity }}" data-zp-product-quantity>{% endif %}{% endif %}{%- endcapture -%}{% capture zp_pqtytgn %}zps_qtyattr_{{ zp_pblkmkr }}{% endcapture %}{% if zp_current_entity_content contains zp_pqtytgn %}{% assign zp_pqtycntr = zp_pqtycntr | remove: ' name="quantity"' | remove: ' required ' %}{% endif %} 
{%- if zp_main_product_available %}{% capture zp_pbtnintgr -%}{%- if zp_intgrt_wboldqtbrk %}<div class="shappify-qty-msg">{{ bold_selected_or_first_available_variant.metafields.shappify_qb.pricing_html }}</div>{% endif -%}{% capture zp_replace_integration %}<zp_product_info_integration></zp_product_info_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_info_integration>', '' | replace: '</zp_product_info_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- endcapture %}{% else %}{% assign zp_pbtnintgr = '' %}{% endif -%}{%- capture zp_pbtnmsg -%}{% if zp_shw_vrntsslctr %}<div class="{{ 'zpa-product-message__wrapper ' | append: zp_pmsgwcl | strip }}"><span class="{{ 'zpa-product-message hidden product-success zp ' | append: zp_patccl | strip }}" data-zp-product-messages data-zp-success-message="{{ zp_patcm }}"></span></div>{% endif %}{%- endcapture -%}
{%- if zp_main_product_available %}{% capture zp_product_additional_integrations -%}{% if zp_intgrt_wboldprdopts %}{% assign zp_incl_boldcmnsnpt = true %}<script>window.BOLD.common.Shopify.saveProduct({{ product.handle | json }},{{ product.id | json }},{{ product | json }});{% for variant in product.variants %}{% assign csp_metafield_namespace = variant.id | prepend: "csp" %}window.BOLD.common.Shopify.saveVariant({{ variant.id | json }},{variant:{{ variant | json }},inventory_quantity:{{ variant.inventory_quantity | json }},product_id:{{ product.id | json }},product_handle:{{ product.handle | json }},price:{{ variant.price | json }},variant_title:{{ variant.title | json }},group_id:"{{ variant.metafields.bold_rp.rp_group_id }}",csp_metafield:{{ product.metafields[csp_metafield_namespace] | json }}});{% endfor %}</script>{% endif %}{% if zp_intgrt_wboldqtbrk == true %}{% include "bold-product" with product, hide_action: "break" %}{% if bold_hidden_product %}{% break %}{% endif %}{% endif %}{% capture zp_replace_integration %}<zp_additional_integration></zp_additional_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_additional_integration>', '' | replace: '</zp_additional_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- endcapture %}{% else %}{% assign zp_product_additional_integrations = '' %}{% endif -%}{% if zp_first_selected_variant.compare_at_price > zp_first_selected_variant.price %}{% assign zp_current_product_compare_at_price = zp_first_selected_variant.compare_at_price %}{% else %}{% assign zp_current_product_compare_at_price = nil %}{% endif %}{% assign zp_product_discount_amount_recalculate = true %}{% assign zp_product_discount_amount_money = false %}{% assign zp_product_discount_amount_prefix = '' %}{% assign zp_product_discount_amount_suffix = '' %}{% if zp_product_discount_data contains ':' %}{% assign zp_product_discount_data = zp_product_discount_data | split: ':' %}{% assign zp_split_discount_data = true %}{% else %}{% assign zp_split_discount_data = false %}{% endif %}{% if zp_product_discount_data.size > 1 %}{% assign zp_product_discount_amount = 0 | plus: zp_product_discount_data[0] | abs %}{% assign zp_product_discount_type = '' | append: zp_product_discount_data[1] %}{% if zp_product_discount_type == 'percentage' or zp_product_discount_type == 'percentage_dynamic' %}{% assign zp_product_discount_amount_parts = '' | append: zp_product_discount_amount | split: '.' %}{% assign zp_product_discount_decimal_part = '' | append: zp_product_discount_amount_parts[1] | default: '0' %}{% assign zp_product_discount_decimal_part = 0 | plus: zp_product_discount_decimal_part %}{% unless zp_product_discount_decimal_part > 0 %}{% assign zp_product_discount_amount = zp_product_discount_amount_parts[0] %}{% endunless %}{% assign zp_product_discount_amount = 0 | plus: zp_product_discount_amount | at_most: 100 %}{% assign zp_product_saved_amount = zp_current_product_price | times: zp_product_discount_amount | divided_by: 100 | at_most: zp_current_product_price %}{% assign zp_product_discount_amount_recalculate = false %}{% assign zp_product_discount_amount_suffix = '%' %}{% assign zp_prdscdfwcl = '' %}{% elsif zp_product_discount_type == 'compare_price' %}{% if zp_current_product_compare_at_price %}{% assign zp_product_discount_amount = zp_current_product_compare_at_price | minus: zp_current_product_price %}{% else %}{% assign zp_product_discount_amount = 0 %}{% assign zp_unavailable_discount_class = 'hidden' %}{% endif %}{% assign zp_product_saved_amount = zp_product_discount_amount %}{% else %}{% assign zp_product_discount_amount = zp_product_discount_amount | times: 100 %}{% assign zp_product_discount_amount = '' | append: zp_product_discount_amount | split: '.' | first | default: 0 %}{% assign zp_product_discount_amount = 0 | plus: zp_product_discount_amount | at_most: zp_current_product_price %}{% assign zp_product_saved_amount = zp_product_discount_amount %}{% assign zp_product_discount_amount_money = true %}{% endif %}{% if zp_product_discount_type == 'compare_price' %}{% assign zp_product_sale_price = zp_current_product_price %}{% else %}{% assign zp_product_sale_price = zp_current_product_price | minus: zp_product_saved_amount %}{% endif %}{% assign zp_product_discount_amount_prefix = '-' %}{% else %}{% assign zp_product_sale_price = zp_current_product_price %}{% assign zp_unavailable_discount_class = 'hidden' %}{% assign zp_product_discount_amount = 0 %}{% endif %}{% if zp_split_discount_data %}{% assign zp_product_discount_data = zp_product_discount_data | join: ':' %}{% endif %}{% if zp_product_calculate_bundle_price %}{% assign zp_current_product_price = zp_current_product_price | times: zp_product_default_quantity %}{% if zp_current_product_compare_at_price %}{% assign zp_current_product_compare_at_price = zp_current_product_compare_at_price | times: zp_product_default_quantity %}{% endif %}{% assign zp_product_sale_price = zp_product_sale_price | times: zp_product_default_quantity %}{% if zp_product_saved_amount %}{% assign zp_product_saved_amount = zp_product_saved_amount | times: zp_product_default_quantity %}{% endif %}{% if zp_product_discount_amount_recalculate %}{% assign zp_product_discount_amount = zp_product_discount_amount | times: zp_product_default_quantity %}{% endif %}{% endif %}{% if zp_ucprart and zp_product_discount_type == 'compare_price' %}{% assign zp_formatted_product_compare_at_price = zp_current_product_compare_at_price | money %}{% assign zp_formatted_product_price = zp_formatted_product_compare_at_price %}{% else %}{% assign zp_formatted_product_price = zp_current_product_price | money %}{% assign zp_formatted_product_compare_at_price = zp_current_product_compare_at_price | money %}{% endif %}{% assign zp_formatted_product_sale_price = zp_product_sale_price | money %}{% if zp_product_discount_amount_money %}{% assign zp_formatted_product_discount_amount = zp_product_discount_amount | money %}{% else %}{% assign zp_formatted_product_discount_amount = zp_product_discount_amount %}{% endif %}{% assign zp_product_discount_amount_with_suffix = zp_formatted_product_discount_amount | append: zp_product_discount_amount_suffix %}{% assign zp_formatted_product_discount_amount = zp_formatted_product_discount_amount | prepend: zp_product_discount_amount_prefix | append: zp_product_discount_amount_suffix %}{% if zp_product_saved_amount %}{% assign zp_formatted_product_saved_amount = zp_product_saved_amount | money %}{% endif %}{% if zp_cpstdd != blank %}{% if zp_cpstdd['only_cross_sell'] %}{% if zp_bndblen %}{% assign zp_pdscfnatr = 'data-zp-bundle-product-discount-field' %}{% else %}{% assign zp_pdscfnatr = 'data-zp-cross-sell-discount-field' %}{% endif %}{% assign zp_pdscfnatr = zp_pdscfnatr | append: ' data-zp-name' %}{% else %}{% assign zp_pdscfnatr = 'name' %}{% endif %}{% assign zp_cpstdschf = '<input type="hidden" ' | append: zp_pdscfnatr | append: '="discount" value="' | append: zp_cpstdd['code'] | append: '">' %}{% capture zp_paclcntmk %}<zpprdaccc_{{ zp_pblkmkr }}></zpprdaccc_{{ zp_pblkmkr }}>{% endcapture %}{% if zp_current_entity_content contains zp_paclcntmk %}{% assign zp_padclcnt = zp_padclcnt | append: zp_cpstdschf %}{% else %}{% assign zp_pqtycntr = zp_pqtycntr | append: zp_cpstdschf %}{% endif %}{% endif %}{% assign zp_pdstds = nil %}{% if zp_unit_price_enabled %}{% if zp_product_unit_price_tag_included %}{% if zp_first_selected_variant.unit_price_measurement %}{% assign zp_pupwrcl = '' %}{% else %}{% assign zp_pupwrcl = ' hidden' %}{% endif %}{% capture zp_puplbl %}{% assign zp_translation = 'products.product.unit_price_label' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'products.product.price.unit_price' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Unit Price" %}{% endif %}{% endif %}{{ zp_translation }}{% endcapture %}{% capture zp_pupblk %}<span data-zp-product-unit-price>{% if zp_first_selected_variant.unit_price_measurement %}{{ zp_first_selected_variant.unit_price | money }}{% endif %}</span><span> / </span><span data-zp-product-up-base-unit>{% if zp_first_selected_variant.unit_price_measurement %}{% if zp_first_selected_variant.unit_price_measurement.reference_value != 1 %}{{ zp_first_selected_variant.unit_price_measurement.reference_value }}{% endif %}{{ zp_first_selected_variant.unit_price_measurement.reference_unit }}{% endif %}</span>{% endcapture %}{% else %}{% capture zp_pupblk %}{% if zp_unit_price_enabled %}<div class="zpa-offset-bottom-xs{% unless zp_first_selected_variant.unit_price_measurement %} hidden{% endunless %}" data-zp-product-unit-price-wrapper><div class="zpa-dynamic-buy-box__label--xs">{% assign zp_translation = 'products.product.unit_price_label' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'products.product.price.unit_price' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Unit Price" %}{% endif %}{% endif %}{{ zp_translation }}:</div><span data-zp-product-unit-price>{% if zp_first_selected_variant.unit_price_measurement %}{{ zp_first_selected_variant.unit_price | money }}{% endif %}</span><span> / </span><span data-zp-product-up-base-unit>{% if zp_first_selected_variant.unit_price_measurement %}{% if zp_first_selected_variant.unit_price_measurement.reference_value != 1 %}{{ zp_first_selected_variant.unit_price_measurement.reference_value }}{% endif %}{{ zp_first_selected_variant.unit_price_measurement.reference_unit }}{% endif %}</span></div>{% endif %}{% endcapture %}{% assign zp_pvrntcntr = zp_pupblk | append: zp_pvrntcntr %}{% endif %}{% endif %}{%- endform -%}{%- endcapture -%}{% assign zp_pfoptg = zp_pfoptg | remove: '</form>' | strip %}{%- else -%}{% if zp_product_found %}{% assign zp_unplnbcls = '' %}{% assign zp_unpbwrcls = ' hidden' %}{% endif %}{%- endif -%}{% if zp_product_found %}{%- capture zp_product_init_script -%}<script type="text/json" data-zp-product-init>{"translations":{ {% if zp_product_button_caption != blank and zp_product_button_caption != '' %}"addToCart":"{{ zp_product_button_caption | url_escape }}"{% endif %} },"productSelector":"{{ zp_product_selector }}"{% if zp_selected_variants_list != '' %},"selectedVariants":"{{ zp_selected_variants_list | url_escape }}"{% endif %},"productType":"{{ zp_product_type }}","imageSize":"{{ '' | append: zp_current_product_image_size | strip | default: 'master' | url_escape }}","productDiscountData":"{{ zp_product_discount_data }}","initBuilder":{{ zp_any_product_variant_available | default: 'false' }},"sbscrbPriceRefresh":{{ zp_pscrprrf | default: 'false' }},"calculateBundlePrice":{{ zp_product_calculate_bundle_price | default: 'false' }},"defaultQty":{{ 0 | plus: zp_product_default_quantity }},"productElementId":"{{ zp_product_element_id }}"{% if zp_pbtnsjsndt != blank %},"buttons":{{ zp_pbtnsjsndt }}{% endif %}{% if zp_pimgjsndt != blank %},"images":{{ zp_pimgjsndt }}{% endif %},"imageSrcSetData":{{ zp_product_image_sizes_json }},"productBlockId":"{{ zp_product_block_id }}","preloadVariantImages":{{ zp_pprvimg }}{% if zp_pvrnjsndt == blank %},"variantSelectorView":"{{ zp_pvrntslv }}"{% endif %}{% if zp_pvrntslofcl != blank %},"variantOffsetClasses":"{{ zp_pvrntslofcl }}"{% endif %}{% if zp_pvrntslcl != blank %},"variantLabelClasses":"zp {{ zp_pvrntslcl }}"{% endif %}{% if zp_pshwvrntsllbl != nil %},"showVariantLabel":{{ zp_pshwvrntsllbl }}{% endif %}{% if zp_pvrnjsndt != blank %},"variantSelectors":{{ zp_pvrnjsndt }}{% endif %},"enableHistoryState":{{ zp_use_product_variant_from_url }},"productEntityType":"{{ zp_product_entity_type }}","imageGeneralContainer":{{ zp_ugpimgcnt | default: false }},"updExternalPrice":{{ zp_upextppr }}{% if zp_popstng != blank %},"optionsSettings":{{ zp_popstng }}{% endif %}{% if zp_pimgjsndt == blank %},"enableImageLinking":{{ zp_epimgln }}{% endif %}{% if zp_cpstdd != blank %},"productDiscountHash":{{ zp_cpstdd['data_hash'] | json }}{% endif %}{% if zp_product_reviews_source != blank %},"reviewsSource":{{ zp_product_reviews_source | json }}{% endif %}{% if zp_product_reviews_data != blank %},"starRatings":{{ zp_product_reviews_data }}{% endif %}{% if zp_cscfg != blank %},"{{ zp_cscfgk }}":{{ zp_cscfg }}{% endif %},"productEnabled":{{ zp_main_product_available }}{% if zp_pbtnsjsndt == blank %},"redirectUrl":{{ zp_redirect_product_url | json }}{% endif %}{% if zp_pbtnsjsndt == blank %},"redirectTarget":{{ zp_pbtnlt | json }}{% endif %},"product":{{ zp_product_json_integration_content | default: "null" }}{{ zp_adpinstg }}}</script>{%- endcapture -%}{% endif %}{% if zp_product_found %}{% capture zp_product_button_text %}<span class="zpa-btn-custom__caption" data-zp-add-to-cart-text>{% if zp_any_product_variant_available %}{{ zp_product_button_caption }}{% else %}{% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}{% endif %}</span><svg class="zpa-btn-loader zpa-btn-custom__icon" width="14px" height="14px"><use href="#buyButtonLoader"></use></svg>{% endcapture %}{% capture zp_product_sticky_button_text %}<span class="zpa-btn-custom__caption" >{% if zp_any_product_variant_available %}{{ zp_product_sticky_button_caption }}{% else %}{% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}{% endif %}</span><svg class="zpa-btn-loader zpa-btn-custom__icon" width="14px" height="14px"><use href="#buyButtonLoader"></use></svg>{% endcapture %}{% endif %}{% if zp_current_product_settings contains 'srtgs' %}{% assign zp_rvwsrc = zp_current_product_settings['srtgs'] | map: 'rtgsrc' | uniq %}{% assign zp_cptr_rplc_src = true %}{% else %}{% assign zp_rvwsrc = zp_current_product_settings['rtgsrc'] | split: ' ' %}{% assign zp_cptr_rplc_src = false %}{% endif %}{% for zp_rvs_src in zp_rvwsrc %}{% if zp_rvs_src == blank %}{% continue %}{% endif %}{% assign zp_product_review_rating = nil %}{% assign zp_product_review_count = nil %}{% assign zp_product_review_rating_text = '' %}{% if product.id > 0 %}{% case zp_rvs_src %}{% when 'yotpo' %}{% assign zp_prvwdt = product.metafields.yotpo %}{% assign zp_prvwcnt = 0 | plus: zp_prvwdt.reviews_count %}{% if zp_prvwcnt > 0 %}{% assign zp_product_review_rating = 0 | plus: zp_prvwdt.reviews_average %}{% assign zp_product_review_count = 0 | plus: zp_prvwdt.reviews_count %}{% endif %}{% when 'loox' %}{% assign zp_prvwdt = product.metafields.loox %}{% assign zp_prvwcnt = 0 | plus: zp_prvwdt.num_reviews %}{% if zp_prvwcnt > 0 %}{% assign zp_product_review_rating = 0 | plus: zp_prvwdt.avg_rating %}{% assign zp_product_review_count = 0 | plus: zp_prvwdt.num_reviews %}{% endif %}{% when 'spr' %}{% assign zp_prvwdt = product.metafields.spr.reviews %}{% if zp_prvwdt %}{% assign zp_prvwdt = ' ' | append: zp_prvwdt | append: ' ' %}{% assign zp_prvwrtgdt = zp_prvwdt | split: '"ratingValue":' %}{% assign zp_prvwrtgdt = zp_prvwrtgdt[1] | strip | slice: 0, 100 | remove: '"' | remove: "'" | split: '' %}{% assign zp_prvwavchr = '1234567890.' | split: '' %}{% assign zp_product_review_rating = '' %}{% for zp_prvwrchr in zp_prvwrtgdt %}{% if zp_prvwavchr contains zp_prvwrchr %}{% assign zp_product_review_rating = zp_product_review_rating | append: zp_prvwrchr %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_product_review_rating = 0 | plus: zp_product_review_rating %}{% unless zp_product_review_rating > 0 %}{% assign zp_product_review_rating = nil %}{% endunless %}{% assign zp_prvwrtgdt = zp_prvwdt | split: '"reviewCount":' %}{% if zp_prvwrtgdt.size < 2 %}{% assign zp_prvwrtgdt = zp_prvwdt | split: '"ratingCount":' %}{% endif %}{% assign zp_prvwrtgdt = zp_prvwrtgdt[1] | strip | slice: 0, 100 | remove: '"' | remove: "'" | split: '' %}{% assign zp_prvwavchr = '1234567890' | split: '' %}{% assign zp_product_review_count = '' %}{% for zp_prvwrchr in zp_prvwrtgdt %}{% if zp_prvwavchr contains zp_prvwrchr %}{% assign zp_product_review_count = zp_product_review_count | append: zp_prvwrchr %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_product_review_count = 0 | plus: zp_product_review_count %}{% unless zp_product_review_count > 0 %}{% assign zp_product_review_count = nil %}{% endunless %}{% endif %}{% when 'opinew' %}{% assign zp_prvwdt = product.metafields.opinew_metafields %}{% assign zp_prvwcnt = 0 | plus: zp_prvwdt.reviews_count %}{% if zp_prvwcnt > 0 %}{% assign zp_product_review_rating = 0 | plus: zp_prvwdt.reviews_average %}{% assign zp_product_review_count = 0 | plus: zp_prvwdt.reviews_count %}{% endif %}{% when 'judgeme' %}{% assign zp_prvwdt = product.metafields.judgeme.badge %}{% if zp_prvwdt %}{% assign zp_prvwdt = ' ' | append: zp_prvwdt | append: ' ' %}{% assign zp_prvwrtgdt = zp_prvwdt | split: ' data-average-rating=' %}{% assign zp_prvwrtgdt = zp_prvwrtgdt[1] | strip | split: ' ' | first | strip | remove: '"' | remove: "'" | split: '' %}{% assign zp_prvwavchr = '1234567890.' | split: '' %}{% assign zp_product_review_rating = '' %}{% for zp_prvwrchr in zp_prvwrtgdt %}{% if zp_prvwavchr contains zp_prvwrchr %}{% assign zp_product_review_rating = zp_product_review_rating | append: zp_prvwrchr %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_product_review_rating = 0 | plus: zp_product_review_rating %}{% unless zp_product_review_rating > 0 %}{% assign zp_product_review_rating = nil %}{% endunless %}{% assign zp_prvwrtgdt = zp_prvwdt | split: ' data-number-of-reviews=' %}{% assign zp_prvwrtgdt = zp_prvwrtgdt[1] | strip | split: ' ' | first | strip | remove: '"' | remove: "'" | split: '' %}{% assign zp_prvwavchr = '1234567890' | split: '' %}{% assign zp_product_review_count = '' %}{% for zp_prvwrchr in zp_prvwrtgdt %}{% if zp_prvwavchr contains zp_prvwrchr %}{% assign zp_product_review_count = zp_product_review_count | append: zp_prvwrchr %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_product_review_count = 0 | plus: zp_product_review_count %}{% unless zp_product_review_count > 0 %}{% assign zp_product_review_count = nil %}{% endunless %}{% endif %}{% endcase %}{% endif %}{% assign zp_prvwdt = nil %}{% assign zp_prvwrtgdt = nil %}{% assign zp_prvwcnt = nil %}{% if zp_product_review_count != empty and zp_product_review_count > 0 %}{% assign zp_product_review_wrapper_classes = '' %}{% else %}{% assign zp_product_review_wrapper_classes = 'disabled hidden' %}{% endif %}{% if zp_cptr_rplc_src %}{% assign zp_rvwmkr = zp_pblkmkr | append: '_' | append: zp_rvs_src %}{% else %}{% assign zp_rvwmkr = zp_pblkmkr %}{% endif %}{% if zp_product_review_rating != empty and zp_product_review_rating > 0 %}{% assign zp_product_review_rating_text = '' | append: zp_product_review_rating %}{% if zp_product_review_rating_text contains '.' %}{% assign zp_prvwrtgdt = zp_product_review_rating | split: '.' %}{% if zp_prvwrtgdt[1].size > 1 %}{% assign zp_prtgdtdcm = '' | append: zp_prvwrtgdt[1] | split: '' %}{% assign zp_product_review_rating_text = zp_prvwrtgdt[0] | append: '.' | append: zp_prtgdtdcm[0] %}{% endif %}{% assign zp_prvwrtgdt = nil %}{% assign zp_prtgdtdcm = nil %}{% else %}{% assign zp_product_review_rating_text = zp_product_review_rating_text | append: '.0' %}{% endif %}{% assign zp_prvwtrtng = zp_product_review_rating %}{% for zp_itridx in (1..5) %}{% if zp_prvwtrtng > 0 and zp_prvwtrtng <= 0.5 %}{% assign zp_product_rating_star_classes = 'zpa-star-half' %}{% elsif zp_prvwtrtng > 0.5 %}{% assign zp_product_rating_star_classes = 'zpa-star-full' %}{% else %}{% assign zp_product_rating_star_classes = '' %}{% endif %}{% assign zp_prtgsitr = 'zps_rvic_' | append: zp_itridx %}{% assign zp_prvwtrtng = zp_prvwtrtng | minus: 1 %}{% if zp_rprfrmf %}{% capture zp_crrpi %}zps_rvic_{{ zp_itridx }}_{{ zp_rvwmkr }}{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_product_rating_star_classes %}{% else %}{% assign zp_product_content_parts = zp_product_content_parts | replace: zp_prtgsitr, zp_product_rating_star_classes %}{% endif %}{% endfor %}{% else %}{% for zp_itridx in (1..5) %}{% assign zp_prtgsitr = 'zps_rvic_' | append: zp_itridx %}{% if zp_rprfrmf %}{% capture zp_crrpi %}zps_rvic_{{ zp_itridx }}_{{ zp_rvwmkr }}{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, "" %}{% else %}{% assign zp_product_content_parts = zp_product_content_parts | replace: zp_prtgsitr, "" %}{% endif %}{% endfor %}{% endif %}{% capture zp_crrpi %}<zpprdrvavg_{{ zp_rvwmkr }}></zpprdrvavg_{{ zp_rvwmkr }}>{% endcapture %}{% capture zp_crrpi2 %}<zpprdrvcnt_{{ zp_rvwmkr }}></zpprdrvcnt_{{ zp_rvwmkr }}>{% endcapture %}{% capture zp_crrpi3 %}zps_unvavlblprdrv_{{ zp_rvwmkr }}{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_product_review_rating_text | replace: zp_crrpi2, zp_product_review_count | replace: zp_crrpi3, zp_product_review_wrapper_classes %}{% endfor %}{% assign zp_hard_set_display_product_markup = false %}{% assign zp_tmp_display_product_markup = zp_display_product_markup %}{% if zp_use_current_entity_markup and zp_product_view_type == 'dynamicproductsection' %}{% assign zp_include_product_markup = true %}{% assign zp_hard_set_display_product_markup = true %}{% elsif zp_use_current_entity_markup and zp_dynamic_product_section_excluded and zp_dynamic_product_section_markup_excluded and zp_current_entity.handle == product.handle %}{% assign zp_include_product_markup = true %}{% assign zp_hard_set_display_product_markup = true %}{% assign zp_dynamic_product_section_markup_excluded = false %}{% elsif zp_use_product_markup %}{% assign zp_include_product_markup = true %}{% else %}{% assign zp_include_product_markup = false %}{% endif %}{% if zp_include_product_markup %}{% if zp_hard_set_display_product_markup %}{% assign zp_display_product_markup = true %}{% endif %}{%- if zp_product_found and zp_display_product_markup -%}{% assign zp_product_custom_title = zp_product_custom_title | strip_html | strip | default: product.title | strip_html | strip %}{% assign zp_product_custom_description = zp_product_custom_description | strip_html | strip | default: product.description | strip_html | strip %}{% if zp_current_selected_variant != blank %}{% assign zp_product_schema_variant = zp_current_selected_variant %}{% else %}{% assign zp_product_schema_variant = zp_first_selected_variant %}{% endif %}{% assign zp_product_custom_image = zp_product_custom_image | strip | default: zp_product_image | strip %}{% if zp_pshcren or zp_pcrdt != blank %}{% if zp_first_selected_variant.available and zp_first_selected_variant.featured_media != blank and zp_first_selected_variant.featured_media.media_type == 'image' %}{% assign zp_product_custom_image_obj = zp_first_selected_variant.featured_media %}{% else %}{% assign zp_product_custom_image_obj = product.media | where: 'media_type', 'image' | first | default: zp_product_schema_variant.featured_image | default: product.featured_image %}{% endif %}{% assign zp_product_custom_image = zp_product_custom_image_obj | image_url: width: zp_product_custom_image_obj.preview_image.width | prepend: 'https:' %}{% elsif zp_product_custom_image == blank or zp_product_custom_image == '' or zp_product_custom_image contains '/default_images_product_icon_360x240.jpg' %}{% assign zp_product_custom_image_obj = zp_product_schema_variant.featured_image | default: product.featured_image %}{% assign zp_product_custom_image = zp_product_custom_image_obj | image_url: width: zp_product_custom_image_obj.preview_image.width | prepend: 'https:' %}{% endif %}{%- endif -%}{% if zp_main_product_available %}{% capture zp_pftintctn -%}<zp_product_additional_footer_integration></zp_product_additional_footer_integration>{%- endcapture %}{% else %}{% assign zp_pftintctn = '' %}{% endif %}{% capture zp_product_markup_integration %}{% if zp_product_found and zp_display_product_markup %}<script type="application/ld+json">{"@context":"http://schema.org/","@type":"Product",{% if product.vendor != blank %}"brand":{"@type":"Brand","name":{{ product.vendor | json }}},{% endif %}"name":{{ zp_product_custom_title | json }},"description":{{ zp_product_custom_description | json }},"url":{{ request.origin | append: zp_current_entity.url | json }},{% if zp_product_custom_image != blank %}"image":[{{ zp_product_custom_image | json }}],{% endif %}{% if zp_product_schema_variant.sku != blank %}"sku":{{ zp_product_schema_variant.sku | json }},{% endif %}"offers":[{% assign zp_prd_schema_offer_separator = '' %}{% for zp_prd_variant in zp_current_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% unless zp_selected_variants contains zp_variant_id %}{% continue %}{% endunless %}{{ zp_prd_schema_offer_separator }}{% assign zp_prd_schema_offer_separator = ',' %}{% assign zp_prd_variant_prc = zp_prd_variant.price %}{% if zp_prd_variant.compare_at_price > zp_prd_variant.price %}{% assign zp_prd_variant_cmpr_prc = zp_prd_variant.compare_at_price %}{% else %}{% assign zp_prd_variant_cmpr_prc = nil %}{% endif %}{% assign zp_prd_variant_dscnt_amnt_recalculate = true %}{% assign zp_prd_variant_dscnt_amnt_money = false %}{% assign zp_prd_variant_dscnt_amnt_prefix = '' %}{% assign zp_prd_variant_dscnt_amnt_suffix = '' %}{% if zp_product_discount_data contains ':' %}{% assign zp_product_discount_data = zp_product_discount_data | split: ':' %}{% assign zp_split_discount_data = true %}{% else %}{% assign zp_split_discount_data = false %}{% endif %}{% if zp_product_discount_data.size > 1 %}{% assign zp_prd_variant_dscnt_amnt = 0 | plus: zp_product_discount_data[0] | abs %}{% assign zp_prd_variant_dscnt_tp = '' | append: zp_product_discount_data[1] %}{% if zp_prd_variant_dscnt_tp == 'percentage' or zp_prd_variant_dscnt_tp == 'percentage_dynamic' %}{% assign zp_product_discount_amount_parts = '' | append: zp_prd_variant_dscnt_amnt | split: '.' %}{% assign zp_product_discount_decimal_part = '' | append: zp_product_discount_amount_parts[1] | default: '0' %}{% assign zp_product_discount_decimal_part = 0 | plus: zp_product_discount_decimal_part %}{% unless zp_product_discount_decimal_part > 0 %}{% assign zp_prd_variant_dscnt_amnt = zp_product_discount_amount_parts[0] %}{% endunless %}{% assign zp_prd_variant_dscnt_amnt = 0 | plus: zp_prd_variant_dscnt_amnt | at_most: 100 %}{% assign zp_prd_variant_svd_amnt = zp_prd_variant_prc | times: zp_prd_variant_dscnt_amnt | divided_by: 100 | at_most: zp_prd_variant_prc %}{% assign zp_prd_variant_dscnt_amnt_recalculate = false %}{% assign zp_prd_variant_dscnt_amnt_suffix = '%' %}{% elsif zp_prd_variant_dscnt_tp == 'compare_price' %}{% if zp_prd_variant_cmpr_prc %}{% assign zp_prd_variant_dscnt_amnt = zp_prd_variant_cmpr_prc | minus: zp_prd_variant_prc %}{% else %}{% assign zp_prd_variant_dscnt_amnt = 0 %}{% assign zp_prd_variant_unvlbl_dscnt_cls = 'hidden' %}{% endif %}{% assign zp_prd_variant_svd_amnt = zp_prd_variant_dscnt_amnt %}{% else %}{% assign zp_prd_variant_dscnt_amnt = zp_prd_variant_dscnt_amnt | times: 100 %}{% assign zp_prd_variant_dscnt_amnt = '' | append: zp_prd_variant_dscnt_amnt | split: '.' | first | default: 0 %}{% assign zp_prd_variant_dscnt_amnt = 0 | plus: zp_prd_variant_dscnt_amnt | at_most: zp_prd_variant_prc %}{% assign zp_prd_variant_svd_amnt = zp_prd_variant_dscnt_amnt %}{% assign zp_prd_variant_dscnt_amnt_money = true %}{% endif %}{% if zp_prd_variant_dscnt_tp == 'compare_price' %}{% assign zp_prd_variant_sale_prc = zp_prd_variant_prc %}{% else %}{% assign zp_prd_variant_sale_prc = zp_prd_variant_prc | minus: zp_prd_variant_svd_amnt %}{% endif %}{% assign zp_prd_variant_dscnt_amnt_prefix = '-' %}{% else %}{% assign zp_prd_variant_sale_prc = zp_prd_variant_prc %}{% assign zp_prd_variant_unvlbl_dscnt_cls = 'hidden' %}{% assign zp_prd_variant_dscnt_amnt = 0 %}{% endif %}{% if zp_split_discount_data %}{% assign zp_product_discount_data = zp_product_discount_data | join: ':' %}{% endif %}{"@type":"Offer",{% if zp_prd_variant.sku != blank %}"sku":{{ zp_prd_variant.sku | json }},{% endif %}{% if zp_prd_variant.barcode.size == 12 %}"gtin12":{{ zp_prd_variant.barcode }},{% endif %}{% if zp_prd_variant.barcode.size == 13 %}"gtin13":{{ zp_prd_variant.barcode }},{% endif %}{% if zp_prd_variant.barcode.size == 14 %}"gtin14":{{ zp_prd_variant.barcode }},{% endif %}"url":{{ request.origin | append: zp_prd_variant.url | json }},"priceCurrency":{{ cart.currency.iso_code | json }},"price":{{ zp_prd_variant_sale_prc | divided_by: 100.00 | json }},"availability":"http://schema.org/{% if zp_prd_variant.available %}InStock{% else %}OutOfStock{% endif %}"}{% endfor %}]{% if zp_product_review_count %},"aggregateRating":{"@type":"AggregateRating","ratingValue":{{ zp_product_review_rating | json }},"ratingCount":{{ zp_product_review_count | json }}}{% endif %}}</script>{% endif %}{% endcapture %}{% assign zp_display_product_markup = zp_tmp_display_product_markup %}{% assign zp_tmp_display_product_markup = nil %}{% else %}{% assign zp_product_markup_integration = '' %}{% endif %}{% if zp_rprfrtg and zp_product_view_type == 'dynamicproductsection' %}{% assign zp_selected_variant_image_id = zp_first_selected_variant.image.id %}{% if zp_selected_variant_image_id != blank %}{% assign zp_carousel_image_id_search = ' data-pc-thumb-img="' | append: zp_selected_variant_image_id | append: '"' %}{% assign zp_carousel_image_id_replace = zp_carousel_image_id_search | append: ' data-default-active' %}{% assign zp_product_content_parts = zp_product_content_parts | replace: zp_carousel_image_id_search, zp_carousel_image_id_replace %}{% endif %}{% endif %}{%- elsif zp_ansmpbndprav == false and zp_asmpbdpf -%}{% assign zp_product_button_text = 'products.product.sold_out' | t %}{% if zp_product_button_text contains 'Translation missing' or zp_product_button_text contains 'translation missing' %}{% assign zp_product_button_text = "Sold Out" %}{% endif %}{% assign zp_product_sticky_button_text = 'products.product.sold_out' | t %}{% if zp_product_sticky_button_text contains 'Translation missing' or zp_product_sticky_button_text contains 'translation missing' %}{% assign zp_product_sticky_button_text = "Sold Out" %}{% endif %}{%- endif -%}{%- if zp_rprfrtg and zp_product_content_parts contains zp_opened_cross_sell_tag -%}{% assign zp_product_with_cross_sell_tag = true %}{%- else -%}{% assign zp_product_with_cross_sell_tag = false %}{%- endif -%}{%- if zp_rprfrmf -%}{% assign zp_cscntprts = zp_current_product_settings['crsslls'] | default: zp_current_product_settings['bndl'] | default: nil %}{% for zp_cscntprt in zp_cscntprts %}{% assign zp_csblkmkr = zp_cscntprt | first %}{% assign zp_cross_sell_product_iterator = zp_cross_sell_product_iterator | plus: 1 %}{% if zp_bndblen %}{% assign zp_cross_sell_selector = 'zpbundleproductselector' %}{% else %}{% assign zp_cross_sell_selector = 'zpcrosssellselector' %}{% endif %}{% assign zp_cross_sell_selector = zp_cross_sell_selector | append: zp_product_selector_suffix | append: zp_cross_sell_product_iterator %}{% assign zp_cross_sell_wrapper_selector = zp_cross_sell_selector | append: zp_main_product_selector_suffix %}{% assign zp_cross_sell_empty_wrapper_selector = zp_cross_sell_wrapper_selector | append: '-empty' %}{% capture zp_crrpi %}zp_cross_sell_wrapper_selector_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi2 %}<zpdcsprdct_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi3 %}</zpdcsprdct_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi4 %}zp_cross_sell_wrapper_selector_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi5 %}zps_csimgsrc_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi6 %}<zpcsslprc_{{ zp_csblkmkr }}></zpcsslprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi7 %}<zpprscst_{{ zp_csblkmkr }}></zpprscst_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi8 %}zpprscsa_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi9 %}<zpprscsdes_{{ zp_csblkmkr }}></zpprscsdes_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi10 %}<zpcsprc_{{ zp_csblkmkr }}></zpcsprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi11 %}<zpcscompareprc_{{ zp_csblkmkr }}></zpcscompareprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi12 %}<zpcssvdprc_{{ zp_csblkmkr }}></zpcssvdprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi13 %}<zpcsdcsamnt_{{ zp_csblkmkr }}></zpcsdcsamnt_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi14 %}<zpcsildcs_{{ zp_csblkmkr }}></zpcsildcs_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi15 %}zps_csunvavlblprdcl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi16 %}zps_csunavlbldccl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi17 %}zps_csimgdattr_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi18 %}zps_csunvavlblqtycl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi19 %}<zpcsvrnts_{{ zp_csblkmkr }}></zpcsvrnts_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi20 %}<zpcsvrnts_{{ zp_csblkmkr }}></zpcsvrnts_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi21 %}<zpcsqtyctrl_{{ zp_csblkmkr }}></zpcsqtyctrl_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi22 %}<zpcspmsg_{{ zp_csblkmkr }}></zpcspmsg_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi23 %}<zpcsunprc_{{ zp_csblkmkr }}></zpcsunprc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi24 %}<zpcsunplb_{{ zp_csblkmkr }}></zpcsunplb_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi25 %}zps_unvavlblcsunp_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi26 %}zps_unvavlblbndilcl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi27 %}<zpcsaccc_{{ zp_csblkmkr }}></zpcsaccc_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi28 %}zps_qtyicrtl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi29 %}zps_qtyinttl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi30 %}zps_qtydcrtl_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi31 %}zps_elid_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi32 %}zps_qtyattr_{{ zp_csblkmkr }}{% endcapture %}{% capture zp_crrpi33 %}<zpcsctw_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi34 %}</zpcsctw_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi35 %}<zpcspcim_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi36 %}</zpcspcim_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi37 %}<zpcspcv_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi38 %}</zpcspcv_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi39 %}<zpcsctwt_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi40 %}</zpcsctwt_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi41 %}<zpcspcti_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi42 %}</zpcspcti_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi43 %}<zpcspctv_{{ zp_csblkmkr }}>{% endcapture %}{% capture zp_crrpi44 %}</zpcspctv_{{ zp_csblkmkr }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_cross_sell_empty_wrapper_selector | replace: zp_crrpi2, '<div style="display:none!important">' | replace: zp_crrpi3, '</div>' | replace: zp_crrpi4, zp_cross_sell_wrapper_selector | replace: zp_crrpi5, zp_csimg | replace: zp_crrpi6, zp_fmcsslpr | replace: zp_crrpi7, zp_cspttl | replace: zp_crrpi8, zp_cspttla | replace: zp_crrpi9, zp_cspdsc | replace: zp_crrpi10, zp_fmcspr | replace: zp_crrpi11, zp_fmcscapr | replace: zp_crrpi12, zp_fmcsdsctsvam | replace: zp_crrpi13, zp_fmcsdsctam | replace: zp_crrpi14, zp_csdsctamwsfx | replace: zp_crrpi15, zp_uncscls | replace: zp_crrpi16, zp_csunvbdsctcl | replace: zp_crrpi17, zp_csimgdatr | replace: zp_crrpi18, zp_uncsqtycls | replace_first: zp_crrpi19, zp_csvrntcntr | replace: zp_crrpi20, '' | replace: zp_crrpi21, zp_csqtycntr | replace: zp_crrpi22, zp_csmsgs | replace: zp_crrpi23, zp_csupblck | replace: zp_crrpi24, zp_csuplbl | replace: zp_crrpi25, zp_csupwrprcls | replace: zp_crrpi26, zp_unbndpiwrcl | replace: zp_crrpi27, zp_csatnlclscnt | replace: zp_crrpi28, zp_csqtyicrtr | replace: zp_crrpi29, zp_csqtyinpttr | replace: zp_crrpi30, zp_csqtydcrtr | replace: zp_crrpi31, zp_cselid | replace: zp_crrpi32, '' | replace: zp_crrpi33, '' | replace: zp_crrpi34, '' | replace: zp_crrpi35, '' | replace: zp_crrpi36, '' | replace: zp_crrpi37, '' | replace: zp_crrpi38, '' | replace: zp_crrpi39, '' | replace: zp_crrpi40, '' | replace: zp_crrpi41, '' | replace: zp_crrpi42, '' | replace: zp_crrpi43, '' | replace: zp_crrpi44, '' %}{% endfor %}{%- elsif zp_product_with_cross_sell_tag -%}{% assign zp_cross_sell_product_iterator = zp_cross_sell_product_iterator | plus: 1 %}{% if zp_bndblen %}{% assign zp_cross_sell_selector = 'zpbundleproductselector' %}{% else %}{% assign zp_cross_sell_selector = 'zpcrosssellselector' %}{% endif %}{% assign zp_cross_sell_selector = zp_cross_sell_selector | append: zp_product_selector_suffix | append: zp_cross_sell_product_iterator %}{% assign zp_cross_sell_wrapper_selector = zp_cross_sell_selector | append: zp_main_product_selector_suffix %}{% assign zp_cross_sell_empty_wrapper_selector = zp_cross_sell_wrapper_selector | append: '-empty' %}{% assign zp_product_content_parts = zp_product_content_parts | replace: 'zp_cross_sell_wrapper_selector', zp_cross_sell_empty_wrapper_selector | replace: zp_opened_cross_sell_tag, '<div style="display:none!important" ' | replace: zp_closed_cross_sell_tag, '</div>' | replace: 'zp_cross_sell_wrapper_selector', zp_cross_sell_wrapper_selector | replace: 'zps_csimgsrc', zp_csimg | replace: '<zpcsslprc></zpcsslprc>', zp_fmcsslpr | replace: '<zpcsprc></zpcsprc>', zp_fmcspr | replace: '<zpcscompareprc></zpcscompareprc>', zp_fmcscapr | replace: '<zpcssvdprc></zpcssvdprc>', zp_fmcsdsctsvam | replace: '<zpcsdcsamnt></zpcsdcsamnt>', zp_fmcsdsctam | replace: 'zps_csunvavlblprdcl', zp_uncscls | replace: 'zps_csunavlbldccl', zp_csunvbdsctcl | replace: 'zps_csimgdattr', zp_csimgdatr | replace: 'zps_csunvavlblqtycl', zp_uncsqtycls | replace_first: '<zpcsvrnts></zpcsvrnts>', zp_csvrntcntr | replace: '<zpcsqtyctrl></zpcsqtyctrl>', zp_csqtycntr | replace: '<zpcspmsg></zpcspmsg>', zp_csmsgs | replace: '<zpcsunprc></zpcsunprc>', zp_csupblck | replace: 'zps_unvavlblcsunp', zp_csupwrprcls | replace: 'zps_unvavlblbndilcl', zp_unbndpiwrcl %}{%- endif -%}{% if zp_rprfrmf %}{% capture zp_pvrnttgn %}<zpprdvrnts_{{ zp_pblkmkr }}></zpprdvrnts_{{ zp_pblkmkr }}>{% endcapture %}{% unless zp_current_entity_content contains zp_pvrnttgn %}{% assign zp_padclcnt = zp_padclcnt | append: zp_pvrntcntr %}{% endunless %}{% capture zp_pqtytgn %}<zpprdqtyctrl_{{ zp_pblkmkr }}></zpprdqtyctrl_{{ zp_pblkmkr }}>{% endcapture %}{% if zp_current_entity_content contains zp_pqtytgn %}{% assign zp_pioqtytg = true %}{% else %}{% assign zp_pioqtytg = false %}{% endif %}{% capture zp_pqtytgn %}zps_qtyattr_{{ zp_pblkmkr }}{% endcapture %}{% if zp_current_entity_content contains zp_pqtytgn %}{% assign zp_pinqtysl = true %}{% else %}{% assign zp_pinqtysl = false %}{% endif %}{% if zp_pioqtytg == false and zp_pinqtysl == false %}{% assign zp_padclcnt = zp_padclcnt | append: zp_pqtycntr %}{% endif %}{% if zp_padclcnt.size > 0 %}{% assign zp_padclcnt = '<div style="display:none!important">' | append: zp_padclcnt | append: '</div>' %}{% endif %}{% assign zp_additional_product_footer_data = '' | append: zp_product_additional_integrations | append: zp_product_init_script | append: zp_product_markup_integration | append: zp_product_footer_integration_content %}{% if zp_product_found %}{% assign zp_puntxt = 'products.product.sold_out' | t %}{% if zp_puntxt contains 'Translation missing' or zp_puntxt contains 'translation missing' %}{% assign zp_puntxt = "Sold Out" %}{% endif %}{% else %}{% assign zp_puntxt = zp_product_button_text %}{% endif %}{% for zp_btnstgs in zp_current_product_settings['btns'] %}{% capture zp_btntg %}<zpprdsbtntxt_{{ zp_pblkmkr }}_{{ zp_btnstgs['id'] }}></zpprdsbtntxt_{{ zp_pblkmkr }}_{{ zp_btnstgs['id'] }}>{% endcapture %}{% if zp_any_product_variant_available %}{% assign zp_btncpt = zp_btnstgs['cpt'] %}{% else %}{% assign zp_btncpt = zp_puntxt %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_btntg, zp_btncpt %}{% endfor %}{% capture zp_crrpi %}zp_product_wrapper_selector_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi2 %}<zpprdprc_{{ zp_pblkmkr }}></zpprdprc_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi3 %}<zpprdcompareprc_{{ zp_pblkmkr }}></zpprdcompareprc_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi4 %}<zpprdvrnts_{{ zp_pblkmkr }}></zpprdvrnts_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi5 %}<zpprdvrnts_{{ zp_pblkmkr }}></zpprdvrnts_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi6 %}<zpprdqtyctrl_{{ zp_pblkmkr }}></zpprdqtyctrl_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi7 %}<zpprdform_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi8 %}</zpprdform_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi9 %}zps_unvavlblprdcl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi10 %}zps_unavlbldccl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi11 %}zps_unvvlblprdpdwc_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi12 %}zps_unvavlblqtycl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi13 %}<zpprdbtntxt_{{ zp_pblkmkr }}></zpprdbtntxt_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi14 %}<zpprdstkbtntxt_{{ zp_pblkmkr }}></zpprdstkbtntxt_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi15 %}zps_prdbtntp_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi16 %}<zpprdbtnintgr_{{ zp_pblkmkr }}></zpprdbtnintgr_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi17 %}<zpprdbtnmsg_{{ zp_pblkmkr }}></zpprdbtnmsg_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi18 %}zps_prdimgsrc_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi19 %}zps_prdimgdattr_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi20 %}<zpprdslprc_{{ zp_pblkmkr }}></zpprdslprc_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi21 %}<zpprdsvdprc_{{ zp_pblkmkr }}></zpprdsvdprc_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi22 %}<zpprddcsamnt_{{ zp_pblkmkr }}></zpprddcsamnt_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi23 %}<zpprdildcs_{{ zp_pblkmkr }}></zpprdildcs_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi24 %}<zpprdimgldr_{{ zp_pblkmkr }}></zpprdimgldr_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi25 %}<zppradtnfintgr_{{ zp_pblkmkr }}></zppradtnfintgr_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi26 %}<zpprdunprc_{{ zp_pblkmkr }}></zpprdunprc_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi27 %}<zpprdunplb_{{ zp_pblkmkr }}></zpprdunplb_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi28 %}zps_unvavlblprdunp_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi29 %}zps_csunvavlblgcl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi30 %}<zpprdfptrm_{{ zp_pblkmkr }}></zpprdfptrm_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi31 %}zps_unavlblfptrm_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi32 %}<zpprdrvavg_{{ zp_pblkmkr }}></zpprdrvavg_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi33 %}<zpprdrvcnt_{{ zp_pblkmkr }}></zpprdrvcnt_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi34 %}zps_unvavlblprdrv_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi35 %}<zpprspt_{{ zp_pblkmkr }}></zpprspt_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi36 %}zpprspa_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi37 %}<zpprspdes_{{ zp_pblkmkr }}></zpprspdes_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi38 %}<zppdcb_{{ zp_pblkmkr }}></zppdcb_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi39 %}<zpprdaccc_{{ zp_pblkmkr }}></zpprdaccc_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi40 %}zps_unvavlblbndilcl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi41 %}zps_csunvavlblbwcl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi42 %}zps_qtyicrtl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi43 %}zps_qtyinttl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi44 %}zps_qtydcrtl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi45 %}zps_qtyattr_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi46 %}<zplpsbcr_{{ zp_pblkmkr }}></zplpsbcr_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi47 %}<zpaptsbcr_{{ zp_pblkmkr }}></zpaptsbcr_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi48 %}<zpslsbcr_{{ zp_pblkmkr }}></zpslsbcr_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi49 %}<zprcsbcr_{{ zp_pblkmkr }}></zprcsbcr_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi50 %}zps_elid_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi51 %}zps_qtyattr_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi53 %}<zpdprdct_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi54 %}</zpdprdct_{{ zp_pblkmkr }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_product_wrapper_selector | replace: zp_crrpi2, zp_formatted_product_price | replace: zp_crrpi3, zp_formatted_product_compare_at_price | replace_first: zp_crrpi4, zp_pvrntcntr | replace: zp_crrpi5, '' | replace: zp_crrpi6, zp_pqtycntr | replace: zp_crrpi7, zp_pfoptg | replace: zp_crrpi8, zp_pfcstg | replace: zp_crrpi9, zp_unpcls | replace: zp_crrpi10, zp_unavailable_discount_class | replace: zp_crrpi11, zp_prdscdfwcl | replace: zp_crrpi12, zp_unqtycls | replace: zp_crrpi13, zp_product_button_text | replace: zp_crrpi14, zp_product_sticky_button_text | replace: zp_crrpi15, zp_product_button_type | replace: zp_crrpi16, zp_pbtnintgr | replace: zp_crrpi17, zp_pbtnmsg | replace: zp_crrpi18, zp_product_image | replace: zp_crrpi19, zp_product_image_data_attrs | replace: zp_crrpi20, zp_formatted_product_sale_price | replace: zp_crrpi21, zp_formatted_product_saved_amount | replace: zp_crrpi22, zp_formatted_product_discount_amount | replace: zp_crrpi23, zp_product_discount_amount_with_suffix | replace: zp_crrpi24, zp_pimgldcnt | replace: zp_crrpi25, zp_pftintctn | replace: zp_crrpi26, zp_pupblk | replace: zp_crrpi27, zp_puplbl | replace: zp_crrpi28, zp_pupwrcl | replace: zp_crrpi29, zp_uncswrcl | replace: zp_crrpi30, zp_pfpmtcp | replace: zp_crrpi31, zp_unpfptrmscl | replace: zp_crrpi32, zp_product_review_rating_text | replace: zp_crrpi33, zp_product_review_count | replace: zp_crrpi34, zp_product_review_wrapper_classes | replace: zp_crrpi35, zp_product_title | replace: zp_crrpi36, zp_pttpra | replace: zp_crrpi37, zp_product_description | replace: zp_crrpi38, zp_checkout_button | replace: zp_crrpi39, zp_padclcnt | replace: zp_crrpi40, zp_unbndpiwrcl | replace: zp_crrpi41, zp_smpbndlprdctwprprcls | replace: zp_crrpi42, zp_pqtyicrtr | replace: zp_crrpi43, zp_pqtyinpttr | replace: zp_crrpi44, zp_pqtydcrtr | replace_first: zp_crrpi45, zp_pqtyaatr | replace: zp_crrpi46, zp_lpscrwgc | replace: zp_crrpi47, zp_aptscrwgc | replace: zp_crrpi48, zp_slscrwgc | replace: zp_crrpi49, zp_rcscrwgc | replace: zp_crrpi50, zp_product_element_id | replace: zp_crrpi51, '' | replace: zp_crrpi53, "" | replace: zp_crrpi54, zp_additional_product_footer_data %}{% if zp_product_sold_out_view_enabled %}{% capture zp_crrpi %}zps_unvavlblprdwcl_{{ zp_pblkmkr }}{% endcapture %}{% capture zp_crrpi2 %}zps_unvavlbllnkcl_{{ zp_pblkmkr }}{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_unpbwrcls | replace: zp_crrpi2, zp_unplnbcls %}{% endif %}{% else %}{% assign zp_product_content_parts = zp_product_content_parts | replace: 'zp_product_wrapper_selector', zp_product_wrapper_selector | replace: '<zpprdprc></zpprdprc>', zp_formatted_product_price | replace: '<zpprdcompareprc></zpprdcompareprc>', zp_formatted_product_compare_at_price | replace_first: '<zpprdvrnts></zpprdvrnts>', zp_pvrntcntr | replace: '<zpprdqtyctrl></zpprdqtyctrl>', zp_pqtycntr | replace: '<zpprdform>', zp_pfoptg | replace: '</zpprdform>', zp_pfcstg | replace: 'zps_unvavlblprdcl', zp_unpcls | replace: 'zps_unavlbldccl', zp_unavailable_discount_class | replace: 'zps_unvavlblqtycl', zp_unqtycls | replace: '<zpprdbtntxt></zpprdbtntxt>', zp_product_button_text | replace: '<zpprdstkbtntxt></zpprdstkbtntxt>', zp_product_sticky_button_text | replace: 'zps_prdbtntp', zp_product_button_type | replace: '<zpprdbtnintgr></zpprdbtnintgr>', zp_pbtnintgr | replace: '<zpprdbtnmsg></zpprdbtnmsg>', zp_pbtnmsg | replace: 'zps_prdimgsrc', zp_product_image | replace: 'zps_prdimgdattr', zp_product_image_data_attrs | replace: '<zpprdslprc></zpprdslprc>', zp_formatted_product_sale_price | replace: '<zpprdsvdprc></zpprdsvdprc>', zp_formatted_product_saved_amount | replace: '<zpprddcsamnt></zpprddcsamnt>', zp_formatted_product_discount_amount | replace: '<zpprdimgldr></zpprdimgldr>', zp_pimgldcnt | replace: '<zppradtnfintgr></zppradtnfintgr>', zp_pftintctn | replace: '<zpprdunprc></zpprdunprc>', zp_pupblk | replace: '<zpprdunplb></zpprdunplb>', zp_puplbl | replace: 'zps_unvavlblprdunp', zp_pupwrcl | replace: 'zps_csunvavlblgcl', zp_uncswrcl | replace: '<zpprdfptrm></zpprdfptrm>', zp_pfpmtcp | replace: 'zps_unavlblfptrm', zp_unpfptrmscl | replace: '<zpprdrvavg></zpprdrvavg>', zp_product_review_rating_text | replace: '<zpprdrvcnt></zpprdrvcnt>', zp_product_review_count | replace: 'zps_unvavlblprdrv', zp_product_review_wrapper_classes | replace: '<zpprdmrkp></zpprdmrkp>', '' %}{% assign zp_current_entity_content = zp_current_entity_content | append: zp_product_additional_integrations | append: zp_product_content_parts | append: zp_product_init_script | append: zp_product_markup_integration | append: zp_product_footer_integration_content | append: zp_product_content[1] %}{% if zp_product_sold_out_view_enabled %}{% capture zp_crrpi %}zps_unvavlblprdwcl_{{ zp_product_element_id }}{% endcapture %}{% capture zp_crrpi2 %}zps_unvavlbllnkcl_{{ zp_product_element_id }}{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, zp_unpbwrcls | replace: zp_crrpi2, zp_unplnbcls %}{% endif %}{% endif %}{% if zp_rprfrmf %}{% capture zp_crrpi %}<zpctw_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi2 %}</zpctw_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi3 %}<zppcim_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi4 %}</zppcim_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi5 %}<zppcv_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi6 %}</zppcv_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi7 %}<zpctwt_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi8 %}</zpctwt_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi9 %}<zppcti_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi10 %}</zppcti_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi11 %}<zppctv_{{ zp_pblkmkr }}>{% endcapture %}{% capture zp_crrpi12 %}</zppctv_{{ zp_pblkmkr }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_crrpi, '' | replace: zp_crrpi2, '' | replace: zp_crrpi3, '' | replace: zp_crrpi4, '' | replace: zp_crrpi5, '' | replace: zp_crrpi6, '' | replace: zp_crrpi7, '' | replace: zp_crrpi8, '' | replace: zp_crrpi9, '' | replace: zp_crrpi10, '' | replace: zp_crrpi11, '' | replace: zp_crrpi12, '' %}{% endif %}{% if zp_pprid %}{% capture zp_pphrpr %}zps_pp{{ zp_pprid }}_{% endcapture %}{% capture zp_purrpr %}zps_pu{{ zp_pprid }}_{% endcapture %}{% capture zp_pttrpr %}zps_pt{{ zp_pprid }}_{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_pphrpr, zp_pphpr | replace: zp_purrpr, zp_purpr | replace: zp_pttrpr, zp_pttpr %}{% endif %}{% if zp_pstprcnt.size > 0 %}{% assign zp_pstprcnt = zp_pstprcnt | replace: '\u0026', '&' | replace: '\u003c', '<' | replace: '\u003e', '>' | replace: '\u0022', '"' %}{% if zp_app_integrations contains 'bestcurrencyconverter' %}{% assign zp_static_price = zp_pstprcnt | strip | strip_html | split: '' %}{% assign zp_static_selected_prices = '' %}{% assign zp_available_chars = "0123456789,. '" | split: '' %}{% assign zp_price_delimiter_detected = false %}{% for zp_tmp_part in zp_static_price %}{% if zp_available_chars contains zp_tmp_part %}{% if zp_tmp_part == ',' or zp_tmp_part == '.' %}{% assign zp_price_delimiter_detected = true %}{% assign zp_static_price_char = zp_tmp_part %}{% elsif zp_tmp_part == ' ' and zp_price_delimiter_detected %}{% assign zp_price_delimiter_detected = false %}{% assign zp_static_price_char = '__zp1502150400__' %}{% else %}{% assign zp_static_price_char = zp_tmp_part %}{% endif %}{% else %}{% assign zp_static_price_char = '__zp1502150400__' %}{% endif %}{% assign zp_static_selected_prices = zp_static_selected_prices | append: zp_static_price_char %}{% endfor %}{% assign zp_static_price = '' %}{% assign zp_static_selected_prices = zp_static_selected_prices | split: '__zp1502150400__' %}{% for zp_tmp_part in zp_static_selected_prices %}{% assign zp_tmppart = '' | append: zp_tmp_part | strip %}{% if zp_tmppart.size > 0 %}{% assign zp_tmppart = zp_amount_format_template | replace: zp_amount_template, zp_tmppart %}{% assign zp_tmppart_without_html = zp_tmppart | strip_html | strip %}{% assign zp_pstprcnt = zp_pstprcnt | replace: zp_tmppart_without_html, zp_tmppart %}{% endif %}{% endfor %}{% assign zp_static_selected_prices = '' %}{% assign zp_available_chars = '' %}{% assign zp_tmppart = '' %}{% assign zp_tmppart_without_html = '' %}{% endif %}{% if zp_rprfrmf %}{% capture zp_updated_product_price %}<zpprdstcprc_{{ zp_pblkmkr }}></zpprdstcprc_{{ zp_pblkmkr }}>{% endcapture %}{% assign zp_current_entity_content = zp_current_entity_content | replace: zp_updated_product_price, zp_pstprcnt %}{% else %}{% assign zp_product_content_parts = zp_product_content_parts | replace: '<zpprdstcprc></zpprdstcprc>', zp_pstprcnt %}{% endif %}{% endif %}{% if zp_pstcpprcnt.size > 0 %}{% assign zp_pstcpprcnt = zp_pstcpprcnt | replace: '\u0026', '&' | replace: '\u003c', '<' | replace: '\u003e', '>' | replace: '\u0022', '"' %}{% if zp_app_integrations contains 'bestcurrencyconverter' %}{% assign zp_static_price = zp_pstcpprcnt | strip | strip_html | split: '' %}{% assign zp_static_selected_prices = '' %}{% assign zp_available_chars = "0123456789,. '" | split: '' %}{% assign zp_price_delimiter_detected = false %}{% for zp_tmp_part in zp_static_price %}{% if zp_available_chars contains zp_tmp_part %}{% if zp_tmp_part == ',' or zp_tmp_part == '.' %}{% assign zp_price_delimiter_detected = true %}{% assign zp_static_price_char = zp_tmp_part %}{% elsif zp_tmp_part == ' ' and zp_price_delimiter_detected %}{% assign zp_price_delimiter_detected = false %}{% assign zp_static_price_char = '__zp1502150400__' %}{% else %}{% assign zp_static_price_char = zp_tmp_part %}{% endif %}{% else %}{% assign zp_static_price_char = '__zp1502150400__' %}{% endif %}{% assign zp_static_selected_prices = zp_static_selected_prices | append: zp_static_price_char %}{% endfor %}{% assign zp_static_price = '' %}{% assign zp_static_selected_prices = zp_static_selected_prices | split: '__zp1502150400__' %}{% for zp_tmp_part in zp_static_selected_prices %}{% assign zp_tmppart = '' | append: zp_tmp_part | strip %}{% if zp_tmppart.size > 0 %}{% assign zp_tmppart = zp_amount_format_template | replace: zp_amount_template, zp_tmppart %}{% assign zp_tmppart_without_html = zp_tmppart | strip_html | strip %}{% assign zp_pstcpprcnt = zp_pstcpprcnt | replace: zp_tmppart_without_html, zp_tmppart %}{% endif %}{% endfor %}{% assign zp_static_selected_prices = '' %}{% assign zp_available_chars = '' %}{% assign zp_tmppart = '' %}{% assign zp_tmppart_without_html = '' %}{% endif %}{% if zp_rprfrmf %}{% capture zp_updated_product_price %}<zpprdstccprprc_{{ zp_pblkmkr }}></zpprdstccprprc_{{ zp_pblkmkr }}>{% endcapture %}{% assign zp_product_content_parts = zp_product_content_parts | replace: zp_updated_product_price, zp_pstcpprcnt %}{% else %}{% assign zp_product_content_parts = zp_product_content_parts | replace: '<zpprdstccprprc></zpprdstccprprc>', zp_pstcpprcnt %}{% endif %}{% endif %}{% if zp_uspgrt %}{% assign zp_pgrdprscnt = zp_pgrdprscnt | append: zp_current_entity_content %}{% assign zp_current_entity_content = zp_pgrtpl %}{% endif %}{% endfor %}{% if zp_uspgrt %}{% assign zp_current_entity_content = zp_tentcnt | replace: zp_pgrtpl, zp_pgrdprscnt | replace: zp_pgrtplotg, '' | replace: zp_pgrtplctg, '' %}{% assign zp_pgrdprscnt = nil %}{% assign zp_tentcnt = nil %}{% endif %}{%- endif -%}{%- endfor -%}{% assign zp_pshcren = false %}{% assign zp_pcrdt = nil %}{% assign zp_lpscrwgc = nil %}{% assign zp_aptscrwgc = nil %}{% assign zp_slscrwgc = nil %}{% if zp_app_integrations contains 'shoppaywidget' %}{% assign zp_spwvcl = '' %}{% else %}{% assign zp_spwvcl = 'hidden' %}{% endif %}{% assign zp_current_entity_content = zp_current_entity_content | replace: 'zps_dsbspintgcl', zp_spwvcl | replace: '<zpprdbtnldr></zpprdbtnldr>', '<svg class="zpa-btn-loader zpa-btn-custom__icon" width="14px" height="14px"><use href="#buyButtonLoader"></use></svg>' | replace: '<zpprdicrqtyicn></zpprdicrqtyicn>', '<svg class="zpa-quantity-selector__icon" width="10px" height="10px"><use href="#iconIncrease"></use></svg>' | replace: '<zpprddcrqtyicn></zpprddcrqtyicn>', '<svg class="zpa-quantity-selector__icon" width="10px" height="10px"><use href="#iconDecrease"></use></svg>' | replace: '<zpprdstrricn></zpprdstrricn>', '<svg class="zpa-review-stars__icon" width="24px" height="24px"><use href="#reviewStars"></use></svg>' | replace: '<zpprdimgldr></zpprdimgldr>', '<svg class="hidden" width="215px" height="150px" data-zp-image-loader><use href="#elementLoader"></use></svg>' %} 