{% comment %}
  Article structured data
  https://developers.google.com/search/docs/data-types/articles
{% endcomment %}

{% if template contains 'article' %}
  {%- capture article_description  -%}
    {%- if article.excerpt != blank -%}
      {{ article.excerpt | strip_html }}
    {%- else -%}
      {{ article.content | truncatewords: 100 | strip_html }}
    {%- endif -%}
  {%- endcapture -%}

  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "BlogPosting",
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "{{ shop.url }}{{ article.url }}"
      },
      "headline": {{ article.title | json }},
      {% if article.image %}
        "image": {
          "@type": "ImageObject",
          "url": {{ article.image | img_url: '800x800' | prepend: 'https:' | json }},
          "height": 800,
          "width": 800
        },
      {% endif %}
      "datePublished": {{ article.published_at | date: '%Y-%m-%dT%H:%M:%S%z' | json}},
      "dateModified": {{ article.published_at | date: '%Y-%m-%dT%H:%M:%S%z' | json}},
      "author": {
        "@type": "Person",
        "name": {{ article.author | json }}
      },
      "publisher": {
        "@type": "Organization",
        "name": {{ shop.name | json }},
        "logo": {
          "@type": "ImageObject",
          "url": {{ article.user.image | img_url: '200x200' | prepend: 'https:' | json }}
        }
      },
      "description": {{ article_description | json }}
    }
  </script>
{% endif %}

{% comment %}
  Breadcrumbs
  https://developers.google.com/search/docs/data-types/breadcrumbs
{% endcomment %}

{%- if current_tags -%}
  {%- assign tag_names = current_tags | join: ', ' %}
  {% comment %}Converting an array to a handle converts it to a string{% endcomment %}
  {%- capture tag_handles -%}
    {%- for tag in current_tags -%}
      {{- tag | handle | append: '|' -}}
    {%- endfor -%}
  {%- endcapture -%}
{%- endif -%}

{% if collection.url == blank %}
  {% assign collection_url = '/collections/all' %}
{% else %}
  {% assign collection_url = collection.url %}
{% endif %}

{% if template.name == 'product'
    or template.name == 'list-collections'
    or template.name == 'collection'
    or template.name == 'blog'
    or template.name == 'article'
    or template.name == 'search'
    or template.name == 'cart'
    or template.name == 'page'
%}

  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
      {% case template.name %}
        {% when 'product' %}
          {% if product.collections.size >= 1 %}
            {
              "@type": "ListItem",
              "position": 1,
              "item": {
                "@id": "{{ shop.url }}/collections",
                "name": {{ 'structured_data.breadcrumbs.collections' | t | json }}
              }
            },
            {
              "@type": "ListItem",
              "position": 2,
              "item": {
                "@id": "{{ shop.url }}/collections/{{ product.collections.first.handle }}",
                "name": {{ product.collections.first.title | json }}
              }
            },
          {% else %}
            {
              "@type": "ListItem",
              "position": 1,
              "item": {
                "@id": "{{ shop.url }}/products",
                "name": {{ 'structured_data.breadcrumbs.products' | t | json }}
              }
            },
          {% endif %}
          {
            "@type": "ListItem",
            "position": {% if product.collections.size >= 1 %}3{% else %}2{% endif %},
            "item": {
              "@id": "{{ shop.url }}{{ product.url | within: collection }}",
              "name": {{ product.title | json }}
            }
          }
        {% when 'list-collections' %}
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@id": "{{ shop.url }}/collections",
              "name": {{ 'structured_data.breadcrumbs.collections' | t | json }}
            }
          }
          {% if current_page != 1 %}
            ,
            {
              "@type": "ListItem",
              "position": 2,
              "item": {
                "@id": "{{ shop.url }}/collections",
                "name": {{ 'structured_data.breadcrumbs.page' | t: page: current_page | json }}
              }
            }
          {% endif %}
        {% when 'collection' %}
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@id": "{{ shop.url }}/collections",
              "name": {{ 'structured_data.breadcrumbs.collections' | t | json }}
            }
          },
          {
            "@type": "ListItem",
            "position": 2,
            "item": {
              "@id": "{{ shop.url }}{{ collection_url }}",
              "name": {{ collection.title | json }}
            }
          }
          {% if current_tags %}
            ,
            {
              "@type": "ListItem",
              "position": 3,
              "item": {
                "@id": "{{ shop.url }}{{ collection_url }}/{{- tag_handles | split: '|' | join: '+' -}}",
                "name": {{ 'structured_data.breadcrumbs.tags_html' | t: tags: tag_names | json }}
              }
            }
          {% endif %}
          {% if current_page != 1 %}
            {% if current_tags %}
              {% assign position = 4 %}
            {% else %}
              {% assign position = 3 %}
            {% endif %}
            ,
            {
              "@type": "ListItem",
              "position": {{ position }},
              "item": {
                "@id": "{{ shop.url }}/collections?page={{ current_page }}",
                "name": {{ 'structured_data.breadcrumbs.page' | t: page: current_page | json }}
              }
            }
          {% endif %}
        {% when 'blog' or 'article' %}
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@id": "{{ shop.url }}{{ blog.url }}",
              "name": {{ blog.title | json }}
            }
          }
          {% if template.name == 'article' %}
            ,
            {
              "@type": "ListItem",
              "position": 2,
              "item": {
                "@id": "{{ shop.url }}{{ article.url }}",
                "name": {{ article.title | json }}
              }
            }
          {% elsif current_tags %}
            ,
            {
              "@type": "ListItem",
              "position": 2,
              "item": {
                "@id": "{{ shop.url }}{{ blog.url }}/tagged/{{- tag_handles | split: '|' | join: '+' -}}",
                "name": {{ 'structured_data.breadcrumbs.tags_html' | t: tags: tag_names | json }}
              }
            }
          {% endif %}
          {% if current_page != 1 %}
            {% if current_tags %}
              {% assign position = 3 %}
              {%- capture url -%}
                {{ shop.url }}{{ blog.url }}/tagged/{{ tag_handles | split: '|' | join: '+' }}?page={{ current_page }}
              {%- endcapture -%}
            {% else %}
              {% assign position = 2 %}
                {%- capture url -%}
                {{ shop.url }}{{ blog.url }}?page={{ current_page }}
              {%- endcapture -%}
            {% endif %}
            ,
            {
              "@type": "ListItem",
              "position": {{ position }},
              "item": {
                "@id": "{{ url }}",
                "name": {{ 'structured_data.breadcrumbs.page' | t: page: current_page | json }}
              }
            }
          {% endif %}
        {% when 'search' %}
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@id": "{{ shop.url }}/search",
              "name": {{ 'structured_data.breadcrumbs.search' | t | json }}
            }
          }
          {% if search.performed %}
          ,
            {
              "@type": "ListItem",
              "position": 2,
              "item": {
                "@id": "{{ shop.url }}/search?q={{ search.terms }}",
                "name": {{ search.terms | json }}
              }
            }
            {% if current_page != 1 %}
            ,
              {
                "@type": "ListItem",
                "position": 3,
                "item": {
                  "@id": "{{ shop.url }}/search?page={{ current_page }}&q={{ search.terms }}",
                  "name": {{ 'structured_data.breadcrumbs.page' | t: page: current_page | json }}
                }
              }
            {% endif %}
          {% endif %}
        {% when 'cart' %}
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@id": "{{ shop.url }}/cart",
              "name": {{ 'structured_data.breadcrumbs.cart' | t | json }}
            }
          }
        {% when 'page' %}
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@id": "{{ page.url }}",
              "name": {{ page.title | json }}
            }
          }
        {% else %}
        {% endcase %}
      ]
    }
  </script>
{% endif %}

{% comment %}
  Product structured data
  https://developers.google.com/search/docs/data-types/products
{% endcomment %}

{% if template contains 'product' %}
  {% assign selected_variant = product.selected_or_first_available_variant | default: product.variants.first %}
  {% assign product_image = selected_variant.featured_image | default: product.featured_image %}
  {%- capture product_name -%}
    {{ product.title }}
    {%- if selected_variant.title != 'Default Title' and selected_variant.option1 == 'Default Title' -%}
      - {{ selected_variant.title }}
    {%- endif -%}
  {%- endcapture -%}

  {%- assign now = 'now' | date: '%Y-%m-%d' | split: '-' -%}
  {% capture year_from_now %}{{ now[0] | plus: 1 }}-{{ now[1] }}-{{ now[2] | at_most: 28 }}{% endcapture %}

  <script type="application/ld+json">
  {
    "@context": "http://schema.org/",
    "@type": "Product",
    "name": {{ product_name | strip_newlines | json }},
    "image": {{ product_image | img_url: '1024x1024' | prepend: 'https:' | json }},
    {% if product.description != blank %}
      "description": {{ product.description | json }},
    {% endif %}
    {% if product.vendor %}
      "brand": {
        "@type": "Brand",
        "name": {{ product.vendor | json }}
      },
    {% endif %}
    {% if selected_variant.sku != blank %}
      "sku": {{ selected_variant.sku | json }},
    {% endif %}
    {% if selected_variant.barcode != blank %}
      "mpn": {{ selected_variant.barcode | json }},
    {% endif %}
    {% if product.metafields.reviews.rating.value != blank %}
      "review": {
        "@type": "Review",
          "reviewRating": {
          "@type": "Rating",
          "ratingValue": "{{ product.metafields.reviews.rating.value }}"
        }
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "{{ product.metafields.reviews.rating.value }}",
        "reviewCount": "{{ product.metafields.reviews.rating_count }}"
      },
    {% endif %}
    "offers": {
      "@type": "Offer",
      "priceCurrency": {{ cart.currency.iso_code | json }},
      "price": {{ selected_variant.price | divided_by: 100.0 | json }},
      "availability": "http://schema.org/{% if selected_variant.available %}InStock{% else %}OutOfStock{% endif %}",
      "url": "{{ shop.url }}{{ selected_variant.url }}",
      "seller": {
        "@type": "Organization",
        "name": {{ shop.name | json }}
      },
      "priceValidUntil": {{ year_from_now | json }}
    }
  }
  </script>
{% endif %}

{% comment %}
  Store information
  https://developers.google.com/search/docs/data-types/sitename
{% endcomment %}

<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "WebSite",
    "name": "{{ shop.name }}",
    "url": "{{ shop.url }}"
  }
</script>
