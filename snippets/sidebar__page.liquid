{% if page != blank %}
  <div class="sidebar-block__toggle" data-has-toggle-option>
    <h3 class="sidebar-block__heading" {% if settings.toggle_sidebar %}data-sidebar-block__toggle="closed" aria-label="toggle"{% endif %}>
      {{ pages[page].title }}
      {% if settings.toggle_sidebar %}
        <button class="sidebar-block__toggle-icon icon-style--{{ settings.toggle_icon_style }}">
          {% if settings.toggle_icon_style == 'plus_and_minus' %}
            {% render 'icon',
                    name: 'plus',
                    icon_class: 'icon--active'
            %}
            {% render 'icon', name: 'minus' %}
          {% else %}
            {% render 'icon',
                    name: 'down-caret',
                    icon_class: 'icon--active'
            %}
          {% endif %}
        </button>
      {% endif %}
    </h3>
  </div>
  <div class="sidebar-block__content content" {% if settings.toggle_sidebar %}data-sidebar-block__content--collapsible{% endif %}>
    {{ pages[page].content }}
  </div>
{% else %}
  <div class="sidebar-block__toggle" data-has-toggle-option>
    <h3 class="sidebar-block__heading" {% if settings.toggle_sidebar %}data-sidebar-block__toggle="closed" aria-label="toggle"{% endif %}>
      {% if settings.toggle_sidebar %}
        <button class="sidebar-block__toggle-icon icon-style--{{ settings.toggle_icon_style }}">
          {% if settings.toggle_icon_style == 'plus_and_minus' %}
            {% render 'icon',
                    name: 'plus',
                    icon_class: 'icon--active'
            %}
            {% render 'icon', name: 'minus' %}
          {% else %}
            {% render 'icon',
                    name: 'down-caret',
                    icon_class: 'icon--active'
            %}
          {% endif %}
        </button>
      {% endif %}
    </h3>
  </div>
  <div class="sidebar-block__content content" {% if settings.toggle_sidebar %}data-sidebar-block__content--collapsible{% endif %}>
    <p>{{ 'homepage.onboarding.no_content' | t }}</p>
  </div>
{% endif %}
