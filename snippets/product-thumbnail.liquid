{% if related_products == true %}
  {% assign hover_enabled = block.settings.show_related_products_title_on_hover %}
{% else %}
  {% assign hover_enabled = settings.thumbnail_hover_enabled %}
{% endif %}

{% if type == 'block' %}
  {% assign object = block %}
{% else %}
  {% assign object = section %}
{% endif %}

{% assign secondaryImage = product.media[1] %}

{% assign collection_handles = product.collections | map: 'handle' %}
{% if product.featured_media.preview_image.alt contains 'youtube' or image.alt contains 'vimeo' %}
  {% assign alt_text = product.title %}
{% else %}
  {% assign alt_text = product.featured_media.preview_image.alt %}
{% endif %}

{% if type == 'block' %}
  {% assign image_crop = block.settings.image_crop %}
{% else %}
  {% assign image_crop = section.settings.image_crop %}
{% endif %}

{% if align_height == true %}
  {% style %}
    .shopify-model-viewer-ui model-viewer {
      --progress-bar-height: 2px;
      --progress-bar-color: {{ settings.regular_color }};
    }
  {% endstyle %}
{% endif %}

<div class="
  {%
    render 'column-width',
    value: products_per_row
  %}
  medium-down--one-half
  {% if mobile_products_per_row == '1' %}
    small-down--one-whole
  {% else %}
    small-down--one-half
  {% endif %}
  column
  {% if settings.enable_quickshop %}
    quick-shop--true
    quick-shop--closed
    product-{{ product.id }} js-product_section
  {% endif %}
  {% if settings.stickers_enabled %}
    has-thumbnail-sticker
  {% endif %}
  {% if product.media[1] != blank and settings.show_secondary_image == true %}
    has-secondary-image-swap
  {% endif %}
  thumbnail
  product__thumbnail
  product__grid-item
  thumbnail__hover-overlay--{{ hover_enabled }}
  has-padding-bottom"
  {% if section.settings.pagination_type != 'basic_pagination' %}
    data-load-more--grid-item
  {% endif %}>
  <div class="product-wrap">

      <div class="product-image__wrapper">
        <div class="image__container product__imageContainer">
          <a href="{{ product.url | within: collection }}">
            {% if settings.stickers_enabled %}
              {%
                render 'product-thumbnail__sticker',
                product: product,
                context: 'thumbnail',
                collection_handles: collection_handles
              %}
            {% endif %}

            {% comment %} Primary image {% endcomment %}
            {%
              render 'image-element',
              image: product.featured_media.preview_image,
              alt: alt_text,
              object_fit: align_height,
              max_height: height,
            %}

            {% comment %} Secondary image {% endcomment %}
            {% if product.media[1] != blank and settings.show_secondary_image == true %}
              {% if product.media[1].media_type == 'external_video' %}
                <div class="product-thumbnail__video secondary swap--visible video-on-hover">
                  <div data-youtube-video>
                    {{ product.media[1] | external_video_tag }}
                  </div>
                </div>
              {% elsif product.media[1].media_type == 'video' %}
                <div class="product-thumbnail__video secondary swap--visible video-on-hover" data-html5-video>
                  {{ product.media[1] | video_tag: controls: false }}
                </div>
              {% else %}
                {%
                  render 'image-element',
                  image: product.media[1],
                  alt: alt_text,
                  object_fit: align_height,
                  max_height: height,
                  additional_classes: 'secondary swap--visible',
                %}
              {% endif %}
            {% endif %}
          </a>
        </div>

        {% if hover_enabled or settings.enable_quickshop %}
          <div class="thumbnail-overlay__container">
            <a class="hidden-product-link" href="{{ product.url | within: collection }}">{{ product.title | escape }}</a>

            {% if hover_enabled %}
              <div class="quick-shop__info animated fadeInDown">
                <div class="thumbnail-overlay">
                  <div class="info text-align-center">
                    {% if settings.display_vendor %}
                      <span class="product-thumbnail__vendor">{{ product.vendor }}</span>
                    {% endif %}

                    <p class="product-thumbnail__title">{{ product.title | escape }}</p>

                    {% if collection_handles contains 'coming-soon' %}
                      <span>{{ 'collections.general.coming_soon' | t }}</span>
                    {% elsif product.available == false %}
                      <span class="product-thumbnail__price">
                        {{ 'collections.general.sold_out' | t }}
                      </span>
                    {% else %}
                      <span
                        class="
                          product-thumbnail__price price
                          {% if product.compare_at_price > product.price %}
                            sale
                          {% endif %}
                        "
                      >
                        {% if product.price_varies %}
                          <small><em>{{ 'products.general.from' | t }}</em></small>
                        {% endif %}
                        {% if product.price_min > 0 %}
                          <span class="money">
                            {%
                              render 'price-element',
                              price: product.price_min
                            %}
                          </span>
                        {% else %}
                          {{ settings.free_price_text }}
                        {% endif %}
                        {%- assign variant = product.selected_or_first_available_variant -%}
                        {% if product.compare_at_price > product.price %}
                          <span class="product-thumbnail__was-price compare-at-price">
                            <span class="money">
                              {%
                                render 'price-element',
                                price: product.compare_at_price,
                              %}
                            </span>
                          </span>
                        {% endif %}
                      </span>
                      {% if settings.select_first_available_variant %}
                        {%- assign variant_for_unit_price = product.variants | sort: 'price' | first -%}
                        {%
                          render 'unit-price',
                          item: variant_for_unit_price,
                          class: 'product-thumbnail__unit-price'
                        %}
                      {% endif %}
                    {% endif %}
                  </div>
                </div>
              </div>
            {% endif %}

            {% if settings.enable_quickshop %}
              {%
                render 'product-thumbnail__quick-shop-button',
                product: product,
              %}
            {% endif %}

            {% if hover_enabled %}
              {% if settings.enable_shopify_collection_badges and product.metafields.reviews.rating.value != blank %}
                <div class="product__rating rating">
                  {%
                    render 'rating-stars',
                    value: product.metafields.reviews.rating.value.rating,
                    scale_max: product.metafields.reviews.rating.value.scale_max,
                  %}

                  <p class="rating__text">
                    <span aria-hidden="true">
                      {{ product.metafields.reviews.rating.value }} / {{ product.metafields.reviews.rating.value.scale_max }}
                    </span>
                  </p>

                  <p class="rating__count">
                    <span aria-hidden="true">
                      {{ product.metafields.reviews.rating_count }}
                      {% if product.metafields.reviews.rating_count > 1 %}
                        {{ "general.accessibility.star_reviews_text" | t }}
                      {% else %}
                        {{ "general.accessibility.star_review_text" | t }}
                      {% endif %}
                    </span>
                  </p>
                </div>
              {% endif %}
            {% endif %}
          </div>
        {% endif %}
      </div>

      <div class="thumbnail__caption text-align-{{ settings.thumbnail_text_alignment }}">
        {%
          render 'product-thumbnail__product-info',
          product: product,
          collection_handles: collection_handles
        %}
      </div>
    </div>

  {%
    render 'product-thumbnail__swatch',
    product: product
  %}

  {% if hover_enabled == false %}
    {% if settings.enable_shopify_collection_badges and product.metafields.reviews.rating.value != blank %}
      <div
        class="
          product__rating
          rating
          is-justify-{{ settings.thumbnail_text_alignment }}
        "
      >
        {%
          render 'rating-stars',
          value: product.metafields.reviews.rating.value.rating,
          scale_max: product.metafields.reviews.rating.value.scale_max,
        %}

        <p class="rating__text">
          <span aria-hidden="true">
            {{ product.metafields.reviews.rating.value }} / {{ product.metafields.reviews.rating.value.scale_max }}
          </span>
        </p>

        <p class="rating__count">
          <span aria-hidden="true">
            {{ product.metafields.reviews.rating_count }}
            {% if product.metafields.reviews.rating_count > 1 %}
              {{ "general.accessibility.star_reviews_text" | t }}
            {% else %}
              {{ "general.accessibility.star_review_text" | t }}
            {% endif %}
          </span>
        </p>
      </div>
    {% endif %}
  {% endif %}
</div>
