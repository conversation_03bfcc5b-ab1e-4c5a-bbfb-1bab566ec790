{% comment %}
Required values
name: <icon name>
______________
Optional values
icon_class: <string>
{% endcomment %}

<span class="icon {{ icon_class }}" data-icon="{{ name | downcase }}">

  {% if name == 'avatar' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="42.97" r="18.28"/><path d="M50,1A49,49,0,1,0,99,50,49.05,49.05,0,0,0,50,1ZM79.43,78.5a33.73,33.73,0,0,0-58.87,0A41,41,0,1,1,91,50,40.85,40.85,0,0,1,79.43,78.5Z"/></g></svg>

  {% elsif name == 'avatar-2' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="28.38" r="23.38"/><path d="M86.23,95a36.23,36.23,0,0,0-72.46,0Z"/></g></svg>

  {% elsif name == 'bag' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M79.67,28.66H68.77V22.19a18.77,18.77,0,1,0-37.54,0v6.47H20.33L16.41,96.58H83.59ZM38.39,22.19a11.61,11.61,0,0,1,23.22,0v6.47H38.39Z"/></g></svg>

  {% elsif name == 'basket' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="82.55 31.46 67.48 5.42 60.55 9.43 73.31 31.46 26.69 31.46 39.45 9.43 32.52 5.42 17.45 31.46 1 31.46 1 45 99 45 99 31.46 82.55 31.46"/><polygon points="19.47 94.58 80.53 94.58 89.61 52 10.38 52 19.47 94.58"/></g></svg>

  {% elsif name == 'battery' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M91.9,32.66H87.5V28.48a5.2,5.2,0,0,0-5.2-5.2H10.2A5.2,5.2,0,0,0,5,28.48v43a5.2,5.2,0,0,0,5.2,5.2H82.3a5.2,5.2,0,0,0,5.2-5.2V67.34h4.4a3.1,3.1,0,0,0,3.1-3.1V35.76A3.1,3.1,0,0,0,91.9,32.66ZM25.72,66.13a1.49,1.49,0,0,1-1.5,1.5H15.41a1.48,1.48,0,0,1-1.5-1.51V33.87a1.48,1.48,0,0,1,1.5-1.5h8.81a1.49,1.49,0,0,1,1.5,1.5Zm18.19,0a1.48,1.48,0,0,1-1.5,1.5H33.59a1.48,1.48,0,0,1-1.5-1.5V33.87a1.49,1.49,0,0,1,1.5-1.5h8.82a1.49,1.49,0,0,1,1.5,1.5Zm18.18,0a1.48,1.48,0,0,1-1.5,1.5H51.78a1.49,1.49,0,0,1-1.5-1.51V33.87a1.49,1.49,0,0,1,1.5-1.5h8.81a1.48,1.48,0,0,1,1.5,1.5Zm17.82-32L74.56,66.41a1.42,1.42,0,0,1-1.4,1.22H70a1.53,1.53,0,0,1-1.5-1.41V34a1.48,1.48,0,0,1,1.5-1.5H78.5A1.42,1.42,0,0,1,79.91,34.16Z"/></g></svg>

  {% elsif name == 'bee' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M88.79,56.71,66.86,39.3a9.37,9.37,0,0,0-3.12-1.65l.67-.83a10.45,10.45,0,0,0,2.48-6.6c0-5-3.79-9.37-9.52-11.39h0c2.21-5.35,6-11.76,10.9-11.31A2.47,2.47,0,0,0,71,5.26a2.5,2.5,0,0,0-2.26-2.72c-8.55-.77-13.8,9-16.27,15.19h0a24.09,24.09,0,0,0-5.09.08h0C44.91,11.59,39.65,1.75,31.05,2.54a2.51,2.51,0,0,0-2.26,2.72,2.48,2.48,0,0,0,2.72,2.26c4.92-.42,8.78,6.13,11,11.51h0c-5.45,2.12-8.94,6.38-8.94,11.19A10.46,10.46,0,0,0,36,36.82l.67.82a2.8,2.8,0,0,0-.31.09,9.09,9.09,0,0,0-2.76,1.48l-22.14,17a16.39,16.39,0,0,0-.59,26,16.71,16.71,0,0,0,6.94,3.37,15.88,15.88,0,0,0,3.43.38,16.08,16.08,0,0,0,8.59-2.52,16.86,16.86,0,0,0,2.85-2.26c.3.48.6.94.93,1.41h0l0,.06a.25.25,0,0,0,0,.07h0A59.61,59.61,0,0,0,48.33,97a2.47,2.47,0,0,0,2.9,0A59.61,59.61,0,0,0,65.86,82.72c.38-.56.75-1.12,1.1-1.69a16.88,16.88,0,0,0,2.78,2.35A16,16,0,0,0,81.63,85.9a16.88,16.88,0,0,0,7.06-3.12,16.33,16.33,0,0,0,.1-26.07ZM41.62,64.5l.27-.58,4-8.73h8.4l4.14,9.31ZM31.71,74a10.58,10.58,0,0,1-.77,1.39,12,12,0,0,1-3.77,3.78,10.91,10.91,0,0,1-8.26,1.49,11.47,11.47,0,0,1-4.39-20.49l22.14-17A4.47,4.47,0,0,1,38,42.47a4.53,4.53,0,0,1,1.41-.23,4.67,4.67,0,0,1,1.75.35,4.16,4.16,0,0,1,1.12.67,4.46,4.46,0,0,1,1.58,2.65,4.86,4.86,0,0,1,.07.82,4.41,4.41,0,0,1-.42,1.88l-1.4,3-6.61,14.3,0,0ZM49.78,91.9a59.3,59.3,0,0,1-8.89-8.1H58.67A59.3,59.3,0,0,1,49.78,91.9ZM89.12,74.63a11.63,11.63,0,0,1-3.44,4.16,11.86,11.86,0,0,1-5,2.19,11.08,11.08,0,0,1-8.2-1.75,12.3,12.3,0,0,1-3.75-4,10.52,10.52,0,0,1-.61-1.17L64.59,66,58.27,51.72s0,0,0,0v0l-1.39-3.12A4.32,4.32,0,0,1,56.53,46a4.43,4.43,0,0,1,1.74-2.87,2.9,2.9,0,0,1,.41-.27.51.51,0,0,1,.16-.09,3.81,3.81,0,0,1,.5-.23l0,0A4.2,4.2,0,0,1,61,42.23a3.59,3.59,0,0,1,.84.09,4.44,4.44,0,0,1,2,.89L85.68,60.63a11.87,11.87,0,0,1,4.53,9.25A10.72,10.72,0,0,1,89.12,74.63Z"/></g></svg>

  {% elsif name == 'behance' %}
    <svg width="100" height="63" viewBox="0 0 100 63" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M90.2843 8.80849V2.57701H65.2362V8.80849H90.2843Z" fill="currentColor"/>
      <path d="M41.3877 28.8803C43.1899 28.0338 44.8001 26.828 46.1196 25.3369C48.1401 22.727 49.1606 19.4806 48.9966 16.1841C49.0917 12.7884 48.095 9.45179 46.153 6.66469C42.9761 2.34005 37.5888 0.118483 29.9911 0H0V62.2038H28.0139C30.9639 62.2252 33.9082 61.9423 36.8002 61.3596C39.1619 60.8971 41.3996 59.9419 43.3671 58.5561C45.3347 57.1704 46.988 55.3854 48.219 53.3175C50.0754 50.358 51.036 46.9247 50.9849 43.4315C51.0835 40.2192 50.2377 37.0485 48.5522 34.312C46.8401 31.7499 44.317 29.837 41.3877 28.8803ZM14.3402 10.7968H25.9256C28.4279 10.7255 30.9255 11.0555 33.3234 11.7743C35.545 12.6962 36.6558 14.6179 36.6558 17.517C36.6558 20.1274 35.8116 21.9601 34.1232 22.9821C32.1164 24.0955 29.84 24.63 27.5474 24.5261H14.3402V10.7968ZM33.79 50.3073C31.7977 51.1358 29.6473 51.515 27.4918 51.4181H14.3402V34.823H27.6696C29.7933 34.746 31.9104 35.1051 33.8899 35.8782C36.5336 37.0779 37.8554 39.3106 37.8554 42.5429C37.8776 46.3751 36.5114 48.9521 33.79 50.3073Z" fill="currentColor"/>
      <path d="M99.5704 32.4237C99.104 29.1197 97.8882 25.9662 96.0159 23.2042C94.1332 20.3322 91.4652 18.0614 88.3293 16.6617C85.0248 15.2325 81.4546 14.5207 77.8546 14.5735C71.3232 14.5735 66.01 16.6358 61.9149 20.7605C57.8198 24.8852 55.776 30.8094 55.7834 38.533C55.7834 46.775 58.0457 52.7251 62.5703 56.3833C66.9967 59.995 72.5531 61.9337 78.2656 61.8594C85.4561 61.8594 91.0433 59.6934 95.0273 55.3613C97.6043 52.6288 99.0373 49.9518 99.3483 47.3082H87.4518C86.6614 48.9581 85.3885 50.3291 83.8016 51.2395C82.2148 52.15 80.3888 52.557 78.5656 52.4067C76.3219 52.4498 74.1129 51.8484 72.2008 50.6739C69.2387 48.8744 67.6836 45.7383 67.5355 41.2655H99.9703C100.065 38.3124 99.9318 35.3562 99.5704 32.4237ZM87.8739 33.5345H67.7576C68.037 30.9637 69.1396 28.552 70.9012 26.6588C71.8221 25.7774 72.9149 25.0957 74.1114 24.6561C75.3079 24.2165 76.5822 24.0285 77.8546 24.104C80.3733 24.0245 82.8316 24.8836 84.7526 26.5144C85.7248 27.414 86.5031 28.5026 87.0398 29.7135C87.5765 30.9245 87.8603 32.2322 87.8739 33.5567V33.5345Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'bicep' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M85.21,31.36a21,21,0,0,0-18.32-2.08A20.67,20.67,0,0,0,54.08,42.16a26.32,26.32,0,0,0-1.59,11.4c-4.16-.49-11.05-.54-16.76,3.15L29,33.75l.18-.11.35.4a5.52,5.52,0,0,0,7.86.43L48.62,24.09a5.52,5.52,0,0,0,1.82-3.94,5.59,5.59,0,0,0-1.6-4L37.63,4.81a7.51,7.51,0,0,0-5.35-2.19A7.32,7.32,0,0,0,27,5L11.17,21.72A20.71,20.71,0,0,0,5.59,35.54L5,77.76A19.37,19.37,0,0,0,24.34,97.38,19.45,19.45,0,0,0,28.12,97C49.54,92.82,78,83.27,91.82,61.61a2.55,2.55,0,0,0,.24-.5l1.76-5A21,21,0,0,0,85.21,31.36Z"/></g></svg>

  {% elsif name == 'bicycle' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M78.64,41a20.83,20.83,0,0,0-4.14.42L64.84,18.52H54.09v7.74H59.7l3.37,8H39.77V26.7H25.45v7.74H30l.78,2.73L27.9,42.12a20.22,20.22,0,1,0,13.32,23h9.09l1-2L63.49,42h2.84l1,2.47A20.23,20.23,0,1,0,78.64,41ZM36.9,42H53.57l-8,15.39H41.22A20.22,20.22,0,0,0,34.6,46ZM30.63,52.9a12.27,12.27,0,0,1,2.6,4.48H28.05ZM21.36,73.74a12.49,12.49,0,0,1,0-25,12.8,12.8,0,0,1,2.57.26l-9.26,16.1H33.23A12.5,12.5,0,0,1,21.36,73.74Zm57.28,0a12.47,12.47,0,0,1-8.17-21.92l4.6,10.93,7.13-3L77.6,48.81c.34,0,.69-.05,1-.05a12.49,12.49,0,0,1,0,25Z"/></g></svg>

  {% elsif name == 'binoculars' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><title>binoculars</title><g><path d="M19,47.33a18,18,0,1,0,18,18v-.06A18,18,0,0,0,19,47.33Z"/><path d="M81,47.33a18,18,0,1,0,18,18v-.06A18,18,0,0,0,81,47.33Z"/><path d="M35.88,49.6l.88-25.21v-.58a7.18,7.18,0,0,0-12.92-4.33l0,.11L22.6,21.94a1,1,0,0,1-.11.23L11.93,43.37a23.08,23.08,0,0,1,24,6.23Z"/><path d="M81,42.26a23.23,23.23,0,0,1,7.08,1.11L77.49,22.17a1,1,0,0,1-.11-.23l-.79-1.56L76,19.24a7.18,7.18,0,0,0-12.73,4.57v.69l.87,25.1A23.22,23.22,0,0,1,81,42.26Z"/><path d="M50,30.7a12.14,12.14,0,0,0-6.77,2.07l-.76,22.31a19,19,0,0,1,15.07,0l-.76-22.3A12.17,12.17,0,0,0,50,30.7Z"/></g></svg>

  {% elsif name == 'bolt' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M76.69,47,33,94.55a1.29,1.29,0,0,1-2.21-1.21L40.59,59H24.32a1.22,1.22,0,0,1-.88-.32,1.28,1.28,0,0,1-.13-1.81L65.72,5.46A1.34,1.34,0,0,1,66.44,5,1.3,1.3,0,0,1,67,5,1.28,1.28,0,0,1,68,6.6L59.34,44.87H75.72a1.24,1.24,0,0,1,.84.32A1.28,1.28,0,0,1,76.69,47Z"/></g></svg>

  {% elsif name == 'bookmark' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="18.33 95 18.33 5 81.67 5 81.67 95 50.56 73.89 18.33 95"/></g></svg>

  {% elsif name == 'box' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="7.5 73.52 47.5 91.17 47.5 46.34 7.5 28.68 7.5 73.52"/><path d="M75.84,36V47.35a2.5,2.5,0,0,1-5,0V38.24L52.5,46.34V91.17l18.34-8.09V71.18a2.5,2.5,0,0,1,5,0v9.69L92.5,73.52V28.68Z"/><polygon points="28.89 17.04 11.19 24.85 50 41.97 67.2 34.38 28.89 17.04"/><polygon points="88.81 24.85 50 7.73 35.05 14.33 73.35 31.68 88.81 24.85"/></g></svg>

  {% elsif name == 'briefcase' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="65.11 74.42 34.89 74.42 34.89 60.73 6.13 60.73 6.13 94.67 93.87 94.67 93.87 60.73 65.11 60.73 65.11 74.42"/><rect x="40.89" y="60.92" width="18.22" height="7.49"/><path d="M77.68,26.88V16A10.68,10.68,0,0,0,67,5.33H33A10.68,10.68,0,0,0,22.32,16V26.88H6.13V54.73H93.87V26.88Zm-8.36-2.46H30.68V16A2.33,2.33,0,0,1,33,13.68H67A2.33,2.33,0,0,1,69.32,16Z"/></g></svg>

  {% elsif name == 'bullet' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="50" r="17"/></g></svg>

  {% elsif name == 'bunny' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M16,32.94a42.65,42.65,0,0,1-5.62,7.46c-.88.87-.59,1.49-.3,1.81a1.45,1.45,0,0,1,.37.9,3.39,3.39,0,0,1-.05,1.21c-.19.56,1.12,1.87,2.8,3.74a23.54,23.54,0,0,0,4.48,3.46,1.59,1.59,0,0,1,.74,1.35v9.91a1.6,1.6,0,0,0,.19.74l1.49,2.83c1.45,2.72,7.43,8.33,9.09,9.87a1.55,1.55,0,0,1,.5,1l1.23,10.4a1.59,1.59,0,0,1-1.44,1.76c-1.3.11-3,.46-3.78,1.44-.88,1.13-.15,2.48.42,3.23a1.6,1.6,0,0,0,1.2.63c1.85.08,6.91.19,8.28-.88C37.24,92.48,40.22,81.65,40,83s9.51,6.34,9.51,6.34c-.74-1.49-8.86,0-8.86,0-1.35.12-3.13,2.45-2.85,3.81a1.62,1.62,0,0,0,1.53,1.27c4.69.09,21.72.4,26.05,0,5-.5,8.21-3.23,8.4-2.48s7.84,3.54,11.57,2,4.67-4.48,4.85-7.28-5-3.92-5-4.1a46.46,46.46,0,0,0,2.06-11.2c.56-7.28-2.62-12.7-4.86-16.43S70.08,43.58,62.62,43c-6-.45-15.49,1.86-19.23,1.86-2.58,0-2.84-3.12-2.75-5.05a1.58,1.58,0,0,1,1.54-1.51C54.47,37.93,60.42,34,63,31.37a1.58,1.58,0,0,0-.94-2.69c-4.73-.57-14.07-1.62-16.28-1.34s-6.28,2.3-8.23,3.32a1.59,1.59,0,0,1-1.94-.36h0a1.59,1.59,0,0,1,0-2.12c2.19-2.37,7.32-8.51,7.9-14.66a18.35,18.35,0,0,0-.36-7.11,1.58,1.58,0,0,0-2.5-.77c-3,2.4-10.13,8.26-11.88,11.62a38,38,0,0,0-3,9.93,1.6,1.6,0,0,1-1,1.21C22.54,29.24,16.56,31.58,16,32.94Z"/></g></svg>

  {% elsif name == 'calculator' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M52,52V90H90V52ZM80,78.5H62v-5H80Zm0-10H62v-5H80Z"/><path d="M10,52V90H48V52ZM38.55,77,35,80.55l-6-6-6,6L19.45,77l6-6-6-6L23,61.45l6,6,6-6L38.55,65l-6,6Z"/><path d="M52,10V48H90V10ZM80,31.5H62v-5H80Z"/><path d="M10,10V48H48V10ZM38,31.5H31.5V38h-5V31.5H20v-5h6.5V20h5v6.5H38Z"/></g></svg>

  {% elsif name == 'calendar' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M7.5,90.21A4.75,4.75,0,0,0,12.24,95H87.76a4.75,4.75,0,0,0,4.74-4.74V30.35H7.5Z"/><path d="M87.76,16h-11V5.05h-8V16H31.2V5.05h-8V16h-11A4.75,4.75,0,0,0,7.5,20.69v4.66h85V20.69A4.75,4.75,0,0,0,87.76,16Z"/></g></svg>

  {% elsif name == 'cart' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="45.55" cy="88.68" r="6.49"/><circle cx="74.82" cy="88.68" r="6.49"/><path d="M96.79,22.81a8.45,8.45,0,0,0-8.25-10.3H27.1L25.54,4.84H3v8H19L32,76.75H91.12v-8H38.51l-1.36-6.69H88Z"/></g></svg>

  {% elsif name == 'chat' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M85.78,29.3H69.3V43a16,16,0,0,1-16,16H36.4l-.7.58v.88a11,11,0,0,0,11,11h18.7L85.28,88.2V71.53h.5a11,11,0,0,0,11-11V40.34A11,11,0,0,0,85.78,29.3Z"/><path d="M64.3,43V22.84a11,11,0,0,0-11-11h-39a11,11,0,0,0-11,11V43a11,11,0,0,0,11,11h.5V70.7L34.57,54H53.28A11,11,0,0,0,64.3,43Zm-50-18.14h40v5h-40Zm0,17.23v-5h40v5Z"/></g></svg>

  {% elsif name == 'checkmark' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="92.17 15.03 30.71 76.49 7.83 53.6 2.17 59.26 30.71 87.8 97.83 20.69 92.17 15.03"/></g></svg>

  {% elsif name == 'circle' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="50" r="45"/></g></svg>

  {% elsif name == 'clock' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,5A45,45,0,1,0,95,50,45,45,0,0,0,50,5ZM73.75,54H46V15h8V46H73.75Z"/></g></svg>

  {% elsif name == 'cloud' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95,59A16.62,16.62,0,0,1,78.38,75.62H21.62a16.62,16.62,0,0,1,0-33.24,16.12,16.12,0,0,1,3.64.41,24.92,24.92,0,0,1,48.2.34A16.61,16.61,0,0,1,95,59Z"/></g></svg>

  {% elsif name == 'clover' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M75.2,41.43c-2.64.7-5.55,2-8.55,3.16-2.78,1-6.2,3.25-8.15,1.75,1.25-2.75,4.69-3.15,7-4.13,2.69-1.17,5.29-2.51,7.7-3.78C81,34.25,88,20,77.13,14.37c-1.92-1-5.16-.78-7.77-1.52-5.65-1.6-8.17-8-15.19-6.31C47.77,8.05,42.49,14.8,42.54,21.8c0,3,1.42,6.43,2.37,9.8.82,2.95,2.55,6.51,1.22,9.27-4.65-4.4-5.37-12.16-9.39-16.93C33.43,20,26.35,17.36,21,18.76c-9.71,2.56-7,10.39-10.76,15.9-1.36,2-3.69,3.73-4.66,6.1-2,5,1.22,11.07,5,13.53,6,3.94,13.13,2.73,21.7.15,2.15-.64,5.33-2.93,7.79-1C38.42,55.8,35.35,56.15,33,57a90.49,90.49,0,0,0-8.56,3.16C17.64,63.54,12.1,72.49,16,79.93c2.64,5,8.91,4.48,12.23,6.77,4.64,3.23,6.61,7.74,13.51,6,10.4-2.63,11.19-14.12,9.7-23-.29-1.71-1.34-4.54-.63-6.25,1.91,11.67,10.4,29.23,32.82,30.31h.19a4,4,0,0,0,.19-8C64.87,84.81,60,69,58.78,62.32c.69,1,1.17,2.16,1.73,3.05C64.29,71.44,68,76.06,73.57,77.81c6.6,2.06,14.55-2,15.85-8.06.43-2-.22-4.28.45-6.44.94-3,3.81-4.59,4.68-7.68C97.7,44.47,83.46,39.27,75.2,41.43Z"/></g></svg>

  {% elsif name == 'clubhouse' %}

    <svg width="100" height="78" viewBox="0 0 100 78" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M30.3416 28.8643C29.6763 26.708 29.7502 25.0721 30.5634 24.0311C31.3765 23.0645 32.4854 22.6183 33.8161 22.544C36.1078 22.4696 36.9949 24.1055 38.3256 28.3438C39.1388 31.0207 40.7651 34.894 41.7262 37.0504C42.9829 39.6529 44.7571 43.1477 45.5703 44.3442C46.2357 45.3852 46.8271 45.6826 47.4924 45.6826C48.6752 45.6826 49.5623 45.0134 49.5623 43.8237C49.5623 43.2214 49.0234 42.2577 48.6462 41.5831L48.5274 41.3699C48.3935 41.1155 48.2296 40.8098 48.0455 40.4664C47.3146 39.1032 46.264 37.1437 45.4964 35.4213C44.4614 33.0419 43.2786 30.0676 42.3176 27.3839C41.7262 25.8224 41.0608 23.5917 40.3955 21.361C39.3605 17.9406 38.8431 15.8586 38.8431 14.2903C38.8431 11.9109 40.6173 10.3493 43.2786 10.3493C45.2007 10.3493 46.4574 11.316 47.2706 14.8108C47.936 18.0082 49.1188 23.2199 50.8191 27.3096C51.928 29.9864 53.5543 33.5624 54.5154 35.3469C54.7086 35.687 54.9097 36.0112 55.0929 36.3066C55.4705 36.9153 55.7721 37.4017 55.7721 37.652C55.7721 37.9829 55.3837 38.434 54.8918 39.0053C54.61 39.3327 54.2941 39.6995 53.9979 40.1058C53.4804 40.775 53.2586 41.2212 53.2586 41.7417C53.2586 42.1878 53.5543 42.6339 53.9979 43.2288C54.4414 43.8237 54.885 44.4185 55.4025 44.4185C55.7721 44.4185 55.9939 44.2698 56.2157 43.9724C58.5074 41.0724 61.0209 39.0648 63.9107 37.4222C67.3113 35.4889 70.7925 34.5222 73.5278 33.9274C74.8585 33.63 75.2281 33.3325 75.2281 32.589C75.2281 31.6967 74.5628 31.1762 73.6756 31.1018C73.132 31.041 72.6377 31.0796 71.9105 31.1363C71.7479 31.1489 71.573 31.1626 71.3839 31.1762C70.6447 31.2505 70.275 30.8787 69.8315 29.9121C69.6247 29.473 69.3875 28.987 69.1286 28.4564C67.7216 25.5731 65.6721 21.3733 64.361 16.2236C63.6956 13.6211 63.1042 10.8699 62.7346 7.29394C62.5128 5.65808 62.8085 5.06322 63.6217 4.31965C64.6567 3.42736 66.5048 3.05557 67.9094 3.42736C69.6097 3.8735 70.4968 5.21193 71.3839 10.4237C71.8275 12.8775 72.5667 16.0005 73.4539 18.6841C74.5628 22.1046 75.9673 24.9369 78.1112 28.7291C79.294 30.8111 80.7725 32.9675 82.325 35.0563C82.0293 35.9485 81.5118 36.5434 79.8115 37.9562C78.1112 39.369 76.4109 40.8561 74.8585 43.6074C73.7496 45.615 73.2321 47.8457 73.2321 49.3329C73.2321 50.7457 73.5278 51.0431 74.4149 51.0431C75.9673 51.0431 77.2241 50.7457 77.298 50.0764C77.6676 47.3996 78.1112 45.6894 79.5897 43.6749C80.4768 42.5596 82.0293 41.1468 83.286 40.0314C85.6516 38.0982 86.4648 36.9828 87.278 34.2316C87.6476 32.9675 88.0912 31.7778 88.6826 30.6624C90.0872 28.0599 92.6007 25.0856 96.5255 25.0856C97.7822 25.0856 98.8172 25.4574 99.4825 26.4984C99.8521 27.0933 100 27.9112 100 28.5061C100 29.7806 99.1285 31.5272 98.6623 32.4615L98.5954 32.5957L98.3406 33.1413C96.7061 36.636 94.3077 41.7638 94.3077 47.6227C94.3077 61.0881 89.5764 67.787 85.8734 71.2074C82.251 74.6279 76.552 77.6089 69.8248 77.6089C65.0196 77.6089 60.0598 76.1961 55.9939 73.5193C50.4168 69.8038 46.8912 64.0118 43.3438 58.1842L43.1241 57.8231C40.2409 52.9899 38.2449 49.1909 35.1333 41.8295C33.3793 37.5641 31.7462 33.4001 30.3416 28.8643ZM54.0854 0C51.9415 0 49.3541 1.11536 49.3541 3.94094C49.3541 5.79987 49.8716 8.55109 50.389 10.7751C51.0321 12.3432 51.2867 13.4779 51.8006 15.7682C51.9956 16.6372 52.2279 17.6726 52.5329 18.9611C53.1982 21.7191 54.0114 24.0985 54.8246 26.1805C55.7856 28.7155 56.8945 30.8718 58.447 33.7718C59.1862 35.1845 59.5559 35.1845 61.3301 34.2179C62.7347 33.4743 64.8046 32.582 66.3571 32.0615C63.6957 26.2617 61.4779 21.4217 60.3691 17.332C60.0734 16.1423 59.0384 11.3767 58.7427 9.29467C58.5209 7.21266 58.2991 5.42809 57.7816 3.49479C57.1902 1.11536 56.3771 0 54.0854 0Z" fill="currentColor"/>
      <path d="M22.8751 53.7469C21.3092 54.7203 13.0094 59.0939 11.1343 59.7901C9.79694 60.2904 8.34531 60.1687 7.4918 57.627C6.44339 54.4837 7.91519 53.7402 10.012 52.9222C11.8669 52.1989 19.5619 49.4004 21.3495 48.9137C22.6063 48.5757 23.48 49.0016 24.0848 50.5225C24.7501 52.1922 24.2931 52.8614 22.8751 53.7469Z" fill="currentColor"/>
      <path d="M16.0336 36.0296C13.8024 36.0904 4.53476 35.6916 2.41779 35.4821C0.576361 35.2995 -0.169619 34.4816 0.031997 31.798C0.247054 28.9183 1.36266 28.7426 3.17049 28.9183C5.2673 29.1211 14.9785 30.7097 16.5242 31.1896C18.1237 31.6898 18.3455 32.4199 18.1506 33.8259C17.9019 35.6511 17.082 35.9958 16.0336 36.0296Z" fill="currentColor"/>
      <path d="M17.1828 17.1564C14.629 16.3452 7.04824 11.7283 5.0724 10.5386C3.28474 9.457 3.05624 8.48359 4.29954 6.11091C5.40171 4.01539 6.62485 3.65036 8.43939 4.59673C10.5429 5.69857 17.9691 11.512 19.3872 12.6611C20.7581 13.7697 20.57 14.3443 19.8509 15.7706C19.1318 17.1969 18.4597 17.562 17.1828 17.1564Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'discord' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_47_83)"><path d="M84.6542 18.7921C78.2806 15.8798 71.4458 13.7341 64.2996 12.5052C64.1694 12.4814 64.0394 12.5407 63.9724 12.6593C63.0934 14.2162 62.1197 16.2473 61.4379 17.8437C53.7517 16.6978 46.1049 16.6978 38.5763 17.8437C37.8944 16.2118 36.8854 14.2162 36.0024 12.6593C35.9354 12.5447 35.8054 12.4854 35.6752 12.5052C28.533 13.7302 21.6982 15.8758 15.3206 18.7921C15.2654 18.8158 15.218 18.8554 15.1866 18.9067C2.22246 38.1944 -1.32896 57.008 0.413251 75.5884C0.421134 75.6792 0.472374 75.7662 0.543325 75.8215C9.0967 82.0768 17.3821 85.8743 25.5137 88.3914C25.6438 88.431 25.7817 88.3836 25.8645 88.2768C27.788 85.661 29.5027 82.9028 30.9728 80.0022C31.0596 79.8323 30.9768 79.6308 30.7994 79.5636C28.0797 78.5362 25.49 77.2835 22.9989 75.861C22.8018 75.7464 22.7861 75.4658 22.9673 75.3314C23.4916 74.9402 24.0159 74.5332 24.5165 74.1222C24.607 74.0472 24.7332 74.0313 24.8397 74.0787C41.2054 81.5197 58.9231 81.5197 75.0956 74.0787C75.2021 74.0274 75.3283 74.0433 75.4228 74.1183C75.9235 74.5293 76.4477 74.9402 76.9759 75.3314C77.1572 75.4658 77.1454 75.7464 76.9483 75.861C74.4572 77.3112 71.8675 78.5362 69.1438 79.5597C68.9665 79.6269 68.8876 79.8323 68.9744 80.0022C70.4761 82.8987 72.1907 85.6569 74.0787 88.2729C74.1576 88.3836 74.2994 88.431 74.4296 88.3914C82.6006 85.8743 90.8859 82.0768 99.4393 75.8215C99.5142 75.7662 99.5615 75.6832 99.5694 75.5923C101.655 54.1114 96.077 35.452 84.7842 18.9106C84.7566 18.8554 84.7094 18.8158 84.6542 18.7921ZM33.4168 64.2749C28.4896 64.2749 24.4297 59.7701 24.4297 54.2379C24.4297 48.7056 28.4108 44.2009 33.4168 44.2009C38.462 44.2009 42.4825 48.7452 42.4037 54.2379C42.4037 59.7701 38.4225 64.2749 33.4168 64.2749ZM66.6448 64.2749C61.7177 64.2749 57.6579 59.7701 57.6579 54.2379C57.6579 48.7056 61.6389 44.2009 66.6448 44.2009C71.6901 44.2009 75.7106 48.7452 75.6318 54.2379C75.6318 59.7701 71.6901 64.2749 66.6448 64.2749Z" fill="currentColor"/></g><defs><clipPath><rect width="100" height="100" fill="currentColor"/></clipPath></defs></svg>


  {% elsif name == 'document' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="59.3 34.06 59.3 7.5 14.14 7.5 14.14 92.5 85.86 92.5 85.86 34.06 59.3 34.06"/><polygon points="64.3 10.52 64.3 29.06 82.42 29.06 64.3 10.52"/></g></svg>

  {% elsif name == 'document-2' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M59.3,34.06V7.5H14.14v85H85.86V34.06ZM22.44,57.58H70.18v5H22.44Zm0,10.82H62.8v5H22.44ZM71.66,84.22H22.44v-5H71.66Zm4.42-32.46H22.44v-5H76.08Z"/><polygon points="64.3 10.52 64.3 29.06 82.42 29.06 64.3 10.52"/></g></svg>

  {% elsif name == 'dollar' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M72.92,63.05a17.11,17.11,0,0,0-8.68-12.74A30.06,30.06,0,0,0,54,47V23.68a16.9,16.9,0,0,1,10.12,6.9L71,26.49a25,25,0,0,0-17-11V7.07H46v8.41c-.7.09-1.42.21-2.15.36s-14.74,2.9-16.76,16.6A16.93,16.93,0,0,0,31.45,46.3,24.57,24.57,0,0,0,46,54.44v22c-2.62-.65-6.55-2.34-10.9-6.69l-5.65,5.66C35.89,81.82,42.06,83.9,46,84.53v8.4h8V84.15C59.91,83,64.66,80.7,68.13,77.27A16.66,16.66,0,0,0,72.92,63.05ZM37.37,40.93A9,9,0,0,1,35,33.6c1.17-8,10-9.83,10.38-9.91l.61-.11V46.29A17.25,17.25,0,0,1,37.37,40.93ZM62.5,71.59A18.09,18.09,0,0,1,54,76V55.12a21.13,21.13,0,0,1,6.34,2.17A9.21,9.21,0,0,1,65,64.13,8.74,8.74,0,0,1,62.5,71.59Z"/></g></svg>

  {% elsif name == 'down-arrow' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="77.2 51.82 54 78.32 54 4.96 46 4.96 46 78.32 22.8 51.82 16.78 57.09 50 95.04 83.22 57.09 77.2 51.82"/></g></svg>

  {% elsif name == 'down-caret' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="50 77.1 2.15 28.51 7.85 22.9 50 65.7 92.15 22.9 97.85 28.51 50 77.1"/></g></svg>

  {% elsif name == 'dribbble' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1.10429 39.5005C4.41773 23.9959 14.9193 11.1548 28.9529 4.63308C31.4706 8.11039 39.5974 19.7203 47.2499 33.2808C25.4837 39.0806 6.04017 39.4756 1.10429 39.5005Z" fill="currentColor"/>
      <path d="M12.6214 83.2092C4.7696 74.3782 0 62.7461 0 50C0 49.4854 0.00777525 48.9726 0.0232147 48.4617C5.37526 48.5075 28.0864 48.1894 51.3949 41.1868C52.8845 44.0381 54.2445 46.9542 55.5398 49.8704C55.2152 49.9679 54.8744 50.0653 54.5336 50.1627C54.1947 50.2596 53.8549 50.3567 53.5321 50.4536C29.3769 58.2522 15.606 78.4647 12.6214 83.2092Z" fill="currentColor"/>
      <path d="M69.8066 95.9251C63.7337 98.5471 57.0378 100 50.0025 100C38.3816 100 27.6867 96.036 19.1962 89.3864C21.5585 85.0356 32.4715 67.5486 58.7781 58.3596C58.8428 58.3273 58.8913 58.3111 58.9398 58.2949C58.9885 58.2786 59.0371 58.2624 59.102 58.2299C65.8312 75.6392 68.8151 90.4365 69.8066 95.9251Z" fill="currentColor"/>
      <path d="M99.3963 57.8245C97.2208 71.6668 89.3655 83.6236 78.2907 91.235C77.4686 86.6369 74.6734 72.7103 68.5577 55.8971C82.936 53.6255 95.6922 56.7461 99.3963 57.8245Z" fill="currentColor"/>
      <path d="M88.8498 18.5167C95.6863 26.941 99.8374 37.6326 100 49.2884C95.8057 48.5015 80.7116 45.9462 65.5137 47.8614C65.2869 47.3698 65.0822 46.856 64.8737 46.333C64.725 45.96 64.5745 45.5823 64.4128 45.2045C63.3765 42.7421 62.2106 40.2147 61.0449 37.817C77.7845 31.0018 86.3956 21.5098 88.8498 18.5167Z" fill="currentColor"/>
      <path d="M83.2353 12.6403C80.9809 15.4995 73.0982 24.2301 57.0943 30.2351C49.5904 16.4409 41.3552 4.89466 38.7233 1.2773C42.3482 0.441613 46.1238 0 50.0025 0C62.7598 0 74.4012 4.77727 83.2353 12.6403Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'droplet' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,5.75s-18.7,48.4-21.9,57.4A22.41,22.41,0,0,0,26.8,71a23.2,23.2,0,1,0,46.4.1,25,25,0,0,0-1.3-7.8C68.7,54.15,50,5.75,50,5.75Z"/></g></svg>

  {% elsif name == 'email' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M52.79,62.87a3,3,0,0,1-2.22.88,3,3,0,0,1-2.09-.89L8,22.38V81.86H92V22.53Z"/><polygon points="87.89 18.14 12.24 18.14 50.6 56.51 87.89 18.14"/></g></svg>

  {% elsif name == 'euro' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M77.59,66.25A27,27,0,0,1,31.38,61H58.83V53H29.2a25.09,25.09,0,0,1,0-6H58.83V39H31.38a27,27,0,0,1,45.31-6.37l6.12-5.16A35,35,0,0,0,22.81,39H16v8h5.14c-.08,1-.14,2-.14,3s.06,2,.14,3H16v8h6.79A35,35,0,0,0,84,71.07Z"/></g></svg>

  {% elsif name == 'eye' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M5,49.81v.38s43.85,58.85,90,0v-.38C48.85-9,5,49.81,5,49.81ZM50,63.7A13.7,13.7,0,1,1,63.7,50,13.7,13.7,0,0,1,50,63.7Z"/><path d="M50,41.29V58.71a8.71,8.71,0,0,0,0-17.42Z"/></g></svg>

  {% elsif name == 'facebook' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M57,27.73V38H72.83l-2.09,16H57V95H40.45V53.94H26.62V38H40.45V26.15C40.45,12.46,48.83,5,61,5h0a115.36,115.36,0,0,1,12.34.63V19.94H64.92C58.26,19.94,57,23.1,57,27.73Z"/></g></svg>

  {% elsif name == 'film' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95,33.15V66.88a2.89,2.89,0,0,1-2.9,2.91,2.76,2.76,0,0,1-1-.18L74,63.46V76.7a2.91,2.91,0,0,1-2.9,2.9H7.9A2.92,2.92,0,0,1,5,76.7V23.3a2.92,2.92,0,0,1,2.9-2.9H71.05A2.91,2.91,0,0,1,74,23.3V36.57l17.16-6.15A2.89,2.89,0,0,1,95,33.15Z"/></g></svg>

  {% elsif name == 'fire' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M73.58,43.35a30.63,30.63,0,0,0-3.09,8.87S71,47.47,67.91,39,62,27.68,60.38,21.49C58.11,13,63,5,63,5s-19.28,3.71-28,20.52c-7.84,14.94-4.64,23.91-4.64,23.91a22.54,22.54,0,0,1-5-7.32A20.4,20.4,0,0,1,24,34S10.28,49.12,16.88,68.2C21.52,81.6,30.18,90.46,40.59,94A16.83,16.83,0,0,1,32,80c-.31-11.24,7.42-16.7,12.06-24.13,6.6-10.72,4.75-17.52,4.75-17.52s5.46,3.09,8.66,14.54a30.56,30.56,0,0,1,.92,10,47.27,47.27,0,0,1-4,15.36S60.49,77,62.13,65.82c2.79,2.89,5.37,7.12,5.67,11.45A17.89,17.89,0,0,1,57.08,95c11.86-2.78,20.21-12.78,23.2-20.1C84,65.72,83,57.47,82.34,50.46a36.09,36.09,0,0,1,2.58-16.9S78.42,35.41,73.58,43.35Z"/></g></svg>

  {% elsif name == 'flag' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="75.47 34.15 90.23 8.28 17.77 8.28 17.77 5 9.77 5 9.77 95 17.77 95 17.77 60 90.23 60 75.47 34.15"/></g></svg>

  {% elsif name == 'flickr' %}
    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M23.7974 26C19.0924 25.9978 14.4925 27.391 10.5794 30.0033C6.66632 32.6156 3.61585 36.3298 1.81382 40.6759C0.0117927 45.0221 -0.460839 49.8051 0.455703 54.4199C1.37225 59.0347 3.63679 63.2741 6.96291 66.6017C10.289 69.9294 14.5273 72.1959 19.1417 73.1146C23.7561 74.0333 28.5393 73.5629 32.8863 71.7629C37.2333 69.9629 40.9489 66.9142 43.563 63.0023C46.1772 59.0905 47.5725 54.4912 47.5725 49.7862C47.5666 43.4815 45.0602 37.4364 40.6031 32.9772C36.146 28.5181 30.1021 26.0088 23.7974 26V26Z" fill="currentColor"/>
      <path d="M76.2137 26C71.5093 26 66.9104 27.395 62.9988 30.0087C59.0872 32.6224 56.0384 36.3373 54.2381 40.6836C52.4378 45.03 51.9668 49.8126 52.8845 54.4267C53.8023 59.0408 56.0678 63.2791 59.3943 66.6057C62.7209 69.9322 66.9592 72.1977 71.5733 73.1155C76.1874 74.0333 80.97 73.5622 85.3163 71.7619C89.6627 69.9616 93.3776 66.9128 95.9913 63.0012C98.6049 59.0896 100 54.4907 100 49.7863C99.9941 43.4796 97.4862 37.4328 93.0267 32.9733C88.5671 28.5138 82.5204 26.0059 76.2137 26V26Z" fill="currentColor"/>
    </svg>
  {% elsif name == 'flower' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M83.59,78.34a17,17,0,0,1-32.23,7.35,21.93,21.93,0,0,0,.75-14.85,19.28,19.28,0,0,0,14.37-9.16A21.76,21.76,0,0,0,78.19,66,16.92,16.92,0,0,1,83.59,78.34ZM47.72,73.81h0a16.89,16.89,0,0,0-1-3.12l-.65-.11a19.38,19.38,0,0,1-13-9.69,21.79,21.79,0,0,1-12.61,4l-.92,0A16.94,16.94,0,1,0,45.89,85.51h0a16.53,16.53,0,0,0,1.58-3.83,17.19,17.19,0,0,0,.59-4.45A17.37,17.37,0,0,0,47.72,73.81Zm-17-22.12a19.23,19.23,0,0,1,5.21-13.17,22.06,22.06,0,0,1-6.87-10.18A16.79,16.79,0,0,0,20.45,26a17,17,0,0,0-2.54,33.71h0a16.29,16.29,0,0,0,2.53.19,16.69,16.69,0,0,0,2.13-.14h0A16.66,16.66,0,0,0,28.23,58a16.9,16.9,0,0,0,3-2c-.06-.24-.11-.49-.15-.74A18.41,18.41,0,0,1,30.72,51.69ZM50,4.71A17,17,0,0,0,33,21.66a17.24,17.24,0,0,0,.37,3.55A16.93,16.93,0,0,0,36,31.29c.13.18.25.36.38.53a17.29,17.29,0,0,0,3.48,3.46c.3-.19.61-.37.92-.54A19.28,19.28,0,0,1,59.58,35a21.82,21.82,0,0,1,6.58-8.27h0A17,17,0,0,0,50,4.71ZM79.55,27.1a16.73,16.73,0,0,0-9.21,2.74,16.93,16.93,0,0,0-6.66,8.27l.36.38a19.22,19.22,0,0,1,5.24,13.2,19,19,0,0,1-.61,4.8,3.17,3.17,0,0,1-.12.44,16.85,16.85,0,0,0,11,4.07h.5a16.95,16.95,0,0,0-.5-33.89ZM64.27,51.39a14,14,0,0,0-1.64-6.34,13.86,13.86,0,0,0-2.39-3.29,5.1,5.1,0,0,0-.47-.46A13.5,13.5,0,0,0,58,39.87l-.45-.29a14.19,14.19,0,0,0-15.12,0l-.26.17a14.06,14.06,0,0,0-2,1.55l-.36.35a14.14,14.14,0,0,0-2.63,3.67,14.28,14.28,0,0,0-1.5,6.36A14.45,14.45,0,0,0,36,54.58c.1.45.21.89.34,1.33a16,16,0,0,0,.57,1.5,13.45,13.45,0,0,0,.78,1.51,14.25,14.25,0,0,0,9,6.64,13.23,13.23,0,0,0,1.9.33c.45,0,.91.07,1.38.07h0a13.94,13.94,0,0,0,1.72-.1A14.29,14.29,0,0,0,62,59.46h0a14.36,14.36,0,0,0,.76-1.31,13.59,13.59,0,0,0,.67-1.51c.14-.39.27-.79.38-1.2a14,14,0,0,0,.5-3.74C64.28,51.59,64.28,51.49,64.27,51.39Z"/></g></svg>

  {% elsif name == 'folder' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M74.62,36.9H33.82a10.2,10.2,0,0,1-9.77-7.37l-.5-1.78A2.51,2.51,0,0,0,21.69,26a.64.64,0,0,1-.2,0l-.38,0H6a2.52,2.52,0,0,0-1.95.91,2.6,2.6,0,0,0-.55,2.09l8.88,48.22a3.93,3.93,0,0,0,3.86,3.21H83.12l-6.77-42A1.74,1.74,0,0,0,74.62,36.9Z"/><path d="M25.94,22.43l.29.24.13.12a4.36,4.36,0,0,1,.43.43l.18.2a3.7,3.7,0,0,1,.37.49,3.92,3.92,0,0,1,.38.55c.09.15.18.31.26.47l.21.46c0,.09.1.21.14.33a5.27,5.27,0,0,1,.22.67L29,28.12a5,5,0,0,0,4.78,3.6h40.8a6.9,6.9,0,0,1,6.85,5.83l5,31.35,9.93-46.31a2.42,2.42,0,0,0-2.39-2.94H27.6a2.45,2.45,0,0,0-2.41,2l0,.23.14.09Z"/></g></svg>

  {% elsif name == 'gear' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><style>.cls-1{fill-rule:evenodd;}</style></defs><g><path class="cls-1" d="M94.37,44.11h0l-7.43-1.85-3-.74-1-2.89a33,33,0,0,0-1.55-3.76l-1.32-2.74,1.56-2.6L86,22.36a1.56,1.56,0,0,0-.12-1l-7.19-7.17A1.24,1.24,0,0,0,78,14a1,1,0,0,0-.33,0l-7.16,4.3-2.62,1.56-2.73-1.32A30.94,30.94,0,0,0,61.37,17l-2.88-1-.74-3L55.9,5.63A1.54,1.54,0,0,0,55.06,5H44.94a1.38,1.38,0,0,0-.84.65l-1.85,7.41-.74,3-2.88,1a30.94,30.94,0,0,0-3.76,1.55l-2.73,1.32-2.62-1.56L22.35,14A1.41,1.41,0,0,0,22,14a1.52,1.52,0,0,0-.72.16l-7.17,7.17a1.46,1.46,0,0,0-.12,1l4.3,7.16,1.56,2.6-1.32,2.74A33,33,0,0,0,17,38.62l-1,2.89-3,.74L5.63,44.11a1.48,1.48,0,0,0-.63.83V55.06a1.44,1.44,0,0,0,.63.84l7.43,1.85,3,.74,1,2.88a31.75,31.75,0,0,0,1.55,3.77l1.32,2.72-1.56,2.62L14,77.64a1.56,1.56,0,0,0,.12,1l7.19,7.17A1.24,1.24,0,0,0,22,86a1,1,0,0,0,.33,0l7.16-4.3,2.62-1.56,2.73,1.32A30.94,30.94,0,0,0,38.63,83l2.88,1,.74,3,1.85,7.43a1.54,1.54,0,0,0,.84.63H55.06a1.38,1.38,0,0,0,.84-.63l1.85-7.43.74-3,2.88-1a30.94,30.94,0,0,0,3.76-1.55l2.73-1.32,2.62,1.56L77.64,86A1.7,1.7,0,0,0,78,86a1.52,1.52,0,0,0,.72-.16l7.17-7.17a1.45,1.45,0,0,0,.12-1l-4.3-7.16-1.56-2.62,1.32-2.72A31.75,31.75,0,0,0,83,61.37l1-2.88,3-.74,7.43-1.85a1.54,1.54,0,0,0,.63-.84V44.94A1.34,1.34,0,0,0,94.37,44.11ZM50,66.88A16.88,16.88,0,1,1,66.88,50,16.88,16.88,0,0,1,50,66.88Z"/></g></svg>

  {% elsif name == 'gem' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="2.13 36.24 42.69 84.73 29.23 36.24 2.13 36.24"/><polygon points="36.15 36.24 49.95 85.97 63.77 36.24 36.15 36.24"/><polygon points="57.41 84.03 97.78 36.24 70.69 36.24 57.41 84.03"/><polygon points="97.88 29.59 86.44 15.71 75.25 29.59 97.88 29.59"/><polygon points="79.23 14.03 56.84 14.03 67.92 28.05 79.23 14.03"/><polygon points="60.63 29.59 49.88 15.96 38.41 29.59 60.63 29.59"/><polygon points="42.8 14.03 20.68 14.03 31.5 27.45 42.8 14.03"/><polygon points="13.64 15.94 2.13 29.59 24.66 29.59 13.64 15.94"/></g></svg>

  {% elsif name == 'gift' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M90.36,25.75A14.11,14.11,0,0,0,71,6c-3.13,1.74-15.09,8.66-21.38,16.12C43.35,14.66,31.39,7.74,28.26,6A14.11,14.11,0,0,0,8.91,25.75H7v25H47.26v-18h6v18H93.49v-25ZM75,12.93l.27-.17A6.12,6.12,0,1,1,82,23l-4.21,2.78H57.09C61.52,21.32,69.27,16.11,75,12.93Zm-59.5,1.56A6.13,6.13,0,0,1,24,12.76l.27.17C30,16.11,37.75,21.32,42.18,25.75H21.45L17.24,23A6.13,6.13,0,0,1,15.51,14.49Z"/><rect x="53.26" y="56.78" width="35.97" height="39.47"/><rect x="11.29" y="56.78" width="35.97" height="39.47"/></g></svg>

  {% elsif name == 'globe' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M37.49,96.61A48.15,48.15,0,0,1,13,80.89H27.59A42.55,42.55,0,0,0,37.49,96.61Z"/><path d="M65.42,80.89c-2.68,6.26-6.7,12.09-12.7,15.49V80.89Z"/><path d="M87,80.89A48.31,48.31,0,0,1,60.94,97,42.3,42.3,0,0,0,71.27,80.89Z"/><path d="M98.14,52.74A47.84,47.84,0,0,1,91,75.46H73.09a86,86,0,0,0,3.15-21v-.18c0-.53,0-1.06,0-1.58Z"/><path d="M70.85,52.74c0,.49,0,1,0,1.48v.1A81.24,81.24,0,0,1,67.4,75.46H52.72V52.74Z"/><path d="M25.77,75.46H9A47.84,47.84,0,0,1,1.86,52.74H22.57c0,.52,0,1,.05,1.58A85.58,85.58,0,0,0,25.77,75.46Z"/><path d="M25.73,24.58a88.43,88.43,0,0,0-3.16,22.73H1.86a47.75,47.75,0,0,1,7.2-22.73Z"/><path d="M70.85,47.31H52.72V24.58H67.47A81.42,81.42,0,0,1,70.85,47.31Z"/><path d="M98.14,47.31H76.28a88.87,88.87,0,0,0-3.16-22.73H90.94A47.75,47.75,0,0,1,98.14,47.31Z"/><path d="M87,19.15H71.3A43,43,0,0,0,60.68,3,48,48,0,0,1,87,19.15Z"/><path d="M65.42,19.15H52.72V3.72C56.37,5.92,61.53,10.39,65.42,19.15Z"/><path d="M37.79,3.38A42.85,42.85,0,0,0,27.56,19.15H13A48,48,0,0,1,37.79,3.38Z"/><path d="M47.28,80.89V97c-6.63-3.28-11-9.44-13.83-16.08Z"/><path d="M47.28,52.74V75.46H31.46a81.5,81.5,0,0,1-3.41-21.24c0-.5,0-1,0-1.48Z"/><path d="M47.28,24.58V47.31H28a81,81,0,0,1,3.37-22.73Z"/><path d="M47.28,3.08V19.15H33.44C37.76,9.43,43.64,5,47.28,3.08Z"/></g></svg>

  {% elsif name == 'grid' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="5" y="5" width="21.72" height="21.72"/><rect x="39.14" y="5" width="21.72" height="21.72"/><rect x="73.28" y="5" width="21.72" height="21.72"/><rect x="5" y="39.14" width="21.72" height="21.72"/><rect x="39.14" y="39.14" width="21.72" height="21.72"/><rect x="73.28" y="39.14" width="21.72" height="21.72"/><rect x="5" y="73.28" width="21.72" height="21.72"/><rect x="39.14" y="73.28" width="21.72" height="21.72"/><rect x="73.28" y="73.28" width="21.72" height="21.72"/></g></svg>

  {% elsif name == 'hanger' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95.92,70,54,41.91V39.34a11.46,11.46,0,0,0,8.16-10.71c0-6.26-5.46-11.36-12.16-11.36s-12.16,5.1-12.16,11.36h8c0-1.85,1.86-3.36,4.16-3.36s4.16,1.51,4.16,3.36S52.3,32,50,32v0h0l-4,4h0v5.91L4.09,70a6.93,6.93,0,0,0,3.82,12.7H92.07A6.93,6.93,0,0,0,95.92,70ZM11.46,74.73,50,48.86,88.55,74.73Z"/></g></svg>

  {% elsif name == 'heart' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M87.62,17.7a22.92,22.92,0,0,0-31.69,0L50,23.45,44.06,17.7a22.91,22.91,0,0,0-31.68,0,23.87,23.87,0,0,0,0,34.52L50,88.66,87.62,52.22A23.87,23.87,0,0,0,87.62,17.7Z"/></g></svg>

  {% elsif name == 'house' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="97.44 45.2 87.46 37.3 87.46 37.28 85.07 35.41 50.02 7.66 13.99 34.69 12.2 36.01 12.21 36.03 2.56 43.26 7.36 49.66 12.46 45.84 12.46 92.34 40.84 92.34 40.84 88.34 40.84 57.97 59.08 57.97 59.08 88.34 59.08 92.34 87.46 92.34 87.46 47.5 92.48 51.48 97.44 45.2"/></g></svg>

  {% elsif name == 'houzz' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="9.04 95 9.04 5 29.81 5 29.81 25.77 90.96 43.08 90.96 95 62.12 95 62.12 65 39.04 65 39.04 95 9.04 95"/></g></svg>

  {% elsif name == 'info' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,7A43,43,0,1,0,93,50,43,43,0,0,0,50,7Zm6.93,68.29a6.68,6.68,0,0,1-6.88,6.45,7.1,7.1,0,0,1-4.86-1.89,6.25,6.25,0,0,1-2-4.56V45.72a6.67,6.67,0,0,1,6.87-6.44,7.14,7.14,0,0,1,4.87,1.88,6.25,6.25,0,0,1,2,4.56ZM50,33.05a7.4,7.4,0,1,1,7.39-7.4A7.39,7.39,0,0,1,50,33.05Z"/></g></svg>

  {% elsif name == 'instagram' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M53.9761 33.8241C55.7845 34.2571 59.0099 35.8707 60.4962 37.0857C62.332 38.5864 63.8205 40.4962 64.9097 42.7477C66.2306 45.4785 66.568 46.955 66.568 50.0036C66.568 53.0523 66.2306 54.5288 64.9097 57.2596C63.2107 60.7718 60.7831 63.1985 57.2564 64.9093C54.5292 66.2323 53.0521 66.5704 50 66.5704C46.9479 66.5704 45.4708 66.2323 42.7435 64.9093C39.2169 63.1985 36.7893 60.7718 35.0902 57.2596C34.5614 56.1662 33.972 54.6454 33.7803 53.8801C33.3048 51.9804 33.3048 48.0268 33.7803 46.1272C34.2622 44.2025 35.8559 41.0067 37.0811 39.5082C39.6545 36.3607 43.7087 34.0563 47.6397 33.5068C49.2781 33.2776 52.3336 33.4307 53.9761 33.8241Z" fill="currentColor"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M32.0079 0.0317186C28.7384 0.15994 24.7849 0.545001 22.7694 0.931454C11.094 3.17026 3.15765 11.1065 0.925248 22.775C0.0737574 27.2264 0 29.3946 0 50.0036C0 70.6127 0.0737574 72.7809 0.925248 77.2323C3.15765 88.9008 11.1 96.8426 22.7694 99.0748C27.2211 99.9263 29.3895 100 50 100C70.6105 100 72.7789 99.9263 77.2306 99.0748C88.9 96.8426 96.8423 88.9008 99.0748 77.2323C99.9262 72.7809 100 70.6127 100 50.0036C100 29.3946 99.9262 27.2264 99.0748 22.775C96.8342 11.0635 88.84 3.10466 77.1225 0.919726C72.8445 0.122169 71.1712 0.0615375 52.0875 0.0146224C41.9185 -0.0102267 32.8827 -0.00267253 32.0079 0.0317186ZM70.9873 21.4496C71.4642 19.9162 73.2636 18.0475 74.7515 17.5404C75.936 17.1368 77.9368 17.2092 79.096 17.6976C80.1553 18.1441 81.6935 19.6343 82.1943 20.6992C82.8185 22.0275 82.9304 23.6723 82.4921 25.0814C81.9779 26.7347 80.209 28.5038 78.5561 29.0179C73.9113 30.4623 69.5428 26.094 70.9873 21.4496ZM31.8117 31.8167C36.0587 27.5699 41.537 24.982 47.6143 24.3512C50.6348 24.0377 54.6424 24.4818 57.8042 25.4806C65.6897 27.9714 72.0338 34.3151 74.5249 42.2C76.129 47.2778 76.129 52.7295 74.5249 57.8072C72.0338 65.6922 65.6897 72.0358 57.8042 74.5267C52.7261 76.1308 47.274 76.1308 42.1958 74.5267C34.3104 72.0358 27.9662 65.6922 25.4752 57.8072C22.5672 48.6023 24.9986 38.6293 31.8117 31.8167Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'kickstarter' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M75.8628 50.0203L83.8188 42.1173C92.0604 33.9307 92.0604 20.5969 83.8188 12.4103C75.5771 4.22359 62.1538 4.22359 53.9123 12.4103L51.0153 15.2878C47.1802 9.81647 40.8562 6.25 33.6346 6.25C21.9657 6.25 12.5 15.6525 12.5 27.2435V72.7565C12.5 84.3475 21.9657 93.75 33.6346 93.75C40.8562 93.75 47.1802 90.1836 51.0153 84.7123L53.9123 87.5898C62.1538 95.7765 75.5771 95.7765 83.8188 87.5898C92.0604 79.403 92.0604 66.0693 83.8188 57.8827L75.8628 50.0203Z" fill="currentColor"/></svg>

  {% elsif name == 'krona' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="49.57 38.62 44.02 32.86 23.93 52.23 23.93 12.51 15.93 12.51 15.93 84.33 23.93 84.33 23.93 69.78 46.86 87.5 51.75 81.16 26.04 61.3 49.57 38.62"/><path d="M65.57,40.12V35.54h-8V84.33h8V53.93c.58-2.22,3.91-11.86,18.22-11.34l.28-8C75.75,34.28,69.75,36.74,65.57,40.12Z"/></g></svg>

  {% elsif name == 'leaf' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M72.93,59.25c0-.24,0-.48,0-.71v0C73.07,55.22,72.18,40.75,50,5,24.31,46.4,27.17,59.25,27.17,59.25A22.87,22.87,0,0,0,46,81.77V95h8V81.78A22.88,22.88,0,0,0,72.93,59.25Z"/></g></svg>

  {% elsif name == 'leaf-2' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M87.31,94.51C85.18,87.06,80.64,79,75.16,71.3c9.32-11.18,11-26.26,3.2-36.67l0,0L77.86,34c-3.18-4.61-15.3-16.3-60.06-29.22L12.69,3.29V8.61c0,51.1,9.38,64,12.29,66.72,5.08,6.52,13,9.84,21.53,9.84a37.07,37.07,0,0,0,22.12-7.66l.72-.57C74,83.66,77.84,90.48,79.62,96.71Z"/></g></svg>

  {% elsif name == 'left-arrow' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="95.04 46 21.68 46 48.18 22.8 42.91 16.78 4.96 50 42.91 83.22 48.18 77.2 21.68 54 95.04 54 95.04 46"/></g></svg>

  {% elsif name == 'left-caret' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="71.49 97.85 22.9 50 71.49 2.15 77.1 7.85 34.3 50 77.1 92.15 71.49 97.85"/></g></svg>

  {% elsif name == 'lightbulb' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,7A25.7,25.7,0,0,0,30.27,49.16l.09.11c4.08,5.69,5.73,9.13,6.38,11.09H63.26c.64-1.94,2.27-5.34,6.28-10.94a1.3,1.3,0,0,1,.1-.15.48.48,0,0,1,.09-.11.61.61,0,0,1,.12-.15A25.7,25.7,0,0,0,50,7ZM48.36,18.84A17,17,0,0,0,42.91,21a11.41,11.41,0,0,0-5.75,9,2,2,0,0,1-2,1.82H35a2,2,0,0,1-1.81-2.18c.9-9.82,9.89-13.87,14.52-14.64a2,2,0,1,1,.67,3.94Z"/><path d="M62.83,75.12v3.75a2.26,2.26,0,0,1-2.26,2.26H39.43a2.26,2.26,0,0,1-2.26-2.26V75.12Z"/><rect x="37.17" y="64.36" width="25.66" height="6.76"/><path d="M58.86,85.13a8.38,8.38,0,0,1-16.73,0Z"/></g></svg>

  {% elsif name == 'like' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M98,44.86a7.42,7.42,0,0,0-7.41-7.4l-31.11.44a.49.49,0,0,1-.41-.24.47.47,0,0,1,0-.47c1.31-2.84,3-10.52.9-21.27-.78-4.05-3.12-6.66-6-6.66a5,5,0,0,0-4.89,4.09,81.76,81.76,0,0,1-1.6,9.34c-2.1,8.79-12.93,19.81-19.76,23.84a1,1,0,0,0-.49.84V86.64a1,1,0,0,0,.51.86,28.11,28.11,0,0,0,13.81,3.24h.77c2.85,0,7.63-.08,14.62-.08H57l23.29-.48c4.29,0,7.4-2.84,7.4-6.74A7.34,7.34,0,0,0,85.31,78h.15A7.4,7.4,0,0,0,90.1,64.81,7.38,7.38,0,0,0,92.67,52,7.4,7.4,0,0,0,98,44.86Z"/><rect x="2" y="43.66" width="18.06" height="45.58" rx="1.46"/></g></svg>

  {% elsif name == 'link' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M87.83,59.4,79.4,51a20.11,20.11,0,0,0-25.32-2.55l-2.5-2.5A20.11,20.11,0,0,0,49,20.6L40.6,12.17A20.1,20.1,0,0,0,12.17,40.6L20.6,49a20.11,20.11,0,0,0,25.32,2.55l2.5,2.5A20.1,20.1,0,0,0,51,79.39l8.43,8.44A20.1,20.1,0,0,0,87.83,59.4Zm-53-12.48a12.06,12.06,0,0,1-8.56-3.54l-8.43-8.44A12.1,12.1,0,0,1,34.94,17.83l8.44,8.43a12.09,12.09,0,0,1,2.34,13.8L36.8,31.15,31.15,36.8l8.91,8.92A12,12,0,0,1,34.82,46.92ZM82.17,82.17a12.11,12.11,0,0,1-17.11,0l-8.44-8.43a12.09,12.09,0,0,1-2.34-13.8l8.92,8.91,5.65-5.65-8.91-8.92a12.09,12.09,0,0,1,13.8,2.34l8.43,8.44A12.11,12.11,0,0,1,82.17,82.17Z"/></g></svg>

  {% elsif name == 'linkedin' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path data-name="LinkedIn" d="M55.4,43.2c2.56-4,7.14-9.59,17.39-9.59C85.48,33.61,95,41.9,95,59.73V93H75.71V62c0-7.8-2.79-13.13-9.77-13.13-5.33,0-8.5,3.59-9.9,7.06a13.18,13.18,0,0,0-.64,4.7V93H36.1s.26-52.58,0-58H55.4V43.2M15.92,7C9.32,7,5,11.33,5,17s4.19,10,10.66,10h.13c6.73,0,10.92-4.46,10.92-10S22.52,7,15.92,7ZM6.15,93H25.44V35H6.15Z"/></g></svg>

  {% elsif name == 'lock' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M81.73,40.87h-9V31.78a22.78,22.78,0,0,0-45.56,0v9.09H18.27a4.73,4.73,0,0,0-4.73,4.73V86.28A4.73,4.73,0,0,0,18.27,91H81.73a4.73,4.73,0,0,0,4.73-4.72V45.6A4.73,4.73,0,0,0,81.73,40.87ZM35.22,31.78a14.78,14.78,0,0,1,29.56,0v9.09H35.22Z"/></g></svg>

  {% elsif name == 'map' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="9.73 89.88 32.58 77.08 32.58 10.12 9.73 22.92 9.73 89.88"/><polygon points="67.42 22.92 67.42 89.88 90.27 77.08 90.27 10.12 67.42 22.92"/><polygon points="38.58 77.08 61.42 89.88 61.42 22.92 38.58 10.12 38.58 77.08"/></g></svg>

  {% elsif name == 'medium' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M56.406 47.3219C56.406 63.1012 43.7789 75.8929 28.2035 75.8929C12.628 75.8929 0 63.0984 0 47.3219C0 31.5455 12.6271 18.75 28.2035 18.75C43.7799 18.75 56.406 31.5426 56.406 47.3219Z" fill="currentColor"/><path d="M87.3452 47.3219C87.3452 62.1747 81.0316 74.2201 73.2434 74.2201C65.4552 74.2201 59.1417 62.1747 59.1417 47.3219C59.1417 32.4691 65.4542 20.4237 73.2425 20.4237C81.0307 20.4237 87.3442 32.4652 87.3442 47.3219" fill="currentColor"/><path d="M100 47.3219C100 60.6264 97.7799 71.4185 95.0404 71.4185C92.3009 71.4185 90.0817 60.6293 90.0817 47.3219C90.0817 34.0145 92.3019 23.2253 95.0404 23.2253C97.7789 23.2253 100 34.0136 100 47.3219Z" fill="currentColor"/></svg>


  {% elsif name == 'megaphone' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="3.47" y="30.84" width="10" height="32.15"/><path d="M17.47,30.84V63h0l-.13,4.22A9.56,9.56,0,0,0,26,77.43l13.84,1.22a8.38,8.38,0,0,0,.86,0A9.55,9.55,0,0,0,50.23,70c0-.07,0-.15,0-.22l.11-3.42a118.34,118.34,0,0,1,21.23,6.18V21.31A150.37,150.37,0,0,1,17.47,30.84ZM42.25,69.35a1.6,1.6,0,0,1-.54,1,1.54,1.54,0,0,1-1.13.35L26.73,69.47a1.56,1.56,0,0,1-1.41-1.69c0-.07,0-.15,0-.22l.14-4.37A163.19,163.19,0,0,1,42.39,64.9Z"/><rect x="77.86" y="28.62" width="18.01" height="8" transform="translate(-4.74 17.87) rotate(-11.43)"/><rect x="83.25" y="37.52" width="8" height="18.43" transform="translate(39.04 133.17) rotate(-89.02)"/><rect x="82.69" y="52.29" width="8" height="17.83" transform="translate(6.41 130.6) rotate(-76.06)"/></g></svg>

  {% elsif name == 'menu' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="5" y="12" width="90" height="8"/><rect x="5" y="46" width="90" height="8"/><rect x="5" y="80" width="90" height="8"/></g></svg>

  {% elsif name == 'messenger' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M50.0077 0C21.8429 0 0 20.6322 0 48.5057C0 63.0843 5.97701 75.6782 15.7011 84.3755C16.5096 85.1073 17.0038 86.1188 17.0421 87.2222L17.318 96.1226C17.3378 96.777 17.5182 97.4165 17.8434 97.9847C18.1686 98.5529 18.6285 99.0325 19.1826 99.3811C19.7368 99.7297 20.3682 99.9367 21.0212 99.9838C21.6742 100.031 22.3287 99.9168 22.9272 99.6513L32.8544 95.2759C33.7011 94.908 34.6398 94.8352 35.5211 95.0728C40.0805 96.3257 44.9349 97.0038 49.9923 97.0038C78.1571 97.0038 100 76.3755 100 48.5019C100 20.6322 78.1609 0 50.0077 0ZM76.2644 37.8927L63.7433 57.7509C63.2721 58.4984 62.6512 59.1402 61.9197 59.6359C61.1882 60.1316 60.3619 60.4703 59.493 60.6309C58.6241 60.7914 57.7313 60.7702 56.871 60.5687C56.0107 60.3671 55.2014 59.9896 54.4942 59.4598L44.5326 51.9962C44.0889 51.6647 43.55 51.4856 42.9962 51.4856C42.4424 51.4856 41.9034 51.6647 41.4598 51.9962L28.0192 62.1992C26.2375 63.5594 23.8812 61.41 25.0766 59.5172L37.5977 39.659C38.0689 38.9115 38.6898 38.2697 39.4213 37.7741C40.1528 37.2784 40.9791 36.9396 41.848 36.7791C42.7169 36.6186 43.6097 36.6397 44.47 36.8413C45.3303 37.0428 46.1396 37.4204 46.8467 37.9502L56.8084 45.4138C57.2521 45.7452 57.791 45.9243 58.3448 45.9243C58.8986 45.9243 59.4376 45.7452 59.8812 45.4138L73.3218 35.2107C75.1226 33.8314 77.4789 35.9808 76.2644 37.8927Z" fill="currentColor"/></svg>

  {% elsif name == 'minus' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="5" y="46" width="90" height="8"/></g></svg>

  {% elsif name == 'moon' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M21.93,81.51A47,47,0,0,1,9.74,79.93,46.34,46.34,0,1,0,59.5,5,47.49,47.49,0,0,1,21.93,81.51Z"/></g></svg>

  {% elsif name == 'music' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M90.39,29.62l0-3.23V2.82l-.29.05v0L35.51,12.22l-3,.51h0V68.67A15.15,15.15,0,0,0,24.9,66.6a15.29,15.29,0,1,0,15.25,16h0V38.23l42.52-7.29V60.07A15.15,15.15,0,0,0,75.06,58,15.29,15.29,0,1,0,90.32,74h0V29.63Z"/></g></svg>

  {% elsif name == 'opensea' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M100 50C100 77.6127 77.6127 100 50 100C22.3873 100 0 77.6127 0 50C0 22.3873 22.3873 0 50 0C77.6185 0 100 22.3873 100 50ZM24.6678 51.6801L24.8835 51.341L37.8905 30.9932C38.0806 30.6953 38.5275 30.7261 38.6713 31.0497C40.8443 35.9196 42.7193 41.9762 41.8409 45.7468C41.4659 47.2982 40.4385 49.3992 39.2826 51.341C39.1337 51.6236 38.9693 51.901 38.7946 52.1681C38.7124 52.2914 38.5737 52.3633 38.4247 52.3633H25.0479C24.6883 52.3633 24.4777 51.9729 24.6678 51.6801ZM82.6443 58.682V55.4611C82.6443 55.1631 82.3567 54.9474 82.0741 55.0296L66.8634 59.4269C66.7812 59.4474 66.7092 59.4936 66.6527 59.5553C65.0236 61.3549 63.639 62.5626 63.2723 62.8824L63.2469 62.9046C62.3017 63.706 61.1304 64.1427 59.8975 64.1427H54.3906V58.5279H58.7674C58.8804 58.5279 58.9883 58.4868 59.0705 58.4149L59.6355 57.896C59.877 57.6751 60.1647 57.408 60.5088 57.0638C60.5381 57.0345 60.5678 57.0049 60.5978 56.975C60.7786 56.7948 60.9719 56.6023 61.1612 56.3909C61.3873 56.17 61.6082 55.9234 61.8136 55.682C62.1578 55.3121 62.4866 54.9268 62.8308 54.521C63.0774 54.2539 63.3034 53.9508 63.5243 53.6477C63.7709 53.36 64.0123 53.0364 64.2383 52.7282C64.3225 52.6045 64.412 52.4795 64.5035 52.3519C64.5907 52.2301 64.6797 52.106 64.7674 51.9782C64.9318 51.7316 65.0962 51.4696 65.2349 51.223C65.6664 50.5552 66.0312 49.8411 66.3394 49.1271C66.4809 48.8212 66.594 48.4992 66.7039 48.1862C66.7177 48.147 66.7315 48.1078 66.7452 48.0689C66.8685 47.7041 66.9712 47.36 67.0483 46.9952C67.2332 46.1373 67.2743 45.2846 67.1921 44.4318C67.1716 44.1647 67.151 43.9027 67.0894 43.6562V43.6151C67.0688 43.4404 67.0277 43.2452 66.9712 43.0654C66.7863 42.2332 66.4986 41.401 66.1339 40.5842C66.0106 40.2811 65.8668 39.9729 65.7281 39.6904C65.3993 39.0791 65.0551 38.4677 64.6647 37.877C64.5864 37.7522 64.4998 37.6258 64.4134 37.4996C64.3469 37.4024 64.2804 37.3053 64.2178 37.2092C63.9706 36.8278 63.6926 36.4602 63.4229 36.1035C63.3619 36.0229 63.3014 35.9428 63.2417 35.8633C63.0825 35.6542 62.9082 35.4451 62.7328 35.2347C62.6366 35.1194 62.5401 35.0036 62.4455 34.8872C62.1784 34.5636 61.9164 34.2554 61.6493 33.9523C60.6938 32.8735 59.6921 31.8975 58.7982 31.0653C58.6338 30.9009 58.454 30.7365 58.2691 30.5773C57.5756 29.9454 56.9437 29.3957 56.4146 28.9694C56.2556 28.8471 56.113 28.7266 55.9803 28.6144C55.89 28.5381 55.8043 28.4657 55.7211 28.3992C55.5608 28.2797 55.4217 28.1737 55.3049 28.0847C55.2308 28.0283 55.1657 27.9787 55.1098 27.9368C55.0687 27.906 55.0225 27.8855 54.9763 27.87L54.3906 27.7057V22.8563C54.3906 22.0652 54.0721 21.3563 53.5636 20.8374C53.055 20.3186 52.3461 20.0001 51.5653 20.0001C50.0036 20.0001 48.7399 21.2792 48.7399 22.8563V26.1234L48.4471 26.0413L47.6508 25.8152L46.9265 25.6149L46.9198 25.6129L46.906 25.6097H46.8906L41.3939 24.12C41.1525 24.0532 40.947 24.3152 41.0703 24.5361L41.9487 26.1594C41.9986 26.2841 42.0618 26.4089 42.1267 26.5371C42.1687 26.6201 42.2115 26.7045 42.2518 26.7913C42.3957 27.0789 42.5395 27.382 42.6782 27.6851C42.8015 27.9522 42.9248 28.2142 43.0686 28.5019C43.1291 28.6371 43.1907 28.774 43.2533 28.9129C43.4829 29.4227 43.7243 29.9588 43.9624 30.5362C44.1679 31.0242 44.3734 31.5122 44.5532 32.0208C45.0464 33.2999 45.5138 34.6663 45.9197 36.0688C46.0207 36.3827 46.1014 36.6864 46.1833 36.9943C46.2179 37.1247 46.2528 37.2559 46.2895 37.389L46.346 37.6355C46.5104 38.288 46.6543 38.9352 46.757 39.5876C46.8392 40.0345 46.9162 40.4609 46.9573 40.8924C47.019 41.3804 47.0806 41.8685 47.1012 42.3565C47.1423 42.8034 47.1628 43.2709 47.1628 43.7178C47.1628 44.8582 47.0601 45.9575 46.8186 46.9952C46.8035 47.0509 46.7884 47.1074 46.7731 47.1643C46.7055 47.4168 46.6354 47.6786 46.5515 47.9302C46.4757 48.1995 46.3773 48.4689 46.2752 48.7486C46.2391 48.8476 46.2025 48.9479 46.1662 49.05C46.1594 49.0682 46.1527 49.0864 46.1459 49.1047C46.0703 49.3081 45.9934 49.5152 45.8991 49.7179C45.3905 50.9405 44.7587 52.158 44.1063 53.2984C43.1508 54.9885 42.1902 56.4731 41.5172 57.4286C41.4761 57.4903 41.4359 57.549 41.3972 57.6056C41.349 57.676 41.3031 57.7431 41.2604 57.8087C41.0498 58.1066 41.2655 58.5279 41.6302 58.5279H48.7399V64.1427H41.548C39.6165 64.1427 37.8288 63.0485 36.9658 61.2967C36.5189 60.4183 36.3442 59.4525 36.447 58.5073C36.4726 58.2248 36.262 57.9577 35.9744 57.9577H21.4468C21.2002 57.9577 20.9999 58.158 20.9999 58.4046V58.7025C20.9999 67.9698 28.4846 75.4801 37.7209 75.4801H63.7811C68.6661 75.4801 71.4399 71.0286 74.1663 66.6533C74.9263 65.4336 75.6826 64.2198 76.4799 63.1101C77.9131 61.1169 81.3601 59.5347 82.3669 59.1032C82.5313 59.0313 82.6443 58.8669 82.6443 58.682Z" fill="currentColor"/></svg>

  {% elsif name == 'paper-clip' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M21.69,92.12A17.69,17.69,0,0,1,9.18,61.92L56.44,14.67A23.17,23.17,0,1,1,89.21,47.44L45.12,91.53l-5.66-5.66,44.1-44.09A15.17,15.17,0,0,0,62.1,20.32L14.84,67.58A9.69,9.69,0,0,0,28.55,81.29L69.61,40.23a4.21,4.21,0,0,0-6-6L30.72,67.19l-5.66-5.65L58,28.61A12.22,12.22,0,1,1,75.27,45.89L34.21,87A17.64,17.64,0,0,1,21.69,92.12Z"/></g></svg>

  {% elsif name == 'pentagram' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,3.18A46.82,46.82,0,1,0,96.82,50,46.87,46.87,0,0,0,50,3.18ZM85.31,33H62.16L55,11.15A39.26,39.26,0,0,1,85.31,33ZM36.53,60.89l6.83,4.93-11.1,8ZM21.62,40.69H35.17l-2.62,7.89ZM39.05,53.28l4.18-12.59H56.61l4.14,12.68L49.92,61.13ZM50,20.34,54.12,33H45.76ZM63.24,61,67.4,73.75l-10.94-7.9Zm4-12.29-2.62-8H78.46ZM45,11.16,37.71,33h-23A39.24,39.24,0,0,1,45,11.16ZM10.82,50a38.69,38.69,0,0,1,.68-7.19L30,56.19,22.76,78.12A39.06,39.06,0,0,1,10.82,50ZM30.87,84.17l19-13.63,19,13.75a39.06,39.06,0,0,1-38.06-.12ZM77,78.39l-7.2-22.07L88.52,42.88A39.78,39.78,0,0,1,89.18,50,39.08,39.08,0,0,1,77,78.39Z"/></g></svg>

  {% elsif name == 'phone' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95,76.76A11.6,11.6,0,0,1,91.59,85l-3.87,3.87A20.79,20.79,0,0,1,73.13,95a19.81,19.81,0,0,1-2.87-.22,73.37,73.37,0,0,1-26.14-9.52A83,83,0,0,1,27.59,72.4,83.11,83.11,0,0,1,14.72,55.87a73.35,73.35,0,0,1-9.5-26.13,20.54,20.54,0,0,1,5.92-17.48L15,8.39a11.65,11.65,0,0,1,16.43,0l8.48,8.49a11.61,11.61,0,0,1,0,16.44l-3.76,3.76a5.72,5.72,0,0,0-.58,7.33,82.45,82.45,0,0,0,9.13,10.86,81.3,81.3,0,0,0,10.88,9.14,5.7,5.7,0,0,0,7.32-.6l3.76-3.76a11.66,11.66,0,0,1,16.44,0l8.48,8.5A11.57,11.57,0,0,1,95,76.76Z"/></g></svg>

  {% elsif name == 'photo' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M88,7.5H12.05a3.85,3.85,0,0,0-3.84,3.84v65l23.27-22L48.62,71.51,77.2,44.35,91.79,59.2V11.34A3.85,3.85,0,0,0,88,7.5ZM31.43,44.64a12.5,12.5,0,1,1,12.5-12.5A12.51,12.51,0,0,1,31.43,44.64Z"/><path d="M77.08,51.36,54.45,72.87h0l-5.91,5.61L31.38,61.34,10.28,81.27l-2.07,2v5.44a3.85,3.85,0,0,0,3.84,3.84H88a3.85,3.85,0,0,0,3.84-3.84V66.33l-.46-.47Z"/><circle cx="31.43" cy="32.14" r="7.5"/></g></svg>

  {% elsif name == 'pin' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,5A30.4,30.4,0,0,0,23.85,50.91L50,95,76.15,50.91A30.34,30.34,0,0,0,50,5Zm0,44a13.61,13.61,0,1,1,13.6-13.6A13.6,13.6,0,0,1,50,49Z"/></g></svg>

  {% elsif name == 'pinterest' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M86,34.61C86,54.89,74.33,70,57.17,70c-5.76,0-11.18-3-13-6.45,0,0-3.1,11.92-3.76,14.22-2.31,8.12-9.12,16.25-9.64,16.92a.72.72,0,0,1-1.27-.3c-.14-1-1.88-11.29.16-19.64L36.5,46.62a19.32,19.32,0,0,1-1.71-8.19c0-7.66,4.59-13.38,10.3-13.38,4.86,0,7.2,3.54,7.2,7.76,0,4.73-3.11,11.8-4.71,18.35-1.34,5.49,2.84,10,8.42,10,10.12,0,16.93-12.58,16.93-27.49,0-11.32-7.88-19.81-22.22-19.81-16.19,0-26.28,11.7-26.28,24.76A14.6,14.6,0,0,0,28,48.72c1,1.13,1.13,1.59.77,2.88l-1.09,4.15A1.85,1.85,0,0,1,25,57.05C17.49,54.1,14,46.19,14,37.3,14,22.61,26.8,5,52.18,5,72.57,5,86,19.29,86,34.61Z"/></g></svg>

  {% elsif name == 'plus' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="95 46 54 46 54 5 46 5 46 46 5 46 5 54 46 54 46 95 54 95 54 54 95 54 95 46"/></g></svg>

  {% elsif name == 'pound' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M73.86,85.76a13.43,13.43,0,0,1-3.72,1.49,19.41,19.41,0,0,1-14-1.89A31.15,31.15,0,0,0,38,82.15a34.79,34.79,0,0,0,4.73-21.38c-.12-1.22-.29-2.43-.5-3.6H59.5v-8H40a57.45,57.45,0,0,0-5.76-11.23A15.13,15.13,0,0,1,32.9,23.35c3.48-7.66,11.75-10.27,12.6-10.52,6.87-1.35,12.33-.56,16.24,2.33,5.79,4.3,6.62,12.07,6.63,12.15l4-.37,4-.36c0-.46-1.1-11.31-9.76-17.78C60.75,4.43,53.08,3.16,43.8,5l-.21,0c-.52.14-12.7,3.38-18,15a23.08,23.08,0,0,0,1.92,22.28,51.36,51.36,0,0,1,3.8,6.85H22v8H34.09c2,8.92.74,19.43-9.71,29.06l4.46,6.53c.13-.06,12.83-6.1,23.55-.35a28.14,28.14,0,0,0,13.27,3.4,26.52,26.52,0,0,0,6.46-.8,21.5,21.5,0,0,0,5.93-2.44Z"/></g></svg>

  {% elsif name == 'recycle' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M22.81,65,17.7,72.66l6.9,11.5H41V65H22.81Z"/><path d="M31.35,49.23a1,1,0,0,1,.8.29L36,53.37l-4.3-18H9.53L15,38.79a1,1,0,0,1,.45.65,1,1,0,0,1-.16.77L6.17,53.44,16.56,70.76,30.61,49.67A1,1,0,0,1,31.35,49.23Z"/><path d="M50,16.21a.6.6,0,0,1,.09-.13L46.19,7.72,32.77,7.66,24.25,21.71l16.4,9.94Z"/><path d="M59.14,31.53a1,1,0,0,1-.66.54l-5.3,1.3,17.64,5.68,11.51-19L76.52,23a1,1,0,0,1-.78.05,1,1,0,0,1-.58-.54L68.59,7.82,48.4,7.73l10.74,23A1,1,0,0,1,59.14,31.53Z"/><path d="M84.55,38.76,68.73,49.6,78.94,64.49a1,1,0,0,1,.08.14l9.23-.12,5.58-12.2Z"/><path d="M61.21,66.49a1,1,0,0,1-.22-.82l1-5.36L49.55,74l12.54,18.3-.27-6.49a1,1,0,0,1,1-1l16.1.07,8.4-18.36L62,66.85A1.06,1.06,0,0,1,61.21,66.49Z"/></g></svg>

  {% elsif name == 'reddit' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M38.538 50C35.6725 50 33.3334 52.3392 33.3334 55.2047C33.3334 58.0702 35.6725 60.4094 38.538 60.4094C41.4035 60.4094 43.7427 58.0702 43.7427 55.2047C43.7427 52.3392 41.4035 50 38.538 50Z" fill="currentColor"/><path d="M50.0585 72.7485C52.0468 72.7485 58.8304 72.5146 62.3977 68.9474C62.924 68.4211 62.924 67.6023 62.5146 67.0175C61.9883 66.4912 61.1111 66.4912 60.5848 67.0175C58.3041 69.2398 53.5673 70.0585 50.117 70.0585C46.6667 70.0585 41.8714 69.2398 39.6491 67.0175C39.1228 66.4912 38.2456 66.4912 37.7193 67.0175C37.193 67.5439 37.193 68.4211 37.7193 68.9474C41.2281 72.4561 48.0702 72.7485 50.0585 72.7485Z" fill="currentColor"/><path d="M56.2573 55.2047C56.2573 58.0702 58.5965 60.4094 61.462 60.4094C64.3275 60.4094 66.6667 58.0702 66.6667 55.2047C66.6667 52.3392 64.3275 50 61.462 50C58.5965 50 56.2573 52.3392 56.2573 55.2047Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M100 50C100 77.6142 77.6142 100 50 100C22.3858 100 0 77.6142 0 50C0 22.3858 22.3858 0 50 0C77.6142 0 100 22.3858 100 50ZM76.0234 42.6901C80.0585 42.6901 83.3334 45.9649 83.3334 50C83.3334 52.9825 81.5205 55.5555 79.1228 56.7251C79.2398 57.4269 79.2983 58.1287 79.2983 58.8889C79.2983 70.117 66.2573 79.1813 50.117 79.1813C33.9766 79.1813 20.9357 70.117 20.9357 58.8889C20.9357 58.1287 20.9942 57.3684 21.1111 56.6667C18.538 55.4971 16.7837 52.9825 16.7837 50C16.7837 45.9649 20.0585 42.6901 24.0936 42.6901C26.0234 42.6901 27.8363 43.5088 29.1228 44.7368C34.1521 41.0526 41.1111 38.7719 48.8889 38.538L52.5731 21.1111C52.6901 20.7602 52.8655 20.4678 53.1579 20.2924C53.4503 20.117 53.8012 20.0585 54.1521 20.117L66.2573 22.6901C67.076 20.9357 68.8304 19.7661 70.8772 19.7661C73.7427 19.7661 76.0819 22.1053 76.0819 24.9708C76.0819 27.8363 73.7427 30.1754 70.8772 30.1754C68.0702 30.1754 65.7895 27.9532 65.6725 25.2047L54.8538 22.924L51.5205 38.538C59.1228 38.8304 66.0234 41.1696 70.9942 44.7368C72.2807 43.4503 74.0351 42.6901 76.0234 42.6901Z" fill="currentColor"/></svg>

  {% elsif name == 'right-arrow' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="57.09 16.78 51.82 22.8 78.32 46 4.96 46 4.96 54 78.32 54 51.82 77.2 57.09 83.22 95.04 50 57.09 16.78"/></g></svg>

  {% elsif name == 'right-caret' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="28.51 97.85 22.9 92.15 65.7 50 22.9 7.85 28.51 2.15 77.1 50 28.51 97.85"/></g></svg>

  {% elsif name == 'rocket' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M39.37,27A87.83,87.83,0,0,0,25.88,47.25l-.06.12c-6.14-2-12.09,1.13-16.93,3a2.12,2.12,0,0,1-2.6-3C11.18,38.48,22.69,22.15,39.37,27ZM73,60.62A86.34,86.34,0,0,1,52.75,74.11l-.12.06c2,6.15-1.13,12.1-3,16.94a2.12,2.12,0,0,0,3,2.6C61.52,88.83,77.86,77.3,73,60.62Zm-8.5-25.15a8,8,0,1,0,0,11.32A8,8,0,0,0,64.53,35.47ZM88.62,32A69.24,69.24,0,0,1,71,57.09,82.86,82.86,0,0,1,51,70.51a73.63,73.63,0,0,1-8.22,3.38L26.11,57.2A75,75,0,0,1,29.48,49,84,84,0,0,1,42.91,29,69.26,69.26,0,0,1,68,11.37Zm-21.27.67a12,12,0,1,0-4,19.61,11.81,11.81,0,0,0,4-2.63A12,12,0,0,0,67.35,32.64ZM15.48,87.35,26.81,76A2,2,0,0,0,24,73.2L12.66,84.52a2,2,0,0,0,2.82,2.83ZM13.36,76.73,24.68,65.41a2,2,0,0,0-2.83-2.83L10.53,73.9a2,2,0,1,0,2.83,2.83ZM26.1,89.47,37.42,78.15a2,2,0,0,0-2.83-2.83L23.27,86.64a2,2,0,0,0,0,2.83,2,2,0,0,0,2.83,0ZM72.16,9.85l18,18A77.59,77.59,0,0,0,94,6,77.73,77.73,0,0,0,72.16,9.85Z"/></g></svg>

  {% elsif name == 'rss' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M19.11,65.77A14.62,14.62,0,1,0,33.73,80.39,14.63,14.63,0,0,0,19.11,65.77Z"/><path d="M15.11,35.25v8A42.18,42.18,0,0,1,57.25,85.39h8A50.19,50.19,0,0,0,15.11,35.25Z"/><path d="M15.11,5v8A72.47,72.47,0,0,1,87.5,85.39h8A80.48,80.48,0,0,0,15.11,5Z"/></g></svg>

  {% elsif name == 'ruler' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M79.36,80.07,73.53,85.9l-2.12-2.12L77.24,78l-5.5-5.5-3.33,3.33-2.12-2.12,3.33-3.33L65.2,65.9l-5.84,5.84-2.12-2.12,5.84-5.84-5.51-5.5-3.33,3.33-2.12-2.12,3.33-3.33L50.2,50.9l-5.84,5.84-2.12-2.12,5.84-5.84-4.67-4.67-3.34,3.34L38,45.33,41.29,42l-4.43-4.42L31,43.4l-2.12-2.12,5.83-5.83L29.24,30l-3.33,3.33-2.12-2.12,3.33-3.33-5.26-5.26L16,28.4l-2.12-2.12,5.83-5.83L5.5,6.21V94.5H93.79ZM21.17,78.83v-35l35,35Z"/></g></svg>

  {% elsif name == 'search' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M92.83,90.18,71.47,68.83a38.58,38.58,0,1,0-6.29,5l22,22ZM14,41.46A30.47,30.47,0,1,1,44.47,71.93,30.51,30.51,0,0,1,14,41.46Z"/></g></svg>

  {% elsif name == 'shield' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,5s-25.26,9.47-34.74,9.47V48a39,39,0,0,0,2.23,13.1A60,60,0,0,0,50,95,60,60,0,0,0,82.51,61.06,39,39,0,0,0,84.74,48V14.47C75.26,14.47,50,5,50,5Zm-3,62.71L31.38,52,37,46.38,46.63,56,64.35,35.54l6,5.24Z"/></g></svg>

  {% elsif name == 'skull-and-crossbones' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M75.48,27.25A28.84,28.84,0,0,1,78,32.19l5.68-5a7.21,7.21,0,0,0,.76,1,6.64,6.64,0,1,0,2-10.49,6.64,6.64,0,1,0-10.61,1.12L76,19l-4.31,3.78A23.28,23.28,0,0,1,75.48,27.25Z"/><path d="M22.94,69.87a12.12,12.12,0,0,1-1.07-3.38l-6.21,5.44a.8.8,0,0,1-.13-.13,6.64,6.64,0,1,0-2,10.48,6.64,6.64,0,1,0,10.61-1.12,6.74,6.74,0,0,0-.93-.85L29,75.2A11.36,11.36,0,0,1,22.94,69.87Z"/><path d="M15.53,28.21a6.51,6.51,0,0,0,.76-1l5.68,5a29.45,29.45,0,0,1,2.55-4.93,22.8,22.8,0,0,1,3.8-4.48L24,19l.12-.14a6.64,6.64,0,1,0-10.61-1.12,6.64,6.64,0,1,0,2,10.49Z"/><path d="M84.47,71.8a.8.8,0,0,1-.13.13l-6.21-5.44a12.36,12.36,0,0,1-1.06,3.37A11.36,11.36,0,0,1,71,75.2l5.82,5.11a7.37,7.37,0,0,0-.93.85,6.64,6.64,0,1,0,10.61,1.12,6.64,6.64,0,1,0-2-10.48Z"/><path d="M73.47,68.12a11.1,11.1,0,0,0,.92-4.92c0-1.45-.12-2.9-.12-4.26,0-1.85.78-3.47,1.19-5.25a44.24,44.24,0,0,0,.91-6.7,29.93,29.93,0,0,0-1.54-12,26.17,26.17,0,0,0-2.71-5.58,19.71,19.71,0,0,0-3.45-4c-6.34-5.63-15.42-6.93-18.66-7h0c-3.23.1-12.31,1.4-18.65,7a19.43,19.43,0,0,0-3.46,4A26.29,26.29,0,0,0,25.18,35a29.81,29.81,0,0,0-1.55,12,44.24,44.24,0,0,0,.91,6.7c.41,1.78,1.19,3.4,1.19,5.25,0,1.36-.13,2.81-.12,4.26a11.1,11.1,0,0,0,.92,4.92c1,2.11,3.55,3.49,5.81,3.94a1.46,1.46,0,0,0,.22,0c1.53.24,3.35-.15,4.77.52a2.54,2.54,0,0,1,1.38,1.75c.31,1-.08,2.73.39,3.58A3,3,0,0,0,40.19,79h0a7.14,7.14,0,0,0,1.39.67h0a0,0,0,0,0,0,0v-.07a1.23,1.23,0,0,1,2.46,0v.93a.08.08,0,0,0,0,.06l.68.21.14,0,.15,0,.26.07.37.1.13,0,.47.12h0l.44.09h0c.38.09.91.17,1.32.22h.12l.48.06a0,0,0,0,0,0,0s0-.23,0-.33v-.07a1.22,1.22,0,0,1,.65-1.09,1.2,1.2,0,0,1,1.16,0,1.22,1.22,0,0,1,.65,1.09v.07c0,.1,0,.33,0,.33a0,0,0,0,0,0,0l.48-.06h.12c.41,0,.94-.13,1.32-.22h0l.44-.09h0l.47-.12.13,0,.37-.1.26-.07.29-.09.68-.21a.08.08,0,0,0,0-.06v-.93a1.23,1.23,0,0,1,2.46,0v.07a0,0,0,0,0,0,0h0a7.14,7.14,0,0,0,1.39-.67h0a3,3,0,0,0,1.09-1.1c.47-.85.08-2.56.39-3.58a2.54,2.54,0,0,1,1.38-1.75c1.42-.67,3.25-.29,4.78-.52l.21,0C69.92,71.61,72.45,70.23,73.47,68.12ZM46.33,60a4.35,4.35,0,0,1-1.83,2.72,13.3,13.3,0,0,1-3.11,1.61l-.07,0a19.47,19.47,0,0,1-3.58,1.25,10.17,10.17,0,0,1-2.12.27h-.18A4.91,4.91,0,0,1,30.71,63a12.21,12.21,0,0,1-.93-3.47c0-.23-.05-.44-.08-.67a16.11,16.11,0,0,1-.11-3l-.05,0a4.38,4.38,0,0,1,.82-2.37s0,0,0,0l.05-.08.28-.37a5.45,5.45,0,0,1,.46-.5,1.21,1.21,0,0,1,.16-.16,1.91,1.91,0,0,1,1.22-.45A3,3,0,0,1,34,52.4a6.58,6.58,0,0,1,.8.54l1.32,1a16.58,16.58,0,0,0,3.09,2,10.88,10.88,0,0,0,2.47.83,18.91,18.91,0,0,1,2.72.35,3.06,3.06,0,0,1,1.91,2.31A2.14,2.14,0,0,1,46.33,60Zm5.92,12.81a1,1,0,0,1-.53.17c-.57,0-1.13-.46-1.72-.48s-1.15.48-1.71.48a1,1,0,0,1-.54-.17c-.85-.58-.52-2-.35-2.79s.39-1.34.59-2a14,14,0,0,1,.86-2.25,3.13,3.13,0,0,1,.46-.84,1.34,1.34,0,0,1,.67-.45H50a1.31,1.31,0,0,1,.66.45,3.47,3.47,0,0,1,.47.84A15.07,15.07,0,0,1,52,68c.19.67.42,1.33.58,2S53.1,72.23,52.25,72.81Zm18-13.94c0,.23,0,.44-.08.67A11.89,11.89,0,0,1,69.29,63a4.91,4.91,0,0,1-4.73,2.89h-.18a10.26,10.26,0,0,1-2.12-.27,19.47,19.47,0,0,1-3.58-1.25h0a14.59,14.59,0,0,1-3.13-1.64A4.44,4.44,0,0,1,53.66,60h0a2.39,2.39,0,0,1,0-.6,3.06,3.06,0,0,1,1.91-2.32,18.91,18.91,0,0,1,2.72-.35,10.41,10.41,0,0,0,2.47-.83,16.58,16.58,0,0,0,3.09-2l1.32-1a7.46,7.46,0,0,1,.8-.54,3,3,0,0,1,1.48-.47,1.91,1.91,0,0,1,1.22.45,1.21,1.21,0,0,1,.16.16,4.51,4.51,0,0,1,.46.51l.29.37,0,.07s0,0,0,0a4.33,4.33,0,0,1,.82,2.37l0,0A17,17,0,0,1,70.3,58.87Z"/></svg>

  {% elsif name == 'skull' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M82.53,19.74C73.92,6.41,55.75,3.66,50,3.5h0c-5.73.16-23.9,2.91-32.51,16.24a42.39,42.39,0,0,0-6.26,25.84,66.77,66.77,0,0,0,1.34,9.86c.62,2.61,1.75,5,1.75,7.71,0,4.39-.83,9.4,1.18,13.5,1.5,3.1,5.23,5.13,8.54,5.79,2.31.46,5.16-.22,7.35.81a3.79,3.79,0,0,1,2,2.58c.47,1.49-.11,4,.58,5.27a4.54,4.54,0,0,0,1.61,1.62h0a11.53,11.53,0,0,0,2,1l0,0h0a.06.06,0,0,0,.06-.06v-.09h0v0a1.81,1.81,0,0,1,3.62,0v1.36h0s0,.07.06.08l1,.32.42.12.38.11.55.15.2,0,.68.17h0l.64.14h0a16.16,16.16,0,0,0,1.93.33l.18,0,.7.08h0s.06,0,0,0,0-.34,0-.49v-.11A1.82,1.82,0,0,1,50,94h0a1.73,1.73,0,0,1,.85.22,1.8,1.8,0,0,1,1,1.59V96c0,.15,0,.49,0,.49s0,0,0,0h0l.7-.08.18,0a16.16,16.16,0,0,0,1.93-.33h0l.64-.14h0l.68-.17.2,0,.55-.15.38-.11c.14,0,.28-.08.42-.12l1-.32a.09.09,0,0,0,0-.08h0V93.55a1.81,1.81,0,0,1,3.62,0v0h0v.09a.06.06,0,0,0,0,.06h0l0,0a11.3,11.3,0,0,0,2-1h0A4.62,4.62,0,0,0,66,91.1c.69-1.26.11-3.78.58-5.27a3.82,3.82,0,0,1,2-2.58c2.19-1,5-.35,7.35-.81,3.31-.66,7-2.69,8.54-5.79,2-4.1,1.19-9.11,1.18-13.5,0-2.72,1.13-5.1,1.75-7.71a66.77,66.77,0,0,0,1.34-9.86A42.39,42.39,0,0,0,82.53,19.74Zm-40.62,49a22,22,0,0,1-4.67,2.43A28.87,28.87,0,0,1,32,73a15.76,15.76,0,0,1-3.12.39,7.29,7.29,0,0,1-7.23-4.23A19.68,19.68,0,0,1,20.14,63,24.43,24.43,0,0,1,20,58.62l-.06,0a6.28,6.28,0,0,1,1.21-3.48s0,0,0,0a.76.76,0,0,1,.08-.11c.12-.18.27-.37.41-.55a6,6,0,0,1,.68-.74,1.75,1.75,0,0,1,.23-.24c1.34-1.07,2.74-.68,4,0a12.75,12.75,0,0,1,1.18.79,37.48,37.48,0,0,0,6.49,4.34,14.72,14.72,0,0,0,3.64,1.22,26.86,26.86,0,0,1,4,.52,4.46,4.46,0,0,1,2.8,3.4C44.82,65.8,43.43,67.58,41.91,68.71ZM52.52,78c-.83.57-1.65-.32-2.52-.35h0c-.87,0-1.68.92-2.52.35s-.58-2.22-.38-3.12c.18-.75.43-1.49.65-2.24a17.16,17.16,0,0,1,1-2.52,3.46,3.46,0,0,1,.52-.94c.14-.16.5-.49.74-.5H50c.24,0,.6.34.74.5a3.46,3.46,0,0,1,.52.94,16.1,16.1,0,0,1,1,2.52c.22.75.48,1.49.65,2.24C53.1,75.75,53.46,77.32,52.52,78ZM79.86,63a19.4,19.4,0,0,1-1.48,6.1,7.29,7.29,0,0,1-7.23,4.23A15.76,15.76,0,0,1,68,73a28.87,28.87,0,0,1-5.27-1.84,21.59,21.59,0,0,1-4.67-2.43c-1.52-1.13-2.91-2.91-2.72-4.91a4.46,4.46,0,0,1,2.8-3.4,26.86,26.86,0,0,1,4-.52,15.27,15.27,0,0,0,3.64-1.21,38.11,38.11,0,0,0,6.49-4.35,12.75,12.75,0,0,1,1.18-.79c1.23-.71,2.63-1.1,4,0,.08.06.15.16.24.24a6.78,6.78,0,0,1,.67.74l.41.55a.76.76,0,0,1,.08.11s0,0,0,0a6.39,6.39,0,0,1,1.22,3.48L80,58.62A24.43,24.43,0,0,1,79.86,63Z"/></g></svg>

  {% elsif name == 'slash' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="3.22" y="46" width="93.56" height="8" transform="translate(-14.42 79.15) rotate(-69.34)"/></g></svg>

  {% elsif name == 'slider' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="16.84 5 8.84 5 8.84 27.63 5.66 27.63 5.66 39.63 8.84 39.63 8.84 95 16.84 95 16.84 39.63 20.02 39.63 20.02 27.63 16.84 27.63 16.84 5"/><polygon points="41.72 5 33.72 5 33.72 62.88 30.43 62.88 30.43 74.88 33.72 74.88 33.72 95 41.72 95 41.72 74.88 44.8 74.88 44.8 62.88 41.72 62.88 41.72 5"/><polygon points="66.5 5 58.5 5 58.5 32.85 55.2 32.85 55.2 44.85 58.5 44.85 58.5 95 66.5 95 66.5 44.85 69.57 44.85 69.57 32.85 66.5 32.85 66.5 5"/><polygon points="94.34 53.74 91.27 53.74 91.27 5 83.27 5 83.27 53.74 79.98 53.74 79.98 65.74 83.27 65.74 83.27 95 91.27 95 91.27 65.74 94.34 65.74 94.34 53.74"/></g></svg>

  {% elsif name == 'snapchat' %}

    <svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M87.4094 64.2583C86.8401 62.786 85.7361 61.5917 84.325 60.9219L83.6545 60.5587L82.4476 59.9459C78.6487 58.006 75.3728 55.1545 72.9041 51.6387C72.2741 50.7068 71.7281 49.7193 71.2725 48.688C71.0937 48.1433 71.0937 47.8255 71.2278 47.5532C71.3619 47.3262 71.5407 47.1219 71.7642 46.9857C73.0742 46.0873 74.3929 45.2021 75.7202 44.3302C77.2007 43.3455 78.4271 42.0145 79.2962 40.4489C79.9339 39.2652 80.2509 37.9309 80.2149 36.5821C80.1789 35.2333 79.7912 33.9184 79.0913 32.7715C78.3914 31.6246 77.4044 30.6868 76.2311 30.0538C75.0577 29.4208 73.74 29.1153 72.4123 29.1684C71.5212 29.1697 70.6343 29.2919 69.775 29.5316C69.7974 27.4888 69.775 25.3099 69.5739 23.199C69.3505 19.2178 68.147 15.3571 66.0735 11.9706C64.0001 8.58418 61.123 5.78021 57.7059 3.8156C53.5205 1.42088 48.7864 0.191583 43.9828 0.252133C38.954 0.252133 34.3722 1.45509 30.2821 3.8156C26.8555 5.77094 23.9696 8.57211 21.8913 11.9601C19.813 15.3481 18.6094 19.2136 18.3918 23.199C18.213 25.3326 18.1683 27.5115 18.213 29.5316C17.3393 29.2878 16.4371 29.1657 15.531 29.1684C14.2058 29.1224 12.8922 29.4325 11.7228 30.0673C10.5535 30.7021 9.56988 31.6391 8.87142 32.7837C8.17296 33.9283 7.7844 35.2398 7.74502 36.5857C7.70563 37.9316 8.01681 39.2643 8.64711 40.4489C9.52042 42.0248 10.7547 43.3639 12.2455 44.3529L14.2794 45.7147L16.1344 46.9403C16.3579 47.0992 16.5814 47.3035 16.7155 47.5532C16.872 47.8482 16.872 48.166 16.6708 48.7561C16.2238 49.7775 15.6651 50.7308 15.0393 51.6614C12.6395 55.0942 9.46716 57.8956 5.78628 59.8324C3.66301 60.9672 1.47268 61.7163 0.556324 64.2583C-0.136533 66.1876 0.332822 68.3892 2.09849 70.2277C2.74665 70.9086 3.48421 71.4988 4.33352 71.9527C6.05448 72.906 7.8872 73.655 9.78697 74.177C10.1849 74.2772 10.5629 74.4461 10.9045 74.6764C11.5526 75.2438 11.4409 76.1063 12.3126 77.4C12.7596 78.0356 13.296 78.603 13.9218 79.0569C15.7321 80.328 17.766 80.4188 19.9116 80.4869C21.8561 80.555 24.0464 80.6458 26.572 81.4855C27.6225 81.826 28.6953 82.5069 29.9469 83.3013C34.0691 86.2036 38.9669 87.7559 43.9828 87.75C50.8667 87.75 55.0239 85.1625 58.0635 83.2786C59.1064 82.5603 60.223 81.9591 61.3937 81.4855C63.8969 80.6458 66.0872 80.5777 68.0541 80.4869C70.1997 80.3961 72.2335 80.328 74.0439 79.0569C74.7815 78.5349 75.4296 77.8313 75.8766 77.0142C76.5024 75.9474 76.4801 75.1984 77.0612 74.6764C77.3741 74.4494 77.7317 74.2905 78.0893 74.1997C80.0338 73.6777 81.8889 72.9287 83.6322 71.9527C84.5217 71.4758 85.3232 70.8461 86.0013 70.0915L86.0236 70.0688C87.6999 68.2531 88.1246 66.1195 87.4317 64.2583H87.4094Z" fill="currentColor" stroke="currentColor" stroke-width="0.5"/>
    </svg>

  {% elsif name == 'snowflake' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="74.98 41.22 87.83 46.97 91.09 39.66 83.92 36.46 91.59 32.37 87.83 25.31 80.16 29.4 81.5 21.66 73.61 20.29 71.21 34.16 54 43.34 54 23.83 65.11 15.19 60.2 8.87 54 13.7 54 5 46 5 46 13.7 39.8 8.87 34.89 15.19 46 23.83 46 42.96 29.57 33.16 27.84 19.19 19.9 20.17 20.87 27.97 13.4 23.52 9.3 30.39 16.77 34.84 9.45 37.7 12.36 45.15 25.48 40.03 41.86 49.8 25.02 58.78 12.17 53.03 8.91 60.34 16.08 63.54 8.4 67.63 12.17 74.69 19.84 70.6 18.5 78.34 26.39 79.71 28.79 65.84 46 56.66 46 76.17 34.89 84.81 39.8 91.13 46 86.3 46 95 54 95 54 86.3 60.2 91.13 65.11 84.81 54 76.17 54 57.04 70.43 66.84 72.16 80.81 80.1 79.83 79.13 72.03 86.6 76.48 90.7 69.61 83.23 65.16 90.55 62.3 87.64 54.85 74.52 59.97 58.14 50.2 74.98 41.22"/></g></svg>

  {% elsif name == 'spotify' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M79.5763 44.3261C63.4593 34.7545 36.8745 33.8745 21.4888 38.5441C19.0182 39.2934 16.4055 37.8987 15.6567 35.428C14.908 32.9562 16.3016 30.3452 18.774 29.5941C36.4357 24.2331 65.7956 25.2684 84.3498 36.2824C86.5727 37.6019 87.3017 40.472 85.984 42.6907C84.6657 44.913 81.7932 45.6456 79.5763 44.3261ZM79.0485 58.5028C77.9183 60.3376 75.5193 60.9126 73.6869 59.7865C60.25 51.5267 39.7607 49.1343 23.8639 53.9597C21.8023 54.5825 19.6248 53.42 18.9991 51.3625C18.3775 49.3008 19.5406 47.1275 21.5981 46.5006C39.7583 40.9903 62.3332 43.6592 77.766 53.1435C79.5984 54.272 80.1752 56.6722 79.0485 58.5028ZM72.9304 72.1177C72.0325 73.5906 70.1135 74.0528 68.6459 73.1548C56.9041 65.9786 42.1257 64.3576 24.7213 68.3335C23.0442 68.718 21.373 67.6666 20.9903 65.99C20.6058 64.3128 21.653 62.6417 23.3338 62.2589C42.38 57.9045 58.718 59.7787 71.8969 67.8319C73.3663 68.7293 73.829 70.6489 72.9304 72.1177ZM50.0003 0C22.3862 0 0 22.3852 0 49.9994C0 77.6154 22.3862 100 50.0003 100C77.615 100 100 77.6154 100 49.9994C100 22.3852 77.615 0 50.0003 0Z" fill="currentColor"/></svg>


  {% elsif name == 'square' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="5" y="5" width="90" height="90"/></g></svg>

  {% elsif name == 'star' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M94.59,37.25,73.41,59.37,83,90.15A1.43,1.43,0,0,1,82,92a1,1,0,0,1-.45.08h-.06a2.18,2.18,0,0,1-.57-.15L50,77,19,91.89a1.46,1.46,0,0,1-1.94-.67A1.42,1.42,0,0,1,17,90.15l9.56-30.78L5.4,37.25a1.45,1.45,0,0,1,0-2,1.42,1.42,0,0,1,1-.4H35.64l13.07-26a1.45,1.45,0,0,1,1.94-.66,1.48,1.48,0,0,1,.65.66l13.07,26H93.55a1.44,1.44,0,0,1,1,2.45Z"/></g></svg>

  {% elsif name == 'sun' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="50" r="20.36"/><rect x="46" y="5" width="8" height="18.41"/><rect x="76.59" y="46" width="18.41" height="8"/><rect x="66.36" y="20.43" width="18.41" height="8" transform="translate(4.41 59.7) rotate(-44.28)"/><rect x="71.57" y="66.36" width="8" height="18.41" transform="translate(-31.3 76.43) rotate(-45.46)"/><rect x="46" y="76.59" width="8" height="18.41"/><rect x="5" y="46" width="18.41" height="8"/><rect x="15.23" y="71.57" width="18.41" height="8" transform="translate(-45.82 38.53) rotate(-44.28)"/><rect x="20.43" y="15.23" width="8" height="18.41" transform="translate(-10.12 24.71) rotate(-45.46)"/></g></svg>

  {% elsif name == 'sun-2' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95,64.62,83.2,50,95,35.37,76.85,30.49l1-18.77-17.54,6.7L50,2.69,39.73,18.42l-17.55-6.7,1,18.77L5,35.37,16.79,50,5,64.62l18.14,4.89L22.2,88.28l17.54-6.7L50,97.31,60.27,81.58l17.55,6.7-1-18.77Zm-45,11A25.62,25.62,0,1,1,75.63,50,25.65,25.65,0,0,1,50,75.62ZM70.63,50A20.63,20.63,0,1,1,50,29.38,20.64,20.64,0,0,1,70.63,50Z"/></g></svg>

  {% elsif name == 'tag' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M96.75,9.24,91.09,3.58l-15.5,15.5-1.25-.18A44.7,44.7,0,0,0,68,18.45H49.69A15.86,15.86,0,0,0,38.46,23.1L4.86,56.69a5.49,5.49,0,0,0,0,7.76L35.22,94.8a5.46,5.46,0,0,0,7.75,0L77.24,60.54a15.88,15.88,0,0,0,4.65-11.23V31.51a45.33,45.33,0,0,0-.54-6.87ZM61.83,46.53a8,8,0,0,1,0-16,7.93,7.93,0,0,1,5.59,2.29l.14.13a8,8,0,0,1-5.73,13.63Z"/></g></svg>

  {% elsif name == 'tiktok' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M52.9749 0H72.2473C72.2473 0 71.1479 21.9414 98.9569 23.5497V40.494C98.9569 40.494 84.0823 41.2981 72.2473 33.2568L72.4413 68.2941C72.4413 74.5674 70.3459 80.6997 66.4204 85.9149C62.4949 91.1302 56.9156 95.194 50.3887 97.592C43.8618 99.9901 36.6805 100.615 29.7538 99.3865C22.8271 98.1585 16.4663 95.1331 11.4763 90.6932C6.48623 86.2533 3.09133 80.5985 1.72119 74.4443C0.351042 68.2902 1.06724 61.9134 3.77914 56.1209C6.49104 50.3284 11.0767 45.3806 16.9559 41.9036C22.8351 38.4267 29.7435 36.5768 36.8069 36.5882H41.7866V53.992C38.5375 53.0978 35.0535 53.1365 31.8305 54.1026C28.6076 55.0688 25.8098 56.9131 23.8354 59.3732C21.8609 61.8333 20.8105 64.7839 20.8334 67.8049C20.8563 70.8259 21.9515 73.7636 23.9631 76.1998C25.9747 78.6361 28.8002 80.4468 32.0375 81.3743C35.2748 82.3017 38.7591 82.2987 41.9944 81.3657C45.2297 80.4327 48.0513 78.6171 50.0575 76.1775C52.0638 73.7378 53.1526 70.7982 53.169 67.7772L52.9749 0Z" fill="currentColor"/></svg>

  {% elsif name == 'tree' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="65.6 54.78 74.08 54.78 56.74 28.21 65.15 28.21 50 5 34.85 28.21 43.26 28.21 25.92 54.78 34.4 54.78 17.07 81.34 46.06 81.34 46.06 95 50 95 53.94 95 53.94 81.34 82.93 81.34 65.6 54.78"/></g></svg>

  {% elsif name == 'triangle' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="50 11.03 72.5 50 95 88.97 50 88.97 5 88.97 27.5 50 50 11.03"/></g></svg>

  {% elsif name == 'truck' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M26.39,65.23a5.82,5.82,0,0,0-5.73,4.88,5,5,0,0,0-.09,1v0a5.47,5.47,0,0,0,.1,1,5.82,5.82,0,0,0,11.45,0,5.47,5.47,0,0,0,.1-1v0a5,5,0,0,0-.09-1A5.84,5.84,0,0,0,26.39,65.23Z"/><path d="M72.37,65.23a5.84,5.84,0,0,0-5.74,4.83,5.6,5.6,0,0,0,0,2,5.83,5.83,0,0,0,11.48,0,5.6,5.6,0,0,0,0-2A5.84,5.84,0,0,0,72.37,65.23Z"/><path d="M88.13,48.94l-.27-.47-9.73-17a1,1,0,0,0-.87-.5H59V24.11a1,1,0,0,0-1-1H11a1,1,0,0,0-1,1v47a1,1,0,0,0,1,1h5.63a8.26,8.26,0,0,1-.06-1v0c0-.32,0-.64.05-1a9.82,9.82,0,0,1,19.55,0c0,.31,0,.63,0,1v0a8.26,8.26,0,0,1-.06,1H58a1,1,0,0,0,.3,0h4.29c0-.33-.05-.66-.05-1s0-.67.05-1a9.83,9.83,0,0,1,19.56,0c0,.33,0,.66,0,1s0,.67,0,1H89a1,1,0,0,0,1-1v-19ZM69.93,44a.44.44,0,0,1,.44-.43H80.91l5.48,9.32h-16a.44.44,0,0,1-.44-.44Z"/></g></svg>

  {% elsif name == 'tumblr' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M71.25,76.57a10.64,10.64,0,0,1-7.75,3.07c-5.07,0-7.34-3.07-7.34-7.61V46.31H72.57V30.72H56.16V5H43.8A34.83,34.83,0,0,1,23.94,30.87V46.31H36V75.93C36,80,39.87,95,59.63,95c11.62,0,16.43-7.48,16.43-7.48Z"/></g></svg>

  {% elsif name == 'twitch' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.913 0L6.25 17.8571V82.1429H27.4457V100L45.1087 82.1429H59.2391L91.0326 50V0H23.913ZM83.9674 46.4286L69.837 60.7143H55.7065L43.3424 73.2143V60.7143H27.4457V7.14286H83.9674V46.4286Z" fill="currentColor"/><path d="M73.3696 19.6428H66.3044V41.0714H73.3696V19.6428Z" fill="currentColor"/><path d="M53.9402 19.6428H46.875V41.0714H53.9402V19.6428Z" fill="currentColor"/></svg>


  {% elsif name == 'x-social' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
      <path d="M59.5135833,42.3214344 L96.7408333,0 L87.9191667,0 L55.59475,36.7471068 L29.7773333,0 L0,0 L39.041,55.5681337 L0,99.9486553 L8.82216667,99.9486553 L42.9575833,61.1424613 L70.2226667,99.9486553 L100,99.9486553 L59.5114167,42.3214344 L59.5135833,42.3214344 Z M47.4304167,56.0577017 L43.47475,50.5243684 L12.0009167,6.49506112 L25.55125,6.49506112 L50.951,42.0281174 L54.9066667,47.5614507 L87.9233333,93.7489813 L74.373,93.7489813 L47.4304167,56.0598207 L47.4304167,56.0577017 Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'umbrella' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M85.59,47A35.59,35.59,0,0,0,54,11.65V5H46v6.65A35.59,35.59,0,0,0,14.41,47H46v34.6a5.39,5.39,0,0,1-10.78,0h-8a13.39,13.39,0,0,0,26.78,0V47Z"/></g></svg>

  {% elsif name == 'up-arrow' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="83.22 42.91 50 4.96 16.78 42.91 22.8 48.18 46 21.68 46 95.04 54 95.04 54 21.68 77.2 48.18 83.22 42.91"/></g></svg>

  {% elsif name == 'up-caret' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="92.15 77.1 50 34.3 7.85 77.1 2.15 71.49 50 22.9 97.85 71.49 92.15 77.1"/></g></svg>

  {% elsif name == 'vimeo' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M92.67,14.61c-4.21-5.42-13.24-4.51-16-4.07-4.2.76-17.44,6.93-22.1,20.92L54,33.56h2.4c4.5-.45,6.92,0,8.11,1.2s1.5,3,1.21,6.17c-.3,3.46-2.11,7.36-3.91,10.67l-.16.3c-1.35,2.56-3.9,7.37-6.46,7.67-1,.15-2.11-.44-3.15-1.65-3.46-3.76-4.22-10.53-4.82-16.54-.29-2.1-.45-3.92-.75-5.71L46.21,34a96.46,96.46,0,0,0-2.42-11.42C42.58,18.82,39.89,14,36.12,13c-3.61-1.06-8.12.29-11,2.1A98.78,98.78,0,0,0,11.47,25.89C9.8,27.54,8,29.19,6.19,30.7A3.13,3.13,0,0,0,5,33.11a4.3,4.3,0,0,0,.59,1.79c.16.16.16.31.31.46a4.86,4.86,0,0,0,3.46,2.86,11.4,11.4,0,0,0,5.57-.76c2.85-.89,4.05-1.2,5.25.9a30.46,30.46,0,0,1,2.4,6.47,28.9,28.9,0,0,0,1.06,3.31c1.51,4.51,2.71,9.32,4.07,14.6l.44,1.94c2.11,8.88,5.11,21.06,12.78,24.37a8.23,8.23,0,0,0,3.76.75c3.77,0,7.82-1.81,10.09-3.16C63.64,81.23,70,73.71,74.61,67.54c12.64-17.29,19.1-36.39,20-41.36S94.77,17.32,92.67,14.61Z"/></g></svg>

  {% elsif name == 'whatsapp' %}

    <svg width="100" height="97" viewBox="0 0 100 97" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M0.419355 47.9516C0.429032 21.5129 22.7645 0 50.2097 0C63.529 0.00322581 76.029 5.00323 85.429 14.0677C94.8322 23.129 100.006 35.1839 100 47.9903C99.9903 74.429 77.6548 95.9452 50.2097 95.9452H50.1903C41.9038 95.9508 33.7394 93.9473 26.3968 90.1064L0 96.7742L7.06452 71.9258C2.70968 64.6581 0.412903 56.4064 0.419355 47.9516ZM72.9096 57.9903C71.6645 57.3935 65.5484 54.4935 64.4064 54.0903C63.2677 53.6935 62.4387 53.4871 61.6096 54.6871C60.7838 55.8871 58.3967 58.5871 57.6742 59.3839C56.9451 60.1839 56.2193 60.2806 54.9742 59.6839C54.7709 59.5859 54.4938 59.4702 54.1519 59.3274C52.3999 58.5958 48.944 57.1525 44.9709 53.7355C41.2709 50.5613 38.7742 46.6387 38.0484 45.4355C37.3226 44.2387 37.9709 43.5903 38.5935 42.9935C38.9731 42.6315 39.4124 42.1157 39.8495 41.6026C40.055 41.3613 40.26 41.1206 40.458 40.8968C40.9782 40.3089 41.2071 39.8621 41.5155 39.2601C41.5743 39.1453 41.636 39.0249 41.7032 38.8968C42.1161 38.1 41.9096 37.3968 41.5967 36.8C41.3996 36.4199 40.3405 33.9381 39.3233 31.5545C38.7261 30.1552 38.1434 28.7898 37.758 27.9032C36.8693 25.8409 35.9655 25.8437 35.2398 25.8459C35.1437 25.8462 35.0507 25.8464 34.9613 25.8419C34.2355 25.8097 33.4096 25.8 32.5742 25.8C31.7516 25.8 30.4 26.1 29.258 27.3C29.1838 27.3782 29.1007 27.4637 29.0102 27.5568C27.7135 28.8906 24.9032 31.7813 24.9032 37.2935C24.9032 43.1631 29.3154 48.8315 29.9739 49.6775L29.9838 49.6903C30.0255 49.7437 30.101 49.8478 30.2093 49.9972C31.7178 52.0778 39.594 62.9414 51.2387 67.7871C54.2064 69.0161 56.5226 69.7548 58.3322 70.3097C61.3129 71.2226 64.0258 71.0903 66.1677 70.7839C68.5548 70.4387 73.529 67.8871 74.5613 65.0903C75.6 62.2935 75.6 59.8935 75.2903 59.3935C75.0472 58.9926 74.4638 58.7203 73.6002 58.3171C73.3869 58.2174 73.1564 58.1098 72.9096 57.9903Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'wrench' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M94.26,29.06A18.81,18.81,0,0,1,71,41.93L41.9,71A18.81,18.81,0,0,1,17.05,93.7a1.52,1.52,0,0,1-.55-.35,1.56,1.56,0,0,1,0-2.21l9.83-9.83-2.45-4.85-4.35-2L9.33,84.65A1.6,1.6,0,0,1,8,85.08a1.62,1.62,0,0,1-1.15-.87A18.83,18.83,0,0,1,29.65,58.26L58.29,29.64A18.81,18.81,0,0,1,84.24,6.85a1.43,1.43,0,0,1,.44.3,1.56,1.56,0,0,1,0,2.21L75.14,18.9l2.45,4.86,4.35,2,9.24-9.23a1.23,1.23,0,0,1,.53-.35,1.56,1.56,0,0,1,2,.88A18.87,18.87,0,0,1,94.26,29.06Z"/></g></svg>

  {% elsif name == 'x' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="97.83 7.83 92.17 2.17 50 44.34 7.83 2.17 2.17 7.83 44.34 50 2.17 92.17 7.83 97.83 50 55.66 92.17 97.83 97.83 92.17 55.66 50 97.83 7.83"/></g></svg>

  {% elsif name == 'yen' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="54 43.19 81.7 10.91 75.63 5.7 50 35.57 24.37 5.7 18.3 10.91 46 43.19 46 43.71 25.84 43.71 25.84 51.71 46 51.71 46 59.03 25.84 59.03 25.84 67.03 46 67.03 46 94.3 54 94.3 54 67.03 74.17 67.03 74.17 59.03 54 59.03 54 51.71 74.17 51.71 74.17 43.71 54 43.71 54 43.19"/></g></svg>

  {% elsif name == 'youtube' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M79.77,18.85H20.23A15.22,15.22,0,0,0,5,34.08V65.92A15.22,15.22,0,0,0,20.23,81.15H79.77A15.22,15.22,0,0,0,95,65.92V34.08A15.22,15.22,0,0,0,79.77,18.85Zm-26,38.09L42.36,62.81a1.41,1.41,0,0,1-2-1.26V38.45a1.41,1.41,0,0,1,2-1.26l11.45,5.87,11.06,5.69a1.4,1.4,0,0,1,0,2.5Z"/></g></svg>

  {% elsif name == 'zoom' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M92.83,90.18,71.47,68.83a38.58,38.58,0,1,0-6.29,5l22,22ZM14,41.46A30.47,30.47,0,1,1,44.47,71.93,30.51,30.51,0,0,1,14,41.46Z"/><polygon points="48.47 20.31 40.47 20.31 40.47 37.46 23.32 37.46 23.32 45.46 40.47 45.46 40.47 62.61 48.47 62.61 48.47 45.46 65.62 45.46 65.62 37.46 48.47 37.46 48.47 20.31"/></g></svg>

  {% elsif name == 'video-thumbnail' %}

    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 25H25V1H1V25Z" fill="{{ settings.shop_bg_color }}"/><path class="media-badge__outline" d="M0.5 25V25.5H1H25H25.5V25V1V0.5H25H1H0.5V1V25Z" stroke="{{ settings.regular_color }}" stroke-opacity="0.05"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8.19995 5.8V20.2L19.3999 12.5858L8.19995 5.8Z" fill="{{ settings.regular_color }}" fill-opacity="0.6"/></svg>

  {% elsif name == '3d-thumbnail' %}

  <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 25H25V1H1V25Z" fill="{{ settings.shop_bg_color }}"/><path class="media-badge__outline" d="M0.5 25V25.5H1H25H25.5V25V1V0.5H25H1H0.5V1V25Z" stroke="{{ settings.regular_color }}" stroke-opacity="0.05"/><g opacity="0.6"><path fill-rule="evenodd" clip-rule="evenodd" d="M13 6L19.0622 9.5V16.5L13 20L6.93782 16.5V9.5L13 6Z" stroke="{{ settings.regular_color }}" stroke-width="1.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13 20V12.6024C13.6225 12.2002 13.6225 12.2002 13.6225 12.2002L19 9V16.4082L13 20Z" fill="{{ settings.regular_color }}"/></g></svg>

  {% else %}
    <p>?</p>
  {% endif %}
</span>
