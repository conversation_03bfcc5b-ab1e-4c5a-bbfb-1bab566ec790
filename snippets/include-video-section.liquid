<style>

  .star_checked {
    color: {{ settings.review_star_checked_color }};
  }
  .star_unchecked {
    color: {{ settings.review_star_empty_color }};
  }
  .review-container {
    max-width: 330px;
    width: 100%;
    height: auto;
    padding: 20px;
    border-radius: 20px;
    background: {{ settings.review_container_background_color }};
    margin: 20px;
  }
  .video-container {
    width: 300px;
    height: 430px;
    border-radius: 20px;
    margin: 20px;
    flex-shrink: 0;
  }
  .main-container {
    display: flex;
    background-color: {{ settings.video_section_background_color }};
  }
  .videosList {
    display: flex;
    margin: auto;
    width: 100vw;
    overflow-x: hidden;
    overflow-y: hidden;
  }
  .video {
    object-fit: cover;
    height: 100%;
  }
  @media (max-width: 580px) {
    .main-container {
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;
      align-items: center;
    }
    .review-container {
      margin-bottom: 20px;
      /* height: 300px; */
      max-width: 300px;
    }
    .video-container {
      max-width: 186px;
      width: 100%;
      margin: 10px;
    }
  }
  .review-header {
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 16px;
    color: {{ settings.review_header_color }};
  }
  .review-description {
    margin-bottom: 12px;
    text-align: left;
    color: {{ settings.review_description_color }};
  }
</style>

<body>
  <div class="main-container">
    <div class="review-container">
      <div>
        <div class="review-header">
          {{ settings.title }}
        </div>
        <div class="review-description">
          {{ settings.review_description }}
        </div>
        <div>
          {% assign numStars = settings.review_stars %}
          {% assign emptyStars = 5 | minus: settings.review_stars %}
          {% if numStars > 0 and numStars <= 5 %}
            {% for i in (1..numStars) %}
              <span class="fa fa-star star_checked"></span>
            {% endfor %}
            {% for i in (1..emptyStars) %}
              <span class="fa fa-star star_unchecked"></span>
            {% endfor %}
          {% endif %}
        </div>
      </div>
    </div>

    <div class="videosList">
      {% for i in (1..6) %}
        {% assign video_Id = 'review_video_' | append: i %}
        {% if settings[video_Id] != "" %}
          <div class="video-container">
            <video
              id="{{video_Id}}"
              class="video"
              controls
              src="{{ settings[video_Id] }}"
              width="100%"
              height="100%"></video>
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</body>
<script>
        // Get the videosList container
        var videosList = document.querySelector(".videosList");

        // Add event listeners for click and touch
        videosList.addEventListener("mousedown", startDrag);
        videosList.addEventListener("touchstart", startDrag);

        // Variables to store initial touch position and scroll position
        var startX, startScrollLeft;

        // Function to handle touch start event
        function startDrag(event) {
          startX = event.clientX || event.touches[0].clientX;
          startScrollLeft = videosList.scrollLeft;

          // Add event listeners for touch move and touch end
          document.addEventListener("mousemove", drag);
          document.addEventListener("mouseup", endDrag);
          document.addEventListener("touchmove", drag, { passive: false });
          document.addEventListener("touchend", endDrag, { passive: false });
        }

        // Function to handle touch move event
        function drag(event) {
          var currentX = event.clientX || event.touches[0].clientX;
          var deltaX = startX - currentX;

          // Set the new scroll position based on the touch movement
          videosList.scrollLeft = startScrollLeft + deltaX;

          // Prevent default action (text selection)
          event.preventDefault();
        }

        // Function to handle touch end event
        function endDrag() {
          // Remove event listeners for touch move and touch end
          document.removeEventListener("mousemove", drag);
          document.removeEventListener("touchmove", drag);
          document.removeEventListener("mouseup", endDrag);
          document.removeEventListener("touchend", endDrag);
        }
</script>