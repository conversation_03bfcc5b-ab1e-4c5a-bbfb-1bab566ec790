<style>

  .multiStepProgressContainer {
    margin: 30px;
  }

  .progressbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }

  .step {
    margin-top: 10px;
    position: relative;
    text-align: center;
  }

  .progressbar .step.active .circle svg circle:first-of-type {
    fill: #5461C8;
  }

  .stepname {
    color: black;
    font-size: 14px;
    font-weight: 400;
    font-style: normal;
    line-height: normal;
  }

  .circle {
    width: 28px;
    height: 40px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 20px;
    color: white;
    transition: background-color 0.5s ease
    , border 0.5s ease;
    z-index: 1;
  }

  .circle.active {
    background-color: white;
  }

  .progress-line {
    width: 100%;
    height: 4px;
    background-color: #f1f1f1;
    z-index: 0;
    transition: background-color 0.5s ease;
  }

  .progress_container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin: 2rem auto;
    position: relative;
  }
  .progress_container::before {
    content: "";
    background-color: gray;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    height: 4px;
    width: 100%;
    z-index: 1;
  }
  .progress {
    background-color: blueviolet;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    height: 4px;
    width: 0;
    z-index: 1;
    transition: 0.4s ease-in;
  }

  .next_step_button_container {
    margin: 20px 0;
    width: 100%;
  }

  .previousButton {
    margin-bottom: 10px;
    border: 1px;
    border-radius: 5px;
    cursor: pointer;
  }
  .next_step_button {
    width: 483px;
    height: 50px;
    color: white;
    background: #C963CF;
    border: 1px solid;
    font-size: 22px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
  }
  .next_step_button:hover {
    opacity: 0.8;
  }
  .satisfy_container {
    margin: 10px 0;
    width: 100%;
  }
  .satisfy_button {
    width: 100%;
    height: 50px;
    color: black;
    background: rgba(241, 241, 241, 0.76);
    border: 1px solid rgba(241, 241, 241, 0.76);
    font-size: 22px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }
  .subtotalContainer {
    display: flex;
    justify-content: center;
    font-size: 22px;
    font-weight: 500;
    margin-top: 30px;
  }
  .subtotalText {
    color: #5461C9;
  }
  .swatchContainer {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin: 0 0 20px;
    gap: 20px;
  }
  .extraCharges {
    position: relative;
    top: 30px;
    left: 67%;
    font-size: 16px;
    font-weight: 400;
    background-color: white;
    color: #C963CF;
    width: fit-content;
    padding: 1px 3px;
    border: 1px solid white;
    border-radius: 10px;
  }
  .swatchDiv {
    width: 147px;
    margin-top: auto;
  }
  .swatchLabel {
    margin-top: 20px;
    font-size: 20px;
  }
  .swatchImage {
    height: 147px;
    width: 147px;
    border-radius: 10px;
  }
  .swatchImageLabel {
    display: flex;
    justify-content: center;
  }
  .prevNextButtonContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
    margin-top: 40px;
    gap: 15px;
  }

  @import url('https://fonts.googleapis.com/css?family=Poppins:400, 500, 600, 700 &display=swap');

  ::selection {
    color: #fff;
    background: #d43f8d;
  }
  .multiStepContainer {
    width: 100%;
    background: #fff;
    text-align: center;
    border-radius: 5px;
    padding-top: 0;
    padding-bottom: 20px;
  }
  .stepLabel {
    display: flex;
    font-size: 20px;
    font-weight: 400;
    margin-bottom: 30px;
  }
  .stepNote {
    display: flex;
    font-size: 16px;
    font-weight: 300;
    font-style: italic;
    color: black;
  }
  .multiStepContainer .form-outer {
    display: flex;
    overflow: hidden;
  }
  .multiStepContainer .form-outer form {
    display: flex;
    width: 400%;
  }
  .multiStepContainer .form-outer .page {
    min-width: 100%;
    transition: transform 0.5s ease;
    overflow: hidden;
  }
  .multiStepContainer .form-outer .page .field {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-top: 30px;
  }
  form .page .field .label {
    position: absolute;
    top: -30px;
    font-weight: 500;
  }
  form .page .field input {
    height: 100%;
    width: 100%;
    border: 1px solid lightgrey;
    border-radius: 5px;
    padding-left: 15px;
    font-size: 18px;
  }
  form .page .field select {
    width: 100%;
    padding-left: 10px;
    font-size: 17px;
    font-weight: 500;
  }
  form .page .field .next_step_button {
    width: 100%;
    border: none;
    background: #C963CF;
    margin-top: -20px;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: 0.5s ease;
  }
  form .page .field .next_step_button:hover {
    background: #C963CF;
    opacity: 0.7;
  }
  form .page .btns button {
    margin-top: -20px !important;
  }
  form .page .btns button.prev {
    margin-right: 3px;
    font-size: 17px;
  }
  form .page .btns button.next {
    margin-left: 3px;
  }
  .multiStepContainer .progress-bar {
    display: flex;
    margin: 20px 0;
    user-select: none;
    justify-content: space-between;
  }
  .multiStepContainer .progress-bar .step {
    text-align: center;
    width: 100%;
    position: relative;
  }
  .multiStepContainer .progress-bar .step p {
    font-weight: 400;
    font-size: 14px;
    color: #000;
  }
  .multiStepContainer .form-outer .page {
    display: none;
  }
  .multiStepContainer .form-outer .page.active {
    display: block;
    /* Show the active page */
  }

  .progress-bar .step .bullet {
    height: 25px;
    width: 25px;
    display: inline-block;
    border-radius: 50%;
    position: relative;
    transition: 0.2s;
    font-weight: 500;
    font-size: 17px;
    line-height: 25px;
  }

  .progress-bar .step .bullet span {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .progress-bar .step .bullet.active span {
    display: none;
  }


  .progress-bar .step .bullet:before,
  .progress-bar .step .bullet:after {
    position: absolute;
    content: '';
    bottom: 11px;
    right: -63px;
    height: 3px;
    width: 60px;
    background: #f1f1f1;
  }

  .progress-bar .step .bullet.active:after {
    background: #5461C9;
    transform: scaleX(0);
    transform-origin: left;
    animation: animate 0.3s linear forwards;
  }
  @keyframes animate {
    100% {
      transform: scaleX(1);
    }
  }
  .progress-bar .step:last-child .bullet:before,
  .progress-bar .step:last-child .bullet:after {
    display: none;
  }
  .stepname.active {
    color: #5461C9;
    transition: 0.2s linear;
  }
  .progressbar .step .check {
    position: absolute;
    left: 52%;
    top: 37%;
    font-size: 11px;
    transform: translate(-50%, -50%);
    display: none;
  }
  .progressbar .step .check.active {
    display: block;
    color: #5461C9;
  }
  .progressbar .progress-line.active {
    background: #5461C9;
    transform: scaleX(0);
    transform-origin: left;
    animation: animate 0.3s linear forwards;
  }
  .progressbar .step.active .bullet svg circle:first-of-type {
    fill: #5461C9;
  }

  .toggle-button-cover {
    display: flex;
    position: relative;
    width: 100%;
    height: 90px;
    box-sizing: border-box;
    border-radius: 10px;
    margin-bottom: 20px;
    background-color: rgba(241, 241, 241, 0.76);
  }

  .button-cover {
    margin: 10px 20px;
    border-radius: 4px;
  }

  .button-cover,
  .knobs,
  .layer {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  .toggleButton {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .toggleButton.r,
  .toggleButton.r .layer {
    border-radius: 100px;
  }

  .toggleButton.b2 {
    border-radius: 2px;
  }

  .checkbox {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 3;
  }

  .knobs {
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .layer {
    display: flex;
    justify-content: end;
    align-items: center;
    width: 100%;
    background-color: rgba(241, 241, 241, 0.76);
    transition: 0.3s ease all;
  }

  #button-10 .knobs:before,
  #button-10 .knobs:after,
  #button-10 .knobs span {
    position: absolute;
    top: 3px;
    width: 50%;
    height: 90%;
    font-size: 10px;
    font-weight: bold;
    text-align: center;
    line-height: 1;
    padding: 9px 4px;
    border-radius: 2px;
    transition: 0.3s ease all;
    border-radius: 10px;
  }

  #button-10 .knobs:before {
    content: "";
    left: 0;
    background-color: white;
  }

  #button-10 .knobs:after {
    right: 4px;
    color: #4e4e4e;
  }

  #button-10 .knobs span {
    display: flex;
    justify-content: center;
    align-items: center;
    left: 0;
    color: black;
    z-index: 1;
    font-size: 20px;
    font-weight: 500;
  }

  #button-10 .checkbox:checked + .knobs span {
    color: black;
  }

  #button-10 .checkbox:checked + .knobs:before {
    left: 50%;
    background-color: white;
  }

  #button-10 .checkbox:checked + .knobs:after {
    color: black;
    top: 10px;
    margin-top: 10px;
  }

  #button-10 .checkbox:checked ~ .layer {
    background-color: rgba(241, 241, 241, 0.76);
  }

  .dualSecondOption {
    font-size: 20px;
    font-weight: 500;
    margin-right: 18%;
    z-index: 2;
  }
  .tooltip1 {
    position: absolute;
    bottom: 0;
    left: 25%;
    transform: translateX(-50%);
    color: black;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    text-decoration: underline;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tooltip2 {
    position: absolute;
    bottom: 0;
    left: 73%;
    transform: translateX(-50%);
    color: black;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    text-decoration: underline;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tooltip1 .tooltiptext1,
  .tooltip2 .tooltiptext2 {
    display: none;
    width: 120px !important;
    background-color: black !important;
    color: #fff !important;
    text-align: center !important;
    border-radius: 6px !important;
    padding: 5px !important;
    position: absolute !important;
    z-index: 1 !important;
    bottom: 35px !important;
    left: 50% !important;
    margin-left: -60px !important;
  }

  .seeDetails1 {
    cursor: pointer;
    position: relative;
    z-index: 14;
    top: -55px;
    font-size: 12px;
    left: 115px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
  }

  .seeDetailsText1 {
    display: none;
    width: 100px;
    height: fit-content;
    background-color: black;
    color: white;
    padding: 5px;
    position: absolute;
    z-index: 2;
    bottom: 175px;
    border-radius: 10px;
    top: -90px;
  }

  .seeDetailsText1::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: black transparent transparent transparent;
  }

  .seeDetails1:hover .seeDetailsText1 {
    display: block;
  }

  .seeDetails2 {
    cursor: pointer;
    position: relative;
    z-index: 14;
    top: -73px;
    font-size: 12px;
    right: 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    left: 386px;
  }

  .seeDetailsText2 {
    display: none;
    width: 100px;
    height: fit-content;
    background-color: black;
    color: white;
    padding: 5px;
    position: absolute;
    z-index: 2;
    bottom: 175px;
    border-radius: 10px;
    top: -90px;
  }

  .seeDetailsText2::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: black transparent transparent transparent;
  }

  .seeDetails2:hover .seeDetailsText2 {
    display: block;
  }


  .tooltip1 .tooltiptext1::after,
  .tooltip2 .tooltiptext2::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: black transparent transparent transparent;
  }


  .tooltip1:hover .tooltip1 .tooltiptext1,
  .tooltip2:hover .tooltip2 .tooltiptext2 {
    display: block;
  }

  .tooltipSwatch {
    color: black;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    text-decoration: underline;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .tooltiptextSwatch {
    display: none;
    width: 120px !important;
    background-color: black !important;
    color: #fff !important;
    text-align: center !important;
    border-radius: 6px !important;
    padding: 5px 10px !important;
    position: absolute !important;
    z-index: 1 !important;
    bottom: 35px !important;
    left: 50% !important;
    margin-left: -60px !important;
  }

  .tooltiptextSwatch::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: black transparent transparent transparent;
  }

  .tooltipSwatch:hover .tooltiptextSwatch {
    display: block;
  }

  .photoGuideContainer {
    display: flex;
    align-items: center;
    margin: 35px 0;
  }
  .photoGuideContainer img {
    width: 35px;
    margin-right: 20px;
  }
  .photoGuide {
    text-decoration: underline;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
  }
  .selectFileButton {
    border: 3px solid #5461C9 !important;
    border-radius: 10px !important;
    padding: 0 20px !important;
    color: white !important;
    background-color: #5461C9;
  }
  .dragBoxUnique {
    border: 1px solid rgba(0, 0, 0, 0.10) !important;
    background-color: #edebf0 !important;
    border-radius: 10px !important;
    font-size: 20px !important;
    margin-bottom: 30px;
    box-shadow: none;
  }
  .multi-image-uploader {
    padding: 0 10px;
  }
  .extraOptionDiv {
    display: flex;
    width: 100%;
    background: #F1F1F1;
    border: 1px solid #F1F1F1;
    border-radius: 10px;
    height: auto;
    margin-bottom: 30px;
    padding-bottom: 20px;
  }
  .optionImageDiv {
    width: 30%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .optionImage {
    border-radius: 50%;
  }
  .optionDetailContainer {
    padding: 20px 0 0;
    width: 55%;
    text-align: left;
  }
  .optionLabel {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 8px;
    line-height: normal;
  }
  .optionDescription {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: normal;
  }
  .optionPrice {
    font-size: 16px;
    font-weight: 700;
    color: #5461C9;
    line-height: normal;
  }
  .optionCheckboxContainer {
    width: 15%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
  }

  .optionCheckboxContainer input[type="checkbox"] {
    width: 38px;
    height: 38px;
  }
  .sectionNote {
    margin: 20px 0;
    font-size: 22px;
    font-weight: 400;
  }

  .sizeChartContainer {
    display: flex;
    justify-content: space-between;
  }
  .sizeChartLabel {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    text-decoration: underline;
    font-size: 18px;
    font-weight: 500;
  }
  .chart_heading {
    font-size: 26px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 50px;
  }
  .guideNote {
    padding: 0 40px;
    margin-bottom: 25px;
    text-align: left;
  }
  .guideList {
    list-style-type: disc;
    padding: 0 55px;
    margin-bottom: 30px;
    text-align: left;
  }
  .photoGuideImages {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 25px;
  }
  .modal-content {
    overflow-y: scroll;
    height: 627px;
  }
  .guideImage {
    border-radius: 10px;
  }
  .photoGuideDeleteButton {
    display: flex;
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #5461C8;
    border: 1px solid #5461C8;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    width: 30px;
    height: 30px;
    font-size: 25px;
    align-items: center;
    justify-content: center;
  }
  .pricePercentage {
    font-size: 20px;
    font-weight: 400;
  }
  .engravingsContainer {
    display: flex;
    flex-direction: column;
    margin-top: 35px;
  }
  .optionalText {
    color: #5461C8;
    font-size: 20px;
    font-weight: 400;
  }
  .frontEngraving {
    font-size: 20px;
    font-weight: 400;
    display: flex;
  }
  .frontEngravingNote {
    font-size: 16px;
    font-weight: 300;
    margin-bottom: 25px;
    text-align: left;
    font-style: italic;
    width: 90%;
  }
  .backEngravingsMainContainer {
    background: rgba(84, 97, 201, 0.04);
    border: 1px solid rgba(84, 97, 201, 0.04);
    border-radius: 15px;
    margin-bottom: 30px;
    margin-top: 50px;
    padding: 20px 0;
    width: 100%;
    border-spacing: 10px;
    display: flex;
    flex-wrap: wrap;
  }
  .backEngravingsContainer {
    display: flex;
    width: 100%;
    height: auto;
    padding-bottom: 20px;
  }
  .backEngravingsInputContainerMobile {
    display: none;
  }
  .backEngravingsMainContainer .optionDetailContainer {
    padding: 20px 0 0;
    width: 100%;
    text-align: left;
    display: table-cell;
    vertical-align: top;
  }
  .backEngravingsMainContainer .optionDetailMainContainer {
    flex: 1 1 66%;
  }
  .backEngravingsMainContainer .optionLabel {
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 8px;
    line-height: normal;
  }
  .backEngravingsMainContainer .optionDescription {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: normal;
  }
  .backEngravingsMainContainer .optionPrice {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 0 10px;
    font-weight: 700;
    color: #5461C9;
    line-height: normal;
    flex: 1 1 0;
  }
  .backEngravingsMainContainer .optionCheckboxContainer {
    width: 25%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    margin-top: 10px;
    flex: 1 1 0;
  }

  .backEngravingsMainContainer .optionCheckboxContainer input[type="checkbox"] {
    width: 38px;
    height: 38px;
  }
  .backEngravingsInputContainer {
    padding: 0 10px;
    flex: 1 1 100%;
  }
  .backEngravingsInput {
    padding-top: 7px;
    border: 1px solid;
    border-radius: 10px;
    height: 65px;
  }
  .uploaderSection {
    margin-bottom: 30px;
  }
  @media (max-width: 1080px) {
    .dualSecondOption {
      margin-right: 15%;
    }
    .tooltipSwatch,
    .tooltip1,
    .tooltip2,
    .seeDetails1,
    .seeDetails2 {
      display: none;
    }
    .optionImageDiv {
      padding-top: 20px;
    }
    .optionDetailContainer {
      margin-bottom: 15px;
    }
    .optionCheckboxContainer {
      margin-bottom: 30px;
    }
    .extraOptionDiv {
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 351px;
    }
    .optionDetailContainer {
      text-align: center;
    }
    .product-block {
      text-align: center;
    }
    #shopify-block-judge_me_reviews_preview_badge_nxpdpj {
      text-align: center;
    }
    .product-title {
      text-align: center;
    }
    .swatchContainer {
      gap: 8px;
    }
    .swatchDiv {
      width: 110px;
    }
    .swatchImage {
      width: 110px;
      height: 110px;
    }
    .satisfy_button {
      width: 100%;
    }
    .next_step_button {
      width: 100%;
    }
    .extraCharges {
      left: 58%;
      font-size: 14px;
    }
    .sizeChartBox {
      justify-content: center;
    }
  }
  @media (max-width: 1080px) {
    .frontEngravingInput {
      height: 64px;
    }
  }
  @media (max-width: 1080px) and (min-width: 768px) {
    .backEngravingsMainContainer .optionCheckboxContainer input[type="checkbox"] {
      width: 32px;
      height: 32px;
    }
    .backEngravingsMainContainer .optionCheckboxContainer {
      width: 45%;
    }

  }
  @media only screen and (max-width: 768px) {
    .frontEngravingInput {
      height: 60px;
    }
    .backEngravingsMainContainer {
      display: flex;
      flex-direction: column;
    }
    .backEngravingsContainer {
      flex-direction: column;
    }
    .backEngravingsMainContainer .optionLabel {
      text-align: center;
      order: 1;
      padding: 0 40px;
    }
    .backEngravingsMainContainer .optionDescription {
      text-align: center;
      order: 2;
      padding: 0 20px;
    }
    .backEngravingsInputContainer {
      order: 3;
    }
    .backEngravingsMainContainer .optionPrice {
      justify-content: center;
      order: 4;
      margin-top: 20px;
    }
    .backEngravingsMainContainer .optionCheckboxContainer {
      width: 100%;
      order: 5;
      margin-bottom: 0 !important;
    }
    .backEngravingsMainContainer .backEngravingsInput {
      padding-top: 4px;
      height: 60px;
    }
    .backEngravingsInputContainerDesktop {
      display: none;
    }
    .backEngravingsInputContainerMobile {
      display: flex;
      margin: 20px 0 30px;
    }
    .sizeChartBox {
      justify-content: center;
    }
    .toggleExtraCharges {
      left: 32% !important;
    }
  }

  @media (max-width: 480px) {
    .stepname {
      font-size: 12px;
    }
    .stepLabelContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .uploaderStepLabel {
      margin-bottom: 0;
    }
    .photoGuideContainerDesktop {
      display: none !important;
    }
    .photoGuideContainerMobile {
      display: flex !important;
    }
  }

</style>

<head>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" />
</head>

{% assign metaValues = product.metafields.cuddleclones.pdp_customizer.value %}

<input
  type="hidden"
  name="properties[_original_unique_key]"
  value=""
  id="relatedProduct" />

<div class="multiStepContainer">
  <div class="multiStepProgressContainer">
    <div class="progressbar">
      {% for step in metaValues.steps %}
        <div class="step {% if forloop.first %}active{% endif %}">
          <div class="circle">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="28"
                height="28"
                viewBox="0 0 28 28"
                fill="none">
                <circle
                  cx="14"
                  cy="14"
                  r="14"
                  fill="#F1F1F1" />
                <circle
                  cx="14"
                  cy="14"
                  r="8"
                  fill="white" />
              </svg>
            </div>
            <p class="stepname">{{ step.step_name }}</p>
            <div class="check fas fa-check"></div>
          </div>
        </div>
        {% unless forloop.last %}
          <div class="progress-line"></div>
        {% endunless %}

      {% endfor %}
    </div>
  </div>


  <div class="form-outer">
    {% for step in metaValues.steps %}
      <div class="page slide-page {% if forloop.first %}active{%endif%}" style="min-width: 100%;">
        {% if step.identifier == "image_uploader" %}
          <div class="photoGuideContainer photoGuideContainerDesktop" onclick="handlePhotoGuidePopup()">
            <img src="{{"photo_guide.png" |  asset_url}}" alt="Photo_Guide"><span class="photoGuide">Photo Guide</span>
          </div>
        {% endif %}
        {% if step.size_chart %}
          <div class="sizeChartContainer">
            <div>
              <p class="stepLabel">
                {{ step.label }}
              </p>
            </div>
            <p class="sizeChartLabel">
              <span>
                <svg
                  style="border:1px solid black"
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="33"
                  viewBox="0 0 32 33"
                  fill="none">
                  <path d="M12.6119 19.4921L7.44755 24.7143L7.30853 21.4153C7.30525 21.3389 7.28679 21.2655 7.25422 21.1994C7.22165 21.1332 7.1756 21.0757 7.1187 21.0299C7.00379 20.9375 6.85167 20.8989 6.6958 20.9226C6.53993 20.9463 6.39307 21.0304 6.28755 21.1563C6.18202 21.2823 6.12647 21.4398 6.1331 21.5942L6.32241 26.136C6.32658 26.1695 6.33326 26.2023 6.34238 26.2342C6.3621 26.2537 6.35216 26.2874 6.36318 26.3157C6.38866 26.3804 6.42647 26.4384 6.47468 26.4869C6.52372 26.5346 6.58219 26.5718 6.64716 26.5965C6.66688 26.616 6.70048 26.6057 6.72891 26.6164C6.76092 26.6252 6.79379 26.6315 6.82726 26.6353L11.371 26.774C11.4564 26.7766 11.5435 26.7602 11.626 26.7259C11.7085 26.6915 11.7844 26.6401 11.8482 26.5754C11.9502 26.4727 12.0167 26.3412 12.0362 26.2038C12.0472 26.1265 12.043 26.0494 12.0238 25.9771C12.0046 25.9047 11.9709 25.8384 11.9245 25.782C11.8781 25.7256 11.8199 25.6802 11.7534 25.6484C11.6868 25.6166 11.6132 25.599 11.5367 25.5966L8.23638 25.4944L13.4007 20.2722C13.5162 20.1554 13.5854 20.0023 13.5931 19.8466C13.6008 19.6909 13.5463 19.5453 13.4417 19.4419C13.3371 19.3384 13.1909 19.2856 13.0353 19.295C12.8797 19.3044 12.7273 19.3753 12.6119 19.4921Z" fill="#231F20" />
                  <path d="M19.1941 13.3118L24.547 8.28318L24.5646 11.585C24.565 11.6615 24.5808 11.7355 24.6109 11.8028C24.641 11.8701 24.6849 11.9294 24.7401 11.9772C24.8515 12.0738 25.0021 12.1179 25.1588 12.1C25.3154 12.082 25.4653 12.0034 25.5753 11.8814C25.6854 11.7594 25.7467 11.6041 25.7458 11.4496L25.7237 6.9038C25.7208 6.87024 25.7153 6.83721 25.7074 6.80499C25.6884 6.78478 25.6996 6.75145 25.6896 6.72275C25.6665 6.65718 25.6309 6.59777 25.5845 6.54753C25.5372 6.49808 25.4802 6.4588 25.4162 6.43166C25.3972 6.41145 25.3632 6.42052 25.3352 6.40878C25.3035 6.39884 25.2709 6.39133 25.2376 6.3863L20.7021 6.08051C20.6168 6.07474 20.5292 6.08796 20.4455 6.11923C20.3618 6.1505 20.284 6.19906 20.2178 6.26144C20.1122 6.36032 20.0409 6.48923 20.0163 6.62579C20.0025 6.70266 20.0039 6.77983 20.0204 6.85289C20.0369 6.92594 20.0682 6.99343 20.1124 7.05151C20.1567 7.10958 20.2132 7.15709 20.2785 7.19132C20.3438 7.22554 20.4168 7.24581 20.4931 7.25096L23.7875 7.47458L18.4345 12.5032C18.3148 12.6156 18.24 12.7661 18.2266 12.9214C18.2131 13.0768 18.2622 13.2243 18.3629 13.3315C18.4637 13.4387 18.6078 13.4969 18.7637 13.4932C18.9196 13.4895 19.0744 13.4242 19.1941 13.3118Z" fill="#231F20" />
                </svg>
              </span>
              <a
                class="sizeChartLinkText"
                style="color:black"
                onclick="handleSizeChartPopup()">Size Guide</a>
            </p>
          </div>
        {% else %}
          <div class="stepLabelContainer">
            <div>
              <p class="stepLabel {% if step.identifier == "image_uploader" %}uploaderStepLabel{%endif%}">
                {{ step.label }}
              </p>
            </div>
            {% if step.identifier == "image_uploader" %}
              <div>
                <div
                  class="photoGuideContainer photoGuideContainerMobile"
                  style="display:none;"
                  onclick="handlePhotoGuidePopup()">
                  <img src="{{"photo_guide.png" |  asset_url}}" alt="Photo_Guide"><span class="photoGuide">Photo Guide</span>
                </div>
              </div>
            {% endif %}
          </div>
        {% endif %}


        {% if step.toggle_options %}
          <input
            type="hidden"
            class="{{step.label| downcase | replace: " ","" }}HiddenInput {%if step.native%}nativeHiddenInputs{% else %}customHiddenInputs{%endif%}"
            value="" />
          {% for option in step.toggle_options %}
            <p class="stepLabel">
              {{ option.label }}
            </p>
            <div class="toggle-button-cover">
              <div class="button-cover">
                <div class="toggleButton b2" id="button-10">
                  <input
                    type="hidden"
                    class="defaultCheckbox toggleCheckboxes {{option.label | downcase |  replace: " ","" }}HiddenInput {{option.label | downcase |  replace: " ","" }}ToggleHiddenInputs"
                    value="{{option.options[0].label}}">
                  <input
                    type="checkbox"
                    class="checkbox {{option.options[0].label | downcase |  replace: " ",""}}"
                    onchange="handleToggleSwitch(this,'{{option.options[0].label |  downcase |  replace: ' ','' |  replace: '-','' }}','{{option.options[0].label}}','{{option.options[1].label}}','{{option.label |  downcase |  replace: ' ','' |  replace: '-',''}}','{{step.step_name |  downcase |  replace: ' ','' |  replace: '-',''}}','{{option.options[0].show_selective_swatches |  downcase |  replace: ' ','_' }}','{{option.options[1].show_selective_swatches |  downcase |  replace: ' ','_' }}')"
                    {% if option.default_checked %}
                    checked
                    {% endif %} />
                  <div class="knobs">
                    {% if option.options[0].extra_charges and option.options[0].extra_charges > 0 %}
                      <p class="extraCharges toggleExtraCharges" style="top: -15px;left:-7%;background:#F1F1F1;border-color:#F1F1F1">+&nbsp;${{ option.options[0].extra_charges }}</p>
                    {% endif %}
                    <span>{{ option.options[0].label }}</span>
                  </div>
                  <div class="layer">
                    {% if option.options[1].extra_charges and option.options[1].extra_charges > 0 %}
                      <p class="extraCharges toggleExtraCharges" style="top: -15px;left:28%;background:#F1F1F1;border-color:#F1F1F1;z-index:2;">+&nbsp;${{ option.options[1].extra_charges }}</p>
                    {% endif %}
                    <span class="dualSecondOption">{{ option.options[1].label }}</span>
                  </div>
                </div>
              </div>
            </div>
            {% if option.options[0].see_details_text %}
              <div class="seeDetails1">
                SEE DETAILS<img
                  width="15px"
                  height="15px"
                  src="{{"questionMark.png" |  asset_url}}"
                  alt="questionMark">
                <p class="seeDetailsText1">{{ option.options[0].see_details_text }}</p>
              </div>
            {% endif %}
            {% if option.options[1].see_details_text %}
              <div class="seeDetails2">
                SEE DETAILS<img
                  width="15px"
                  height="15px"
                  src="{{"questionMark.png" |  asset_url}}"
                  alt="questionMark">
                <p class="seeDetailsText2">{{ option.options[1].see_details_text }}</p>
              </div>
            {% endif %}

            {% if option.options and option.options.size > 0 %}
              {% for dualoption in option.options %}
                {% if dualoption.sub_options and dualoption.sub_options.size > 0 %}
                  {% if forloop.first %}
                    <input
                      type="hidden"
                      class="defaultCheckbox {{option.label | downcase |  replace: " ",""}}{{dualoption.sub_option_label | downcase}}HiddenInput"
                      value="{{option.options[0].sub_options[0].label}}">
                  {% endif %}
                  <div class="swatchContainer {% if forloop.first %}{{dualoption.label |  downcase |  replace: " ","" |  replace: "-","" }}FirstCase{%else%}{{dualoption.label |  downcase | replace: " ","" |  replace: "-","" }}SecondCase{%endif%}" style="{% if forloop.first %}{%else%}display:none;{%endif%}">
                    {% assign classname = dualoption.sub_option_label | downcase | replace: " ", "" | replace: '"', "" %}
                    {% for suboption in dualoption.sub_options %}
                      <div class="swatchDiv {%if suboption.show_with_option and suboption.show_with_option!=""%}show_with_{{suboption.show_with_option |  downcase |  replace: ' ','_' }}{%else%}simpleShowOption{%endif%}">
                        {% if suboption.extra_charges and suboption.extra_charges > 0 %}
                          <p class="extraCharges">+&nbsp;${{ suboption.extra_charges }}</p>
                        {% endif %}
                        {% if suboption.show_with_option and suboption.show_with_option != "" %}
                          {% assign show_with_option_name = suboption.show_with_option %}
                        {% endif %}
                        <img
                          style="{% if forloop.first %}border:4px solid rgb(84, 97, 201);{% endif %}"
                          onclick="selectSubOptionSwatch('{{step.step_name |  downcase |  replace: ' ','' }}','{{option.label |  downcase |  replace: ' ','' }}','{{classname}}','{{ suboption.label | replace: '"', '&quot;' }}',this,{{ suboption.extra_charges }})"
                          class="swatchImage _{{option.label| downcase| replace: ' ','' }}SwatchImages"
                          src="{{suboption.imageUrl}}"
                          alt="{{suboption.alt}}">
                        <p class="swatchImageLabel">{{ suboption.label }}</p>

                        {% if suboption.see_details_text and suboption.see_details_text != "" %}
                          <div class="tooltipSwatch">
                            SEE DETAILS<img
                              width="15px"
                              height="15px"
                              src="{{"questionMark.png" |  asset_url}}"
                              alt="questionMark">
                            <span class="tooltiptextSwatch">{{ suboption.see_details_text }}</span>
                          </div>
                        {% endif %}
                      </div>
                    {% endfor %}
                  </div>
                {% endif %}
              {% endfor %}
            {% endif %}
          {% endfor %}
        {% endif %}

        {% if step.identifier == "swatches" and step.swatches %}
          {% for option in step.swatches %}
            <div>
              <p class="stepLabel">{{ option.label }}:</p>
              <p class="stepNote">
                {% if step.step_note %}Note :
                {% endif %}
                {{ step.step_note }}</p>
              <div class="swatchContainer">
                {% assign classname = step.step_name | downcase | replace: " ", "" | replace: '"', "" %}
                <input
                  type="hidden"
                  data-name="{{classname}}"
                  id="{{option.name}}HiddenInput_{{forloop.index}} {{classname}}HiddenInput_{{forloop.index}}"
                  class="{%if step.gallery_image_change%}{{classname}}HiddenInput{% elsif step.gallery_image_change_on_options%} {{option.name}}HiddenInput{%endif%} {{classname}}HiddenInput_{{forloop.index}} {%if step.native%}nativeHiddenInputs{% else %}customHiddenInputs{%endif%}"
                  value='{{option.options[0].label}}'>
                {% for swatchOption in option.options %}
                  <div class="swatchDiv">
                    {% if swatchOption.extra_charges and swatchOption.extra_charges > 0 %}
                      <p class="extraCharges">+&nbsp;${{ swatchOption.extra_charges }}</p>
                    {% endif %}
                    <img
                      style="{% if forloop.first %}border:4px solid rgb(84, 97, 201);{% endif %}"
                      onclick="selectSwatch('{{classname}}','{{option.name}}','{{ swatchOption.label | replace: '"', '&quot;' }}',this,{{ swatchOption.extra_charges }},{{step.create_uploaders}},{{forloop.index}},{{forloop.parentloop.index}},{{step.native}})"
                      class="swatchImage _{{classname}}SwatchImages_{{forloop.parentloop.index}}"
                      src="{{swatchOption.imageUrl}}"
                      alt="{{swatchOption.alt}}">
                    <p class="swatchImageLabel">{{ swatchOption.label }}</p>
                    {% if swatchOption.sub_label and swatchOption.sub_label != "" %}
                      <p class="swatchImageLabel">{{ swatchOption.sub_label }}</p>
                    {% endif %}
                  </div>
                {% endfor %}
              </div>
            </div>
          {% endfor %}
        {% endif %}

        {% comment %} Image Uploader Section  {% endcomment %}
        {% if step.identifier == "image_uploader" %}
          <div class="multi-image-uploader" data-value="{{step.step_name| downcase| replace: ' ','' }}">
            {% for i in (1..step.fixed_number_of_uploaders) %}
              {% comment %} <p class="stepLabel">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                {{ step.label }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                </p> {% endcomment %}
              <div class="uploaderSection_{{ i }} uploaderSection">
                <div class="image-uploader" id="image-uploader-{{ i }}">
                  <button class="close-button-{{ i }} close-button-simple">✖</button>

                  {% assign suffix = 'th' %}
                  {% if i == 1 or i == 21 or i == 31 or i == 41 %}
                    {% assign suffix = 'st' %}
                  {% elsif i == 2 or i == 22 or i == 32 or i == 42 %}
                    {% assign suffix = 'nd' %}
                  {% elsif i == 3 or i == 23 or i == 33 or i == 43 %}
                    {% assign suffix = 'rd' %}
                  {% endif %}

                  {% comment %} <label style="font-weight: 600;">Upload Your {{ i }}{{ suffix }} Pet Photo:</label> {% endcomment %}
                  <div class="selected-image-{{ i }} selected-image-container-simple"></div>

                  <input
                    type="hidden"
                    name="properties[_image_url_{{ i }}]"
                    value=""
                    id="hiddenInputSimple-{{ i }} hiddenInputImage_{{ i }}"
                    class="hiddenInputSimple-{{ i }} hiddenInputImage">

                  <div class="uploadOuter_{{ i }} uploadOuter">
                    <span class="dragBoxUnique">
                      <span class="dragBoxText">Drag + Drop</span>
                      <input
                        class="image_uploader_{{ i }} fileInput"
                        onclick="this.value=null"
                        type="file"
                        id="uploadFile-{{ i }}"
                        onChange="portraitDragNdrop(event,'image-uploader-{{ i }}',{{ i }},'{{step.step_name |  replace: ' ','' |  downcase }}',{{step.fixed_number_of_uploaders}})"
                        ondragover="drag(event)"
                        ondrop="drop(event)">
                      <span class="orText">
                        <strong>or</strong>
                      </span>
                      <label class="btn btn-primary selectFileButton" for="uploadFile-{{ i }}">
                        Select File
                      </label>
                    </span>
                  </div>

                </div>

                {% if step.optional_pet_name_field %}
                  <div class="pajamas-pdp-line-item-size-wrapper" style="margin-bottom:5px;text-align:left !important">
                    <label style="font-weight: 600;font-size:20px !important;font-weight:400 !important">Pet Name (Optional):</label>
                  </div>
                  <div class="pajamas-pdp-line-item-size-wrapper" style="margin-bottom:1rem">
                    <input
                      class="multiTextField_{{ i }} input"
                      type="text"
                      name="properties[Pet Name {{ i}}]"
                      id="custom-text-field custom-text-field-{{ i }}"
                      value=""
                      placeholder="Type Your Pets Name Here"
                      style="border:1px solid;border-radius:10px;">
                  </div>
                {% endif %}
              </div>

              {% if step.front_engraving and step.front_engraving.display %}
                <div class="frontEngravingsContainer">
                  <p class="frontEngraving">{{ step.front_engraving.label }}
                    {% if step.front_engraving.optional %}
                      <span class="optionalText">&nbsp;&nbsp;&nbsp;&nbsp;(Optional)</span>
                    {% endif %}:
                  </p>
                  <p class="frontEngravingNote">Note : {{ step.front_engraving.note }}</p>
                  <textarea
                    class="input frontEngravingInput"
                    maxlength="12"
                    type="text"
                    name="properties[Front Engraving {{i}}]"
                    id="frontEngravingInput"
                    value=""
                    placeholder="{{step.front_engraving.input_field_placeholder}}"
                    style="border:1px solid;border-radius:10px;"></textarea>
                </div>
              {% endif %}

              {% if step.back_engraving and step.back_engraving.display %}
                <input
                  type="hidden"
                  class="backEngravingsIDInput"
                  value="{{step.back_engraving.id}}" />
                <div class="backEngravingsMainContainer">
                  <div class="optionCheckboxContainer">
                    <input
                      type="checkbox"
                      value="No"
                      name="properties[Back Engravings {{i}}]"
                      class="backEngravingCheckbox backEngravingCheckbox_{{i}}"
                      id="backEngravingCheckbox"
                      onchange="handleBackEngraving(this,{{i}},'{{step.step_name |  replace: ' ','' |  downcase }}',{{step.back_engraving.extra_charges}})">
                  </div>
                  <div class="optionDetailMainContainer">
                    <div class="optionDetailContainer">
                      <div class="optionLabel">
                        {{ step.back_engraving.label }}
                      </div>
                      <div class="optionDescription">
                        {{ step.back_engraving.descritpion }}
                      </div>
                    </div>
                  </div>
                  <div class="optionPrice">
                    +${{ step.back_engraving.extra_charges }}</div>
                  <div class="backEngravingsInputContainer">
                    <textarea
                      disabled
                      maxlength="32"
                      class="input backEngravingsInput backEngravingsInput_{{i}}"
                      type="text"
                      name="properties[Back Engraving text {{i}}]"
                      id="backEngravingInput"
                      oninput="handleBackEngravingInput(this,{{i}},'{{step.step_name |  replace: ' ','' |  downcase }}')"
                      value=""
                      placeholder="{{step.back_engraving.input_field_placeholder}}"></textarea>
                  </div>
                </div>
              {% endif %}
            {% endfor %}
          </div>

        {% endif %}

        <div class="extraOptionsContainer">
          {% for option in step.extra_options %}
            <div class="extraOptionDiv">
              <div class="optionImageDiv">
                <img
                  class="optionImage"
                  width="76px"
                  height="76px"
                  src="{{option.imageUrl}}"
                  alt="{{option.label}}">
              </div>
              <div class="optionDetailContainer">
                <div class="optionLabel">
                  {{ option.label }}
                </div>
                <div class="optionDescription">
                  {{ option.descritpion }}
                </div>
                <div class="optionPrice">
                  +${{ option.price }}</div>
              </div>
              <div class="optionCheckboxContainer">
                <input
                  type="checkbox"
                  class="extraOption"
                  id="option-{{ forloop.index }}"
                  name="extra_options"
                  onchange="handleExtraOptionsPrice(this,{{option.price}})"
                  value="{{ option.id }}">
                <label for="option-{{ forloop.index }}"></label>
              </div>
            </div>
          {% endfor %}
        </div>

        <div class="subtotalContainer">
          <span class="subtotalText">Subtotal:&nbsp;</span>$
          <span class="subTotalPrice">{{ product.price | divided_by: 100.00 }}</span>
        </div>

        {% if step.note %}
          <p class="sectionNote">{{ step.note }}</p>
        {% endif %}

        <div class="field">
          {% if forloop.first %}
            <div class="next_step_button_container" style="margin-top: 40px;">
              <button class="{{ step.step_name | downcase | replace: " ", "" }}NextButton next_step_button">Next Step</button>
            </div>
            <div class="satisfy_container">
              <button class="satisfy_button" disabled>
                <span style="display: flex;">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="30"
                    height="30"
                    viewBox="0 0 41 41"
                    fill="none">
                    <path d="M20.5 3.41669C17.1212 3.41669 13.8183 4.41861 11.009 6.29575C8.19965 8.17289 6.01004 10.8409 4.71704 13.9625C3.42404 17.0841 3.08574 20.519 3.7449 23.8328C4.40406 27.1467 6.03109 30.1906 8.42024 32.5798C10.8094 34.9689 13.8533 36.5959 17.1672 37.2551C20.481 37.9143 23.9159 37.576 27.0375 36.283C30.1591 34.99 32.8271 32.8004 34.7043 29.991C36.5814 27.1817 37.5833 23.8788 37.5833 20.5C37.5833 18.2566 37.1414 16.0352 36.2829 13.9625C35.4244 11.8899 34.1661 10.0066 32.5797 8.42028C30.9934 6.83395 29.1101 5.5756 27.0375 4.71708C24.9648 3.85856 22.7434 3.41669 20.5 3.41669ZM27.8458 16.4171L20.0387 26.6671C19.8796 26.8738 19.6752 27.0414 19.4413 27.1569C19.2073 27.2724 18.95 27.3327 18.6891 27.3334C18.4297 27.3348 18.1733 27.277 17.9394 27.1645C17.7056 27.0521 17.5004 26.8878 17.3396 26.6842L13.1712 21.3713C13.0333 21.194 12.9316 20.9914 12.8719 20.7748C12.8123 20.5583 12.7959 20.3321 12.8236 20.1092C12.8514 19.8864 12.9228 19.6711 13.0337 19.4758C13.1446 19.2806 13.293 19.109 13.4702 18.9711C13.8281 18.6924 14.2821 18.5674 14.7322 18.6234C14.9551 18.6512 15.1703 18.7226 15.3656 18.8335C15.5609 18.9445 15.7324 19.0928 15.8704 19.27L18.655 22.8234L25.1125 14.2817C25.2493 14.1022 25.4202 13.9515 25.6153 13.838C25.8104 13.7246 26.026 13.6507 26.2496 13.6205C26.4733 13.5904 26.7007 13.6046 26.9189 13.6623C27.1371 13.7201 27.3418 13.8203 27.5212 13.9571C27.7007 14.094 27.8515 14.2648 27.9649 14.4599C28.0784 14.655 28.1523 14.8706 28.1824 15.0943C28.2125 15.3179 28.1983 15.5453 28.1406 15.7635C28.0828 15.9817 27.9827 16.1864 27.8458 16.3659V16.4171Z" fill="#5461C9" />
                  </svg>
                </span>
                <span>{{ metaValues.guarantee_note }}</span>
              </button>
            </div>
          {% else %}
            <div class="prevNextButtonContainer">
              <div class="previousButton_container">
                <button class="{{ step.step_name | downcase | replace: " ", "" }}PrevButton previousButton">
                  <span style="display: flex;">
                    <svg
                      height="50px"
                      id="Layer_1"
                      style="enable-background:new 0 0 512 512;color:#F1F1F1"
                      version="1.1"
                      viewBox="0 0 512 512"
                      width="75px"
                      fill="#cfcfcf"
                      xml:space="preserve"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"><polygon points="352,128.4 319.7,96 160,256 160,256 160,256 319.7,416 352,383.6 224.7,256 " /></svg>
                  </span>
                </button>
              </div>
              {% if step.identifier and step.identifier == "image_uploader" %}
                <div class="next_step_button_container">
                  <button
                    disabled
                    style="cursor:not-allowed;opacity:0.5;pointer-events:none"
                    class="{{ step.step_name | downcase | replace: " ", "" }}NextButton next_step_button">Next Step</button>
                </div>
              {% elsif step.option_type == "last_step" %}
                <div class="next_step_button_container">
                  <button class="{{ step.step_name | downcase | replace: " ", "" }}NextButton next_step_button">ADD TO CART</button>
                </div>
              {% else %}
                <div class="next_step_button_container">
                  <button class="{{ step.step_name | downcase | replace: " ", "" }}NextButton next_step_button">Next Step</button>
                </div>
              {% endif %}
            </div>
            <div class="satisfy_container">
              <button class="satisfy_button" disabled>
                <span style="display: flex;">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="30"
                    height="30"
                    viewBox="0 0 41 41"
                    fill="none">
                    <path d="M20.5 3.41669C17.1212 3.41669 13.8183 4.41861 11.009 6.29575C8.19965 8.17289 6.01004 10.8409 4.71704 13.9625C3.42404 17.0841 3.08574 20.519 3.7449 23.8328C4.40406 27.1467 6.03109 30.1906 8.42024 32.5798C10.8094 34.9689 13.8533 36.5959 17.1672 37.2551C20.481 37.9143 23.9159 37.576 27.0375 36.283C30.1591 34.99 32.8271 32.8004 34.7043 29.991C36.5814 27.1817 37.5833 23.8788 37.5833 20.5C37.5833 18.2566 37.1414 16.0352 36.2829 13.9625C35.4244 11.8899 34.1661 10.0066 32.5797 8.42028C30.9934 6.83395 29.1101 5.5756 27.0375 4.71708C24.9648 3.85856 22.7434 3.41669 20.5 3.41669ZM27.8458 16.4171L20.0387 26.6671C19.8796 26.8738 19.6752 27.0414 19.4413 27.1569C19.2073 27.2724 18.95 27.3327 18.6891 27.3334C18.4297 27.3348 18.1733 27.277 17.9394 27.1645C17.7056 27.0521 17.5004 26.8878 17.3396 26.6842L13.1712 21.3713C13.0333 21.194 12.9316 20.9914 12.8719 20.7748C12.8123 20.5583 12.7959 20.3321 12.8236 20.1092C12.8514 19.8864 12.9228 19.6711 13.0337 19.4758C13.1446 19.2806 13.293 19.109 13.4702 18.9711C13.8281 18.6924 14.2821 18.5674 14.7322 18.6234C14.9551 18.6512 15.1703 18.7226 15.3656 18.8335C15.5609 18.9445 15.7324 19.0928 15.8704 19.27L18.655 22.8234L25.1125 14.2817C25.2493 14.1022 25.4202 13.9515 25.6153 13.838C25.8104 13.7246 26.026 13.6507 26.2496 13.6205C26.4733 13.5904 26.7007 13.6046 26.9189 13.6623C27.1371 13.7201 27.3418 13.8203 27.5212 13.9571C27.7007 14.094 27.8515 14.2648 27.9649 14.4599C28.0784 14.655 28.1523 14.8706 28.1824 15.0943C28.2125 15.3179 28.1983 15.5453 28.1406 15.7635C28.0828 15.9817 27.9827 16.1864 27.8458 16.3659V16.4171Z" fill="#5461C9" />
                  </svg>
                </span>
                <span>{{ metaValues.guarantee_note }}</span>
              </button>
            </div>
          {% endif %}
        </div>
      </div>
    {% endfor %}
  </div>
</div>
<div
  style="display: none;"
  class="popup-chart-container"
  id="pajamasSizeChartOpen">
  <div class="modal-overlay" onclick="closeSizeChartPopup()"></div>
  <div class="modal-content" style="overflow-y: hidden;">
    <h5 class="pajamas-new-size-chart-open-header">Size Guide</h5>
    <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
      <img
        alt="{{ image.alt }}"
        class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1"
        loading="lazy"
        src="{{ product.metafields.custom.size_chart | img_url: 'master' }}" />
    </div>
    <div class="delete-button">
      <span onclick="closeSizeChartPopup()">x</span>
    </div>
  </div>
</div>
{% comment %} ----------Photo Guide Chart-----------  {% endcomment %}
{% for step in metaValues.steps %}
  {% if step.identifier == "image_uploader" %}
    <div
      style="display: none;"
      class="popup-photo-guide-container"
      id="pajamasSizeChartOpen">
      <div class="modal-overlay" onclick="closePhotoGuidePopup()"></div>
      <div class="modal-content">
        <h5 class="chart_heading">{{ step.photo_guide_details.label }}</h5>
        {% for note in step.photo_guide_details.guide_notes %}
          <p class="guideNote">{{ note.text }}</p>
        {% endfor %}
        <ul class="guideList">
          {% for bullet in step.photo_guide_details.guide_bullets %}
            <li>{{ bullet.text }}</li>
          {% endfor %}
        </ul>
        <h5 class="chart_heading">{{ step.photo_guide_details.photo_example_label }}</h5>
        <div class="photoGuideImages">
          {% for img in step.photo_guide_details.example_images %}
            <img
              class="guideImage"
              width="160px"
              src="{{img.imageUrl}}"
              alt="">
          {% endfor %}
        </div>
        <div class="delete-button photoGuideDeleteButton">
          <span onclick="closePhotoGuidePopup()">x</span>
        </div>
      </div>
    </div>
  {% endif %}
{% endfor %}
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

<script>
  let productDetail={{product | json}};

  let productVariants = {{ product.variants | json }};

  let metaValues = {{ product.metafields.cuddleclones.pdp_customizer.value | json }};

  const slidePages = document.querySelectorAll(".slide-page");

  const progressText = document.querySelectorAll(".stepname");

  const progressCheck = document.querySelectorAll(".step .circle .check");
  
  const bullet = document.querySelectorAll(".step .circle");

  const stepsLines = document.querySelectorAll(".progressbar .progress-line");
  
  let current = 1;

  let brokenNativeVariant={};

  let formattedVariant={};
  
  let brokenVariantString="";

  let galleryImageValue={};

  let brokenValues = getBrokenValues();

  let nativeValues = getNativeValues();
  formattedVariant = { ...formattedVariant, ...nativeValues };

  if(metaValues){
    metaValues?.steps?.forEach((step)=>{
      const imageChangeCheck = step.gallery_image_change;

      if (imageChangeCheck) {
        galleryImageValue[step.step_name.toLowerCase()] = "";
      }
      if(step.gallery_image_change_on_options){
        step.swatches.forEach(swatchOption => {
          if(swatchOption.gallery_image_change){
            galleryImageValue[swatchOption.name.toLowerCase()] = "";
          }
        });
      }
    });
  }

  if(Object.keys(galleryImageValue) && Object.keys(formattedVariant)){
    updateGalleryImageValueForBrokenValues(formattedVariant);
  }

  function updateGalleryImageValueForBrokenValues(formattedVariant){
    for(const [key,value] of Object.entries(formattedVariant)){
      if(galleryImageValue.hasOwnProperty(key)){
        galleryImageValue[key] = value;
      }
    }
    handleImageSlider();
  }

  for(const [key,value] of Object.entries(galleryImageValue)){
    if(value===""){
      const getKeyHiddenInput=document.querySelector(`.${key}HiddenInput`);
      if(getKeyHiddenInput){
        galleryImageValue[key]=getKeyHiddenInput.value;
      }
    }
  }
  handleImageSlider();

  let finalVariantValue = `${Object.values(formattedVariant).join(" / ")}`;
  
  validatePrice(finalVariantValue);
  
  document.addEventListener("DOMContentLoaded", () => {
    setTimeout(()=>{
      productVariants.forEach(variant => {
        if (finalVariantValue == variant.title) {
          updatePriceContainer(variant);
        }
      });
    },5000);
  });

  var itemsArray=[];

  var shopifyCartUrl = '{{shop.metafields.cuddleclones.store_url}}/cart/add.js';

  let extraOptions=document.querySelectorAll(".extraOption");

  function updateSlidePosition() {
    slidePages.forEach((page, index) => {
      if (index === (current-1)) {
        page.classList.add('active');
      } else {
        page.classList.remove('active');
      }
    });
  }

   //get default values of broken options
  function getBrokenValues() {
    let hiddenInputs = document.querySelectorAll('.defaultCheckbox');

    hiddenInputs.forEach(input => {
      let className = Array.from(input.classList).find(c => c.endsWith('HiddenInput'));
      if (className) {
        let key = className.replace('HiddenInput', '');
        brokenNativeVariant[key] = input.value;
      }
    });
    brokenVariantString = Object.values(brokenNativeVariant).join(" ");
    return brokenNativeVariant;
  }

  //get default values of native options
  function getNativeValues() {
    let hiddenInputs = document.querySelectorAll('.nativeHiddenInputs');
    hiddenInputs.forEach(input => {
      let className = Array.from(input.classList).find(c => c.endsWith('HiddenInput'));

      if (className) {
        let key = className.replace('HiddenInput', '');
        formattedVariant[key] = formattedVariant[key] 
        ? formattedVariant[key] + " / " + input.value 
        : input.value;

      }
    });

    for (let key in formattedVariant) {
      if (formattedVariant[key] === "") {
        formattedVariant[key] = brokenVariantString;
      }
    }
    return formattedVariant;
  }

  function selectSwatch(name,optionLabel,value,currentImage,extraCharges=0,createUploaders,numberOfUploaders,swatchIndex,isNative){
    let swatchHiddenInput;
    if(optionLabel===""){
      swatchHiddenInput=document.querySelector(`.${name}HiddenInput_${swatchIndex}`);
    }else{
      swatchHiddenInput=document.querySelector(`.${optionLabel}HiddenInput`);
    }
    swatchHiddenInput.value="";
    swatchHiddenInput.value=value;
    const swatchImages=document.querySelectorAll(`._${name}SwatchImages_${swatchIndex}`);
    swatchImages.forEach(img=>{
      img.style.border="none";
    });
    currentImage.style.border="4px solid #5461C9";
    if(createUploaders){
      handleCreateMultiUploader(numberOfUploaders,name);
    }
    if(isNative){
      if(optionLabel===""){
        updateVariantTitle(name,value);
      }else{
        updateVariantTitle(optionLabel,value);
      }

      const hiddenInputs=document.querySelectorAll(`.${name}HiddenInput`);
      if(hiddenInputs.length>0){
        formattedVariant[name]="";
        hiddenInputs.forEach(input=>{
          formattedVariant[name]=formattedVariant[name]
          ? formattedVariant[name] + " / " + input.value 
          : input.value;
        });
      }
      
      //finalVariantValue=Object.values(formattedVariant).join(" / ");
    }
    validatePrice(finalVariantValue);
    if(optionLabel===""){
      handleGalleryImageSlider(name,value);
    }else{
      handleGalleryImageSlider(optionLabel,value);
    }
  }

  function handleGalleryImageSlider(key,value){
    if(galleryImageValue.hasOwnProperty(key)){
      galleryImageValue[key]=value;
    }
    handleImageSlider();
  }
    
  localStorage.setItem("lastOptionSelected",0);

  function handleImageUploaderLabels(){
    let allUploaders=document.querySelectorAll(".uploaderSection");

    if(allUploaders.length==1){
      let firstUploaderLabel=document.querySelector(".uploaderSection_1 label");
      if(firstUploaderLabel){
        firstUploaderLabel.style.display="flex";
        firstUploaderLabel.textContent="Upload Image:";
      }
    }else if(allUploaders.length>1){
      let firstUploaderLabel=document.querySelector(".uploaderSection_1 label");
      if(firstUploaderLabel){
        document.querySelector(".uploaderSection_1 label").style.display="flex";
      }
    }
  }
  
  function handleCreateMultiUploader(numberOfUploaders,stepname){
    let numberOfElements=numberOfUploaders;
    let lastSelectedOption=parseInt(localStorage.getItem('lastOptionSelected'));
    const imageUploaderContainer=document.querySelector(".multi-image-uploader");
    if(lastSelectedOption==0){
      imageUploaderContainer.innerHTML="";
    }

    if(numberOfElements>lastSelectedOption){
      let index;
      if(lastSelectedOption==0){
        index=1;
      }else{
        index=lastSelectedOption+1;
      }
      for (index; index <= numberOfElements; index++) {
        createImagesUploaders(index,stepname);
      }
      let imageUploaderContainer=document.querySelector(".multi-image-uploader");
      let uploadersButtonName=imageUploaderContainer.getAttribute("data-value");
      let lastSelectedOptionValue=parseInt(localStorage.getItem('lastOptionSelected'));
      if(numberOfUploaders>lastSelectedOptionValue){
        showHideNextButton(uploadersButtonName,false);
      }
      handleImageUploaderLabels();
    }else if(numberOfElements<lastSelectedOption){
      var imageSelector = document.querySelectorAll('.uploaderSection');
      imageSelector.forEach(function (input, arrayIndex) {
        if (arrayIndex >= numberOfElements && arrayIndex < lastSelectedOption) {
          input.remove();
        }
      });
      handleImageUploaderLabels();
    }
    localStorage.setItem('lastOptionSelected', numberOfElements);
  }

  function getOrdinalSuffix(number) {
    if (number % 100 >= 11 && number % 100 <= 13) {
      return "th";
    }
    switch (number % 10) {
      case 1:
        return "st";
      case 2:
        return "nd";
      case 3:
        return "rd";
      default:
        return "th";
    }
  }

  function createImagesUploaders(i,stepname){
    let imageUploaderContainer=document.querySelector(".multi-image-uploader");
    stepname=imageUploaderContainer.getAttribute("data-value");
    const uploaderSection=document.createElement("div");
    uploaderSection.classList.add(`uploaderSection_${i}`,"uploaderSection");

    let ordinalSuffix = getOrdinalSuffix(i);
    const imageUploaderDiv=document.createElement("div");
    imageUploaderDiv.classList.add("image-uploader");
    imageUploaderDiv.setAttribute("id","image-uploader-"+i);
    imageUploaderDiv.style.marginBottom = "30px";

    let closeButton=document.createElement("button");
    closeButton.classList.add("close-button-"+i,"close-button-simple");
    closeButton.textContent="✖";

    let uploaderLabel=document.createElement("label");
    uploaderLabel.style.fontWeight = "600";
    uploaderLabel.style.display = "flex";
    uploaderLabel.textContent = `Upload ${i}${ordinalSuffix} Pet Image:`;
    

    let selectedImageContainer=document.createElement("div");
    selectedImageContainer.classList.add("selected-image-"+i,"selected-image-container-simple");

    let hiddenInput = document.createElement("input");
    hiddenInput.setAttribute("type", "hidden");
    hiddenInput.setAttribute("name", `properties[_image_url_${i}]`);
    hiddenInput.setAttribute("value","");
    hiddenInput.setAttribute("id", `hiddenInputSimple-${i} hiddenInputImage_${i}`);
    hiddenInput.setAttribute("class", `hiddenInputSimple-${i} hiddenInputImage`);

    let uploadOuterDiv=document.createElement("div");
    uploadOuterDiv.classList.add("uploadOuter_"+i,"uploadOuter");

    let dragBoxSpan=document.createElement("span");
    dragBoxSpan.classList.add("dragBoxUnique");

    let dragBoxText=document.createElement("span");
    dragBoxText.classList.add("dragBoxText");
    dragBoxText.textContent="Drag + Drop";

    let fileInput = document.createElement("input");
    fileInput.classList.add(`image_uploader_${i}`, `fileInput`);
    fileInput.setAttribute("onclick", "this.value=null");
    fileInput.setAttribute("type", "file");
    fileInput.setAttribute("id", "uploadFile-" + i);
    fileInput.setAttribute("onchange", `portraitDragNdrop(event, 'image-uploader-${i}', ${i}, '${stepname}')`);
    fileInput.setAttribute("ondragover", "drag(event)");
    fileInput.setAttribute("ondrop", "drop(event)");

    let orTextSpan=document.createElement("span");
    orTextSpan.classList.add("orText");
      
    let orTextContent=document.createElement("strong");
    orTextContent.textContent="or";

    let uploadFileLabel=document.createElement("label");
    uploadFileLabel.classList.add("btn", "btn-primary","selectFileButton");
    uploadFileLabel.setAttribute("for","uploadFile");

    orTextSpan.appendChild(orTextContent);

    uploadFileLabel.textContent="Select File";

    dragBoxSpan.appendChild(dragBoxText);
    dragBoxSpan.appendChild(fileInput);
    dragBoxSpan.appendChild(orTextSpan);
    dragBoxSpan.appendChild(uploadFileLabel);

    uploadOuterDiv.appendChild(dragBoxSpan);

    imageUploaderDiv.appendChild(closeButton);
    imageUploaderDiv.appendChild(hiddenInput);
    imageUploaderDiv.appendChild(selectedImageContainer);
    imageUploaderDiv.appendChild(uploadOuterDiv);
      
    imageUploaderDiv.style.cssText="margin:10px 0px;";
    
    uploaderSection.appendChild(uploaderLabel);
    
    uploaderSection.appendChild(imageUploaderDiv);

    //create front engraving and back engraving box
    let frontEngravingContainer;
    let backEngravingContainer;
    metaValues?.steps?.forEach(step=>{
      if(step.identifier && step.identifier==="image_uploader"){
        
      if(step.front_engraving && step.front_engraving.display){
          frontEngravingContainer = createFrontEngravingContainer(step.front_engraving,i);
        }
        if(step.back_engraving && step.back_engraving.display){
          backEngravingContainer = createBackEngravingContainer(step.back_engraving,i,stepname);
        }
      }
    });

    if(frontEngravingContainer){
      uploaderSection.appendChild(frontEngravingContainer);
      uploaderSection.style.marginBottom="30px";
    }
    if(backEngravingContainer){
      uploaderSection.appendChild(backEngravingContainer);
      uploaderSection.style.marginBottom="30px";
    }
    imageUploaderContainer.appendChild(uploaderSection);

  }

  function createFrontEngravingContainer(frontEngravings,index){
    if (frontEngravings) {
      const container = document.createElement('div');
      container.classList.add('frontEngravingsContainer');

      const engravingLabel = document.createElement('p');
      engravingLabel.classList.add('frontEngraving');
      engravingLabel.innerHTML = `${frontEngravings.label}`;

      // Add optional text if needed
      if (frontEngravings.optional) {
        const optionalText = document.createElement('span');
        optionalText.classList.add('optionalText');
        optionalText.innerHTML = '&nbsp;&nbsp;&nbsp;&nbsp;(Optional)';
        engravingLabel.appendChild(optionalText);
      }

      engravingLabel.innerHTML += ':';

      // Engraving Note
      const engravingNote = document.createElement('p');
      engravingNote.classList.add('frontEngravingNote');
      engravingNote.innerHTML = `Note: ${frontEngravings.note}`;

      // Textarea input
      const textarea = document.createElement('textarea');
      textarea.classList.add('input', 'frontEngravingInput');
      textarea.maxLength = 12;
      textarea.type = 'text';
      textarea.name = 'properties[Front Engraving '+index+']';
      textarea.id = 'frontEngravingInput';
      textarea.placeholder = frontEngravings.input_field_placeholder;
      textarea.style.border = '1px solid';
      textarea.style.borderRadius = '10px';

      // Append elements to the container
      container.appendChild(engravingLabel);
      container.appendChild(engravingNote);
      container.appendChild(textarea);

      return container;
    }
  }

  function createBackEngravingContainer(backEngravings,index,stepName){
    if (backEngravings) {
      const mainContainer = document.createElement('div');
      mainContainer.classList.add('backEngravingsMainContainer');

      const engravingsIdInput = document.createElement('input');
      engravingsIdInput.classList.add('backEngravingsIDInput');
      engravingsIdInput.type = 'hidden';
      engravingsIdInput.value = backEngravings.id;

      // Checkbox container
      const checkboxContainer = document.createElement('div');
      checkboxContainer.classList.add('optionCheckboxContainer');

      const checkboxInput = document.createElement('input');
      checkboxInput.type = 'checkbox';
      checkboxInput.classList.add('backEngravingCheckbox', `backEngravingCheckbox_${index}`);
      checkboxInput.id = 'backEngravingCheckbox';
      checkboxInput.setAttribute("onchange",`handleBackEngraving(this,${index},'${stepName.toLowerCase().replace(' ', '')}',${backEngravings.extra_charges})`);
      checkboxInput.name = `properties[Back Engravings ${index}]`;
      checkboxInput.value = 'No';

      checkboxContainer.appendChild(checkboxInput);
      checkboxContainer.appendChild(engravingsIdInput);
      mainContainer.appendChild(checkboxContainer);

      // Option detail container
      const detailMainContainer = document.createElement('div');
      detailMainContainer.classList.add('optionDetailMainContainer');

      const optionDetailContainer = document.createElement('div');
      optionDetailContainer.classList.add('optionDetailContainer');

      const optionLabel = document.createElement('div');
      optionLabel.classList.add('optionLabel');
      optionLabel.innerText = backEngravings.label;

      const optionDescription = document.createElement('div');
      optionDescription.classList.add('optionDescription');
      optionDescription.innerText = backEngravings.descritpion;

      optionDetailContainer.appendChild(optionLabel);
      optionDetailContainer.appendChild(optionDescription);
      detailMainContainer.appendChild(optionDetailContainer);
      mainContainer.appendChild(detailMainContainer);

      // Option price
      const optionPrice = document.createElement('div');
      optionPrice.classList.add('optionPrice');
      optionPrice.innerText = `+$${backEngravings.extra_charges}`;
      mainContainer.appendChild(optionPrice);

      // Input container for desktop
      const inputContainerDesktop = document.createElement('div');
      inputContainerDesktop.classList.add('backEngravingsInputContainer');

      const textareaDesktop = document.createElement('textarea');
      textareaDesktop.maxLength = 32;
      textareaDesktop.value = "";
      textareaDesktop.disabled = true;
      textareaDesktop.classList.add('input', 'backEngravingsInput',`backEngravingsInput_${index}`);
      textareaDesktop.name = `properties[Back Engraving text ${index}]`;
      textareaDesktop.id = 'backEngravingInput'; // Note: This ID should be unique if used in both containers
      textareaDesktop.placeholder = backEngravings.input_field_placeholder;
      textareaDesktop.setAttribute("oninput",`handleBackEngravingInput(this,${index},'${stepName.toLowerCase().replace(' ','')}')`);

      inputContainerDesktop.appendChild(textareaDesktop);

      // Append the main container to the body or a specific element
      //mainContainer.appendChild(engravingsContainer);
      mainContainer.appendChild(inputContainerDesktop);
      return mainContainer;
    }
  }
    
  function selectSubOptionSwatch(stepname,optionName,name,value,currentImage,extraCharges=0){
    const swatchSubOptionHiddenInput=document.querySelector(`.${optionName}${name}HiddenInput`);
    swatchSubOptionHiddenInput.value=value;
    const swatchImages=document.querySelectorAll(`._${optionName}SwatchImages`);
    swatchImages.forEach(img=>{
      img.style.border="none"
    });
    currentImage.style.border="4px solid #5461C9";
    updateBrokenVariantTitle(stepname,name,optionName,value);
  }

  function handleSizeChartPopup() {
    // Get the popup chart container
    const popupContainer = document.querySelector('.popup-chart-container');
    // Toggle the visibility of the popup container
    if (popupContainer.style.display === 'none') {
      popupContainer.style.display = 'block';
    } else {
      popupContainer.style.display = 'none';
    }
  }
  function openPopup() {
    const popupContainer = document.querySelector('.popup-chart-container');
    popupContainer.style.display = 'flex';
  }

  function closeSizeChartPopup() {
    const popupContainer = document.querySelector('.popup-chart-container');
    popupContainer.style.display = 'none';
  }


  function handlePhotoGuidePopup() {
    // Get the popup chart container
    const popupContainer = document.querySelector('.popup-photo-guide-container');
    // Toggle the visibility of the popup container
    if (popupContainer.style.display === 'none') {
      popupContainer.style.display = 'block';
    } else {
      popupContainer.style.display = 'none';
    }
  }
  function openPopup() {
    const popupContainer = document.querySelector('.popup-photo-guide-container');
    popupContainer.style.display = 'flex';
  }

  function closePhotoGuidePopup() {
    const popupContainer = document.querySelector('.popup-photo-guide-container');
    popupContainer.style.display = 'none';
  }

  let firstDivImage=null;
  const checkboxInputValues = document.querySelectorAll(".toggleCheckboxes");
  let togglesValue = Array.from(checkboxInputValues)
      .map(inputValue => inputValue.value.replace(/\s+/g, '_').toLowerCase())
      .join('_');
  const selectiveSwatchesToHide = document.querySelectorAll(`.swatchDiv`);

  selectiveSwatchesToHide.forEach((swatch,index) => {
    const hasShowWithClass = Array.from(swatch.classList).some(className => className.startsWith('show_with'));

    if (hasShowWithClass) {
      if (swatch.classList.contains(`show_with_${togglesValue}`)) {
        swatch.style.display = 'block';
        if (!firstDivImage) {
          firstDivImage = swatch;
        }
      } else {
        swatch.style.display = 'none';
      }
    }
  });

  function handleToggleSwitch(toggleSwitch,name,firstValue,secondValue,brokenVariantName,stepname,swatchesWithFirst,swatchesWithSecond){
    const hiddenInput = document.querySelector(`.${brokenVariantName}ToggleHiddenInputs`);

    const firstCase=document.querySelector(`.${firstValue.toLowerCase().replace(/ /g,"")}FirstCase`);
    
    const secondCase=document.querySelector(`.${secondValue.toLowerCase().replace(/ /g,"")}SecondCase`);
    
    if (toggleSwitch.checked) {
      hiddenInput.value = secondValue;

      brokenNativeVariant[brokenVariantName]=secondValue;

      const checkboxInputValues = document.querySelectorAll(".toggleCheckboxes");

      let togglesValue = Array.from(checkboxInputValues)
        .map(inputValue => inputValue.value.replace(/\s+/g, '_').toLowerCase())
        .join('_');  

      let firstDivImage=null;
      if(firstCase && secondCase){
        firstCase.style.display="none";
        secondCase.style.display="grid";      

        const selectiveSwatchesToHide = document.querySelectorAll(`.swatchDiv`);

        selectiveSwatchesToHide.forEach((swatch,index) => {
          const hasShowWithClass = Array.from(swatch.classList).some(className => className.startsWith('show_with'));

          if (hasShowWithClass) {
            if (swatch.classList.contains(`show_with_${togglesValue}`)) {
              swatch.style.display = 'block';
              if (!firstDivImage) {
                firstDivImage = swatch;
              }
            } else {
              swatch.style.display = 'none';
            }
          }else{
            if (swatch.classList.contains(`simpleShowOption`)) {
              swatch.style.display = 'block';
              if (!firstDivImage) {
                firstDivImage = swatch;
              }
            }
          }
        });

        if(firstDivImage){
          const swatch_image=firstDivImage.querySelector("img");
          $(swatch_image).trigger('click');
        }

      }
      
      if(!firstCase && !secondCase && swatchesWithSecond===togglesValue){
        const selectiveSwatchesToHide = document.querySelectorAll(`.swatchDiv`);

        let firstVisibleSwatch = null;
        selectiveSwatchesToHide.forEach((swatch,index) => {
          const hasShowWithClass = Array.from(swatch.classList).some(className => className.startsWith('show_with'));

          if (hasShowWithClass) {
            if (swatch.classList.contains(`show_with_${swatchesWithSecond}`)) {
              swatch.style.display = 'block';
              if (!firstVisibleSwatch) {
                firstVisibleSwatch = swatch;
              }
            } else {
              swatch.style.display = 'none';
            }
          }
        });
        
        const swatch_image=firstVisibleSwatch.querySelector("img");
        $(swatch_image).trigger('click');
      }

      brokenValues=getBrokenValues();
      formattedVariant[stepname]=brokenVariantString;
      updateGalleryImageValueForBrokenValues(formattedVariant);
      let finalVariantValue=Object.values(formattedVariant).join(" / ");
      validatePrice(finalVariantValue);
    } else {
      hiddenInput.value = firstValue;

      brokenNativeVariant[brokenVariantName]=firstValue;

      if(firstCase && secondCase){
        firstCase.style.display="grid";
        secondCase.style.display="none";

        if(swatchesWithFirst && swatchesWithSecond){        
          const selectiveSwatchesToDisplay = secondCase.querySelectorAll(`.swatchDiv:not(.show_with_${swatchesWithSecond})`);
          
          selectiveSwatchesToDisplay.forEach((swatch,index)=>{
            swatch.style.display = 'block';
            if (index === 0) {
              const swatchImage = swatch.querySelector("img");

              if (swatchImage) {
                $(swatchImage).trigger('click');
              }
            }
          });
            
          const selectiveSwatchesToHide = secondCase.querySelectorAll(`.swatchDiv:not(.show_with_${swatchesWithSecond})`);
          
          selectiveSwatchesToHide.forEach(swatch=>{
            swatch.style.display = 'none';
          });
        }
      }

      const checkboxInputValues = document.querySelectorAll(".toggleCheckboxes");

      let togglesValue = Array.from(checkboxInputValues)
        .map(inputValue => inputValue.value.replace(/\s+/g, '_').toLowerCase())
        .join('_');

      if(!firstCase && !secondCase && swatchesWithFirst===togglesValue){
        const selectiveSwatchesToHide = document.querySelectorAll(`.swatchDiv`);

        let firstVisibleSwatch = null;
        selectiveSwatchesToHide.forEach((swatch,index) => {
          const hasShowWithClass = Array.from(swatch.classList).some(className => className.startsWith('show_with'));

          if (hasShowWithClass) {
            if (swatch.classList.contains(`show_with_${swatchesWithFirst}`)) {
              swatch.style.display = 'block';
              if (!firstVisibleSwatch) {
                firstVisibleSwatch = swatch;
              }
            } else {
              swatch.style.display = 'none';
            }
          }
        });
        const swatchImage=firstVisibleSwatch.querySelector("img");
        $(swatchImage).trigger('click');
      }else{
        const swatchedDivName=firstValue.toLowerCase().replace(/\s/g,"");
        const attachedSwatchesContainer=document.querySelector(`.${swatchedDivName}FirstCase`);

        if(attachedSwatchesContainer){
          const attachedSwatches=attachedSwatchesContainer.querySelectorAll(".swatchDiv");
          let firstDivImage=null;
          attachedSwatches.forEach((swatch,index) => {
            if (swatch.classList.contains(`show_with_${togglesValue}`)) {
              swatch.style.display = 'block';
              if (!firstDivImage) {
                firstDivImage = swatch;
              }
            } else {
              swatch.style.display = 'none';
            }
          });
          if(firstDivImage){
            const swatch_image=firstDivImage.querySelector("img");
            $(swatch_image).trigger('click');
          }
        }
      }
      
      brokenValues=getBrokenValues();
      formattedVariant[stepname]=brokenVariantString;
      updateGalleryImageValueForBrokenValues(formattedVariant);
      let finalVariantValue=Object.values(formattedVariant).join(" / ");
      validatePrice(finalVariantValue);
    }
  }

  function updatePriceContainer(variant){
    var priceDiv=document.querySelector(".originalPrice");
    let price;
    let formattedPrice;
    if (priceDiv) {
      price = variant.price / 100; 
      formattedPrice = `$${price.toFixed(2)}`; 
      priceDiv.textContent = formattedPrice ;
    }
    
    const priceUIContainer=document.querySelector(".price-ui");
    const compareAtPriceDiv=priceUIContainer.querySelector(".sizeChartBox .compare-at-price");
    let compareAtPrice;
    let formattedComparePrice;
    if(compareAtPriceDiv){
      compareAtPrice = variant.compare_at_price / 100;
      if(compareAtPrice>0){
        formattedComparePrice = `$${compareAtPrice.toFixed(2)}`;
        compareAtPriceDiv.innerHTML="";
        compareAtPriceDiv.textContent=' '+formattedComparePrice;
      }
    }

    var pricePercentageContainer=document.querySelector(".pricePercentage");
    pricePercentageContainer.textContent="";
    if(pricePercentageContainer && compareAtPrice>0){
      const content=document.createElement("p");
      var amountSaved = compareAtPrice - price;
      var discountPercentage = Math.floor((amountSaved / compareAtPrice) * 100);
      content.textContent=`You save: ${discountPercentage}% ($${amountSaved.toFixed(2)})`;
      const defaultPricePercentage=document.querySelector(".pricePercentageDefault");
      if(defaultPricePercentage){
        defaultPricePercentage.style.display="none";
      }
      pricePercentageContainer.appendChild(content);
    }
  }

  async function portraitDragNdrop(event, containerId, number,buttonName,numberImages){
    let hiddenInput = document.querySelector(`.hiddenInputSimple-${number}`);
    
    var files = event.target.files;

    if (files.length > 1) {
      alert("Please select only one image file.");
      return;
    }

    var file = files[0];

    var fileName = URL.createObjectURL(files[0]);

    if (!file.type.startsWith("image/")) {
      alert("Please select only image files.");
      return;
    }

    if (file.type !== "image/jpg" && file.type !== "image/png" && file.type !== "image/bmp" && file.type !== "image/jpeg") {
      alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
      return;
    }

    if (files[0].size > 15 * 1024 * 1024) {
      // Convert MB to bytes
      alert("Please select images smaller than 15MB.");
      return;
    }

    let uploadedImageName;
    var previewImg = document.createElement("img");
    previewImg.src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif";


    let imageContainer=document.querySelector(`.selected-image-${number}`);
    document.querySelector(`.close-button-${number}`).style.display="block";
    document.querySelector(`#image-uploader-${number}`).style.padding="20px";

    previewImg.setAttribute("alt", "Image ");
    previewImg.setAttribute("id", "previewImageSimple");
    imageContainer.appendChild(previewImg);

    document.querySelector(".uploadOuter_"+number).style.display = "none"; 

    const formData = new FormData();
    formData.append('image', files[0]); 

      try {
        const response = await fetch(`${api_details[0].url}/image_uploader/print_image`, {
          method: "POST",
          body: formData,
          headers: {
            ...headerForImage,
          },
        })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          previewImg.src=data.imageUrl;
          hiddenInput.value = data.imageUrl;
          
          const containerWidth = 250;
          const containerHeight = 250;
          const imageAspectRatio = previewImg.naturalWidth / previewImg.naturalHeight;

          let imageWidth, imageHeight;
          if (imageAspectRatio >= 1) {
            // Landscape or square image
            imageWidth = containerWidth;
            imageHeight = containerWidth / imageAspectRatio;
          } else {
            // Portrait image
            imageHeight = containerHeight;
            imageWidth = containerHeight * imageAspectRatio;
          }

          // Set image dimensions and quality
          previewImg.style.width = `${imageWidth}px`;
          previewImg.style.height = `${imageHeight}px`;
          previewImg.style.objectFit = "contain"; 

          const imageUrl = data.imageUrl;
          const parts = imageUrl.split('/');
          uploadedImageName = parts[parts?.length - 1];
        })
        .catch((error) => {
          console.error("Error uploading new image:", error);
        });           
      } catch (error) {
          console.log('Error:', error);
      }

      document.querySelector(`.close-button-${number}`).addEventListener("click",function(event){
        event.preventDefault();
        if (uploadedImageName) {
          imageContainer.innerHTML="";
          document.querySelector(".uploadOuter_"+number).style.display = "block";
          document.querySelector(`.close-button-${number}`).style.display="none";
          document.querySelector(".hiddenInputSimple-"+number).value="";
          document.getElementById(`image-uploader-${number}`).style.padding="0px";
          const cartButton = document.querySelector(`.${buttonName}NextButton`);
          cartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
          cartButton.setAttribute("disabled", "true");
          }
      });

      let isAllImagesUploaded=validateAllUploaders(numberImages);
      let isCheckboxesCheckedWithInputFieldsFilled=validateBackEngravingsCheckboxesWithInputs();

      if(isAllImagesUploaded && isCheckboxesCheckedWithInputFieldsFilled){
        showHideNextButton(buttonName,true);
      }else{
        showHideNextButton(buttonName,false);
      }
    }

    function handleBackEngraving(checkbox,index,buttonName,price){
      const backEngravingInput=document.querySelector(`.backEngravingsInput_${index}`);
      if(checkbox.checked){
        checkbox.value="Yes";
        backEngravingInput.disabled=false;
      }else{
        checkbox.value="No";
        backEngravingInput.disabled=true;
        backEngravingInput.value="";
      }

      const uploadNextBtn=document.querySelector(`.${buttonName}NextButton`);
      let isCheckboxesCheckedWithInputFieldsFilled=validateBackEngravingsCheckboxesWithInputs();

      let isAllImagesUploaded=validateAllUploaders();

      if(isCheckboxesCheckedWithInputFieldsFilled && isAllImagesUploaded){
        showHideNextButton(buttonName,true);
      }else{
        showHideNextButton(buttonName,false);
      }
      handleExtraOptionsPrice(checkbox,price);
    }

    function handleBackEngravingInput(input,index,buttonName){      
      let isCheckboxesCheckedWithInputFieldsFilled=validateBackEngravingsCheckboxesWithInputs();

      let isAllImagesUploaded=validateAllUploaders();

      if(isCheckboxesCheckedWithInputFieldsFilled  && isAllImagesUploaded){
        showHideNextButton(buttonName,true);
      }else{
        showHideNextButton(buttonName,false);
      }
    }

    function showHideNextButton(buttonName,isShow){
      const uploadNextBtn=document.querySelector(`.${buttonName}NextButton`);
      if(isShow){
        uploadNextBtn.style.cssText="cursor:pointer;opacity:1;pointer-events:auto";
        uploadNextBtn.removeAttribute("disabled");
      }else{
        uploadNextBtn.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
        uploadNextBtn.setAttribute("disabled", "true");
      }
    }

    function validateAllUploaders(numberImages){
      const allUploadersHiddenInputs = document.querySelectorAll(".hiddenInputImage");
      if(allUploadersHiddenInputs){
        const hasEmptyInput = Array.from(allUploadersHiddenInputs).some(input => input.value === "");
        if (hasEmptyInput) {
          return false;
        }
      }
      return true;
    }

    function validateBackEngravingsCheckboxesWithInputs(numberImages){
      const backEngravingsCheckboxes = document.querySelectorAll('.backEngravingCheckbox');

      let isValid = true;

      backEngravingsCheckboxes.forEach((checkbox, i) => {
        if(checkbox.checked){
          const associatedInput = document.querySelector(`.backEngravingsInput_${i+1}`);

          if (!associatedInput || associatedInput.value.trim().length === 0) {
            isValid = false;
          }
        }
      });
      return isValid;
    }

    function validatePrice(finalValue){
      finalVariantValue=finalValue.trim();
      productVariants.forEach(variant => {
        if (finalVariantValue === variant.title) {
          let priceDiv=document.querySelectorAll(`.subTotalPrice`);
          priceDiv.forEach(div=>{
            div.innerHTML = (variant.price / 100).toFixed(2);
          });
          const thumbnailImages=document.querySelectorAll(".product-gallery__thumbnail");
          if(thumbnailImages){
            thumbnailImages.forEach(image => {
              const imageTitle = $(image).attr('data-title'); 

              if (imageTitle === finalVariantValue) {
                $(image).trigger('click');
                $(thumbnailImages).removeClass('is-nav-selected');
                $(image).addClass('is-nav-selected'); 
              }
            });
          }

          updatePriceContainer(variant);
        }
      });
    }

    

    function updateVariantTitle(stepName,value){
      formattedVariant[stepName] = value;

      let finalVariantValue=Object.values(formattedVariant).join(" / ");
      validatePrice(finalVariantValue);
    }

    function updateBrokenVariantTitle(stepname,name,optionName,value){
      const keyName=optionName+name;
      brokenNativeVariant[keyName] = value;
      brokenValues = getBrokenValues();
      formattedVariant[stepname]=brokenVariantString;
      let finalVariantValue=Object.values(formattedVariant).join(" / ");
      validatePrice(finalVariantValue);
      updateGalleryImageValueForBrokenValues(formattedVariant);
    }

    function addEventListeners(stepName, index) {
      const nextBtn = document.querySelector(`.${stepName.toLowerCase().replace(/ /g, "")}NextButton`);
      
      const prevBtn = document.querySelector(`.${stepName.toLowerCase().replace(/ /g, "")}PrevButton`);
      
      let stepNameLower = stepName.toLowerCase().replace(/ /g, "");

      let brokenNativeVariantString = Object.values(brokenNativeVariant).join(" ");

      const multiStepProgressContainer=document.querySelector(".multiStepProgressContainer");
  
      if (nextBtn) {
        nextBtn.addEventListener("click", async function(event) {
          event.preventDefault();

          let extraOptions = document.querySelectorAll(".extraOption");

          let getHiddenInput = document.querySelector(`.${stepName.toLowerCase().replace(/ /g, "")}HiddenInput`);
          let getHiddenInputValue;
          let isNativeElement;
          if (getHiddenInput && isNativeElement) {
            isNativeElement=Array.from(getHiddenInput?.classList).find(c => c.includes("nativeHiddenInputs")) || false;
            if(isNativeElement){
              getHiddenInputValue = getHiddenInput.value;
              getHiddenInputValue.replace(/"/g, '\\"');
            }
          }
                  
          if (getHiddenInputValue) {
            formattedVariant[stepName.toLowerCase().replace(/ /g, "")] = getHiddenInputValue;
          }
          
          if (current < slidePages.length) {
            current += 1;
            updateSlidePosition();
            bullet[current - 2].classList.add("active");
            stepsLines[current - 2].classList.add("active");
            progressCheck[current - 2].classList.add("active");
            progressText[current - 2].classList.add("active");
            bullet[current - 1].parentElement.classList.add("active");
            multiStepProgressContainer.scrollIntoView({ behavior: 'smooth' });
          } else {
            productVariants.forEach(variant => {

              if (finalVariantValue == variant.title) {
                let variantIdInput = document.querySelector("#variantid");
      
                if (variantIdInput) {
                  variantIdInput.value = variant.id;
                }

                const thumbnailImages=document.querySelectorAll(".product-gallery__thumbnail");
                if(thumbnailImages){
                  thumbnailImages.forEach(image => {
                    const imageTitle = $(image).attr('data-title'); 

                    if (imageTitle === finalVariantValue) {
                      $(image).trigger('click');
                      $(thumbnailImages).removeClass('is-nav-selected');
                      $(image).addClass('is-nav-selected'); 
                    }
                  });
                }

                updatePriceContainer(variant);

                const finalvalues=finalVariantValue.split(" / ");
                
                finalvalues.forEach((value, index) => {
                  const hiddenInput = document.createElement("input");
                  hiddenInput.type = "hidden";
                  hiddenInput.setAttribute("name", `properties[_option_${productDetail.options[index]}]`);
                  hiddenInput.setAttribute("value", value);

                  document.querySelector("#product_form_{{product.id}}").appendChild(hiddenInput);
                });

                const customHiddenInputs=document.querySelectorAll(".customHiddenInputs");

                if(customHiddenInputs){
                  customHiddenInputs.forEach((item, index) => {
                    const hiddenInput = document.createElement("input");
                    hiddenInput.type = "hidden";
                    hiddenInput.setAttribute("name", `properties[${item.getAttribute("data-name")}]`);
                    hiddenInput.setAttribute("value", item.value);
                    document.querySelector("#product_form_{{product.id}}").appendChild(hiddenInput);
                  });
                }
              }
            });
            
            const uniqueKey=document.querySelector("#relatedProduct").value;
            if (extraOptions) {
              extraOptions.forEach((option,index) => {
                if (option.checked) {
                  var item = {
                    quantity: 1,
                    id: option.value,
                    properties:{
                      "_unique_key":uniqueKey
                    }
                  };
                  itemsArray.push(item);
                }
              });
            }

            const backEngravingsProducts=document.querySelectorAll(".backEngravingCheckbox");

            const backEngravingHiddenInput = document.querySelector(".backEngravingsIDInput");

            if(backEngravingsProducts) {
              backEngravingsProducts.forEach((option,index) => {
                if (option.checked) {
                  if(backEngravingHiddenInput){
                    const backEngravingId = backEngravingHiddenInput.value;
                    var item = {
                      quantity: 1,
                      id: backEngravingId,
                      properties:{
                        "_unique_key":uniqueKey
                      }
                    };
                    itemsArray.push(item);
                  }
                }
              });
            }
            
            async function addToCart(itemsArray, shopifyCartUrl) {

              var formData =$("#product_form_{{product.id}}").serializeArray();

              let mainItemId=null;

              const primaryProduct = formData.reduce((acc, { name, value }) => {
                const match = name.match(/^properties\[(.+?)\]$/);

                if (match) {
                  const extractedName = match[1];

                  if (acc[extractedName]) {
                    if (Array.isArray(acc[extractedName])) {
                      acc[extractedName].push(value);
                    } else {
                      acc[extractedName] = [acc[extractedName], value];
                    }
                  } else {
                    acc[extractedName] = value;
                  }
                }else if (name === 'id') {
                  mainItemId = value;
                }
                return acc;
              }, {});

              const mainItem = {
                quantity: 1,
                id: mainItemId, 
                properties: { ...primaryProduct },
              };

              itemsArray.unshift(mainItem);

              const combinedItems = itemsArray.map(item => ({
                quantity: 1, 
                id: item.id, 
                properties: item.properties || {}, 
              }));

              const combinedOptions = {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ items: combinedItems })
              };

              const response = await fetch(shopifyCartUrl, combinedOptions);

              if(response.ok){
                window.location.href = "{{ shop.url }}/cart";
              }
            }
            addToCart(itemsArray, shopifyCartUrl);
          }
        });
      }

      if (prevBtn) {
        prevBtn.addEventListener("click", function(event) {
          event.preventDefault();
          if (current > 1) {
            current -= 1;
            updateSlidePosition();
            bullet[current-1].classList.remove("active");
            stepsLines[current-1].classList.remove("active");
            progressCheck[current-1].classList.remove("active");
            progressText[current-1].classList.remove("active");
            bullet[current].parentElement.classList.remove("active");
            multiStepProgressContainer.scrollIntoView({ behavior: 'smooth' });
          }
        });
      }
  }


  metaValues.steps.forEach((step, index) => {
    addEventListeners(step.step_name, index + 1);
  });

  function handleExtraOptionsPrice(checkbox, optionPrice) {
    const variantPriceCents = parseFloat(document.querySelector(".subTotalPrice").textContent) * 100;

    let priceDivs = document.querySelectorAll(`.subTotalPrice`);

    if (checkbox.checked) {
      priceDivs.forEach(div => {
        let newPriceCents = variantPriceCents + (optionPrice*100);
        div.innerHTML = (newPriceCents / 100).toFixed(2);
      });
    } else {
      priceDivs.forEach(div => {
        let newPriceCents = variantPriceCents - (optionPrice*100);
        div.innerHTML = (newPriceCents / 100).toFixed(2);
      });
    }
  }

  function handleImageSlider(){
    let concatenatedImageName="";  
    if (Object.keys(galleryImageValue).length === 1) {
      concatenatedImageName = Object.values(galleryImageValue)[0].replace(/\s/g, '');
    } else {
      concatenatedImageName = Object.values(galleryImageValue).reduce((acc, value) => acc.concat(value.replace(/\s/g, '')), '');
    }

    const thumbnailImages=document.querySelectorAll(".product-gallery__thumbnail");
    
    if(thumbnailImages){
      thumbnailImages.forEach(image => {
        const imageTitle = $(image).attr('data-title'); 

        if (imageTitle === concatenatedImageName) {
          $(image).trigger('click'); 
          $(thumbnailImages).removeClass('is-nav-selected'); 
          $(image).addClass('is-nav-selected'); 
        }
      });
    }
  }
</script>

<script>
  document.getElementById('relatedProduct').value = Date.now();
</script>