{% comment %}
  Facebook Open Graph meta tags
{% endcomment %}

<meta name="author" content="{{ shop.name }}">
<meta property="og:url" content="{{ canonical_url }}">
<meta property="og:site_name" content="{{ shop.name }}">

{%- capture page_image -%}
  <meta property="og:image" content="https:{{ page_image | img_url: '1200x' }}">
  <meta property="og:image:secure_url" content="https:{{ page_image | img_url: '1200x' }}">
  <meta property="og:image:width" content="{{ page_image.width }}">
  <meta property="og:image:height" content="{{ page_image.height }}">
{%- endcapture -%}

{%- if template.name == 'product' -%}
  <meta property="og:type" content="product">
  <meta property="og:title" content="{{ product.title | strip_html | escape }}">
  <meta property="og:price:amount" content="{{ product.price | money_without_currency | strip_html | escape }}">
  <meta property="og:price:currency" content="{{ cart.currency.iso_code }}">
  {%- if product.featured_image.size > 0 -%}
    {%- for image in product.images limit: 3 -%}
      <meta property="og:image" content="http:{{ image.src | product_img_url: '600x' }}">
      <meta property="og:image:secure_url" content="https:{{ image.src | product_img_url: '600x' }}">
      {%- if image.alt contains 'youtube' or image.alt contains 'vimeo' -%}
        {% assign src = image.alt | split: 'src="' %}
        {% assign src = src[1] | split: '"' | first %}
        <meta property="og:video" content="{{ src }}">
      {%- endif -%}
    {%- endfor -%}
  {%- else -%}
    {{ page_image }}
  {%- endif -%}
{%- elsif template.name == 'collection' -%}
  <meta property="og:type" content="product.group">
  <meta property="og:title" content="{{ collection.title | strip_html | escape }}">
  {%- if collection.products_count > 0 -%}
    {% assign image = collection.products.first.featured_image %}
    <meta property="og:image" content="http:{{ image.src | product_img_url: '600x' }}">
    <meta property="og:image:secure_url" content="https:{{ image.src | product_img_url: '600x' }}">
  {%- else -%}
    {{ page_image }}
  {%- endif -%}
{%- elsif template.name == 'article' -%}
  <meta property="og:type" content="article">
  <meta property="og:title" content="{{ article.title | strip_html | escape }}">
  {% assign img_tag = '<' | append: 'img' %}
  {%- if article.image -%}
    <meta property="og:image" content="http:{{ article.image | img_url: '600x' }}">
    <meta property="og:image:secure_url" content="https:{{ article.image | img_url: '600x' }}">
  {%- elsif article.content contains img_tag -%}
    {% assign src = article.content | split: 'src="' %}
    {% assign src = src[1] | split: '"' | first | remove: 'https:' | remove: 'http:' %}
    {%- if src -%}
      <meta property="og:image" content="http:{{ src }}">
      <meta property="og:image:secure_url" content="https:{{ src }}">
    {%- endif -%}
  {%- else -%}
    {{ page_image }}
  {%- endif -%}
{%- elsif template.name == 'blog' -%}
  <meta property="og:type" content="article">
  <meta property="og:title" content="{{ blog.title | strip_html | escape }}">
  {%- if blog.articles_count > 0 -%}
    {% assign article = blog.articles.first %}
    {% assign img_tag = '<' | append: 'img' %}
    {%- if article.image -%}
      <meta property="og:image" content="http:{{ article.image | img_url: '600x' }}">
      <meta property="og:image:secure_url" content="https:{{ article.image | img_url: '600x' }}">
    {%- elsif article.content contains img_tag -%}
      {% assign src = article.content | split: 'src="' %}
      {% assign src = src[1] | split: '"' | first | remove: 'https:' | remove: 'http:' %}
      {%- if src -%}
        <meta property="og:image" content="http:{{ src }}">
        <meta property="og:image:secure_url" content="https:{{ src }}">
      {%- endif -%}
    {%- else -%}
      {{ page_image }}
    {%- endif -%}
  {%- endif -%}
{%- elsif template.name == 'page' -%}
  <meta property="og:type" content="article">
  <meta property="og:title" content="{{ page.title | strip_html | escape }}">
  {% assign img_tag = '<' | append: 'img' %}
  {%- if page.content contains img_tag -%}
    {% assign src = page.content | split: 'src="' %}
    {% assign src = src[1] | split: '"' | first | remove: 'https:' | remove: 'http:' %}
    {%- if src -%}
      <meta property="og:image" content="http:{{ src }}">
      <meta property="og:image:secure_url" content="https:{{ src }}">
    {%- endif -%}
  {%- else -%}
    {{ page_image }}
  {%- endif -%}
{%- else -%}
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{ page_title | escape }}">
{%- endif -%}
{%- if template.name == 'index' -%}
  {{ page_image }}
{%- endif -%}
{%- if page_description -%}
  <meta property="og:description" content="{{ page_description | escape }}">
{%- endif -%}

{% comment %}
  Twitter user name of the site, based on theme settings
{% endcomment %}
{%- if settings.x_link != blank -%}
  <meta name="twitter:site" content="{{ settings.x_link | split: 'x.com/' | last | split: 'twitter.com/' | last | prepend: '@' }}">
{%- endif -%}
<meta name="twitter:card" content="summary">
{%- if template.name == 'product' -%}
  <meta name="twitter:title" content="{{ product.title }}">
  <meta name="twitter:description" content="{{ product.description | strip_html | truncatewords: 140, '' | escape }}">
  <meta name="twitter:image" content="https:{{ product.featured_image.src | img_url: '240x' }}">
  <meta name="twitter:image:width" content="240">
  <meta name="twitter:image:height" content="240">
{%- elsif template.name == 'article' -%}
  <meta name="twitter:title" content="{{ article.title }}">
  <meta name="twitter:description" content="{{ article.excerpt_or_content | strip_html | truncatewords: 140, '' | escape }}">
  {%- if article.image -%}
    <meta property="twitter:image" content="http:{{ article.image | img_url: '600x' }}">
  {%- elsif article.content contains "<img" -%}
    {% assign src = article.content | split: 'src="' %}
    {% assign src = src[1] | split: '"' | first | replace: '//cdn', 'http://cdn' | replace: 'http:http://', 'http://' | remove: 'https:' %}
    {%- if src -%}
      <meta property="twitter:image" content="{{ src }}">
    {%- endif -%}
  {%- endif -%}
{%- endif -%}
