{% if settings.notify_me_form %}
<div class="product__notify-form notify_form notify-form-{{ product.id }} card card-content" id="notify-form-{{ product.id }}" {% if product.available %}style="display:none"{% endif %}>
  <p class="notify-form__success-message message"></p>
  {% form 'contact', id: "notify_me" %}
    {% if form.errors %}
      <p><em>{{ 'products.notify_form.post_error' | t }}</em></p>
    {% else %}
      <p>
        <label aria-hidden="true" class="visually-hidden" for="contact[email]">
          {{ 'products.notify_form.email' | t }}
        </label>

        <div class="notify-form__description content">
          {% if settings.notify_me_description %}
            {{ settings.notify_me_description }}
          {% endif %}
        </div>
        <div class="notify_form__inputs {% if customer %}customer--true{% endif %}"  data-url="{{ canonical_url }}">
          {% if product.variants.size >= 1 and product.available == false %}
            {% capture notifyEmailInput %}
              <input required type="email" class="notify_email input" name="contact[email]" id="contact[email]" placeholder="{{ 'products.notify_form.email' | t }}" value="{% if customer %}{{ customer.email }}{% else %}{{ contact.fields.email }}{% endif %}" />
            {% endcapture %}

            <input type="hidden" name="challenge" value="false" />
            <input type="hidden" name="contact[body]" class="notify_form_message" data-body="{{ 'products.notify_form.message_content' | t: product: product.title, url: canonical_url }}" value="{{ 'products.notify_form.message_content' | t: product: product.title, url: canonical_url }}" />
            <div class="field has-addons">
              <div class="control">
                {{ notifyEmailInput }}
              </div>
              <div class="control">
                <input class="action_button is-inverse button" type="submit" value="{{ 'products.notify_form.send' | t }}" style="margin-bottom:0px" />
              </div>
            </div>
          {% endif %}
        </div>
      </p>
    {% endif %}
  {% endform %}
</div>
{% endif %}
