{%- comment -%}
  Updated: 08/27/2024
  This file is system-generated and should not be modified. We reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost
{%- endcomment -%}
{% if zp_override_template_name != blank %}{% assign zp_entity_marker = zp_override_template_name %}{% else %}{% assign zp_entity_marker = template.name %}{% endif %}{% if zp_override_template_suffix != blank %}{% assign zp_template_suffix = zp_override_template_suffix %}{% else %}{% assign zp_template_suffix = template.suffix %}{% endif %}{% assign zp_product_template_suffix = zp_template_suffix | remove: '-' %}{% assign zp_shop_dir_name = shop.permanent_domain | sha1 %}{% assign zp_collect_entity_content_parts = false %}{% assign zp_split_tests_available = false %}{% assign zp_use_json_entity_settings = false %}{% assign zp_use_only_custom_template = false %}{% assign zp_shop_metafields_loaded = false %}{% assign zp_current_entity_metafields_loaded = false %}{% assign zp_use_product_markup = false %}{% assign zp_use_current_entity_markup = false %}{% assign zp_use_native_entity_description = false %}{% assign zp_use_native_og_images = false %}{% assign zp_og_entity_type = 'website' %}{% assign zp_is_product_page = false %}{% assign zp_current_entity_index_state = nil %}{% if zp_template_suffix contains 'zipifypages' %}{% assign zp_enable_content_parsing = true %}{% else %}{% assign zp_enable_content_parsing = false %}{% endif %}{% if zp_enable_content_parsing and zp_entity_marker == 'article' %}{% assign zp_current_entity = article %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'aac' %}{% assign zp_scripts_metafield_key = 'articlescripts' %}{% assign zp_entity_data_metafield_key = 'articledata' %}{% assign zp_entity_styles_folder = 'articles' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_use_product_markup = true %}{% elsif zp_enable_content_parsing and zp_entity_marker == 'blog' %}{% assign zp_current_entity = blog %}{% assign zp_current_entity_content = '' | append: zp_current_entity.metafields['zipifypagesblogparts']['blogheaderfooter'] %}{% assign zp_entity_attributes_class = 'bac' %}{% assign zp_scripts_metafield_key = 'blogscripts' %}{% assign zp_entity_data_metafield_key = 'blogdata' %}{% assign zp_entity_styles_folder = 'blogs' %}{% assign zp_use_product_markup = true %}{% elsif zp_enable_content_parsing and zp_entity_marker == 'page' %}{% assign zp_current_entity = page %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'pac' %}{% assign zp_scripts_metafield_key = 'pagescripts' %}{% assign zp_entity_data_metafield_key = 'pagedata' %}{% assign zp_entity_styles_folder = 'pages' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_split_tests_available = true %}{% assign zp_use_product_markup = true %}{% elsif zp_entity_marker == 'index' %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% assign zp_shop_metafields_loaded = true %}{% assign zp_index_page = zp_shop_metafields['indexpage'] %}{% assign zp_index_page_handle = zp_shop_metafields['indexpagehandle'] %}{% if zp_index_page.type == 'page_reference' %}{% assign zp_current_entity = zp_index_page.value %}{% elsif zp_index_page_handle.size > 0 %}{% assign zp_current_entity = pages[zp_index_page_handle] %}{% else %}{% assign zp_current_entity = nil %}{% endif %}{% assign zp_current_entity_id_size = '' | append: zp_current_entity.id | size %}{% if zp_current_entity_id_size > 0 %}{% assign zp_entity_marker = 'page' %}{% assign zp_enable_content_parsing = true %}{% assign zp_current_entity_content = '' | append: zp_current_entity.content %}{% assign zp_entity_attributes_class = 'pac' %}{% assign zp_scripts_metafield_key = 'pagescripts' %}{% assign zp_entity_data_metafield_key = 'pagedata' %}{% assign zp_entity_styles_folder = 'pages' %}{% assign zp_collect_entity_content_parts = true %}{% assign zp_split_tests_available = true %}{% assign zp_use_product_markup = true %}{% else %}{% assign zp_current_entity = nil %}{% assign zp_enable_content_parsing = false %}{% endif %}{% elsif zp_entity_marker == 'product' %}{% assign zp_current_entity = product %}{% assign zp_entity_attributes_class = 'ppac' %}{% assign zp_scripts_metafield_key = 'productpagescripts' %}{% assign zp_entity_data_metafield_key = 'productpagedata' %}{% assign zp_entity_styles_folder = 'product_pages' %}{% assign zp_use_viewport_meta = false %}{% assign zp_use_json_entity_settings = true %}{% assign zp_is_product_page = true %}{% if zp_template_suffix contains 'zipifypages-' %}{% assign zp_current_entity_metafields = shop.metafields[zp_product_template_suffix] %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_index_state = zp_current_entity_metafields['config'].value['page_index_state'] %}{% else %}{% assign zp_current_entity_index_state = zp_current_entity_metafields['config']['page_index_state'] %}{% endif %}{% assign zp_current_entity_main_metafields = zp_current_entity.metafields['zipifypages'] %}{% if zp_use_meta_tags == true %}{% assign zp_use_meta_tags = true %}{% elsif zp_use_meta_tags == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_meta_tags = true %}{% else %}{% assign zp_use_meta_tags = false %}{% endif %}{% if zp_use_open_graph_tags == true %}{% assign zp_use_open_graph_tags = true %}{% elsif zp_use_open_graph_tags == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_open_graph_tags = true %}{% else %}{% assign zp_use_open_graph_tags = false %}{% endif %}{% if zp_use_favicon == true %}{% assign zp_use_favicon = true %}{% elsif zp_use_favicon == nil and zp_current_entity_index_state != 'custom' %}{% assign zp_use_favicon = true %}{% else %}{% assign zp_use_favicon = false %}{% endif %}{% assign zp_display_content_for_header = false %}{% assign zp_use_native_entity_description = true %}{% assign zp_use_native_og_images = true %}{% assign zp_og_entity_type = 'product' %}{% assign zp_use_current_entity_markup = true %}{% assign zp_product_page_with_zp_layout = true %}{% else %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% assign zp_use_meta_tags = false %}{% assign zp_use_open_graph_tags = false %}{% assign zp_use_favicon = false %}{% assign zp_display_content_for_header = false %}{% assign zp_use_only_custom_template = true %}{% assign zp_product_page_with_zp_layout = false %}{% endif %}{% assign zp_current_entity_metafields_loaded = true %}{% assign zp_current_entity_data = zp_current_entity_metafields['config'] | default: zp_current_entity_metafields[zp_entity_data_metafield_key] | default: 'noassigneddata' %}{% if zp_current_entity_data == 'noassigneddata' %}{% assign zp_enable_content_parsing = false %}{% else %}{% assign zp_enable_content_parsing = true %}{% assign zp_split_tests_available = true %}{% endif %}{% assign zp_current_entity_data = nil %}{% else %}{% assign zp_current_entity = nil %}{% assign zp_current_entity_content = '' %}{% assign zp_use_meta_tags = false %}{% assign zp_use_open_graph_tags = false %}{% assign zp_use_viewport_meta = false %}{% assign zp_use_favicon = false %}{% assign zp_display_content_for_header = false %}{% endif %}{% if zp_enable_content_parsing %}{% unless zp_shop_metafields_loaded %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% endunless %}{% unless zp_current_entity_metafields_loaded %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% endunless %}{% if zp_current_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% if zp_use_only_custom_template %}{% assign zp_entity_custom_template = true %}{% elsif zp_current_entity.template_suffix == 'custom.zipifypages' %}{% assign zp_entity_custom_template = true %}{% elsif zp_current_entity_index_state == 'custom' %}{% assign zp_entity_custom_template = true %}{% else %}{% assign zp_entity_custom_template = false %}{% endif %}{% endif %}{% if zp_collect_entity_content_parts and zp_current_entity_content contains ':|zpendofcontent|:' %}{% assign zp_entity_with_multiparts = true %}{% else %}{% assign zp_entity_with_multiparts = false %}{% endif %}{% assign zp_gdpr_enabled = false %}{% assign zp_alternative_entity_present = false %}{% assign zp_split_single_page_render = false %}{% assign zp_split_test_redirect = false %}{% assign zp_split_test_view_type_redirect = false %}{% if zp_entity_with_multiparts %}{% assign zp_current_entity_content = '' | append: zp_current_entity_content | split: ':|zpendofcontent|:' | first %}{% endif %}{% if zp_enable_content_parsing and zp_split_tests_available %}{% if zp_product_page_with_zp_layout %}{% assign zp_current_entity_object_metafields = zp_current_entity_main_metafields %}{% else %}{% assign zp_current_entity_object_metafields = zp_current_entity_metafields %}{% endif %}{% if zp_current_entity_object_metafields['splittest'] != blank %}{% if zp_current_entity_object_metafields['splittest'].type == 'json' %}{% assign zp_split_test_data = zp_current_entity_object_metafields['splittest'].value %}{% assign zp_split_test_type = zp_split_test_data.type %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data.alternative_handle %}{% assign zp_split_dataset = zp_split_test_data.dataset | json %}{% assign zp_split_dataset = '' | append: zp_split_dataset %}{% assign zp_split_single_page_render = '' | append: zp_split_test_data.single_page_render %}{% assign zp_split_token = zp_split_test_data.token%}{% else %}{% assign zp_split_test_data = '' | append: zp_current_entity_object_metafields['splittest'] | split: ':|~|:' %}{% assign zp_split_test_type = zp_split_test_data[0] %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data[1] %}{% assign zp_split_dataset = zp_split_test_data[2] %}{% assign zp_split_single_page_render = '' | append: zp_split_test_data[3] %}{% assign zp_split_token = '' | append: zp_split_test_data[4] %}{% endif %}{% if zp_split_test_type == 'mainlayout' %}{% if zp_entity_marker == 'article' %}{% assign zp_alternative_entity = articles[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.content %}{% elsif zp_entity_marker == 'blog' %}{% assign zp_alternative_entity = blogs[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.metafields['zipifypagesblogparts']['blogheaderfooter'] %}{% elsif zp_entity_marker == 'page' %}{% assign zp_alternative_entity = pages[zp_alternative_entity_handle] %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity.content %}{% else %}{% assign zp_alternative_entity = nil %}{% assign zp_alternative_entity_content = '' %}{% endif %}{% assign zp_alternative_entity_id_size = '' | append: zp_alternative_entity.id | size %}{% if zp_alternative_entity_id_size > 0 %}{% assign zp_alternative_entity_content = '' | append: zp_alternative_entity_content | split: ':|zpendofcontent|:' | first %}{% assign zp_alternative_entity_present = true %}{% if zp_split_single_page_render == 'true' %}{% assign zp_split_single_page_render = true %}{% else %}{% assign zp_split_single_page_render = false %}{% endif %}{% assign zp_alternative_entity_title = '' | append: zp_alternative_entity.title %}{% assign zp_alternative_entity_metafields = zp_alternative_entity.metafields['zipifypages'] %}{% if zp_alternative_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_alternative_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_alternative_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% assign zp_entity_scripts = '' | append: zp_alternative_entity_metafields[zp_scripts_metafield_key] | split: '|;|~|;|' %}{% assign zp_alternative_header_scripts = '' | append: zp_entity_scripts[0] | strip %}{% assign zp_entity_scripts = '' %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_alternative_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_alternative_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data contains '"gdpr_enabled":true' %}{% assign zp_gdpr_enabled = true %}{% elsif zp_blocks_data contains '"gdpr_enabled":false' %}{% assign zp_gdpr_enabled = false %}{% elsif zp_blocks_data contains '"gdpr_status":"enable_with_checkbox"' or zp_blocks_data contains '"gdpr_status":"enable_without_checkbox"' %}{% assign zp_gdpr_enabled = true %}{% endif %}{% endif %}{% assign zp_alternative_entity_content_wrapper_class = "zp " | append: zp_entity_attributes_class | append: '-' | append: zp_alternative_entity.id %}{% if zp_alternative_entity.template_suffix == 'custom.zipifypages' %}{% assign zp_alternative_entity_custom_template = true %}{% else %}{% assign zp_alternative_entity_custom_template = false %}{% endif %}{% elsif zp_split_test_type == 'redirect' or zp_split_test_type == 'redirecttarget' %}{% assign zp_split_test_redirect = true %}{% elsif zp_split_test_type == 'viewtyperedirect' %}{% assign zp_split_test_view_type_redirect = true %}{% endif %}{% endif %}{% endif %}{% if zp_enable_content_parsing %}{% assign zp_entity_title = page_title | remove: ':|~|:' | remove: ':--:' | append: entity_title_suffix | strip %}{% if zp_gdpr_enabled == false %}{% assign zp_blocks_data_start_numbers = '123456789' | split: '' %}{% assign zp_blocks_data = '' | append: zp_current_entity_metafields['btnpopups'] | strip | default: 'nobuttons' %}{% assign zp_blocks_data_first_char = zp_blocks_data | slice: 0, 1 %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_first_char %}{% assign zp_blocks_data_parts_count = '' %}{% assign zp_blocks_data_count_parts = zp_blocks_data | slice: 0, 3 | split: '' %}{% for zp_blocks_data_count_part in zp_blocks_data_count_parts %}{% if zp_blocks_data_start_numbers contains zp_blocks_data_count_part %}{% assign zp_blocks_data_parts_count = zp_blocks_data_parts_count | append: zp_blocks_data_count_part %}{% else %}{% break %}{% endif %}{% endfor %}{% assign zp_blocks_data_parts_count_index = zp_blocks_data_parts_count.size | plus: 2 %}{% assign zp_blocks_data_parts_count = 0 | plus: zp_blocks_data_parts_count %}{% for i in (2..zp_blocks_data_parts_count) %}{% assign zp_blocks_data_metafield_key = 'btnpopups' | append: i %}{% assign zp_blocks_data = zp_blocks_data | append: zp_current_entity_metafields[zp_blocks_data_metafield_key] %}{% endfor %}{% assign zp_blocks_data = zp_blocks_data | slice: zp_blocks_data_parts_count_index, zp_blocks_data.size %}{% endif %}{% assign zp_blocks_data = zp_blocks_data | replace: 'https://s3.amazonaws.com/shopify-pages-development/pictures/', 'https://cdn01.zipify.com/' | replace: '_ilcdn_/', 'https://cdn05.zipify.com/' %}{% if zp_blocks_data contains '"gdpr_enabled":true' %}{% assign zp_gdpr_enabled = true %}{% elsif zp_blocks_data contains '"gdpr_enabled":false' %}{% assign zp_gdpr_enabled = false %}{% elsif zp_blocks_data contains '"gdpr_status":"enable_with_checkbox"' or zp_blocks_data contains '"gdpr_status":"enable_without_checkbox"' %}{% assign zp_gdpr_enabled = true %}{% endif %}{% endif %}{% assign zp_blocks_data = '' %}{% endif %}{% if zp_enable_content_parsing %}{% assign zp_enable_loop_subscriptions_assets_load = true %}{% capture zp_replace_integration %}<zp_additional_property_integration></zp_additional_property_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_additional_property_integration>', '' | replace: '</zp_additional_property_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{%- assign zp_use_split_test_redirect = false -%}{% assign zp_available_split_test_redirect_context = 'tpl,lsctn' | split: ',' %}{% if zp_use_split_test_redirect and zp_available_split_test_redirect_context contains renderctx %}{%- render 'split-test-redirect.zipifypages', renderctx: 'snpt' -%}{% endif %}{% assign zp_available_render_context = 'tpl,thm,idxtpl,lsctn' | split: ',' %}{% if zp_available_render_context contains renderctx %}{% if zp_use_favicon == nil and renderctx == 'thm' %}{% assign zp_use_favicon = false %}{% endif %}{% if zp_use_meta_tags == nil and renderctx == 'thm' %}{% assign zp_use_meta_tags = false %}{% endif %}{% if zp_use_open_graph_tags == nil and renderctx == 'thm' %}{% assign zp_use_open_graph_tags = false %}{% endif %}{% if zp_use_viewport_meta == nil and renderctx == 'thm' %}{% assign zp_use_viewport_meta = false %}{% endif %}{% assign zp_display_content_for_header = false %}{% endif %}{% assign zp_alternative_analytics_loading = true %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_recharge_subscriptions_enabled = zp_current_entity_metafields['config'].value['enbrcharge'] %}{% else %}{% assign zp_recharge_subscriptions_enabled = zp_current_entity_metafields['config']['enbrcharge'] %}{% endif %}{% if zp_alternative_entity_present %}{% unless zp_recharge_subscriptions_enabled %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_recharge_subscriptions_enabled = zp_alternative_entity_metafields['config'].value['enbrcharge'] %}{% else %}{% assign zp_recharge_subscriptions_enabled = zp_alternative_entity_metafields['config']['enbrcharge'] %}{% endif %}{% endunless %}{% endif %}{% assign zp_app_integrations = '' | append: zp_shop_metafields['appintegrations'] | split: ',' %}{% if zp_recharge_subscriptions_enabled %}{% assign zp_js_config_app_integrations = 'recharge' | split: ',' | concat: zp_app_integrations | uniq %}{% else %}{% assign zp_js_config_app_integrations = zp_app_integrations | uniq %}{% endif %}{% assign zp_shopify_options_selector = false %}{% assign zp_unit_price_enabled = false %}{% assign zp_settings_keys = zp_shop_metafields['blockssettings'] %}{% if zp_settings_keys != blank %}{% assign zp_use_json_blocks_settings = true %}{% if zp_settings_keys.type == 'json' %}{% assign zp_settings_keys = zp_settings_keys.value %}{% endif %}{% else %}{% assign zp_use_json_blocks_settings = false %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% assign zp_object_settings_data = '' | append: zp_shop_metafields['buyboxessettings'] | strip %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% endif %}{% for zp_setting_key_data in zp_settings_keys %}{% if zp_use_json_blocks_settings %}{% assign zp_setting_key = zp_setting_key_data | first %}{% assign zp_setting_value = zp_setting_key_data | last | strip %}{% else %}{% assign zp_setting_key = zp_setting_key_data %}{% assign zp_setting_value = '' | append: zp_settings_values[forloop.index0] | strip %}{% endif %}{% if zp_setting_key == 'show_unavailable_variants' %}{% if zp_setting_value == 'true' %}{% assign zp_shopify_options_selector = true %}{% endif %}{% elsif zp_setting_key == 'show_unit_price' %}{% if zp_setting_value == 'true' %}{% assign zp_unit_price_enabled = true %}{% endif %}{% endif %}{% endfor %}{% if zp_use_open_graph_tags == nil or zp_use_open_graph_tags == true %}{% assign zp_show_open_graph_tags = true %}{% else %}{% assign zp_show_open_graph_tags = false %}{% endif %}{% if zp_use_meta_tags == nil or zp_use_meta_tags == true %}{% assign zp_show_meta_tags = true %}{% else %}{% assign zp_show_meta_tags = false %}{% endif %}{% assign zp_local_fonts_style_url = 'https://zipifypages.zipify.com' | append: '/shop_fonts/' | append: zp_shop_dir_name | append: '/shopify_fonts.css' %}{% assign zp_fonts_style_version = '' | append: zp_shop_metafields['fontsstylesversion'] | strip | default: 'nofontsstyles' %}{% if zp_fonts_style_version == 'nofontsstyles' %}{% assign zp_fonts_style_url = 'https://zipifypages.zipify.com' | append: '/shop_fonts/' | append: zp_shop_dir_name | append: '/shopify_fonts.css' %}{% elsif zp_fonts_style_version == 'emptyfontsstyles' %}{% assign zp_fonts_style_url = '' %}{% else %}{% assign zp_fonts_style_url = 'https://cdn03.zipify.com' | append: '/' | append: zp_shop_dir_name | append: '/fonts/' | append: zp_fonts_style_version | append: '.css' %}{% endif %}{% assign zp_open_graph_custom_mode = false %}{% assign zp_twitter_card = 'summary' %}{% if zp_current_entity_metafields['config'].type == 'json' and zp_current_entity_metafields['config'].value['page_last_sync_date'] != blank %}{% assign zp_use_config_entity_settings = true %}{% else %}{% assign zp_use_config_entity_settings = false %}{% endif %}{% if zp_use_json_entity_settings or zp_use_config_entity_settings %}{% if zp_use_config_entity_settings %}{% assign zp_object_settings = zp_current_entity_metafields['config'].value %}{% assign zp_entity_data_settings = '' | append: zp_object_settings['page_settings_data'] %}{% assign zp_entity_fonts_settings = '' | append: zp_object_settings['page_fonts_settings'] %}{% elsif zp_current_entity_metafields[zp_entity_data_metafield_key].type == 'json' %}{% assign zp_object_settings = zp_current_entity_metafields[zp_entity_data_metafield_key].value %}{% assign zp_entity_data_settings = zp_object_settings['page_settings_data'] | default: '' %}{% assign zp_entity_fonts_settings = zp_object_settings['page_fonts_settings'] | default: '' %}{% else %}{% assign zp_object_settings = zp_current_entity_metafields[zp_entity_data_metafield_key] %}{% assign zp_entity_data_settings = zp_object_settings['page_settings_data'] | default: '' %}{% assign zp_entity_fonts_settings = zp_object_settings['page_fonts_settings'] | default: '' %}{% endif %}{% assign zp_entity_fonts_settings = zp_entity_fonts_settings | replace: zp_local_fonts_style_url, zp_fonts_style_url | replace: '__fonts_style_url__', zp_fonts_style_url %}{% assign zp_setting_value = '' | append: zp_object_settings['page_with_default_settings'] %}{% if zp_setting_value == 'true' %}{% assign zp_entity_with_default_styles = true %}{% else %}{% assign zp_entity_with_default_styles = false %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['page_fixed_layout'] %}{% if zp_setting_value == 'true' %}{% assign zp_entity_fixed_layout = true %}{% else %}{% assign zp_entity_fixed_layout = false %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['enable_stamped'] %}{% if zp_setting_value == 'true' %}{% assign zp_load_stamped_scripts = true %}{% else %}{% assign zp_load_stamped_scripts = false %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['enable_loox'] %}{% if zp_setting_value == 'true' %}{% assign zp_load_loox_scripts = true %}{% else %}{% assign zp_load_loox_scripts = false %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['open_graph_mode'] %}{% if zp_setting_value == 'custom' %}{% assign zp_open_graph_custom_mode = true %}{% assign zp_use_native_og_images = false %}{% endif %}{% assign zp_open_graph_title = '' | append: zp_object_settings['open_graph_title'] | strip %}{% assign zp_open_graph_url = '' | append: zp_object_settings['open_graph_url'] | strip %}{% assign zp_open_graph_description = '' | append: zp_object_settings['open_graph_description'] | strip %}{% assign zp_open_graph_image_url = '' | append: zp_object_settings['open_graph_image_url'] | strip %}{% assign zp_setting_value = nil %}{% else %}{% assign zp_entity_data_settings = '' %}{% assign zp_entity_fonts_settings = '' %}{% assign zp_open_graph_title = '' %}{% assign zp_open_graph_url = '' %}{% assign zp_open_graph_description = '' %}{% assign zp_open_graph_image_url = '' %}{% assign zp_entity_with_default_styles = false %}{% assign zp_entity_fixed_layout = false %}{% assign zp_load_loox_scripts = false %}{% assign zp_load_stamped_scripts = false %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% assign zp_object_settings_data = '' | append: zp_current_entity_metafields[zp_entity_data_metafield_key] | strip %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_setting_key in zp_settings_keys %}{% assign zp_setting_value = '' | append: zp_settings_values[forloop.index0] | strip %}{% if 'page_settings_data' == zp_setting_key %}{% assign zp_entity_data_settings = zp_setting_value %}{% elsif 'page_fonts_settings' == zp_setting_key %}{% assign zp_entity_fonts_settings = zp_setting_value %}{% assign zp_entity_fonts_settings = zp_entity_fonts_settings | replace: zp_local_fonts_style_url, zp_fonts_style_url | replace: '__fonts_style_url__', zp_fonts_style_url %}{% elsif 'page_with_default_settings' == zp_setting_key %}{% if zp_setting_value == 'true' %}{% assign zp_entity_with_default_styles = true %}{% endif %}{% elsif 'page_fixed_layout' == zp_setting_key %}{% if zp_setting_value == 'true' %}{% assign zp_entity_fixed_layout = true %}{% endif %}{% elsif 'enable_loox' == zp_setting_key %}{% if zp_setting_value == 'true' %}{% assign zp_load_loox_scripts = true %}{% endif %}{% elsif 'enable_stamped' == zp_setting_key %}{% if zp_setting_value == 'true' %}{% assign zp_load_stamped_scripts = true %}{% endif %}{% elsif 'open_graph_mode' == zp_setting_key %}{% if zp_setting_value == 'custom' %}{% assign zp_open_graph_custom_mode = true %}{% assign zp_use_native_og_images = false %}{% endif %}{% elsif 'open_graph_title' == zp_setting_key %}{% assign zp_open_graph_title = zp_setting_value %}{% elsif 'open_graph_url' == zp_setting_key %}{% assign zp_open_graph_url = zp_setting_value %}{% elsif 'open_graph_description' == zp_setting_key %}{% assign zp_open_graph_description = zp_setting_value %}{% elsif 'open_graph_image_url' == zp_setting_key %}{% assign zp_open_graph_image_url = zp_setting_value %}{% endif %}{% endfor %}{% endif %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_analytics_settings = zp_current_entity_metafields['config'].value['analytics'] %}{% assign zp_current_entity_fonts_css_state = zp_current_entity_metafields['config'].value['fontscss'] %}{% else %}{% assign zp_analytics_settings = zp_current_entity_metafields['config']['analytics'] %}{% assign zp_current_entity_fonts_css_state = nil %}{% endif %}{% if zp_alternative_entity_present == true %}{% assign zp_alternative_data_settings = '' %}{% assign zp_alternative_fonts_settings = '' %}{% assign zp_alternative_entity_with_default_styles = false %}{% assign zp_alternative_entity_fixed_layout = false %}{% if zp_alternative_entity_metafields['config'].type == 'json' and zp_alternative_entity_metafields['config'].value['page_last_sync_date'] != blank %}{% assign zp_object_settings = zp_alternative_entity_metafields['config'].value %}{% assign zp_alternative_data_settings = '' | append: zp_object_settings['page_settings_data'] %}{% assign zp_alternative_fonts_settings = '' | append: zp_object_settings['page_fonts_settings'] %}{% assign zp_alternative_fonts_settings = zp_alternative_fonts_settings | replace: zp_local_fonts_style_url, zp_fonts_style_url | replace: '__fonts_style_url__', zp_fonts_style_url %}{% assign zp_setting_value = '' | append: zp_object_settings['page_with_default_settings'] | strip %}{% if zp_setting_value == 'true' %}{% assign zp_alternative_entity_with_default_styles = true %}{% endif %}{% assign zp_setting_value = '' | append: zp_object_settings['page_fixed_layout'] | strip %}{% if zp_setting_value == 'true' %}{% assign zp_alternative_entity_fixed_layout = true %}{% endif %}{% assign zp_alternative_last_sync = '' | append: zp_object_settings['page_last_sync_date'] | strip %}{% else %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% assign zp_object_settings_data = '' | append: zp_alternative_entity_metafields[zp_entity_data_metafield_key] | strip %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_setting_key in zp_settings_keys %}{% assign zp_setting_value = '' | append: zp_settings_values[forloop.index0] | strip %}{% if 'page_settings_data' == zp_setting_key %}{% assign zp_alternative_data_settings = zp_setting_value %}{% elsif 'page_fonts_settings' == zp_setting_key %}{% assign zp_alternative_fonts_settings = zp_setting_value %}{% assign zp_alternative_fonts_settings = zp_alternative_fonts_settings | replace: zp_local_fonts_style_url, zp_fonts_style_url | replace: '__fonts_style_url__', zp_fonts_style_url %}{% elsif 'page_with_default_settings' == zp_setting_key %}{% if zp_setting_value == 'true' %}{% assign zp_alternative_entity_with_default_styles = true %}{% endif %}{% elsif 'page_fixed_layout' == zp_setting_key %}{% if zp_setting_value == 'true' %}{% assign zp_alternative_entity_fixed_layout = true %}{% endif %}{% elsif 'page_last_sync_date' == zp_setting_key %}{% assign zp_alternative_last_sync = zp_setting_value %}{% endif %}{% endfor %}{% endif %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_alternative_analytics_settings = zp_alternative_entity_metafields['config'].value['analytics'] %}{% assign zp_alternative_entity_fonts_css_state = zp_alternative_entity_metafields['config'].value['fontscss'] %}{% else %}{% assign zp_alternative_analytics_settings = zp_alternative_entity_metafields['config']['analytics'] %}{% assign zp_alternative_entity_fonts_css_state = nil %}{% endif %}{% endif %}{% assign zp_setting_value = '' %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% if zp_show_meta_tags == true or zp_show_open_graph_tags == true %}{% if zp_use_native_entity_description %}{% assign zp_entity_description = page_description %}{% else %}{% assign zp_entity_description = '' | append: zp_current_entity.metafields['global']['description_tag'] | strip %}{% endif %}{% if zp_entity_description.size < 1 %}{% if zp_split_single_page_render == true %}{% assign zp_entity_description = '' | append: zp_current_entity_content | split: zp_split_parts_separator | first | replace: 'zp__script', 'script' %}{% else %}{% assign zp_entity_description = '' | append: zp_current_entity_content %}{% endif %}{% assign zp_entity_description = '' | append: zp_entity_description | strip | strip_html | strip | newline_to_br | strip_newlines | strip %}{% assign zp_entity_description = zp_entity_description | replace: '<br />', ' ' | replace: '<br/>', ' ' | replace: '<br>', ' ' | replace: ':|~|:', '' | replace: ':--:', '' %}{% assign zp_entity_description_parts = zp_entity_description | split: ' ' %}{% assign zp_entity_description = '' %}{% for zp_part in zp_entity_description_parts %}{% assign zp_content_part = zp_part | strip %}{% if zp_content_part.size > 0 %}{% assign zp_entity_description = zp_entity_description | append: ' ' | append: zp_content_part %}{% endif %}{% endfor %}{% assign zp_part = '' %}{% assign zp_content_part = '' %}{% assign zp_entity_description = zp_entity_description | strip | truncate: 160, '' %}{% assign zp_entity_description_parts = '' %}{% elsif zp_entity_description == 'no_description' %}{% assign zp_entity_description = '' %}{% endif %}{% endif %}{% if zp_show_meta_tags == true %}<title>{{ zp_entity_title }}</title>{% if zp_entity_description.size > 0 %}<meta name="description" content="{{ zp_entity_description | escape }}">{% endif %}<link rel="canonical" href="{{ canonical_url }}">{% endif %}{% if zp_show_open_graph_tags == true %}{% unless zp_open_graph_custom_mode == true %}{% assign zp_open_graph_title = zp_entity_title | escape %}{% assign zp_open_graph_description = zp_entity_description | escape %}{% assign zp_open_graph_url = canonical_url %}{% assign zp_open_graph_image_url = '' %}{% if zp_use_native_og_images and zp_is_product_page %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_tmp_products_settings = zp_current_entity_metafields['config'].value['products'] %}{% else %}{% assign zp_tmp_products_settings = zp_current_entity_metafields['config']['products'] %}{% endif %}{% assign zp_product_settings_keys = nil %}{% for zp_tmp_product_settings in zp_tmp_products_settings %}{% assign zp_tmp_product_settings_key = zp_tmp_product_settings | first %}{% assign zp_tmp_product_settings_value = zp_tmp_product_settings | last %}{% if zp_tmp_product_settings_value['crntentt'] %}{% assign zp_product_settings_keys = zp_tmp_product_settings_value %}{% break %}{% endif %}{% endfor %}{% assign zp_tmp_products_settings = nil %}{% if zp_product_settings_keys != blank %}{% assign zp_tmp_current_product_schema_config = zp_product_settings_keys['schm'] %}{% if zp_tmp_current_product_schema_config == blank %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_tmp_current_product_schema_config = zp_current_entity_metafields['config'].value['schema'][zp_tmp_product_block_id] %}{% else %}{% assign zp_tmp_current_product_schema_config = zp_current_entity_metafields['config']['schema'][zp_tmp_product_block_id] %}{% endif %}{% endif %}{% assign zp_open_graph_image_url = zp_tmp_current_product_schema_config['img'] %}{% if zp_open_graph_image_url == blank %}{% assign zp_tmp_product_as_current_entity = true %}{% if zp_current_product_og_image_url == blank %}{% assign zp_tmp_product_image_size = '1080x' %}{% assign zp_open_graph_image_url = 'no-image.gif' | img_url: zp_tmp_product_image_size %}{% assign zp_tmp_product_handle = '' %}{% assign zp_tmp_selected_variants = '' %}{% assign zp_tmp_product_link_type = 'cart' %}{% assign zp_tmp_product_link_types = nil %}{% assign zp_tmp_product_image = nil %}{% assign zp_tmp_product_image_type = 'product' %}{% assign zp_tmp_product_image_attributes = '' %}{% assign zp_tmp_product_as_current_entity = false %}{% assign zp_tmp_product_block_id = nil %}{% assign zp_current_product_og_image_url = nil %}{% assign zp_tmp_prdimgs_dt = nil %}{% assign zp_tmp_render_products_from_metafield = true %}{% for zp_product_setting_key_data in zp_product_settings_keys %}{% if zp_tmp_render_products_from_metafield %}{% assign zp_prd_stng_k = zp_product_setting_key_data | first %}{% assign zp_unstringified_product_setting_keys = 'btns,imgs' | split: ',' %}{% if zp_unstringified_product_setting_keys contains zp_prd_stng_k %}{% assign zp_prd_stng_vl = zp_product_setting_key_data | last %}{% else %}{% assign zp_prd_stng_vl = zp_product_setting_key_data | last | strip %}{% endif %}{% assign zp_unstringified_product_setting_keys = nil %}{% else %}{% assign zp_prd_stng_k = zp_product_setting_key_data %}{% assign zp_prd_stng_vl = '' | append: zp_product_settings_values[forloop.index0] | strip %}{% endif %}{% case zp_prd_stng_k %}{% when 'handle' %}{% assign zp_tmp_product_handle = zp_prd_stng_vl %}{% when 'slctvrnt' %}{% assign zp_tmp_selected_variants = zp_prd_stng_vl %}{% when 'btnltp' %}{% assign zp_tmp_product_link_type = zp_prd_stng_vl | replace: '\', '&bsol;' %}{% when 'btns' %}{% assign zp_prdlnk_tps_str = '' %}{% for zp_prdct_sttng_val in zp_prd_stng_vl %}{% assign zp_prdlnk_tps_str = zp_prdlnk_tps_str | append: zp_prdct_sttng_val['tp'] | append: ',' %}{% endfor %}{% assign zp_tmp_product_link_types = zp_prdlnk_tps_str | split: ',' %}{% when 'imgtp' %}{% assign zp_tmp_product_image_type = zp_prd_stng_vl %}{% when 'bid' %}{% assign zp_tmp_product_block_id = zp_prd_stng_vl %}{% when 'crntentt' %}{% if zp_prd_stng_vl == 'true' or zp_prd_stng_vl == '1' %}{% assign zp_tmp_product_as_current_entity = true %}{% endif %}{% when 'imgs' %}{% assign zp_tmp_prdimgs_dt = zp_prd_stng_vl %}{% endcase %}{% endfor %}{% endif %}{% if zp_current_product_og_image_url != blank %}{% assign zp_open_graph_image_url = zp_current_product_og_image_url %}{% assign zp_current_product_og_image_url = nil %}{% elsif zp_tmp_product_image_attributes.size > 0 %}{% assign zp_img_src = zp_tmp_product_image_attributes | append: ' ' %}{% assign zp_img_src_parts = zp_img_src | split: ' src=' %}{% if zp_img_src_parts.size <= 1 %}{% assign zp_img_src_parts = zp_img_src | split: ' data-src=' %}{% endif %}{% if zp_img_src_parts.size > 1 %}{% assign zp_open_graph_image_url = zp_img_src_parts | last | strip | append: ' ' | split: ' ' | first | strip | remove: '"' | remove: "'" %}{% endif %}{% assign zp_img_src = nil %}{% assign zp_img_src_parts = nil %}{% elsif zp_tmp_product_handle.size > 0 %}{% if zp_tmp_product_as_current_entity %}{% assign zp_tmp_product = zp_current_entity %}{% else %}{% assign zp_tmp_product = all_products[zp_tmp_product_handle] %}{% endif %}{% assign zp_tmp_product_id_size = '' | append: zp_tmp_product.id | size %}{% if zp_tmp_product_id_size > 0 %}{% assign zp_tmp_selected_variants = zp_tmp_selected_variants | split: ',' %}{% if zp_tmp_product_custom_variants == blank %}{% assign zp_tmp_product_custom_variants = zp_tmp_product.variants %}{% endif %}{% if zp_tmp_selected_variants.size < 1 %}{% assign zp_tmp_selected_variants = zp_tmp_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% endif %}{% if zp_tmp_use_product_variant_from_url %}{% assign zp_url_selected_variant_id = '' | append: zp_tmp_product.selected_variant.id %}{% else %}{% assign zp_url_selected_variant_id = '' %}{% endif %}{% assign zp_filtered_selected_variants = '' %}{% assign zp_tmp_first_selected_variant = nil %}{% assign zp_tmp_current_selected_variant = nil %}{% assign zp_tmp_any_product_variant_available = false %}{% for zp_prd_variant in zp_tmp_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_tmp_use_product_variant_from_url and zp_url_selected_variant_id == zp_variant_id and zp_tmp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_tmp_first_selected_variant = zp_prd_variant %}{% assign zp_tmp_current_selected_variant = zp_prd_variant %}{% endif %}{% if zp_tmp_selected_variants contains zp_variant_id %}{% assign zp_filtered_selected_variants = zp_filtered_selected_variants | append: zp_variant_id | append: ',' %}{% if zp_tmp_first_selected_variant == nil %}{% assign zp_tmp_first_selected_variant = zp_prd_variant %}{% endif %}{% if zp_prd_variant.available %}{% assign zp_tmp_any_product_variant_available = true %}{% endif %}{% endif %}{% endfor %}{% assign zp_tmp_selected_variants = zp_filtered_selected_variants | split: ',' %}{% if zp_tmp_selected_variants.size < 1 %}{% assign zp_tmp_selected_variants = zp_tmp_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% assign zp_tmp_any_product_variant_available = zp_tmp_product.available %}{% if zp_tmp_use_product_variant_from_url and zp_tmp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_tmp_first_selected_variant = zp_tmp_product.selected_variant %}{% assign zp_tmp_current_selected_variant = zp_tmp_product.selected_variant %}{% else %}{% assign zp_tmp_first_selected_variant = zp_tmp_product_custom_variants | first %}{% endif %}{% endif %}{% unless zp_tmp_first_selected_variant.available %}{% assign zp_tmp_first_available_variant = nil %}{% for zp_prd_variant in zp_tmp_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_tmp_selected_variants contains zp_variant_id and zp_prd_variant.available %}{% assign zp_tmp_first_available_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% unless zp_tmp_first_available_variant == nil %}{% assign zp_tmp_first_selected_variant = zp_tmp_first_available_variant %}{% endunless %}{% assign zp_tmp_first_available_variant = nil %}{% endunless %}{% assign zp_variant_id = nil %}{% assign zp_shw_vrntsslctr_lnktps = 'cart,checkout,cart_current_page,cart_external' | split: ',' %}{% if zp_tmp_product_link_types != blank %}{% assign zp_shw_vrntsslctr = false %}{% for zp_link_type in zp_tmp_product_link_types %}{% if zp_shw_vrntsslctr_lnktps contains zp_link_type %}{% assign zp_shw_vrntsslctr = true %}{% break %}{% endif %}{% endfor %}{% elsif zp_shw_vrntsslctr_lnktps contains zp_tmp_product_link_type %}{% assign zp_shw_vrntsslctr = true %}{% else %}{% assign zp_shw_vrntsslctr = false %}{% endif %}{% assign zp_show_variants_selectors = zp_shw_vrntsslctr %}{% assign zp_tmp_product_featured_image = zp_tmp_product.featured_image %}{% if zp_tmp_prdimgs_dt != blank and zp_tmp_product_image_id != blank %}{% assign zp_tmp_first_product_image_type = nil %}{% for zp_tmp_prdimg_dt in zp_tmp_prdimgs_dt %}{% assign zp_tmp_prdimg_id = '' | append: zp_tmp_prdimg_dt['id'] %}{% if zp_tmp_prdimg_id == zp_tmp_product_image_id %}{% assign zp_tmp_first_product_image_type = zp_tmp_prdimg_dt['tp'] %}{% break %}{% endif %}{% endfor %}{% if zp_tmp_first_product_image_type != blank and zp_tmp_first_product_image_type != '' %}{% assign zp_tmp_product_image_type = zp_tmp_first_product_image_type %}{% endif %}{% endif %}{% assign zp_first_selected_variant_image_id = '' | append: zp_tmp_first_selected_variant.image.id | strip %}{% if zp_tmp_product_image_type == 'variant' and zp_shw_vrntsslctr == true and zp_first_selected_variant_image_id.size > 0 %}{% assign zp_tmp_product_featured_image = zp_tmp_first_selected_variant.image %}{% endif %}{% assign zp_tmp_product_image_srcset_available = true %}{% assign zp_open_graph_image_url = zp_tmp_product_featured_image | img_url: zp_tmp_product_image_size %}{% assign zp_tmp_product_featured_image = nil %}{% assign zp_tmp_first_selected_variant = nil %}{% endif %}{% assign zp_tmp_product = nil %}{% endif %}{% endif %}{% assign zp_open_graph_image_url = '<meta property="og:image" content="' | append: zp_open_graph_image_url | append: '">' %}{% assign zp_tmp_current_product_schema_config = nil %}{% else %}{% capture zp_open_graph_image_url %}{% for zp_image in product.images limit:3 -%}<meta property="og:image" content="{{ zp_image.src | product_img_url: '1080x' }}">{% endfor %}{% endcapture %}{% endif %}{% else %}{% if zp_split_single_page_render == true %}{% assign zp_open_graph_search_content = ' ' | append: zp_current_entity_content | split: zp_split_parts_separator | first | append: ' ' %}{% else %}{% assign zp_open_graph_search_content = ' ' | append: zp_current_entity_content | append: ' ' %}{% endif %}{% assign zp_open_graph_search_content = zp_open_graph_search_content | replace: '<zp_rch_product6>', '__zp_image_1502150400__<zp_rch_product6>' | replace: '<zp_rch_product5>', '__zp_image_1502150400__<zp_rch_product5>' | replace: '<zp_rch_product4>', '__zp_image_1502150400__<zp_rch_product4>' | replace: '<zp_rch_product3>', '__zp_image_1502150400__<zp_rch_product3>' | replace: '<zp_rch_product2>', '__zp_image_1502150400__<zp_rch_product2>' | replace: '<zp_rch_product1>', '__zp_image_1502150400__<zp_rch_product1>' | replace: '<zp_bvo_product3>', '__zp_image_1502150400__<zp_bvo_product3>' | replace: '<zp_bvo_product2>', '__zp_image_1502150400__<zp_bvo_product2>' | replace: '<zp_bvo_product1>', '__zp_image_1502150400__<zp_bvo_product1>' | replace: '<zp_bvh_product3>', '__zp_image_1502150400__<zp_bvh_product3>' | replace: '<zp_bvh_product2>', '__zp_image_1502150400__<zp_bvh_product2>' | replace: '<zp_bvh_product1>', '__zp_image_1502150400__<zp_bvh_product1>' | replace: '<zp_bv_product3>', '__zp_image_1502150400__<zp_bv_product3>' | replace: '<zp_bv_product2>', '__zp_image_1502150400__<zp_bv_product2>' | replace: '<zp_bv_product1>', '__zp_image_1502150400__<zp_bv_product1>' | replace: '<zp_product3>', '__zp_image_1502150400__<zp_product3>' | replace: '<zp_product2>', '__zp_image_1502150400__<zp_product2>' | replace: '<zp_product1>', '__zp_image_1502150400__<zp_product1>' | replace: '<zp_ofrbox_product>', '__zp_image_1502150400__<zp_ofrbox_product>' | replace: '<zp_product>', '__zp_image_1502150400__<zp_product>' | replace: '<zpdproduct ', '__zp_image_1502150400__<zpdproduct ' | replace: '<img ', '__zp_image_1502150400__<img ' | replace_first: '__zp_image_1502150400__', '__zp_fimage_1502150400__' %}{% if zp_open_graph_search_content contains '__zp_fimage_1502150400__' %}{% assign zp_open_graph_search_content = zp_open_graph_search_content | split: '__zp_fimage_1502150400__' | last | append: ' ' %}{% assign zp_first_tag_name = zp_open_graph_search_content | slice: 0, 19 %}{% assign zp_is_product_image = false %}{% assign zp_is_default_image = false %}{% if zp_first_tag_name contains 'zp_rch_product6' %}{% assign zp_image_tag_type = 'zp_rch_product6' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_rch_product5' %}{% assign zp_image_tag_type = 'zp_rch_product5' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_rch_product4' %}{% assign zp_image_tag_type = 'zp_rch_product4' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_rch_product3' %}{% assign zp_image_tag_type = 'zp_rch_product3' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_rch_product2' %}{% assign zp_image_tag_type = 'zp_rch_product2' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_rch_product1' %}{% assign zp_image_tag_type = 'zp_rch_product1' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bvo_product3' %}{% assign zp_image_tag_type = 'zp_bvo_product3' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bvo_product2' %}{% assign zp_image_tag_type = 'zp_bvo_product2' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bvo_product1' %}{% assign zp_image_tag_type = 'zp_bvo_product1' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bvh_product3' %}{% assign zp_image_tag_type = 'zp_bvh_product3' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bvh_product2' %}{% assign zp_image_tag_type = 'zp_bvh_product2' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bvh_product1' %}{% assign zp_image_tag_type = 'zp_bvh_product1' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bv_product3' %}{% assign zp_image_tag_type = 'zp_bv_product3' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bv_product2' %}{% assign zp_image_tag_type = 'zp_bv_product2' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_bv_product1' %}{% assign zp_image_tag_type = 'zp_bv_product1' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_product3' %}{% assign zp_image_tag_type = 'zp_product3' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_product2' %}{% assign zp_image_tag_type = 'zp_product2' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_product1' %}{% assign zp_image_tag_type = 'zp_product1' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_ofrbox_product' %}{% assign zp_image_tag_type = 'zp_ofrbox_product' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zp_product' %}{% assign zp_image_tag_type = 'zp_product' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains 'zpdproduct' %}{% assign zp_image_tag_type = 'zpdproduct' %}{% assign zp_is_product_image = true %}{% elsif zp_first_tag_name contains '<img ' %}{% assign zp_image_tag_type = 'image' %}{% assign zp_is_default_image = true %}{% endif %}{% if zp_is_product_image %}{% assign zp_tmp_closed_product_tag = '</' | append: zp_image_tag_type | append: '>' %}{% assign zp_open_graph_search_content = zp_open_graph_search_content | split: zp_tmp_closed_product_tag | first %}{% assign zp_tmp_product_image_size = '1080x' %}{% assign zp_open_graph_image_url = 'no-image.gif' | img_url: zp_tmp_product_image_size %}{% assign zp_tmp_product_handle = '' %}{% assign zp_tmp_selected_variants = '' %}{% assign zp_tmp_product_link_type = 'cart' %}{% assign zp_tmp_product_link_types = nil %}{% assign zp_tmp_product_image = nil %}{% assign zp_tmp_product_image_type = 'product' %}{% assign zp_tmp_product_image_attributes = '' %}{% assign zp_tmp_product_as_current_entity = false %}{% assign zp_tmp_product_block_id = nil %}{% assign zp_current_product_og_image_url = nil %}{% assign zp_tmp_prdimgs_dt = nil %}{% if zp_image_tag_type == 'zpdproduct' %}{% if zp_open_graph_search_content contains '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_open_graph_search_content = zp_open_graph_search_content | remove: '<zpprdmrkp></zpprdmrkp>' %}{% assign zp_display_prdtmp_markup = true %}{% else %}{% assign zp_display_prdtmp_markup = false %}{% endif %}{% assign zp_tmp_product_content_parts = zp_open_graph_search_content | append: ' ' | split: '>' %}{% assign zp_product_settings = zp_tmp_product_content_parts[0] %}{% assign zp_product_settings_tmp = zp_product_settings | append: '>' %}{% assign zp_product_settings = ' ' | append: zp_product_settings | split: ' data-' %}{% assign zp_product_settings_keys = '' %}{% assign zp_product_settings_values = '' %}{% for zp_product_setting in zp_product_settings %}{% assign zp_product_setting_parts = zp_product_setting | split: '=' %}{% if zp_product_setting_parts.size < 2 %}{% continue %}{% endif %}{% assign zp_prd_stng_k = '' | append: zp_product_setting_parts[0] | strip %}{% if zp_prd_stng_k.size < 1 %}{% continue %}{% endif %}{% assign zp_product_settings_keys = zp_product_settings_keys | append: zp_prd_stng_k | append: '__zp1502150400__' %}{% assign zp_product_setting_size = zp_product_setting | size %}{% assign zp_product_setting_key_size = '' | append: zp_product_setting_parts[0] | size | plus: 1 %}{% assign zp_prd_stng_vl = '' | append: zp_product_setting | slice: zp_product_setting_key_size, zp_product_setting_size | strip %}{% assign zp_product_setting_value_last_index = zp_prd_stng_vl | size | minus: 1 %}{% assign zp_product_setting_value_first_letter = zp_prd_stng_vl | first %}{% if zp_product_setting_value_first_letter == '"' or zp_product_setting_value_first_letter == "'" %}{% assign zp_prd_stng_vl = zp_prd_stng_vl | slice: 1, zp_product_setting_value_last_index %}{% endif %}{% assign zp_product_setting_value_last_index = zp_prd_stng_vl | size | minus: 1 %}{% assign zp_product_setting_value_last_letter = zp_prd_stng_vl | last %}{% if zp_product_setting_value_last_letter == '"' or zp_product_setting_value_last_letter == "'" %}{% assign zp_prd_stng_vl = zp_prd_stng_vl | slice: 0, zp_product_setting_value_last_index %}{% endif %}{% assign zp_product_settings_values = zp_product_settings_values | append: zp_prd_stng_vl | append: '__zp1502150400__' %}{% endfor %}{% assign zp_product_settings_keys = zp_product_settings_keys | split: '__zp1502150400__' %}{% assign zp_product_settings_values = zp_product_settings_values | split: '__zp1502150400__' %}{% assign zp_product_settings = nil %}{% assign zp_product_settings_tmp = nil %}{% assign zp_product_setting_parts = nil %}{% assign zp_tmp_render_products_from_metafield = false %}{% assign zp_tmp_product_content_parts = nil %}{% for zp_product_setting_key_data in zp_product_settings_keys %}{% if zp_tmp_render_products_from_metafield %}{% assign zp_prd_stng_k = zp_product_setting_key_data | first %}{% assign zp_unstringified_product_setting_keys = 'btns,imgs' | split: ',' %}{% if zp_unstringified_product_setting_keys contains zp_prd_stng_k %}{% assign zp_prd_stng_vl = zp_product_setting_key_data | last %}{% else %}{% assign zp_prd_stng_vl = zp_product_setting_key_data | last | strip %}{% endif %}{% assign zp_unstringified_product_setting_keys = nil %}{% else %}{% assign zp_prd_stng_k = zp_product_setting_key_data %}{% assign zp_prd_stng_vl = '' | append: zp_product_settings_values[forloop.index0] | strip %}{% endif %}{% case zp_prd_stng_k %}{% when 'handle' %}{% assign zp_tmp_product_handle = zp_prd_stng_vl %}{% when 'slctvrnt' %}{% assign zp_tmp_selected_variants = zp_prd_stng_vl %}{% when 'btnltp' %}{% assign zp_tmp_product_link_type = zp_prd_stng_vl | replace: '\', '&bsol;' %}{% when 'btns' %}{% assign zp_prdlnk_tps_str = '' %}{% for zp_prdct_sttng_val in zp_prd_stng_vl %}{% assign zp_prdlnk_tps_str = zp_prdlnk_tps_str | append: zp_prdct_sttng_val['tp'] | append: ',' %}{% endfor %}{% assign zp_tmp_product_link_types = zp_prdlnk_tps_str | split: ',' %}{% when 'imgtp' %}{% assign zp_tmp_product_image_type = zp_prd_stng_vl %}{% when 'bid' %}{% assign zp_tmp_product_block_id = zp_prd_stng_vl %}{% when 'crntentt' %}{% if zp_prd_stng_vl == 'true' or zp_prd_stng_vl == '1' %}{% assign zp_tmp_product_as_current_entity = true %}{% endif %}{% when 'imgs' %}{% assign zp_tmp_prdimgs_dt = zp_prd_stng_vl %}{% endcase %}{% endfor %}{% if zp_tmp_product_image_type == 'custom' %}{% assign zp_tmp_product_content_parts = zp_open_graph_search_content | split: '<img ' %}{% assign zp_tmp_product_content_parts = zp_tmp_product_content_parts[1] | split: '>' | first %}{% assign zp_img_src = zp_tmp_product_content_parts | append: ' ' %}{% assign zp_img_src_parts = zp_img_src | split: ' src=' %}{% if zp_img_src_parts.size <= 1 %}{% assign zp_img_src_parts = zp_img_src | split: ' data-src=' %}{% endif %}{% if zp_img_src_parts.size > 1 %}{% assign zp_current_product_og_image_url = zp_img_src_parts | last | strip | append: ' ' | split: ' ' | first | strip | remove: '"' | remove: "'" %}{% endif %}{% assign zp_img_src = nil %}{% assign zp_img_src_parts = nil %}{% assign zp_tmp_product_content_parts = nil %}{% else %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_tmp_current_product_schema_config = zp_current_entity_metafields['config'].value['schema'][zp_tmp_product_block_id] %}{% else %}{% assign zp_tmp_current_product_schema_config = zp_current_entity_metafields['config']['schema'][zp_tmp_product_block_id] %}{% endif %}{% assign zp_current_product_og_image_url = zp_tmp_current_product_schema_config['img'] %}{% assign zp_tmp_current_product_schema_config = nil %}{% endif %}{% else %}{% assign zp_tmp_opened_product_tag = '<' | append: zp_image_tag_type | append: '>' %}{% assign zp_tmp_closed_product_tag = '</' | append: zp_image_tag_type | append: '>' %}{% assign zp_settings_separator = ':|~|:' %}{% assign zp_key_separator = ':--:' %}{% assign zp_object_settings_data = zp_open_graph_search_content | append: ' ' | split: zp_tmp_closed_product_tag | first | split: zp_tmp_opened_product_tag | last %}{% assign zp_settings_keys = '' %}{% assign zp_settings_values = '' %}{% assign zp_object_settings = zp_object_settings_data | strip | split: zp_settings_separator %}{% assign zp_object_settings_data = '' %}{% for zp_setting in zp_object_settings %}{% assign zp_setting_key = zp_setting | strip | split: zp_key_separator %}{% assign zp_remove_setting_key = zp_setting_key | first %}{% assign zp_setting_key = zp_setting_key | first | downcase %}{% assign zp_settings_keys = zp_settings_keys | append: zp_settings_separator | append: zp_setting_key %}{% assign zp_remove_setting_key = zp_remove_setting_key | append: zp_key_separator %}{% assign zp_setting_value = zp_setting | remove_first: zp_remove_setting_key %}{% assign zp_settings_values = zp_settings_values | append: zp_settings_separator | append: zp_setting_value %}{% endfor %}{% assign zp_object_settings = '' %}{% assign zp_settings_keys = zp_settings_keys | remove_first: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | remove_first: zp_settings_separator %}{% assign zp_settings_keys = zp_settings_keys | split: zp_settings_separator %}{% assign zp_settings_values = zp_settings_values | split: zp_settings_separator %}{% for zp_prd_stng_k in zp_settings_keys %}{% assign zp_prd_stng_vl = '' | append: zp_settings_values[forloop.index0] %}{% case zp_prd_stng_k %}{% when 'handle' %}{% assign zp_tmp_product_handle = zp_prd_stng_vl %}{% when 'slctvrnt' %}{% assign zp_tmp_selected_variants = zp_prd_stng_vl %}{% when 'prdbtnltp' %}{% assign zp_tmp_product_link_type = zp_prd_stng_vl | replace: '\', '&bsol;' %}{% when 'prdimgtp' %}{% assign zp_tmp_product_image_type = zp_prd_stng_vl %}{% when 'imgimgattr' %}{% assign zp_tmp_product_image_attributes = zp_prd_stng_vl %}{% when 'prdimg' %}{% assign zp_tmp_product_image = zp_prd_stng_vl %}{% endcase %}{% endfor %}{% if zp_tmp_product_image_type == 'custom' and zp_tmp_product_image != blank %}{% assign zp_current_product_og_image_url = zp_tmp_product_image %}{% assign zp_tmp_product_image = nil %}{% endif %}{% endif %}{% if zp_current_product_og_image_url != blank %}{% assign zp_open_graph_image_url = zp_current_product_og_image_url %}{% assign zp_current_product_og_image_url = nil %}{% elsif zp_tmp_product_image_attributes.size > 0 %}{% assign zp_img_src = zp_tmp_product_image_attributes | append: ' ' %}{% assign zp_img_src_parts = zp_img_src | split: ' src=' %}{% if zp_img_src_parts.size <= 1 %}{% assign zp_img_src_parts = zp_img_src | split: ' data-src=' %}{% endif %}{% if zp_img_src_parts.size > 1 %}{% assign zp_open_graph_image_url = zp_img_src_parts | last | strip | append: ' ' | split: ' ' | first | strip | remove: '"' | remove: "'" %}{% endif %}{% assign zp_img_src = nil %}{% assign zp_img_src_parts = nil %}{% elsif zp_tmp_product_handle.size > 0 %}{% if zp_tmp_product_as_current_entity %}{% assign zp_tmp_product = zp_current_entity %}{% else %}{% assign zp_tmp_product = all_products[zp_tmp_product_handle] %}{% endif %}{% assign zp_tmp_product_id_size = '' | append: zp_tmp_product.id | size %}{% if zp_tmp_product_id_size > 0 %}{% assign zp_tmp_selected_variants = zp_tmp_selected_variants | split: ',' %}{% if zp_tmp_product_custom_variants == blank %}{% assign zp_tmp_product_custom_variants = zp_tmp_product.variants %}{% endif %}{% if zp_tmp_selected_variants.size < 1 %}{% assign zp_tmp_selected_variants = zp_tmp_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% endif %}{% if zp_tmp_use_product_variant_from_url %}{% assign zp_url_selected_variant_id = '' | append: zp_tmp_product.selected_variant.id %}{% else %}{% assign zp_url_selected_variant_id = '' %}{% endif %}{% assign zp_filtered_selected_variants = '' %}{% assign zp_tmp_first_selected_variant = nil %}{% assign zp_tmp_current_selected_variant = nil %}{% assign zp_tmp_any_product_variant_available = false %}{% for zp_prd_variant in zp_tmp_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_tmp_use_product_variant_from_url and zp_url_selected_variant_id == zp_variant_id and zp_tmp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_tmp_first_selected_variant = zp_prd_variant %}{% assign zp_tmp_current_selected_variant = zp_prd_variant %}{% endif %}{% if zp_tmp_selected_variants contains zp_variant_id %}{% assign zp_filtered_selected_variants = zp_filtered_selected_variants | append: zp_variant_id | append: ',' %}{% if zp_tmp_first_selected_variant == nil %}{% assign zp_tmp_first_selected_variant = zp_prd_variant %}{% endif %}{% if zp_prd_variant.available %}{% assign zp_tmp_any_product_variant_available = true %}{% endif %}{% endif %}{% endfor %}{% assign zp_tmp_selected_variants = zp_filtered_selected_variants | split: ',' %}{% if zp_tmp_selected_variants.size < 1 %}{% assign zp_tmp_selected_variants = zp_tmp_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% assign zp_tmp_any_product_variant_available = zp_tmp_product.available %}{% if zp_tmp_use_product_variant_from_url and zp_tmp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_tmp_first_selected_variant = zp_tmp_product.selected_variant %}{% assign zp_tmp_current_selected_variant = zp_tmp_product.selected_variant %}{% else %}{% assign zp_tmp_first_selected_variant = zp_tmp_product_custom_variants | first %}{% endif %}{% endif %}{% unless zp_tmp_first_selected_variant.available %}{% assign zp_tmp_first_available_variant = nil %}{% for zp_prd_variant in zp_tmp_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_tmp_selected_variants contains zp_variant_id and zp_prd_variant.available %}{% assign zp_tmp_first_available_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% unless zp_tmp_first_available_variant == nil %}{% assign zp_tmp_first_selected_variant = zp_tmp_first_available_variant %}{% endunless %}{% assign zp_tmp_first_available_variant = nil %}{% endunless %}{% assign zp_variant_id = nil %}{% assign zp_shw_vrntsslctr_lnktps = 'cart,checkout,cart_current_page,cart_external' | split: ',' %}{% if zp_tmp_product_link_types != blank %}{% assign zp_shw_vrntsslctr = false %}{% for zp_link_type in zp_tmp_product_link_types %}{% if zp_shw_vrntsslctr_lnktps contains zp_link_type %}{% assign zp_shw_vrntsslctr = true %}{% break %}{% endif %}{% endfor %}{% elsif zp_shw_vrntsslctr_lnktps contains zp_tmp_product_link_type %}{% assign zp_shw_vrntsslctr = true %}{% else %}{% assign zp_shw_vrntsslctr = false %}{% endif %}{% assign zp_show_variants_selectors = zp_shw_vrntsslctr %}{% assign zp_tmp_product_featured_image = zp_tmp_product.featured_image %}{% if zp_tmp_prdimgs_dt != blank and zp_tmp_product_image_id != blank %}{% assign zp_tmp_first_product_image_type = nil %}{% for zp_tmp_prdimg_dt in zp_tmp_prdimgs_dt %}{% assign zp_tmp_prdimg_id = '' | append: zp_tmp_prdimg_dt['id'] %}{% if zp_tmp_prdimg_id == zp_tmp_product_image_id %}{% assign zp_tmp_first_product_image_type = zp_tmp_prdimg_dt['tp'] %}{% break %}{% endif %}{% endfor %}{% if zp_tmp_first_product_image_type != blank and zp_tmp_first_product_image_type != '' %}{% assign zp_tmp_product_image_type = zp_tmp_first_product_image_type %}{% endif %}{% endif %}{% assign zp_first_selected_variant_image_id = '' | append: zp_tmp_first_selected_variant.image.id | strip %}{% if zp_tmp_product_image_type == 'variant' and zp_shw_vrntsslctr == true and zp_first_selected_variant_image_id.size > 0 %}{% assign zp_tmp_product_featured_image = zp_tmp_first_selected_variant.image %}{% endif %}{% assign zp_tmp_product_image_srcset_available = true %}{% assign zp_open_graph_image_url = zp_tmp_product_featured_image | img_url: zp_tmp_product_image_size %}{% assign zp_tmp_product_featured_image = nil %}{% assign zp_tmp_first_selected_variant = nil %}{% endif %}{% assign zp_tmp_product = nil %}{% endif %}{% elsif zp_is_default_image %}{% assign zp_open_graph_search_content = zp_open_graph_search_content | split: '>' | first %}{% assign zp_img_src = zp_open_graph_search_content | append: ' ' %}{% assign zp_img_src_parts = zp_img_src | split: ' src=' %}{% if zp_img_src_parts.size <= 1 %}{% assign zp_img_src_parts = zp_img_src | split: ' data-src=' %}{% endif %}{% if zp_img_src_parts.size > 1 %}{% assign zp_open_graph_image_url = zp_img_src_parts | last | strip | append: ' ' | split: ' ' | first | strip | remove: '"' | remove: "'" %}{% endif %}{% assign zp_img_src = nil %}{% assign zp_img_src_parts = nil %}{% if zp_open_graph_image_url contains 'zps_prdimgsrc_' or zp_open_graph_search_content contains 'zps_prdimgdattr_' or zp_open_graph_search_content contains 'zps_mprdimgdt_' %}{% assign zp_tmp_product_id = nil %}{% assign zp_tmp_product_image_id = nil %}{% if zp_open_graph_search_content contains ' data-src=' %}{% assign zp_tmp_product_og_image_url = zp_open_graph_search_content | append: ' ' | split: ' data-src=' | last | split: ' ' | first | remove: '"' | remove: "'" %}{% elsif zp_open_graph_search_content contains ' src=' %}{% assign zp_tmp_product_og_image_url = zp_open_graph_search_content | append: ' ' | split: ' src=' | last | split: ' ' | first | remove: '"' | remove: "'" %}{% endif %}{% if zp_tmp_product_og_image_url == blank or zp_tmp_product_og_image_url == '' %}{% if zp_open_graph_search_content contains 'zps_mprdimgdt_' %}{% assign zp_tmp_product_data = zp_open_graph_search_content | append: ' ' | split: 'zps_mprdimgdt_' | last | split: '_' %}{% assign zp_tmp_product_id = zp_tmp_product_data[0] %}{% assign zp_tmp_product_image_id = zp_tmp_product_data[1] %}{% elsif zp_open_graph_search_content contains 'zps_prdimgdattr_' %}{% assign zp_tmp_product_id = zp_open_graph_search_content | append: ' ' | split: 'zps_prdimgdattr_' | last | split: ' ' | first %}{% else %}{% assign zp_tmp_product_id = zp_open_graph_image_url | split: 'zps_prdimgsrc_' | last %}{% endif %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_product_settings_keys = zp_current_entity_metafields['config'].value['products'][zp_tmp_product_id] %}{% else %}{% assign zp_product_settings_keys = zp_current_entity_metafields['config']['products'][zp_tmp_product_id] %}{% endif %}{% if zp_product_settings_keys != blank %}{% assign zp_tmp_current_product_schema_config = zp_product_settings_keys['schm'] %}{% assign zp_current_product_og_image_url = zp_tmp_current_product_schema_config['img'] %}{% assign zp_tmp_current_product_schema_config = nil %}{% if zp_current_product_og_image_url == blank %}{% assign zp_tmp_product_image_size = '1080x' %}{% assign zp_open_graph_image_url = 'no-image.gif' | img_url: zp_tmp_product_image_size %}{% assign zp_tmp_product_handle = '' %}{% assign zp_tmp_selected_variants = '' %}{% assign zp_tmp_product_link_type = 'cart' %}{% assign zp_tmp_product_link_types = nil %}{% assign zp_tmp_product_image = nil %}{% assign zp_tmp_product_image_type = 'product' %}{% assign zp_tmp_product_image_attributes = '' %}{% assign zp_tmp_product_as_current_entity = false %}{% assign zp_tmp_product_block_id = nil %}{% assign zp_current_product_og_image_url = nil %}{% assign zp_tmp_prdimgs_dt = nil %}{% assign zp_tmp_render_products_from_metafield = true %}{% for zp_product_setting_key_data in zp_product_settings_keys %}{% if zp_tmp_render_products_from_metafield %}{% assign zp_prd_stng_k = zp_product_setting_key_data | first %}{% assign zp_unstringified_product_setting_keys = 'btns,imgs' | split: ',' %}{% if zp_unstringified_product_setting_keys contains zp_prd_stng_k %}{% assign zp_prd_stng_vl = zp_product_setting_key_data | last %}{% else %}{% assign zp_prd_stng_vl = zp_product_setting_key_data | last | strip %}{% endif %}{% assign zp_unstringified_product_setting_keys = nil %}{% else %}{% assign zp_prd_stng_k = zp_product_setting_key_data %}{% assign zp_prd_stng_vl = '' | append: zp_product_settings_values[forloop.index0] | strip %}{% endif %}{% case zp_prd_stng_k %}{% when 'handle' %}{% assign zp_tmp_product_handle = zp_prd_stng_vl %}{% when 'slctvrnt' %}{% assign zp_tmp_selected_variants = zp_prd_stng_vl %}{% when 'btnltp' %}{% assign zp_tmp_product_link_type = zp_prd_stng_vl | replace: '\', '&bsol;' %}{% when 'btns' %}{% assign zp_prdlnk_tps_str = '' %}{% for zp_prdct_sttng_val in zp_prd_stng_vl %}{% assign zp_prdlnk_tps_str = zp_prdlnk_tps_str | append: zp_prdct_sttng_val['tp'] | append: ',' %}{% endfor %}{% assign zp_tmp_product_link_types = zp_prdlnk_tps_str | split: ',' %}{% when 'imgtp' %}{% assign zp_tmp_product_image_type = zp_prd_stng_vl %}{% when 'bid' %}{% assign zp_tmp_product_block_id = zp_prd_stng_vl %}{% when 'crntentt' %}{% if zp_prd_stng_vl == 'true' or zp_prd_stng_vl == '1' %}{% assign zp_tmp_product_as_current_entity = true %}{% endif %}{% when 'imgs' %}{% assign zp_tmp_prdimgs_dt = zp_prd_stng_vl %}{% endcase %}{% endfor %}{% endif %}{% if zp_current_product_og_image_url != blank %}{% assign zp_open_graph_image_url = zp_current_product_og_image_url %}{% assign zp_current_product_og_image_url = nil %}{% elsif zp_tmp_product_image_attributes.size > 0 %}{% assign zp_img_src = zp_tmp_product_image_attributes | append: ' ' %}{% assign zp_img_src_parts = zp_img_src | split: ' src=' %}{% if zp_img_src_parts.size <= 1 %}{% assign zp_img_src_parts = zp_img_src | split: ' data-src=' %}{% endif %}{% if zp_img_src_parts.size > 1 %}{% assign zp_open_graph_image_url = zp_img_src_parts | last | strip | append: ' ' | split: ' ' | first | strip | remove: '"' | remove: "'" %}{% endif %}{% assign zp_img_src = nil %}{% assign zp_img_src_parts = nil %}{% elsif zp_tmp_product_handle.size > 0 %}{% if zp_tmp_product_as_current_entity %}{% assign zp_tmp_product = zp_current_entity %}{% else %}{% assign zp_tmp_product = all_products[zp_tmp_product_handle] %}{% endif %}{% assign zp_tmp_product_id_size = '' | append: zp_tmp_product.id | size %}{% if zp_tmp_product_id_size > 0 %}{% assign zp_tmp_selected_variants = zp_tmp_selected_variants | split: ',' %}{% if zp_tmp_product_custom_variants == blank %}{% assign zp_tmp_product_custom_variants = zp_tmp_product.variants %}{% endif %}{% if zp_tmp_selected_variants.size < 1 %}{% assign zp_tmp_selected_variants = zp_tmp_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% endif %}{% if zp_tmp_use_product_variant_from_url %}{% assign zp_url_selected_variant_id = '' | append: zp_tmp_product.selected_variant.id %}{% else %}{% assign zp_url_selected_variant_id = '' %}{% endif %}{% assign zp_filtered_selected_variants = '' %}{% assign zp_tmp_first_selected_variant = nil %}{% assign zp_tmp_current_selected_variant = nil %}{% assign zp_tmp_any_product_variant_available = false %}{% for zp_prd_variant in zp_tmp_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_tmp_use_product_variant_from_url and zp_url_selected_variant_id == zp_variant_id and zp_tmp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_tmp_first_selected_variant = zp_prd_variant %}{% assign zp_tmp_current_selected_variant = zp_prd_variant %}{% endif %}{% if zp_tmp_selected_variants contains zp_variant_id %}{% assign zp_filtered_selected_variants = zp_filtered_selected_variants | append: zp_variant_id | append: ',' %}{% if zp_tmp_first_selected_variant == nil %}{% assign zp_tmp_first_selected_variant = zp_prd_variant %}{% endif %}{% if zp_prd_variant.available %}{% assign zp_tmp_any_product_variant_available = true %}{% endif %}{% endif %}{% endfor %}{% assign zp_tmp_selected_variants = zp_filtered_selected_variants | split: ',' %}{% if zp_tmp_selected_variants.size < 1 %}{% assign zp_tmp_selected_variants = zp_tmp_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% assign zp_tmp_any_product_variant_available = zp_tmp_product.available %}{% if zp_tmp_use_product_variant_from_url and zp_tmp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_tmp_first_selected_variant = zp_tmp_product.selected_variant %}{% assign zp_tmp_current_selected_variant = zp_tmp_product.selected_variant %}{% else %}{% assign zp_tmp_first_selected_variant = zp_tmp_product_custom_variants | first %}{% endif %}{% endif %}{% unless zp_tmp_first_selected_variant.available %}{% assign zp_tmp_first_available_variant = nil %}{% for zp_prd_variant in zp_tmp_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_tmp_selected_variants contains zp_variant_id and zp_prd_variant.available %}{% assign zp_tmp_first_available_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% unless zp_tmp_first_available_variant == nil %}{% assign zp_tmp_first_selected_variant = zp_tmp_first_available_variant %}{% endunless %}{% assign zp_tmp_first_available_variant = nil %}{% endunless %}{% assign zp_variant_id = nil %}{% assign zp_shw_vrntsslctr_lnktps = 'cart,checkout,cart_current_page,cart_external' | split: ',' %}{% if zp_tmp_product_link_types != blank %}{% assign zp_shw_vrntsslctr = false %}{% for zp_link_type in zp_tmp_product_link_types %}{% if zp_shw_vrntsslctr_lnktps contains zp_link_type %}{% assign zp_shw_vrntsslctr = true %}{% break %}{% endif %}{% endfor %}{% elsif zp_shw_vrntsslctr_lnktps contains zp_tmp_product_link_type %}{% assign zp_shw_vrntsslctr = true %}{% else %}{% assign zp_shw_vrntsslctr = false %}{% endif %}{% assign zp_show_variants_selectors = zp_shw_vrntsslctr %}{% assign zp_tmp_product_featured_image = zp_tmp_product.featured_image %}{% if zp_tmp_prdimgs_dt != blank and zp_tmp_product_image_id != blank %}{% assign zp_tmp_first_product_image_type = nil %}{% for zp_tmp_prdimg_dt in zp_tmp_prdimgs_dt %}{% assign zp_tmp_prdimg_id = '' | append: zp_tmp_prdimg_dt['id'] %}{% if zp_tmp_prdimg_id == zp_tmp_product_image_id %}{% assign zp_tmp_first_product_image_type = zp_tmp_prdimg_dt['tp'] %}{% break %}{% endif %}{% endfor %}{% if zp_tmp_first_product_image_type != blank and zp_tmp_first_product_image_type != '' %}{% assign zp_tmp_product_image_type = zp_tmp_first_product_image_type %}{% endif %}{% endif %}{% assign zp_first_selected_variant_image_id = '' | append: zp_tmp_first_selected_variant.image.id | strip %}{% if zp_tmp_product_image_type == 'variant' and zp_shw_vrntsslctr == true and zp_first_selected_variant_image_id.size > 0 %}{% assign zp_tmp_product_featured_image = zp_tmp_first_selected_variant.image %}{% endif %}{% assign zp_tmp_product_image_srcset_available = true %}{% assign zp_open_graph_image_url = zp_tmp_product_featured_image | img_url: zp_tmp_product_image_size %}{% assign zp_tmp_product_featured_image = nil %}{% assign zp_tmp_first_selected_variant = nil %}{% endif %}{% assign zp_tmp_product = nil %}{% endif %}{% else %}{% assign zp_open_graph_image_url = '' %}{% endif %}{% else %}{% assign zp_current_product_og_image_url = zp_tmp_product_og_image_url %}{% endif %}{% endif %}{% else %}{% assign zp_open_graph_image_url = '' %}{% endif %}{% endif %}{% assign zp_open_graph_search_content = nil %}{% endif %}{% assign zp_product_settings_keys = nil %}{% endunless %}{% unless zp_use_native_og_images %}{% assign zp_open_graph_image_url = '' | append: zp_open_graph_image_url | strip | split: '?' | first %}{% assign zp_available_open_graph_image_protocols = ' http:, https:, ' | split: ',' %}{% assign zp_open_graph_image_protocol = ' ' | append: zp_open_graph_image_url | split: '//' | first %}{% if zp_available_open_graph_image_protocols contains zp_open_graph_image_protocol %}{% assign zp_available_open_graph_extensions = 'gif,jpeg,jpg,png,ico,svg,svgz,bmp,webp' | split: ',' %}{% assign zp_open_graph_image_extension = ' ' | append: zp_open_graph_image_url | split: '.' | last | strip %}{% unless zp_available_open_graph_extensions contains zp_open_graph_image_extension %}{% assign zp_open_graph_image_url = '' %}{% endunless %}{% else %}{% assign zp_open_graph_image_url = '' %}{% endif %}{% endunless %}{% capture zp_open_graph_meta_html %}<meta property="og:title" content="zps_open_graph_title" />__zpogdescstart1502150400__<meta property="og:description" content="zps_open_graph_description" />__zpogdescend1502150400__<meta property="og:type" content="{{ zp_og_entity_type }}" /><meta property="og:url" content="zps_open_graph_url" />{% if zp_use_native_og_images %}{{ zp_open_graph_image_url }}{% else %}__zpogimgstart1502150400__<meta property="og:image" content="zps_open_graph_image_url" />__zpogimgend1502150400__{% endif %}<meta name="twitter:card" content="zps_twitter_card" />{% endcapture %}{% if zp_open_graph_image_url.size > 0 %}{% assign zp_twitter_card = 'summary_large_image' %}{% else %}{% assign zp_twitter_card = 'summary' %}{% assign zp_open_graph_meta_parts = zp_open_graph_meta_html | append: ' ' | split: '__zpogimgstart1502150400__' %}{% assign zp_open_graph_meta_content_before = zp_open_graph_meta_parts | first %}{% assign zp_open_graph_meta_content_after = zp_open_graph_meta_parts | last | append: ' ' | split: '__zpogimgend1502150400__' | last %}{% assign zp_open_graph_meta_html = zp_open_graph_meta_content_before | append: zp_open_graph_meta_content_after | strip %}{% assign zp_open_graph_meta_parts = nil %}{% assign zp_open_graph_meta_content_before = nil %}{% assign zp_open_graph_meta_content_after = nil %}{% endif %}{% if zp_open_graph_description.size < 1 %}{% assign zp_open_graph_meta_parts = zp_open_graph_meta_html | append: ' ' | split: '__zpogdescstart1502150400__' %}{% assign zp_open_graph_meta_content_before = zp_open_graph_meta_parts | first %}{% assign zp_open_graph_meta_content_after = zp_open_graph_meta_parts | last | append: ' ' | split: '__zpogdescend1502150400__' | last %}{% assign zp_open_graph_meta_html = zp_open_graph_meta_content_before | append: zp_open_graph_meta_content_after | strip %}{% assign zp_open_graph_meta_parts = nil %}{% assign zp_open_graph_meta_content_before = nil %}{% assign zp_open_graph_meta_content_after = nil %}{% endif %}{% assign zp_open_graph_image_url = zp_open_graph_image_url | escape %}{% assign zp_open_graph_meta_html = zp_open_graph_meta_html | replace: 'zps_open_graph_title', zp_open_graph_title | replace: 'zps_open_graph_description', zp_open_graph_description | replace: 'zps_open_graph_url', zp_open_graph_url | replace: 'zps_open_graph_image_url', zp_open_graph_image_url | replace: 'zps_twitter_card', zp_twitter_card | remove: '__zpogimgstart1502150400__' | remove: '__zpogimgend1502150400__' | remove: '__zpogdescstart1502150400__' | remove: '__zpogdescend1502150400__' %}{% assign zp_open_graph_title = nil %}{% assign zp_open_graph_description = nil %}{% assign zp_open_graph_url = nil %}{% assign zp_open_graph_image_url = nil %}{% assign zp_twitter_card = nil %}{{ zp_open_graph_meta_html }}{% assign zp_open_graph_meta_html = nil %}{% endif %}{% if zp_use_viewport_meta == nil or zp_use_viewport_meta == true %}{% assign zp_use_viewport_meta_tag = true %}{% else %}{% assign zp_use_viewport_meta_tag = false %}{% endif %}{% if zp_current_entity.template_suffix == 'custom.zipifypages' and zp_use_viewport_meta_tag == true %}<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">{% endif %}{% if zp_gdpr_enabled == true %}<script>!function(){function e(){var e=(new Date).valueOf(),t=function(){var e;try{e=JSON.parse(localStorage.getItem("zp-geoip"))}catch(e){console.error(e)}return e}(),r=t&&t.expires>e;return t&&t.location&&r}function t(e){(document.head||document.querySelector("head")).appendChild(e)}var r,n,o;e()||((r=document.createElement("link")).setAttribute("rel","dns-prefetch"),r.setAttribute("href","https://gip.zipify.com"),t(r),(n=document.createElement("link")).setAttribute("rel","preconnect"),n.setAttribute("href","https://gip.zipify.com"),t(n),(o=document.createElement("link")).setAttribute("rel","preload"),o.setAttribute("href","https://gip.zipify.com/json/"),o.setAttribute("as","fetch"),o.setAttribute("type","application/json"),o.setAttribute("crossorigin","anonymous"),t(o))}();</script>{% endif %}{% assign zp_global_styles_data = zp_shop_metafields['globalstyles'] %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_global_styles_state = zp_current_entity_metafields['config'].value['global_styles_structure'] %}{% assign zp_current_entity_use_global_styles = true %}{% if zp_current_entity_global_styles_state == 'ordinary' %}{% assign zp_global_style_state_key = 'styles_version' %}{% elsif zp_current_entity_global_styles_state == 'hybrid' %}{% assign zp_global_style_state_key = 'hybrid_styles_version' %}{% else %}{% assign zp_global_style_state_key = nil %}{% assign zp_current_entity_use_global_styles = false %}{% endif %}{% else %}{% assign zp_current_entity_use_global_styles = false %}{% endif %}{% if zp_global_styles_data != blank and zp_current_entity_use_global_styles %}{% assign zp_use_global_styles_css = true %}{% assign zp_global_styles_data = zp_global_styles_data.value %}{% assign zp_global_style_name = zp_global_styles_data[zp_global_style_state_key] %}{% else %}{% assign zp_use_global_styles_css = false %}{% assign zp_global_styles_data = nil %}{% assign zp_global_style_name = nil %}{% endif %}{% if zp_use_global_styles_css and zp_global_styles_data['fonts_css'] != blank %}{% assign zp_use_global_styles_fonts_css = true %}{% else %}{% assign zp_use_global_styles_fonts_css = false %}{% endif %}{% if zp_current_entity_fonts_css_state != blank %}{% assign zp_use_entity_fonts_css = true %}{% else %}{% assign zp_use_entity_fonts_css = false %}{% endif %}{% if zp_alternative_entity_present and zp_alternative_entity_fonts_css_state != blank %}{% assign zp_use_alternative_entity_fonts_css = true %}{% else %}{% assign zp_use_alternative_entity_fonts_css = false %}{% endif %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_current_entity_native_lazyloading_state = zp_current_entity_metafields['config'].value['native_lzld'] %}{% if zp_current_entity_native_lazyloading_state %}{% assign zp_current_entity_use_native_lazyloading = true %}{% else %}{% assign zp_current_entity_use_native_lazyloading = false %}{% endif %}{% else %}{% assign zp_current_entity_use_native_lazyloading = false %}{% endif %}<link rel="preload" href="https://cdn03.zipify.com/css/zipifypages.css" as="style"><link rel="dns-prefetch" href="https://{{ shop.domain }}/"><link rel="dns-prefetch" href="https://cdn01.zipify.com/"><link rel="dns-prefetch" href="https://cdn02.zipify.com/"><link rel="dns-prefetch" href="https://cdn03.zipify.com/"><link rel="preconnect" href="https://{{ shop.domain }}/"><link rel="preconnect" href="https://cdn01.zipify.com/"><link rel="preconnect" href="https://cdn02.zipify.com/"><link rel="preconnect" href="https://cdn03.zipify.com/"><link rel="stylesheet" type="text/css" href="https://cdn03.zipify.com/css/zipifypages.css">{% if zp_use_global_styles_css %}<link rel="preconnect" href="https://cdn16.zipify.com/"><link rel="dns-prefetch" href="https://cdn16.zipify.com/">{% endif %}{% if zp_use_global_styles_fonts_css or zp_use_entity_fonts_css or zp_use_alternative_entity_fonts_css %}<link rel="dns-prefetch" href="https://cdn17.zipify.com/"><link rel="preconnect" href="https://cdn17.zipify.com/">{% else %}<link rel="preload" href="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js" as="script">{% endif %}{% if zp_current_entity_use_native_lazyloading == false %}<script type="module" async src="https://cdn03.zipify.com/javascript/lazysizes.min.js"></script><link rel="preload" href="https://cdn03.zipify.com/javascript/lazysizes.min.js" as="script">{% endif %}{% if zp_use_global_styles_css %}<link rel="stylesheet" type="text/css" href="{{ 'https://cdn16.zipify.com' | append: '/' | append: zp_shop_dir_name | append: '/gs/' | append: zp_global_style_name | append: '.css' }}">{% endif %}{% assign zp_global_style_name = nil %}{% if zp_alternative_entity_present == false and zp_entity_style_name != 'nostyles' %}<link rel="stylesheet" type="text/css" href="{{ 'https://cdn03.zipify.com' | append: '/' | append: zp_shop_dir_name | append: '/stylesheet/' | append: zp_entity_styles_folder | append: '/' | append: zp_entity_style_name | append: '.css' }}">{% endif %}<style>body{font-synthesis:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility}</style><script>(()=>{if(window.zpLazySizes={},!1 in HTMLImageElement.prototype){var e=document.createElement("script");e.async=!0,e.type="module",e.src="https://cdn03.zipify.com/javascript/lazysizes.min.js",document.head.appendChild(e)}"loading"in HTMLIFrameElement.prototype&&document.querySelectorAll('iframe[loading="lazy"]').forEach((e=>{e.addEventListener("load",(function t(r){e.removeEventListener("load",t),e.removeEventListener("error",t),r.target.src=r.target.dataset.src}))})),document.addEventListener("DOMContentLoaded",(()=>{if(!window.zpLazySizes?.loader){var e=document.querySelectorAll("iframe.zpa-lazyload, video.zpa-lazyload"),t=new IntersectionObserver(((e,t)=>{e.forEach((e=>{if(e.isIntersecting){const r=e.target;r.src=r.dataset.src,r.removeAttribute("data-src"),t.unobserve(r)}}))}));e?.forEach((e=>t.observe(e)))}const r=new IntersectionObserver((e=>{e.forEach((({boundingClientRect:{width:e,y:t},isIntersecting:n,target:o})=>{if(!n||!e)return;const a=Object.entries(o.__zpBgImageSrcset__);if(!a.length)return;const c=window.devicePixelRatio*e,[,s]=a.find((([e])=>e>=c))||a.at(-1);o.style.setProperty("--zp-background-image",`url("${s}")`),delete o.__zpBgImageSrcset__,r.unobserve(o)}))}),{rootMargin:"500px"});const n=[document.querySelector("#zp-blocks-json-data"),...document.querySelectorAll("[data-zp-block-configs]")].reduce(((e,t)=>e.concat(function(e){if(!e)return null;try{return JSON.parse(e.textContent)}catch{return null}}(t)||[])),[]).flatMap((e=>[e.bg,...e.children?.map((e=>e.bg))||[]])).filter(Boolean);for(const{selector:e,srcset:t}of n)for(const n of document.querySelectorAll(`.zp.${e}`))n.__zpBgImageSrcset__=t,r.observe(n)}))})();</script>{% if zp_display_content_for_header == nil or zp_display_content_for_header == true %}{% if zp_content_for_header == nil %}{% assign zp_content_for_header = content_for_header %}{% endif %}{% assign zp_content_for_header = zp_content_for_header | replace: 'window.ShopifyAnalytics.merchantGoogleAnalytics.call(', 'window.ZipifyPages.ShopifyAnalytics.init();window.ShopifyAnalytics.merchantGoogleAnalytics.call(' | replace: 'doShift(true)', '' | replace: 'bundle-upsell.smar7apps.com', '' | strip %}{% if zp_content_for_header contains 'window.ZipifyPages.ShopifyAnalytics.init()' %}{% assign zp_alternative_analytics_loading = false %}{% assign zp_app_analytics_init_script_included = true %}{% else %}{% assign zp_app_analytics_init_script_included = false %}{% endif %}{% if zp_app_analytics_init_script_included and zp_alternative_entity_present %}{% assign zp_content_for_header = zp_content_for_header | replace: 'window.ShopifyAnalytics.lib.page(', 'window.ZipifyPages.ShopifyAnalytics.page(' | strip %}{% if zp_content_for_header contains 'window.ZipifyPages.ShopifyAnalytics.page(' %}{% assign zp_alternative_analytics_loading = false %}{% endif %}{% endif %}{% assign zp_display_content_for_header_scripts = true %}{% else %}{% assign zp_display_content_for_header_scripts = false %}{% endif %}{% if zp_alternative_entity_present %}{% if zp_entity_style_name != 'nostyles' %}{% assign zp_alternative_entity_styles_url = 'https://cdn03.zipify.com' | append: '/' | append: zp_shop_dir_name | append: '/stylesheet/' | append: zp_entity_styles_folder | append: '/' | append: zp_entity_style_name | append: '.css' | json %}{% else %}{% assign zp_alternative_entity_styles_url = 'null' %}{% endif %}{% if zp_current_entity_metafields['config'].value['styles'] != blank %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['config'].value['styles'] %}{% else %}{% assign zp_entity_style_name = '' | append: zp_current_entity_metafields['stylesversion'] %}{% endif %}{% assign zp_entity_style_name = zp_entity_style_name | strip | default: 'nostyles' %}{% if zp_entity_style_name != 'nostyles' %}{% assign zp_main_entity_styles_url = 'https://cdn03.zipify.com' | append: '/' | append: zp_shop_dir_name | append: '/stylesheet/' | append: zp_entity_styles_folder | append: '/' | append: zp_entity_style_name | append: '.css' | json %}{% else %}{% assign zp_main_entity_styles_url = 'null' %}{% endif %}{% assign zp_shop_dir_name = nil %}{% assign zp_entity_style_name = nil %}{% assign zp_entity_styles_folder = nil %}{% assign zp_split_data_string = '{' %}{% assign zp_replacement_value = zp_entity_marker | json %}{% assign zp_split_data_string = zp_split_data_string | append: 'entityMarker:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_split_token | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',testToken:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_current_entity.handle | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',handles:{main:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_alternative_entity_handle | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',alternative:' | append: zp_replacement_value %}{% assign zp_split_data_string = zp_split_data_string | append: '},styles:{main:' | append: zp_main_entity_styles_url %}{% assign zp_main_entity_styles_url = nil %}{% assign zp_split_data_string = zp_split_data_string | append: ',alternative:' | append: zp_alternative_entity_styles_url %}{% assign zp_alternative_entity_styles_url = nil %}{% assign zp_replacement_value = zp_split_single_page_render | json %}{% assign zp_split_data_string = zp_split_data_string | append: '},singlePageRender:' | append: zp_replacement_value %}{% assign zp_main_entity_content_wrapper_class = "zp " | append: zp_entity_attributes_class | append: '-' | append: zp_current_entity.id %}{% if zp_entity_custom_template and zp_entity_fixed_layout != true %}{% assign zp_main_entity_content_wrapper_class = zp_main_entity_content_wrapper_class | append: ' zpa-store-header-footer' %}{% endif %}{% if zp_entity_with_default_styles %}{% assign zp_main_entity_content_wrapper_class = zp_main_entity_content_wrapper_class | append: ' zpa-default-styles-text' %}{% endif %}{% if zp_entity_fixed_layout %}{% assign zp_entity_layout_wrapper_class = ' zpa-fixed-layout' %}{% elsif zp_entity_custom_template != true %}{% assign zp_entity_layout_wrapper_class = ' zpa-wide-layout' %}{% else %}{% assign zp_entity_layout_wrapper_class = '' %}{% endif %}{% assign zp_main_entity_content_wrapper_class = zp_main_entity_content_wrapper_class | append: zp_entity_layout_wrapper_class %}{% assign zp_main_entity_content_wrapper_class = zp_main_entity_content_wrapper_class | append: ' zpa-' | append: zp_entity_marker | append: '-template' %}{% assign zp_replacement_value = zp_main_entity_content_wrapper_class | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',wrapperClasses:{main:' | append: zp_replacement_value %}{% if zp_alternative_entity_custom_template and zp_alternative_entity_fixed_layout != true %}{% assign zp_alternative_entity_content_wrapper_class = zp_alternative_entity_content_wrapper_class | append: ' zpa-store-header-footer' %}{% endif %}{% if zp_alternative_entity_with_default_styles %}{% assign zp_alternative_entity_content_wrapper_class = zp_alternative_entity_content_wrapper_class | append: ' zpa-default-styles-text' %}{% endif %}{% if zp_alternative_entity_fixed_layout %}{% assign zp_entity_layout_wrapper_class = ' zpa-fixed-layout' %}{% elsif zp_alternative_entity_custom_template != true %}{% assign zp_entity_layout_wrapper_class = ' zpa-wide-layout' %}{% else %}{% assign zp_entity_layout_wrapper_class = '' %}{% endif %}{% assign zp_alternative_entity_content_wrapper_class = zp_alternative_entity_content_wrapper_class | append: zp_entity_layout_wrapper_class %}{% assign zp_alternative_entity_content_wrapper_class = zp_alternative_entity_content_wrapper_class | append: ' zpa-' | append: zp_entity_marker | append: '-template' %}{% assign zp_replacement_value = zp_alternative_entity_content_wrapper_class | json %}{% assign zp_alternative_entity_content_wrapper_class = nil %}{% assign zp_split_data_string = zp_split_data_string | append: ',alternative:' | append: zp_replacement_value %}{% assign zp_replacement_value = '' | append: zp_current_entity.title | json %}{% assign zp_split_data_string = zp_split_data_string | append: '},titles:{main:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_alternative_entity_title | json %}{% assign zp_alternative_entity_title = nil %}{% assign zp_split_data_string = zp_split_data_string | append: ',alternative:' | append: zp_replacement_value %}{% assign zp_replacement_value = '{' | append: 'classes:false,timeout:9000,' | append: zp_entity_fonts_settings | append: '}' %}{% assign zp_entity_fonts_settings = nil %}{% assign zp_split_data_string = zp_split_data_string | append: '},fonts:{main:' | append: zp_replacement_value %}{% assign zp_replacement_value = '{' | append: 'classes:false,timeout:9000,' | append: zp_alternative_fonts_settings | append: '}' %}{% assign zp_alternative_fonts_settings = nil %}{% assign zp_split_data_string = zp_split_data_string | append: ',alternative:' | append: zp_replacement_value %}{% assign zp_split_data_string = zp_split_data_string | append: '},fontsCss:{main:' | append: zp_use_entity_fonts_css %}{% assign zp_split_data_string = zp_split_data_string | append: ',alternative:' | append: zp_use_alternative_entity_fonts_css %}{% assign zp_replacement_value = zp_analytics_settings | json %}{% assign zp_split_data_string = zp_split_data_string | append: '},analyticsSettings:{main:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_alternative_analytics_settings | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',alternative:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_split_dataset | json %}{% assign zp_split_data_string = zp_split_data_string | append: '},dataSet:' | append: zp_replacement_value %}{% assign zp_replacement_value = '' | append: zp_alternative_last_sync | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',lastSync:' | append: zp_replacement_value %}{% assign zp_replacement_value = routes.root_url | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',locale:{root:' | append: zp_replacement_value | append: '}}' %}{% assign zp_replacement_value = nil %}{% assign zp_split_dataset = nil %}<script>!function(t){function e(){}function n(){this.storage=C?window.sessionStorage:null}function r(){this.events={}}function o(t){this.defaultVariation="a";try{this.data=JSON.parse(t)}catch(t){this.data=null}}function i(){}function a(){this.storage=new i}function s(t,e){this.mainState="main",this.scopes={"article":"articles","blog":"blogs","page":"pages","product":"products"},this.testToken=""+e,this.entityType=""+t,this.availableStates={a:"main",b:"alternative"},this.stateStorage=new a,this.state=this.getStoredState()}function p(t){this.localeData=t}function c(t){this.locale=new p(t)}function u(t){t=t||{};this.state=t.state,this.scope=t.scope,this.handles=t.handles||{},this.titles=t.titles||{},this.styles=t.styles||{},this.wrapperClasses=t.wrapperClasses||{},this.fonts=t.fonts||{},this.fontsCss=t.fontsCss||{},this.analyticsSettings=t.analyticsSettings||{},this.route=new c(t.localeData)}function l(){}function h(){}function y(){}function f(){}function g(){}function S(){}function d(){this.urlHelper=new S}function m(){}function E(){_=!0,N.trigger("entityPrepared")}function v(){M=!0,N.trigger("entityStateLoaded")}function I(){R.entity={getContent:A.getContent.bind(A)}}function P(){return q.rootPath()}String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}),e.prototype.request=function(t){var e=new XMLHttpRequest,n=this.prepareParams(t);e.timeout=n.timeout,e.onload=function(){e.readyState===XMLHttpRequest.DONE&&(200===e.status?n.onSuccess(e.responseText,e):n.onError(e,null))},e.ontimeout=function(t){n.onTimeout(e,t)},e.onerror=function(t){n.onError(e,t)},e.open(n.method,n.url,!0);for(var r,o=0,i=n.headers.length;o<i;o++)r=n.headers[o],e.setRequestHeader(r[0],r[1]);e.send()},e.prototype.prepareParams=function(t){return{url:t.url,method:t.method||"GET",timeout:t.timeout||0,onSuccess:t.onSuccess||function(){},onError:t.onError||function(){},onTimeout:t.onTimeout||function(){},headers:t.headers||[]}},n.prototype.getItem=function(){return this.storage?this.storage.getItem.apply(this.storage,arguments):null},n.prototype.setItem=function(){this.storage&&this.storage.setItem.apply(this.storage,arguments)},r.prototype.on=function(t,e,n){"function"==typeof e&&(this.events[t]=this.events[t]||[],this.events[t].push([e,n]))},r.prototype.trigger=function(t,e){for(var n,r=this.events[t]||[],o=0,i=r.length;o<i;o++)(n=r[o])[0].call(n[1],e)},o.prototype.selectVariation=function(){if(!this.data)return this.defaultVariation;try{var t,e=[];for(a in this.data)this.data.hasOwnProperty(a)&&(t=parseFloat(this.data[a]),isNaN(t)||e.push([t,a]));if(1<e.length){for(var n,r,o=0,i=null,a=0,s=e.length;a<s;a++)o+=e[a][0];for(r=Math.random()*o,a=0,s=e.length;a<s;a++){if(r<=(n=e[a][0])){i=e[a][1];break}r-=n}return i||this.defaultVariation}return this.defaultVariation}catch(t){return console.error("selectVariation error",t),this.defaultVariation}},i.prototype.getItem=function(t){t=document.cookie.match(new RegExp("(?:^|; )"+t.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g,"\\$1")+"=([^;]*)"));return t?decodeURIComponent(t[1]):void 0},i.prototype.setItem=function(t,e,n){var r,o=(n=n||{}).expires;"number"==typeof o&&o&&((r=new Date).setTime(r.getTime()+1e3*o),o=n.expires=r),o&&o.toUTCString&&(n.expires=o.toUTCString());var i,a,s=t+"="+(e=encodeURIComponent(e));for(i in n)n.hasOwnProperty(i)&&(s+="; "+i,!0!==(a=n[i])&&(s+="="+a));document.cookie=s},i.prototype.removeItem=function(t){this.setItem(t,"",{expires:-1})},a.prototype.getItem=function(){return this.storage.getItem.apply(this.storage,arguments)},a.prototype.setItem=function(){this.storage.setItem.apply(this.storage,arguments)},s.prototype.getAvailableState=function(t){return this.availableStates[t]||this.mainState},s.prototype._getStoredKey=function(){return"zpCurrentState"+this.entityType+this.testToken},s.prototype.getState=function(){return this.state},s.prototype.setState=function(t){return this.state=t,this.stateStorage.setItem(this._getStoredKey(),this.state, {expires:31536e3}),this.state},s.prototype.setMainState=function(){return this.setState(this.mainState)},s.prototype.getStoredState=function(){return this.stateStorage.getItem(this._getStoredKey())},s.prototype.currentScope=function(){return this.scopes[this.entityType]},s.prototype.isMainState=function(){return this.getState()===this.mainState},s.prototype.getMainStateValue=function(){return this.mainState},p.prototype.rootPath=function(){return this.localeData.root},p.prototype.baseRootPath=function(){return"/"},p.prototype.isChanged=function(){return this.baseRootPath()!==this.rootPath()},c.prototype.getRequestPath=function(t){return this.locale.isChanged()?this.locale.rootPath()+t:t},u.prototype.setState=function(t){return this.state=t,this.state},u.prototype.entityHandle=function(){return this.handles[this.state]},u.prototype.entityTitle=function(){return this.titles[this.state]},u.prototype.entityStylesLink=function(){return this.styles[this.state]},u.prototype.entityWrapperClasses=function(){return this.wrapperClasses[this.state]},u.prototype.entityFontsSettings=function(){return this.fonts[this.state]},u.prototype.entityFontsCssState=function(){return this.fontsCss[this.state]},u.prototype.entityAnalyticsSettings=function(){return this.analyticsSettings[this.state]},u.prototype.entityPath=function(){if(this.scope&&this.entityHandle()){var t="/"+this.scope+"/"+this.entityHandle();return this.route.getRequestPath(t)}return null},u.prototype.entityUrl=function(){var t=this.entityPath();return t?"https://{{ shop.domain }}"+t:null},l.prototype.loadCurrentState=function(){C||T?N.trigger("currentStateSelected",V.selectVariation()):N.trigger("currentStateSelectionError","storage unavailable")},l.prototype.loadEntitySource=function(){var t=j.entityPath();if(t)if(D.isMainState())E();else try{b.request({url:t,onSuccess:function(t){N.trigger("alternativeSourceLoaded",t)},onError:function(t){N.trigger("alternativeSourceLoadingError",t)},onTimeout:function(t){N.trigger("alternativeSourceTimeoutError",t)}})}catch(t){N.trigger("alternativeSourceLoadingError",t)}else N.trigger("alternativeSourceLoadingError","currentScope or currentEntityHandle is empty")},h.prototype.saveEntityParts=function(t){t=this.convertHtmlToDOM(t);x.setItem("zipifyPagesEntityLastSync",L),x.setItem("zipifyPagesEntityContent",this.prepareEntityContent(t)),x.setItem("zipifyPagesEntityLocale",P())},h.prototype.convertHtmlToDOM=function(t){return k.htmlToDOM(t)},h.prototype.prepareEntityContent=function(t){t=t.querySelector("#zp-entity-content-wrapper");return t?("string"==typeof t.innerHTML?t.innerHTML:"").trim():""},h.prototype.getContent=function(){return x.getItem("zipifyPagesEntityContent")},h.prototype.isCachedContentExpired=function(){var t=parseInt(x.getItem("zipifyPagesEntityLastSync"));return(t=isNaN(t)?-1:t)<L},h.prototype.isLocaleWasChanged=function(){var t=x.getItem("zipifyPagesEntityLocale");return P()!==t},y.prototype.parse=function(t){if("string"!=typeof t)return[];var e=document.createElement("div");return e.innerHTML=t,e},y.prototype.htmlToDOM=function(t){var e=document.createElement("html");return e.innerHTML=t,e},y.prototype.parseAndAppendElement=function(t,e,n,r){var o=null,i=e||document.getElementsByTagName("body")[0],e=i&&void 0!==i.appendChild,a=!1,s="function"==typeof r;if(t&&e)try{switch(t.nodeType){case 3:var p=t.nodeValue||"",o=0<p.length?document.createTextNode(p):null;break;case 1:var c=(t.nodeName||"").toLowerCase();o="svg"===c||"use"===c?document.createElementNS("http://www.w3.org/2000/svg",t.nodeName):document.createElement(t.nodeName);for(var u=t.attributes.length-1;0<=u;u--){var l,h=""+t.attributes[u].name;"use"===c&&h.match(/^xlink:/)?(l=h.split(":"),o.setAttributeNS("http://www.w3.org/1999/xlink",l[1],t.attributes[u].value)):o.setAttribute(h,t.attributes[u].value)}o.innerHTML=n?t.innerHTML:"","script"===c&&null!==o.getAttribute("src")&&null===o.getAttribute("async")&&(a=!0,s&&(o.onload=function(){r(o)},o.onerror=function(){r(o)}))}o?(i.appendChild(o),!a&&s&&r(o)):s&&r()}catch(t){console.error(t),s&&r()}else s&&r()},y.prototype.appendSingleElement=function(e,n,t){var r=t||0,o=this,i=e[r];i&&n&&(t=i.childNodes.length<1,o.parseAndAppendElement(i,n,t,function(t){0<i.childNodes.length?o.appendSingleElement(i.childNodes,t,0):o.appendSingleElement(e,n,r+1)}),0<i.childNodes.length&&0<e.length&&r<e.length&&o.appendSingleElement(e,n,r+1))},y.prototype.unescapeScriptTags=function(t){return"string"==typeof t?t.replace(/<\\\/script>/g,"<\/script>"):""},f.prototype.run=function(t,e,n){t[n||0](this._itemCallback.bind(this,t,e,n))},f.prototype._itemCallback=function(t,e,n){n=(n||0)+1;n===t.length?e&&e():this.run(t,e,n)},f.prototype.generateRunList=function(t,n){var r=[];return[].forEach.call(t,function(e){r.push(function(t){n(e,t)})}),r},g.prototype.recreateScriptElement=function(t,e){for(var n=document.createElement("script"),r=t.attributes,o=0;o<r.length;o++)n.setAttribute(r[o].name,r[o].value);return t.src?t.async||(n.onload=e,n.onerror=e):n.textContent=t.innerText,n},g.prototype.replaceScriptElement=function(t,e,n){e.insertAdjacentElement("afterend",t),e.parentNode.removeChild(e),e.src&&!e.async||n&&n()},g.prototype.getScriptElements=function(t){var t=t.querySelectorAll("script"),n=["application/javascript","application/ecmascript","application/x-ecmascript","application/x-javascript","text/ecmascript","text/javascript","text/javascript1.0","text/javascript1.1","text/javascript1.2","text/javascript1.3","text/javascript1.4","text/javascript1.5","text/jscript","text/livescript","text/x-ecmascript","text/x-javascript","module"],r=[];return[].forEach.call(t,function(t){var e=t.getAttribute("type");e&&-1===n.indexOf(e)||r.push(t)}),r},g.prototype.runScripts=function(t){t=this.getScriptElements(t);t.length&&(t=z.generateRunList(t,this.runScript.bind(this)),z.run(t))},g.prototype.runScript=function(t,e){var n=this.recreateScriptElement(t,e);this.replaceScriptElement(n,t,e)},S.prototype.parseUrl=function(t){var e=(""+t).trim();if(!/^\//.test(e)&&!/^https?:\/\//.test(e))return{};e=document.createElement("a");return e.href=t,{protocol:e.protocol,host:e.host,hostname:e.hostname,port:e.port,pathname:e.pathname,hash:e.hash,search:e.search,origin:e.origin}},S.prototype.searchQueryParams=function(t){t=this.parseUrl(t);return t.search?(""+t.search).replace(/^\?+/,""):""},S.prototype.filterSearchParams=function(t,e){if(!(t&&e instanceof RegExp))return t;for(var n,r=this.splitSearchParams(t),o=[],i=0,a=r.length;i<a;i++)n=r[i],e.test(n)||o.push(n);return o.join("&")},S.prototype.splitSearchParams=function(t){return t?(""+(""+t).replace(/^\?+/,"")).split("&"):[]},d.prototype.searchIdentifierParam=function(){return"zpstest={{ zp_split_token | url_escape }}"},d.prototype.getSearchParams=function(){var t=[this.searchIdentifierParam()],e=this.urlHelper.searchQueryParams(window.location.href).trim();return 0<e.length&&t.push(this._filterSearchParams(e)),t.join("&")},d.prototype._filterSearchParams=function(t){var e=new RegExp("^zpstest=");return this.urlHelper.filterSearchParams(t,e)},m.prototype.currentEntity=function(){return D.getState()},m.prototype.isSinglePageRender=function(){return T},m.prototype.isMainEntity=function(){return D.isMainState()},m.prototype.mainEntityState=function(){return D.getMainStateValue()},m.prototype.currentEntityTitle=function(){return j.entityTitle()},m.prototype.currentEntityStylesLink=function(){return j.entityStylesLink()},m.prototype.currentEntityWrapperClasses=function(){return j.entityWrapperClasses()},m.prototype.currentEntityFontsSettings=function(){return j.entityFontsSettings()},m.prototype.currentEntityFontsCssState=function(){return j.entityFontsCssState()},m.prototype.currentEntityAnalyticsSettings=function(){return j.entityAnalyticsSettings()},m.prototype.currentEntityPath=function(){return j.entityPath()},m.prototype.currentEntityUrl=function(){return j.entityUrl()},m.prototype.searchIdentifierParam=function(){return O.searchIdentifierParam()},m.prototype.currentEntitySearchParams=function(){return O.getSearchParams()},m.prototype.onEntityStateLoaded=function(t){"function"==typeof t&&(M?t.call(this):N.on("entityStateLoaded",t.bind(this)))},m.prototype.onEntityPrepared=function(t){"function"==typeof t&&(_?t.call(this):N.on("entityPrepared",t.bind(this)))},m.prototype.onEntityContentInjected=function(t){"function"==typeof t&&(H?t.call(this):N.on("entityContentInjected",t.bind(this)))},m.prototype.prepareAndAppendHTML=function(t,e){t=k.unescapeScriptTags(t),t=k.parse(t);"append"in window.Element.prototype?(U.runScripts(t),Array.prototype.slice.call(t.childNodes).forEach(function(t){e.append(t)})):k.appendSingleElement(t.childNodes,e,0)},m.prototype.entitiesWrapperClasses=function(){return t.wrapperClasses},m.prototype.triggerEntityContentInjection=function(){H=!0,N.trigger("entityContentInjected")};var w,T=!!t.singlePageRender,C=function(){try{return window.sessionStorage.setItem("zipifyPagesSTStorage","true"),window.sessionStorage.removeItem("zipifyPagesSTStorage"),!0}catch(t){return console.error("checkStorageAccessibilityError",t),!1}}(),L=(w=t.lastSync?parseInt(t.lastSync):(new Date).getTime(),isNaN(w)?0:w),x=new n,b=new e,A=new h,N=new r,M=!1,_=!1,H=!1,R=new m,F=new l,D=new s(t.entityMarker,t.testToken),j=new u({state:D.getState(),scope:D.currentScope(),localeData:t.locale,handles:t.handles,titles:t.titles,styles:t.styles,wrapperClasses:t.wrapperClasses,fonts:t.fonts,fontsCss:t.fontsCss,analyticsSettings:t.analyticsSettings}),k=new y,O=new d,U=new g,z=new f,V=new o(t.dataSet),q=new p(t.locale);N.on("entityStateLoaded",function(){R.isSinglePageRender()?E():A.isLocaleWasChanged()||A.isCachedContentExpired()?F.loadEntitySource():(I(),E())}),N.on("alternativeSourceLoaded",function(t){A.saveEntityParts(t),I(),E()}),N.on("alternativeSourceLoadingError",function(t){console.error("alternativeSourceLoadingError",t),D.setMainState(),j.setState(D.getState()),E()}),N.on("alternativeSourceTimeoutError",function(t){console.error("alternativeSourceTimeoutError",t),D.setMainState(),j.setState(D.getState()),E()}),N.on("currentStateSelected",function(t){t=D.getAvailableState(t);D.setState(t),j.setState(D.getState()),v()}),N.on("currentStateSelectionError",function(t){console.error("currentStateSelectionError",t),D.setMainState(),j.setState(D.getState()),v()}),D.getState()?v():F.loadCurrentState(),window.ZipifyPages=window.ZipifyPages||{},window.ZipifyPages.SplitTest=R}({{ zp_split_data_string }});</script>{% assign zp_split_data_string = nil %}<script>!function(){var t=!1;window.ZipifyPages.SplitTest.onEntityStateLoaded(function(){if(!t){t=!0;var e=this.currentEntityStylesLink();if(e){var i=document.createElement("link");i.setAttribute("rel","stylesheet"),i.setAttribute("type","text/css"),i.setAttribute("href",e),document.getElementsByTagName("head")[0].appendChild(i)}}})}();</script><script>!function(){var t=!1;window.ZipifyPages.SplitTest.onEntityStateLoaded(function(){if(!t){t=!0;var i=this.currentEntityAnalyticsSettings();try{window.ZipifyPages.analytics=i||{}}catch(i){window.ZipifyPages.analytics={}}}})}();</script>{% elsif zp_split_test_redirect or zp_split_test_view_type_redirect %}<script>!function(){function r(){}function e(){this.urlHelper=new r}function t(){}r.prototype.parseUrl=function(r){var e=(""+r).trim();if(!/^\//.test(e)&&!/^https?:\/\//.test(e))return{};e=document.createElement("a");return e.href=r,{protocol:e.protocol,host:e.host,hostname:e.hostname,port:e.port,pathname:e.pathname,hash:e.hash,search:e.search,origin:e.origin}},r.prototype.searchQueryParams=function(r){r=this.parseUrl(r);return r.search?(""+r.search).replace(/^\?+/,""):""},r.prototype.filterSearchParams=function(r,e){if(!(r&&e instanceof RegExp))return r;for(var t,o=(""+(""+r).replace(/^\?+/,"")).split("&"),a=[],n=0,p=o.length;n<p;n++)t=o[n],e.test(t)||a.push(t);return a.join("&")},e.prototype.searchIdentifierParam=function(){return"zpstest={{ zp_split_token | url_escape }}"},e.prototype.getSearchParams=function(){var r=[this.searchIdentifierParam()],e=this.urlHelper.searchQueryParams(window.location.href).trim();return 0<e.length&&r.push(this._filterSearchParams(e)),r.join("&")},e.prototype._filterSearchParams=function(r){var e=new RegExp("^zpstest=");return this.urlHelper.filterSearchParams(r,e)},t.prototype.searchIdentifierParam=function(){return a.searchIdentifierParam()},t.prototype.currentEntitySearchParams=function(){return a.getSearchParams()};var a=new e;window.ZipifyPages=window.ZipifyPages||{},window.ZipifyPages.SplitTestRedirect=new t}();</script>{% endif %}{% if zp_alternative_entity_present and zp_use_alternative_entity_fonts_css == false %}{% assign zp_include_alternative_webfont_loader = true %}{% else %}{% assign zp_include_alternative_webfont_loader = false %}{% endif %}{% if zp_use_entity_fonts_css == false or zp_use_global_styles_fonts_css == false or zp_include_alternative_webfont_loader %}{% if zp_use_global_styles_fonts_css %}{% assign zp_global_styles_webfont_fonts_data = 'null' %}{% else %}{% assign zp_global_styles_webfont_fonts_data = zp_global_styles_data['font_families'] | default: 'null' | replace: '=>', ':' %}{% endif %}<script>!function(){function t(){this.customFontsStylesUrl="{{ zp_fonts_style_url }}",this.globalStylesFonts={{ zp_global_styles_webfont_fonts_data }}||{},this.webFontScriptLoadInit=!1,this.webFontScriptLoaded=!1,this.webFontQueue=[]}t.prototype.load=function(t){var o={google:(o=t||{}).google||{},custom:o.custom||{}},e=this._combineFonts(this.globalStylesFonts,o),n=this._prepareWebFontFontsStyles(e.google),t=this._prepareWebFontFontsStyles(e.custom),e=(o.custom.urls||[])[0]||this.customFontsStylesUrl,o={};0<n.length&&(o.google={families:n}),0<t.length&&(o.custom={families:t,urls:[e]}),this._loadFonts(o)},t.prototype._combineFonts=function(t,o){t=this._collectFonts(t),o=this._collectFonts({google:(o.google||{}).families,custom:(o.custom||{}).families});return this._combineFontsStyles(t,o)},t.prototype._collectFonts=function(t){return{google:this._collectFontsData(t.google||{}),custom:this._collectFontsData(t.custom||{})}},t.prototype._collectFontsData=function(t){for(var o,e={},n=0,s=t.length;n<s;n++)(o=this._prepareFontData(t[n])).family&&o.styles&&(e[o.family]=o.styles);return e},t.prototype._prepareFontData=function(t){t=(""+t).split(":");return{family:t[0],styles:(""+t[1]).split(",")}},t.prototype._combineFontsStyles=function(t,o){for(var e,n,s={},i=Object.keys(t),l=0,r=i.length;l<r;l++)e=i[l],n=this._collectFontFamilies(t[e],o[e]),s[e]=this._groupFontsStylesByFamily(n,t[e],o[e]);return s},t.prototype._collectFontFamilies=function(t,o){return Object.keys(t).concat(Object.keys(o)).filter(function(t,o,e){return e.indexOf(t)===o})},t.prototype._groupFontsStylesByFamily=function(t,o,e){for(var n,s={},i=0,l=t.length;i<l;i++)s[n=t[i]]=(o[n]||[]).concat(e[n]||[]).filter(function(t,o,e){return e.indexOf(t)===o});return s},t.prototype._prepareWebFontFontsStyles=function(t){for(var o,e=[],n=Object.keys(t),s=0,i=n.length;s<i;s++)o=n[s],e.push([o,t[o].join(",")].join(":"));return e},t.prototype._loadWebFontScript=function(){var t;this.webFontScriptLoadInit||(this.webFontScriptLoadInit=!0,(t=document.createElement("script")).src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js",t.type="text/javascript",t.async=!0,t.onload=this._webFontLoadFonts.bind(this),document.getElementsByTagName("head")[0].appendChild(t))},t.prototype._loadFonts=function(t){this.webFontScriptLoaded?this._webFontLoadFontsConfig(t):this.webFontQueue.push(t)},t.prototype._webFontLoadFonts=function(){this.webFontScriptLoaded=!0;for(var t=0,o=this.webFontQueue.length;t<o;t++)this._webFontLoadFontsConfig(this.webFontQueue[t])},t.prototype._webFontLoadFontsConfig=function(t){t&&(t.google||t.custom)&&window.WebFont.load(t)},window.ZipifyPages=window.ZipifyPages||{},window.ZipifyPages.FontsLoader=new t,window.ZipifyPages.FontsLoader._loadWebFontScript()}();</script>{% endif %}{% if zp_alternative_entity_present %}<script>!function(){var n=!1;window.ZipifyPages.SplitTest.onEntityStateLoaded(function(){if(!n){var t=!(n=!0),o=this.currentEntityFontsCssState(),i=this.currentEntityFontsSettings();if(!o&&i)try{t=!0,window.ZipifyPages.FontsLoader.load(i)}catch(t){console.error(t)}if(!{{ zp_use_global_styles_fonts_css }}&&!t)try{window.ZipifyPages.FontsLoader.load()}catch(t){console.error(t)}}})}();</script>{% elsif zp_use_entity_fonts_css %}{% unless zp_use_global_styles_fonts_css %}<script>try{window.ZipifyPages.FontsLoader.load()}catch(e){console.error(e)}</script>{% endunless %}{% else %}{% assign zp_entity_fonts_settings = '{' | append: 'classes:false,timeout:9000,' | append: zp_entity_fonts_settings | append: '}' %}<script>try{window.ZipifyPages.FontsLoader.load({{ zp_entity_fonts_settings }})}catch(e){console.error(e)}</script>{% assign zp_entity_fonts_settings = '' %}{% endif %}{% capture zp_entity_data %}
  {% if zp_use_favicon == nil or zp_use_favicon == true %}
    {% assign zp_custom_favicon = zp_shop_metafields['faviconpath'] | strip | replace: 'https://s3.amazonaws.com/shopify-pages-development/shops/', 'https://cdn02.zipify.com/' %}
    {% if zp_custom_favicon.size > 0 %}
      <link rel="shortcut icon" href="{{ zp_custom_favicon }}" type="image/png"/>
    {% else %}
      {% capture zp_replace_integration %}<zp_favicon_integration><link rel="shortcut icon" href="{{ 'favicon.png' | asset_url }}" type="image/png" /></zp_favicon_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_favicon_integration>', '' | replace: '</zp_favicon_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}
    {% endif %}
  {% endif %}
{% endcapture %}{{ zp_entity_data | strip }}{% assign zp_entity_data = nil %}<script>!function(){window.ZipifyPages=window.ZipifyPages||{},window.ZipifyPages.currency={{ shop.currency | json }},window.ZipifyPages.shopDomain={{ shop.permanent_domain | json }},window.ZipifyPages.moneyFormat={{ shop.money_format | json }},window.ZipifyPages.shopifyOptionsSelector={{ zp_shopify_options_selector | default: 'false' }},window.ZipifyPages.unitPriceEnabled={{ zp_unit_price_enabled | default: 'false' }},window.ZipifyPages.alternativeAnalyticsLoading={{ zp_alternative_analytics_loading | default: 'false' }},window.ZipifyPages.integrations="{{ zp_js_config_app_integrations | join: ',' }}".split(","),window.ZipifyPages.crmUrl="https://crms.zipify.com",window.ZipifyPages.translations={soldOut:{% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation | json }},unavailable:{% assign zp_translation = 'products.product.unavailable' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Unavailable" %}{% endif %}{{ zp_translation | json }}},window.ZipifyPages.routes={root:{{ routes.root_url | json }},cart:{index:{{ routes.cart_url | json }},add:{{ routes.cart_add_url | json }},change:{{ routes.cart_change_url | json }},clear:{{ routes.cart_clear_url | json }},update:{{ routes.cart_update_url | json }}}};try{window.ZipifyPages.analytics=window.ZipifyPages.analytics||{{ zp_analytics_settings | json }}||{}}catch(i){window.ZipifyPages.analytics=window.ZipifyPages.analytics||{}}window.ZipifyPages.eventsSubscriptions=[],window.ZipifyPages.on=function(i,a){window.ZipifyPages.eventsSubscriptions.push([i,a])}}();</script><script>!function(){var e=!1,t=[],n=!1,i=!1;function r(){}function a(e){this.integrations=e||[]}function o(){window.trekkie.url=function(){var e=l();if(e)return e;var t=this.canonical();if(t)return t.indexOf("?")>0?t:t+s(window.location.search);var n=window.location.href,i=n.indexOf("#");return-1===i?n:n.slice(0,i)}}function c(){window.trekkie.pageDefaults=function(){var e=window.location.href,t=e.indexOf("?");return t=(e=-1===t?"":e.slice(t)).indexOf("#"),e="?"===(e=-1===t?e:e.slice(0,t))?"":e,{path:w(),referrer:document.referrer,search:d()||e,title:u(),url:this.url(),properties:{}}}}function f(){var e=window.trekkie.generatePageObject;window.trekkie.generatePageObject=function(){var t=e.apply(window.trekkie,arguments);t.path=w(),t.title=u(),t.search&&(t.search=s(t.search));var n=v&&"function"==typeof y.searchIdentifierParam?y.searchIdentifierParam():P&&"function"==typeof g.searchIdentifierParam?g.searchIdentifierParam():null;return n?(t.search?t.search="?"+n+"&"+(""+t.search).replace(/^\?+/,""):t.search="?"+n,t):t}}function s(e){var t=new RegExp("^zpstest=");return S.filterSearchParams(e,t)}function w(){return v&&"function"==typeof y.currentEntityPath&&y.currentEntityPath()?y.currentEntityPath():window.location.pathname}function u(){return v&&"function"==typeof y.currentEntityTitle&&y.currentEntityTitle()?y.currentEntityTitle():document.title}function d(){return v&&"function"==typeof y.currentEntitySearchParams?"?"+y.currentEntitySearchParams():P&&"function"==typeof g.currentEntitySearchParams?"?"+g.currentEntitySearchParams():s(window.location.search)}function p(){return v&&"function"==typeof y.currentEntityUrl?y.currentEntityUrl():P?function(){var e=window.location.href,t=e.indexOf("?");t>-1&&(e=e.slice(0,t));(t=e.indexOf("#"))>-1&&(e=e.slice(0,t));return e}():null}function l(){var e=p();if(e){var t=d();return t?e+t:e}return null}function h(){if(!i&&void 0!==window.ga){i=!0;var e=l(),t=function(){var e=w();if(e){var t=d();return t?e+t:e}return null}(),n=u();e&&window.ga("set","location",e),t&&window.ga("set","page",t),n&&window.ga("set","title",n)}}r.prototype.parseUrl=function(e){var t=(""+e).trim();if(!/^\//.test(t)&&!/^https?:\/\//.test(t))return{};var n=document.createElement("a");return n.href=e,{protocol:n.protocol,host:n.host,hostname:n.hostname,port:n.port,pathname:n.pathname,hash:n.hash,search:n.search,origin:n.origin}},r.prototype.searchQueryParams=function(e){var t=this.parseUrl(e);return t.search?(""+t.search).replace(/^\?+/,""):""},r.prototype.filterSearchParams=function(e,t){if(!(e&&t instanceof RegExp))return e;for(var n,i=this.splitSearchParams(e),r=[],a=0,o=i.length;a<o;a++)n=i[a],t.test(n)||r.push(n);return r.join("&")},r.prototype.splitSearchParams=function(e){return e?(""+(""+e).replace(/^\?+/,"")).split("&"):[]},a.prototype.init=function(){e||(e=!0,function(){for(var e=0,n=t.length;e<n;e++)t[e].call();t=[]}())},a.prototype.ready=function(n){"function"==typeof n&&(e?n.call():t.push(n))},a.prototype.page=function(){if(!n){n=!0;var e=arguments;m?(o(),f(),c(),v&&"function"==typeof y.onEntityStateLoaded?y.onEntityStateLoaded((function(){window.trekkie.page.call(window.trekkie,e)})):window.trekkie.page.call(window.trekkie,e)):window.trekkie.page.call(window.trekkie,e)}},window.ZipifyPages=window.ZipifyPages||{};var y=window.ZipifyPages.SplitTest,g=window.ZipifyPages.SplitTestRedirect,k=new a(window.ZipifyPages.integrations),v=void 0!==y,P=void 0!==g,m=v||P,S=new r;function E(){window.Shopify.analytics.publish("zp_page_viewed",{handle:window.location.pathname,title:document.title,shopData:{presentmentCurrency:{code:Shopify.currency.active,rate:Shopify.currency.rate},shopCurrencyCode:ZipifyPages.currency},splitTestData:{handle:window.ZipifyPages.SplitTest?.currentEntityUrl(),title:u(),test_token:window.ZipifyPages.SplitTestRedirect?.searchIdentifierParam()??window.ZipifyPages.SplitTest?.searchIdentifierParam()}})}window.ZipifyPages.ShopifyAnalytics=k,m&&(window.GoogleAnalyticsObject="ga",window.ga=window.ga||function(){window.ga.q.push(arguments),"create"===arguments[0]&&void 0!==window._gaUTrackerOptions&&h()},window.ga.q=window.ga.q||[],window.ga.l=1*new Date),window.ZipifyPages.alternativeAnalyticsLoading?(window.trekkie=window.trekkie||[],window.trekkie.push(["ready",function(){try{m&&h();var e=window.ZipifyPages&&window.ZipifyPages.ShopifyAnalytics;void 0!==e&&"function"==typeof e.init?(e.init(),function(){if(m&&"function"==typeof(window.trekkie&&window.trekkie.page)&&(o(),f(),c(),v)){var e=window.trekkie.page;window.trekkie.page=function(){var t=arguments;y.onEntityStateLoaded((function(){e.call(window.trekkie,t)}))}}}()):console.error("window.ZipifyPages.ShopifyAnalytics.init is not defined")}catch(e){console.error(e)}}])):m&&k.ready(h);const Z=()=>document.cookie.match(/_shopify_y=([^;]*)/);window.addEventListener("DOMContentLoaded",(()=>{!function(e){if(Z())return e();document.addEventListener("trackingConsentAccepted",(()=>{const t=setInterval((()=>{Z()&&(e(),clearInterval(t))}),25)}),{once:!0})}(E)}))}();</script>{% assign zp_entity_scripts = '' | append: zp_current_entity_metafields[zp_scripts_metafield_key] | split: '|;|~|;|' %}{% assign zp_entity_header_scripts = '' | append: zp_entity_scripts[0] | strip %}{% assign zp_entity_scripts = '' %}{% assign zp_replacement_key = '</script' %}{% assign zp_replacement_value = '<\/script' %}{% if zp_entity_header_scripts.size > 0 %}{% if zp_alternative_entity_present == true %}<script type="text/template" id="zp-main-header-scripts">{{ zp_entity_header_scripts | replace: zp_replacement_key, zp_replacement_value }}</script>{% else %}{{ zp_entity_header_scripts }}{% endif %}{% assign zp_entity_header_scripts = '' %}{% endif %}{% if zp_alternative_entity_present == true and zp_alternative_header_scripts.size > 0 %}{% assign zp_replacement_key = '</script' %}{% assign zp_replacement_value = '<\/script' %}<script type="text/template" id="zp-alternative-header-scripts">{{ zp_alternative_header_scripts | replace: zp_replacement_key, zp_replacement_value }}</script>{% assign zp_alternative_header_scripts = '' %}{% endif %}{% if zp_alternative_entity_present == true %}<script>!function(){var e=!1;window.ZipifyPages.SplitTest.onEntityStateLoaded(function(){if(!e){e=!0;var t=document.querySelector("#zp-"+this.currentEntity()+"-header-scripts");if(t){var n=document.getElementsByTagName("head")[0];this.prepareAndAppendHTML(t.innerHTML,n)}}})}();</script>{% endif %}{% if zp_display_content_for_header_scripts %}{{ zp_content_for_header }}{% assign zp_content_for_header = '' %}{% endif %}{% if zp_alternative_entity_present %}{% assign zp_js_check_stamped_load_script = true %}{% else %}{% assign zp_js_check_stamped_load_script = false %}{% endif %}{% assign zp_stamped_script_id = 'stamped-script-widget' %}{% assign zp_stamped_script_src = 'https://cdn-stamped-io.azureedge.net/files/widget.min.js' %}{% if zp_js_check_stamped_load_script %}<script>!function(){var e=!1;window.ZipifyPages.SplitTest.onEntityContentInjected(function(){if(!e&&(e=!0,document.querySelector("[data-zp-stamped-init]"))){var t=document.createElement("script");t.setAttribute("id","{{ zp_stamped_script_id }}"),t.setAttribute("type","text/javascript"),t.setAttribute("src","{{ zp_stamped_script_src }}"),t.setAttribute("async",!0),document.getElementsByTagName("head")[0].appendChild(t)}})}();</script>{% elsif zp_load_stamped_scripts %}<script async type="text/javascript" id="{{ zp_stamped_script_id }}" src="{{ zp_stamped_script_src }}"></script>{% endif %}{%- if zp_load_loox_scripts and shop.metafields.loox %}{{ shop.metafields.loox['global_html_head'] }}{% endif %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_judgeme_enabled = zp_current_entity_metafields['config'].value['enable_judgeme'] %}{% else %}{% assign zp_judgeme_enabled = zp_current_entity_metafields['config']['enable_judgeme'] %}{% endif %}{% assign zp_load_judgeme_scripts = true %}{% assign zp_ld_boldsbsrpnsscrpt = true %}{% if zp_alternative_entity_present %}{% unless zp_judgeme_enabled %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_judgeme_enabled = zp_alternative_entity_metafields['config'].value['enable_judgeme'] %}{% else %}{% assign zp_judgeme_enabled = zp_alternative_entity_metafields['config']['enable_judgeme'] %}{% endif %}{% endunless %}{% if zp_entity_custom_template and zp_alternative_entity_custom_template %}{% assign zp_load_judgeme_scripts = false %}{% assign zp_ld_boldsbsrpnsscrpt = false %}{% endif %}{% elsif zp_entity_custom_template %}{% assign zp_load_judgeme_scripts = false %}{% assign zp_ld_boldsbsrpnsscrpt = false %}{% endif %}{% assign zp_unavailable_third_party_scripts_context = 'thm,thmtpl,idxthmtpl' | split: ',' %}{% if zp_unavailable_third_party_scripts_context contains renderctx %}{% assign zp_load_judgeme_scripts = false %}{% assign zp_ld_boldsbsrpnsscrpt = false %}{% endif %}{% if zp_judgeme_enabled and zp_load_judgeme_scripts %}{% capture zp_judgeme_core_content %}{% render 'judgeme_core' %}{% endcapture %}{% unless zp_judgeme_core_content contains 'Could not find asset' %}{{ zp_judgeme_core_content }}{% endunless %}{% assign zp_judgeme_core_content = nil %}{% endif %}{% if zp_app_integrations contains 'subscriptionsbybold' %}{% assign zp_incl_boldsbsrpns = true %}{% else %}{% assign zp_incl_boldsbsrpns = false %}{% endif %}{% if zp_incl_boldsbsrpns and zp_ld_boldsbsrpnsscrpt %}{{ 'bsub.js' | asset_url | script_tag }}{{ 'bsub.scss.css' | asset_url | stylesheet_tag }}{%- render 'bold-subscription-widget' -%}{% endif %}{%- if zp_app_integrations contains 'recharge' or zp_recharge_subscriptions_enabled %}<script>!function(){function r(e){return Object.prototype.toString.call(e)}function n(e){return"[object Object]"===r(e)}function o(e){return"[object Function]"===r(e)}function e(){n(window.ReChargeWidget)&&n(window.ReChargeWidget.eventService)&&o(window.ReChargeWidget.eventService.addEventListener)&&window.ReChargeWidget.eventService.addEventListener("widget:build",d),function(){if(n(window.ReChargeWidget)&&o(window.ReChargeWidget.getWidgets)){var e=window.ReChargeWidget.getWidgets();if(function(e){return"[object Array]"===r(e)}(e))for(var t=0,i=e.length;t<i;t++)d(e[t])}}()}function d(t){if(t&&o(t.destroy)){var e=t.container;if(void 0!==(e&&e.dataset||{}).zpAddToCartForm)try{t.destroy()}catch(e){console.error(e),function(e){if(e&&e.container)try{var t=e.selectors||{},t=t.widgetContainer||t.widget||".rc_container_wrapper,.rc-container-wrapper",t=e.container.querySelector(t);if(!t)return;t.parentNode.removeChild(t)}catch(e){console.error(e)}}(t)}}}window.ZipifyPages=window.ZipifyPages||{},window.ZipifyPages.ReChargeWidgetLoadSubscribed=!0,n(window.ReChargeWidget)?e():window.addEventListener("ReChargeWidget-loaded",e)}();</script>{% endif -%}{%- assign zp_load_loop_subscriptions_scripts = false %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_loop_subscriptions_enabled = zp_current_entity_metafields['config'].value['enbloop'] %}{% else %}{% assign zp_loop_subscriptions_enabled = zp_current_entity_metafields['config']['enbloop'] %}{% endif %}{% if zp_alternative_entity_present %}{% unless zp_loop_subscriptions_enabled %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_loop_subscriptions_enabled = zp_alternative_entity_metafields['config'].value['enbloop'] %}{% else %}{% assign zp_loop_subscriptions_enabled = zp_alternative_entity_metafields['config']['enbloop'] %}{% endif %}{% endunless %}{% if zp_entity_custom_template or zp_alternative_entity_custom_template %}{% assign zp_load_loop_subscriptions_scripts = false %}{% endif %}{% elsif zp_entity_custom_template %}{% assign zp_load_loop_subscriptions_scripts = false %}{% endif %}{% if zp_load_loop_subscriptions_scripts and zp_loop_subscriptions_enabled and zp_enable_loop_subscriptions_assets_load %}{% capture zp_loop_subscription_css_content %}{% render 'loop-widget.css' %}{% endcapture %}{% unless zp_loop_subscription_css_content contains 'Could not find asset' %}<script type="text/javascript" id="loop-subscription-script" async src="{{ 'loop-widget.js' | asset_url }}"></script><style class="loop-style">{{ zp_loop_subscription_css_content }}</style>{% endunless %}{% assign zp_loop_subscription_css_content = nil %}{% endif -%}{% capture zp_replace_integration %}<zp_additional_integration></zp_additional_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_additional_integration>', '' | replace: '</zp_additional_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{% else %}{% assign zp_entity_marker = request.page_type %}{% if zp_entity_marker == 'article' %}{% assign zp_current_entity = article %}{% elsif zp_entity_marker == 'blog' %}{% assign zp_current_entity = blog %}{% elsif zp_entity_marker == 'page' %}{% assign zp_current_entity = page %}{% elsif zp_entity_marker == 'product' %}{% assign zp_current_entity = product %}{% else %}{% assign zp_current_entity = nil %}{% endif %}{% if zp_current_entity != blank and zp_current_entity.template_suffix contains 'zipifypages' and zp_current_entity.template_suffix != template.suffix %}{% if zp_current_entity_metafields_loaded and zp_product_page_with_zp_layout %}{% assign zp_current_entity_object_metafields = zp_current_entity_main_metafields %}{% elsif zp_current_entity_metafields_loaded %}{% assign zp_current_entity_object_metafields = zp_current_entity_metafields %}{% else %}{% assign zp_current_entity_object_metafields = zp_current_entity.metafields['zipifypages'] %}{% endif %}{% if zp_current_entity_object_metafields['splittest'] != blank and zp_current_entity_object_metafields['splittest'].type == 'json' %}{% assign zp_split_test_data = zp_current_entity_object_metafields['splittest'].value %}{% assign zp_split_test_type = zp_split_test_data.type %}{% if zp_split_test_type == 'viewtyperedirect' %}{% assign zp_split_token = zp_split_test_data.token%}{% unless zp_shop_metafields_loaded %}{% assign zp_shop_metafields = shop.metafields['zipifypages'] %}{% endunless %}{% assign zp_alternative_analytics_loading = true %}{% if zp_current_entity_metafields['config'].type == 'json' %}{% assign zp_recharge_subscriptions_enabled = zp_current_entity_metafields['config'].value['enbrcharge'] %}{% else %}{% assign zp_recharge_subscriptions_enabled = zp_current_entity_metafields['config']['enbrcharge'] %}{% endif %}{% if zp_alternative_entity_present %}{% unless zp_recharge_subscriptions_enabled %}{% if zp_alternative_entity_metafields['config'].type == 'json' %}{% assign zp_recharge_subscriptions_enabled = zp_alternative_entity_metafields['config'].value['enbrcharge'] %}{% else %}{% assign zp_recharge_subscriptions_enabled = zp_alternative_entity_metafields['config']['enbrcharge'] %}{% endif %}{% endunless %}{% endif %}{% assign zp_app_integrations = '' | append: zp_shop_metafields['appintegrations'] | split: ',' %}{% if zp_recharge_subscriptions_enabled %}{% assign zp_js_config_app_integrations = 'recharge' | split: ',' | concat: zp_app_integrations | uniq %}{% else %}{% assign zp_js_config_app_integrations = zp_app_integrations | uniq %}{% endif %}<script>!function(){function r(){}function e(){this.urlHelper=new r}function t(){}r.prototype.parseUrl=function(r){var e=(""+r).trim();if(!/^\//.test(e)&&!/^https?:\/\//.test(e))return{};e=document.createElement("a");return e.href=r,{protocol:e.protocol,host:e.host,hostname:e.hostname,port:e.port,pathname:e.pathname,hash:e.hash,search:e.search,origin:e.origin}},r.prototype.searchQueryParams=function(r){r=this.parseUrl(r);return r.search?(""+r.search).replace(/^\?+/,""):""},r.prototype.filterSearchParams=function(r,e){if(!(r&&e instanceof RegExp))return r;for(var t,o=(""+(""+r).replace(/^\?+/,"")).split("&"),a=[],n=0,p=o.length;n<p;n++)t=o[n],e.test(t)||a.push(t);return a.join("&")},e.prototype.searchIdentifierParam=function(){return"zpstest={{ zp_split_token | url_escape }}"},e.prototype.getSearchParams=function(){var r=[this.searchIdentifierParam()],e=this.urlHelper.searchQueryParams(window.location.href).trim();return 0<e.length&&r.push(this._filterSearchParams(e)),r.join("&")},e.prototype._filterSearchParams=function(r){var e=new RegExp("^zpstest=");return this.urlHelper.filterSearchParams(r,e)},t.prototype.searchIdentifierParam=function(){return a.searchIdentifierParam()},t.prototype.currentEntitySearchParams=function(){return a.getSearchParams()};var a=new e;window.ZipifyPages=window.ZipifyPages||{},window.ZipifyPages.SplitTestRedirect=new t}();</script><script>!function(){window.ZipifyPages=window.ZipifyPages||{},window.ZipifyPages.currency={{ shop.currency | json }},window.ZipifyPages.shopDomain={{ shop.permanent_domain | json }},window.ZipifyPages.moneyFormat={{ shop.money_format | json }},window.ZipifyPages.shopifyOptionsSelector={{ zp_shopify_options_selector | default: 'false' }},window.ZipifyPages.unitPriceEnabled={{ zp_unit_price_enabled | default: 'false' }},window.ZipifyPages.alternativeAnalyticsLoading={{ zp_alternative_analytics_loading | default: 'false' }},window.ZipifyPages.integrations="{{ zp_js_config_app_integrations | join: ',' }}".split(","),window.ZipifyPages.crmUrl="https://crms.zipify.com",window.ZipifyPages.translations={soldOut:{% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation | json }},unavailable:{% assign zp_translation = 'products.product.unavailable' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Unavailable" %}{% endif %}{{ zp_translation | json }}},window.ZipifyPages.routes={root:{{ routes.root_url | json }},cart:{index:{{ routes.cart_url | json }},add:{{ routes.cart_add_url | json }},change:{{ routes.cart_change_url | json }},clear:{{ routes.cart_clear_url | json }},update:{{ routes.cart_update_url | json }}}};try{window.ZipifyPages.analytics=window.ZipifyPages.analytics||{{ zp_analytics_settings | json }}||{}}catch(i){window.ZipifyPages.analytics=window.ZipifyPages.analytics||{}}window.ZipifyPages.eventsSubscriptions=[],window.ZipifyPages.on=function(i,a){window.ZipifyPages.eventsSubscriptions.push([i,a])}}();</script><script>!function(){var e=!1,t=[],n=!1,i=!1;function r(){}function a(e){this.integrations=e||[]}function o(){window.trekkie.url=function(){var e=l();if(e)return e;var t=this.canonical();if(t)return t.indexOf("?")>0?t:t+s(window.location.search);var n=window.location.href,i=n.indexOf("#");return-1===i?n:n.slice(0,i)}}function c(){window.trekkie.pageDefaults=function(){var e=window.location.href,t=e.indexOf("?");return t=(e=-1===t?"":e.slice(t)).indexOf("#"),e="?"===(e=-1===t?e:e.slice(0,t))?"":e,{path:w(),referrer:document.referrer,search:d()||e,title:u(),url:this.url(),properties:{}}}}function f(){var e=window.trekkie.generatePageObject;window.trekkie.generatePageObject=function(){var t=e.apply(window.trekkie,arguments);t.path=w(),t.title=u(),t.search&&(t.search=s(t.search));var n=v&&"function"==typeof y.searchIdentifierParam?y.searchIdentifierParam():P&&"function"==typeof g.searchIdentifierParam?g.searchIdentifierParam():null;return n?(t.search?t.search="?"+n+"&"+(""+t.search).replace(/^\?+/,""):t.search="?"+n,t):t}}function s(e){var t=new RegExp("^zpstest=");return S.filterSearchParams(e,t)}function w(){return v&&"function"==typeof y.currentEntityPath&&y.currentEntityPath()?y.currentEntityPath():window.location.pathname}function u(){return v&&"function"==typeof y.currentEntityTitle&&y.currentEntityTitle()?y.currentEntityTitle():document.title}function d(){return v&&"function"==typeof y.currentEntitySearchParams?"?"+y.currentEntitySearchParams():P&&"function"==typeof g.currentEntitySearchParams?"?"+g.currentEntitySearchParams():s(window.location.search)}function p(){return v&&"function"==typeof y.currentEntityUrl?y.currentEntityUrl():P?function(){var e=window.location.href,t=e.indexOf("?");t>-1&&(e=e.slice(0,t));(t=e.indexOf("#"))>-1&&(e=e.slice(0,t));return e}():null}function l(){var e=p();if(e){var t=d();return t?e+t:e}return null}function h(){if(!i&&void 0!==window.ga){i=!0;var e=l(),t=function(){var e=w();if(e){var t=d();return t?e+t:e}return null}(),n=u();e&&window.ga("set","location",e),t&&window.ga("set","page",t),n&&window.ga("set","title",n)}}r.prototype.parseUrl=function(e){var t=(""+e).trim();if(!/^\//.test(t)&&!/^https?:\/\//.test(t))return{};var n=document.createElement("a");return n.href=e,{protocol:n.protocol,host:n.host,hostname:n.hostname,port:n.port,pathname:n.pathname,hash:n.hash,search:n.search,origin:n.origin}},r.prototype.searchQueryParams=function(e){var t=this.parseUrl(e);return t.search?(""+t.search).replace(/^\?+/,""):""},r.prototype.filterSearchParams=function(e,t){if(!(e&&t instanceof RegExp))return e;for(var n,i=this.splitSearchParams(e),r=[],a=0,o=i.length;a<o;a++)n=i[a],t.test(n)||r.push(n);return r.join("&")},r.prototype.splitSearchParams=function(e){return e?(""+(""+e).replace(/^\?+/,"")).split("&"):[]},a.prototype.init=function(){e||(e=!0,function(){for(var e=0,n=t.length;e<n;e++)t[e].call();t=[]}())},a.prototype.ready=function(n){"function"==typeof n&&(e?n.call():t.push(n))},a.prototype.page=function(){if(!n){n=!0;var e=arguments;m?(o(),f(),c(),v&&"function"==typeof y.onEntityStateLoaded?y.onEntityStateLoaded((function(){window.trekkie.page.call(window.trekkie,e)})):window.trekkie.page.call(window.trekkie,e)):window.trekkie.page.call(window.trekkie,e)}},window.ZipifyPages=window.ZipifyPages||{};var y=window.ZipifyPages.SplitTest,g=window.ZipifyPages.SplitTestRedirect,k=new a(window.ZipifyPages.integrations),v=void 0!==y,P=void 0!==g,m=v||P,S=new r;function E(){window.Shopify.analytics.publish("zp_page_viewed",{handle:window.location.pathname,title:document.title,shopData:{presentmentCurrency:{code:Shopify.currency.active,rate:Shopify.currency.rate},shopCurrencyCode:ZipifyPages.currency},splitTestData:{handle:window.ZipifyPages.SplitTest?.currentEntityUrl(),title:u(),test_token:window.ZipifyPages.SplitTestRedirect?.searchIdentifierParam()??window.ZipifyPages.SplitTest?.searchIdentifierParam()}})}window.ZipifyPages.ShopifyAnalytics=k,m&&(window.GoogleAnalyticsObject="ga",window.ga=window.ga||function(){window.ga.q.push(arguments),"create"===arguments[0]&&void 0!==window._gaUTrackerOptions&&h()},window.ga.q=window.ga.q||[],window.ga.l=1*new Date),window.ZipifyPages.alternativeAnalyticsLoading?(window.trekkie=window.trekkie||[],window.trekkie.push(["ready",function(){try{m&&h();var e=window.ZipifyPages&&window.ZipifyPages.ShopifyAnalytics;void 0!==e&&"function"==typeof e.init?(e.init(),function(){if(m&&"function"==typeof(window.trekkie&&window.trekkie.page)&&(o(),f(),c(),v)){var e=window.trekkie.page;window.trekkie.page=function(){var t=arguments;y.onEntityStateLoaded((function(){e.call(window.trekkie,t)}))}}}()):console.error("window.ZipifyPages.ShopifyAnalytics.init is not defined")}catch(e){console.error(e)}}])):m&&k.ready(h);const Z=()=>document.cookie.match(/_shopify_y=([^;]*)/);window.addEventListener("DOMContentLoaded",(()=>{!function(e){if(Z())return e();document.addEventListener("trackingConsentAccepted",(()=>{const t=setInterval((()=>{Z()&&(e(),clearInterval(t))}),25)}),{once:!0})}(E)}))}();</script>{% endif %}{% endif %}{% endif %}{% endif %}