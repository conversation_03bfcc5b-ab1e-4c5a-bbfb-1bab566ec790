{% comment %}
  @param img
    An Image Drop to display.

  @param alt
    Alt text for the image.

  @param progressive {Boolean} (optional)
    If true, image is progressive.

  @param stretch_width {Boolean} (optional)
    If true, remove the max-width from the image tag.

  @param additional_classes {String} (optional)
    Additional classes added to the image tag.

  @param additional_wrapper_classes {String} (optional)
    Additional classes added to the image element wrap tag.

  @param inline_style {String} (optional)
    CSS styles to apply to the image tag's style attribute.
    Defaults to blank.

  @param back_to_basics {Boolean} (optional)
    If true, use standard image tag.

  @param height {Number} (optional)
    The height to set the image to.

  @param max_height {Number} (optional)
    The max height to set the image to.

  @param object_fit {Boolean} (optional)
    If true, preserves the image's max-height and aspect ratio.

  @param image_crop {Boolean} (optional)
    If true, crops the image to the max_height.

  @param calculate_aspect_ratio {Boolean} (optional)
    If true, adds the `data-calculate-aspect-ratio` data attribute to the image.

  @param focal_point {String} (optional)
    Applies `background-size:cover;background-position:{{ focal_point }};` to the style property
    of the container if the image is a background-image and `object-fit:cover;object-position:[focal point]`
    otherwise on the image element.
    Defaults to blank.
{% endcomment %}

{% liquid
  assign inline_style = inline_style | default: blank
  assign focal_point = focal_point | default: blank
%}

{% if focal_point != blank %}
  {%- capture focal_point_style -%}
    object-fit:cover;object-position:{{ focal_point }};
  {%- endcapture -%}

  {% if inline_style != blank %}
    {% assign inline_style = inline_style | append: focal_point_style %}
  {% else %}
    {% assign inline_style = focal_point_style %}
  {% endif %}
{% endif %}

{% comment %}Check if image is progressive (does not support transparent pngs){% endcomment %}
{% if progressive %}
  {%- capture srcset -%}
    {{ image | img_url: '5000x', format: 'pjpg' }} 5000w,
    {{ image | img_url: '4500x', format: 'pjpg' }} 4500w,
    {{ image | img_url: '4000x', format: 'pjpg' }} 4000w,
    {{ image | img_url: '3500x', format: 'pjpg' }} 3500w,
    {{ image | img_url: '3000x', format: 'pjpg' }} 3000w,
    {{ image | img_url: '2500x', format: 'pjpg' }} 2500w,
    {{ image | img_url: '2000x', format: 'pjpg' }} 2000w,
    {{ image | img_url: '1800x', format: 'pjpg' }} 1800w,
    {{ image | img_url: '1600x', format: 'pjpg' }} 1600w,
    {{ image | img_url: '1400x', format: 'pjpg' }} 1400w,
    {{ image | img_url: '1200x', format: 'pjpg' }} 1200w,
    {{ image | img_url: '1000x', format: 'pjpg' }} 1000w,
    {{ image | img_url: '800x', format: 'pjpg' }} 800w,
    {{ image | img_url: '600x', format: 'pjpg' }} 600w,
    {{ image | img_url: '400x', format: 'pjpg' }} 400w,
    {{ image | img_url: '200x', format: 'pjpg' }} 200w
  {%- endcapture -%}
{% else %}
  {%- capture srcset -%}
    {{ image | img_url: '5000x' }} 5000w,
    {{ image | img_url: '4500x' }} 4500w,
    {{ image | img_url: '4000x' }} 4000w,
    {{ image | img_url: '3500x' }} 3500w,
    {{ image | img_url: '3000x' }} 3000w,
    {{ image | img_url: '2500x' }} 2500w,
    {{ image | img_url: '2000x' }} 2000w,
    {{ image | img_url: '1800x' }} 1800w,
    {{ image | img_url: '1600x' }} 1600w,
    {{ image | img_url: '1400x' }} 1400w,
    {{ image | img_url: '1200x' }} 1200w,
    {{ image | img_url: '1000x' }} 1000w,
    {{ image | img_url: '800x' }} 800w,
    {{ image | img_url: '600x' }} 600w,
    {{ image | img_url: '400x' }} 400w,
    {{ image | img_url: '200x' }} 200w,
  {%- endcapture -%}
{% endif %}

{% if image_crop == true %}
  {% capture height_style %}
    {% if height %}height: {{ height }}px;{% endif %}
    max-height: {{ max_height }}px;
  {% endcapture %}
{% elsif object_fit == true %}
  {% capture height_style %}
    {% if height %}height: {{ height }}px;{% endif %}
    max-height: {{ max_height }}px; max-width: calc({{ image.width }} /  {{ image.height }} * {{ max_height }}px);
  {% endcapture %}
{% else %}
  {% comment %}Used to reset variable{% endcomment %}
  {% assign height_style = '' %}
{% endif %}

{% comment %}Loading style{% endcomment %}
{% if settings.image_loading_style == 'blur-up' %}
  {% comment %}Low quality image loads first{% endcomment %}
  {% assign low_quality_image = image | img_url: '50x' %}
{% elsif settings.image_loading_style == 'color' %}
  {% comment %}Dominant image color loads first{% endcomment %}
  {% assign dominant_color_image = image | img_url: '1x' %}
  {% capture image_background_color %}
    background: url({{ dominant_color_image }});
  {% endcapture %}
{% endif %}

{% if back_to_basics %}
  <div
    class="
      image-element__wrap
      {% if additional_wrapper_classes %}
        {{ additional_wrapper_classes }}
      {% endif %}
    "
    style="{{ image_background_color }}{{ height_style }}"
  >
    <img
      alt="{{ alt | escape }}"
      src="{{ image | img_url: '2000x' }}"
      style="{{ inline_style }}"
      {% if additional_classes %}
        class="{{ additional_classes }}"
      {% endif %}
    />
  </div>
{% else %}
  {% comment %}Build image element{% endcomment %}
  <div
    class="
      image-element__wrap
      {% if additional_wrapper_classes %}
        {{ additional_wrapper_classes }}
      {% endif %}
    "
    {% if calculate_aspect_ratio %}
      data-calculate-aspect-ratio
    {% endif %}
    style="{{ image_background_color }}{{ height_style }} {% unless stretch_width == true or object_fit %}max-width: {{ image.width }}px;{% endunless %}"
  >
    <img
      alt="{{ alt | escape }}"
      class="
        lazyload
        transition--{{ settings.image_loading_style }}
        {{ additional_classes }}
      "
      {% if low_quality_image %}
        src="{{ low_quality_image }}"
      {% endif %}
      data-src="{{ image | img_url: '1600x' }}"
      data-sizes="auto"
      data-srcset="{{ srcset }}"
      data-aspectratio="{{ image.width }}/{{ image.height }}"
      height="{{ image.height }}"
      width="{{ image.width }}"
      srcset="data:image/svg+xml;utf8,<svg%20xmlns='http://www.w3.org/2000/svg'%20width='{{ image.width }}'%20height='{{ image.height }}'></svg>"
      style="{{ inline_style }}{{ height_style }};"
    />
  </div>
{% endif %}

{% comment %}Fallback for no javascript{% endcomment %}
<noscript class="noscript">
  <img class="{{ additional_classes }}" src="{{ image | img_url: '2000x' }}" alt="{{ alt | escape }}" style="{{ inline_style }}">
</noscript>
