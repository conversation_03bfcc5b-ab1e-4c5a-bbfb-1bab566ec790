<style>

  .rounded-button,
  .circle-button {
    border-radius: 50%;
    margin-bottom: 7px;
    padding: 5px;
    background-color: white;
    color: black;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
    border: 1px solid gray;
    width: 40px;
    height: 40px;
    margin-right: 5px;
  }
  .rounded-button:hover,
  .circle-button:hover {
    border-color: #5461c8;
    border: 2px solid #5461c8;
  }

  .rectangle-button {
    border-radius: 10px;
    padding: 5px;
    background-color: white;
    color: black;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
    border: 1px solid gray;
    /* width: 80px; */
    height: 30px;
    margin: 3px;
  }
  .rectangle-button:hover {
    border-color: #5461c8;
    border: 2px solid #5461c8;
  }
  .swatches-select {
    display: flex;
    flex-direction: column;
  }
  .swatches-label {
    font-weight: bold;
    color: #363636;
    margin-bottom: 5px;
  }
  [type=radio] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  /* IMAGE STYLES */
  [type=radio] + img {
    cursor: pointer;
    border-radius: 50%;
  }

  /* CHECKED STYLES */
  [type=radio]:checked + img {
    outline: 2px solid #5461c8;
    border-radius: 50%;
  }
  #selected-swatches {
    font-weight: 400;
  }
  .custom-option-input-label-image-1 {
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .extraOptionDiv {
    display: flex;
    width: 100%;
    background: #F1F1F1;
    border: 1px solid #F1F1F1;
    border-radius: 10px;
    height: auto;
    margin-bottom: 30px;
    padding-bottom: 20px;
  }
  .optionImageDiv {
    width: 30%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .optionImage {
    border-radius: 50%;
  }
  .optionDetailContainer {
    padding: 20px 0 0;
    width: 55%;
    text-align: left;
  }
  .optionLabel {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 8px;
    line-height: normal;
  }
  .optionDescription {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: normal;
  }
  .optionPrice {
    font-size: 16px;
    font-weight: 700;
    color: #5461C9;
    line-height: normal;
  }
  .optionCheckboxContainer {
    width: 15%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
  }

  .optionCheckboxContainer input[type="checkbox"] {
    width: 38px;
    height: 38px;
  }

  @media (max-width: 767px) {
    .extraOptionDiv {
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 351px;
    }
    .optionImageDiv {
      padding-top: 20px;
    }
    .optionDetailContainer {
      margin-bottom: 15px;
    }
    .optionCheckboxContainer {
      margin-bottom: 30px;
    }
    .extraOptionDiv {
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 351px;
    }
    .optionDetailContainer {
      text-align: center;
    }
  }

</style>

{% if show_payment_button == false or product.selling_plan_groups.size > 0 %}
  {% assign show_payment_button = false %}
{% else %}
  {% assign show_payment_button = true %}
{% endif %}
{% assign oms = shop.metafields.cuddleclones.api_details.value %}

<link rel="stylesheet" href="{{ 'custom.style.css' | asset_url }}">


<div
  class="
    clearfix
    product_form
    init
    smart-payment-button--{{ show_payment_button }}
    {% if product.variants.size > 1 or product.options.size > 1 %}product_form_options{% endif %}
    product_form--{{ settings.product_form_style }}
    product_form--has-quantity-box-{{ settings.display_product_quantity }}
  "
  id="product-form-{{ product.id }}"
  data-product-form
  data-money-format="{{ shop.money_format | strip_html }}"
  data-shop-currency="{{ shop.currency }}"
  data-select-id="product-select-{{ product.id }}{{ product-form }}{{ object.id }}{{ block.id }}"
  data-enable-state="{% if template.name == 'product' %}true{% else %}false{% endif %}"
  {% if settings.limit_quantity or settings.display_inventory_left %}
  data-variant-inventory='[{%- for v in product.variants -%}{"id":{{v.id}},"inventory_quantity":{{v.inventory_quantity}},"inventory_management":"{{v.inventory_management}}","inventory_policy":"{{v.inventory_policy}}"}{% if forloop.last == false %},{% endif %}{%- endfor -%}]'
  {% endif %}
  data-product-id="{{ product.id }}">
  {% form 'product'
    , product %}
    <div style="display: none;" data-shop-pay-installments-template>{{ form | payment_terms }}</div>

    {% comment %}
      Display variant options for a product

      @param product
      @param selected_variant
      @param style

      @param enable_swatches
      @param swatches_shape
      @param swatches_option_trigger
      @param swatches_option_style
      @param swatches_product_page_size
      @param swatch_colors
    {% endcomment %}

    {% liquid
      assign style = 'select'

      if settings.product_form_style == 'radio'
        assign style = 'radio'
      endif

      assign product = product
      assign sold_out_options = sold_out_options
      assign selected_variant = selected_variant
      assign enable_swatches = settings.enable_swatches
      assign swatches_shape = settings.swatch_style
      assign swatches_option_trigger = settings.swatch_trigger
      assign recipient_form_input_classes = 'input'
      assign recipient_form_textarea_rows = 0
      assign recipient_form_textarea_classes = 'textarea'
    %}

    {% comment %}Inject @pixelunion/shopify-variants-ui/variant-selection begin{% endcomment %}
    {% comment %}
      Display variant options for a product

      @param product
      @param selected_variant
      @param variant_selection_id
      @param sold_out_options
      @param style

      @param enable_swatches
      @param swatches_shape
      @param swatches_option_trigger
      @param swatches_option_style
      @param swatches_product_page_size
      @param swatches_custom_colors
    {% endcomment %}

    {% comment %} custom options------------------------ {% endcomment %}
    {% unless product.tags contains "custom-design" %}
      <input
        type="hidden"
        name="properties[_original_unique_key]"
        value=""
        id="originalkey" />
    {% endunless %}
    <input
      type="hidden"
      name="id"
      id="variantid"
      value="" />

    {% if product.tags contains "custom-design" %}
      {% render 'custom-design'
        , product: product %}
    {% elsif product.tags contains "memorial-gift-box" %}
      {% render 'memorial-gift-box'
        , product: product %}
    {% else %}
      {% if product.tags contains "custom-option" %}
        {% assign options = product.metafields.cuddleclones.custom_options.value %}
        {% for option in options %}
          {% if option.display == true and option.type == "dropdown" %}
            <input
              type="hidden"
              name="properties[{{option.heading}}]"
              value=""
              class="{{option.name}}HiddenInput">
            <div class="selector-wrapper" style="{% if option.show_size_chart %}justify-content:space-between;flex-direction:column;gap:0;{%endif%}">
              <div style="{% if option.show_size_chart %}display:flex;align-items:baseline;justify-content:space-between;width:100%;{%endif%}">
                <div>
                  <label
                    for="data-variant-custom-option-{{ forloop.index }}"
                    data-variant-option-name="{{ option.name }}"
                    data-variant-option-choose-name="{{ option.name }}">
                    {% if option.field == 'optional' %}
                      {{ option.heading }} ({{ option.field | capitalize }})
                    {% else %}
                      {{ option.heading }}
                    {% endif %}
                  </label>
                </div>
                {% if option.show_size_chart %}
                  <div>
                    <h4 class="open-new-pajamas-size-chart-header">
                      <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                        <span class="icon sizeChartIcon"  data-icon="right-caret">  
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                        </span>
                      </a>
                    </h4>
                    <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                      <div class="modal-overlay"></div>
                      <div class="modal-content">
                        <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                        <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                          <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                        </div>
                        <div class="delete-button">
                          <span  onclick="closePopup()">x</button>
                        </div>
                      </div>
                    </div>
                  </div>
                {%endif%}
              </div>
              <span class="select" data-dropdown-form-style="">
                <select
                class="single-option-selector {{option.name}}Option"
                  onchange="getSelectedValue('{{option.name}}',this,'{{option.heading}}',this.options[this.selectedIndex].getAttribute('data-sub-options'),{{option.show_related_images}});"
                  id="data-variant-custom-option-{{ forloop.index }}"
                  data-variant-option
                  data-variant-option-index="{{ forloop.index }}"
                  data-variant-option-chosen-value="{% if selected_variant %}{{ option.selected_value }}{% else %}false{% endif %}">

                  <option
                  value="not-selected"
                  {% if option.field=="mandatory" %}
                    disabled
                    {% endif %}
                    selected
                    data-variant-option-value-wrapper
                    data-variant-option-value
                    data-variant-option-value-index="{{ forloop.index }}"
                    data-sub-options="">
                    {{ option.placeholder_text }}
                  </option>
                  
                  {% for value in option.options %}
                    <option
                    value="{{ value.value | escape }}"
                      {% if selected_variant and option.selected_value == value %}
                      selected{% endif %}
                      data-variant-option-value-wrapper
                      data-variant-option-value
                      data-variant-option-value-index="
                    {{ forloop.index }}
                    "
                      data-sub-options="{{ value.sub_options }}">
                      {{ value.name }}
                    </option>
                  {% endfor %}
                </select>
              </span>
            </div>
          {%- elsif option.display == true and option.type == "button" -%}
            <input
              type="hidden"
              name="properties[{{option.heading}}]"
              class="{{option.name}}HiddenInput"
              value="">
            {%- if option.shape == 'circle' -%}
              {% assign headingWithHyphen = option.heading | replace: " ", "_" %}
              <div class="selector-wrapper" style="{% if option.show_size_chart %}justify-content:space-between;flex-direction:column;gap:0;{%endif%}">
                <div style="{% if option.show_size_chart %}display:flex;align-items:baseline;justify-content:space-between;width:100%{%endif%}">
                  <div>
                    <label
                      for="data-variant-custom-option-{{ forloop.index }}"
                      data-variant-option-name="{{ option.name }}"
                      data-variant-option-choose-name="{{ option.name }}">
                      {% if option.field == 'optional' %}
                        {{ option.heading }} ({{ option.field | capitalize }})
                      {% else %}
                        {{ option.heading }}
                      {% endif %}
                    </label>
                  </div>
                  {% if option.show_size_chart %}
                    <div>
                      <h4 class="open-new-pajamas-size-chart-header">
                        <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                          <span class="icon sizeChartIcon"  data-icon="right-caret">  
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                          </span>
                        </a>
                      </h4>
                      <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                        <div class="modal-overlay"></div>
                        <div class="modal-content">
                          <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                          <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                            <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                          </div>
                          <div class="delete-button">
                            <span  onclick="closePopup()">x</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {%endif%}
                </div>
                <span>
                  {% for value in option.options %}
                    <button
                      type="button"
                      class="rounded-button {{headingWithHyphen}}CircleButtonCustom"
                      onclick="changeBorderColor('{{option.name}}',this, '.rounded-button','{{option.heading}}','Circle')">{{ value.name }}</button>
                  {%- endfor -%}
                </span>
              </div>
            {%- elsif option.shape == 'rectangle' -%}
              {% assign headingWithHyphen = option.heading | replace: " ", "_" %}
              <div class="selector-wrapper" style="{% if option.show_size_chart %}justify-content:space-between;flex-direction:column;gap:0;{%endif%}">
                <div style="{% if option.show_size_chart %}display:flex;align-items:baseline;justify-content:space-between;width:100%{%endif%}">
                  <div>
                    <label
                      for="data-variant-custom-option-{{ forloop.index }}"
                      data-variant-option-name="{{ option.name }}"
                      data-variant-option-choose-name="{{ option.name }}">
                      {% if option.field == 'optional' %}
                        {{ option.heading }} ({{ option.field | capitalize }})
                      {% else %}
                        {{ option.heading }}
                      {% endif %}
                    </label>
                  </div>
                  {% if option.show_size_chart %}
                    <div>
                      <h4 class="open-new-pajamas-size-chart-header">
                        <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                          <span class="icon sizeChartIcon"  data-icon="right-caret">  
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                          </span>
                        </a>
                      </h4>
                      <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                        <div class="modal-overlay"></div>
                        <div class="modal-content">
                          <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                          <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                            <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                          </div>
                          <div class="delete-button">
                            <span  onclick="closePopup()">x</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {%endif%}
                </div>
                <span>
                  {% for value in option.options %}
                    <button
                      type="button"
                      class="rectangle-button {{headingWithHyphen}}RectangleButtonCustom"
                      onclick="changeBorderColor('{{option.name}}',this, '.rectangle-button','{{option.heading}}','Rectangle')">{{ value.name }}</button>
                  {%- endfor -%}
                </span>
              </div>
            {%- endif -%}
          {%- elsif option.display == true and option.type == "swatches" -%}
            <input
              type="hidden"
              name="properties[{{option.heading}}]"
              class="{{option.name}}HiddenInput"
              value="">
            <div class="swatches-select" style="margin-bottom: 10px;">
              <div class="swatches-label" style="{% if option.show_size_chart %}display:flex;justify-content:space-between;{%endif%}">
                <div>
                  <label
                    for="data-variant-custom-option-{{ forloop.index }}"
                    data-variant-option-name="{{ option.name }}"
                    data-variant-option-choose-name="{{ option.name }}">
                    {% if option.field == 'optional' %}
                      {{ option.heading }} ({{ option.field | capitalize }}):
                      <span id="{{option.name}}-selected-swatches"></span>
                      {% else %}
                      {{ option.heading }}:
                      <span id="{{option.name}}-selected-swatches"></span>
                    {% endif %}
                  </label>
                </div>
                {% if option.show_size_chart %}
                  <div>
                    <h4 class="open-new-pajamas-size-chart-header">
                      <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                        <span class="icon sizeChartIcon"  data-icon="right-caret">  
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                        </span>
                      </a>
                    </h4>
                    <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                      <div class="modal-overlay"></div>
                      <div class="modal-content">
                        <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                        <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                          <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                        </div>
                        <div class="delete-button">
                          <span  onclick="closePopup()">x</button>
                        </div>
                      </div>
                    </div>
                  </div>
                {%endif%}
              </div>
              <div id="keydown_Swatch" style="margin-bottom: 15px;">
                {% for value in option.options %}
                  <label style="margin-right: 10px;">
                    <input
                      id="{{option.name}}"
                      type="radio"
                      name="{{option.name}}"
                      value="{{value.name}}">
                    <img
                      src="{{value.image}}"
                      alt="{{value.name}}"
                      aria-label="{{option.name}} {{value.name}}"
                      title="{{value.name}}"
                      width="50px"
                      height="50px"
                      tabindex="0"
                      onkeydown="selectSwatch_PDP(event)"
                      onclick="selectBackground('{{option.name}}','{{value.name}}','{{option.heading}}',{{option.show_related_images}})">
                  </label>
                {%- endfor -%}
              </div>
            </div>
          {%- elsif option.display == true and option.type == "text_field" -%}
            {% assign textFieldName = option.name | replace: '_', ' ' %}

            <input
              type="hidden"
              name="properties[{{option.heading}}]"
              class="{{option.name}}HiddenInput"
              value="">
            <div class="pajamas-pdp-line-item-size-wrapper" style="margin-bottom: 5px;">
              <label
                for="custom_option_{{ forloop.index }}"
                data-variant-option-name="{{ option.name }}"
                data-variant-option-choose-name="{{ option.name }}"
                style="font-weight: 600;">
                {% if option.field == 'optional' %}
                  {{ option.heading }} ({{ option.field | capitalize }}):
                {% else %}
                  {{ option.heading }}:
                {% endif %}
              </label>
            </div>
            <div class="pajamas-pdp-line-item-size-wrapper" style="margin-bottom:20px;">
              <input
                style="border-radius: 10px;border:1px solid;max-width:350px;"
                type="text"
                oninput="handleInputChange('{{option.name}}',event,'{{option.field}}')"
                value=""
                aria-label="custom_option_{{ forloop.index }}"
                placeholder="{{option.placeholder_text}}"
                id="custom_option_{{ forloop.index }} custom-text-field"
                class="input">
            </div>
          {%- endif -%}
        {%- endfor -%}
      {%- endif -%}
      {% if product.tags contains "native" %}
        {% assign nativeOptions = product.metafields.cuddleclones.native_options.value %}
        {% for option in nativeOptions %}
          {% if option.type == "dropdown" %}
            {% if option.conditional %}

              {% assign nameWithoutUnderscore = option.name | replace: '_', ' ' %}

              <input
                type="hidden"
                name="properties[{{nameWithoutUnderscore}}]"
                value=""
                class="{{option.name}}HiddenInput">
              <div class="selector-wrapper {{option.name}}" style="display:none">
                <label
                  for="data-variant-option-{{ forloop.index }}"
                  data-variant-option-name="{{ option.heading }}"
                  data-variant-option-choose-name="{{ option.heading }}">
                  {{ option.heading }}
                </label>
                <span class="select" data-dropdown-form-style="">
                  <select
                    class="single-option-selector nativeLengthOption"
                    id="data-variant-option-{{ forloop.index }}"
                    onchange="handleSuboptionsValue(this,'{{option.name}}');"
                    data-variant-option
                    data-variant-option-index="{{ forloop.index }}"
                    data-variant-option-chosen-value="{% if selected_variant %}{{ option.selected_value }}{% else %}false{% endif %}"
                    style="color: black;">

                    <option
                      value="not-selected"
                      disabled
                      selected
                      data-variant-option-value-wrapper
                      data-variant-option-value
                      data-variant-option-value-index="{{ forloop.index }}">
                      {{ option.placeholder_text }}
                    </option>

                    {% for value in option.options %}
                      {% if value.display %}
                        <option
                          value="{{ value.value | escape }}"
                          {% if selected_variant and option.selected_value == value %}
                          selected{% endif %}
                          data-variant-option-value-wrapper
                          data-variant-option-value
                          data-variant-option-value-index="
                          {{ forloop.index }}
                          ">
                          {{ value.name }}
                        </option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </span>
              </div>
            {% elsif option.multi_uploader %}
              <input
                type="hidden"
                name="properties[{{option.name}}]"
                value=""
                class="{{option.name}}HiddenInput">
              <div class="selector-wrapper" style="{% if option.show_size_chart %}justify-content:space-between;flex-direction:column;gap:0;{%endif%}">
                <div style="{% if option.show_size_chart %}display:flex;align-items:baseline;justify-content:space-between;width:100%{%endif%}">
                  <div>
                    <label
                      for="data-variant-option-{{ forloop.index }}"
                      data-variant-option-name="{{ option.heading }}"
                      data-variant-option-choose-name="{{ option.heading }}">
                      {{ option.heading }} {{ option.sub_options }}
                    </label>
                  </div>
                  {% if option.show_size_chart %}
                    <div>
                      <h4 class="open-new-pajamas-size-chart-header">
                        <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                          <span class="icon sizeChartIcon"  data-icon="right-caret">  
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                          </span>
                        </a>
                      </h4>
                      <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                        <div class="modal-overlay"></div>
                        <div class="modal-content">
                          <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                          <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                            <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                          </div>
                          <div class="delete-button">
                            <span  onclick="closePopup()">x</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {%endif%}
                </div>
                <span class="select" data-dropdown-form-style="">
                  <select
                    class="single-option-selector nativeOption multiUploader"
                    onchange="handleNativeAction('{{option.heading}}',this,'','dropdown','', {{option.show_related_images}});"
                    id="data-variant-option-{{ forloop.index }}"
                    data-variant-option
                    data-variant-option-index="{{ forloop.index }}"
                    data-variant-option-chosen-value="{% if selected_variant %}{{ option.selected_value }}{% else %}false{% endif %}"
                    style="color: black;">

                    <option
                      value="not-selected"
                      disabled
                      selected
                      data-variant-option-value-wrapper
                      data-variant-option-value
                      data-variant-option-value-index="{{ forloop.index }}">
                      {{ option.placeholder_text }}
                    </option>

                    {% for value in option.options %}
                      <p>{{ value.sub_options }}</p>
                      {% if value.display %}
                        <option
                          value="{{ value.value | escape }}"
                          {% if selected_variant and option.selected_value == value %}
                          selected{% endif %}
                          data-variant-option-value-wrapper
                          data-variant-option-value
                          data-variant-option-value-index="
                        {{ forloop.index }}
                        ">
                          {{ value.name }}
                        </option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </span>
              </div>
            {% else %}
              <input
                type="hidden"
                name="properties[{{option.name}}]"
                value=""
                class="{{option.name}}HiddenInput">
              <div class="selector-wrapper" style="{% if option.show_size_chart %}justify-content:space-between;flex-direction:column;gap:0;{%endif%}">
                <div style="{% if option.show_size_chart %}display:flex;align-items:baseline;justify-content:space-between;width:100%;{%endif%}">
                  <label
                    for="data-variant-option-{{ forloop.index }}"
                    data-variant-option-name="{{ option.heading }}"
                    data-variant-option-choose-name="{{ option.heading }}">
                    {{ option.heading }} {{ option.sub_options }}
                  </label>
                  {% if option.show_size_chart %}
                    <div>
                      <h4 class="open-new-pajamas-size-chart-header">
                        <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                          <span class="icon sizeChartIcon"  data-icon="right-caret">  
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                          </span>
                        </a>
                      </h4>
                      <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                        <div class="modal-overlay"></div>
                        <div class="modal-content">
                          <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                          <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                            <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                          </div>
                          <div class="delete-button">
                            <span  onclick="closePopup()">x</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {%endif%}
                </div>
                <span class="select" data-dropdown-form-style="">
                  <select
                    class="single-option-selector nativeOption {{option.name}}Option"
                    onchange="handleNativeAction('{{option.heading}}',this,'','dropdown', '', {{option.show_related_images}});"
                    id="data-variant-option-{{ forloop.index }}"
                    data-variant-option
                    data-variant-option-index="{{ forloop.index }}"
                    data-variant-option-chosen-value="{% if selected_variant %}{{ option.selected_value }}{% else %}false{% endif %}"
                    style="color: #black;">

                    <option
                      value="not-selected"
                      disabled
                      selected
                      data-variant-option-value-wrapper
                      data-variant-option-value
                      data-variant-option-value-index="{{ forloop.index }}"
                      data-sub-options="">
                      {{ option.placeholder_text }}
                    </option>

                    {% for value in option.options %}
                      <p>{{ value.sub_options }}</p>
                      {% if value.display %}
                        <option
                          value="{{ value.value | escape }}"
                          {% if selected_variant and option.selected_value == value %}
                          selected{% endif %}
                          data-variant-option-value-wrapper
                          data-variant-option-value
                          data-variant-option-value-index="
                          {{ forloop.index }}
                          "
                          data-sub-options="{{ value.sub_options }}">
                          {{ value.name }}
                        </option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </span>
              </div>
            {% endif %}
          {%- elsif option.type == "button" -%}
            <input
              type="hidden"
              name="properties[{{option.name}}]"
              class="{{option.name}}HiddenInput"
              value="">
            {%- if option.shape == 'circle' -%}
              <div class="selector-wrapper defaultNumberOfOptions" style="{% if option.show_size_chart %}justify-content:space-between;flex-direction:column;gap:0;{%endif%}">
                <div style="{% if option.show_size_chart %}display:flex;align-items:baseline;justify-content:space-between;width:100%{%endif%}">
                  <div>
                    <label
                      for="data-variant-option-{{ forloop.index }}"
                      data-variant-option-name="{{ option.heading }}"
                      data-variant-option-choose-name="{{ option.heading }}">
                      {{ option.heading }}
                    </label>
                  </div>
                  {% if option.show_size_chart %}
                    <div>
                      <h4 class="open-new-pajamas-size-chart-header">
                        <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                          <span class="icon sizeChartIcon"  data-icon="right-caret">  
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                          </span>
                        </a>
                      </h4>
                      <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                        <div class="modal-overlay"></div>
                        <div class="modal-content">
                          <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                          <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                            <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                          </div>
                          <div class="delete-button">
                            <span  onclick="closePopup()">x</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {%endif%}
                </div>
                <span>
                  {% for value in option.options %}
                    {% if value.display %}
                      {% assign headingWithHyphen = option.heading | replace: " ", "_" %}
                      <button
                        type="button"
                        class="rounded-button {{headingWithHyphen}}CircleButton"
                        onclick="handleNativeAction('{{option.heading}}',this, '.rounded-button','button','Circle', {{option.show_related_images}})">{{ value.name }}</button>
                    {% endif %}
                  {%- endfor -%}
                </span>
              </div>
            {%- elsif option.shape == 'rectangle' -%}
              <div class="{{option.heading}}"></div>
              <div class="selector-wrapper defaultNumberOfOptions" style="{% if option.show_size_chart %}justify-content:space-between;flex-direction:column;gap:0;{%endif%}">
                <div style="{% if option.show_size_chart %}display:flex;align-items:baseline;justify-content:space-between;width:100%{%endif%}">
                  <div>  
                    <label
                      for="data-variant-option-{{ forloop.index }}"
                      data-variant-option-name="{{ option.heading }}"
                      data-variant-option-choose-name="{{ option.heading }}">
                      {{ option.heading }}
                    </label>
                  </div>
                  {% if option.show_size_chart %}
                    <div>
                      <h4 class="open-new-pajamas-size-chart-header">
                        <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                          <span class="icon sizeChartIcon"  data-icon="right-caret">  
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                          </span>
                        </a>
                      </h4>
                      <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                        <div class="modal-overlay"></div>
                        <div class="modal-content">
                          <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                          <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                            <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                          </div>
                          <div class="delete-button">
                            <span  onclick="closePopup()">x</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {%endif%}
                </div>
                
                <span class="optionsContainer">
                  {% for value in option.options %}
                    {% if value.display %}
                      {% assign headingWithHyphen = option.heading | replace: " ", "_" %}
                      <button
                        type="button"
                        class="rectangle-button {{headingWithHyphen}}RectangleButton"
                        onclick="handleNativeAction('{{option.heading}}',this, '.rectangle-button', 'button','Rectangle', {{option.show_related_images}})">{{ value.name }}</button>
                    {% endif %}
                  {%- endfor -%}
                </span>
              </div>
            {%- endif -%}
          {%- elsif option.type == "swatches" -%}
            <input
              type="hidden"
              name="properties[{{option.name}}]"
              class="{{option.name}}HiddenInput"
              value="">
            <div class="swatches-select" style="margin-bottom: 10px;">
              <div class="swatches-label" style="{% if option.show_size_chart %}display:flex;justify-content:space-between;{%endif%}">
                <div>
                  <label
                    for="data-variant-option-{{ forloop.index }}"
                    data-variant-option-name="{{ option.heading }}"
                    data-variant-option-choose-name="{{ option.heading }}">
                    {{ option.heading }}:
                    <span id="{{option.heading}}-selected-swatches"></span>
                  </label>
                </div>
                {% if option.show_size_chart %}
                  <div>
                    <h4 class="open-new-pajamas-size-chart-header">
                      <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
                        <span class="icon sizeChartIcon"  data-icon="right-caret">  
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
                        </span>
                      </a>
                    </h4>
                    <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
                      <div class="modal-overlay"></div>
                      <div class="modal-content">
                        <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
                        <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                          <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
                        </div>
                        <div class="delete-button">
                          <span  onclick="closePopup()">x</button>
                        </div>
                      </div>
                    </div>
                  </div>
                {%endif%}
              </div>
              <div style="margin-bottom: 15px;">
                {% for value in option.options %}
                  {% if value.display %}
                    <label style="margin-right: 10px;">
                      <input
                        type="radio"
                        name="{{option.name}}"
                        value="{{value.name}}">
                      <img
                        src="{{value.image}}"
                        alt="{{value.name}}"
                        title="{{value.name}}"
                        width="50px"
                        height="50px"
                        onclick="handleNativeAction('{{option.heading}}','{{value.name}}','','swatches','', {{option.show_related_images}})">
                    </label>
                  {% endif %}
                {%- endfor -%}
              </div>
            </div>
          {%- elsif option.type == "text_field" -%}
            <input
              type="hidden"
              name="properties[{{option.name}}]"
              class="{{option.name}}HiddenInput"
              value="">
            <div class="pajamas-pdp-line-item-size-wrapper" style="margin-bottom: 5px;">
              <label
                for="data-variant-option-{{ forloop.index }}"
                data-variant-option-name="{{ option.name }}"
                data-variant-option-choose-name="{{ option.name }}"
                style="font-weight: 600;">
                {{ option.heading }}:
              </label>
            </div>
            <div class="pajamas-pdp-line-item-size-wrapper" style="margin-bottom: 20px;">
              <input
                style="border-radius: 10px;border:1px solid"
                type="text"
                name="properties[_option_Name]"
                onchange="handleInputChange('{{option.name}}',event)"
                value=""
                placeholder="{{option.placeholder_text}}"
                id="custom-text-field"
                class="input">
            </div>
          {%- endif -%}
        {%- endfor -%}
      {% else %}
        {% for option in product.options_with_values %}
          {% assign option_index = forloop.index0 %}
          {% assign show_swatches = false %}
          {% if enable_swatches %}
            {%- assign swatches_option_trigger = swatches_option_trigger | strip | downcase -%}
            {%- assign option_name = option.name | strip | downcase -%}

            {% if option_name == swatches_option_trigger %}
              {% assign show_swatches = true %}
              {% assign swatch_option_key = 'option' | append: forloop.index %}
            {% endif %}
          {% endif %}

          {% if style == 'select' and show_swatches == false %}
            {%
              render 'options-select'
              , product: product
              , selected_variant: selected_variant
              , option: option
              , option_index: option_index
            %}
          {% elsif style == 'radio' or show_swatches == true %}
            {%
              render 'options-radios'
              , product: product
              , selected_variant: selected_variant
              , option: option
              , option_index: option_index
              , show_swatches: show_swatches
              , swatch_option_key: swatch_option_key
              , swatch_size: swatches_product_page_size
              , swatches_option_style: swatches_option_style
              , swatch_file_type: swatch_file_type
              , swatches_custom_colors: swatches_custom_colors
              , swatches_shape: swatches_shape
              ,
            %}
          {% endif %}
        {% endfor %}
      {% endif %}
    {% endif %}

    <variant-selection
      {% if variant_selection_id != blank %}
      id="{{ variant_selection_id }}"
      {% endif %}
      class="variant-selection"
      product-url="{{ product.url }}.js"
      variant="{% if selected_variant %}{{ selected_variant.id }}{% else %}not-selected{% endif %}"
      data-variant-selection>
      {% if product.has_only_default_variant %}
        <input
          class="variant-selection__variants variant-selection__variants--default"
          name="id"
          type="hidden"
          value="{{ product.variants.first.id }}"
          data-variants>
      {% else %}
        <noscript>
          <style>
            .variant-selection__variants {
              display: block !important;
            }
          </style>
        </noscript>
        <select
          class="variant-selection__variants"
          name="id"
          style="display: none"
          data-variants>
          <option
            value="not-selected"
            disabled
            {% if selected_variant == blank %}
            selected{% endif %}>
            {{ 'product.variants.choose_variant' | t }}
          </option>
          {% for variant in product.variants %}
            <option
              {% if selected_variant and selected_variant.id == variant.id %}
              selected{% endif %}
              value="{{ variant.id }}"
              {% unless variant.available %}
              disabled{% endunless %}>
              {{ variant.title }} - {{ variant.price | money }}
            </option>
          {% endfor %}
        </select>

        {% comment %}Inject @pixelunion/shopify-variants-ui/options-selection begin{% endcomment %}
        {% comment %}
          Display variant options for a product

          @param product
          @param selected_variant
          @param variant_selection_id
          @param sold_out_options
          @param style

          @param enable_swatches
          @param swatches_shape
          @param swatches_option_trigger
          @param swatches_option_style
          @param swatches_product_page_size
          @param swatches_custom_colors
          @param swatch_file_type
        {% endcomment %}

        <options-selection
          {% if variant_selection_id != blank %}
          variant-selection="{{ variant_selection_id }}"
          {% endif %}
          style="display: none;"
          {% if sold_out_options == 'disabled' %}
          disable-unavailable{% endif %}
          {% if sold_out_options == 'hidden' %}
          remove-unavailable{% endif %}
          data-options-selection>
          <script>
            (function() {
              const scriptTag = document.scripts[document.scripts.length - 1];
              const parentTag = scriptTag.parentNode;
                    
              parentTag.style.display = '';
            })()
          </script>
        </options-selection>
        {% comment %}Inject @pixelunion/shopify-variants-ui/options-selection end{% endcomment %}

      {% endif %}
    </variant-selection>

    {% assign heading = product.metafields.cuddleclones.single_uploader.value %}
    {% if product.tags contains "uploader" %}
      {% if product.tags contains "crop" %}
        {% if heading %}
          <h3 style="margin-bottom: 10px;">{{ heading[0].heading_text }}</h3>
        {% endif %}
        <div class="image-uploader" id="image-uploader-1">
          <button class="close-button" onclick="closeImageUploader(event)">✖</button>
          <h3 class="cropHeading">
            Please select the area of the image you want to display
          </h3>
          <div class="selected-image-1 selected-image-container"></div>
          <div class="finishCropButton">
            <button onclick="handleCroppedImage(event)">FINISH CROPPING</button>
          </div>
          <input
            type="hidden"
            name="properties[_image_url]"
            id="hiddenInputCropped"
            value="">
          <div class="uploadOuter">
            <span class="dragBoxUnique">
              <span class="dragDropText">Drag + Drop</span>
              <input
                onclick="this.value=null;"
                type="file"
                class="image_uploader"
                onChange="dragNdropUnique(event, 'image-uploader-1',1)"
                ondragover="drag()"
                ondrop="drop()"
                id="uploadFile-1" />
              <span class="orText">
                <strong>or</strong>
              </span>
              <label
                for="uploadFile"
                class="btn btn-primary"
                class="selectFileLabel">
                <img
                  style="height: 19px; width: 25px"
                  src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/7500a60f83aabde22eb73dc843c72079.png?v=1706695966"
                  alt="logo" />
                Select File
              </label>
            </span>
          </div>
        </div>
      {% elsif product.tags contains "multi-uploader" %}
        <div class="multi-image-uploader"></div>
      {% else %}

        {% if heading %}
          <label class="uploaderHeading" for="uploadFile-1">{{ heading[0].heading_text }}</label>
        {% endif %}
        <div class="image-uploader" id="image-uploader-1">
          <button class="close-button-simple">✖</button>
          <div class="selected-image-1 selected-image-container-simple"></div>
          <input
            type="hidden"
            name="properties[_image_url]"
            id="hiddenInputSimple"
            value="">
          <div class="uploadOuter">
            <span class="dragBoxUnique">
              <span class="dragDropText">Drag + Drop</span>
              <input
                onclick="this.value=null;"
                type="file"
                class="image_uploader"
                onChange="dragNdrop(event, 'image-uploader-1',1)"
                ondragover="drag()"
                ondrop="drop()"
                id="uploadFile-1" />
              <span class="orText">
                <strong>or</strong>
              </span>
              <label for="uploadFile" class="btn btn-primary">
                <img
                  style="height: 19px; width: 25px"
                  src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/7500a60f83aabde22eb73dc843c72079.png?v=1706695966"
                  alt="logo" />
                Select File
              </label>
            </span>
          </div>
        </div>
        {% if heading and heading[1].display %}
          <div style="margin-top: 20px;">
            <div class="pajamas-pdp-line-item-size-wrapper" style="margin-bottom: 5px;">
              <label
                for="custom-text-field"
                data-variant-option-name="{{ forloop.index }}"
                data-variant-option-choose-name="{{ heading[1].name }}"
                style="font-weight: 600;">{{ heading[1].heading }}:</label>
            </div>
            <div class="pajamas-pdp-line-item-size-wrapper" style="margin-bottom:20px;">
              <input
                style="border-radius: 10px;border:1px solid;max-width:100%;"
                type="text"
                value=""
                name="properties[{{heading[1].name}}]"
                placeholder="{{heading[1].placeholder}}"
                id="custom-text-field"
                class="input">
            </div>
          </div>
        {% endif %}
      {% endif %}

    {% endif %}

    {% assign extra_options = product.metafields.cuddleclones.extras.value %}

    {% if extra_options %}
      {% assign cartItems = cart.items %}
      {% assign showLabel = false %}
      {% assign hasOtherOptions = false %}

      {% for option in extra_options.extras_products %}
        {% if option.identifier == "rush" %}

          {% assign  showInternationalRush =  true%}
          {%if option.show_international_rush == false and localization.country.iso_code != 'US'%}
            {% assign  showInternationalRush =  false%}
          {%endif%}

          {% assign rushDisplay = option.display %}
          {% assign isAlreadyCartContainRush = false %}

          {% for item in cartItems %}
            {% for property in item.properties %}
              {% if property.first == '_rush_key' and property.last == option.rush_key_value %}
                {% assign isAlreadyCartContainRush = true %}
              {% endif %}
            {% endfor %}
          {% endfor %}
          {% if product.tags contains 'rush-enable' %}
            <input
              type="hidden"
              name="properties[_original_rush_key]"
              value="{{option.rush_key_value}}">
          {% endif %}
        {% else %}
          {% if option.display %}
            {% assign hasOtherOptions = true %}
          {% endif %}
        {% endif %}
      {% endfor %}

      {% if showInternationalRush%}
        {% if isAlreadyCartContainRush and hasOtherOptions%}
          {% assign showLabel = true %}
        {% elsif rushDisplay and isAlreadyCartContainRush == false %}
          {% assign showLabel = true %}
        {% elsif rushDisplay == false and hasOtherOptions %}
          {% assign showLabel = true %}
        {% endif %}
      {% endif %}

      {% if showLabel %}
        <div class="selector-wrapper" style="margin-top: 30px;">
          <label
            for="data-variant-option-extras"
            data-variant-option-name="{{ extra_options.main_heading | replace: ' ','-' }}"
            data-variant-option-choose-name="{{ extra_options.main_heading | replace: ' ','-' }}">
            {{ extra_options.main_heading }}
          </label>
        </div>
      {% endif %}

      <div class="extraOptionsContainer">
        {% for option in extra_options.extras_products %}
          {% if option.display %}
            {% if option.identifier == "rush" %} 
              {% if isAlreadyCartContainRush == false and showInternationalRush%}
                <div class="extraOptionDiv">
                  <div class="optionImageDiv">
                    <img
                      class="optionImage"
                      width="76px"
                      height="76px"
                      src="{{ option.imageUrl }}"
                      alt="{{ option.name }}">
                  </div>
                  <div class="optionDetailContainer">
                    <div class="optionLabel">
                      {{ option.label }}
                    </div>
                    <div class="optionDescription">
                      {{ option.description }}
                    </div>
                    <div class="optionPrice">
                      +${{ option.price }}</div>
                  </div>
                  <div class="optionCheckboxContainer">
                    <input
                      type="checkbox"
                      class="extraOption"
                      aria-label="Add {{ option.label }}. {{ option.description }}. Cost ${{ option.price }}"
                      id="option-{{ forloop.index }}"
                      {%if option.rush_key_value and option.rush_key_value!=""%}
                        data-identifier="{{ option.identifier }}"
                        data-rush-key="{{ option.rush_key_value }}"
                      {%endif%}
                      name="extra_options"
                      value="{{ option.id }}">
                    <label for="option-{{ forloop.index }}"></label>
                  </div>
                </div>
              {% endif %}
            {% else %}
              <div class="extraOptionDiv">
                <div class="optionImageDiv">
                  <img
                    class="optionImage"
                    width="76px"
                    height="76px"
                    src="{{ option.imageUrl }}"
                    alt="{{ option.name }}">
                </div>
               
                <div class="optionDetailContainer">
                  <div class="optionLabel">
                    {{ option.label }}
                  </div>
                  <div class="optionDescription">
                    {{ option.description }}
                  </div>
                  <div class="optionPrice">
                    +${{ option.price }}</div>
                </div>
                <div class="optionCheckboxContainer">
                  <input
                    type="checkbox"
                    class="extraOption"
                    aria-label="Add {{ option.label }}. {{ option.description }}. Cost ${{ option.price }}"
                    id="option-{{ forloop.index }}"
                    data-identifier="{{ option.identifier }}"
                    data-rush-key="{{ option.rush_key_value }}"
                    name="extra_options"
                    value="{{ option.id }}">
                  <label for="option-{{ forloop.index }}"></label>
                </div>
              </div>
            {% endif %}
          {% endif %}
        {% endfor %}
      </div>
    {% endif %}

    {% comment %}Inject @pixelunion/shopify-variants-ui/variant-selection end{% endcomment %}

    {% if product.available %}
      {% if settings.display_inventory_left %}
        <p class="items_left">
          {% if selected_variant.inventory_management != blank and selected_variant.inventory_quantity > 0 %}
            {% capture items_left_text %}
              {% if selected_variant.inventory_quantity == 1 %}
                {{ 'products.product.items_left_count.one' | t }}
              {% else %}
                {{ 'products.product.items_left_count.other' | t }}
              {% endif %}
            {% endcapture %}
            {% if selected_variant.inventory_quantity <= settings.inventory_threshold %}
              {{ selected_variant.inventory_quantity }} {{ items_left_text }}
            {% endif %}
          {% endif %}
        </p>
      {% endif %}

      <div class="purchase-details has-quantity-box-{{ settings.display_product_quantity }}">
        {% unless show_payment_button %}
          {% comment %}Inject @pixelunion/pxs-gift-card-recipient-form/recipient-form begin{% endcomment %}
          {% comment %}
            @param form {Object}
            The form object

            @param section {Object}
            The section object

            @param show_recipient_form {Boolean}
            If true, show the gift card recipient form

            @param show_recipient_form_placeholders {Boolean}
            If true, show recipient form placeholder attributes

            @param show_recipient_form_labels {Boolean}
            If true, show recipient form input labels

            @param show_recipient_form_max_characters_message {Boolean}
            If true, show the recipient form max characters message

            @param recipient_form_textarea_rows {Number}
            A numerical value to specify the number of textarea rows

            @param recipient_form_textarea_classes {String}
            A space separated list of class names added to recipient form textarea

            @param recipient_form_input_classes {String}
            A space separated list of class names added to recipient form inputs

            @param recipient_form_label_classes {String}
            A space separated list of class names added to recipient form labels

            @param recipient_form_label_position {String}
            The recipient form input label position, either 'top', or 'bottom'
          {% endcomment %}

          {% liquid
            assign show_recipient_form_placeholders = show_recipient_form_placeholders | default: false
            assign show_recipient_form_labels = show_recipient_form_labels | default: true, allow_false: true
            assign show_recipient_form_max_characters_message = show_recipient_form_max_characters_message | default: true, allow_false: true
            assign recipient_form_textarea_rows = recipient_form_textarea_rows | default: 5
            assign recipient_form_label_position = recipient_form_label_position | default: 'top'
          %}

          {%- if show_recipient_form and product.gift_card? -%}
            <details class="recipient-disclosure" data-recipient-disclosure>
              <summary class="recipient-disclosure__summary">
                <label class="recipient-disclosure__checkbox-label" for="recipient-disclosure__checkbox-{{ section.id }}">
                  <input
                    class="recipient-disclosure__checkbox"
                    id="recipient-disclosure__checkbox-{{ section.id }}"
                    type="checkbox"
                    name="properties[__shopify_send_gift_card_to_recipient]"
                    tabindex="-1"
                    data-recipient-disclosure-checkbox>

                  {%- render 'recipient-form-disclosure-icon' -%}

                  <span class="recipient-disclosure__checkbox-label-text">
                    {{- 'recipient.form.checkbox_label' | t -}}
                  </span>
                </label>
              </summary>

              <div class="recipient-form" data-recipient-form>
                <div class="recipient-form__field">
                  {%- if show_recipient_form_labels -%}
                    {%- capture recipient_email_label -%}
                      <label
                        class="
                          recipient-form__label
                          {{ recipient_form_label_classes }}
                        "
                        for="recipient-email-{{ section.id }}"
                      >
                        {{- 'recipient.form.email_label' | t -}}
                        <span class="recipient-form__required"> *</span>
                      </label>
                    {%- endcapture -%}
                  {%- endif -%}

                  {%- capture recipient_email_input -%}
                    <input
                      class="
                        recipient-form__input
                        recipient-form__input--email
                        {{ recipient_form_input_classes }}
                      "
                      id="recipient-email-{{ section.id }}"
                      type="email"
                      {%- if show_recipient_form_placeholders -%}
                        placeholder="{{- 'recipient.form.email_placeholder' | t -}}"
                      {%- endif -%}
                      {%- unless show_recipient_form_labels -%}
                        aria-label="{{- 'recipient.form.email_placeholder' | t -}}"
                      {%- endunless -%}
                      name="properties[Recipient email]"
                      value="{{ form.email }}"
                      data-recipient-form-input
                      data-recipient-form-email-input
                    >
                  {%- endcapture -%}
                  {%- capture recipient_error_message -%}
                    <span class="recipient-form__error-message" data-recipient-form-error-message>
                      {{- 'recipient.form.error_message' | t -}}
                    </span>
                  {%- endcapture -%}
                  {%- if recipient_form_label_position == 'top' -%}
                    <div class="recipient-form__input-wrapper">
                      {{- recipient_email_label -}}
                      {{- recipient_email_input -}}
                    </div>
                    {{- recipient_error_message -}}
                  {%- else -%}
                    <div class="recipient-form__input-wrapper">
                      {{- recipient_email_input -}}
                      {{- recipient_email_label -}}
                    </div>
                    {{- recipient_error_message -}}
                  {%- endif -%}
                </div>

                <div class="recipient-form__field">
                  <div class="recipient-form__input-wrapper">
                    {%- if show_recipient_form_labels -%}
                      {%- capture recipient_name_label -%}
                        <label
                          class="
                            recipient-form__label
                            {{ recipient_form_label_classes }}
                          "
                          for="recipient-name-{{ section.id }}"
                        >
                          {{- 'recipient.form.name_label' | t -}}
                        </label>
                      {%- endcapture -%}
                    {%- endif -%}

                    {%- capture recipient_name_input -%}
                      <input
                        class="
                          recipient-form__input
                          recipient-form__input--name
                          {{ recipient_form_input_classes }}
                        "
                        id="recipient-name-{{ section.id }}"
                        type="text"
                        {%- if show_recipient_form_placeholders -%}
                          placeholder="{{- 'recipient.form.name_placeholder' | t -}}"
                        {%- endif -%}
                        {%- unless show_recipient_form_labels -%}
                          aria-label="{{- 'recipient.form.name_placeholder' | t -}}"
                        {%- endunless -%}
                        name="properties[Recipient name]"
                        value="{{ form.name }}"
                        data-recipient-form-input
                      >
                    {%- endcapture -%}
                    {%- if recipient_form_label_position == 'top' -%}
                      {{ recipient_name_label }}
                      {{ recipient_name_input }}
                    {%- else -%}
                      {{ recipient_name_input }}
                      {{ recipient_name_label }}
                    {%- endif -%}
                  </div>
                </div>

                <div class="recipient-form__field">
                  <div class="recipient-form__input-wrapper">
                    {%- liquid
                      assign max_characters_length = 200

                      if show_recipient_form_max_characters_message
                        assign max_characters_message = 'recipient.form.max_characters' | t: max_characters: max_characters_length
                      endif
                    -%}

                    {%- if show_recipient_form_labels -%}
                      {%- capture recipient_message_label -%}
                        <label
                          class="
                            recipient-form__label
                            {{ recipient_form_label_classes }}
                          "
                          for="recipient-message-{{ section.id }}"
                        >
                          {{- 'recipient.form.message_label' | t -}}
                        </label>
                      {%- endcapture -%}
                    {%- endif -%}

                    {%- capture recipient_message_input -%}
                      <textarea
                        class="
                          recipient-form__input
                          recipient-form__input--message
                          {{ recipient_form_input_classes }}
                          {{ recipient_form_textarea_classes }}
                        "
                        id="recipient-message-{{ section.id }}"
                        {%- if show_recipient_form_placeholders -%}
                          placeholder="{{- 'recipient.form.message_placeholder' | t -}}"
                        {%- endif -%}
                        {%- unless show_recipient_form_labels -%}
                          aria-label="{{- 'recipient.form.message_placeholder' | t -}}"
                        {%- endunless -%}
                        name="properties[Message]"
                        {%- if recipient_form_textarea_rows != 0 -%}
                          rows="{{ recipient_form_textarea_rows }}"
                        {%- endif -%}
                        maxlength="{{ max_characters_length }}"
                        data-recipient-form-input
                      >
                        {{- form.message -}}
                      </textarea>
                    {%- endcapture -%}
                    {%- if recipient_form_label_position == 'top' -%}
                      {{ recipient_message_label }}
                      {{ recipient_message_input }}
                    {%- else -%}
                      {{ recipient_message_input }}
                      {{ recipient_message_label }}
                    {%- endif -%}
                  </div>

                  {%- if show_recipient_form_max_characters_message -%}
                    <span class="recipient-form__max-characters-message">
                      <small>
                        {{- max_characters_message -}}
                      </small>
                    </span>
                  {%- endif -%}
                </div>
              </div>
            </details>
          {%- endif -%}
          {% comment %}Inject @pixelunion/pxs-gift-card-recipient-form/recipient-form end{% endcomment %}

        {% endunless %}

        {% if settings.display_product_quantity %}
          <div class="purchase-details__quantity product-quantity-box quantity-box--{{ settings.qty_box_style }}">
            {%
              render 'quantity-box'
              , variant: selected_variant
              , selection
            %}
          </div>
        {% endif %}

        {% if show_payment_button %}
          {% comment %}Inject @pixelunion/pxs-gift-card-recipient-form/recipient-form begin{% endcomment %}
          {% comment %}
            @param form {Object}
            The form object

            @param section {Object}
            The section object

            @param show_recipient_form {Boolean}
            If true, show the gift card recipient form

            @param show_recipient_form_placeholders {Boolean}
            If true, show recipient form placeholder attributes

            @param show_recipient_form_labels {Boolean}
            If true, show recipient form input labels

            @param show_recipient_form_max_characters_message {Boolean}
            If true, show the recipient form max characters message

            @param recipient_form_textarea_rows {Number}
            A numerical value to specify the number of textarea rows

            @param recipient_form_textarea_classes {String}
            A space separated list of class names added to recipient form textarea

            @param recipient_form_input_classes {String}
            A space separated list of class names added to recipient form inputs

            @param recipient_form_label_classes {String}
            A space separated list of class names added to recipient form labels

            @param recipient_form_label_position {String}
            The recipient form input label position, either 'top', or 'bottom'
          {% endcomment %}

          {% liquid
            assign show_recipient_form_placeholders = show_recipient_form_placeholders | default: false
            assign show_recipient_form_labels = show_recipient_form_labels | default: true, allow_false: true
            assign show_recipient_form_max_characters_message = show_recipient_form_max_characters_message | default: true, allow_false: true
            assign recipient_form_textarea_rows = recipient_form_textarea_rows | default: 5
            assign recipient_form_label_position = recipient_form_label_position | default: 'top'
          %}

          {%- if show_recipient_form and product.gift_card? -%}
            <details class="recipient-disclosure" data-recipient-disclosure>
              <summary class="recipient-disclosure__summary">
                <label class="recipient-disclosure__checkbox-label" for="recipient-disclosure__checkbox-{{ section.id }}">
                  <input
                    class="recipient-disclosure__checkbox"
                    id="recipient-disclosure__checkbox-{{ section.id }}"
                    type="checkbox"
                    name="properties[__shopify_send_gift_card_to_recipient]"
                    tabindex="-1"
                    data-recipient-disclosure-checkbox>

                  {%- render 'recipient-form-disclosure-icon' -%}

                  <span class="recipient-disclosure__checkbox-label-text">
                    {{- 'recipient.form.checkbox_label' | t -}}
                  </span>
                </label>
              </summary>

              <div class="recipient-form" data-recipient-form>
                <div class="recipient-form__field">
                  {%- if show_recipient_form_labels -%}
                    {%- capture recipient_email_label -%}
                      <label
                        class="
                          recipient-form__label
                          {{ recipient_form_label_classes }}
                        "
                        for="recipient-email-{{ section.id }}"
                      >
                        {{- 'recipient.form.email_label' | t -}}
                        <span class="recipient-form__required"> *</span>
                      </label>
                    {%- endcapture -%}
                  {%- endif -%}

                  {%- capture recipient_email_input -%}
                    <input
                      class="
                        recipient-form__input
                        recipient-form__input--email
                        {{ recipient_form_input_classes }}
                      "
                      id="recipient-email-{{ section.id }}"
                      type="email"
                      {%- if show_recipient_form_placeholders -%}
                        placeholder="{{- 'recipient.form.email_placeholder' | t -}}"
                      {%- endif -%}
                      {%- unless show_recipient_form_labels -%}
                        aria-label="{{- 'recipient.form.email_placeholder' | t -}}"
                      {%- endunless -%}
                      name="properties[Recipient email]"
                      value="{{ form.email }}"
                      data-recipient-form-input
                      data-recipient-form-email-input
                    >
                  {%- endcapture -%}
                  {%- capture recipient_error_message -%}
                    <span class="recipient-form__error-message" data-recipient-form-error-message>
                      {{- 'recipient.form.error_message' | t -}}
                    </span>
                  {%- endcapture -%}
                  {%- if recipient_form_label_position == 'top' -%}
                    <div class="recipient-form__input-wrapper">
                      {{- recipient_email_label -}}
                      {{- recipient_email_input -}}
                    </div>
                    {{- recipient_error_message -}}
                  {%- else -%}
                    <div class="recipient-form__input-wrapper">
                      {{- recipient_email_input -}}
                      {{- recipient_email_label -}}
                    </div>
                    {{- recipient_error_message -}}
                  {%- endif -%}
                </div>

                <div class="recipient-form__field">
                  <div class="recipient-form__input-wrapper">
                    {%- if show_recipient_form_labels -%}
                      {%- capture recipient_name_label -%}
                        <label
                          class="
                            recipient-form__label
                            {{ recipient_form_label_classes }}
                          "
                          for="recipient-name-{{ section.id }}"
                        >
                          {{- 'recipient.form.name_label' | t -}}
                        </label>
                      {%- endcapture -%}
                    {%- endif -%}

                    {%- capture recipient_name_input -%}
                      <input
                        class="
                          recipient-form__input
                          recipient-form__input--name
                          {{ recipient_form_input_classes }}
                        "
                        id="recipient-name-{{ section.id }}"
                        type="text"
                        {%- if show_recipient_form_placeholders -%}
                          placeholder="{{- 'recipient.form.name_placeholder' | t -}}"
                        {%- endif -%}
                        {%- unless show_recipient_form_labels -%}
                          aria-label="{{- 'recipient.form.name_placeholder' | t -}}"
                        {%- endunless -%}
                        name="properties[Recipient name]"
                        value="{{ form.name }}"
                        data-recipient-form-input
                      >
                    {%- endcapture -%}
                    {%- if recipient_form_label_position == 'top' -%}
                      {{ recipient_name_label }}
                      {{ recipient_name_input }}
                    {%- else -%}
                      {{ recipient_name_input }}
                      {{ recipient_name_label }}
                    {%- endif -%}
                  </div>
                </div>

                <div class="recipient-form__field">
                  <div class="recipient-form__input-wrapper">
                    {%- liquid
                      assign max_characters_length = 200

                      if show_recipient_form_max_characters_message
                        assign max_characters_message = 'recipient.form.max_characters' | t: max_characters: max_characters_length
                      endif
                    -%}

                    {%- if show_recipient_form_labels -%}
                      {%- capture recipient_message_label -%}
                        <label
                          class="
                            recipient-form__label
                            {{ recipient_form_label_classes }}
                          "
                          for="recipient-message-{{ section.id }}"
                        >
                          {{- 'recipient.form.message_label' | t -}}
                        </label>
                      {%- endcapture -%}
                    {%- endif -%}

                    {%- capture recipient_message_input -%}
                      <textarea
                        class="
                          recipient-form__input
                          recipient-form__input--message
                          {{ recipient_form_input_classes }}
                          {{ recipient_form_textarea_classes }}
                        "
                        id="recipient-message-{{ section.id }}"
                        {%- if show_recipient_form_placeholders -%}
                          placeholder="{{- 'recipient.form.message_placeholder' | t -}}"
                        {%- endif -%}
                        {%- unless show_recipient_form_labels -%}
                          aria-label="{{- 'recipient.form.message_placeholder' | t -}}"
                        {%- endunless -%}
                        name="properties[Message]"
                        {%- if recipient_form_textarea_rows != 0 -%}
                          rows="{{ recipient_form_textarea_rows }}"
                        {%- endif -%}
                        maxlength="{{ max_characters_length }}"
                        data-recipient-form-input
                      >
                        {{- form.message -}}
                      </textarea>
                    {%- endcapture -%}
                    {%- if recipient_form_label_position == 'top' -%}
                      {{ recipient_message_label }}
                      {{ recipient_message_input }}
                    {%- else -%}
                      {{ recipient_message_input }}
                      {{ recipient_message_label }}
                    {%- endif -%}
                  </div>

                  {%- if show_recipient_form_max_characters_message -%}
                    <span class="recipient-form__max-characters-message">
                      <small>
                        {{- max_characters_message -}}
                      </small>
                    </span>
                  {%- endif -%}
                </div>
              </div>
            </details>
          {%- endif -%}
          {% comment %}Inject @pixelunion/pxs-gift-card-recipient-form/recipient-form end{% endcomment %}

        {% endif %}


        {% if product.tags contains "print-on-demand" %}
          {% if localization.country.iso_code != 'US' %}
            <div style="margin-top: 40px;">
              <p>
                Product is not available for sale in your country
              </p>
            </div>
          {% else %}
            <div class="purchase-details__buttons purchase-details__spb--{{ show_payment_button }}">
              {% if settings.cart_action == 'reload_page' %}
                <input
                  type="hidden"
                  name="return_to"
                  value="back" />
              {% elsif settings.cart_action == 'redirect_cart' %}
                <input
                  type="hidden"
                  name="return_to"
                  value="{{ routes.cart_url }}" />
              {% endif %}

              {% capture add_to_cart_label %}{% if collection_handles contains 'pre-order' %}{{ 'collections.general.pre_order' | t }}{% else %}{{ 'products.product.add_to_cart' | t }}{% endif %}{% endcapture %}
              {% unless product.tags contains "custom-design" or product.tags contains "memorial-gift-box"%}
                <button
                  {% if product.tags contains "show-custom-cart-button" %}
                  id="add-to-cart-button"
                  {% endif %}
                  type="submit"
                  name="add"
                  class="button {% if settings.cart_action == 'mini_cart' or settings.cart_action == 'drawer' %} ajax-submit {% endif %}action_button button--add-to-cart {% if show_payment_button %} action_button--secondary {% endif %}"
                  data-label={{ add_to_cart_label | json }}
                  data-add-to-cart-trigger>
                  {% if settings.button_cart_icon != blank and settings.button_cart_icon_position == 'left' %}
                    {%
                      render 'icon'
                      , name: settings.button_cart_icon
                      , icon_class: 'icon--cart'
                    %}
                  {% endif %}
                  <span class="text">{{ add_to_cart_label }}</span>
                  {% if settings.button_cart_icon != blank and settings.button_cart_icon_position == 'right' %}
                    {%
                      render 'icon'
                      , name: settings.button_cart_icon
                      , icon_class: 'icon--cart icon--right'
                    %}
                  {% endif %}
                  <svg
                    x="0px"
                    y="0px"
                    width="32px"
                    height="32px"
                    viewBox="0 0 32 32"
                    class="checkmark">
                    <path
                      fill="none"
                      stroke-width="2"
                      stroke-linecap="square"
                      stroke-miterlimit="10"
                      d="M9,17l3.9,3.9c0.1,0.1,0.2,0.1,0.3,0L23,11" />
                  </svg>
                </button>
              {% endunless %}
              {% unless product.tags contains "show-custom-cart-button" %}
                {% if show_payment_button %}
                  {{ form | payment_button }}
                {% endif %}
              {% endunless %}
            </div>
          {% endif %}
        {% else %}
          <div class="purchase-details__buttons purchase-details__spb--{{ show_payment_button }}">
            {% if settings.cart_action == 'reload_page' %}
              <input
                type="hidden"
                name="return_to"
                value="back" />
            {% elsif settings.cart_action == 'redirect_cart' %}
              <input
                type="hidden"
                name="return_to"
                value="{{ routes.cart_url }}" />
            {% endif %}

            {% capture add_to_cart_label %}{% if collection_handles contains 'pre-order' %}{{ 'collections.general.pre_order' | t }}{% else %}{{ 'products.product.add_to_cart' | t }}{% endif %}{% endcapture %}
            {% unless product.tags contains "custom-design" or product.tags contains "memorial-gift-box"%}
              <button
                {% if product.tags contains "show-custom-cart-button" %}
                id="add-to-cart-button"
                {% endif %}
                type="submit"
                name="add"
                class="button {% if settings.cart_action == 'mini_cart' or settings.cart_action == 'drawer' %} ajax-submit {% endif %}action_button button--add-to-cart {% if show_payment_button %} action_button--secondary {% endif %}"
                data-label={{ add_to_cart_label | json }}
                data-add-to-cart-trigger>
                {% if settings.button_cart_icon != blank and settings.button_cart_icon_position == 'left' %}
                  {%
                    render 'icon'
                    , name: settings.button_cart_icon
                    , icon_class: 'icon--cart'
                  %}
                {% endif %}
                <span class="text">{{ add_to_cart_label }}</span>
                {% if settings.button_cart_icon != blank and settings.button_cart_icon_position == 'right' %}
                  {%
                    render 'icon'
                    , name: settings.button_cart_icon
                    , icon_class: 'icon--cart icon--right'
                  %}
                {% endif %}
                <svg
                  x="0px"
                  y="0px"
                  width="32px"
                  height="32px"
                  viewBox="0 0 32 32"
                  class="checkmark">
                  <path
                    fill="none"
                    stroke-width="2"
                    stroke-linecap="square"
                    stroke-miterlimit="10"
                    d="M9,17l3.9,3.9c0.1,0.1,0.2,0.1,0.3,0L23,11" />
                </svg>
              </button>
            {% endunless %}
            {% unless product.tags contains "show-custom-cart-button" %}
              {% if show_payment_button %}
                {{ form | payment_button }}
              {% endif %}
            {% endunless %}
          </div>
        {% endif %}
        <div class="cart-warning"></div>
        {%if product.tags contains "enable-cross-sell-products"%}
          {% render 'cross-sell-products'
          , product: product %}
        {%endif%}
      </div>

    {% endif %}
    {%if product.tags contains "bypass_attribute" %}
      <input
        type="hidden"
        name="properties[_Bypass_Attribute]"
        value=""
        id="Bypass_value" />
      {% endif %}
  {% endform %}

  <div class="surface-pick-up surface-pick-up--loading" data-surface-pick-up></div>
  <div class="surface-pick-up__modal" data-surface-pick-up-modal></div>
</div>

<script>
  const api_details = {{oms |  json}}
      const headerForImage = {
      "api-token":
      api_details[0].api_token,
    };
    var preview = document.getElementById("image-uploader-1");
    let cropper;
    let imageType;

    //for cropper image uploader
    function dragNdropUnique(event, containerId, number) {
      
      var previewImg = document.createElement("img");
      var files = event.target.files;

      if (files.length > 1) {
        alert("Please select only one image file.");
        return;
      }

      var file = files[0];
      var fileName = URL.createObjectURL(files[0]);

      if (!file.type.startsWith("image/")) {
        alert("Please select only image files.");
        return;
      }
      
      if (file.type !== "image/jpg" && file.type !== "image/png" && file.type !== "image/bmp" && file.type !== "image/jpeg") {
        alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
        return;
      }


      if (files[0].size > 15 * 1024 * 1024) {
        // Convert MB to bytes
        alert("Please select images smaller than 15MB.");
        return;
      }

      document.querySelector(".close-button").style.display="block";
      document.querySelector(".image-uploader").style.padding="20px";
      imageType = file.type;

      document.querySelector(".cropHeading").style.display = "block";
      document.querySelector(".finishCropButton").style.display = "flex";

      if (event.target.files.length) {
        // start file reader
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target.result) {
            previewImg.setAttribute("id", "imagePreview");
            previewImg.setAttribute("alt", "image-cropped");
            cropper = new Cropper(previewImg, {
              viewMode: 1,
              aspectRatio: 1, 
              guides: true, 
              background: false,
              zoomable:false,
              crop(event) {
                const canvas = cropper.getCroppedCanvas({
                  width: 200, 
                  height: 200, 
                  imageSmoothingEnabled: true,
                  imageSmoothingQuality: "high",
                });

                const roundedCanvas = document.createElement("canvas");
                const roundedContext = roundedCanvas.getContext("2d");
                const radius = canvas.width / 2;

                roundedCanvas.width = canvas.width;
                roundedCanvas.height = canvas.height;

                roundedContext.beginPath();
                roundedContext.arc(radius, radius, radius, 0, Math.PI * 2);
                roundedContext.closePath();
                roundedContext.clip();

                roundedContext.drawImage(canvas, 0, 0);
              },
            });
          }
        };
        reader.readAsDataURL(event.target.files[0]);
      }

      const hiddenUniqueImage = document.querySelector(
        ".selected-image-1" + " img"
      );
      const hiddenUniqueInput = document.querySelector(
        ".uniqueUploadedImage-1"
      );

      previewImg.setAttribute("src", fileName);
      previewImg.setAttribute("alt", "characteristic-image-" + number);
      document.querySelector(".uploadOuter").style.display = "none";

      // Find existing image container if it exists
      var existingImageContainer = preview.querySelector(".selected-image-1");

      if (existingImageContainer) {
        // If there's already an image, replace it with the new one
        var existingImage = existingImageContainer.querySelector("img");

        // existingImageContainer.appendChild(deleteButtonDiv); // Add new image
        existingImageContainer.appendChild(previewImg); // Add new image
      } else {
        // If there's no existing image, create a new image container
        existingImageContainer = document.createElement("div");
        existingImageContainer.classList.add("selected-image-1");
        existingImageContainer.appendChild(previewImg); // Add new image
      }
    }

    // for cropped image deletion
    async function deleteUniqueImage(event) {
      document.querySelector(".image-uploader").style.cssText =
        "width: 100%; margin: auto;";
      const imageContainer = event.target.closest(".selected-image-container");
      const imageElement = imageContainer.querySelector('img');
      const imageUrl = imageElement.src;
      const parts = imageUrl.split('/');
      let imageName = parts[parts?.length - 1];
      imageContainer.innerHTML = "";
      // if (imageName) {
      //   await fetch(`${api_details[0].url}image_uploader/delete_image?image_name=${imageName}`, {
      //           headers: headerForImage,
      //           method: 'DELETE'
      //       })
      //       .then(response => {
      //           if (!response.ok) {
      //               throw new Error(`HTTP error! Status: ${response.status}`);
      //           }
      //           return response.json();
      //       })
      //       .then(data => {
      //           console.log('Previous image deleted:', data);
      //       })
      //       .catch(error => {
      //           console.error('Error deleting previous image:', error);
      //       });
      // }
      
    }

    let imgSrc;

    function handleCroppedImage(e) {
      document.querySelector(".selected-image-container").style.padding="20px";
      document.querySelector(".cropHeading").style.display = "none";
      document.querySelector(".finishCropButton").style.display = "none";
      document.querySelector(".image-uploader").style.cssText =
        "max-width: 243px; margin: 0;";

      document.querySelector(".cropper-container").style.display = "none";

      // Show the cropped image container
      document.getElementById("imagePreview").style.display = "block";

      e.preventDefault();
      // get result to data uri
      let previewImg = document.getElementById("imagePreview");
      previewImg.style.setProperty("display", "block", "important");
      e.preventDefault();
      // get result to data uri
      imgSrc = cropper.getCroppedCanvas().toDataURL();

      // show image cropped
      previewImg.src =
        "https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif";
      previewImg.style.setProperty("border-radius", "50%");
      let blob = base64toBlob(
        imgSrc.replace(/^data:image\/(png|jpg|jpeg);base64,/, ""),
        imageType
      );
      uploadImage(blob);
    }

    //for handling cropping image
    function base64toBlob(base64Data, contentType) {
      contentType = contentType || "";
      var sliceSize = 1024;
      var byteCharacters = atob(base64Data);
      var byteArrays = [];

      for (
        var offset = 0;
        offset < byteCharacters.length;
        offset += sliceSize
      ) {
        var slice = byteCharacters.slice(offset, offset + sliceSize);

        var byteNumbers = new Array(slice.length);
        for (var i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }

        var byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }

      var blob = new Blob(byteArrays, { type: contentType });
      return blob;
    }

    function uploadImage(blob) {
      const cartButton = document.getElementById("add-to-cart-button");
      let hiddenInputCropped=document.getElementById("hiddenInputCropped");
      let previewImg = document.getElementById("imagePreview");

      let formData = new FormData();
      formData.append("image", blob, "image." + imageType.split("/")[1]);

      // Upload the new image
      fetch(`${api_details[0].url}/image_uploader/print_image`, {
        method: "POST",
        body: formData,
        headers: {
          ...headerForImage,
        },
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          previewImg.setAttribute("src", data.imageUrl);
          let newImageUrl=data.imageUrl+'?x-oss-process=image/circle,r_1000';
          hiddenInputCropped.value = newImageUrl;
          cartButton.style.cssText="cursor:pointer;opacity:1;pointer-events:auto";
          cartButton.removeAttribute("disabled");
        })
        .catch((error) => {
          console.error("Error uploading new image:", error);
        });
    }

    //for discard image uploading that is used for cropping
    function closeImageUploader(event){
      event.preventDefault();
      document.querySelector(".image-uploader").style.padding="0px";
      document.querySelector(".selected-image-container").style.padding="0px";
      document.querySelector(".close-button").style.display="none";
      const imageContainer = document.querySelector(".selected-image-container");
      const imageElement = document.querySelector(".selected-image-container img");
      document.querySelector(".cropHeading").style.display = "none";
      document.querySelector(".finishCropButton").style.display = "none";
      document.querySelector(".image-uploader").style.cssText =
        "width: 100%; margin: auto;";
      document.querySelector(".uploadOuter").style.display = "block";
      const imageUrl = imageElement.src;
      const parts = imageUrl.split('/');
      let imageName = parts[parts?.length - 1];
      imageContainer.innerHTML = "";
      // if (imageName) {
      //   fetch(`${api_details[0].url}image_uploader/delete_image?image_name=${imageName}`, {
      //           headers: headerForImage,
      //           method: 'DELETE'
      //       })
      //       .then(response => {
      //           if (!response.ok) {
      //               throw new Error(`HTTP error! Status: ${response.status}`);
      //           }
      //           return response.json();
      //       })
      //       .then(data => {
      //           console.log('Previous image deleted:', data);
      //       })
      //       .catch(error => {
      //           console.error('Error deleting previous image:', error);
      //       });
      // }
      const cartButton = document.getElementById("add-to-cart-button");
      cartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
      cartButton.setAttribute("disabled", "true");
    }

    //Image uploader single without cropping and multi-uploader functionality
    async function dragNdrop(event, containerId, number){
      var files = event.target.files;

      if (files.length > 1) {
        alert("Please select only one image file.");
        return;
      }

      var file = files[0];
      var fileName = URL.createObjectURL(files[0]);

      
      if (!file.type.startsWith("image/")) {
        alert("Please select only image files.");
        return;
      }

      if (file.type !== "image/jpg" && file.type !== "image/png" && file.type !== "image/bmp" && file.type !== "image/jpeg") {
        alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
        return;
      }

      if (files[0].size > 15 * 1024 * 1024) {
        // Convert MB to bytes
        alert("Please select images smaller than 15MB.");
        return;
      }

      let uploadedImageName;
      var previewImg = document.createElement("img");
      previewImg.src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif";


      const imageContainer=document.querySelector(".selected-image-container-simple");
      document.querySelector(".close-button-simple").style.display="block";
      document.querySelector(".image-uploader").style.padding="20px";

      previewImg.setAttribute("alt", "Image Simple Uploader");
      previewImg.setAttribute("id", "previewImageSimple");
      imageContainer.appendChild(previewImg);

      document.querySelector(".uploadOuter").style.display = "none";

      let hiddenInputSimple=document.getElementById("hiddenInputSimple");
      const cartButton = document.getElementById("add-to-cart-button");


      const formData = new FormData();
      formData.append('image', files[0]); 

      let contains_custom_option = false;
      let contains_native_option = false;

      let product={{product.tags | json }};
      for( tag in product ){
        if(product[tag] ==="custom-option"){
          contains_custom_option = true;
        }
      }
      for( tag in product ){
        if(product[tag] ==="native"){
          contains_native_option = true;
        }
      }

      try {
            const response = await fetch(`${api_details[0].url}/image_uploader/print_image`, {
              method: "POST",
              body: formData,
              headers: {
                ...headerForImage,
              },
            })
              .then((response) => {
                if (!response.ok) {
                  throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
              })
              .then((data) => {
                // const hw = "?x-oss-process=image/resize,h_250"; // Adjust width and height here
                // const imageUrlWithAspectRatio = data.imageUrl + hw;

                previewImg.src = data.imageUrl;

                hiddenInputSimple.value=data.imageUrl;

                const containerWidth = 250;
                const containerHeight = 250;
                const imageAspectRatio = previewImg.naturalWidth / previewImg.naturalHeight;

               let imageWidth, imageHeight;
                if (imageAspectRatio >= 1) {
                  // Landscape or square image
                  imageWidth = containerWidth;
                  imageHeight = containerWidth / imageAspectRatio;
                } else {
                  // Portrait image
                  imageHeight = containerHeight;
                  imageWidth = containerHeight * imageAspectRatio;
                }

                // Set image dimensions and quality
                previewImg.style.width = `${imageWidth}px`;
                previewImg.style.height = `${imageHeight}px`;
                previewImg.style.objectFit = "contain"; // Maintain aspect ratio and fill container

                const imageUrl = data.imageUrl;
                const parts = imageUrl.split('/');
                uploadedImageName = parts[parts?.length - 1];

                validateHiddenInputs();
                checkVariant();                
              })
              .catch((error) => {
                console.error("Error uploading new image:", error);
              });           
        } catch (error) {
            console.log('Error:', error);
        }

        document.querySelector(".close-button-simple").addEventListener("click",function(event){
          event.preventDefault();
          if (uploadedImageName) {
            imageContainer.innerHTML="";
            document.querySelector(".uploadOuter").style.display = "block";
            document.querySelector(".close-button-simple").style.display="none";
            document.getElementById("hiddenInputSimple").value="";
            document.querySelector(".image-uploader").style.padding="0px";
            const cartButton = document.getElementById("add-to-cart-button");
            cartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
            cartButton.setAttribute("disabled", "true");
            //  fetch(`${api_details[0].url}image_uploader/delete_image?image_name=${uploadedImageName}`, {
            //         headers: headerForImage,
            //         method: 'DELETE'
            //     })
            //     .then(response => {
            //         if (!response.ok) {
            //             throw new Error(`HTTP error! Status: ${response.status}`);
            //         }
            //         return response.json();
            //     })
            //     .then(data => {
                  
            //       console.log('Previous image deleted:', data);
            //     })
            //     .catch(error => {
            //         console.error('Error deleting previous image:', error);
            //     });
          }
        });
    }


</script>

<script>
      function changeBorderColor(name,button, className,key,shape) {
        var buttons = document.querySelectorAll(className);
        buttons.forEach(function(btn) {
          btn.style.border = '1px solid gray';
        });
        if(button.textContent!=="No"){
          document.querySelector(`.${name}HiddenInput`).value=button.textContent;
        }else{
          document.querySelector(`.${name}HiddenInput`).value="";
        }
        button.style.border = '2px solid #5461c8';
        validateHiddenInputs();
        handleSliderImage(button.textContent,key);
      }

      

      function selectBackground(name,value,key,showRelatedImages){
        let hiddenInput = document.querySelector(`.${name}HiddenInput`);
        let selectedSwatches = document.getElementById(`${name}-selected-swatches`);
        hiddenInput.value = value;
        selectedSwatches.textContent = value;
        validateHiddenInputs();
        handleSliderImage(value,key,showRelatedImages);
      }
      function getSelectedValue(name,selectElement,key,display,showRelatedImages) {
        if(customOptions){
          const findCombinedOption = customOptions.find(
            (item) => item.hasOwnProperty("combined_with") && item.heading == key
          );
          if(findCombinedOption){
            handleCombineConditionalSelector(findCombinedOption,selectElement);
          }
        }
        let selectedValue = selectElement.value;
        let validSelectedValue=selectedValue != 'not-selected' ? selectedValue : ''
        document.querySelector(`.${name}HiddenInput`).value = validSelectedValue;
        validateHiddenInputs();
        handleSliderImage(validSelectedValue,key,showRelatedImages);
        checkSubOptions();
      }

      function handleInputChange(name,event,fieldType) {
        const inputValue = event.target.value;
        document.querySelector(`.${name}HiddenInput`).value=inputValue;
        validateHiddenInputs();
      }


      function validateHiddenInputs(){
        let customOption={{product.metafields.cuddleclones.custom_options.value|json}};
        let nativeOption={{product.metafields.cuddleclones.native_options.value|json}};

        let allCustomFieldsFilled = true;
        let isImageUploaded = true;
        let subOption = true;
        let checkNativeInputs=true;
        let checkMultiImages=true;

        let productTags = {{ product.tags | json }};

        for (let tag of productTags) {
          if(tag==="native"){
            checkNativeInputs=areAllAttributesFilled(formattedNativeOptions);
            subOption = checkSubOptions();
          }
        }

        for (let tag of productTags) {
          if(tag==="multi-uploader" ){
            checkMultiImages=validateMultiImages();
          }
        }

        for (let tag of productTags) {
            if (tag === "uploader" && !productTags.includes("multi-uploader")) {
                let imageValue = document.getElementById("hiddenInputSimple").value;
                isImageUploaded = imageValue !== "";
                break;
            }
        }

        if (customOption) {
          let isAnyFieldEmpty = false; // Flag variable to track if any mandatory field is empty

          for (let i in customOption) {
              if (customOption.hasOwnProperty(i)) {
                  let hiddenInput = document.querySelector(`.${customOption[i].name}HiddenInput`);

                  if (hiddenInput) {
                      let inputValue = hiddenInput.value;

                      if (customOption[i].field === 'mandatory' && (inputValue.trim() === "not-selected" || inputValue.trim() === "") && customOption[i].display) {
                          isAnyFieldEmpty = true; // Set the flag to true if any mandatory field is empty
                      }
                  } else {
                      isAnyFieldEmpty = true; 
                  }
              }
          }
          allCustomFieldsFilled = !isAnyFieldEmpty; 
        }
        const cartButton = document.getElementById("add-to-cart-button");
        if (allCustomFieldsFilled  && isImageUploaded && subOption && checkNativeInputs && checkMultiImages) {
            cartButton.style.cssText = "cursor:pointer;opacity:1;pointer-events:auto";
            cartButton.removeAttribute("disabled");
            return true;
        } else {
            cartButton.style.cssText = "cursor:not-allowed;opacity:0.5;pointer-events:none";
            cartButton.setAttribute("disabled", "true");
            return false;
        }
      }
</script>
<script>
  const getOriginalKey=Date.now();
  const originalKeyInput=document.getElementById('originalkey');
  if(originalKeyInput){
    originalKeyInput.value = getOriginalKey;
    document.querySelectorAll('#uniqueKey')?.forEach(item=>{
      item.value=getOriginalKey;
    });
  }
</script>
{% if product.tags contains "uploader" or product.tags contains "custom-option" %}
  <script>
    const getCartButton=document.getElementById("add-to-cart-button");
    if(getCartButton){
      getCartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
      getCartButton.setAttribute("disabled", "true");
    }
  </script>
{% endif %}

{% if product.tags contains "native" %}
  <script>
    if(getCartButton){
      getCartButton.style.cssText='';
    }
  </script>
{% endif %}

<script>
  
  let nativeOption={{product.metafields.cuddleclones.native_options.value | json}};
  let formattedNativeOptions = {};

  if(nativeOption){
    nativeOption.forEach(option => {
      if(!option.conditional ){
        formattedNativeOptions[option.heading] = "";
      }
    });
  }

   let variants={{product.variants | json}};

  function handleNativeAction(name, value, element, type,shape, showRelatedImages) {
    let withHyphenName=name;
    if(withHyphenName.includes(' ')){
      withHyphenName=name.replace(/ /g,"_");
    }

    const hiddenInput = document.querySelector(`._option_${withHyphenName}HiddenInput`);

      if (type === "dropdown") {
        if (nativeOption) {
          const findOptions = nativeOption.find(
            (item) => item.conditional_subOptions && item.heading == name
          );
                    
          if (findOptions && !findOptions.hasOwnProperty("combined_with")) {
            let subOption = findOptions.options.find(
              (item) => item.name == value.value
            );

            if (subOption && subOption.sub_options) {
              const className = `.${findOptions.conditional_subOptions}`;
              const selectDiv = document.querySelector(className);

              if (selectDiv) {
                selectDiv.style.display = "flex";
              }
            } else {
              const className = `.${findOptions.conditional_subOptions}`;
              const selectDiv = document.querySelector(className);
              if (selectDiv) {
                selectDiv.style.display = "none";
              }
            }
          }else if(findOptions && findOptions.hasOwnProperty("combined_with")){
            handleCombineConditionalSelector(findOptions,value);
          }
          const findNumberOfOptions = nativeOption.find(
            (item) => item.conditional_NumberOptions && item.heading == name
          );
          if(findNumberOfOptions){
            const conditionalContainerName=findNumberOfOptions.conditional_NumberOptions;
            handleConditionalNumberOfOptions(findNumberOfOptions,value.selectedIndex,conditionalContainerName);
          }
          value.style.color = "black";
          formattedNativeOptions[name] = value.value;
          if (formattedGalleryNativeOptions.hasOwnProperty(name)) {
            formattedGalleryNativeOptions[name] = value.value;
          } 
          combinedValues=value.value;
          hiddenInput.value = value.value;
          handleNativeSliderImage(hiddenInput.value);
        }
       
      } else if (type === "button") {
        formattedNativeOptions[name] = value.textContent;
        if (formattedGalleryNativeOptions.hasOwnProperty(name)) {
          formattedGalleryNativeOptions[name] = value.textContent;
        }
        const buttons = document.querySelectorAll(`.${withHyphenName}${shape}Button`);
        buttons.forEach(function (btn) {
          btn.style.border = "1px solid gray";
        });
        hiddenInput.value = value.textContent;
        value.style.border = "2px solid #5461c8";
        validateHiddenInputs();
        checkVariant();
        handleNativeSliderImage(hiddenInput.value, showRelatedImages);
      } else if (type === "swatches") {
        formattedNativeOptions[name] = value;
        if (formattedGalleryNativeOptions.hasOwnProperty(name)) {
          formattedGalleryNativeOptions[name] = value;
        } 
        combinedValues[name]=value;
          hiddenInput.value = value;
        const swatchesElement = document.getElementById(
          `${name}-selected-swatches`
        );
        if (swatchesElement) {
          swatchesElement.textContent = value;
        }
        handleNativeSliderImage(value, showRelatedImages);
      }
      checkVariant();
  }

  function handleCombineConditionalSelector(primaryOptions,value){
    const combinedOptionName=primaryOptions.combined_with;

    let subOption = primaryOptions.options.find(
      (item) => item.name == value.value
    );

    const combinedOption=document.querySelector(`.${combinedOptionName}Option`);
    const selectedOption = combinedOption.options[combinedOption.selectedIndex];

    let combinedOptionDisplay = selectedOption.getAttribute('data-sub-options');
    if(combinedOptionDisplay==="true"){
      combinedOptionDisplay=true;
    }else{
      combinedOptionDisplay=false;
    }
    if (subOption){
      if(subOption.sub_options && combinedOptionDisplay){
        const className = `.${primaryOptions.conditional_subOptions}`;
        const selectDiv = document.querySelector(className);
        
        if (selectDiv) {
          selectDiv.style.display = "flex";
        }
      }else {
        const className = `.${primaryOptions.conditional_subOptions}`;
        const selectDiv = document.querySelector(className);
        if (selectDiv) {
          selectDiv.style.display = "none";
        }
      }
    } 
  }

  function checkVariant() {
    let variantIdInput = document.getElementById("variantid");
    let concatenatedValue = Object.values(formattedNativeOptions).join(' / ');
    let imageUploaded=true;
    let selectSubOption=true;
    let checkNativeInputs=true;
    let checkMultiImage=true;
    
    imageUploaded = checkImageUploaded();
    selectSubOption = checkSubOptions();
    checkNativeInputs = areAllAttributesFilled(formattedNativeOptions);
    let productTags = {{ product.tags | json }};
   
    variants.forEach(function(variant) {
      if (variant.title === concatenatedValue) {
        var priceDiv=document.querySelector(".originalPrice");
        let price;
        let formattedPrice;
        if (priceDiv) {
          price = variant.price / 100; 
          formattedPrice = `$${price.toFixed(2)}`; 
          priceDiv.textContent = formattedPrice ;
        }
        
        const compareAtPriceDiv=document.querySelector(".compare-at-price");
        let compareAtPrice;
        let formattedComparePrice;
        if(compareAtPriceDiv){
          compareAtPrice = variant.compare_at_price / 100;
          if(compareAtPrice>0){
            formattedComparePrice = `$${compareAtPrice.toFixed(2)}`; 
            compareAtPriceDiv.textContent=' '+formattedComparePrice;
          }
        }

        var pricePercentageContainer=document.querySelector(".pricePercentage");
        pricePercentageContainer.textContent="";
        if(pricePercentageContainer && compareAtPrice>0){
          const content=document.createElement("p");
          var amountSaved = compareAtPrice - price;
          var discountPercentage = Math.floor((amountSaved / compareAtPrice) * 100);
          content.textContent=`You save: ${discountPercentage}% ($${amountSaved.toFixed(2)})`;
          pricePercentageContainer.appendChild(content);
        }

        variantIdInput.value = variant.id;
        for (let tag of productTags) {
          if(tag==="multi-uploader"){
            checkMultiImage=validateMultiImages();
          }
        }
        let button = document.getElementById("add-to-cart-button");
        if (imageUploaded && selectSubOption && checkNativeInputs && checkMultiImage) {
          if (button) {
            button.removeAttribute("disabled");
            validateHiddenInputs();
          }
        }else{
          button.style.cssText = "cursor:not-allowed;opacity:0.5;pointer-events:none";
          button.setAttribute("disabled", "true");
        }
      }
    });
  }


  function areAllAttributesFilled(obj) {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (!obj[key] && obj[key] !== 0) {
          return false;
        }
      }
    }
    return true; 
  }

  function checkImageUploaded() {
    let productTags = {{ product.tags | json }};
    for (let tag of productTags) {
        if (tag === "uploader" && !productTags.includes("multi-uploader")) {
            let imageValue = document.getElementById("hiddenInputSimple").value;
            return imageValue !== "";
        }
    }
    return true
  }

  function checkSubOptions() {
    if (nativeOption) {
      const foundConditionalObject = nativeOption.find(item => item.hasOwnProperty('conditional') && item.conditional);
      const elementWithSubOption = nativeOption.find(item => item.hasOwnProperty('conditional_subOptions') && item.conditional_subOptions);
      
      if(foundConditionalObject && elementWithSubOption){
        if (elementWithSubOption) {
          const className = `.${elementWithSubOption.conditional_subOptions}`;
          
          if (className) {
            const selectDiv = document.querySelector(className);
            
            if (selectDiv) {
              const selectElement = selectDiv.querySelector('span select');
              const subOptionsHiddenInput = document.querySelector(`${className}HiddenInput`);
              
              if (selectElement && subOptionsHiddenInput) {
                const displayPropertyValue = window.getComputedStyle(selectDiv).getPropertyValue('display');
                const subOptionsHiddenInputValue = subOptionsHiddenInput.value;
                let formattedString = elementWithSubOption.conditional_subOptions.replace(/_/g, " ");
                if (displayPropertyValue === 'none') {
                  subOptionsHiddenInput.value = '';
                  if (formattedGalleryNativeOptions.hasOwnProperty(formattedString)) {
                    formattedGalleryNativeOptions[formattedString]="";
                    combinedValues[formattedString]="";
                  }
                  selectElement.selectedIndex=0;
                  handleNativeSliderImage(subOptionsHiddenInput.value);
                  return true;
                } else if (displayPropertyValue === 'flex' && subOptionsHiddenInputValue !== '') {
                  if(formattedGalleryNativeOptions.hasOwnProperty(formattedString)){
                    formattedGalleryNativeOptions[formattedString]=subOptionsHiddenInput.value;
                    combinedValues[formattedString]=subOptionsHiddenInput.value;
                  }
                  handleNativeSliderImage(subOptionsHiddenInput.value);
                  return true;
                } else {
                  subOptionsHiddenInput.value = '';
                  if (formattedGalleryNativeOptions.hasOwnProperty(formattedString)) {
                    formattedGalleryNativeOptions[formattedString]="";
                    combinedValues[formattedString]="";
                  }
                  handleNativeSliderImage(subOptionsHiddenInput.value);
                  return false;
                }
              }
            }
          }
        }
      }else{
        return true;
      }
    }
  }

  function handleSuboptionsValue(selectElement, optionName) {
    const selectedValue = selectElement.value;
    selectElement.style.color="black";
    document.querySelector(`.${optionName}HiddenInput`).value=selectedValue;
    checkVariant();
    validateHiddenInputs();
  }

  sessionStorage.setItem("lastOptionSelected",0);
  let multiUploaderContainer=document.querySelector(".multi-image-uploader");
  let multiUploaderSelect=document.querySelector(".multiUploader");
  if(multiUploaderSelect){
    multiUploaderSelect.addEventListener("change",function(event){
      let numberOfElements=multiUploaderSelect.selectedIndex;
      let multiUploaderContainer=document.querySelector(".multi-image-uploader");
      let lastSelectedOption=parseInt(sessionStorage.getItem('lastOptionSelected'));

      if(numberOfElements>lastSelectedOption){
        let index;
        if(lastSelectedOption==0){
          index=1;
          if(multiUploaderContainer){
            const multiUploaderHeading=document.createElement("h3");
            multiUploaderHeading.textContent=textFields.textField_heading;
            multiUploaderContainer.appendChild(multiUploaderHeading);
          }

        }else{
          index=lastSelectedOption+1;
        }
        for (index; index <= numberOfElements; index++) {
            createMultiUploaders(index);
        }
        handleUploaderLabels();
      }else if(numberOfElements<lastSelectedOption){
        var imageSelector = document.querySelectorAll('.uploaderSection');
        imageSelector.forEach(function (input, arrayIndex) {
          if (arrayIndex >= numberOfElements && arrayIndex < lastSelectedOption) {
              input.remove();
          }
        });
        handleUploaderLabels();
      }
      sessionStorage.setItem('lastOptionSelected', numberOfElements);

      validateHiddenInputs();
      checkVariant();
    })
  }

  function handleUploaderLabels(){
    let allUploaders=document.querySelectorAll(".uploaderSection");
    if(allUploaders.length==1){
      let firstUploaderLabel=document.querySelector(".uploaderSection_1 label");
      if(firstUploaderLabel){
        firstUploaderLabel.style.display="none";
      }
    }else if(allUploaders.length>1){
      let firstUploaderLabel=document.querySelector(".uploaderSection_1 label");
      if(firstUploaderLabel){
        document.querySelector(".uploaderSection_1 label").style.display="block";
      }
    }
  }

  function getOrdinalSuffix(number) {
    if (number % 100 >= 11 && number % 100 <= 13) {
        return "th";
    }
    switch (number % 10) {
        case 1:
            return "st";
        case 2:
            return "nd";
        case 3:
            return "rd";
        default:
            return "th";
    }
  }
  let textFields;
  let limit;
   if(nativeOption){
     textFields=nativeOption.find(item => item.textField_display === true || item.textField_display === false);
     limit = nativeOption.find(item => item.limit );
    }
    let limitValue = limit?.limit;
  
  function createMultiUploaders(i){
    const uploaderSection=document.createElement("div");
    uploaderSection.classList.add(`uploaderSection_${i}`,"uploaderSection");

    let ordinalSuffix = getOrdinalSuffix(i);
    const imageUploaderDiv=document.createElement("div");
    imageUploaderDiv.classList.add("image-uploader");
    imageUploaderDiv.setAttribute("id","image-uploader-"+i);

    let closeButton=document.createElement("button");
    closeButton.classList.add("close-button-"+i,"close-button-simple");
    closeButton.textContent="✖";

      let uploaderLabel=document.createElement("label");
      uploaderLabel.style.fontWeight = "600";
      uploaderLabel.textContent = `Upload Your ${i}${ordinalSuffix} Pet Photo:`;
    

    let selectedImageContainer=document.createElement("div");
    selectedImageContainer.classList.add("selected-image-"+i,"selected-image-container-simple");

    let hiddenInput = document.createElement("input");
    hiddenInput.setAttribute("type", "hidden");
    hiddenInput.setAttribute("name", `properties[_image_url_${i}]`);
    hiddenInput.setAttribute("value","");
    hiddenInput.setAttribute("id", `hiddenInputSimple-${i} hiddenInputImage_${i}`);
    hiddenInput.setAttribute("class", `hiddenInputSimple-${i}`);

    let uploadOuterDiv=document.createElement("div");
    uploadOuterDiv.classList.add("uploadOuter_"+i,"uploadOuter");

    let dragBoxSpan=document.createElement("span");
    dragBoxSpan.classList.add("dragBoxUnique");

    let dragBoxText=document.createElement("span");
    dragBoxText.classList.add("dragBoxText");
    dragBoxText.textContent="Drag + Drop";

    let fileInput = document.createElement("input");
    fileInput.classList.add(`image_uploader_${i}`, `fileInput`);
    fileInput.setAttribute("onclick", "this.value=null");
    fileInput.setAttribute("type", "file");
    fileInput.setAttribute("id", "uploadFile-" + i);
    fileInput.setAttribute("onChange", `multiDragNdrop(event,'image-uploader-${i}',${i})`);
    fileInput.setAttribute("ondragover", "drag(event)");
    fileInput.setAttribute("ondrop", "drop(event)");

    let orTextSpan=document.createElement("span");
    orTextSpan.classList.add("orText");
      
    let orTextContent=document.createElement("strong");
    orTextContent.textContent="or";

    let uploadFileLabel=document.createElement("label");
    uploadFileLabel.classList.add("btn", "btn-primary");
    uploadFileLabel.setAttribute("for","uploadFile");

    let selectFileLogo=document.createElement("img");
    selectFileLogo.style.cssText="height:19px;width:25px;";
    selectFileLogo.src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/7500a60f83aabde22eb73dc843c72079.png?v=1706695966";
    selectFileLogo.setAttribute("alt","Select File Logo");

    orTextSpan.appendChild(orTextContent);

    uploadFileLabel.appendChild(selectFileLogo);
    uploadFileLabel.textContent="Select File";

    dragBoxSpan.appendChild(dragBoxText);
    dragBoxSpan.appendChild(fileInput);
    dragBoxSpan.appendChild(orTextSpan);
    dragBoxSpan.appendChild(uploadFileLabel);

    uploadOuterDiv.appendChild(dragBoxSpan);
     

    imageUploaderDiv.appendChild(closeButton);
    imageUploaderDiv.appendChild(hiddenInput);
    imageUploaderDiv.appendChild(selectedImageContainer);
    imageUploaderDiv.appendChild(uploadOuterDiv);
      
    imageUploaderDiv.style.cssText="margin:10px 0px;";

    
      uploaderSection.appendChild(uploaderLabel);
    
    uploaderSection.appendChild(imageUploaderDiv);

    if(textFields.textField_display){
      if(textFields.textField_text.length){
          let textFieldContainer=document.createElement("div");
          textFieldContainer.setAttribute("class","pajamas-pdp-line-item-size-wrapper");
          textFieldContainer.style.cssText="margin-bottom:5px";

          let textFeildLabel = document.createElement("label");
          textFeildLabel.style.fontWeight = "600";
          textFeildLabel.textContent = textFields.textField_text[i-1].text;

          textFieldContainer.appendChild(textFeildLabel);

          const textFieldInputContainer=document.createElement("div");
          textFieldInputContainer.setAttribute("class","pajamas-pdp-line-item-size-wrapper");
          textFieldInputContainer.style.marginBottom="1rem";

          let textInput = document.createElement("input");
          textInput.setAttribute("class",`multiTextField_${i} input`);
          textInput.setAttribute("type", "text");
          textInput.setAttribute("name", `properties[${textFields.textField_text[i-1].name}]`);
          textInput.setAttribute("id", `custom-text-field custom-text-field-${i} custom_option_${i}`);
          textInput.value="";
          textInput.setAttribute("maxlength", limitValue);
          textInput.setAttribute("placeholder","Type Your Pets Name Here");
          textInput.style.cssText="border:1px solid;border-radius:10px;";

          textFieldInputContainer.appendChild(textInput);

          uploaderSection.appendChild(textFieldContainer);
          uploaderSection.appendChild(textFieldInputContainer);
      }
    }
    multiUploaderContainer.appendChild(uploaderSection);
  }

  async function multiDragNdrop(event, containerId, number){
    let hiddenInput = document.querySelector(`.hiddenInputSimple-${number}`);
      var files = event.target.files;

      if (files.length > 1) {
        alert("Please select only one image file.");
        return;
      }

      var file = files[0];
      var fileName = URL.createObjectURL(files[0]);

      if (!file.type.startsWith("image/")) {
        alert("Please select only image files.");
        return;
      }

      if (file.type !== "image/jpg" && file.type !== "image/png" && file.type !== "image/bmp" && file.type !== "image/jpeg") {
        alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
        return;
      }

      if (files[0].size > 15 * 1024 * 1024) {
        // Convert MB to bytes
        alert("Please select images smaller than 15MB.");
        return;
      }

      let uploadedImageName;
      var previewImg = document.createElement("img");
      previewImg.src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif";


      let imageContainer=document.querySelector(`.selected-image-${number}`);
      document.querySelector(`.close-button-${number}`).style.display="block";
      document.querySelector(`#image-uploader-${number}`).style.padding="20px";

      previewImg.setAttribute("alt", "Image ");
      previewImg.setAttribute("id", "previewImageSimple");
      imageContainer.appendChild(previewImg);

      document.querySelector(".uploadOuter_"+number).style.display = "none"; 

      const formData = new FormData();
      formData.append('image', files[0]); 
      let contains_custom_option = false;
      let contains_native_option = false;
      let product={{product.tags | json }};
      for( tag in product ){
        if(product[tag] ==="custom-option"){
          contains_custom_option = true;
        }
      }
      for( tag in product ){
        if(product[tag] ==="native"){
          contains_native_option = true;
        }
      }

      try {
            const response = await fetch(`${api_details[0].url}/image_uploader/print_image`, {
              method: "POST",
              body: formData,
              headers: {
                ...headerForImage,
              },
            })
              .then((response) => {
                if (!response.ok) {
                  throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
              })
              .then((data) => {
                previewImg.src=data.imageUrl;
                hiddenInput.value = data.imageUrl;

                const containerWidth = 250;
                const containerHeight = 250;
                const imageAspectRatio = previewImg.naturalWidth / previewImg.naturalHeight;

               let imageWidth, imageHeight;
                if (imageAspectRatio >= 1) {
                  // Landscape or square image
                  imageWidth = containerWidth;
                  imageHeight = containerWidth / imageAspectRatio;
                } else {
                  // Portrait image
                  imageHeight = containerHeight;
                  imageWidth = containerHeight * imageAspectRatio;
                }

                // Set image dimensions and quality
                previewImg.style.width = `${imageWidth}px`;
                previewImg.style.height = `${imageHeight}px`;
                previewImg.style.objectFit = "contain"; // Maintain aspect ratio and fill container

                const imageUrl = data.imageUrl;
                const parts = imageUrl.split('/');
                uploadedImageName = parts[parts?.length - 1];
                validateMultiImages();
                validateHiddenInputs();
                checkVariant();
              })
              .catch((error) => {
                console.error("Error uploading new image:", error);
              });           
        } catch (error) {
            console.log('Error:', error);
        }

        document.querySelector(`.close-button-${number}`).addEventListener("click",function(event){
          event.preventDefault();
          if (uploadedImageName) {
            imageContainer.innerHTML="";
            document.querySelector(".uploadOuter_"+number).style.display = "block";
            document.querySelector(`.close-button-${number}`).style.display="none";
            document.querySelector(".hiddenInputSimple-"+number).value="";
            document.getElementById(`image-uploader-${number}`).style.padding="0px";
            const cartButton = document.getElementById("add-to-cart-button");
            cartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
            cartButton.setAttribute("disabled", "true");

            validateMultiImages();
            //  fetch(`${api_details[0].url}image_uploader/delete_image?image_name=${uploadedImageName}`, {
            //         headers: headerForImage,
            //         method: 'DELETE'
            //     })
            //     .then(response => {
            //         if (!response.ok) {
            //             throw new Error(`HTTP error! Status: ${response.status}`);
            //         }
            //         return response.json();
            //     })
            //     .then(data => {
                  
            //       console.log('Previous image deleted:', data);
            //     })
            //     .catch(error => {
            //         console.error('Error deleting previous image:', error);
            //     });
            }
        });
    }

    function validateMultiImages(){
      const imagesContainer=document.querySelector(".multi-image-uploader");
      let isAnyInputEmpty = false;
      if(imagesContainer){
        const hiddenInputs = imagesContainer.querySelectorAll("input[type='hidden']");
        hiddenInputs.forEach(input => {
          if (input.value.trim() === "") {
              isAnyInputEmpty = true;
              return; 
          }
        });
        if (isAnyInputEmpty) {
          return false;
        } else {
          return true;
        }
      }
    }

    function handleConditionalNumberOfOptions(option,number,name){
      document.querySelector(`._option_${name}HiddenInput`).value="";
      formattedNativeOptions[name]="";
      const selectorWrapper=document.querySelector(".defaultNumberOfOptions");
      selectorWrapperSpan=selectorWrapper.querySelector('.optionsContainer');
      selectorWrapperSpan.innerHTML="";

      const findOptionsList = nativeOption.find(
        (item) =>  item.heading == name
      );
      let optionShape=findOptionsList.shape;
      let optionShapeName = optionShape.charAt(0).toUpperCase() + optionShape.slice(1);
      let loopTerminateNumber=option.options.length;

      for (let i = number-1; i < loopTerminateNumber; i++) {
        const value = findOptionsList.options[i];
        const button = document.createElement('button');
        button.type = 'button';
        button.className = `${optionShape}-button ${name}${optionShapeName}Button`;
        button.innerText = value.name;
        button.setAttribute('onclick', `handleNativeAction('${name}', this, '.${optionShape}-button', 'button','${optionShapeName}')`);
        selectorWrapperSpan.appendChild(button);
      }
    }
</script>
<script>
   let customOptions={{product.metafields.cuddleclones.custom_options.value | json}};
  let formattedCustomOptions = {};

  if(customOptions){
    customOptions.forEach(option => {
      const typeCheck = option.type !== "text_field";
      const imageChangeCheck = !('gallery_image_change' in option) || option.gallery_image_change;

      if (typeCheck && imageChangeCheck) {
        formattedCustomOptions[option.heading] = "";
      }
    });
  }
  let nativeOptionGallery={{product.metafields.cuddleclones.native_options.value | json}};
  let formattedGalleryNativeOptions = {};

  if(nativeOptionGallery){
    let conditionalCheck=false;
    nativeOptionGallery.forEach(option => {
      const conditionalCheck = !option.hasOwnProperty('conditional') || option.conditional;

      const imageChangeCheck = !option.hasOwnProperty('gallery_image_change') || option.gallery_image_change;

      if (imageChangeCheck) {
        formattedGalleryNativeOptions[option.heading] = ""; 
      }
    });
  }

  let combinedValues;
  let concatenatedCombinedValues = '';
  combinedValues={...formattedCustomOptions,...formattedGalleryNativeOptions};


function handleSliderImage(value,key,showRelatedImages = false){
    combinedValues={...formattedCustomOptions,...formattedGalleryNativeOptions};
    if(formattedCustomOptions.hasOwnProperty(key)){
      formattedCustomOptions[key] = value;
      combinedValues[key] = value;
    }
    let concatenatedCustomValue = '';
    if (Object.keys(formattedCustomOptions).length === 1) {
      concatenatedCustomValue = Object.values(formattedCustomOptions)[0].replace(/\s/g, '');
    } else {
      concatenatedCustomValue = Object.values(formattedCustomOptions).reduce((acc, value) => acc.concat(value.replace(/\s/g, '')), '');
    }
    if (Object.keys(combinedValues).length === 1) {
      concatenatedCombinedValues = Object.values(combinedValues)[0].replace(/\s/g, '');
    } else {
      concatenatedCombinedValues = Object.values(combinedValues).reduce((acc, value) => acc.concat(value.replace(/\s/g, '')), '');
    }
    const thumbnailImages=document.querySelectorAll(".product-gallery__thumbnail");
    const swatchSelectedValue = value.replace(/\s+/g, "");
    //Filtered Images Functionality
       const filteredImages = [...thumbnailImages].filter(image => {
      const imageTitle = $(image).attr('data-title');
      return imageTitle && imageTitle.includes(swatchSelectedValue);
    });
    if (showRelatedImages && filteredImages.length > 0) {
      thumbnailImages.forEach(image => {
        image.style.display = "none";
      });
      filteredImages.forEach(image => {
        image.style.display = "block";
      });
      let isVariantComplete = areAllAttributesFilled(combinedValues);
      const $slider = $('.product-gallery__thumbnails');
      if ($slider.data('flickity')) {
          $slider.flickity('resize');
       }
        if (!isVariantComplete) {
            $(thumbnailImages).removeClass('is-nav-selected');
            $(filteredImages[0]).addClass('is-nav-selected');
            $(filteredImages[0]).trigger('click');
        }
       else{
          if (thumbnailImages) {
             thumbnailImages.forEach(image => {
                const imageTitle = $(image).attr('data-title');
                if (imageTitle === concatenatedCombinedValues) {
                $(thumbnailImages).removeClass('is-nav-selected');
                $(image).addClass('is-nav-selected');
                $(image).trigger('click');
                }
             });
            }
        }
    }
  }
    
 function handleNativeSliderImage(value , showRelatedImages = false){
    combinedValues={...formattedCustomOptions,...formattedGalleryNativeOptions};
    let concatenatedNativeValue="";
    if (Object.keys(formattedGalleryNativeOptions).length === 1) {
      concatenatedNativeValue = Object.values(formattedGalleryNativeOptions)[0].replace(/\s/g, '');
    } else {
      concatenatedNativeValue = Object.values(formattedGalleryNativeOptions).reduce((acc, value) => acc.concat(value.replace(/\s/g, '')), '');
    }
    if (Object.keys(combinedValues).length === 1) {
      concatenatedCombinedValues = Object.values(combinedValues)[0].replace(/\s/g, '');
    } else {
      concatenatedCombinedValues = Object.values(combinedValues).reduce((acc, value) => acc.concat(value.replace(/\s/g, '')), '');
    }
    const thumbnailImages=document.querySelectorAll(".product-gallery__thumbnail");
    const swatchValue = value.replace(/\s+/g, "");
    //Filtered Images Functionality
    const filteredImages = [...thumbnailImages].filter(image => {
      const imageTitle = $(image).attr('data-title');
      return imageTitle && imageTitle.includes(swatchValue);
    });
    if (showRelatedImages && filteredImages.length > 0) {
      thumbnailImages.forEach(image => {
        image.style.display = "none";
      });
      filteredImages.forEach(image => {
        image.style.display = "block";
      });
      const $slider = $('.product-gallery__thumbnails');
      if ($slider.data('flickity')) {
          $slider.flickity('resize');
        }
      $(thumbnailImages).removeClass('is-nav-selected');
      $(filteredImages[0]).addClass('is-nav-selected');
      $(filteredImages[0]).trigger('click');
    }
    if(thumbnailImages){
      thumbnailImages.forEach(image => {
        const imageTitle = $(image).attr('data-title');
        if (imageTitle === concatenatedCombinedValues) {
          $(thumbnailImages).removeClass('is-nav-selected');
          $(image).addClass('is-nav-selected');
          $(image).trigger('click');
        }
      });
    }
  }

  function toggleHiddenValue(checkbox, index) {
    const hiddenInput = document.getElementById(`extra_option_${index}`);
    hiddenInput.value = checkbox.checked ? "Yes" : "No";
  }

  function checkVariantData() {
    return Object.keys(formattedCustomOptions).length > 0;
  }

  //KeyBoard Event

  function selectSwatch_PDP(event){
    const swatchPosition = Array.from(document.querySelectorAll('#keydown_Swatch img'));
    const currentIndex = swatchPosition.indexOf(event.currentTarget);

    if (event.key === "Enter") {
      event.preventDefault();
     swatchPosition[currentIndex].click(); 
    }
  }
</script>
