{% comment %} Is the color set to transparent? {% endcomment %}
{%- assign mobile_menu_background_color = settings.mobile_menu_background_color | color_extract: 'alpha' -%}
{%- assign mobile_menu_link_color = settings.mobile_menu_link_color | color_extract: 'alpha' -%}
{%- assign mobile_menu_link_hover_color = settings.mobile_menu_link_hover_color | color_extract: 'alpha' -%}

{% comment %} Opacity level {% endcomment %}
{%- assign mobile_menu_opacity = settings.mobile_menu_opacity | divided_by: 100.00 -%}

{% comment %} Mobile specific CSS {% endcomment %}
{%- style -%}
  .mobile-menu {
    background-color: {% if mobile_menu_background_color != 0.0 %}{{ settings.mobile_menu_background_color | color_modify: 'alpha', mobile_menu_opacity }}{% else %}{{ settings.header_background | color_modify: 'alpha', mobile_menu_opacity }}{% endif %};
  }

  {%- if mobile_menu_link_color != 0.0 -%}
    .mobile-menu-link,
    .mobile-menu__item,
    .mobile-menu a {
      color: {{ settings.mobile_menu_link_color }};
    }
  {%- endif -%}

  {%- if mobile_menu_link_hover_color != 0.0 -%}
    .mobile-menu-link:hover,
    .mobile-menu__item:hover,
    .mobile-menu a:hover {
      color: {{ settings.mobile_menu_link_hover_color }};
    }
  {%- endif -%}
{%- endstyle -%}

{% comment %} HTML markup {% endcomment %}
<div id="mobile-header" class="mobile-header">
  <div data-show-mobile-menu="false" class="mobile-menu__toggle-button mobile-toggle__position--{{ settings.logo_menu_position }}">
  </div>
  <div class="mobile-header__content
              mobile-layout--{{ settings.logo_menu_position }}
              mobile-logo__position--{{ settings.logo_mobile_position }}
              header__icon-style-{{ settings.mobile_icon_style | replace: '_', '-' }}">
    <div class="mobile-dropdown mobile-dropdown__position--{{ settings.logo_menu_position }}">
      <div class="mobile-menu__toggle-icon">
        <div class="mobile-header__open-menu">
          {% render 'icon', name: 'menu' %}
        </div>
        <div class="mobile-header__close-menu">
          {% render 'icon', name: 'x' %}
        </div>
      </div>
      {% render 'mobile-menu', nav_blocks: nav_blocks %}
    </div>
    <div class="mobile-logo">
      {% if settings.logo_mobile != blank %}
        <a class="mobile-header__logo header__link primary-logo" href="{{ routes.root_url }}" title="{{ shop.name }}">
          {%
            render 'image-element',
            image: settings.logo_mobile,
            alt: settings.logo_mobile.alt,
            additional_classes: 'primary-logo',
            focal_point: settings.logo_mobile.presentation.focal_point,
          %}
        </a>
      {% elsif section.settings.logo != blank %}
        <a class="mobile-header__logo header__link primary-logo" href="{{ routes.root_url }}" title="{{ shop.name }}">
          {%
            render 'image-element',
            image: section.settings.logo,
            alt: section.settings.logo.alt,
            additional_classes: 'primary-logo',
            focal_point: section.settings.logo.presentation.focal_point,
          %}
        </a>
      {% else %}
        <a href="{{ routes.root_url }}" class="header__link header__logo-text primary-brand">
          <span>{{ shop.name }}</span>
        </a>
      {% endif %}
    </div>
    <div class="mobile-icons {% if settings.show_search_icon_mobile == false %}has-one-icon{% endif %}">
      {% if settings.show_search_icon_mobile %}
        {% comment %} Search icon {% endcomment %}
        <a class="header__link action-area__link" data-show-search-trigger>
          {% render 'icon',
                  name: 'search',
                  icon_class: 'header__icon'
          %}
          <span class="icon-caption">{{ 'general.search.title' | t | escape }}</span>
        </a>
      {% endif %}

      {% comment %} Cart icon {% endcomment %}
      <div class="header-cart action-area__link {% if cart.item_count != 0 %}has-cart-count{% endif %}" data-ajax-cart-trigger>
        <a class="header__link" {% if settings.cart_action != 'mini_cart' or settings.cart_action != 'drawer' %}href="{{ routes.cart_url }}"{% endif %} >
          <span class="header-cart__icon">
            {% render 'icon',
                    name: settings.cart_icon,
                    icon_class: 'header__icon'
            %}
            <span class="header-cart__count header-cart__count--badge badge" data-bind="itemCount">
              {{ cart.item_count }}
            </span>
          </span>
          <span class="header-cart__caption icon-caption">
            {{ 'layout.general.cart' | t | escape }}
            {%- if object.settings.icon_style == 'text' -%}
              <span class="header-cart__count cart__count--text" data-bind="itemCount">{{ cart.item_count }}</span>
            {%- endif -%}
          </span>
        </a>
        {% if settings.cart_action == 'mini_cart' or settings.cart_action == 'drawer' %}
          {% render 'ajax-cart', context: 'mobile-header' %}
        {% endif %}
      </div>

    </div>
  </div>

  {% if settings.logo_mobile_position == 'below' %}
    <div class="mobile-logo__outer">
      {% if settings.logo_mobile != blank %}
        <a class="mobile-header__logo header__link primary-logo" href="{{ routes.root_url }}" title="{{ shop.name }}">
          {%
            render 'image-element',
            image: settings.logo_mobile,
            alt: settings.logo_mobile.alt,
            additional_classes: 'primary-logo',
            focal_point: settings.logo_mobile.presentation.focal_point,
          %}
        </a>
      {% else %}
        <a href="{{ routes.root_url }}" class="header__link header__logo-text primary-brand">
          {{ shop.name }}
        </a>
      {% endif %}
    </div>
  {% endif %}

</div>
