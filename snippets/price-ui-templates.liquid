{% comment %}
  @param template {String}
  A string representing the template to render

  @param template (price)
  The template for all prices in the Price UI
  Innermost element must contain the data attribute 'price'

  @param value {String}
  A money value, empty if used as a template

  @param template (price-percent)
  The template for all prices displayed as a percentage of savings
  Innermost element must contain the data attribute 'price-percent'

  @param value {String}
  The percentage value saved, empty if used as a template

  @param template (price-min)
  The template for price minimums, used in price ranges
  Innermost element must contain the data attribute 'price-min'

  @param value {String}
  A money value, empty if used as a template

  @param template (price-max)
  The template for price maximums, used in price ranges
  Innermost element must contain the data attribute 'price-max'

  @param value {String}
  A money value, empty if used as a template

  @param template (unit-quantity)
  The template for unit quantities, used in unit pricing
  Innermost element must contain the data attribute 'unit-quantity'

  @param value {String}
  A string, empty if used as a template

  @param template (unit-price)
  The template for unit prices, used in unit pricing
  Innermost element must contain the data attribute 'unit-price'

  @param value {String}
  A money value, empty if used as a template

  @param template (unit-measurement)
  The template for unit measurements, used in unit pricing
  Innermost element must contain the data attribute 'unit-measurement'

  @param value {String}
  A string, empty if used as a template

  @param template (price-ui)
  The template for the Price UI.
  Must contain the data attributes 'compare-at-price', 'price', and 'unit-pricing'

  @param compare_at_price {Object}
  Can be a price object or price-range object,
  empty if used as a template or false if not included

  @param price {Object}
  Can be a price object or price-range object,
  empty if used as a template

  @param unit_pricing {Object}
  Must be a unit-pricing object,
  empty if used as a template or false if not included

  @param template (price-ui-badge)
  The template for the Price UI Badge.
  Must contain the data attribute 'badge'

  @param badge {String}
  A string containing product information,
  empty if used as a template
{% endcomment %}


{%- capture output -%}
  {%- capture _price -%}
    <span class="money" data-price>
      {%- if value != blank -%}
        {%- if settings.currency_format == 'money_with_currency_format' -%}
          {{- value | money_with_currency -}}
        {%- else -%}
          {{- value | money -}}
        {%- endif -%}
      {%- endif -%}
    </span>
  {%- endcapture -%}

  {%- if template == 'price' -%}
    {{- _price -}}
  {%- elsif template == 'price-percent' -%}
    <span data-price-percent>{%- if value != blank -%}{{- value -}}{%- endif -%}</span>
  {%- elsif template == 'price-min' -%}
    <span class="price-min" data-price-min>{{ _price }}</span>
  {%- elsif template == 'price-max' -%}
    <span class="price-max" data-price-max>{{ _price }}</span>
  {%- elsif template == 'unit-quantity' -%}
    <span class="unit-quantity" data-unit-quantity>{%- if value != blank -%}{{- value -}}{%- endif -%}</span>
  {%- elsif template == 'unit-price' -%}
    <span class="unit-price" data-unit-price>{{ _price }}</span>
  {%- elsif template == 'unit-measurement' -%}
    <span class="unit-measurement" data-unit-measurement>{%- if value != blank -%}{{- value -}}{%- endif -%}</span>
  {%- elsif template == 'price-ui' -%}
    <div class="sizeChartBox">
      
      <div class="pricesContainer">
        <span class="originalPrice price {% if compare_at_price != blank and compare_at_price != false -%}price--sale{%- endif -%}" data-price>{%- if price != blank -%}{{- price -}}{%- endif -%}</span>
        {%- if compare_at_price != false -%}<span class="compare-at-price" data-compare-at-price>{%- if compare_at_price != blank -%}{{- compare_at_price -}}{%- endif -%}</span>
      </div>
      {% if product.tags contains "meta-size-chart-size-chart" %}
        <div>
          <h4 class="open-new-pajamas-size-chart-header">
            <a class="sizeChartLinkText" href="javascript:;" style="color: #5461c8;" onMouseOver="this.style.color='#47d7ac'" onMouseOut="this.style.color='#5461c8'" onclick="handleSizeChartPopup()">View Size Chart
              <span class="icon sizeChartIcon"  data-icon="right-caret">  
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g id="right-caret"><path d="M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z"/></g></svg>
              </span>
            </a>
          </h4>
          <div style="display: none;" class="popup-chart-container" id="pajamasSizeChartOpen">
            <div class="modal-overlay"></div>
            <div class="modal-content">
              <h5 class="pajamas-new-size-chart-open-header" >Size Chart</h5>
              <div class="new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer">
                <img alt="{{ image.alt }}" class="pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1" loading="lazy" src="{{ product.metafields.custom.size_chart | img_url: 'master' }}"/>             
              </div>
              <div class="delete-button">
                <span  onclick="closePopup()">x</button>
              </div>
            </div>
          </div>
        </div>
      {%endif%}  
   </div>
           <div class="pricePercentage"></div>
  {%- endif -%}
               
    {%- if unit_pricing != false -%}<span class="unit-pricing" data-unit-pricing>{%- if unit_pricing != blank -%}{{- unit_pricing -}}{%- endif -%}</span>{%- endif -%}
  {%- elsif template == 'price-ui-badge' -%}
    <div class="price-ui-badge__sticker price-ui-badge__sticker--{{ badge | handleize }}">
      <span class="price-ui-badge__sticker-text" data-badge>{%- if badge != blank -%}{{- badge -}}{%- endif -%}</span>
    </div>
  {%- endif -%}
{%- endcapture -%}
{{- output -}}


<script>
  function handleSizeChartPopup() {
    // Get the popup chart container
    const popupContainer = document.querySelector('.popup-chart-container');

    // Toggle the visibility of the popup container
    if (popupContainer.style.display === 'none') {
      popupContainer.style.display = 'block';
    } else {
      popupContainer.style.display = 'none';
    }
  }
  function openPopup() {
    const popupContainer = document.querySelector('.popup-chart-container');
    popupContainer.style.display = 'flex';
  }

  function closePopup() {
    const popupContainer = document.querySelector('.popup-chart-container');
    popupContainer.style.display = 'none';
  }


</script>