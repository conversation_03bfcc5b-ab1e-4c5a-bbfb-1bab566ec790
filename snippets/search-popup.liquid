<div class="search-popup js-search-popup">
  <section class="section search-overlay__wrapper">
    <div class="container">

      <div class="one-whole column text-align-center">
        <h2 class="search-popup__title">{{ settings.search_title }}</h2>
      </div>

      <div class="one-whole column" data-autocomplete-{{ settings.enable_autocomplete }}>
        <form class="search-form search-popup__form" action="{{ routes.search_url }}">
          <div class="search__fields">
            <label for="q" class="visually-hidden">{{ settings.search_placeholder }}</label>
            <div class="field">
              <div class="control has-icons-left search-overlay__control">
                <input
                  class="input"
                  aria-label="q"
                  type="text"
                  name="q"
                  placeholder="{{ settings.search_placeholder }}"
                  value="{{ search.terms }}"
                  x-webkit-speech
                  autocapitalize="off"
                  autocomplete="off"
                  autocorrect="off"
                  data-q />
                <button
                  type="submit"
                  name="search"
                  class="visually-hidden">
                  {%
                    render 'icon'
                    , name: 'search'
                    , class: 'icon is-left'
                    ,
                  %}
                  <span class="visually-hidden">Submit</span>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      {% if settings.search_menu != blank %}
        {% assign search_menu = linklists[settings.search_menu] %}
        <div class="one-whole column text-align-center search-menu__title">
          <span class="search-menu__heading">{{ 'general.search.common_terms' | t }}</span>
        </div>
        <div class="one-whole column">
          <div class="search-menu is-flex">
            {% for link in search_menu.links %}
              <div class="search-menu__item">
                <a class="{% if link.active %}is-active{% endif %}" href="{{ link.url }}">
                  {{ link.title }}
                </a>
              </div>
            {% endfor %}
          </div>
        </div>
      {% endif %}

    </div>
  </section>
</div>