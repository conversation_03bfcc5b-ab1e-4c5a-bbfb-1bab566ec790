{%- comment -%}
  Updated: 02/20/2024
  This file is system-generated and should not be modified. We reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost
  Please, read through this article to make sure that your customizations will be preserved: https://help.zipify.com/en/articles/877075-zipify-pages-advanced-work-with-buy-box-blocks
{%- endcomment -%}{% assign zp_first_selected_variant = nil %}{% assign zp_first_available_variant = nil %}{% assign zp_current_selected_variant = nil %}{% assign zp_product_schema_variant = nil %}{% assign zp_any_product_variant_available = false %}{% assign zp_current_product_price = nil %}{% assign zp_use_product_variant_from_url = false %}{% assign zp_product_entity_type = 'alt' %}{% assign zp_current_product_custom_variants = nil %}{% if zp_intgrt_wboldprdopts %}{% assign zp_incl_boldcmnsnpt = true %}<script>window.BOLD.common.Shopify.saveProduct({{ product.handle | json }},{{ product.id | json }},{{ product | json }});{% for variant in product.variants %}{% assign csp_metafield_namespace = variant.id | prepend: "csp" %}window.BOLD.common.Shopify.saveVariant({{ variant.id | json }},{variant:{{ variant | json }},inventory_quantity:{{ variant.inventory_quantity | json }},product_id:{{ product.id | json }},product_handle:{{ product.handle | json }},price:{{ variant.price | json }},variant_title:{{ variant.title | json }},group_id:"{{ variant.metafields.bold_rp.rp_group_id }}",csp_metafield:{{ product.metafields[csp_metafield_namespace] | json }}});{% endfor %}</script>{% endif %}{% if zp_intgrt_wboldqtbrk == true %}{% include "bold-product" with product, hide_action: "break" %}{% if bold_hidden_product %}{% break %}{% endif %}{% endif %}{% capture zp_replace_integration %}<zp_additional_integration></zp_additional_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_additional_integration>', '' | replace: '</zp_additional_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{% if zp_product_redirect_with_params == nil %}{% assign zp_current_product_redirect_with_params = false %}{% else %}{% assign zp_current_product_redirect_with_params = zp_product_redirect_with_params %}{% endif %}{% if zp_app_integrations contains 'bestcurrencyconverter' %}{% assign zp_static_price = zp_bv_product_static_compare_price | strip | strip_html | split: '' %}{% assign zp_static_selected_prices = '' %}{% assign zp_available_chars = "0123456789,. '" | split: '' %}{% assign zp_price_delimiter_detected = false %}{% for zp_tmp_part in zp_static_price %}{% if zp_available_chars contains zp_tmp_part %}{% if zp_tmp_part == ',' or zp_tmp_part == '.' %}{% assign zp_price_delimiter_detected = true %}{% assign zp_static_price_char = zp_tmp_part %}{% elsif zp_tmp_part == ' ' and zp_price_delimiter_detected %}{% assign zp_price_delimiter_detected = false %}{% assign zp_static_price_char = '__zp1502150400__' %}{% else %}{% assign zp_static_price_char = zp_tmp_part %}{% endif %}{% else %}{% assign zp_static_price_char = '__zp1502150400__' %}{% endif %}{% assign zp_static_selected_prices = zp_static_selected_prices | append: zp_static_price_char %}{% endfor %}{% assign zp_static_price = '' %}{% assign zp_static_selected_prices = zp_static_selected_prices | split: '__zp1502150400__' %}{% for zp_tmp_part in zp_static_selected_prices %}{% assign zp_tmppart = '' | append: zp_tmp_part | strip %}{% if zp_tmppart.size > 0 %}{% assign zp_tmppart = zp_amount_format_template | replace: zp_amount_template, zp_tmppart %}{% assign zp_tmppart_without_html = zp_tmppart | strip_html | strip %}{% assign zp_bv_product_static_compare_price = zp_bv_product_static_compare_price | replace: zp_tmppart_without_html, zp_tmppart %}{% endif %}{% endfor %}{% assign zp_static_selected_prices = '' %}{% assign zp_available_chars = '' %}{% assign zp_tmppart = '' %}{% assign zp_tmppart_without_html = '' %}{% assign zp_static_price = zp_bv_product_static_price | strip | strip_html | split: '' %}{% assign zp_static_selected_prices = '' %}{% assign zp_available_chars = "0123456789,. '" | split: '' %}{% assign zp_price_delimiter_detected = false %}{% for zp_tmp_part in zp_static_price %}{% if zp_available_chars contains zp_tmp_part %}{% if zp_tmp_part == ',' or zp_tmp_part == '.' %}{% assign zp_price_delimiter_detected = true %}{% assign zp_static_price_char = zp_tmp_part %}{% elsif zp_tmp_part == ' ' and zp_price_delimiter_detected %}{% assign zp_price_delimiter_detected = false %}{% assign zp_static_price_char = '__zp1502150400__' %}{% else %}{% assign zp_static_price_char = zp_tmp_part %}{% endif %}{% else %}{% assign zp_static_price_char = '__zp1502150400__' %}{% endif %}{% assign zp_static_selected_prices = zp_static_selected_prices | append: zp_static_price_char %}{% endfor %}{% assign zp_static_price = '' %}{% assign zp_static_selected_prices = zp_static_selected_prices | split: '__zp1502150400__' %}{% for zp_tmp_part in zp_static_selected_prices %}{% assign zp_tmppart = '' | append: zp_tmp_part | strip %}{% if zp_tmppart.size > 0 %}{% assign zp_tmppart = zp_amount_format_template | replace: zp_amount_template, zp_tmppart %}{% assign zp_tmppart_without_html = zp_tmppart | strip_html | strip %}{% assign zp_bv_product_static_price = zp_bv_product_static_price | replace: zp_tmppart_without_html, zp_tmppart %}{% endif %}{% endfor %}{% assign zp_static_selected_prices = '' %}{% assign zp_available_chars = '' %}{% assign zp_tmppart = '' %}{% assign zp_tmppart_without_html = '' %}{% endif %}{% if zp_current_product_custom_variants == blank %}{% assign zp_current_product_custom_variants = product.variants %}{% endif %}{% if zp_selected_variants.size < 1 %}{% assign zp_selected_variants = zp_current_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% endif %}{% if zp_use_product_variant_from_url %}{% assign zp_url_selected_variant_id = '' | append: product.selected_variant.id %}{% else %}{% assign zp_url_selected_variant_id = '' %}{% endif %}{% assign zp_filtered_selected_variants = '' %}{% assign zp_first_selected_variant = nil %}{% assign zp_current_selected_variant = nil %}{% assign zp_any_product_variant_available = false %}{% assign zp_basic_variant_ids = '' %}{% assign zp_bold_variant_ids = '' %}{% assign zp_product_min_price = nil %}{% assign zp_product_max_price = nil %}{% for zp_prd_variant in zp_current_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_intgrt_wboldqtbrk and zp_integrate_with_bold_custom_pricing %}{% assign zp_basic_variant_ids = zp_basic_variant_ids | append: zp_variant_id | append: ',' %}{% assign zp_bold_variant_id = zp_prd_variant.metafields.shappify_csp['csp_base'] | default: zp_prd_variant.metafields.shappify_qb['qb_parent'] | default: zp_prd_variant.metafields.shappify_bundle['bundle_parent'] | default: 'notassigned' %}{% assign zp_bold_variant_ids = zp_bold_variant_ids | append: zp_bold_variant_id | append: ',' %}{% endif %}{% if zp_use_product_variant_from_url and zp_url_selected_variant_id == zp_variant_id and zp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_first_selected_variant = zp_prd_variant %}{% assign zp_current_selected_variant = zp_prd_variant %}{% endif %}{% if zp_selected_variants contains zp_variant_id %}{% assign zp_filtered_selected_variants = zp_filtered_selected_variants | append: zp_variant_id | append: ',' %}{% if zp_first_selected_variant == nil %}{% assign zp_first_selected_variant = zp_prd_variant %}{% endif %}{% if zp_prd_variant.available %}{% assign zp_any_product_variant_available = true %}{% endif %}{% if zp_product_min_price == blank or zp_product_min_price > zp_prd_variant.price %}{% assign zp_product_min_price = zp_prd_variant.price %}{% endif %}{% if zp_product_max_price == blank or zp_product_max_price < zp_prd_variant.price %}{% assign zp_product_max_price = zp_prd_variant.price %}{% endif %}{% endif %}{% endfor %}{% assign zp_selected_variants = zp_filtered_selected_variants | split: ',' %}{% if zp_selected_variants.size < 1 %}{% assign zp_selected_variants = zp_current_product_custom_variants | map: 'id' | join: ',' | split: ',' %}{% assign zp_any_product_variant_available = product.available %}{% if zp_use_product_variant_from_url and zp_selected_variants contains zp_url_selected_variant_id %}{% assign zp_first_selected_variant = product.selected_variant %}{% assign zp_current_selected_variant = product.selected_variant %}{% else %}{% assign zp_first_selected_variant = zp_current_product_custom_variants | first %}{% endif %}{% endif %}{% unless zp_first_selected_variant.available %}{% assign zp_first_available_variant = nil %}{% for zp_prd_variant in zp_current_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% if zp_selected_variants contains zp_variant_id and zp_prd_variant.available %}{% assign zp_first_available_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% unless zp_first_available_variant == nil %}{% assign zp_first_selected_variant = zp_first_available_variant %}{% endunless %}{% assign zp_first_available_variant = nil %}{% endunless %}{% assign zp_variant_id = nil %}{% if zp_intgrt_wboldqtbrk and zp_integrate_with_bold_custom_pricing%}{% assign zp_basic_variant_ids = zp_basic_variant_ids | split: ',' %}{% assign zp_bold_variant_ids = zp_bold_variant_ids | split: ',' %}{% assign zp_bold_variant_id = nil %}{% assign zp_variant_id = '' | append: zp_first_selected_variant.id %}{% for zp_prd_variant in zp_bold_variant_ids %}{% if zp_variant_id == zp_prd_variant %}{% assign zp_bold_variant_id = zp_basic_variant_ids[forloop.index0] %}{% break %}{% endif %}{% endfor %}{% if zp_bold_variant_id and zp_bold_variant_id != zp_variant_id %}{% assign zp_break_iterator = false %}{% for zp_prd_variant in product.variants %}{% if zp_break_iterator %}{% break %}{% endif %}{% assign zp_prd_variant_id = '' | append: zp_prd_variant.id %}{% if zp_prd_variant_id == zp_bold_variant_id %}{% assign zp_break_iterator = true %}{% if zp_intgrt_wboldqtbrk %}{% include 'bold-variant' with zp_prd_variant, hide_action: 'skip' %}{% endif %}{% assign zp_first_selected_variant = zp_prd_variant %}{% break %}{% endif %}{% endfor %}{% endif %}{% endif %}{% assign zp_current_product_price = zp_first_selected_variant.price %}{% if zp_first_selected_variant.compare_at_price > zp_first_selected_variant.price %}{% assign zp_current_product_compare_at_price = zp_first_selected_variant.compare_at_price %}{% else %}{% assign zp_current_product_compare_at_price = nil %}{% endif %}{% if zp_product_calculate_bundle_price %}{% assign zp_current_product_price = zp_current_product_price | times: zp_product_default_quantity %}{% if zp_current_product_compare_at_price %}{% assign zp_current_product_compare_at_price = zp_current_product_compare_at_price | times: zp_product_default_quantity %}{% endif %}{% endif %}
<div class="xs-12 zpa-flex--column zpa-bv2 zps_selclassescape" id="{{ zp_product_wrapper_selector }}" data-product-content>
  <div class="flex-row zpa-bv2-margin-h-none zpa-bv2-border zp zps_bvuppclassescape">
    <div class="xs-12 sm-6 md-4 zpa-bv2-label-wrap">
      <div class="flex-row zpa-bv2-label-grow">
        <div class="xs-12 sm-2 zpa-bv2-label zp zps_lblblclassescape">
          <div class="zpa-bv2-label-text-wrap zpa-mobile-offset-bottom-sm zp zps_bvcrnrclassescape">
            <div class="zpa-bv2-label-text">zps_lbltxtcont</div>
          </div>
        </div>
        <div class="xs-12 sm-10 zpa-bv2-image-wrap zpa-mobile-offset-bottom-xs">
          <div class="zpa-overflow zp zps_imgblclassescape">
            <a role="button" data-zp-product-image-link>
              <img class="zpa-img-fluid zpa-center-block zp zps_brd1blclassescape" zps_imgimgattr>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="xs-12 sm-6 md-8">{% if product.available %}
      <form class="flex-row middle-xs zpa-bv2-label-wrap zpa-bv2-content-block zpa-text-center {% if zp_intgrt_wboldqtbrk %}shapp_qb_prod{% endif %}" method="post" enctype="multipart/form-data" data-zp-add-to-cart-form data-zp-link-type="zps_prdbtnltpescape" data-productid="{{ product.id }}" data-zp-redirect-with-url-params="{{ zp_current_product_redirect_with_params }}"{% unless zp_enable_subscription_widget %} data-zp-disable-subscrptn-wdgt{% endunless %}>{% else %}<div class="flex-row middle-xs zpa-bv2-label-wrap zpa-bv2-content-block zpa-text-center">{% endif %}{%- capture zp_replace_integration -%}<zp_product_custom_variants_integration></zp_product_custom_variants_integration>{%- endcapture -%}{% assign zp_replace_integration = '' %}
        <div class="xs-12 sm-12 md-6">
          <div class="zpa-offset-bottom-sm zpa-mobile-offset-bottom-xs">
            <div class="{% unless zp_bv_product_header_title contains ' tsl-' or zp_bv_product_header_title contains '"tsl-' or zp_bv_product_header_title contains ' ts-' or zp_bv_product_header_title contains '"ts-' %}default-styles-wrapper{% endunless %} zpa-word-wrap disable-internal-link zp zps_ttl2blclassescape">{{ zp_bv_product_header_title }}</div>
          </div>
          <div class="zpa-offset-bottom-sm zpa-mobile-offset-bottom-xs">
            <div class="zpa-inline-block zpa-offset-right">
              <div class="{% unless zp_bv_product_static_compare_price contains ' tsl-' or zp_bv_product_static_compare_price contains '"tsl-' or zp_bv_product_static_compare_price contains ' ts-' or zp_bv_product_static_compare_price contains '"ts-' %}default-styles-wrapper{% endunless %} zpa-word-wrap disable-internal-link zp zps_ttl3blclassescape">{{ zp_bv_product_static_compare_price }}</div>
            </div>
            <div class="zpa-inline-block">
              <div class="{% unless zp_bv_product_static_price contains ' tsl-' or zp_bv_product_static_price contains '"tsl-' or zp_bv_product_static_price contains ' ts-' or zp_bv_product_static_price contains '"ts-' %}default-styles-wrapper{% endunless %} zpa-word-wrap disable-internal-link zp zps_ttl4blclassescape">{{ zp_bv_product_static_price }}</div>
            </div>
          </div>{% if zp_any_product_variant_available %}{% if zp_unit_price_enabled %}<div class="zpa-offset-bottom-sm zpa-mobile-offset-bottom-xs zp zps_dcrt3classescape{% unless zp_first_selected_variant.unit_price_measurement %} hidden{% endunless %}" data-zp-product-unit-price-wrapper><span data-zp-product-unit-price>{% if zp_first_selected_variant.unit_price_measurement %}{{ zp_first_selected_variant.unit_price | money }}{% endif %}</span><span> / </span><span data-zp-product-up-base-unit>{% if zp_first_selected_variant.unit_price_measurement %}{% if zp_first_selected_variant.unit_price_measurement.reference_value != 1 %}{{ zp_first_selected_variant.unit_price_measurement.reference_value }}{% endif %}{{ zp_first_selected_variant.unit_price_measurement.reference_unit }}{% endif %}</span></div>{% endif %}{% endif %}
          <div class="zpa-offset-bottom-sm zpa-mobile-offset-bottom-xs">
            <div class="{% unless zp_bv_product_additional_description contains ' tsl-' or zp_bv_product_additional_description contains '"tsl-' or zp_bv_product_additional_description contains ' ts-' or zp_bv_product_additional_description contains '"ts-' %}default-styles-wrapper{% endunless %} zpa-word-wrap disable-internal-link zp zps_ttl5blclassescape">{{ zp_bv_product_additional_description }}</div>
          </div>{% if product.available and zp_shw_vrntsslctr %}
          <div class="zpa-bv2-variants-form zpa-offset-bottom-sm zpa-mobile-offset-bottom-xs">{% capture zp_replace_integration %}<zp_additional_property_integration></zp_additional_property_integration>{% endcapture %}{% if zp_integrate_with_recharge and zp_enable_subscription_widget %}{% assign zp_recharge_wrapper_content = '<div id="' | append: zp_product_selector | append: '-recharge-wrapper" class="zpa-offset-bottom-xs zpa-recharge-wrapper" data-zp-recharge-product="' | append: product.id | append: '" data-zp-wrapper="' | append: zp_product_selector | append: '"></div>' %}{% assign zp_replace_integration = zp_replace_integration | prepend: zp_recharge_wrapper_content %}{% endif %}{{ zp_replace_integration | replace: '<zp_additional_property_integration>', '' | replace: '</zp_additional_property_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}<select class="hidden noreplace" id="{{ zp_product_selector }}" name="id" data-productid="{{ product.id }}" aria-hidden="true">{% assign zp_first_selected_variant_id = '' | append: zp_first_selected_variant.id %}{% for variant in zp_current_product_custom_variants %}{% assign zp_variant_id = '' | append: variant.id %}{% unless zp_selected_variants contains zp_variant_id %}{% continue %}{% endunless %}{% capture zp_replace_integration %}<zp_variant_integration></zp_variant_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_variant_integration>', '' | replace: '</zp_variant_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{% if zp_intgrt_wboldqtbrk %}{% include 'bold-variant' with variant, hide_action: 'skip' %}{% endif %}{% if variant.available %}{% if zp_first_selected_variant_id == zp_variant_id %}{% assign zp_option_selected_attr = 'selected="selected" ' %}{% else %}{% assign zp_option_selected_attr = '' %}{% endif %}<option {{ zp_option_selected_attr }}value="{{ variant.id }}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% else %}<option disabled="disabled" value="{{ variant.id }}">{{ variant.title }} - {% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}</option>{% endif %}{% endfor %}</select>{% if zp_incl_boldsbsrpns %}{% include 'bsub-widget' %}{% endif %}{% if zp_intgrt_wboldprdopts %}<div class="bold_options" data-product-id="{{ product.id }}"></div>{% endif %}{% capture zp_replace_integration %}<zp_property_integration></zp_property_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_property_integration>', '' | replace: '</zp_property_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}<div class="zpa-selector-wrapper zpa-quantity zpa-center-block" data-zp-quantity-wrapper>{% if zp_product_show_quantity %}{% unless zp_product_show_quantity_label == false %}<label for="{{ zp_product_selector }}-quantity">{% assign zp_translation = 'products.product.quantity.label' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = 'products.product.quantity' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Quantity" %}{% endif %}{% endif %}{{ zp_translation }}</label>{% endunless %}<div class="zpa-quantity-block" data-product-wrapper-id="{{ zp_product_element_id }}" data-zp-quantity-el><button class="zpa-btn-custom zpa-quantity-btn" type="button" title="{% assign zp_translation = 'products.product.quantity.decrease' | t: product: product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Decrease quantity" %}{% endif %}{{ zp_translation }}" data-zp-product-decrease-qty><svg class="zpa-quantity-icon" width="12px" height="12px"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#iconDecrease"></use></svg></button><input id="{{ zp_product_selector }}-quantity" class="zpa-quantity-field xs" value="zps_qtyescape" maxlength="3" size="3" type="tel" inputmode="numeric" required pattern="^[1-9][0-9]*" name="quantity" title="{% assign zp_translation = 'products.product.quantity.input_label' | t: product: product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Quantity" %}{% endif %}{{ zp_translation }}" data-zp-product-quantity><button class="zpa-btn-custom zpa-quantity-btn" type="button" title="{% assign zp_translation = 'products.product.quantity.increase' | t: product: product.title %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Increase quantity" %}{% endif %}{{ zp_translation }}" data-zp-product-increase-qty><svg class="zpa-quantity-icon" width="12px" height="12px"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#iconIncrease"></use></svg></button></div>{% else %}<input id="{{ zp_product_selector }}-quantity" type="hidden" name="quantity" value="zps_qtyescape" data-zp-product-quantity>{% endif %}</div></div>{% if zp_selected_variants.size > 1 %}
          <div class="zpa-text-center zpa-offset-bottom-xs">
            <span data-zp-product-price-wrapper><span class="zpa-compare-price zpa-offset-right zpa-word-wrap zp zps_dcrt1classescape" data-zp-product-price>{{ zp_current_product_compare_at_price | money }}</span></span>
            <span class="zpa-regular-price zpa-word-wrap zp zps_dcrt2classescape" data-zp-product-discount-price>{{ zp_current_product_price | money }}</span>
          </div>{% endif %}{% endif %}
        </div>
        <div class="xs-12 sm-12 md-6 zpa-text-center">
          <div class="zpa-offset-bottom-md zpa-tablet-offset-bottom-sm zpa-mobile-offset-bottom-xs">{% if zp_include_product_button_main_classes %}{% assign zp_product_button_base_main_classes = "zpa-mobile-btn-size" %}{% else %}{% assign zp_product_button_base_main_classes = '' %}{% endif %}{% if zp_include_product_button_alignment_classes %}{% assign zp_product_button_base_alignment_classes = "zpa-text-center-sm" %}{% else %}{% assign zp_product_button_base_alignment_classes = '' %}{% endif %}{% if product.available %}{%- if zp_intgrt_wboldqtbrk %}<div class="shappify-qty-msg">{{ bold_selected_or_first_available_variant.metafields.shappify_qb.pricing_html }}</div>{% endif -%}{% capture zp_replace_integration %}<zp_product_info_integration></zp_product_info_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_info_integration>', '' | replace: '</zp_product_info_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}<div class="zps_prdbtnalignescape {{ zp_product_button_base_alignment_classes }}"><button type="submit" name="add" disabled data-zp-add-to-cart data-zp-add-and-go-to-cart="true" class="zpa-btn-custom zpa-add-to-cart-btn zp zps_prdlbtnclassescape {{ zp_product_button_base_main_classes }}" data-zp-link-type="zps_prdbtnltpescape"><span class="zpa-btn-custom__caption" data-zp-add-to-cart-text>{% if zp_any_product_variant_available %}zps_prdbtncpt{% else %}{% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}{% endif %}</span><svg class="zpa-btn-loader zpa-btn-custom__icon" width="14px" height="14px"><use href="#buyButtonLoader"></use></svg></button>{% if zp_shw_vrntsslctr %}<div class="zpa-product-message-wrap zpa-product-message-wrap-center"><span class="zpa-product-message product-success hidden zp zps_crtmsgclassurl_escape" data-zp-product-messages data-zp-success-message="zps_addcrtmsgurl_escape"></span></div>{% endif %}</div>{% else %}<div class="zps_prdbtnalignescape {{ zp_product_button_base_alignment_classes }}"><button type="button" disabled class="zpa-btn-custom zpa-add-to-cart-btn zp zps_prdlbtnclassescape {{ zp_product_button_base_main_classes }}">{% assign zp_translation = 'products.product.sold_out' | t %}{% if zp_translation contains 'Translation missing' or zp_translation contains 'translation missing' %}{% assign zp_translation = "Sold Out" %}{% endif %}{{ zp_translation }}</button></div>{% capture zp_replace_integration %}<zp_product_soldout_integration></zp_product_soldout_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_soldout_integration>', '' | replace: '</zp_product_soldout_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{% endif %}</div>
          <div class="zp zps_img2blclassescape">
            <img class="zpa-img-fluid zp zps_brd2blclassescape" zps_img2attr>
          </div>
        </div>{% if product.available %}
      </form>{% else %}</div>{% endif %}
    </div>{%- if zp_product_found and zp_display_product_markup -%}{% assign zp_product_image = zp_product_image_attributes | append: ' ' | split: ' src=' | last | split: ' ' | first | remove: '"' | remove: "'" %}{% assign zp_product_custom_title = zp_product_custom_title | strip_html | strip | default: product.title | strip_html | strip %}{% assign zp_product_custom_description = zp_product_custom_description | strip_html | strip | default: product.description | strip_html | strip %}{% if zp_current_selected_variant != blank %}{% assign zp_product_schema_variant = zp_current_selected_variant %}{% else %}{% assign zp_product_schema_variant = zp_first_selected_variant %}{% endif %}{% assign zp_product_custom_image = zp_product_custom_image | strip | default: zp_product_image | strip %}{% if zp_prdct_shcrlenbl or zp_pcrls_dt != blank %}{% if zp_first_selected_variant.available and zp_first_selected_variant.featured_media != blank and zp_first_selected_variant.featured_media.media_type == 'image' %}{% assign zp_product_custom_image_obj = zp_first_selected_variant.featured_media %}{% else %}{% assign zp_product_custom_image_obj = product.media | where: 'media_type', 'image' | first | default: zp_product_schema_variant.featured_image | default: product.featured_image %}{% endif %}{% assign zp_product_custom_image = zp_product_custom_image_obj | image_url: width: zp_product_custom_image_obj.preview_image.width | prepend: 'https:' %}{% elsif zp_product_custom_image == blank or zp_product_custom_image == '' or zp_product_custom_image contains '/default_images_product_icon_360x240.jpg' %}{% assign zp_product_custom_image_obj = zp_product_schema_variant.featured_image | default: product.featured_image %}{% assign zp_product_custom_image = zp_product_custom_image_obj | image_url: width: zp_product_custom_image_obj.preview_image.width | prepend: 'https:' %}{% endif %}{%- endif -%}
    {% capture zp_replace_integration %}<zp_product_additional_footer_integration></zp_product_additional_footer_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_additional_footer_integration>', '' | replace: '</zp_product_additional_footer_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}
  </div>
</div><script type="text/json" data-zp-product-init>{"translations":{"addToCart":"zps_prdbtncpturl_escape"},"productSelector":"{{ zp_product_selector }}","selectedVariants":"zps_slctvrnturl_escape","productType":"{{ zp_product_type }}","imageSize":"{{ '' | append: zp_current_product_image_size | strip | default: 'master' | url_escape }}","productDiscountData":"0:compare_price","initBuilder":{{ zp_any_product_variant_available | default: 'false' }},"sbscrbPriceRefresh":{{ zp_integrate_with_recharge | default: 'false' }},"calculateBundlePrice":{{ zp_product_calculate_bundle_price | default: 'false' }},"defaultQty":{{ 0 | plus: zp_product_default_quantity }},"productElementId":"{{ zp_product_element_id }}","redirectUrl":{{ zp_redirect_product_url | json }},"redirectTarget":"zps_prdbtneltescape","product":{% capture zp_replace_integration %}<zp_product_json_integration>{% if zp_intgrt_wboldqtbrk %}{% capture zp_product_json %}{% include 'bold-product', product: product, output: 'json' %}{% endcapture %}{% if zp_product_json contains 'Could not find asset ' %}{{ product | json | default: 'null' }}{% else %}{{ zp_product_json | strip | default: 'null' }}{% endif %}{% assign zp_product_json = nil %}{% else %}{{ product | json | default: 'null' }}{% endif %}</zp_product_json_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_json_integration>', '' | replace: '</zp_product_json_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}}</script>
{% capture zp_replace_integration %}<zp_product_footer_integration></zp_product_footer_integration>{% endcapture %}{{ zp_replace_integration | replace: '<zp_product_footer_integration>', '' | replace: '</zp_product_footer_integration>', '' | strip }}{%- assign zp_replace_integration = '' -%}{% if zp_product_found and zp_display_product_markup %}<script type="application/ld+json">{"@context":"http://schema.org/","@type":"Product",{% if product.vendor != blank %}"brand":{"@type":"Brand","name":{{ product.vendor | json }}},{% endif %}"name":{{ zp_product_custom_title | json }},"description":{{ zp_product_custom_description | json }},"url":{{ request.origin | append: zp_current_entity.url | json }},{% if zp_product_custom_image != blank %}"image":[{{ zp_product_custom_image | json }}],{% endif %}{% if zp_product_schema_variant.sku != blank %}"sku":{{ zp_product_schema_variant.sku | json }},{% endif %}"offers":[{% assign zp_prd_schema_offer_separator = '' %}{% for zp_prd_variant in zp_current_product_custom_variants %}{% assign zp_variant_id = '' | append: zp_prd_variant.id %}{% unless zp_selected_variants contains zp_variant_id %}{% continue %}{% endunless %}{{ zp_prd_schema_offer_separator }}{% assign zp_prd_schema_offer_separator = ',' %}{% assign zp_prd_variant_prc = zp_prd_variant.price %}{% if zp_prd_variant.compare_at_price > zp_prd_variant.price %}{% assign zp_prd_variant_cmpr_prc = zp_prd_variant.compare_at_price %}{% else %}{% assign zp_prd_variant_cmpr_prc = nil %}{% endif %}{% assign zp_prd_variant_dscnt_amnt_recalculate = true %}{% assign zp_prd_variant_dscnt_amnt_money = false %}{% assign zp_prd_variant_dscnt_amnt_prefix = '' %}{% assign zp_prd_variant_dscnt_amnt_suffix = '' %}{% if zp_product_discount_data contains ':' %}{% assign zp_product_discount_data = zp_product_discount_data | split: ':' %}{% assign zp_split_discount_data = true %}{% else %}{% assign zp_split_discount_data = false %}{% endif %}{% if zp_product_discount_data.size > 1 %}{% assign zp_prd_variant_dscnt_amnt = 0 | plus: zp_product_discount_data[0] | abs %}{% assign zp_prd_variant_dscnt_tp = '' | append: zp_product_discount_data[1] %}{% if zp_prd_variant_dscnt_tp == 'percentage' or zp_prd_variant_dscnt_tp == 'percentage_dynamic' %}{% assign zp_product_discount_amount_parts = '' | append: zp_prd_variant_dscnt_amnt | split: '.' %}{% assign zp_product_discount_decimal_part = '' | append: zp_product_discount_amount_parts[1] | default: '0' %}{% assign zp_product_discount_decimal_part = 0 | plus: zp_product_discount_decimal_part %}{% unless zp_product_discount_decimal_part > 0 %}{% assign zp_prd_variant_dscnt_amnt = zp_product_discount_amount_parts[0] %}{% endunless %}{% assign zp_prd_variant_dscnt_amnt = 0 | plus: zp_prd_variant_dscnt_amnt | at_most: 100 %}{% assign zp_prd_variant_svd_amnt = zp_prd_variant_prc | times: zp_prd_variant_dscnt_amnt | divided_by: 100 | at_most: zp_prd_variant_prc %}{% assign zp_prd_variant_dscnt_amnt_recalculate = false %}{% assign zp_prd_variant_dscnt_amnt_suffix = '%' %}{% elsif zp_prd_variant_dscnt_tp == 'compare_price' %}{% if zp_prd_variant_cmpr_prc %}{% assign zp_prd_variant_dscnt_amnt = zp_prd_variant_cmpr_prc | minus: zp_prd_variant_prc %}{% else %}{% assign zp_prd_variant_dscnt_amnt = 0 %}{% assign zp_prd_variant_unvlbl_dscnt_cls = 'hidden' %}{% endif %}{% assign zp_prd_variant_svd_amnt = zp_prd_variant_dscnt_amnt %}{% else %}{% assign zp_prd_variant_dscnt_amnt = zp_prd_variant_dscnt_amnt | times: 100 %}{% assign zp_prd_variant_dscnt_amnt = '' | append: zp_prd_variant_dscnt_amnt | split: '.' | first | default: 0 %}{% assign zp_prd_variant_dscnt_amnt = 0 | plus: zp_prd_variant_dscnt_amnt | at_most: zp_prd_variant_prc %}{% assign zp_prd_variant_svd_amnt = zp_prd_variant_dscnt_amnt %}{% assign zp_prd_variant_dscnt_amnt_money = true %}{% endif %}{% if zp_prd_variant_dscnt_tp == 'compare_price' %}{% assign zp_prd_variant_sale_prc = zp_prd_variant_prc %}{% else %}{% assign zp_prd_variant_sale_prc = zp_prd_variant_prc | minus: zp_prd_variant_svd_amnt %}{% endif %}{% assign zp_prd_variant_dscnt_amnt_prefix = '-' %}{% else %}{% assign zp_prd_variant_sale_prc = zp_prd_variant_prc %}{% assign zp_prd_variant_unvlbl_dscnt_cls = 'hidden' %}{% assign zp_prd_variant_dscnt_amnt = 0 %}{% endif %}{% if zp_split_discount_data %}{% assign zp_product_discount_data = zp_product_discount_data | join: ':' %}{% endif %}{"@type":"Offer",{% if zp_prd_variant.sku != blank %}"sku":{{ zp_prd_variant.sku | json }},{% endif %}{% if zp_prd_variant.barcode.size == 12 %}"gtin12":{{ zp_prd_variant.barcode }},{% endif %}{% if zp_prd_variant.barcode.size == 13 %}"gtin13":{{ zp_prd_variant.barcode }},{% endif %}{% if zp_prd_variant.barcode.size == 14 %}"gtin14":{{ zp_prd_variant.barcode }},{% endif %}"url":{{ request.origin | append: zp_prd_variant.url | json }},"priceCurrency":{{ cart.currency.iso_code | json }},"price":{{ zp_prd_variant_sale_prc | divided_by: 100.00 | json }},"availability":"http://schema.org/{% if zp_prd_variant.available %}InStock{% else %}OutOfStock{% endif %}"}{% endfor %}]{% if zp_product_review_count %},"aggregateRating":{"@type":"AggregateRating","ratingValue":{{ zp_product_review_rating | json }},"ratingCount":{{ zp_product_review_count | json }}}{% endif %}}</script>{% endif %}{% assign zp_first_selected_variant = nil %}{% assign zp_sale_price = nil %}{% assign zp_saved_amount = nil %}{% assign zp_current_product_price = nil %}{% assign zp_product_custom_title = nil %}{% assign zp_product_custom_description = nil %}{% assign zp_product_custom_image = nil %}{% assign zp_use_product_stamped_rating = nil %}{% assign zp_use_product_yotpo_rating = nil %}{% assign zp_use_product_loox_rating = nil %}{% assign zp_use_product_judgeme_rating = nil %}{% assign zp_product_review_rating = nil %}{% assign zp_product_review_count = nil %}{% assign zp_product_found = false %}{% assign zp_use_product_variant_from_url = false %}{% assign zp_product_entity_type = nil %}{% assign zp_current_product_custom_variants = nil %}{% assign zp_product_element_id = nil %}{% assign zp_products_static_discounts = nil %}{% assign zp_current_product_static_discount_data = nil %}{% assign zp_product_calculate_bundle_price = false %}