<div class="search-overlay animated fadeIn" data-search-type="overlay">

  {% render 'icon'
    , name: 'x'
    , icon_class: 'search-overlay__close'
  %}

  <section class="section search-overlay__wrapper">
    <div class="container">

      <div class="one-whole column text-align-center">
        <h1 class="search-overlay__title">{{ settings.search_title }}</h1>
      </div>

      <div class="one-whole column" data-autocomplete-{{ settings.enable_autocomplete }}>
        <form class="search-form search-popup__form" action="{{ routes.search_url }}">
          <div class="search__fields">
            <label for="q" class="visually-hidden">{{ settings.search_placeholder }}</label>
            <div class="field">
              <div class="control has-icons-left search-overlay__control">
                <input
                  class="input"
                  type="text"
                  name="q"
                  aria-label="q"
                  placeholder="{{ settings.search_placeholder }}"
                  value="{{ search.terms }}"
                  x-webkit-speech
                  autocapitalize="off"
                  autocomplete="off"
                  autocorrect="off"
                  data-q />
                <button
                  type="submit"
                  name="search"
                  value="Submit"
                  class="visually-hidden">
                  {%
                    render 'icon'
                    , name: 'search'
                    , class: 'icon is-left'
                    ,
                  %}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      {% if settings.search_menu != blank %}
        {% assign search_menu = linklists[settings.search_menu] %}
        <div class="one-whole column text-align-center">
          <div class="search-menu">
            <span class="search-menu__heading">{{ 'general.search.common_terms' | t }}:</span>
            <ul class="search-menu__list">
              {% for link in search_menu.links %}
                <li class="search-menu__item">
                  <a class="{% if link.active %}is-active{% endif %}" href="{{ link.url }}">
                    {{ link.title }}
                  </a>
                </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      {% endif %}

    </div>
  </section>
</div>