{% capture current_url %}
  {%- if context == 'article' -%}
    {{ shop.url }}{{ article.url }}
  {%- else -%}
    {{ canonical_url }}
  {%- endif -%}
{% endcapture %}

{% capture buttons %}
<div class="social-share-buttons social-share-buttons--{{ settings.social_media_share_style }} field is-grouped has-margin-bottom">
  {% capture title %}{% if context == 'article' %}{{ article.title | strip_html | url_escape }}{% else %}{{ product.title | strip_html | url_escape }}{% endif %}{% endcapture %}
  {% capture shop_name %}{{ shop.name | url_param_escape }}{% endcapture %}
  {% capture x_name %}{{ settings.x_link | split: 'x.com/' | last | split: 'twitter.com/' | last }}{% endcapture %}

  {% comment %} X {% endcomment %}
  <div class="share-btn share-btn--x control">
    <a target="_blank" rel="noopener" class="button" title="{{ 'layout.social_sharing.x_title' | t }}" href="https://x.com/intent/tweet?text={{ title | replace: '&','%26' }}&url={{ current_url }}{%- if x_name != blank -%}&via={{ x_name }}{%- endif -%}">
      {% render 'icon', name: 'x-social' %}
    </a>
  </div>

  {% comment %} Facebook {% endcomment %}
  <div class="share-btn share-btn--facebook control">
    <a target="_blank"  rel="noopener"class="button" title="{{ 'layout.social_sharing.facebook_title' | t }}" href="https://www.facebook.com/sharer/sharer.php?u={{ current_url }}">
      {% render 'icon', name: 'facebook' %}
    </a>
  </div>

  {% comment %} Pinterest {% endcomment %}
  <div class="share-btn share-btn--pinterest control">
    {% if context == 'article' %}
      <a target="_blank"  rel="noopener"data-pin-do="skipLink" class="button" title="{{ 'layout.social_sharing.pinterest_title' | t }}" href="https://pinterest.com/pin/create/button/?url={{ current_url }}&description={{ article.content | strip_html | truncate: 240 }}&media={% if article.image %}{{ article.image | img_url: 'grande' }}{% else %}{% assign my_image_url=article.content | escape %}{% if my_image_url contains '&lt;img' %}{% assign my_image_url = my_image_url | split: 'src=&quot;' %}{% assign my_image_url = my_image_url[1] | split: '&quot;' %}{% assign my_image_url = my_image_url[0] | remove: '//cdn' %}{% assign my_image_url = my_image_url | remove: 'http:http://' %}{% assign my_image_url = my_image_url | remove: 'https:' %}{{ my_image_url | prepend: 'https://cdn'}}{% endif %}{% endif %}">
        {% render 'icon', name: 'pinterest' %}
      </a>
    {% else %}
      <a target="_blank"  rel="noopener"data-pin-do="skipLink" class="button" title="{{ 'layout.social_sharing.pinterest_title' | t }}" href="https://pinterest.com/pin/create/button/?url={{ current_url }}&description={{ title | truncate: 240 }}&media=https:{{ product.featured_image.src | product_img_url: '600x' }}">
        {% render 'icon', name: 'pinterest' %}
      </a>
    {% endif %}
  </div>

  <div class="share-btn share-btn--mail control"><!--Email-->
    <a href="mailto:?subject={{ 'layout.social_sharing.email_subject' | t: title: title }}&amp;body={{ 'layout.social_sharing.email_message' | t: title: title, name: shop_name }}%0D%0A%0D%0A{{ current_url }}" target="_blank"  rel="noopener" class="button" title="{{ 'layout.social_sharing.email_title' | t }}">
      {% render 'icon', name: 'email' %}
    </a>
  </div>
</div>

{% endcapture %}

{{ buttons }}
