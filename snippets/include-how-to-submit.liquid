<style>
  .product__how-to-submit {
    padding: 60px 0;

    @media screen and (max-width: 479px) {
      padding: 30px;
    }
  }

  .product__how-to-submit h2 {
    font-size: 25px;
    margin-bottom: 40px;

    @media screen and (max-width: 767px) {
      font-size: 24px;
    }
  }

  .product__how-to-submit__tabs {
    .tabs {
      display: flex;
      width: 100%;
      text-align: center;
      background-color: $cuddleclones-pink-light;
      color: #fff;
      margin: 0;


      > div {
        flex: 1;
        padding: 20px 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        @media screen and (max-width: 767px) {
          padding: 15px 10px;
          font-size: 18px;
        }

        @media screen and (max-width: 479px) {
          padding: 10px;
          font-size: 13px;
        }


        &.active,
         &:hover {
          background-color: $cuddleclones-pink;
        }
      }
    }

    .contents {

      border-right: 1px solid $cuddleclones-pink;
      border-bottom: 1px solid $cuddleclones-pink;
      border-left: 1px solid $cuddleclones-pink;
      padding: 30px;

      @media screen and (max-width: 479px) {
        padding: 20px 15px;
      }

      & > div {
        display: flex;
        justify-content: space-around;

        @media screen and (max-width: 767px) {
          flex-direction: column;
        }

         &:not(.active) {
          display: none;
        }

        > div {
          width: 45%;

          @media screen and (max-width: 767px) {
            width: 100%;
          }
        }

        .content {
          display: flex;
          flex-direction: column;
          justify-content: center;

          .links {

            a {
              background-color: $cuddleclones-pink;
              display: inline-block;
              color: #fff;
              padding: 5px 20px 3px;
              border-radius: 9999px;
              font-size: 14px;
              transition: all 0.15s ease;

               &:hover {
                color: #fff;
                background-color: $cuddleclones-pink-light;
              }
            }

            [data-prev] {
              float: left;
            }

            [data-next] {
              float: right;
            }
          }
        }
      }
    }
  }
</style>

<div class="product__how-to-submit section is-width-standard ">
  <h2 class="text-center">{{ settings.title }}</h2>


  <div class="product__how-to-submit__tabs">
    <div class="tabs">
      {% for i in (1..3) %}
        {% assign name = 'tab_' | append: i | append: '_name' %}
        <div data-step="{{ forloop.index }}" class="{% if forloop.first %}active{% endif %}">{{ settings[name] }}</div>
      {% endfor %}
    </div>

    <div class="contents">
      {% for i in (1..3) %}
        {% assign image = 'tab_' | append: i | append: '_image' %}
        {% assign name = 'tab_' | append: i | append: '_name' %}
        {% assign description = 'tab_' | append: i | append: '_description' %}

        <div data-step="{{ forloop.index }}" class="{% if forloop.first %}active{% endif %}">
          <div class="image">
            <img src="{{ settings[image]  | img_url: '600x'}}" alt="{{ settings[name] }}">
          </div>
          <div class="content">
            <p>{{ settings[description] | strip_html | strip_newlines }}</p>

            <div class="links">
              {% unless forloop.first %}
                <a
                  href="#"
                  data-to-step="{{ forloop.index | minus: 1 }}"
                  data-prev>&larr; Previous</a>
              {% endunless %}

              {% unless forloop.last %}
                <a
                  href="#"
                  data-to-step="{{ forloop.index | plus: 1 }}"
                  data-next>Next &rarr;</a>
              {% endunless %}
            </div>

          </div>
        </div>
      {% endfor %}
    </div>
  </div>
</div>