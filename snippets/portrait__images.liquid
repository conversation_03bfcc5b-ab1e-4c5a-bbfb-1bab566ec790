<head>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
</head>
<style>
  img {
  ;
  /* vertical-align: middle; */
}
.customGalleryImageContainer {
  position: relative;
  display: block;
}
.mySlides {
  display: none;
  margin-bottom: 20px;
}
.cursor {
  cursor: pointer;
}
.prev {
  left: 10px;
}
.next {
  right: 10px !important;
}
.prev,
.next {
  display: none;
  cursor: pointer;
  position: absolute;
  top: 45%;
  width: auto;
  padding: 16px;
  margin-top: -50px;
  color: black;
  font-weight: bold;
  font-size: 20px;
  border-radius: 0 3px 3px 0;
  user-select: none;
  -webkit-user-select: none;
}
.next {
  right: 0;
  border-radius: 3px 0 0 3px;
}
.prev,
.next {
  background-color: white;
  border-radius: 50%;
  height: 45px;
  width: 45px;
}
.thumbnailRow {
  display: flex;
}
.row:after {
  content: "";
  display: table;
  clear: both;
}
.demo {
  opacity: 0.6;
  height: 50px;
}

.active,
.demo:hover {
  opacity: 1;
}

.columnContainer {
  float: left;
  width: 16.66%;
  margin-right: 10px;
}
.galleryImage {
  width: 559px;
  height: 475px;
  cursor: pointer;
}
.customGalleryImageContainer:hover .prev,
.customGalleryImageContainer:hover .next {
  display: flex;
}

@media (max-width: 800px) {
  .galleryImage {
    width: 100%;
    height: 334px;
  }
  .prev,
  .next {
    display: flex;
  }
}
</style>
<body>
  <div class="customGalleryImageContainer">
    {% for media in images %}
      <div class="mySlides">
        <img
          class="galleryImage"
          src="{{media.img_url}}"
          alt="{{media.alt}}"
          style="width:100%">
      </div>
    {% endfor %}
    <a class="prev" onclick="plusSlides(-1)">
      <img
        width="30"
        src="{{ 'left-arrow.png' | asset_url }}"
        alt="Previous"></a>
    <a class="next" onclick="plusSlides(1)">
      <img
        width="30"
        src="{{ 'right-arrow.png' | asset_url }}"
        alt="Next">
    </a>

    <div class="thumbnailRow">
      {% for media in images %}
        <div class="columnContainer">
          <img
            class="demo cursor"
            src="{{media.img_url}}"
            alt="{{media.alt}}"
            style="width:100%"
            onclick="currentSlide({{forloop.index}})">
        </div>
      {% endfor %}
    </div>
  </div>

  <script>
    let slideIndex = 1;
    showSlides(slideIndex);

    function plusSlides(n) {
      showSlides(slideIndex += n);
    }

    function currentSlide(n) {
      showSlides(slideIndex = n);
    }

    function showSlides(n) {
      let i;
      let slides = document.getElementsByClassName("mySlides");
      let dots = document.getElementsByClassName("demo");
      if (n > slides.length) {slideIndex = 1}
      if (n < 1) {slideIndex = slides.length}
      for (i = 0; i < slides.length; i++) {
        slides[i].style.display = "none";
      }
      for (i = 0; i < dots.length; i++) {
        dots[i].className = dots[i].className.replace(" active", "");
      }
      slides[slideIndex-1].style.display = "block";
      dots[slideIndex-1].className += " active";
    }
  </script>


</body>