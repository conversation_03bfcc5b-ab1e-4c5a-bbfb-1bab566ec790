{% comment %} --------------------Cross Sell Products-------------------------------- {% endcomment %}
<style>
  .cross-sell-main-container {
    margin: 20px 0;
    max-width: 100%;
  }
  .cross-sell-product-container {
    display: flex;
    width: 70%;
    background: #F1F1F1;
    border: 1px solid #F1F1F1;
    border-radius: 10px;
    height: auto;
    margin-bottom: 30px;
    gap: 10px;
    padding: 10px;
  }
  .cross-product-image {
    border-radius: 10px;
  }
  .cross-sell-product-title {
    font-size: 16px;
    margin-top: 10px;
    font-weight: 700;
    color: #363636;
  }
  .product-price-container {
    display: flex;
    margin-top: 5px;
    gap: 5px;
    font-size: 16px;
  }
  .product-price {
    color: #5461c8;
  }
  .product-compare-at-price {
    text-decoration: line-through;
    color: #6a696b;
  }
  .cross-sell-product-options-label {
    font-size: 16px !important;
  }
  .color-choosen-value {
    font-size: 14px;
  }
  .cross-sell-product-swatches-selector-wrapper {
    margin-bottom: 0 !important;
  }
  .cross-sell-product-dropdown-selector-wrapper {
    margin-top: 10px;
  }
  .cross-sell-product-selector-container {
    width: 100%;
    padding-right: 10px;
  }
  .cross-sell-product-selector {
    width: 65%;
    height: 25px;
    border-radius: 5px;
    background-color: white;
    font-size: 14px;
  }
  .cross-sell-size-chart-main-container {
    padding-right: 10px;
  }
  .cross-sell-product-size-chart {
    text-decoration: underline;
    cursor: pointer;
    font-size: 16px;
  }
  .cross-sell-product-add-to-cart-button-container {
    margin-top: 20px;
    margin-bottom: 10px;
  }
  .cross-sell-product-add-to-cart-button {
    border-color: #5461c8;
    border-radius: 30px;
    color: white;
    background-color: #5461c8;
    padding: 10px 20px;
    font-weight: bold;
  }
  .selected-swatches {
    color: hsl(0, 0%, 21%);
    display: block;
    font-size: 14px;
    font-weight: 500;
  }
  .cross-sell-one-half {
    max-width: 40%;
  }
  @media (max-width: 480px) {
    .cross-sell-product-container {
      flex-direction: row;
      width: 100%
    }
    .cross-sell-main-container {
      margin: 20px 0;
      width: 100%;
    }
    .cross-sell-one-half {
      max-width: 50%;
    }
    .cross-sell-second-half {
      width: 100%;
    }
    .cross-sell-product-title {
      display: flex;
      justify-content: center;
      color: #363636;
    }
    .product-price-container {
      justify-content: center;
    }
    .cross-sell-product-swatches-selector-wrapper {
      flex-direction: row;
    }
    .cross-sell-product-add-to-cart-button {
      width: 100%;
    }
    .cross-sell-product-selector {
      width: 100%;
    }
    .sizeChartIcon {
      display: none;
    }
  }
</style>
{% assign oms = shop.metafields.cuddleclones.api_details.value %}
<div class="cross-sell-main-container"></div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>

<script>
  const crossSellOptions = {{ product.metafields.cuddleclones.cross_sell_options.value | json }};
  omsDetails = {{ oms | json }};
  let mainUrl = "";
  let productIds = [];
  let primaryProductPets = ''

  const hostUrl = window.location.host;
  if (hostUrl === "cuddleclones-dev.myshopify.com") {
    mainUrl = "https://oms-dev.cuddleclones.com/";
  } else if (hostUrl === "cuddleclones.com") {
    mainUrl = "https://oms.cuddleclones.com/";
  } else {
    mainUrl = "https://oms-uat.cuddleclones.com/";
  }

  const crossSellProducts = crossSellOptions.cross_sell_products;
  crossSellProducts.forEach((cross_sell) => {
    const renderedCustomOptions = cross_sell.custom_options[0].rendered_custom_option;
    const renderedNativeOptions = cross_sell.native_options[0].rendered_native_option;
    const nativeStyleOption = cross_sell.backend_native_option[0].value;
    const productId = cross_sell.product_id;
    const productImage = cross_sell.product_imageUrl;
    const sizeChartUrl = cross_sell.size_chart_url;
    productIds.push(productId);

    if (productId) {
      const getProductDetail = `${mainUrl}shopify_api/get_product?product_id=${productId}`;
      $.ajax({
        url: getProductDetail,
        type: "GET",
        contentType: "application/json",
        headers: {
          "api-token": omsDetails[0].api_token,
        },
        success: function (productResponse) {
          // Nested AJAX to fetch metafields
          const getProductMetafields = `${mainUrl}shopify_api/get_product_metafields?product_id=${productId}`;
          $.ajax({
            url: getProductMetafields,
            type: "GET",
            contentType: "application/json",
            headers: {
              "api-token": omsDetails[0].api_token,
            },
            success: function (metafieldResponse) {
              // Product Variants
              const variants = productResponse.response.product.variants;
              const productHandle = productResponse.response.product.handle;
              let originalUniqueKey = document.getElementById('originalkey').value;

              // Create the form element
              const form = document.createElement('form');
              // Set attributes for the form
              form.setAttribute('method', 'post');
              form.setAttribute('action', '/cart/add');
              form.setAttribute('id', `product_form_${productId}`);
              form.setAttribute('accept-charset', 'UTF-8');
              form.setAttribute('class', 'shopify-product-form');
              form.setAttribute('enctype', 'multipart/form-data');
              // Cross cell products main heading
              if (!$(".cross-sell-main-container .selector-wrapper").length) {
                const mainHeading = document.createElement('div');
                mainHeading.className = 'selector-wrapper';
                const mainHeadingLabel = document.createElement('label');
                mainHeadingLabel.style.fontSize = '18px';
                mainHeadingLabel.style.color = '#363636';
                mainHeadingLabel.textContent = crossSellOptions.cross_sell_products_main_label;
                mainHeading.appendChild(mainHeadingLabel);
                $(".cross-sell-main-container").append(mainHeading);
              }

              // Build the HTML structure with combined data
              const container = document.createElement("div");
              container.className = "cross-sell-product-container";

              // Image section
              const firstHalf = document.createElement("div");
              firstHalf.className = "cross-sell-one-half";
              const imageContainer = document.createElement("div");
              imageContainer.className = "product-image-container";
              const img = document.createElement("img");
              img.className = "cross-product-image";
              img.style = "cursor: pointer;"
              img.src = productImage;
              img.alt = "cross-sell-product";
              img.addEventListener("click", () => {
                window.open(`${productHandle}`, "_blank");
              });
              imageContainer.appendChild(img);
              firstHalf.appendChild(imageContainer);

              // Details section
              const secondHalf = document.createElement("div");
              secondHalf.className = "cross-sell-second-half";
              const title = document.createElement("h1");
              title.className = "cross-sell-product-title";
              title.textContent = productResponse.response.product.title;
              title.style = "cursor: pointer;"
              title.addEventListener("click", () => {
                  window.open(`${productHandle}`, "_blank");
                });
              secondHalf.appendChild(title);

              // Create variant options container
              const variantOptionsContainer = document.createElement('div');
              variantOptionsContainer.className = 'variant-options-container';

              // Create Price Container
              const priceContainer = document.createElement('div');
              priceContainer.className = `price-container-${productId}`
              const priceSpan = document.createElement('span');
              priceSpan.style.marginRight = '10px';
              priceSpan.style.color = "#363636";
              priceSpan.textContent = `$${variants[0].price}`;
              const comparePriceSpan = document.createElement('span');
              comparePriceSpan.className = "product-compare-at-price";
              if (variants[0].compare_at_price !== '0.00') {
                comparePriceSpan.textContent = `$${variants[0].compare_at_price}`;
              }

              priceContainer.appendChild(priceSpan);
              priceContainer.appendChild(comparePriceSpan);
              secondHalf.appendChild(priceContainer);


              getCustomOptions = metafieldResponse.response.metafields.find(item => item.key === "custom_options").value
              customOptionsObject = JSON.parse(getCustomOptions);
              nativeOptions = metafieldResponse.response.metafields.find(item => item.key === "native_options").value
              nativeOptionsObject = JSON.parse(nativeOptions);
              // Add background swatches
              const hasBackground = renderedCustomOptions.some(
                option => option.render_custom_option_key.toLowerCase().includes('background')
              );
              if (hasBackground) {
                const backgroundOption = $.grep(customOptionsObject, function(option) {
                  return option.name === "background"
                });
                const swatchWrapper = document.createElement('div');
                swatchWrapper.className = 'selector-wrapper cross-sell-product-swatches-selector-wrapper';
                const swatchLabel = document.createElement('label');
                swatchLabel.className = 'cross-sell-product-options-label';
                swatchLabel.textContent = backgroundOption[0].heading;
                const selectedBackground = document.createElement('span');
                selectedBackground.id = `cross-sell-${productId}-selected-swatches`;
                selectedBackground.className = "selected-swatches"
                // selectedBackground.textContent = data.selectedBackground;
                const backgroundHiddenInput = document.createElement('input');
                backgroundHiddenInput.type = 'hidden';
                backgroundHiddenInput.name = 'properties[Background]'
                backgroundHiddenInput.className = `background-${productId}`;
                swatchWrapper.appendChild(swatchLabel);
                swatchWrapper.appendChild(backgroundHiddenInput);
                swatchWrapper.appendChild(selectedBackground);
                const swatchContainer = document.createElement('div');
                swatchContainer.className = 'swatches-container';
                backgroundOption[0].options.forEach(swatch => {
                  const label = document.createElement('label');
                  label.style.marginRight = '10px';
                  const input = document.createElement('input');
                  input.type = 'radio';
                  input.name = `background-${productId}`;
                  input.value = swatch.name;
                  const swatchImg = document.createElement('img');
                  swatchImg.src = swatch.image;
                  swatchImg.alt = swatch.name;
                  swatchImg.title = swatch.name;
                  swatchImg.width = 20;
                  swatchImg.height = 20;
                  swatchImg.onclick = () => chooseBackground(swatch.name, productId);
                  label.appendChild(input);
                  label.appendChild(swatchImg);
                  swatchContainer.appendChild(label);
                });
                variantOptionsContainer.appendChild(swatchWrapper);
                variantOptionsContainer.appendChild(swatchContainer);
              }
              // Add size chart and dropdown
              const hasSize = renderedCustomOptions.some(
                option => option.render_custom_option_key.toLowerCase().includes('size')
              );
              if (hasSize) {
                const sizeOption = $.grep(customOptionsObject, function(option) {
                  return option.name === "size"
                });
                const sizeSelectorWrapper = document.createElement('div');
                sizeSelectorWrapper.className = 'selector-wrapper cross-sell-product-dropdown-selector-wrapper';
                sizeSelectorWrapper.style.justifyContent = 'space-between';
                sizeSelectorWrapper.style.flexDirection = 'column';
                sizeSelectorWrapper.style.gap = '0';
                const sizeLabelContainer = document.createElement('div');
                sizeLabelContainer.style.display = 'flex';
                sizeLabelContainer.style.alignItems = 'baseline';
                sizeLabelContainer.style.justifyContent = 'space-between';
                sizeLabelContainer.style.width = '100%';
                const innerDiv = document.createElement('div');
                const sizeLabel = document.createElement('label');
                sizeLabel.className = 'cross-sell-product-options-label';
                sizeLabel.textContent = sizeOption[0].heading;
                const sizeChart = document.createElement('div');
                sizeChart.className = 'cross-sell-size-chart-main-container';
                const header = document.createElement('h4');
                header.className = 'open-new-pajamas-size-chart-header';
                const sizeChartLink = document.createElement('p');
                sizeChartLink.className = 'cross-sell-product-size-chart';
                sizeChartLink.style.color = '#363636';
                sizeChartLink.textContent = 'Size Chart';
                sizeChartLink.onclick = () => sizeChartPopup(productId);
                const span = document.createElement('span');
                span.className = 'icon sizeChartIcon';
                span.setAttribute('data-icon', 'right-caret');
                const svgNamespace = 'http://www.w3.org/2000/svg';
                const svg = document.createElementNS(svgNamespace, 'svg');
                svg.setAttribute('xmlns', svgNamespace);
                svg.setAttribute('viewBox', '0 0 100 100');
                const g = document.createElementNS(svgNamespace, 'g');
                g.setAttribute('id', 'right-caret');
                const path = document.createElementNS(svgNamespace, 'path');
                path.setAttribute('d', 'M74.25,48.58l-45.69-45a2,2,0,1,0-2.81,2.84l9.81,9.66a2,2,0,0,0,.47.78L69.17,50,36,83.14a2,2,0,0,0-.47.78l-9.81,9.66a2,2,0,1,0,2.81,2.84l45.69-45a2,2,0,0,0,0-2.84Z');
                const popupContainer = document.createElement("div");
                popupContainer.style.display = "none";
                popupContainer.className = `popup-chart-container-${productId}`;
                popupContainer.id = "pajamasSizeChartOpen";
                const modalOverlay = document.createElement("div");
                modalOverlay.className = "modal-overlay";
                const modalContent = document.createElement("div");
                modalContent.className = "modal-content";
                const innerHeader = document.createElement("h5");
                innerHeader.className = "pajamas-new-size-chart-open-header";
                innerHeader.textContent = "Size Chart";
                const innerWrap = document.createElement("div");
                innerWrap.className = "new-pajamas-size-chart-open-inner-wrap sizeChartImageContainer";
                const img = document.createElement("img");
                img.className = "pajamas-new-size-chart-img hs-lazyload hs-id-b47016e1";
                img.loading = "lazy";
                img.src = `${sizeChartUrl}`;
                innerWrap.appendChild(img);
                const deleteButton = document.createElement("div");
                deleteButton.className = "delete-button";
                const closeSpan = document.createElement("span");
                closeSpan.textContent = "x";
                closeSpan.onclick = function() {
                  closeSizePopup(productId);
                };
                deleteButton.appendChild(closeSpan);
                modalContent.appendChild(innerHeader);
                modalContent.appendChild(innerWrap);
                modalContent.appendChild(deleteButton);
                popupContainer.appendChild(modalOverlay);
                popupContainer.appendChild(modalContent);
                g.appendChild(path);
                svg.appendChild(g);
                span.appendChild(svg);
                sizeChartLink.appendChild(span);
                header.appendChild(sizeChartLink);
                sizeChart.appendChild(header);
                sizeChart.appendChild(popupContainer);
                const sizeDropdownDiv = document.createElement('div');
                sizeDropdownDiv.className = 'cross-sell-product-selector-container';
                const sizeHiddenInput = document.createElement('input');
                sizeHiddenInput.type = 'hidden';
                sizeHiddenInput.name = 'properties[Size]'
                sizeHiddenInput.className = `size-${productId}`;
                sizeDropdownDiv.appendChild(sizeHiddenInput);
                const sizeDropdown = document.createElement('select');
                sizeDropdown.className = `single-option-selector cross-sell-product-selector size-dropdown-${productId}`;
                sizeDropdown.onchange = () => setSelectedValue(productId, 'size', variants, renderedNativeOptions);
                const defaultSizeOption = document.createElement('option');
                defaultSizeOption.value = 'not-selected';
                defaultSizeOption.textContent = 'Select Your Size';
                // defaultSizeOption.selected = true;
                sizeDropdown.appendChild(defaultSizeOption);
                sizeOption[0].options.forEach(size => {
                  const option = document.createElement('option');
                  option.value = size.value;
                  option.textContent = size.name;
                  sizeDropdown.appendChild(option);
                });
                innerDiv.appendChild(sizeLabel);
                sizeLabelContainer.appendChild(innerDiv);
                sizeLabelContainer.appendChild(sizeChart);
                sizeDropdownDiv.appendChild(sizeDropdown);
                sizeSelectorWrapper.appendChild(sizeLabelContainer);
                sizeSelectorWrapper.appendChild(sizeDropdownDiv);
                variantOptionsContainer.appendChild(sizeSelectorWrapper);
              }
              // Add Style dropdown for Native option
              const hasStyle = renderedNativeOptions.some(
                option => option.render_native_option_key.toLowerCase().includes('style')
              );
              if (hasStyle) {
                const styleOption = $.grep(nativeOptionsObject, function(option) {
                  return option.name === "_option_Style"
                });
                 const styleLabelDiv = document.createElement('div');
                styleLabelDiv.className = "selector-wrapper cross-sell-product-dropdown-selector-wrapper";
                styleLabelDiv.style.justifyContent = 'space-between';
                styleLabelDiv.style.flexDirection = 'column';
                styleLabelDiv.style.gap = '0';
                const styleLabel = document.createElement('label');
                styleLabel.className = 'cross-sell-product-options-label';
                styleLabel.textContent = `${styleOption[0].heading}`;
                styleLabelDiv.appendChild(styleLabel);
                const styleDropdown = document.createElement('select');
                styleDropdown.className = `single-option-selector cross-sell-product-selector style-dropdown-${productId}`;
                styleDropdown.onchange = () => setSelectedValue(productId, 'style', variants, renderedNativeOptions)
                const styleHiddenInput = document.createElement('input');
                styleHiddenInput.type = 'hidden';
                styleHiddenInput.name = 'properties[Style]'
                styleHiddenInput.className = `style-${productId}`;
                styleLabelDiv.appendChild(styleHiddenInput);
                const defaultStyleOption = document.createElement('option');
                defaultStyleOption.value = 'not-selected';
                defaultStyleOption.textContent = 'Select Your Style';
                defaultStyleOption.selected = true;
                styleDropdown.appendChild(defaultStyleOption);
                styleOption[0].options.forEach(size => {
                  const option = document.createElement('option');
                  option.value = size.value;
                  option.textContent = size.name;
                  styleDropdown.appendChild(option);
                });
                styleLabelDiv.appendChild(styleDropdown);
                variantOptionsContainer.appendChild(styleLabelDiv);
              }
              // Add "Add to Cart" button
              const addToCartContainer = document.createElement('div');
              addToCartContainer.className = 'cross-sell-product-add-to-cart-button-container';

              const addToCartButton = document.createElement('button');
              addToCartButton.className = `cross-sell-product-add-to-cart-button ${productId}-add-to-cart`;
              addToCartButton.textContent = 'Add To Cart';
              addToCartButton.style = "cursor: not-allowed; opacity: 0.5; pointer-events: none;"
              addToCartButton.disabled = true;
              addToCartButton.setAttribute('data-add-to-cart-trigger','');
              addToCartButton.onclick = (event) => crossSellAddToCart(event, productId, variants, originalUniqueKey, nativeStyleOption);

              addToCartContainer.appendChild(addToCartButton);
              variantOptionsContainer.appendChild(addToCartContainer);

              secondHalf.appendChild(variantOptionsContainer);

              // Append both halves to the container
              container.appendChild(firstHalf);
              container.appendChild(secondHalf);

              // Append to main container
              form.appendChild(container)
              $(".cross-sell-main-container").append(form);
            },
            error: function (xhr, status, error) {
              console.error("Error fetching metafields:", error);
            },
          });
        },
        error: function (xhr, status, error) {
          console.error("Error fetching product details:", error);
        },
      });
    }
  });

  function chooseBackground(value, productId) {
    const hiddenInput = document.querySelector(`.background-${productId}`);
    const selectedSwatches = document.getElementById(`cross-sell-${productId}-selected-swatches`);
    if (hiddenInput) hiddenInput.value = value;
    if (selectedSwatches) selectedSwatches.textContent = value;
  }

  async function crossSellAddToCart(event, productId, variants, originalUniqueKey, nativeStyleOption) {
    event.preventDefault();
    
    try {
      const hiddenInputs = document.querySelectorAll(`form[id="product_form_${productId}"] input[type="hidden"]`);
      let allFieldsFilled = true;
      let backgroundValue = null;
      let sizeValue = null;
      let styleValue = null;
      for (const input of hiddenInputs) {
        if (!input.value) {
          allFieldsFilled = false;
          const fieldName = input.name.replace('properties[', '').replace(']', '');
          alert(`Please select a value for ${fieldName}`);
          return;
        }
        if (input.name === "properties[Background]") {
          backgroundValue = input.value;
        }
        if (input.name === "properties[Size]") {
          sizeValue = input.value;
        }
        if (input.name === "properties[Style]") {
          styleValue = input.value;
        }
      }
      if (!backgroundValue) {
        alert("Please select a Background value.");
        return;
      }
      if (!sizeValue) {
        alert("Please select a Size value.");
        return;
      }
      const styleOption = $(`.style-${productId}`)
      if (!styleValue && styleOption.length > 0) {
        alert("Please select a Style value.");
        return;
      }
      const cartResponse = await fetch('/cart.js');
      const cart = await cartResponse.json();
      const matchingPrimaryItem = cart.items.find(item => item.properties._original_unique_key === originalUniqueKey);
      if (!matchingPrimaryItem) {
        throw new Error("Primary item not found in cart.");
      }
      const {
        _option_Number_of_Pet_Images: numberOfPetImages,
        _image_url_1: imageUrl1,
        _image_url_2: imageUrl2,
        _image_url_3: imageUrl3
      } = matchingPrimaryItem.properties;
      let variantTitle = '';
      if (nativeStyleOption !== 'None') {
        variantTitle = `${nativeStyleOption} / ${primaryProductPets}`;
      } else if (styleValue == null) {
        variantTitle = `${primaryProductPets}`;
      } else {
        variantTitle = `${styleValue} / ${primaryProductPets}`;
      }
      
      const matchingVariant = variants.find(variant => variant.title === variantTitle);
      if (!matchingVariant) {
        throw new Error("Matching variant not found.");
      }
      
      const combinedItems = {
        quantity: 1,
        id: matchingVariant.id,
        properties: {
          _option_Number_of_Pet_Images: numberOfPetImages,
          _image_url_1: imageUrl1,
          Size: sizeValue,
          Background: backgroundValue,
          _original_unique_key: originalUniqueKey
        }
      };
      if (nativeStyleOption !== 'None') {
        combinedItems.properties._option_Style = nativeStyleOption;
      } else if (styleValue !== null ) {
        combinedItems.properties._option_Style = styleValue;
      }
      if (imageUrl2) combinedItems.properties._image_url_2 = imageUrl2;
      if (imageUrl3) combinedItems.properties._image_url_3 = imageUrl3;
      const shopifyCartUrl = '{{shop.metafields.cuddleclones.store_url}}/cart/add.js';
      
      const response = await fetch(shopifyCartUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(combinedItems)
      });
      if (response.ok) {
        window.PXUTheme.jsAjaxCart.showDrawer();
        window.PXUTheme.jsAjaxCart.updateView();
      } else {
        const errorText = await response.text();
        console.error("Failed to add item to cart. Response:", errorText);
        alert("An error occurred while adding the item to the cart.");
      }
    } catch (error) {
      console.error("An error occurred in the crossSellAddToCart function:", error);
      alert("An error occurred. Please try again.");
    }
  }

  function setSelectedValue(productId, type, variants, renderedNativeOptions) {
    const dropdownValues = document.querySelector(`.${type}-dropdown-${productId}`);
    if (!dropdownValues) return;
    const selectedValue = dropdownValues.value;
    const hiddenInput = document.querySelector(`.${type}-${productId}`);
    if (!hiddenInput) return;
    hiddenInput.value = selectedValue;
    const renderedNativeOption = renderedNativeOptions[0].render_native_option_key
    if (type == 'style' && renderedNativeOption == 'Style') {
      const variantTitle = `${hiddenInput.value} / ${primaryProductPets}`;
      const matchingVariant = variants.find(variant => variant.title === variantTitle);
      const price_container = $(`.price-container-${productId}`);
      var price = matchingVariant.price;
      var compareAtPrice = matchingVariant.compare_at_price
      price_container.empty();
      const priceSpan = document.createElement('span');
      priceSpan.style.marginRight = '10px';
      priceSpan.style.color = "#363636";
      priceSpan.textContent = `$${price}`;
      const comparePriceSpan = document.createElement('span');
      comparePriceSpan.className = "product-compare-at-price";
      const savedAmountContainer = document.createElement('div');
      if (compareAtPrice !== '0.00') {
        comparePriceSpan.textContent = `$${compareAtPrice}`;
        const savedAmountSpan = document.createElement('span');
        var amountSaved = compareAtPrice - price;
        var discountPercentage = Math.floor((amountSaved / compareAtPrice) * 100);
        savedAmountSpan.textContent=`You save: ${discountPercentage}% ($${amountSaved.toFixed(2)})`;
        savedAmountContainer.appendChild(savedAmountSpan);
      }
      price_container.append(priceSpan);
      price_container.append(comparePriceSpan);
      price_container.append(savedAmountContainer);
    }
  }

  $("#add-to-cart-button").on('click', function() {
    setTimeout(async () => {
      try {
        let originalUniqueKey = document.getElementById('originalkey').value;
        const cartResponse = await fetch('/cart.js');
        const cart = await cartResponse.json();
        const matchingPrimaryItem = cart.items.find(item => item.properties._original_unique_key === originalUniqueKey);
        if (!matchingPrimaryItem) {
          console.log("No matching primary item found. Exiting...");
          return;
        }
        primaryProductPets = '';
        if (hostUrl === "cuddleclones-dev.myshopify.com") {
          primaryProductPets = matchingPrimaryItem.properties._option_Number_of_Pet_Images
        } else if (hostUrl === "cuddleclones.com") {
          primaryProductPets = matchingPrimaryItem.properties._option_Number_of_Pets
        }
        console.log("Primary Product Pets", primaryProductPets);
        for (let index = 0; index < productIds.length; index++) {
          const productData = `${mainUrl}shopify_api/get_product?product_id=${productIds[index]}`;
          $.ajax({
            url: productData,
            type: "GET",
            contentType: "application/json",
            headers: {
              "api-token": omsDetails[0].api_token,
            },
            success: function (productResponse) {
              const productVariants = productResponse.response.product.variants
              const productID = productResponse.response.product.id;
              const productIdsStr = productID.toString();
              let renderedNativeOption = '';
              const renderedNativeValue = crossSellOptions.cross_sell_products
                    .filter(product => productIdsStr == product.product_id)
                    .map(product => {
                      renderedNativeOption = product.native_options[0]?.rendered_native_option.map(opt => opt.render_native_option_key);;
                    });
              if (renderedNativeOption.includes('Style')) {
                return;
              }
              let backendOption = '';
              const backendStyleNativeValues = crossSellOptions.cross_sell_products
                    .filter(product => productIdsStr == product.product_id)
                    .map(product => {
                      backendOption = product.backend_native_option.find(option => option.key === "Style").value;
                    });
              let variantTitle = ''
              if (backendOption == 'None') {
                variantTitle = `${primaryProductPets}`;
              } else {
                variantTitle = `${backendOption} / ${primaryProductPets}`;
              }
              const matchingVariant = productVariants.find(variant => variant.title === variantTitle);
              const price_container = $(`.price-container-${productID}`);
              var price = matchingVariant.price;
              var compareAtPrice = matchingVariant.compare_at_price
              price_container.empty();
              const priceSpan = document.createElement('span');
              priceSpan.style.marginRight = '10px';
              priceSpan.style.color = "#363636";
              priceSpan.textContent = `$${price}`;
              const comparePriceSpan = document.createElement('span');
              comparePriceSpan.className = "product-compare-at-price";
              const savedAmountContainer = document.createElement('div');
              if (compareAtPrice !== '0.00') {
                comparePriceSpan.textContent = `$${compareAtPrice}`;
                const savedAmountSpan = document.createElement('span');
                var amountSaved = compareAtPrice - price;
                var discountPercentage = Math.floor((amountSaved / compareAtPrice) * 100);
                savedAmountSpan.textContent=`You save: ${discountPercentage}% ($${amountSaved.toFixed(2)})`;
                savedAmountContainer.appendChild(savedAmountSpan);
              }
              price_container.append(priceSpan);
              price_container.append(comparePriceSpan);
              price_container.append(savedAmountContainer);
            }
          });
        }
        if (matchingPrimaryItem) {
          $('.cross-sell-product-add-to-cart-button').each(function() {
            $(this).prop('disabled', false);
            $(this).css({
              'cursor': 'pointer',
              'opacity': '1',
              'pointer-events': 'auto'
            });
          });
        }
      } catch (error) {
        console.error('An error occurred:', error);
      }
    }, 5000); // 5000 milliseconds = 5 seconds
  });

  function sizeChartPopup(productId) {
    const popupContainer = document.querySelector(`.popup-chart-container-${productId}`);
    // Togle the visibility of the popup container
    if (popupContainer.style.display === 'none') {
      popupContainer.style.display = 'block';
    } else {
      popupContainer.style.display = 'none';
    }
  }

  function closeSizePopup(productId) {
    const popupContainer = document.querySelector(`.popup-chart-container-${productId}`);
    popupContainer.style.display = 'none';
  }
</script>