{% if shop.metafields.cuddleclones.rush_product_pdp != "" %}
  {% assign rush_product_pdp = shop.metafields.cuddleclones.rush_product_pdp.value %}
{% endif %}
<div class="cart-rush">
  <div class="cart-rush--option-pdp">

    <span class="cart-rush--variant-pdp"><input
        type="radio"
        name="cart-rush-variant-pdp"
        value="none"
        {% unless has_rush_added_pdp %}
        checked{% endunless %}><i>
        <!-- fake input -->
      </i>
    </span>
    <label>Ships in 3-4 Weeks</label>
    <span class="cart-rush--price" data-variant-price="0"></span>
  </div>
  {% for variant in rush_product_pdp.variants %}
    {% assign variant_meta = variant.metafields.cuddleclones.highlighted.value %}
    <div
      class="cart-rush--option-pdp {% if forloop.last == true %}last-option{% endif %}"
      {% if variant_meta !='' %}
      style="display:{{variant_meta.display}}; color:{{variant_meta.color}}; font-weight: {{variant_meta.font-weight}}"
      {% endif %}>
      <span class="cart-rush--variant-pdp"><input
          type="radio"
          name="cart-rush-variant-pdp"
          value="{{variant.id}}"
          {% if has_rush_added_pdp and rush_variant_pdp==variant.id %}
          checked{%endif%}><i>
          <!-- fake input --- -->
        </i>
      </span>
      <label>{{ variant.title }}</label>
      <span class="cart-rush--price" data-variant-price="{{variant.price | money_without_currency }}">
        {% if variant.compare_at_price > variant.price %}
          <s>{{ variant.compare_at_price | money }}</s>
        {% endif %}
        {{ variant.price | money }}
      </span>
    </div>
  {% endfor %}
  {% if section.settings.rush_disclaimer != blank %}
    <div class="rush-disclaimer {% if has_rush_added_pdp %}{% else %}hide{% endif %}">{{ shop.metafields.cuddleclones.pdp_rush_disclaimer.value }}</div>
  {% endif %}
</div>