{% assign collection_group = products | map: 'id' %}
{% assign collection_group_thumb = collection_group | append : 'thumb' %}
{% assign collection_group_mobile = collection_group | append : 'mobile' %}

{% if related_products == true %}
  {% assign hover_enabled = block.settings.show_related_products_title_on_hover %}
{% else %}
  {% assign hover_enabled = settings.thumbnail_hover_enabled %}
{% endif %}

{% if related_products == true %}
  {% assign related_items = true %}
{% else %}
  {% assign related_items = false %}
{% endif %}

{% assign for_limit = limit %}

{% comment %} loop through product list {% endcomment %}
{% for product in products limit: limit %}

  {% comment %} if product display is in the loop of products increase limit by one {% endcomment %}
  {% if product.id == skip_product.id  %}
    {% assign for_limit = limit | plus: 1 %}
  {% endif %}
{% endfor %}

{% for product in products limit: for_limit %}

  {% comment %} Skip product if we're on its product page {% endcomment %}
  {% if product.id != skip_product.id %}
    {% render 'product-thumbnail',
            product: product,
            limit: limit,
            products_per_row: products_per_row,
            mobile_products_per_row: mobile_products_per_row,
            align_height: align_height,
            height: height,
            collection_group_thumb: collection_group_thumb,
            collection_group_mobile: collection_group_mobile,
            related_products: related_items
    %}
  {% endif %}
{% endfor %}
