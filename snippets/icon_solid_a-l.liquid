{% comment %}
Required values
name: <icon name>
______________
Optional values
icon_class: <string>
{% endcomment %}

<span class="icon {{ icon_class }}" data-icon="{{ name | downcase }}">

  {% if name == 'avatar' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="42.97" r="18.28"/><path d="M50,1A49,49,0,1,0,99,50,49.05,49.05,0,0,0,50,1ZM79.43,78.5a33.73,33.73,0,0,0-58.87,0A41,41,0,1,1,91,50,40.85,40.85,0,0,1,79.43,78.5Z"/></g></svg>

  {% elsif name == 'avatar-2' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="28.38" r="23.38"/><path d="M86.23,95a36.23,36.23,0,0,0-72.46,0Z"/></g></svg>

  {% elsif name == 'bag' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M79.67,28.66H68.77V22.19a18.77,18.77,0,1,0-37.54,0v6.47H20.33L16.41,96.58H83.59ZM38.39,22.19a11.61,11.61,0,0,1,23.22,0v6.47H38.39Z"/></g></svg>

  {% elsif name == 'basket' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="82.55 31.46 67.48 5.42 60.55 9.43 73.31 31.46 26.69 31.46 39.45 9.43 32.52 5.42 17.45 31.46 1 31.46 1 45 99 45 99 31.46 82.55 31.46"/><polygon points="19.47 94.58 80.53 94.58 89.61 52 10.38 52 19.47 94.58"/></g></svg>

  {% elsif name == 'battery' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M91.9,32.66H87.5V28.48a5.2,5.2,0,0,0-5.2-5.2H10.2A5.2,5.2,0,0,0,5,28.48v43a5.2,5.2,0,0,0,5.2,5.2H82.3a5.2,5.2,0,0,0,5.2-5.2V67.34h4.4a3.1,3.1,0,0,0,3.1-3.1V35.76A3.1,3.1,0,0,0,91.9,32.66ZM25.72,66.13a1.49,1.49,0,0,1-1.5,1.5H15.41a1.48,1.48,0,0,1-1.5-1.51V33.87a1.48,1.48,0,0,1,1.5-1.5h8.81a1.49,1.49,0,0,1,1.5,1.5Zm18.19,0a1.48,1.48,0,0,1-1.5,1.5H33.59a1.48,1.48,0,0,1-1.5-1.5V33.87a1.49,1.49,0,0,1,1.5-1.5h8.82a1.49,1.49,0,0,1,1.5,1.5Zm18.18,0a1.48,1.48,0,0,1-1.5,1.5H51.78a1.49,1.49,0,0,1-1.5-1.51V33.87a1.49,1.49,0,0,1,1.5-1.5h8.81a1.48,1.48,0,0,1,1.5,1.5Zm17.82-32L74.56,66.41a1.42,1.42,0,0,1-1.4,1.22H70a1.53,1.53,0,0,1-1.5-1.41V34a1.48,1.48,0,0,1,1.5-1.5H78.5A1.42,1.42,0,0,1,79.91,34.16Z"/></g></svg>

  {% elsif name == 'bee' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M88.79,56.71,66.86,39.3a9.37,9.37,0,0,0-3.12-1.65l.67-.83a10.45,10.45,0,0,0,2.48-6.6c0-5-3.79-9.37-9.52-11.39h0c2.21-5.35,6-11.76,10.9-11.31A2.47,2.47,0,0,0,71,5.26a2.5,2.5,0,0,0-2.26-2.72c-8.55-.77-13.8,9-16.27,15.19h0a24.09,24.09,0,0,0-5.09.08h0C44.91,11.59,39.65,1.75,31.05,2.54a2.51,2.51,0,0,0-2.26,2.72,2.48,2.48,0,0,0,2.72,2.26c4.92-.42,8.78,6.13,11,11.51h0c-5.45,2.12-8.94,6.38-8.94,11.19A10.46,10.46,0,0,0,36,36.82l.67.82a2.8,2.8,0,0,0-.31.09,9.09,9.09,0,0,0-2.76,1.48l-22.14,17a16.39,16.39,0,0,0-.59,26,16.71,16.71,0,0,0,6.94,3.37,15.88,15.88,0,0,0,3.43.38,16.08,16.08,0,0,0,8.59-2.52,16.86,16.86,0,0,0,2.85-2.26c.3.48.6.94.93,1.41h0l0,.06a.25.25,0,0,0,0,.07h0A59.61,59.61,0,0,0,48.33,97a2.47,2.47,0,0,0,2.9,0A59.61,59.61,0,0,0,65.86,82.72c.38-.56.75-1.12,1.1-1.69a16.88,16.88,0,0,0,2.78,2.35A16,16,0,0,0,81.63,85.9a16.88,16.88,0,0,0,7.06-3.12,16.33,16.33,0,0,0,.1-26.07ZM41.62,64.5l.27-.58,4-8.73h8.4l4.14,9.31ZM31.71,74a10.58,10.58,0,0,1-.77,1.39,12,12,0,0,1-3.77,3.78,10.91,10.91,0,0,1-8.26,1.49,11.47,11.47,0,0,1-4.39-20.49l22.14-17A4.47,4.47,0,0,1,38,42.47a4.53,4.53,0,0,1,1.41-.23,4.67,4.67,0,0,1,1.75.35,4.16,4.16,0,0,1,1.12.67,4.46,4.46,0,0,1,1.58,2.65,4.86,4.86,0,0,1,.07.82,4.41,4.41,0,0,1-.42,1.88l-1.4,3-6.61,14.3,0,0ZM49.78,91.9a59.3,59.3,0,0,1-8.89-8.1H58.67A59.3,59.3,0,0,1,49.78,91.9ZM89.12,74.63a11.63,11.63,0,0,1-3.44,4.16,11.86,11.86,0,0,1-5,2.19,11.08,11.08,0,0,1-8.2-1.75,12.3,12.3,0,0,1-3.75-4,10.52,10.52,0,0,1-.61-1.17L64.59,66,58.27,51.72s0,0,0,0v0l-1.39-3.12A4.32,4.32,0,0,1,56.53,46a4.43,4.43,0,0,1,1.74-2.87,2.9,2.9,0,0,1,.41-.27.51.51,0,0,1,.16-.09,3.81,3.81,0,0,1,.5-.23l0,0A4.2,4.2,0,0,1,61,42.23a3.59,3.59,0,0,1,.84.09,4.44,4.44,0,0,1,2,.89L85.68,60.63a11.87,11.87,0,0,1,4.53,9.25A10.72,10.72,0,0,1,89.12,74.63Z"/></g></svg>

  {% elsif name == 'behance' %}
    <svg width="100" height="63" viewBox="0 0 100 63" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M90.2843 8.80849V2.57701H65.2362V8.80849H90.2843Z" fill="currentColor"/>
      <path d="M41.3877 28.8803C43.1899 28.0338 44.8001 26.828 46.1196 25.3369C48.1401 22.727 49.1606 19.4806 48.9966 16.1841C49.0917 12.7884 48.095 9.45179 46.153 6.66469C42.9761 2.34005 37.5888 0.118483 29.9911 0H0V62.2038H28.0139C30.9639 62.2252 33.9082 61.9423 36.8002 61.3596C39.1619 60.8971 41.3996 59.9419 43.3671 58.5561C45.3347 57.1704 46.988 55.3854 48.219 53.3175C50.0754 50.358 51.036 46.9247 50.9849 43.4315C51.0835 40.2192 50.2377 37.0485 48.5522 34.312C46.8401 31.7499 44.317 29.837 41.3877 28.8803ZM14.3402 10.7968H25.9256C28.4279 10.7255 30.9255 11.0555 33.3234 11.7743C35.545 12.6962 36.6558 14.6179 36.6558 17.517C36.6558 20.1274 35.8116 21.9601 34.1232 22.9821C32.1164 24.0955 29.84 24.63 27.5474 24.5261H14.3402V10.7968ZM33.79 50.3073C31.7977 51.1358 29.6473 51.515 27.4918 51.4181H14.3402V34.823H27.6696C29.7933 34.746 31.9104 35.1051 33.8899 35.8782C36.5336 37.0779 37.8554 39.3106 37.8554 42.5429C37.8776 46.3751 36.5114 48.9521 33.79 50.3073Z" fill="currentColor"/>
      <path d="M99.5704 32.4237C99.104 29.1197 97.8882 25.9662 96.0159 23.2042C94.1332 20.3322 91.4652 18.0614 88.3293 16.6617C85.0248 15.2325 81.4546 14.5207 77.8546 14.5735C71.3232 14.5735 66.01 16.6358 61.9149 20.7605C57.8198 24.8852 55.776 30.8094 55.7834 38.533C55.7834 46.775 58.0457 52.7251 62.5703 56.3833C66.9967 59.995 72.5531 61.9337 78.2656 61.8594C85.4561 61.8594 91.0433 59.6934 95.0273 55.3613C97.6043 52.6288 99.0373 49.9518 99.3483 47.3082H87.4518C86.6614 48.9581 85.3885 50.3291 83.8016 51.2395C82.2148 52.15 80.3888 52.557 78.5656 52.4067C76.3219 52.4498 74.1129 51.8484 72.2008 50.6739C69.2387 48.8744 67.6836 45.7383 67.5355 41.2655H99.9703C100.065 38.3124 99.9318 35.3562 99.5704 32.4237ZM87.8739 33.5345H67.7576C68.037 30.9637 69.1396 28.552 70.9012 26.6588C71.8221 25.7774 72.9149 25.0957 74.1114 24.6561C75.3079 24.2165 76.5822 24.0285 77.8546 24.104C80.3733 24.0245 82.8316 24.8836 84.7526 26.5144C85.7248 27.414 86.5031 28.5026 87.0398 29.7135C87.5765 30.9245 87.8603 32.2322 87.8739 33.5567V33.5345Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'bicep' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M85.21,31.36a21,21,0,0,0-18.32-2.08A20.67,20.67,0,0,0,54.08,42.16a26.32,26.32,0,0,0-1.59,11.4c-4.16-.49-11.05-.54-16.76,3.15L29,33.75l.18-.11.35.4a5.52,5.52,0,0,0,7.86.43L48.62,24.09a5.52,5.52,0,0,0,1.82-3.94,5.59,5.59,0,0,0-1.6-4L37.63,4.81a7.51,7.51,0,0,0-5.35-2.19A7.32,7.32,0,0,0,27,5L11.17,21.72A20.71,20.71,0,0,0,5.59,35.54L5,77.76A19.37,19.37,0,0,0,24.34,97.38,19.45,19.45,0,0,0,28.12,97C49.54,92.82,78,83.27,91.82,61.61a2.55,2.55,0,0,0,.24-.5l1.76-5A21,21,0,0,0,85.21,31.36Z"/></g></svg>

  {% elsif name == 'bicycle' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M78.64,41a20.83,20.83,0,0,0-4.14.42L64.84,18.52H54.09v7.74H59.7l3.37,8H39.77V26.7H25.45v7.74H30l.78,2.73L27.9,42.12a20.22,20.22,0,1,0,13.32,23h9.09l1-2L63.49,42h2.84l1,2.47A20.23,20.23,0,1,0,78.64,41ZM36.9,42H53.57l-8,15.39H41.22A20.22,20.22,0,0,0,34.6,46ZM30.63,52.9a12.27,12.27,0,0,1,2.6,4.48H28.05ZM21.36,73.74a12.49,12.49,0,0,1,0-25,12.8,12.8,0,0,1,2.57.26l-9.26,16.1H33.23A12.5,12.5,0,0,1,21.36,73.74Zm57.28,0a12.47,12.47,0,0,1-8.17-21.92l4.6,10.93,7.13-3L77.6,48.81c.34,0,.69-.05,1-.05a12.49,12.49,0,0,1,0,25Z"/></g></svg>

  {% elsif name == 'binoculars' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><title>binoculars</title><g><path d="M19,47.33a18,18,0,1,0,18,18v-.06A18,18,0,0,0,19,47.33Z"/><path d="M81,47.33a18,18,0,1,0,18,18v-.06A18,18,0,0,0,81,47.33Z"/><path d="M35.88,49.6l.88-25.21v-.58a7.18,7.18,0,0,0-12.92-4.33l0,.11L22.6,21.94a1,1,0,0,1-.11.23L11.93,43.37a23.08,23.08,0,0,1,24,6.23Z"/><path d="M81,42.26a23.23,23.23,0,0,1,7.08,1.11L77.49,22.17a1,1,0,0,1-.11-.23l-.79-1.56L76,19.24a7.18,7.18,0,0,0-12.73,4.57v.69l.87,25.1A23.22,23.22,0,0,1,81,42.26Z"/><path d="M50,30.7a12.14,12.14,0,0,0-6.77,2.07l-.76,22.31a19,19,0,0,1,15.07,0l-.76-22.3A12.17,12.17,0,0,0,50,30.7Z"/></g></svg>

  {% elsif name == 'bolt' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M76.69,47,33,94.55a1.29,1.29,0,0,1-2.21-1.21L40.59,59H24.32a1.22,1.22,0,0,1-.88-.32,1.28,1.28,0,0,1-.13-1.81L65.72,5.46A1.34,1.34,0,0,1,66.44,5,1.3,1.3,0,0,1,67,5,1.28,1.28,0,0,1,68,6.6L59.34,44.87H75.72a1.24,1.24,0,0,1,.84.32A1.28,1.28,0,0,1,76.69,47Z"/></g></svg>

  {% elsif name == 'bookmark' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="18.33 95 18.33 5 81.67 5 81.67 95 50.56 73.89 18.33 95"/></g></svg>

  {% elsif name == 'box' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="7.5 73.52 47.5 91.17 47.5 46.34 7.5 28.68 7.5 73.52"/><path d="M75.84,36V47.35a2.5,2.5,0,0,1-5,0V38.24L52.5,46.34V91.17l18.34-8.09V71.18a2.5,2.5,0,0,1,5,0v9.69L92.5,73.52V28.68Z"/><polygon points="28.89 17.04 11.19 24.85 50 41.97 67.2 34.38 28.89 17.04"/><polygon points="88.81 24.85 50 7.73 35.05 14.33 73.35 31.68 88.81 24.85"/></g></svg>

  {% elsif name == 'briefcase' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="65.11 74.42 34.89 74.42 34.89 60.73 6.13 60.73 6.13 94.67 93.87 94.67 93.87 60.73 65.11 60.73 65.11 74.42"/><rect x="40.89" y="60.92" width="18.22" height="7.49"/><path d="M77.68,26.88V16A10.68,10.68,0,0,0,67,5.33H33A10.68,10.68,0,0,0,22.32,16V26.88H6.13V54.73H93.87V26.88Zm-8.36-2.46H30.68V16A2.33,2.33,0,0,1,33,13.68H67A2.33,2.33,0,0,1,69.32,16Z"/></g></svg>

  {% elsif name == 'bullet' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="50" r="17"/></g></svg>

  {% elsif name == 'bunny' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M16,32.94a42.65,42.65,0,0,1-5.62,7.46c-.88.87-.59,1.49-.3,1.81a1.45,1.45,0,0,1,.37.9,3.39,3.39,0,0,1-.05,1.21c-.19.56,1.12,1.87,2.8,3.74a23.54,23.54,0,0,0,4.48,3.46,1.59,1.59,0,0,1,.74,1.35v9.91a1.6,1.6,0,0,0,.19.74l1.49,2.83c1.45,2.72,7.43,8.33,9.09,9.87a1.55,1.55,0,0,1,.5,1l1.23,10.4a1.59,1.59,0,0,1-1.44,1.76c-1.3.11-3,.46-3.78,1.44-.88,1.13-.15,2.48.42,3.23a1.6,1.6,0,0,0,1.2.63c1.85.08,6.91.19,8.28-.88C37.24,92.48,40.22,81.65,40,83s9.51,6.34,9.51,6.34c-.74-1.49-8.86,0-8.86,0-1.35.12-3.13,2.45-2.85,3.81a1.62,1.62,0,0,0,1.53,1.27c4.69.09,21.72.4,26.05,0,5-.5,8.21-3.23,8.4-2.48s7.84,3.54,11.57,2,4.67-4.48,4.85-7.28-5-3.92-5-4.1a46.46,46.46,0,0,0,2.06-11.2c.56-7.28-2.62-12.7-4.86-16.43S70.08,43.58,62.62,43c-6-.45-15.49,1.86-19.23,1.86-2.58,0-2.84-3.12-2.75-5.05a1.58,1.58,0,0,1,1.54-1.51C54.47,37.93,60.42,34,63,31.37a1.58,1.58,0,0,0-.94-2.69c-4.73-.57-14.07-1.62-16.28-1.34s-6.28,2.3-8.23,3.32a1.59,1.59,0,0,1-1.94-.36h0a1.59,1.59,0,0,1,0-2.12c2.19-2.37,7.32-8.51,7.9-14.66a18.35,18.35,0,0,0-.36-7.11,1.58,1.58,0,0,0-2.5-.77c-3,2.4-10.13,8.26-11.88,11.62a38,38,0,0,0-3,9.93,1.6,1.6,0,0,1-1,1.21C22.54,29.24,16.56,31.58,16,32.94Z"/></g></svg>

  {% elsif name == 'calculator' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M52,52V90H90V52ZM80,78.5H62v-5H80Zm0-10H62v-5H80Z"/><path d="M10,52V90H48V52ZM38.55,77,35,80.55l-6-6-6,6L19.45,77l6-6-6-6L23,61.45l6,6,6-6L38.55,65l-6,6Z"/><path d="M52,10V48H90V10ZM80,31.5H62v-5H80Z"/><path d="M10,10V48H48V10ZM38,31.5H31.5V38h-5V31.5H20v-5h6.5V20h5v6.5H38Z"/></g></svg>

  {% elsif name == 'calendar' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M7.5,90.21A4.75,4.75,0,0,0,12.24,95H87.76a4.75,4.75,0,0,0,4.74-4.74V30.35H7.5Z"/><path d="M87.76,16h-11V5.05h-8V16H31.2V5.05h-8V16h-11A4.75,4.75,0,0,0,7.5,20.69v4.66h85V20.69A4.75,4.75,0,0,0,87.76,16Z"/></g></svg>

  {% elsif name == 'cart' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="45.55" cy="88.68" r="6.49"/><circle cx="74.82" cy="88.68" r="6.49"/><path d="M96.79,22.81a8.45,8.45,0,0,0-8.25-10.3H27.1L25.54,4.84H3v8H19L32,76.75H91.12v-8H38.51l-1.36-6.69H88Z"/></g></svg>

  {% elsif name == 'chat' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M85.78,29.3H69.3V43a16,16,0,0,1-16,16H36.4l-.7.58v.88a11,11,0,0,0,11,11h18.7L85.28,88.2V71.53h.5a11,11,0,0,0,11-11V40.34A11,11,0,0,0,85.78,29.3Z"/><path d="M64.3,43V22.84a11,11,0,0,0-11-11h-39a11,11,0,0,0-11,11V43a11,11,0,0,0,11,11h.5V70.7L34.57,54H53.28A11,11,0,0,0,64.3,43Zm-50-18.14h40v5h-40Zm0,17.23v-5h40v5Z"/></g></svg>

  {% elsif name == 'checkmark' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="92.17 15.03 30.71 76.49 7.83 53.6 2.17 59.26 30.71 87.8 97.83 20.69 92.17 15.03"/></g></svg>

  {% elsif name == 'circle' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="50" r="45"/></g></svg>

  {% elsif name == 'clock' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,5A45,45,0,1,0,95,50,45,45,0,0,0,50,5ZM73.75,54H46V15h8V46H73.75Z"/></g></svg>

  {% elsif name == 'cloud' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95,59A16.62,16.62,0,0,1,78.38,75.62H21.62a16.62,16.62,0,0,1,0-33.24,16.12,16.12,0,0,1,3.64.41,24.92,24.92,0,0,1,48.2.34A16.61,16.61,0,0,1,95,59Z"/></g></svg>

  {% elsif name == 'clover' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M75.2,41.43c-2.64.7-5.55,2-8.55,3.16-2.78,1-6.2,3.25-8.15,1.75,1.25-2.75,4.69-3.15,7-4.13,2.69-1.17,5.29-2.51,7.7-3.78C81,34.25,88,20,77.13,14.37c-1.92-1-5.16-.78-7.77-1.52-5.65-1.6-8.17-8-15.19-6.31C47.77,8.05,42.49,14.8,42.54,21.8c0,3,1.42,6.43,2.37,9.8.82,2.95,2.55,6.51,1.22,9.27-4.65-4.4-5.37-12.16-9.39-16.93C33.43,20,26.35,17.36,21,18.76c-9.71,2.56-7,10.39-10.76,15.9-1.36,2-3.69,3.73-4.66,6.1-2,5,1.22,11.07,5,13.53,6,3.94,13.13,2.73,21.7.15,2.15-.64,5.33-2.93,7.79-1C38.42,55.8,35.35,56.15,33,57a90.49,90.49,0,0,0-8.56,3.16C17.64,63.54,12.1,72.49,16,79.93c2.64,5,8.91,4.48,12.23,6.77,4.64,3.23,6.61,7.74,13.51,6,10.4-2.63,11.19-14.12,9.7-23-.29-1.71-1.34-4.54-.63-6.25,1.91,11.67,10.4,29.23,32.82,30.31h.19a4,4,0,0,0,.19-8C64.87,84.81,60,69,58.78,62.32c.69,1,1.17,2.16,1.73,3.05C64.29,71.44,68,76.06,73.57,77.81c6.6,2.06,14.55-2,15.85-8.06.43-2-.22-4.28.45-6.44.94-3,3.81-4.59,4.68-7.68C97.7,44.47,83.46,39.27,75.2,41.43Z"/></g></svg>

  {% elsif name == 'clubhouse' %}

    <svg width="100" height="78" viewBox="0 0 100 78" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M30.3416 28.8643C29.6763 26.708 29.7502 25.0721 30.5634 24.0311C31.3765 23.0645 32.4854 22.6183 33.8161 22.544C36.1078 22.4696 36.9949 24.1055 38.3256 28.3438C39.1388 31.0207 40.7651 34.894 41.7262 37.0504C42.9829 39.6529 44.7571 43.1477 45.5703 44.3442C46.2357 45.3852 46.8271 45.6826 47.4924 45.6826C48.6752 45.6826 49.5623 45.0134 49.5623 43.8237C49.5623 43.2214 49.0234 42.2577 48.6462 41.5831L48.5274 41.3699C48.3935 41.1155 48.2296 40.8098 48.0455 40.4664C47.3146 39.1032 46.264 37.1437 45.4964 35.4213C44.4614 33.0419 43.2786 30.0676 42.3176 27.3839C41.7262 25.8224 41.0608 23.5917 40.3955 21.361C39.3605 17.9406 38.8431 15.8586 38.8431 14.2903C38.8431 11.9109 40.6173 10.3493 43.2786 10.3493C45.2007 10.3493 46.4574 11.316 47.2706 14.8108C47.936 18.0082 49.1188 23.2199 50.8191 27.3096C51.928 29.9864 53.5543 33.5624 54.5154 35.3469C54.7086 35.687 54.9097 36.0112 55.0929 36.3066C55.4705 36.9153 55.7721 37.4017 55.7721 37.652C55.7721 37.9829 55.3837 38.434 54.8918 39.0053C54.61 39.3327 54.2941 39.6995 53.9979 40.1058C53.4804 40.775 53.2586 41.2212 53.2586 41.7417C53.2586 42.1878 53.5543 42.6339 53.9979 43.2288C54.4414 43.8237 54.885 44.4185 55.4025 44.4185C55.7721 44.4185 55.9939 44.2698 56.2157 43.9724C58.5074 41.0724 61.0209 39.0648 63.9107 37.4222C67.3113 35.4889 70.7925 34.5222 73.5278 33.9274C74.8585 33.63 75.2281 33.3325 75.2281 32.589C75.2281 31.6967 74.5628 31.1762 73.6756 31.1018C73.132 31.041 72.6377 31.0796 71.9105 31.1363C71.7479 31.1489 71.573 31.1626 71.3839 31.1762C70.6447 31.2505 70.275 30.8787 69.8315 29.9121C69.6247 29.473 69.3875 28.987 69.1286 28.4564C67.7216 25.5731 65.6721 21.3733 64.361 16.2236C63.6956 13.6211 63.1042 10.8699 62.7346 7.29394C62.5128 5.65808 62.8085 5.06322 63.6217 4.31965C64.6567 3.42736 66.5048 3.05557 67.9094 3.42736C69.6097 3.8735 70.4968 5.21193 71.3839 10.4237C71.8275 12.8775 72.5667 16.0005 73.4539 18.6841C74.5628 22.1046 75.9673 24.9369 78.1112 28.7291C79.294 30.8111 80.7725 32.9675 82.325 35.0563C82.0293 35.9485 81.5118 36.5434 79.8115 37.9562C78.1112 39.369 76.4109 40.8561 74.8585 43.6074C73.7496 45.615 73.2321 47.8457 73.2321 49.3329C73.2321 50.7457 73.5278 51.0431 74.4149 51.0431C75.9673 51.0431 77.2241 50.7457 77.298 50.0764C77.6676 47.3996 78.1112 45.6894 79.5897 43.6749C80.4768 42.5596 82.0293 41.1468 83.286 40.0314C85.6516 38.0982 86.4648 36.9828 87.278 34.2316C87.6476 32.9675 88.0912 31.7778 88.6826 30.6624C90.0872 28.0599 92.6007 25.0856 96.5255 25.0856C97.7822 25.0856 98.8172 25.4574 99.4825 26.4984C99.8521 27.0933 100 27.9112 100 28.5061C100 29.7806 99.1285 31.5272 98.6623 32.4615L98.5954 32.5957L98.3406 33.1413C96.7061 36.636 94.3077 41.7638 94.3077 47.6227C94.3077 61.0881 89.5764 67.787 85.8734 71.2074C82.251 74.6279 76.552 77.6089 69.8248 77.6089C65.0196 77.6089 60.0598 76.1961 55.9939 73.5193C50.4168 69.8038 46.8912 64.0118 43.3438 58.1842L43.1241 57.8231C40.2409 52.9899 38.2449 49.1909 35.1333 41.8295C33.3793 37.5641 31.7462 33.4001 30.3416 28.8643ZM54.0854 0C51.9415 0 49.3541 1.11536 49.3541 3.94094C49.3541 5.79987 49.8716 8.55109 50.389 10.7751C51.0321 12.3432 51.2867 13.4779 51.8006 15.7682C51.9956 16.6372 52.2279 17.6726 52.5329 18.9611C53.1982 21.7191 54.0114 24.0985 54.8246 26.1805C55.7856 28.7155 56.8945 30.8718 58.447 33.7718C59.1862 35.1845 59.5559 35.1845 61.3301 34.2179C62.7347 33.4743 64.8046 32.582 66.3571 32.0615C63.6957 26.2617 61.4779 21.4217 60.3691 17.332C60.0734 16.1423 59.0384 11.3767 58.7427 9.29467C58.5209 7.21266 58.2991 5.42809 57.7816 3.49479C57.1902 1.11536 56.3771 0 54.0854 0Z" fill="currentColor"/>
      <path d="M22.8751 53.7469C21.3092 54.7203 13.0094 59.0939 11.1343 59.7901C9.79694 60.2904 8.34531 60.1687 7.4918 57.627C6.44339 54.4837 7.91519 53.7402 10.012 52.9222C11.8669 52.1989 19.5619 49.4004 21.3495 48.9137C22.6063 48.5757 23.48 49.0016 24.0848 50.5225C24.7501 52.1922 24.2931 52.8614 22.8751 53.7469Z" fill="currentColor"/>
      <path d="M16.0336 36.0296C13.8024 36.0904 4.53476 35.6916 2.41779 35.4821C0.576361 35.2995 -0.169619 34.4816 0.031997 31.798C0.247054 28.9183 1.36266 28.7426 3.17049 28.9183C5.2673 29.1211 14.9785 30.7097 16.5242 31.1896C18.1237 31.6898 18.3455 32.4199 18.1506 33.8259C17.9019 35.6511 17.082 35.9958 16.0336 36.0296Z" fill="currentColor"/>
      <path d="M17.1828 17.1564C14.629 16.3452 7.04824 11.7283 5.0724 10.5386C3.28474 9.457 3.05624 8.48359 4.29954 6.11091C5.40171 4.01539 6.62485 3.65036 8.43939 4.59673C10.5429 5.69857 17.9691 11.512 19.3872 12.6611C20.7581 13.7697 20.57 14.3443 19.8509 15.7706C19.1318 17.1969 18.4597 17.562 17.1828 17.1564Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'discord' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_47_83)"><path d="M84.6542 18.7921C78.2806 15.8798 71.4458 13.7341 64.2996 12.5052C64.1694 12.4814 64.0394 12.5407 63.9724 12.6593C63.0934 14.2162 62.1197 16.2473 61.4379 17.8437C53.7517 16.6978 46.1049 16.6978 38.5763 17.8437C37.8944 16.2118 36.8854 14.2162 36.0024 12.6593C35.9354 12.5447 35.8054 12.4854 35.6752 12.5052C28.533 13.7302 21.6982 15.8758 15.3206 18.7921C15.2654 18.8158 15.218 18.8554 15.1866 18.9067C2.22246 38.1944 -1.32896 57.008 0.413251 75.5884C0.421134 75.6792 0.472374 75.7662 0.543325 75.8215C9.0967 82.0768 17.3821 85.8743 25.5137 88.3914C25.6438 88.431 25.7817 88.3836 25.8645 88.2768C27.788 85.661 29.5027 82.9028 30.9728 80.0022C31.0596 79.8323 30.9768 79.6308 30.7994 79.5636C28.0797 78.5362 25.49 77.2835 22.9989 75.861C22.8018 75.7464 22.7861 75.4658 22.9673 75.3314C23.4916 74.9402 24.0159 74.5332 24.5165 74.1222C24.607 74.0472 24.7332 74.0313 24.8397 74.0787C41.2054 81.5197 58.9231 81.5197 75.0956 74.0787C75.2021 74.0274 75.3283 74.0433 75.4228 74.1183C75.9235 74.5293 76.4477 74.9402 76.9759 75.3314C77.1572 75.4658 77.1454 75.7464 76.9483 75.861C74.4572 77.3112 71.8675 78.5362 69.1438 79.5597C68.9665 79.6269 68.8876 79.8323 68.9744 80.0022C70.4761 82.8987 72.1907 85.6569 74.0787 88.2729C74.1576 88.3836 74.2994 88.431 74.4296 88.3914C82.6006 85.8743 90.8859 82.0768 99.4393 75.8215C99.5142 75.7662 99.5615 75.6832 99.5694 75.5923C101.655 54.1114 96.077 35.452 84.7842 18.9106C84.7566 18.8554 84.7094 18.8158 84.6542 18.7921ZM33.4168 64.2749C28.4896 64.2749 24.4297 59.7701 24.4297 54.2379C24.4297 48.7056 28.4108 44.2009 33.4168 44.2009C38.462 44.2009 42.4825 48.7452 42.4037 54.2379C42.4037 59.7701 38.4225 64.2749 33.4168 64.2749ZM66.6448 64.2749C61.7177 64.2749 57.6579 59.7701 57.6579 54.2379C57.6579 48.7056 61.6389 44.2009 66.6448 44.2009C71.6901 44.2009 75.7106 48.7452 75.6318 54.2379C75.6318 59.7701 71.6901 64.2749 66.6448 64.2749Z" fill="currentColor"/></g><defs><clipPath><rect width="100" height="100" fill="currentColor"/></clipPath></defs></svg>


  {% elsif name == 'document' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="59.3 34.06 59.3 7.5 14.14 7.5 14.14 92.5 85.86 92.5 85.86 34.06 59.3 34.06"/><polygon points="64.3 10.52 64.3 29.06 82.42 29.06 64.3 10.52"/></g></svg>

  {% elsif name == 'document-2' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M59.3,34.06V7.5H14.14v85H85.86V34.06ZM22.44,57.58H70.18v5H22.44Zm0,10.82H62.8v5H22.44ZM71.66,84.22H22.44v-5H71.66Zm4.42-32.46H22.44v-5H76.08Z"/><polygon points="64.3 10.52 64.3 29.06 82.42 29.06 64.3 10.52"/></g></svg>

  {% elsif name == 'dollar' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M72.92,63.05a17.11,17.11,0,0,0-8.68-12.74A30.06,30.06,0,0,0,54,47V23.68a16.9,16.9,0,0,1,10.12,6.9L71,26.49a25,25,0,0,0-17-11V7.07H46v8.41c-.7.09-1.42.21-2.15.36s-14.74,2.9-16.76,16.6A16.93,16.93,0,0,0,31.45,46.3,24.57,24.57,0,0,0,46,54.44v22c-2.62-.65-6.55-2.34-10.9-6.69l-5.65,5.66C35.89,81.82,42.06,83.9,46,84.53v8.4h8V84.15C59.91,83,64.66,80.7,68.13,77.27A16.66,16.66,0,0,0,72.92,63.05ZM37.37,40.93A9,9,0,0,1,35,33.6c1.17-8,10-9.83,10.38-9.91l.61-.11V46.29A17.25,17.25,0,0,1,37.37,40.93ZM62.5,71.59A18.09,18.09,0,0,1,54,76V55.12a21.13,21.13,0,0,1,6.34,2.17A9.21,9.21,0,0,1,65,64.13,8.74,8.74,0,0,1,62.5,71.59Z"/></g></svg>

  {% elsif name == 'down-arrow' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="77.2 51.82 54 78.32 54 4.96 46 4.96 46 78.32 22.8 51.82 16.78 57.09 50 95.04 83.22 57.09 77.2 51.82"/></g></svg>

  {% elsif name == 'down-caret' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="50 77.1 2.15 28.51 7.85 22.9 50 65.7 92.15 22.9 97.85 28.51 50 77.1"/></g></svg>

  {% elsif name == 'dribbble' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1.10429 39.5005C4.41773 23.9959 14.9193 11.1548 28.9529 4.63308C31.4706 8.11039 39.5974 19.7203 47.2499 33.2808C25.4837 39.0806 6.04017 39.4756 1.10429 39.5005Z" fill="currentColor"/>
      <path d="M12.6214 83.2092C4.7696 74.3782 0 62.7461 0 50C0 49.4854 0.00777525 48.9726 0.0232147 48.4617C5.37526 48.5075 28.0864 48.1894 51.3949 41.1868C52.8845 44.0381 54.2445 46.9542 55.5398 49.8704C55.2152 49.9679 54.8744 50.0653 54.5336 50.1627C54.1947 50.2596 53.8549 50.3567 53.5321 50.4536C29.3769 58.2522 15.606 78.4647 12.6214 83.2092Z" fill="currentColor"/>
      <path d="M69.8066 95.9251C63.7337 98.5471 57.0378 100 50.0025 100C38.3816 100 27.6867 96.036 19.1962 89.3864C21.5585 85.0356 32.4715 67.5486 58.7781 58.3596C58.8428 58.3273 58.8913 58.3111 58.9398 58.2949C58.9885 58.2786 59.0371 58.2624 59.102 58.2299C65.8312 75.6392 68.8151 90.4365 69.8066 95.9251Z" fill="currentColor"/>
      <path d="M99.3963 57.8245C97.2208 71.6668 89.3655 83.6236 78.2907 91.235C77.4686 86.6369 74.6734 72.7103 68.5577 55.8971C82.936 53.6255 95.6922 56.7461 99.3963 57.8245Z" fill="currentColor"/>
      <path d="M88.8498 18.5167C95.6863 26.941 99.8374 37.6326 100 49.2884C95.8057 48.5015 80.7116 45.9462 65.5137 47.8614C65.2869 47.3698 65.0822 46.856 64.8737 46.333C64.725 45.96 64.5745 45.5823 64.4128 45.2045C63.3765 42.7421 62.2106 40.2147 61.0449 37.817C77.7845 31.0018 86.3956 21.5098 88.8498 18.5167Z" fill="currentColor"/>
      <path d="M83.2353 12.6403C80.9809 15.4995 73.0982 24.2301 57.0943 30.2351C49.5904 16.4409 41.3552 4.89466 38.7233 1.2773C42.3482 0.441613 46.1238 0 50.0025 0C62.7598 0 74.4012 4.77727 83.2353 12.6403Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'droplet' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,5.75s-18.7,48.4-21.9,57.4A22.41,22.41,0,0,0,26.8,71a23.2,23.2,0,1,0,46.4.1,25,25,0,0,0-1.3-7.8C68.7,54.15,50,5.75,50,5.75Z"/></g></svg>

  {% elsif name == 'email' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M52.79,62.87a3,3,0,0,1-2.22.88,3,3,0,0,1-2.09-.89L8,22.38V81.86H92V22.53Z"/><polygon points="87.89 18.14 12.24 18.14 50.6 56.51 87.89 18.14"/></g></svg>

  {% elsif name == 'euro' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M77.59,66.25A27,27,0,0,1,31.38,61H58.83V53H29.2a25.09,25.09,0,0,1,0-6H58.83V39H31.38a27,27,0,0,1,45.31-6.37l6.12-5.16A35,35,0,0,0,22.81,39H16v8h5.14c-.08,1-.14,2-.14,3s.06,2,.14,3H16v8h6.79A35,35,0,0,0,84,71.07Z"/></g></svg>

  {% elsif name == 'eye' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M5,49.81v.38s43.85,58.85,90,0v-.38C48.85-9,5,49.81,5,49.81ZM50,63.7A13.7,13.7,0,1,1,63.7,50,13.7,13.7,0,0,1,50,63.7Z"/><path d="M50,41.29V58.71a8.71,8.71,0,0,0,0-17.42Z"/></g></svg>

  {% elsif name == 'facebook' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M57,27.73V38H72.83l-2.09,16H57V95H40.45V53.94H26.62V38H40.45V26.15C40.45,12.46,48.83,5,61,5h0a115.36,115.36,0,0,1,12.34.63V19.94H64.92C58.26,19.94,57,23.1,57,27.73Z"/></g></svg>

  {% elsif name == 'film' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95,33.15V66.88a2.89,2.89,0,0,1-2.9,2.91,2.76,2.76,0,0,1-1-.18L74,63.46V76.7a2.91,2.91,0,0,1-2.9,2.9H7.9A2.92,2.92,0,0,1,5,76.7V23.3a2.92,2.92,0,0,1,2.9-2.9H71.05A2.91,2.91,0,0,1,74,23.3V36.57l17.16-6.15A2.89,2.89,0,0,1,95,33.15Z"/></g></svg>

  {% elsif name == 'fire' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M73.58,43.35a30.63,30.63,0,0,0-3.09,8.87S71,47.47,67.91,39,62,27.68,60.38,21.49C58.11,13,63,5,63,5s-19.28,3.71-28,20.52c-7.84,14.94-4.64,23.91-4.64,23.91a22.54,22.54,0,0,1-5-7.32A20.4,20.4,0,0,1,24,34S10.28,49.12,16.88,68.2C21.52,81.6,30.18,90.46,40.59,94A16.83,16.83,0,0,1,32,80c-.31-11.24,7.42-16.7,12.06-24.13,6.6-10.72,4.75-17.52,4.75-17.52s5.46,3.09,8.66,14.54a30.56,30.56,0,0,1,.92,10,47.27,47.27,0,0,1-4,15.36S60.49,77,62.13,65.82c2.79,2.89,5.37,7.12,5.67,11.45A17.89,17.89,0,0,1,57.08,95c11.86-2.78,20.21-12.78,23.2-20.1C84,65.72,83,57.47,82.34,50.46a36.09,36.09,0,0,1,2.58-16.9S78.42,35.41,73.58,43.35Z"/></g></svg>

  {% elsif name == 'flag' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="75.47 34.15 90.23 8.28 17.77 8.28 17.77 5 9.77 5 9.77 95 17.77 95 17.77 60 90.23 60 75.47 34.15"/></g></svg>

  {% elsif name == 'flickr' %}
    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M23.7974 26C19.0924 25.9978 14.4925 27.391 10.5794 30.0033C6.66632 32.6156 3.61585 36.3298 1.81382 40.6759C0.0117927 45.0221 -0.460839 49.8051 0.455703 54.4199C1.37225 59.0347 3.63679 63.2741 6.96291 66.6017C10.289 69.9294 14.5273 72.1959 19.1417 73.1146C23.7561 74.0333 28.5393 73.5629 32.8863 71.7629C37.2333 69.9629 40.9489 66.9142 43.563 63.0023C46.1772 59.0905 47.5725 54.4912 47.5725 49.7862C47.5666 43.4815 45.0602 37.4364 40.6031 32.9772C36.146 28.5181 30.1021 26.0088 23.7974 26V26Z" fill="currentColor"/>
      <path d="M76.2137 26C71.5093 26 66.9104 27.395 62.9988 30.0087C59.0872 32.6224 56.0384 36.3373 54.2381 40.6836C52.4378 45.03 51.9668 49.8126 52.8845 54.4267C53.8023 59.0408 56.0678 63.2791 59.3943 66.6057C62.7209 69.9322 66.9592 72.1977 71.5733 73.1155C76.1874 74.0333 80.97 73.5622 85.3163 71.7619C89.6627 69.9616 93.3776 66.9128 95.9913 63.0012C98.6049 59.0896 100 54.4907 100 49.7863C99.9941 43.4796 97.4862 37.4328 93.0267 32.9733C88.5671 28.5138 82.5204 26.0059 76.2137 26V26Z" fill="currentColor"/>
    </svg>
  {% elsif name == 'flower' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M83.59,78.34a17,17,0,0,1-32.23,7.35,21.93,21.93,0,0,0,.75-14.85,19.28,19.28,0,0,0,14.37-9.16A21.76,21.76,0,0,0,78.19,66,16.92,16.92,0,0,1,83.59,78.34ZM47.72,73.81h0a16.89,16.89,0,0,0-1-3.12l-.65-.11a19.38,19.38,0,0,1-13-9.69,21.79,21.79,0,0,1-12.61,4l-.92,0A16.94,16.94,0,1,0,45.89,85.51h0a16.53,16.53,0,0,0,1.58-3.83,17.19,17.19,0,0,0,.59-4.45A17.37,17.37,0,0,0,47.72,73.81Zm-17-22.12a19.23,19.23,0,0,1,5.21-13.17,22.06,22.06,0,0,1-6.87-10.18A16.79,16.79,0,0,0,20.45,26a17,17,0,0,0-2.54,33.71h0a16.29,16.29,0,0,0,2.53.19,16.69,16.69,0,0,0,2.13-.14h0A16.66,16.66,0,0,0,28.23,58a16.9,16.9,0,0,0,3-2c-.06-.24-.11-.49-.15-.74A18.41,18.41,0,0,1,30.72,51.69ZM50,4.71A17,17,0,0,0,33,21.66a17.24,17.24,0,0,0,.37,3.55A16.93,16.93,0,0,0,36,31.29c.13.18.25.36.38.53a17.29,17.29,0,0,0,3.48,3.46c.3-.19.61-.37.92-.54A19.28,19.28,0,0,1,59.58,35a21.82,21.82,0,0,1,6.58-8.27h0A17,17,0,0,0,50,4.71ZM79.55,27.1a16.73,16.73,0,0,0-9.21,2.74,16.93,16.93,0,0,0-6.66,8.27l.36.38a19.22,19.22,0,0,1,5.24,13.2,19,19,0,0,1-.61,4.8,3.17,3.17,0,0,1-.12.44,16.85,16.85,0,0,0,11,4.07h.5a16.95,16.95,0,0,0-.5-33.89ZM64.27,51.39a14,14,0,0,0-1.64-6.34,13.86,13.86,0,0,0-2.39-3.29,5.1,5.1,0,0,0-.47-.46A13.5,13.5,0,0,0,58,39.87l-.45-.29a14.19,14.19,0,0,0-15.12,0l-.26.17a14.06,14.06,0,0,0-2,1.55l-.36.35a14.14,14.14,0,0,0-2.63,3.67,14.28,14.28,0,0,0-1.5,6.36A14.45,14.45,0,0,0,36,54.58c.1.45.21.89.34,1.33a16,16,0,0,0,.57,1.5,13.45,13.45,0,0,0,.78,1.51,14.25,14.25,0,0,0,9,6.64,13.23,13.23,0,0,0,1.9.33c.45,0,.91.07,1.38.07h0a13.94,13.94,0,0,0,1.72-.1A14.29,14.29,0,0,0,62,59.46h0a14.36,14.36,0,0,0,.76-1.31,13.59,13.59,0,0,0,.67-1.51c.14-.39.27-.79.38-1.2a14,14,0,0,0,.5-3.74C64.28,51.59,64.28,51.49,64.27,51.39Z"/></g></svg>

  {% elsif name == 'folder' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M74.62,36.9H33.82a10.2,10.2,0,0,1-9.77-7.37l-.5-1.78A2.51,2.51,0,0,0,21.69,26a.64.64,0,0,1-.2,0l-.38,0H6a2.52,2.52,0,0,0-1.95.91,2.6,2.6,0,0,0-.55,2.09l8.88,48.22a3.93,3.93,0,0,0,3.86,3.21H83.12l-6.77-42A1.74,1.74,0,0,0,74.62,36.9Z"/><path d="M25.94,22.43l.29.24.13.12a4.36,4.36,0,0,1,.43.43l.18.2a3.7,3.7,0,0,1,.37.49,3.92,3.92,0,0,1,.38.55c.09.15.18.31.26.47l.21.46c0,.09.1.21.14.33a5.27,5.27,0,0,1,.22.67L29,28.12a5,5,0,0,0,4.78,3.6h40.8a6.9,6.9,0,0,1,6.85,5.83l5,31.35,9.93-46.31a2.42,2.42,0,0,0-2.39-2.94H27.6a2.45,2.45,0,0,0-2.41,2l0,.23.14.09Z"/></g></svg>

  {% elsif name == 'gear' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><style>.cls-1{fill-rule:evenodd;}</style></defs><g><path class="cls-1" d="M94.37,44.11h0l-7.43-1.85-3-.74-1-2.89a33,33,0,0,0-1.55-3.76l-1.32-2.74,1.56-2.6L86,22.36a1.56,1.56,0,0,0-.12-1l-7.19-7.17A1.24,1.24,0,0,0,78,14a1,1,0,0,0-.33,0l-7.16,4.3-2.62,1.56-2.73-1.32A30.94,30.94,0,0,0,61.37,17l-2.88-1-.74-3L55.9,5.63A1.54,1.54,0,0,0,55.06,5H44.94a1.38,1.38,0,0,0-.84.65l-1.85,7.41-.74,3-2.88,1a30.94,30.94,0,0,0-3.76,1.55l-2.73,1.32-2.62-1.56L22.35,14A1.41,1.41,0,0,0,22,14a1.52,1.52,0,0,0-.72.16l-7.17,7.17a1.46,1.46,0,0,0-.12,1l4.3,7.16,1.56,2.6-1.32,2.74A33,33,0,0,0,17,38.62l-1,2.89-3,.74L5.63,44.11a1.48,1.48,0,0,0-.63.83V55.06a1.44,1.44,0,0,0,.63.84l7.43,1.85,3,.74,1,2.88a31.75,31.75,0,0,0,1.55,3.77l1.32,2.72-1.56,2.62L14,77.64a1.56,1.56,0,0,0,.12,1l7.19,7.17A1.24,1.24,0,0,0,22,86a1,1,0,0,0,.33,0l7.16-4.3,2.62-1.56,2.73,1.32A30.94,30.94,0,0,0,38.63,83l2.88,1,.74,3,1.85,7.43a1.54,1.54,0,0,0,.84.63H55.06a1.38,1.38,0,0,0,.84-.63l1.85-7.43.74-3,2.88-1a30.94,30.94,0,0,0,3.76-1.55l2.73-1.32,2.62,1.56L77.64,86A1.7,1.7,0,0,0,78,86a1.52,1.52,0,0,0,.72-.16l7.17-7.17a1.45,1.45,0,0,0,.12-1l-4.3-7.16-1.56-2.62,1.32-2.72A31.75,31.75,0,0,0,83,61.37l1-2.88,3-.74,7.43-1.85a1.54,1.54,0,0,0,.63-.84V44.94A1.34,1.34,0,0,0,94.37,44.11ZM50,66.88A16.88,16.88,0,1,1,66.88,50,16.88,16.88,0,0,1,50,66.88Z"/></g></svg>

  {% elsif name == 'gem' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="2.13 36.24 42.69 84.73 29.23 36.24 2.13 36.24"/><polygon points="36.15 36.24 49.95 85.97 63.77 36.24 36.15 36.24"/><polygon points="57.41 84.03 97.78 36.24 70.69 36.24 57.41 84.03"/><polygon points="97.88 29.59 86.44 15.71 75.25 29.59 97.88 29.59"/><polygon points="79.23 14.03 56.84 14.03 67.92 28.05 79.23 14.03"/><polygon points="60.63 29.59 49.88 15.96 38.41 29.59 60.63 29.59"/><polygon points="42.8 14.03 20.68 14.03 31.5 27.45 42.8 14.03"/><polygon points="13.64 15.94 2.13 29.59 24.66 29.59 13.64 15.94"/></g></svg>

  {% elsif name == 'gift' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M90.36,25.75A14.11,14.11,0,0,0,71,6c-3.13,1.74-15.09,8.66-21.38,16.12C43.35,14.66,31.39,7.74,28.26,6A14.11,14.11,0,0,0,8.91,25.75H7v25H47.26v-18h6v18H93.49v-25ZM75,12.93l.27-.17A6.12,6.12,0,1,1,82,23l-4.21,2.78H57.09C61.52,21.32,69.27,16.11,75,12.93Zm-59.5,1.56A6.13,6.13,0,0,1,24,12.76l.27.17C30,16.11,37.75,21.32,42.18,25.75H21.45L17.24,23A6.13,6.13,0,0,1,15.51,14.49Z"/><rect x="53.26" y="56.78" width="35.97" height="39.47"/><rect x="11.29" y="56.78" width="35.97" height="39.47"/></g></svg>

  {% elsif name == 'globe' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M37.49,96.61A48.15,48.15,0,0,1,13,80.89H27.59A42.55,42.55,0,0,0,37.49,96.61Z"/><path d="M65.42,80.89c-2.68,6.26-6.7,12.09-12.7,15.49V80.89Z"/><path d="M87,80.89A48.31,48.31,0,0,1,60.94,97,42.3,42.3,0,0,0,71.27,80.89Z"/><path d="M98.14,52.74A47.84,47.84,0,0,1,91,75.46H73.09a86,86,0,0,0,3.15-21v-.18c0-.53,0-1.06,0-1.58Z"/><path d="M70.85,52.74c0,.49,0,1,0,1.48v.1A81.24,81.24,0,0,1,67.4,75.46H52.72V52.74Z"/><path d="M25.77,75.46H9A47.84,47.84,0,0,1,1.86,52.74H22.57c0,.52,0,1,.05,1.58A85.58,85.58,0,0,0,25.77,75.46Z"/><path d="M25.73,24.58a88.43,88.43,0,0,0-3.16,22.73H1.86a47.75,47.75,0,0,1,7.2-22.73Z"/><path d="M70.85,47.31H52.72V24.58H67.47A81.42,81.42,0,0,1,70.85,47.31Z"/><path d="M98.14,47.31H76.28a88.87,88.87,0,0,0-3.16-22.73H90.94A47.75,47.75,0,0,1,98.14,47.31Z"/><path d="M87,19.15H71.3A43,43,0,0,0,60.68,3,48,48,0,0,1,87,19.15Z"/><path d="M65.42,19.15H52.72V3.72C56.37,5.92,61.53,10.39,65.42,19.15Z"/><path d="M37.79,3.38A42.85,42.85,0,0,0,27.56,19.15H13A48,48,0,0,1,37.79,3.38Z"/><path d="M47.28,80.89V97c-6.63-3.28-11-9.44-13.83-16.08Z"/><path d="M47.28,52.74V75.46H31.46a81.5,81.5,0,0,1-3.41-21.24c0-.5,0-1,0-1.48Z"/><path d="M47.28,24.58V47.31H28a81,81,0,0,1,3.37-22.73Z"/><path d="M47.28,3.08V19.15H33.44C37.76,9.43,43.64,5,47.28,3.08Z"/></g></svg>

  {% elsif name == 'grid' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="5" y="5" width="21.72" height="21.72"/><rect x="39.14" y="5" width="21.72" height="21.72"/><rect x="73.28" y="5" width="21.72" height="21.72"/><rect x="5" y="39.14" width="21.72" height="21.72"/><rect x="39.14" y="39.14" width="21.72" height="21.72"/><rect x="73.28" y="39.14" width="21.72" height="21.72"/><rect x="5" y="73.28" width="21.72" height="21.72"/><rect x="39.14" y="73.28" width="21.72" height="21.72"/><rect x="73.28" y="73.28" width="21.72" height="21.72"/></g></svg>

  {% elsif name == 'hanger' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95.92,70,54,41.91V39.34a11.46,11.46,0,0,0,8.16-10.71c0-6.26-5.46-11.36-12.16-11.36s-12.16,5.1-12.16,11.36h8c0-1.85,1.86-3.36,4.16-3.36s4.16,1.51,4.16,3.36S52.3,32,50,32v0h0l-4,4h0v5.91L4.09,70a6.93,6.93,0,0,0,3.82,12.7H92.07A6.93,6.93,0,0,0,95.92,70ZM11.46,74.73,50,48.86,88.55,74.73Z"/></g></svg>

  {% elsif name == 'heart' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M87.62,17.7a22.92,22.92,0,0,0-31.69,0L50,23.45,44.06,17.7a22.91,22.91,0,0,0-31.68,0,23.87,23.87,0,0,0,0,34.52L50,88.66,87.62,52.22A23.87,23.87,0,0,0,87.62,17.7Z"/></g></svg>

  {% elsif name == 'house' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="97.44 45.2 87.46 37.3 87.46 37.28 85.07 35.41 50.02 7.66 13.99 34.69 12.2 36.01 12.21 36.03 2.56 43.26 7.36 49.66 12.46 45.84 12.46 92.34 40.84 92.34 40.84 88.34 40.84 57.97 59.08 57.97 59.08 88.34 59.08 92.34 87.46 92.34 87.46 47.5 92.48 51.48 97.44 45.2"/></g></svg>

  {% elsif name == 'houzz' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="9.04 95 9.04 5 29.81 5 29.81 25.77 90.96 43.08 90.96 95 62.12 95 62.12 65 39.04 65 39.04 95 9.04 95"/></g></svg>

  {% elsif name == 'info' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,7A43,43,0,1,0,93,50,43,43,0,0,0,50,7Zm6.93,68.29a6.68,6.68,0,0,1-6.88,6.45,7.1,7.1,0,0,1-4.86-1.89,6.25,6.25,0,0,1-2-4.56V45.72a6.67,6.67,0,0,1,6.87-6.44,7.14,7.14,0,0,1,4.87,1.88,6.25,6.25,0,0,1,2,4.56ZM50,33.05a7.4,7.4,0,1,1,7.39-7.4A7.39,7.39,0,0,1,50,33.05Z"/></g></svg>

  {% elsif name == 'instagram' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M53.9761 33.8241C55.7845 34.2571 59.0099 35.8707 60.4962 37.0857C62.332 38.5864 63.8205 40.4962 64.9097 42.7477C66.2306 45.4785 66.568 46.955 66.568 50.0036C66.568 53.0523 66.2306 54.5288 64.9097 57.2596C63.2107 60.7718 60.7831 63.1985 57.2564 64.9093C54.5292 66.2323 53.0521 66.5704 50 66.5704C46.9479 66.5704 45.4708 66.2323 42.7435 64.9093C39.2169 63.1985 36.7893 60.7718 35.0902 57.2596C34.5614 56.1662 33.972 54.6454 33.7803 53.8801C33.3048 51.9804 33.3048 48.0268 33.7803 46.1272C34.2622 44.2025 35.8559 41.0067 37.0811 39.5082C39.6545 36.3607 43.7087 34.0563 47.6397 33.5068C49.2781 33.2776 52.3336 33.4307 53.9761 33.8241Z" fill="currentColor"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M32.0079 0.0317186C28.7384 0.15994 24.7849 0.545001 22.7694 0.931454C11.094 3.17026 3.15765 11.1065 0.925248 22.775C0.0737574 27.2264 0 29.3946 0 50.0036C0 70.6127 0.0737574 72.7809 0.925248 77.2323C3.15765 88.9008 11.1 96.8426 22.7694 99.0748C27.2211 99.9263 29.3895 100 50 100C70.6105 100 72.7789 99.9263 77.2306 99.0748C88.9 96.8426 96.8423 88.9008 99.0748 77.2323C99.9262 72.7809 100 70.6127 100 50.0036C100 29.3946 99.9262 27.2264 99.0748 22.775C96.8342 11.0635 88.84 3.10466 77.1225 0.919726C72.8445 0.122169 71.1712 0.0615375 52.0875 0.0146224C41.9185 -0.0102267 32.8827 -0.00267253 32.0079 0.0317186ZM70.9873 21.4496C71.4642 19.9162 73.2636 18.0475 74.7515 17.5404C75.936 17.1368 77.9368 17.2092 79.096 17.6976C80.1553 18.1441 81.6935 19.6343 82.1943 20.6992C82.8185 22.0275 82.9304 23.6723 82.4921 25.0814C81.9779 26.7347 80.209 28.5038 78.5561 29.0179C73.9113 30.4623 69.5428 26.094 70.9873 21.4496ZM31.8117 31.8167C36.0587 27.5699 41.537 24.982 47.6143 24.3512C50.6348 24.0377 54.6424 24.4818 57.8042 25.4806C65.6897 27.9714 72.0338 34.3151 74.5249 42.2C76.129 47.2778 76.129 52.7295 74.5249 57.8072C72.0338 65.6922 65.6897 72.0358 57.8042 74.5267C52.7261 76.1308 47.274 76.1308 42.1958 74.5267C34.3104 72.0358 27.9662 65.6922 25.4752 57.8072C22.5672 48.6023 24.9986 38.6293 31.8117 31.8167Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'kickstarter' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M75.8628 50.0203L83.8188 42.1173C92.0604 33.9307 92.0604 20.5969 83.8188 12.4103C75.5771 4.22359 62.1538 4.22359 53.9123 12.4103L51.0153 15.2878C47.1802 9.81647 40.8562 6.25 33.6346 6.25C21.9657 6.25 12.5 15.6525 12.5 27.2435V72.7565C12.5 84.3475 21.9657 93.75 33.6346 93.75C40.8562 93.75 47.1802 90.1836 51.0153 84.7123L53.9123 87.5898C62.1538 95.7765 75.5771 95.7765 83.8188 87.5898C92.0604 79.403 92.0604 66.0693 83.8188 57.8827L75.8628 50.0203Z" fill="currentColor"/></svg>

  {% elsif name == 'krona' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="49.57 38.62 44.02 32.86 23.93 52.23 23.93 12.51 15.93 12.51 15.93 84.33 23.93 84.33 23.93 69.78 46.86 87.5 51.75 81.16 26.04 61.3 49.57 38.62"/><path d="M65.57,40.12V35.54h-8V84.33h8V53.93c.58-2.22,3.91-11.86,18.22-11.34l.28-8C75.75,34.28,69.75,36.74,65.57,40.12Z"/></g></svg>

  {% elsif name == 'leaf' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M72.93,59.25c0-.24,0-.48,0-.71v0C73.07,55.22,72.18,40.75,50,5,24.31,46.4,27.17,59.25,27.17,59.25A22.87,22.87,0,0,0,46,81.77V95h8V81.78A22.88,22.88,0,0,0,72.93,59.25Z"/></g></svg>

  {% elsif name == 'leaf-2' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M87.31,94.51C85.18,87.06,80.64,79,75.16,71.3c9.32-11.18,11-26.26,3.2-36.67l0,0L77.86,34c-3.18-4.61-15.3-16.3-60.06-29.22L12.69,3.29V8.61c0,51.1,9.38,64,12.29,66.72,5.08,6.52,13,9.84,21.53,9.84a37.07,37.07,0,0,0,22.12-7.66l.72-.57C74,83.66,77.84,90.48,79.62,96.71Z"/></g></svg>

  {% elsif name == 'left-arrow' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="95.04 46 21.68 46 48.18 22.8 42.91 16.78 4.96 50 42.91 83.22 48.18 77.2 21.68 54 95.04 54 95.04 46"/></g></svg>

  {% elsif name == 'left-caret' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="71.49 97.85 22.9 50 71.49 2.15 77.1 7.85 34.3 50 77.1 92.15 71.49 97.85"/></g></svg>

  {% elsif name == 'lightbulb' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,7A25.7,25.7,0,0,0,30.27,49.16l.09.11c4.08,5.69,5.73,9.13,6.38,11.09H63.26c.64-1.94,2.27-5.34,6.28-10.94a1.3,1.3,0,0,1,.1-.15.48.48,0,0,1,.09-.11.61.61,0,0,1,.12-.15A25.7,25.7,0,0,0,50,7ZM48.36,18.84A17,17,0,0,0,42.91,21a11.41,11.41,0,0,0-5.75,9,2,2,0,0,1-2,1.82H35a2,2,0,0,1-1.81-2.18c.9-9.82,9.89-13.87,14.52-14.64a2,2,0,1,1,.67,3.94Z"/><path d="M62.83,75.12v3.75a2.26,2.26,0,0,1-2.26,2.26H39.43a2.26,2.26,0,0,1-2.26-2.26V75.12Z"/><rect x="37.17" y="64.36" width="25.66" height="6.76"/><path d="M58.86,85.13a8.38,8.38,0,0,1-16.73,0Z"/></g></svg>

  {% elsif name == 'like' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M98,44.86a7.42,7.42,0,0,0-7.41-7.4l-31.11.44a.49.49,0,0,1-.41-.24.47.47,0,0,1,0-.47c1.31-2.84,3-10.52.9-21.27-.78-4.05-3.12-6.66-6-6.66a5,5,0,0,0-4.89,4.09,81.76,81.76,0,0,1-1.6,9.34c-2.1,8.79-12.93,19.81-19.76,23.84a1,1,0,0,0-.49.84V86.64a1,1,0,0,0,.51.86,28.11,28.11,0,0,0,13.81,3.24h.77c2.85,0,7.63-.08,14.62-.08H57l23.29-.48c4.29,0,7.4-2.84,7.4-6.74A7.34,7.34,0,0,0,85.31,78h.15A7.4,7.4,0,0,0,90.1,64.81,7.38,7.38,0,0,0,92.67,52,7.4,7.4,0,0,0,98,44.86Z"/><rect x="2" y="43.66" width="18.06" height="45.58" rx="1.46"/></g></svg>

  {% elsif name == 'link' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M87.83,59.4,79.4,51a20.11,20.11,0,0,0-25.32-2.55l-2.5-2.5A20.11,20.11,0,0,0,49,20.6L40.6,12.17A20.1,20.1,0,0,0,12.17,40.6L20.6,49a20.11,20.11,0,0,0,25.32,2.55l2.5,2.5A20.1,20.1,0,0,0,51,79.39l8.43,8.44A20.1,20.1,0,0,0,87.83,59.4Zm-53-12.48a12.06,12.06,0,0,1-8.56-3.54l-8.43-8.44A12.1,12.1,0,0,1,34.94,17.83l8.44,8.43a12.09,12.09,0,0,1,2.34,13.8L36.8,31.15,31.15,36.8l8.91,8.92A12,12,0,0,1,34.82,46.92ZM82.17,82.17a12.11,12.11,0,0,1-17.11,0l-8.44-8.43a12.09,12.09,0,0,1-2.34-13.8l8.92,8.91,5.65-5.65-8.91-8.92a12.09,12.09,0,0,1,13.8,2.34l8.43,8.44A12.11,12.11,0,0,1,82.17,82.17Z"/></g></svg>

  {% elsif name == 'linkedin' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path data-name="LinkedIn" d="M55.4,43.2c2.56-4,7.14-9.59,17.39-9.59C85.48,33.61,95,41.9,95,59.73V93H75.71V62c0-7.8-2.79-13.13-9.77-13.13-5.33,0-8.5,3.59-9.9,7.06a13.18,13.18,0,0,0-.64,4.7V93H36.1s.26-52.58,0-58H55.4V43.2M15.92,7C9.32,7,5,11.33,5,17s4.19,10,10.66,10h.13c6.73,0,10.92-4.46,10.92-10S22.52,7,15.92,7ZM6.15,93H25.44V35H6.15Z"/></g></svg>

  {% elsif name == 'lock' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M81.73,40.87h-9V31.78a22.78,22.78,0,0,0-45.56,0v9.09H18.27a4.73,4.73,0,0,0-4.73,4.73V86.28A4.73,4.73,0,0,0,18.27,91H81.73a4.73,4.73,0,0,0,4.73-4.72V45.6A4.73,4.73,0,0,0,81.73,40.87ZM35.22,31.78a14.78,14.78,0,0,1,29.56,0v9.09H35.22Z"/></g></svg>

  {% else %}
    <p>?</p>
  {% endif %}
</span>
