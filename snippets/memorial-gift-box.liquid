<style>
  .swatchContainer {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin: 0 0 20px;
    gap: 20px;
  }
  .swatchDiv {
    width: 147px;
    margin-top: auto;
  }

  .stepLabel {
    display: flex;
    font-size: 20px;
    font-weight: 400;
    margin-bottom: 30px;
  }
  .swatchImage {
    height: 147px;
    width: 147px;
    border-radius: 10px;
  }
  .swatchImageLabel {
    display: flex;
    justify-content: center;
  }
  .subtotalContainer {
    display: flex;
    justify-content: center;
    font-size: 22px;
    font-weight: 500;
    margin-top: 30px;
  }
  .subtotalText {
    color: #5461C9;
  }
  .satisfy_container {
    margin: 10px 0;
    width: 100%;
    cursor: default;
  }
  .satisfy_button {
    width: 100%;
    height: 50px;
    color: black;
    background: rgba(241, 241, 241, 0.76);
    border: 1px solid rgba(241, 241, 241, 0.76);
    font-size: 22px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }
  .next_step_button_container {
    margin: 40px 0 20px;
    width: 100%;
  }
  .next_step_button {
    height: 50px;
    width: 100%;
    border: none;
    background: #C963CF;
    margin-top: -20px;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: 0.5s ease;
  }
  .next_step_button:hover {
    background: #C963CF;
    opacity: 0.7;
  }

  @media (max-width: 1080px) {
    .swatchContainer {
      gap: 8px;
    }
    .satisfy_button {
      width: 100%;
    }
  }
  @media (max-width: 480px) {
    .product-title {
      text-align: center;
    }
    .pricesContainer {
      display: flex;
      justify-content: center;
    }
    .pricePercentage {
      text-align: center;
    }
  }
</style>

{% assign metaValues = product.metafields.cuddleclones.memorial_gift_box_customizer.value %}

<div>
  {% if metaValues.identifier == "swatches" and metaValues.swatches.size > 0 %}
    {% for option in metaValues.swatches %}
      <div>
        <p class="stepLabel">{{ option.label }}:</p>
        <div class="swatchContainer">
          {% assign classname = "giftbox_option_" %}
          <input
            type="hidden"
            data-name="{{classname}}"
            class="{%if metaValues.gallery_image_change%}{{classname}}HiddenInputs{%endif%} {%if metaValues.native%}nativeHiddenInputs{% else %}customHiddenInputs{%endif%}"
            value='{{option.options[0].label}}'>
          {% for swatchOption in option.options %}
            <div class="swatchDiv">
              {% if swatchOption.extra_charges and swatchOption.extra_charges > 0 %}
                <p class="extraCharges">+&nbsp;${{ swatchOption.extra_charges }}</p>
              {% endif %}
              <img
                style="{% if forloop.first %}border:4px solid rgb(84, 97, 201);{% endif %}"
                class="swatchImage memorialGiftSwatchImages"
                src="{{swatchOption.imageUrl}}"
                alt="{{swatchOption.alt}}"
                onclick="handleGiftBoxSwatch(this,'{{swatchOption.label}}')">
              <p class="swatchImageLabel">{{ swatchOption.label }}</p>
              {% if swatchOption.sub_label and swatchOption.sub_label != "" %}
                <p class="swatchImageLabel">{{ swatchOption.sub_label }}</p>
              {% endif %}
            </div>
          {% endfor %}
        </div>
      </div>
    {% endfor %}
    <div class="subtotalContainer">
      <span class="subtotalText">Subtotal:&nbsp;</span>$
      <span class="subTotalPrice">{{ product.price | divided_by: 100.00 }}</span>
    </div>

    <div class="next_step_button_container">
      <button class="{{ step.step_name | downcase | replace: " ", "" }}NextButton next_step_button" onclick="handleGiftBoxCart()">ADD TO CART</button>
    </div>

    <div class="satisfy_container">
      <button class="satisfy_button" disabled>
        <span style="display: flex;">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="30"
            height="30"
            viewBox="0 0 41 41"
            fill="none">
            <path d="M20.5 3.41669C17.1212 3.41669 13.8183 4.41861 11.009 6.29575C8.19965 8.17289 6.01004 10.8409 4.71704 13.9625C3.42404 17.0841 3.08574 20.519 3.7449 23.8328C4.40406 27.1467 6.03109 30.1906 8.42024 32.5798C10.8094 34.9689 13.8533 36.5959 17.1672 37.2551C20.481 37.9143 23.9159 37.576 27.0375 36.283C30.1591 34.99 32.8271 32.8004 34.7043 29.991C36.5814 27.1817 37.5833 23.8788 37.5833 20.5C37.5833 18.2566 37.1414 16.0352 36.2829 13.9625C35.4244 11.8899 34.1661 10.0066 32.5797 8.42028C30.9934 6.83395 29.1101 5.5756 27.0375 4.71708C24.9648 3.85856 22.7434 3.41669 20.5 3.41669ZM27.8458 16.4171L20.0387 26.6671C19.8796 26.8738 19.6752 27.0414 19.4413 27.1569C19.2073 27.2724 18.95 27.3327 18.6891 27.3334C18.4297 27.3348 18.1733 27.277 17.9394 27.1645C17.7056 27.0521 17.5004 26.8878 17.3396 26.6842L13.1712 21.3713C13.0333 21.194 12.9316 20.9914 12.8719 20.7748C12.8123 20.5583 12.7959 20.3321 12.8236 20.1092C12.8514 19.8864 12.9228 19.6711 13.0337 19.4758C13.1446 19.2806 13.293 19.109 13.4702 18.9711C13.8281 18.6924 14.2821 18.5674 14.7322 18.6234C14.9551 18.6512 15.1703 18.7226 15.3656 18.8335C15.5609 18.9445 15.7324 19.0928 15.8704 19.27L18.655 22.8234L25.1125 14.2817C25.2493 14.1022 25.4202 13.9515 25.6153 13.838C25.8104 13.7246 26.026 13.6507 26.2496 13.6205C26.4733 13.5904 26.7007 13.6046 26.9189 13.6623C27.1371 13.7201 27.3418 13.8203 27.5212 13.9571C27.7007 14.094 27.8515 14.2648 27.9649 14.4599C28.0784 14.655 28.1523 14.8706 28.1824 15.0943C28.2125 15.3179 28.1983 15.5453 28.1406 15.7635C28.0828 15.9817 27.9827 16.1864 27.8458 16.3659V16.4171Z" fill="#5461C9" />
          </svg>
        </span>
        <span>{{ metaValues.guarantee_note }}</span>
      </button>
    </div>
  {% endif %}
</div>

<script>
  let productDetail={{product | json}};

  let productVariants = {{ product.variants | json }};

  let hiddenInputs=document.querySelector(".nativeHiddenInputs");

  var finalVariantValue=hiddenInputs.value;

  document.addEventListener("DOMContentLoaded", () => {
    setTimeout(() => {
      const foundVariant = findVariant(finalVariantValue);
      
      if (foundVariant) {
        updatePriceContainer(foundVariant);
      }
    }, 4000);
  });

  function handleGiftBoxSwatch(currentImage,swatchValue){
    const swatchImages=document.querySelectorAll(`.memorialGiftSwatchImages`);

    swatchImages.forEach(img=>{
      img.style.border="none";
    });

    currentImage.style.border="4px solid #5461C9";

    finalVariantValue=swatchValue;

    handleImageSlider(finalVariantValue);

    let foundVariant=findVariant(finalVariantValue);

    if(foundVariant){
      updatePriceContainer(foundVariant);
      let priceDiv=document.querySelector(`.subTotalPrice`);
      priceDiv.innerHTML = (foundVariant.price / 100).toFixed(2);
    }
  }

  function handleGiftBoxCart(){
    let foundVariant=findVariant(finalVariantValue);

    let variantIdInput = document.querySelector("#variantid");

    if(foundVariant){
      variantIdInput.value = foundVariant.id;
      updatePriceContainer(foundVariant);
    }
  }

  function findVariant(variantTitle) {
    return productVariants.find(variant => {
      return variantTitle === variant.title;
    });
  }

  function updatePriceContainer(variant){
    var priceDiv=document.querySelector(".originalPrice");
    let price;
    let formattedPrice;
    if (priceDiv) {
      price = variant.price / 100; 
      formattedPrice = `$${price.toFixed(2)}`; 
      priceDiv.textContent = formattedPrice ;
    }
    
    const priceUIContainer=document.querySelector(".price-ui");
    const compareAtPriceDiv=priceUIContainer.querySelector(".sizeChartBox .compare-at-price");
    let compareAtPrice;
    let formattedComparePrice;
    if(compareAtPriceDiv){
      compareAtPrice = variant.compare_at_price / 100;
      if(compareAtPrice>0){
        formattedComparePrice = `$${compareAtPrice.toFixed(2)}`;
        compareAtPriceDiv.innerHTML="";
        compareAtPriceDiv.textContent=' '+formattedComparePrice;
      }
    }

    var pricePercentageContainer=document.querySelector(".pricePercentage");
    pricePercentageContainer.textContent="";
    if(pricePercentageContainer && compareAtPrice>0){
      const content=document.createElement("p");
      var amountSaved = compareAtPrice - price;
      var discountPercentage = Math.floor((amountSaved / compareAtPrice) * 100);
      content.textContent=`You save: ${discountPercentage}% ($${amountSaved.toFixed(2)})`;
      const defaultPricePercentage=document.querySelector(".pricePercentageDefault");
      if(defaultPricePercentage){
        defaultPricePercentage.style.display="none";
      }
      pricePercentageContainer.appendChild(content);
    }
  }

  function handleImageSlider(variantValue){
    let concatenatedImageName="";  
    if (variantValue!=="") {
      concatenatedImageName = variantValue.replace(/\s/g, '');
    }

    const thumbnailImages=document.querySelectorAll(".product-gallery__thumbnail");
    
    if(thumbnailImages){
      thumbnailImages.forEach(image => {
        const imageTitle = $(image).attr('data-title'); 

        if (imageTitle === concatenatedImageName) {
          $(image).trigger('click'); 
          $(thumbnailImages).removeClass('is-nav-selected'); 
          $(image).addClass('is-nav-selected'); 
        }
      });
    }
  }
</script>