{% if block.settings.title != blank %}
  <div class="sidebar-block__toggle" data-has-toggle-option>
    <h3 class="sidebar-block__heading" {% if settings.toggle_sidebar %}data-sidebar-block__toggle="closed" aria-label="toggle"{% endif %}>
      {{ block.settings.title }}
      {% if settings.toggle_sidebar %}
        <button class="sidebar-block__toggle-icon icon-style--{{ settings.toggle_icon_style }}">
          {% if settings.toggle_icon_style == 'plus_and_minus' %}
            {%
              render 'icon',
              name: 'plus',
              icon_class: 'icon--active'
            %}
            {%
              render 'icon',
              name: 'minus'
            %}
          {% else %}
            {%
              render 'icon',
              name: 'down-caret',
              icon_class: 'icon--active'
            %}
          {% endif %}
        </button>
      {% endif %}
    </h3>
  </div>
{% endif %}
<div class="sidebar-block__content"  {% if settings.toggle_sidebar %}data-sidebar-block__content--collapsible{% endif %}>
  {% for article in blogs[blog_handle].articles limit: block.settings.blog_post_count %}
    <div class="sidebar-block__recent-post">
      <a href="{{ article.url }}" title="{{ article.title | escape }}">{{ article.title }}</a>
      <div class="meta">
        <span class="label">{{ article.published_at | date: format: "month_day_year" }}</span>
      </div>
    </div>
  {% endfor %}
</div>
