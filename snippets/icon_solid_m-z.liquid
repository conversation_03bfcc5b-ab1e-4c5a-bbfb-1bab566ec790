{% comment %}
Required values
name: <icon name>
______________
Optional values
icon_class: <string>
{% endcomment %}

<span class="icon {{ icon_class }}" data-icon="{{ name | downcase }}">

  {% if name == 'map' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="9.73 89.88 32.58 77.08 32.58 10.12 9.73 22.92 9.73 89.88"/><polygon points="67.42 22.92 67.42 89.88 90.27 77.08 90.27 10.12 67.42 22.92"/><polygon points="38.58 77.08 61.42 89.88 61.42 22.92 38.58 10.12 38.58 77.08"/></g></svg>

  {% elsif name == 'medium' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M56.406 47.3219C56.406 63.1012 43.7789 75.8929 28.2035 75.8929C12.628 75.8929 0 63.0984 0 47.3219C0 31.5455 12.6271 18.75 28.2035 18.75C43.7799 18.75 56.406 31.5426 56.406 47.3219Z" fill="currentColor"/><path d="M87.3452 47.3219C87.3452 62.1747 81.0316 74.2201 73.2434 74.2201C65.4552 74.2201 59.1417 62.1747 59.1417 47.3219C59.1417 32.4691 65.4542 20.4237 73.2425 20.4237C81.0307 20.4237 87.3442 32.4652 87.3442 47.3219" fill="currentColor"/><path d="M100 47.3219C100 60.6264 97.7799 71.4185 95.0404 71.4185C92.3009 71.4185 90.0817 60.6293 90.0817 47.3219C90.0817 34.0145 92.3019 23.2253 95.0404 23.2253C97.7789 23.2253 100 34.0136 100 47.3219Z" fill="currentColor"/></svg>


  {% elsif name == 'megaphone' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="3.47" y="30.84" width="10" height="32.15"/><path d="M17.47,30.84V63h0l-.13,4.22A9.56,9.56,0,0,0,26,77.43l13.84,1.22a8.38,8.38,0,0,0,.86,0A9.55,9.55,0,0,0,50.23,70c0-.07,0-.15,0-.22l.11-3.42a118.34,118.34,0,0,1,21.23,6.18V21.31A150.37,150.37,0,0,1,17.47,30.84ZM42.25,69.35a1.6,1.6,0,0,1-.54,1,1.54,1.54,0,0,1-1.13.35L26.73,69.47a1.56,1.56,0,0,1-1.41-1.69c0-.07,0-.15,0-.22l.14-4.37A163.19,163.19,0,0,1,42.39,64.9Z"/><rect x="77.86" y="28.62" width="18.01" height="8" transform="translate(-4.74 17.87) rotate(-11.43)"/><rect x="83.25" y="37.52" width="8" height="18.43" transform="translate(39.04 133.17) rotate(-89.02)"/><rect x="82.69" y="52.29" width="8" height="17.83" transform="translate(6.41 130.6) rotate(-76.06)"/></g></svg>

  {% elsif name == 'menu' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="5" y="12" width="90" height="8"/><rect x="5" y="46" width="90" height="8"/><rect x="5" y="80" width="90" height="8"/></g></svg>

  {% elsif name == 'messenger' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M50.0077 0C21.8429 0 0 20.6322 0 48.5057C0 63.0843 5.97701 75.6782 15.7011 84.3755C16.5096 85.1073 17.0038 86.1188 17.0421 87.2222L17.318 96.1226C17.3378 96.777 17.5182 97.4165 17.8434 97.9847C18.1686 98.5529 18.6285 99.0325 19.1826 99.3811C19.7368 99.7297 20.3682 99.9367 21.0212 99.9838C21.6742 100.031 22.3287 99.9168 22.9272 99.6513L32.8544 95.2759C33.7011 94.908 34.6398 94.8352 35.5211 95.0728C40.0805 96.3257 44.9349 97.0038 49.9923 97.0038C78.1571 97.0038 100 76.3755 100 48.5019C100 20.6322 78.1609 0 50.0077 0ZM76.2644 37.8927L63.7433 57.7509C63.2721 58.4984 62.6512 59.1402 61.9197 59.6359C61.1882 60.1316 60.3619 60.4703 59.493 60.6309C58.6241 60.7914 57.7313 60.7702 56.871 60.5687C56.0107 60.3671 55.2014 59.9896 54.4942 59.4598L44.5326 51.9962C44.0889 51.6647 43.55 51.4856 42.9962 51.4856C42.4424 51.4856 41.9034 51.6647 41.4598 51.9962L28.0192 62.1992C26.2375 63.5594 23.8812 61.41 25.0766 59.5172L37.5977 39.659C38.0689 38.9115 38.6898 38.2697 39.4213 37.7741C40.1528 37.2784 40.9791 36.9396 41.848 36.7791C42.7169 36.6186 43.6097 36.6397 44.47 36.8413C45.3303 37.0428 46.1396 37.4204 46.8467 37.9502L56.8084 45.4138C57.2521 45.7452 57.791 45.9243 58.3448 45.9243C58.8986 45.9243 59.4376 45.7452 59.8812 45.4138L73.3218 35.2107C75.1226 33.8314 77.4789 35.9808 76.2644 37.8927Z" fill="currentColor"/></svg>

  {% elsif name == 'minus' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="5" y="46" width="90" height="8"/></g></svg>

  {% elsif name == 'moon' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M21.93,81.51A47,47,0,0,1,9.74,79.93,46.34,46.34,0,1,0,59.5,5,47.49,47.49,0,0,1,21.93,81.51Z"/></g></svg>

  {% elsif name == 'music' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M90.39,29.62l0-3.23V2.82l-.29.05v0L35.51,12.22l-3,.51h0V68.67A15.15,15.15,0,0,0,24.9,66.6a15.29,15.29,0,1,0,15.25,16h0V38.23l42.52-7.29V60.07A15.15,15.15,0,0,0,75.06,58,15.29,15.29,0,1,0,90.32,74h0V29.63Z"/></g></svg>

  {% elsif name == 'opensea' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M100 50C100 77.6127 77.6127 100 50 100C22.3873 100 0 77.6127 0 50C0 22.3873 22.3873 0 50 0C77.6185 0 100 22.3873 100 50ZM24.6678 51.6801L24.8835 51.341L37.8905 30.9932C38.0806 30.6953 38.5275 30.7261 38.6713 31.0497C40.8443 35.9196 42.7193 41.9762 41.8409 45.7468C41.4659 47.2982 40.4385 49.3992 39.2826 51.341C39.1337 51.6236 38.9693 51.901 38.7946 52.1681C38.7124 52.2914 38.5737 52.3633 38.4247 52.3633H25.0479C24.6883 52.3633 24.4777 51.9729 24.6678 51.6801ZM82.6443 58.682V55.4611C82.6443 55.1631 82.3567 54.9474 82.0741 55.0296L66.8634 59.4269C66.7812 59.4474 66.7092 59.4936 66.6527 59.5553C65.0236 61.3549 63.639 62.5626 63.2723 62.8824L63.2469 62.9046C62.3017 63.706 61.1304 64.1427 59.8975 64.1427H54.3906V58.5279H58.7674C58.8804 58.5279 58.9883 58.4868 59.0705 58.4149L59.6355 57.896C59.877 57.6751 60.1647 57.408 60.5088 57.0638C60.5381 57.0345 60.5678 57.0049 60.5978 56.975C60.7786 56.7948 60.9719 56.6023 61.1612 56.3909C61.3873 56.17 61.6082 55.9234 61.8136 55.682C62.1578 55.3121 62.4866 54.9268 62.8308 54.521C63.0774 54.2539 63.3034 53.9508 63.5243 53.6477C63.7709 53.36 64.0123 53.0364 64.2383 52.7282C64.3225 52.6045 64.412 52.4795 64.5035 52.3519C64.5907 52.2301 64.6797 52.106 64.7674 51.9782C64.9318 51.7316 65.0962 51.4696 65.2349 51.223C65.6664 50.5552 66.0312 49.8411 66.3394 49.1271C66.4809 48.8212 66.594 48.4992 66.7039 48.1862C66.7177 48.147 66.7315 48.1078 66.7452 48.0689C66.8685 47.7041 66.9712 47.36 67.0483 46.9952C67.2332 46.1373 67.2743 45.2846 67.1921 44.4318C67.1716 44.1647 67.151 43.9027 67.0894 43.6562V43.6151C67.0688 43.4404 67.0277 43.2452 66.9712 43.0654C66.7863 42.2332 66.4986 41.401 66.1339 40.5842C66.0106 40.2811 65.8668 39.9729 65.7281 39.6904C65.3993 39.0791 65.0551 38.4677 64.6647 37.877C64.5864 37.7522 64.4998 37.6258 64.4134 37.4996C64.3469 37.4024 64.2804 37.3053 64.2178 37.2092C63.9706 36.8278 63.6926 36.4602 63.4229 36.1035C63.3619 36.0229 63.3014 35.9428 63.2417 35.8633C63.0825 35.6542 62.9082 35.4451 62.7328 35.2347C62.6366 35.1194 62.5401 35.0036 62.4455 34.8872C62.1784 34.5636 61.9164 34.2554 61.6493 33.9523C60.6938 32.8735 59.6921 31.8975 58.7982 31.0653C58.6338 30.9009 58.454 30.7365 58.2691 30.5773C57.5756 29.9454 56.9437 29.3957 56.4146 28.9694C56.2556 28.8471 56.113 28.7266 55.9803 28.6144C55.89 28.5381 55.8043 28.4657 55.7211 28.3992C55.5608 28.2797 55.4217 28.1737 55.3049 28.0847C55.2308 28.0283 55.1657 27.9787 55.1098 27.9368C55.0687 27.906 55.0225 27.8855 54.9763 27.87L54.3906 27.7057V22.8563C54.3906 22.0652 54.0721 21.3563 53.5636 20.8374C53.055 20.3186 52.3461 20.0001 51.5653 20.0001C50.0036 20.0001 48.7399 21.2792 48.7399 22.8563V26.1234L48.4471 26.0413L47.6508 25.8152L46.9265 25.6149L46.9198 25.6129L46.906 25.6097H46.8906L41.3939 24.12C41.1525 24.0532 40.947 24.3152 41.0703 24.5361L41.9487 26.1594C41.9986 26.2841 42.0618 26.4089 42.1267 26.5371C42.1687 26.6201 42.2115 26.7045 42.2518 26.7913C42.3957 27.0789 42.5395 27.382 42.6782 27.6851C42.8015 27.9522 42.9248 28.2142 43.0686 28.5019C43.1291 28.6371 43.1907 28.774 43.2533 28.9129C43.4829 29.4227 43.7243 29.9588 43.9624 30.5362C44.1679 31.0242 44.3734 31.5122 44.5532 32.0208C45.0464 33.2999 45.5138 34.6663 45.9197 36.0688C46.0207 36.3827 46.1014 36.6864 46.1833 36.9943C46.2179 37.1247 46.2528 37.2559 46.2895 37.389L46.346 37.6355C46.5104 38.288 46.6543 38.9352 46.757 39.5876C46.8392 40.0345 46.9162 40.4609 46.9573 40.8924C47.019 41.3804 47.0806 41.8685 47.1012 42.3565C47.1423 42.8034 47.1628 43.2709 47.1628 43.7178C47.1628 44.8582 47.0601 45.9575 46.8186 46.9952C46.8035 47.0509 46.7884 47.1074 46.7731 47.1643C46.7055 47.4168 46.6354 47.6786 46.5515 47.9302C46.4757 48.1995 46.3773 48.4689 46.2752 48.7486C46.2391 48.8476 46.2025 48.9479 46.1662 49.05C46.1594 49.0682 46.1527 49.0864 46.1459 49.1047C46.0703 49.3081 45.9934 49.5152 45.8991 49.7179C45.3905 50.9405 44.7587 52.158 44.1063 53.2984C43.1508 54.9885 42.1902 56.4731 41.5172 57.4286C41.4761 57.4903 41.4359 57.549 41.3972 57.6056C41.349 57.676 41.3031 57.7431 41.2604 57.8087C41.0498 58.1066 41.2655 58.5279 41.6302 58.5279H48.7399V64.1427H41.548C39.6165 64.1427 37.8288 63.0485 36.9658 61.2967C36.5189 60.4183 36.3442 59.4525 36.447 58.5073C36.4726 58.2248 36.262 57.9577 35.9744 57.9577H21.4468C21.2002 57.9577 20.9999 58.158 20.9999 58.4046V58.7025C20.9999 67.9698 28.4846 75.4801 37.7209 75.4801H63.7811C68.6661 75.4801 71.4399 71.0286 74.1663 66.6533C74.9263 65.4336 75.6826 64.2198 76.4799 63.1101C77.9131 61.1169 81.3601 59.5347 82.3669 59.1032C82.5313 59.0313 82.6443 58.8669 82.6443 58.682Z" fill="currentColor"/></svg>

  {% elsif name == 'paper-clip' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M21.69,92.12A17.69,17.69,0,0,1,9.18,61.92L56.44,14.67A23.17,23.17,0,1,1,89.21,47.44L45.12,91.53l-5.66-5.66,44.1-44.09A15.17,15.17,0,0,0,62.1,20.32L14.84,67.58A9.69,9.69,0,0,0,28.55,81.29L69.61,40.23a4.21,4.21,0,0,0-6-6L30.72,67.19l-5.66-5.65L58,28.61A12.22,12.22,0,1,1,75.27,45.89L34.21,87A17.64,17.64,0,0,1,21.69,92.12Z"/></g></svg>

  {% elsif name == 'pentagram' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,3.18A46.82,46.82,0,1,0,96.82,50,46.87,46.87,0,0,0,50,3.18ZM85.31,33H62.16L55,11.15A39.26,39.26,0,0,1,85.31,33ZM36.53,60.89l6.83,4.93-11.1,8ZM21.62,40.69H35.17l-2.62,7.89ZM39.05,53.28l4.18-12.59H56.61l4.14,12.68L49.92,61.13ZM50,20.34,54.12,33H45.76ZM63.24,61,67.4,73.75l-10.94-7.9Zm4-12.29-2.62-8H78.46ZM45,11.16,37.71,33h-23A39.24,39.24,0,0,1,45,11.16ZM10.82,50a38.69,38.69,0,0,1,.68-7.19L30,56.19,22.76,78.12A39.06,39.06,0,0,1,10.82,50ZM30.87,84.17l19-13.63,19,13.75a39.06,39.06,0,0,1-38.06-.12ZM77,78.39l-7.2-22.07L88.52,42.88A39.78,39.78,0,0,1,89.18,50,39.08,39.08,0,0,1,77,78.39Z"/></g></svg>

  {% elsif name == 'phone' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95,76.76A11.6,11.6,0,0,1,91.59,85l-3.87,3.87A20.79,20.79,0,0,1,73.13,95a19.81,19.81,0,0,1-2.87-.22,73.37,73.37,0,0,1-26.14-9.52A83,83,0,0,1,27.59,72.4,83.11,83.11,0,0,1,14.72,55.87a73.35,73.35,0,0,1-9.5-26.13,20.54,20.54,0,0,1,5.92-17.48L15,8.39a11.65,11.65,0,0,1,16.43,0l8.48,8.49a11.61,11.61,0,0,1,0,16.44l-3.76,3.76a5.72,5.72,0,0,0-.58,7.33,82.45,82.45,0,0,0,9.13,10.86,81.3,81.3,0,0,0,10.88,9.14,5.7,5.7,0,0,0,7.32-.6l3.76-3.76a11.66,11.66,0,0,1,16.44,0l8.48,8.5A11.57,11.57,0,0,1,95,76.76Z"/></g></svg>

  {% elsif name == 'photo' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M88,7.5H12.05a3.85,3.85,0,0,0-3.84,3.84v65l23.27-22L48.62,71.51,77.2,44.35,91.79,59.2V11.34A3.85,3.85,0,0,0,88,7.5ZM31.43,44.64a12.5,12.5,0,1,1,12.5-12.5A12.51,12.51,0,0,1,31.43,44.64Z"/><path d="M77.08,51.36,54.45,72.87h0l-5.91,5.61L31.38,61.34,10.28,81.27l-2.07,2v5.44a3.85,3.85,0,0,0,3.84,3.84H88a3.85,3.85,0,0,0,3.84-3.84V66.33l-.46-.47Z"/><circle cx="31.43" cy="32.14" r="7.5"/></g></svg>

  {% elsif name == 'pin' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,5A30.4,30.4,0,0,0,23.85,50.91L50,95,76.15,50.91A30.34,30.34,0,0,0,50,5Zm0,44a13.61,13.61,0,1,1,13.6-13.6A13.6,13.6,0,0,1,50,49Z"/></g></svg>

  {% elsif name == 'pinterest' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M86,34.61C86,54.89,74.33,70,57.17,70c-5.76,0-11.18-3-13-6.45,0,0-3.1,11.92-3.76,14.22-2.31,8.12-9.12,16.25-9.64,16.92a.72.72,0,0,1-1.27-.3c-.14-1-1.88-11.29.16-19.64L36.5,46.62a19.32,19.32,0,0,1-1.71-8.19c0-7.66,4.59-13.38,10.3-13.38,4.86,0,7.2,3.54,7.2,7.76,0,4.73-3.11,11.8-4.71,18.35-1.34,5.49,2.84,10,8.42,10,10.12,0,16.93-12.58,16.93-27.49,0-11.32-7.88-19.81-22.22-19.81-16.19,0-26.28,11.7-26.28,24.76A14.6,14.6,0,0,0,28,48.72c1,1.13,1.13,1.59.77,2.88l-1.09,4.15A1.85,1.85,0,0,1,25,57.05C17.49,54.1,14,46.19,14,37.3,14,22.61,26.8,5,52.18,5,72.57,5,86,19.29,86,34.61Z"/></g></svg>

  {% elsif name == 'plus' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="95 46 54 46 54 5 46 5 46 46 5 46 5 54 46 54 46 95 54 95 54 54 95 54 95 46"/></g></svg>

  {% elsif name == 'pound' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M73.86,85.76a13.43,13.43,0,0,1-3.72,1.49,19.41,19.41,0,0,1-14-1.89A31.15,31.15,0,0,0,38,82.15a34.79,34.79,0,0,0,4.73-21.38c-.12-1.22-.29-2.43-.5-3.6H59.5v-8H40a57.45,57.45,0,0,0-5.76-11.23A15.13,15.13,0,0,1,32.9,23.35c3.48-7.66,11.75-10.27,12.6-10.52,6.87-1.35,12.33-.56,16.24,2.33,5.79,4.3,6.62,12.07,6.63,12.15l4-.37,4-.36c0-.46-1.1-11.31-9.76-17.78C60.75,4.43,53.08,3.16,43.8,5l-.21,0c-.52.14-12.7,3.38-18,15a23.08,23.08,0,0,0,1.92,22.28,51.36,51.36,0,0,1,3.8,6.85H22v8H34.09c2,8.92.74,19.43-9.71,29.06l4.46,6.53c.13-.06,12.83-6.1,23.55-.35a28.14,28.14,0,0,0,13.27,3.4,26.52,26.52,0,0,0,6.46-.8,21.5,21.5,0,0,0,5.93-2.44Z"/></g></svg>

  {% elsif name == 'recycle' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M22.81,65,17.7,72.66l6.9,11.5H41V65H22.81Z"/><path d="M31.35,49.23a1,1,0,0,1,.8.29L36,53.37l-4.3-18H9.53L15,38.79a1,1,0,0,1,.45.65,1,1,0,0,1-.16.77L6.17,53.44,16.56,70.76,30.61,49.67A1,1,0,0,1,31.35,49.23Z"/><path d="M50,16.21a.6.6,0,0,1,.09-.13L46.19,7.72,32.77,7.66,24.25,21.71l16.4,9.94Z"/><path d="M59.14,31.53a1,1,0,0,1-.66.54l-5.3,1.3,17.64,5.68,11.51-19L76.52,23a1,1,0,0,1-.78.05,1,1,0,0,1-.58-.54L68.59,7.82,48.4,7.73l10.74,23A1,1,0,0,1,59.14,31.53Z"/><path d="M84.55,38.76,68.73,49.6,78.94,64.49a1,1,0,0,1,.08.14l9.23-.12,5.58-12.2Z"/><path d="M61.21,66.49a1,1,0,0,1-.22-.82l1-5.36L49.55,74l12.54,18.3-.27-6.49a1,1,0,0,1,1-1l16.1.07,8.4-18.36L62,66.85A1.06,1.06,0,0,1,61.21,66.49Z"/></g></svg>

  {% elsif name == 'reddit' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M38.538 50C35.6725 50 33.3334 52.3392 33.3334 55.2047C33.3334 58.0702 35.6725 60.4094 38.538 60.4094C41.4035 60.4094 43.7427 58.0702 43.7427 55.2047C43.7427 52.3392 41.4035 50 38.538 50Z" fill="currentColor"/><path d="M50.0585 72.7485C52.0468 72.7485 58.8304 72.5146 62.3977 68.9474C62.924 68.4211 62.924 67.6023 62.5146 67.0175C61.9883 66.4912 61.1111 66.4912 60.5848 67.0175C58.3041 69.2398 53.5673 70.0585 50.117 70.0585C46.6667 70.0585 41.8714 69.2398 39.6491 67.0175C39.1228 66.4912 38.2456 66.4912 37.7193 67.0175C37.193 67.5439 37.193 68.4211 37.7193 68.9474C41.2281 72.4561 48.0702 72.7485 50.0585 72.7485Z" fill="currentColor"/><path d="M56.2573 55.2047C56.2573 58.0702 58.5965 60.4094 61.462 60.4094C64.3275 60.4094 66.6667 58.0702 66.6667 55.2047C66.6667 52.3392 64.3275 50 61.462 50C58.5965 50 56.2573 52.3392 56.2573 55.2047Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M100 50C100 77.6142 77.6142 100 50 100C22.3858 100 0 77.6142 0 50C0 22.3858 22.3858 0 50 0C77.6142 0 100 22.3858 100 50ZM76.0234 42.6901C80.0585 42.6901 83.3334 45.9649 83.3334 50C83.3334 52.9825 81.5205 55.5555 79.1228 56.7251C79.2398 57.4269 79.2983 58.1287 79.2983 58.8889C79.2983 70.117 66.2573 79.1813 50.117 79.1813C33.9766 79.1813 20.9357 70.117 20.9357 58.8889C20.9357 58.1287 20.9942 57.3684 21.1111 56.6667C18.538 55.4971 16.7837 52.9825 16.7837 50C16.7837 45.9649 20.0585 42.6901 24.0936 42.6901C26.0234 42.6901 27.8363 43.5088 29.1228 44.7368C34.1521 41.0526 41.1111 38.7719 48.8889 38.538L52.5731 21.1111C52.6901 20.7602 52.8655 20.4678 53.1579 20.2924C53.4503 20.117 53.8012 20.0585 54.1521 20.117L66.2573 22.6901C67.076 20.9357 68.8304 19.7661 70.8772 19.7661C73.7427 19.7661 76.0819 22.1053 76.0819 24.9708C76.0819 27.8363 73.7427 30.1754 70.8772 30.1754C68.0702 30.1754 65.7895 27.9532 65.6725 25.2047L54.8538 22.924L51.5205 38.538C59.1228 38.8304 66.0234 41.1696 70.9942 44.7368C72.2807 43.4503 74.0351 42.6901 76.0234 42.6901Z" fill="currentColor"/></svg>

  {% elsif name == 'right-arrow' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="57.09 16.78 51.82 22.8 78.32 46 4.96 46 4.96 54 78.32 54 51.82 77.2 57.09 83.22 95.04 50 57.09 16.78"/></g></svg>

  {% elsif name == 'right-caret' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="28.51 97.85 22.9 92.15 65.7 50 22.9 7.85 28.51 2.15 77.1 50 28.51 97.85"/></g></svg>

  {% elsif name == 'rocket' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M39.37,27A87.83,87.83,0,0,0,25.88,47.25l-.06.12c-6.14-2-12.09,1.13-16.93,3a2.12,2.12,0,0,1-2.6-3C11.18,38.48,22.69,22.15,39.37,27ZM73,60.62A86.34,86.34,0,0,1,52.75,74.11l-.12.06c2,6.15-1.13,12.1-3,16.94a2.12,2.12,0,0,0,3,2.6C61.52,88.83,77.86,77.3,73,60.62Zm-8.5-25.15a8,8,0,1,0,0,11.32A8,8,0,0,0,64.53,35.47ZM88.62,32A69.24,69.24,0,0,1,71,57.09,82.86,82.86,0,0,1,51,70.51a73.63,73.63,0,0,1-8.22,3.38L26.11,57.2A75,75,0,0,1,29.48,49,84,84,0,0,1,42.91,29,69.26,69.26,0,0,1,68,11.37Zm-21.27.67a12,12,0,1,0-4,19.61,11.81,11.81,0,0,0,4-2.63A12,12,0,0,0,67.35,32.64ZM15.48,87.35,26.81,76A2,2,0,0,0,24,73.2L12.66,84.52a2,2,0,0,0,2.82,2.83ZM13.36,76.73,24.68,65.41a2,2,0,0,0-2.83-2.83L10.53,73.9a2,2,0,1,0,2.83,2.83ZM26.1,89.47,37.42,78.15a2,2,0,0,0-2.83-2.83L23.27,86.64a2,2,0,0,0,0,2.83,2,2,0,0,0,2.83,0ZM72.16,9.85l18,18A77.59,77.59,0,0,0,94,6,77.73,77.73,0,0,0,72.16,9.85Z"/></g></svg>

  {% elsif name == 'rss' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M19.11,65.77A14.62,14.62,0,1,0,33.73,80.39,14.63,14.63,0,0,0,19.11,65.77Z"/><path d="M15.11,35.25v8A42.18,42.18,0,0,1,57.25,85.39h8A50.19,50.19,0,0,0,15.11,35.25Z"/><path d="M15.11,5v8A72.47,72.47,0,0,1,87.5,85.39h8A80.48,80.48,0,0,0,15.11,5Z"/></g></svg>

  {% elsif name == 'ruler' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M79.36,80.07,73.53,85.9l-2.12-2.12L77.24,78l-5.5-5.5-3.33,3.33-2.12-2.12,3.33-3.33L65.2,65.9l-5.84,5.84-2.12-2.12,5.84-5.84-5.51-5.5-3.33,3.33-2.12-2.12,3.33-3.33L50.2,50.9l-5.84,5.84-2.12-2.12,5.84-5.84-4.67-4.67-3.34,3.34L38,45.33,41.29,42l-4.43-4.42L31,43.4l-2.12-2.12,5.83-5.83L29.24,30l-3.33,3.33-2.12-2.12,3.33-3.33-5.26-5.26L16,28.4l-2.12-2.12,5.83-5.83L5.5,6.21V94.5H93.79ZM21.17,78.83v-35l35,35Z"/></g></svg>

  {% elsif name == 'search' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M92.83,90.18,71.47,68.83a38.58,38.58,0,1,0-6.29,5l22,22ZM14,41.46A30.47,30.47,0,1,1,44.47,71.93,30.51,30.51,0,0,1,14,41.46Z"/></g></svg>

  {% elsif name == 'shield' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M50,5s-25.26,9.47-34.74,9.47V48a39,39,0,0,0,2.23,13.1A60,60,0,0,0,50,95,60,60,0,0,0,82.51,61.06,39,39,0,0,0,84.74,48V14.47C75.26,14.47,50,5,50,5Zm-3,62.71L31.38,52,37,46.38,46.63,56,64.35,35.54l6,5.24Z"/></g></svg>

  {% elsif name == 'skull-and-crossbones' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M75.48,27.25A28.84,28.84,0,0,1,78,32.19l5.68-5a7.21,7.21,0,0,0,.76,1,6.64,6.64,0,1,0,2-10.49,6.64,6.64,0,1,0-10.61,1.12L76,19l-4.31,3.78A23.28,23.28,0,0,1,75.48,27.25Z"/><path d="M22.94,69.87a12.12,12.12,0,0,1-1.07-3.38l-6.21,5.44a.8.8,0,0,1-.13-.13,6.64,6.64,0,1,0-2,10.48,6.64,6.64,0,1,0,10.61-1.12,6.74,6.74,0,0,0-.93-.85L29,75.2A11.36,11.36,0,0,1,22.94,69.87Z"/><path d="M15.53,28.21a6.51,6.51,0,0,0,.76-1l5.68,5a29.45,29.45,0,0,1,2.55-4.93,22.8,22.8,0,0,1,3.8-4.48L24,19l.12-.14a6.64,6.64,0,1,0-10.61-1.12,6.64,6.64,0,1,0,2,10.49Z"/><path d="M84.47,71.8a.8.8,0,0,1-.13.13l-6.21-5.44a12.36,12.36,0,0,1-1.06,3.37A11.36,11.36,0,0,1,71,75.2l5.82,5.11a7.37,7.37,0,0,0-.93.85,6.64,6.64,0,1,0,10.61,1.12,6.64,6.64,0,1,0-2-10.48Z"/><path d="M73.47,68.12a11.1,11.1,0,0,0,.92-4.92c0-1.45-.12-2.9-.12-4.26,0-1.85.78-3.47,1.19-5.25a44.24,44.24,0,0,0,.91-6.7,29.93,29.93,0,0,0-1.54-12,26.17,26.17,0,0,0-2.71-5.58,19.71,19.71,0,0,0-3.45-4c-6.34-5.63-15.42-6.93-18.66-7h0c-3.23.1-12.31,1.4-18.65,7a19.43,19.43,0,0,0-3.46,4A26.29,26.29,0,0,0,25.18,35a29.81,29.81,0,0,0-1.55,12,44.24,44.24,0,0,0,.91,6.7c.41,1.78,1.19,3.4,1.19,5.25,0,1.36-.13,2.81-.12,4.26a11.1,11.1,0,0,0,.92,4.92c1,2.11,3.55,3.49,5.81,3.94a1.46,1.46,0,0,0,.22,0c1.53.24,3.35-.15,4.77.52a2.54,2.54,0,0,1,1.38,1.75c.31,1-.08,2.73.39,3.58A3,3,0,0,0,40.19,79h0a7.14,7.14,0,0,0,1.39.67h0a0,0,0,0,0,0,0v-.07a1.23,1.23,0,0,1,2.46,0v.93a.08.08,0,0,0,0,.06l.68.21.14,0,.15,0,.26.07.37.1.13,0,.47.12h0l.44.09h0c.38.09.91.17,1.32.22h.12l.48.06a0,0,0,0,0,0,0s0-.23,0-.33v-.07a1.22,1.22,0,0,1,.65-1.09,1.2,1.2,0,0,1,1.16,0,1.22,1.22,0,0,1,.65,1.09v.07c0,.1,0,.33,0,.33a0,0,0,0,0,0,0l.48-.06h.12c.41,0,.94-.13,1.32-.22h0l.44-.09h0l.47-.12.13,0,.37-.1.26-.07.29-.09.68-.21a.08.08,0,0,0,0-.06v-.93a1.23,1.23,0,0,1,2.46,0v.07a0,0,0,0,0,0,0h0a7.14,7.14,0,0,0,1.39-.67h0a3,3,0,0,0,1.09-1.1c.47-.85.08-2.56.39-3.58a2.54,2.54,0,0,1,1.38-1.75c1.42-.67,3.25-.29,4.78-.52l.21,0C69.92,71.61,72.45,70.23,73.47,68.12ZM46.33,60a4.35,4.35,0,0,1-1.83,2.72,13.3,13.3,0,0,1-3.11,1.61l-.07,0a19.47,19.47,0,0,1-3.58,1.25,10.17,10.17,0,0,1-2.12.27h-.18A4.91,4.91,0,0,1,30.71,63a12.21,12.21,0,0,1-.93-3.47c0-.23-.05-.44-.08-.67a16.11,16.11,0,0,1-.11-3l-.05,0a4.38,4.38,0,0,1,.82-2.37s0,0,0,0l.05-.08.28-.37a5.45,5.45,0,0,1,.46-.5,1.21,1.21,0,0,1,.16-.16,1.91,1.91,0,0,1,1.22-.45A3,3,0,0,1,34,52.4a6.58,6.58,0,0,1,.8.54l1.32,1a16.58,16.58,0,0,0,3.09,2,10.88,10.88,0,0,0,2.47.83,18.91,18.91,0,0,1,2.72.35,3.06,3.06,0,0,1,1.91,2.31A2.14,2.14,0,0,1,46.33,60Zm5.92,12.81a1,1,0,0,1-.53.17c-.57,0-1.13-.46-1.72-.48s-1.15.48-1.71.48a1,1,0,0,1-.54-.17c-.85-.58-.52-2-.35-2.79s.39-1.34.59-2a14,14,0,0,1,.86-2.25,3.13,3.13,0,0,1,.46-.84,1.34,1.34,0,0,1,.67-.45H50a1.31,1.31,0,0,1,.66.45,3.47,3.47,0,0,1,.47.84A15.07,15.07,0,0,1,52,68c.19.67.42,1.33.58,2S53.1,72.23,52.25,72.81Zm18-13.94c0,.23,0,.44-.08.67A11.89,11.89,0,0,1,69.29,63a4.91,4.91,0,0,1-4.73,2.89h-.18a10.26,10.26,0,0,1-2.12-.27,19.47,19.47,0,0,1-3.58-1.25h0a14.59,14.59,0,0,1-3.13-1.64A4.44,4.44,0,0,1,53.66,60h0a2.39,2.39,0,0,1,0-.6,3.06,3.06,0,0,1,1.91-2.32,18.91,18.91,0,0,1,2.72-.35,10.41,10.41,0,0,0,2.47-.83,16.58,16.58,0,0,0,3.09-2l1.32-1a7.46,7.46,0,0,1,.8-.54,3,3,0,0,1,1.48-.47,1.91,1.91,0,0,1,1.22.45,1.21,1.21,0,0,1,.16.16,4.51,4.51,0,0,1,.46.51l.29.37,0,.07s0,0,0,0a4.33,4.33,0,0,1,.82,2.37l0,0A17,17,0,0,1,70.3,58.87Z"/></svg>

  {% elsif name == 'skull' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M82.53,19.74C73.92,6.41,55.75,3.66,50,3.5h0c-5.73.16-23.9,2.91-32.51,16.24a42.39,42.39,0,0,0-6.26,25.84,66.77,66.77,0,0,0,1.34,9.86c.62,2.61,1.75,5,1.75,7.71,0,4.39-.83,9.4,1.18,13.5,1.5,3.1,5.23,5.13,8.54,5.79,2.31.46,5.16-.22,7.35.81a3.79,3.79,0,0,1,2,2.58c.47,1.49-.11,4,.58,5.27a4.54,4.54,0,0,0,1.61,1.62h0a11.53,11.53,0,0,0,2,1l0,0h0a.06.06,0,0,0,.06-.06v-.09h0v0a1.81,1.81,0,0,1,3.62,0v1.36h0s0,.07.06.08l1,.32.42.12.38.11.55.15.2,0,.68.17h0l.64.14h0a16.16,16.16,0,0,0,1.93.33l.18,0,.7.08h0s.06,0,0,0,0-.34,0-.49v-.11A1.82,1.82,0,0,1,50,94h0a1.73,1.73,0,0,1,.85.22,1.8,1.8,0,0,1,1,1.59V96c0,.15,0,.49,0,.49s0,0,0,0h0l.7-.08.18,0a16.16,16.16,0,0,0,1.93-.33h0l.64-.14h0l.68-.17.2,0,.55-.15.38-.11c.14,0,.28-.08.42-.12l1-.32a.09.09,0,0,0,0-.08h0V93.55a1.81,1.81,0,0,1,3.62,0v0h0v.09a.06.06,0,0,0,0,.06h0l0,0a11.3,11.3,0,0,0,2-1h0A4.62,4.62,0,0,0,66,91.1c.69-1.26.11-3.78.58-5.27a3.82,3.82,0,0,1,2-2.58c2.19-1,5-.35,7.35-.81,3.31-.66,7-2.69,8.54-5.79,2-4.1,1.19-9.11,1.18-13.5,0-2.72,1.13-5.1,1.75-7.71a66.77,66.77,0,0,0,1.34-9.86A42.39,42.39,0,0,0,82.53,19.74Zm-40.62,49a22,22,0,0,1-4.67,2.43A28.87,28.87,0,0,1,32,73a15.76,15.76,0,0,1-3.12.39,7.29,7.29,0,0,1-7.23-4.23A19.68,19.68,0,0,1,20.14,63,24.43,24.43,0,0,1,20,58.62l-.06,0a6.28,6.28,0,0,1,1.21-3.48s0,0,0,0a.76.76,0,0,1,.08-.11c.12-.18.27-.37.41-.55a6,6,0,0,1,.68-.74,1.75,1.75,0,0,1,.23-.24c1.34-1.07,2.74-.68,4,0a12.75,12.75,0,0,1,1.18.79,37.48,37.48,0,0,0,6.49,4.34,14.72,14.72,0,0,0,3.64,1.22,26.86,26.86,0,0,1,4,.52,4.46,4.46,0,0,1,2.8,3.4C44.82,65.8,43.43,67.58,41.91,68.71ZM52.52,78c-.83.57-1.65-.32-2.52-.35h0c-.87,0-1.68.92-2.52.35s-.58-2.22-.38-3.12c.18-.75.43-1.49.65-2.24a17.16,17.16,0,0,1,1-2.52,3.46,3.46,0,0,1,.52-.94c.14-.16.5-.49.74-.5H50c.24,0,.6.34.74.5a3.46,3.46,0,0,1,.52.94,16.1,16.1,0,0,1,1,2.52c.22.75.48,1.49.65,2.24C53.1,75.75,53.46,77.32,52.52,78ZM79.86,63a19.4,19.4,0,0,1-1.48,6.1,7.29,7.29,0,0,1-7.23,4.23A15.76,15.76,0,0,1,68,73a28.87,28.87,0,0,1-5.27-1.84,21.59,21.59,0,0,1-4.67-2.43c-1.52-1.13-2.91-2.91-2.72-4.91a4.46,4.46,0,0,1,2.8-3.4,26.86,26.86,0,0,1,4-.52,15.27,15.27,0,0,0,3.64-1.21,38.11,38.11,0,0,0,6.49-4.35,12.75,12.75,0,0,1,1.18-.79c1.23-.71,2.63-1.1,4,0,.08.06.15.16.24.24a6.78,6.78,0,0,1,.67.74l.41.55a.76.76,0,0,1,.08.11s0,0,0,0a6.39,6.39,0,0,1,1.22,3.48L80,58.62A24.43,24.43,0,0,1,79.86,63Z"/></g></svg>

  {% elsif name == 'slash' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="3.22" y="46" width="93.56" height="8" transform="translate(-14.42 79.15) rotate(-69.34)"/></g></svg>

  {% elsif name == 'slider' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="16.84 5 8.84 5 8.84 27.63 5.66 27.63 5.66 39.63 8.84 39.63 8.84 95 16.84 95 16.84 39.63 20.02 39.63 20.02 27.63 16.84 27.63 16.84 5"/><polygon points="41.72 5 33.72 5 33.72 62.88 30.43 62.88 30.43 74.88 33.72 74.88 33.72 95 41.72 95 41.72 74.88 44.8 74.88 44.8 62.88 41.72 62.88 41.72 5"/><polygon points="66.5 5 58.5 5 58.5 32.85 55.2 32.85 55.2 44.85 58.5 44.85 58.5 95 66.5 95 66.5 44.85 69.57 44.85 69.57 32.85 66.5 32.85 66.5 5"/><polygon points="94.34 53.74 91.27 53.74 91.27 5 83.27 5 83.27 53.74 79.98 53.74 79.98 65.74 83.27 65.74 83.27 95 91.27 95 91.27 65.74 94.34 65.74 94.34 53.74"/></g></svg>

  {% elsif name == 'snapchat' %}

    <svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M87.4094 64.2583C86.8401 62.786 85.7361 61.5917 84.325 60.9219L83.6545 60.5587L82.4476 59.9459C78.6487 58.006 75.3728 55.1545 72.9041 51.6387C72.2741 50.7068 71.7281 49.7193 71.2725 48.688C71.0937 48.1433 71.0937 47.8255 71.2278 47.5532C71.3619 47.3262 71.5407 47.1219 71.7642 46.9857C73.0742 46.0873 74.3929 45.2021 75.7202 44.3302C77.2007 43.3455 78.4271 42.0145 79.2962 40.4489C79.9339 39.2652 80.2509 37.9309 80.2149 36.5821C80.1789 35.2333 79.7912 33.9184 79.0913 32.7715C78.3914 31.6246 77.4044 30.6868 76.2311 30.0538C75.0577 29.4208 73.74 29.1153 72.4123 29.1684C71.5212 29.1697 70.6343 29.2919 69.775 29.5316C69.7974 27.4888 69.775 25.3099 69.5739 23.199C69.3505 19.2178 68.147 15.3571 66.0735 11.9706C64.0001 8.58418 61.123 5.78021 57.7059 3.8156C53.5205 1.42088 48.7864 0.191583 43.9828 0.252133C38.954 0.252133 34.3722 1.45509 30.2821 3.8156C26.8555 5.77094 23.9696 8.57211 21.8913 11.9601C19.813 15.3481 18.6094 19.2136 18.3918 23.199C18.213 25.3326 18.1683 27.5115 18.213 29.5316C17.3393 29.2878 16.4371 29.1657 15.531 29.1684C14.2058 29.1224 12.8922 29.4325 11.7228 30.0673C10.5535 30.7021 9.56988 31.6391 8.87142 32.7837C8.17296 33.9283 7.7844 35.2398 7.74502 36.5857C7.70563 37.9316 8.01681 39.2643 8.64711 40.4489C9.52042 42.0248 10.7547 43.3639 12.2455 44.3529L14.2794 45.7147L16.1344 46.9403C16.3579 47.0992 16.5814 47.3035 16.7155 47.5532C16.872 47.8482 16.872 48.166 16.6708 48.7561C16.2238 49.7775 15.6651 50.7308 15.0393 51.6614C12.6395 55.0942 9.46716 57.8956 5.78628 59.8324C3.66301 60.9672 1.47268 61.7163 0.556324 64.2583C-0.136533 66.1876 0.332822 68.3892 2.09849 70.2277C2.74665 70.9086 3.48421 71.4988 4.33352 71.9527C6.05448 72.906 7.8872 73.655 9.78697 74.177C10.1849 74.2772 10.5629 74.4461 10.9045 74.6764C11.5526 75.2438 11.4409 76.1063 12.3126 77.4C12.7596 78.0356 13.296 78.603 13.9218 79.0569C15.7321 80.328 17.766 80.4188 19.9116 80.4869C21.8561 80.555 24.0464 80.6458 26.572 81.4855C27.6225 81.826 28.6953 82.5069 29.9469 83.3013C34.0691 86.2036 38.9669 87.7559 43.9828 87.75C50.8667 87.75 55.0239 85.1625 58.0635 83.2786C59.1064 82.5603 60.223 81.9591 61.3937 81.4855C63.8969 80.6458 66.0872 80.5777 68.0541 80.4869C70.1997 80.3961 72.2335 80.328 74.0439 79.0569C74.7815 78.5349 75.4296 77.8313 75.8766 77.0142C76.5024 75.9474 76.4801 75.1984 77.0612 74.6764C77.3741 74.4494 77.7317 74.2905 78.0893 74.1997C80.0338 73.6777 81.8889 72.9287 83.6322 71.9527C84.5217 71.4758 85.3232 70.8461 86.0013 70.0915L86.0236 70.0688C87.6999 68.2531 88.1246 66.1195 87.4317 64.2583H87.4094Z" fill="currentColor" stroke="currentColor" stroke-width="0.5"/>
    </svg>

  {% elsif name == 'snowflake' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="74.98 41.22 87.83 46.97 91.09 39.66 83.92 36.46 91.59 32.37 87.83 25.31 80.16 29.4 81.5 21.66 73.61 20.29 71.21 34.16 54 43.34 54 23.83 65.11 15.19 60.2 8.87 54 13.7 54 5 46 5 46 13.7 39.8 8.87 34.89 15.19 46 23.83 46 42.96 29.57 33.16 27.84 19.19 19.9 20.17 20.87 27.97 13.4 23.52 9.3 30.39 16.77 34.84 9.45 37.7 12.36 45.15 25.48 40.03 41.86 49.8 25.02 58.78 12.17 53.03 8.91 60.34 16.08 63.54 8.4 67.63 12.17 74.69 19.84 70.6 18.5 78.34 26.39 79.71 28.79 65.84 46 56.66 46 76.17 34.89 84.81 39.8 91.13 46 86.3 46 95 54 95 54 86.3 60.2 91.13 65.11 84.81 54 76.17 54 57.04 70.43 66.84 72.16 80.81 80.1 79.83 79.13 72.03 86.6 76.48 90.7 69.61 83.23 65.16 90.55 62.3 87.64 54.85 74.52 59.97 58.14 50.2 74.98 41.22"/></g></svg>

  {% elsif name == 'spotify' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M79.5763 44.3261C63.4593 34.7545 36.8745 33.8745 21.4888 38.5441C19.0182 39.2934 16.4055 37.8987 15.6567 35.428C14.908 32.9562 16.3016 30.3452 18.774 29.5941C36.4357 24.2331 65.7956 25.2684 84.3498 36.2824C86.5727 37.6019 87.3017 40.472 85.984 42.6907C84.6657 44.913 81.7932 45.6456 79.5763 44.3261ZM79.0485 58.5028C77.9183 60.3376 75.5193 60.9126 73.6869 59.7865C60.25 51.5267 39.7607 49.1343 23.8639 53.9597C21.8023 54.5825 19.6248 53.42 18.9991 51.3625C18.3775 49.3008 19.5406 47.1275 21.5981 46.5006C39.7583 40.9903 62.3332 43.6592 77.766 53.1435C79.5984 54.272 80.1752 56.6722 79.0485 58.5028ZM72.9304 72.1177C72.0325 73.5906 70.1135 74.0528 68.6459 73.1548C56.9041 65.9786 42.1257 64.3576 24.7213 68.3335C23.0442 68.718 21.373 67.6666 20.9903 65.99C20.6058 64.3128 21.653 62.6417 23.3338 62.2589C42.38 57.9045 58.718 59.7787 71.8969 67.8319C73.3663 68.7293 73.829 70.6489 72.9304 72.1177ZM50.0003 0C22.3862 0 0 22.3852 0 49.9994C0 77.6154 22.3862 100 50.0003 100C77.615 100 100 77.6154 100 49.9994C100 22.3852 77.615 0 50.0003 0Z" fill="currentColor"/></svg>


  {% elsif name == 'square' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><rect x="5" y="5" width="90" height="90"/></g></svg>

  {% elsif name == 'star' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M94.59,37.25,73.41,59.37,83,90.15A1.43,1.43,0,0,1,82,92a1,1,0,0,1-.45.08h-.06a2.18,2.18,0,0,1-.57-.15L50,77,19,91.89a1.46,1.46,0,0,1-1.94-.67A1.42,1.42,0,0,1,17,90.15l9.56-30.78L5.4,37.25a1.45,1.45,0,0,1,0-2,1.42,1.42,0,0,1,1-.4H35.64l13.07-26a1.45,1.45,0,0,1,1.94-.66,1.48,1.48,0,0,1,.65.66l13.07,26H93.55a1.44,1.44,0,0,1,1,2.45Z"/></g></svg>

  {% elsif name == 'sun' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><circle cx="50" cy="50" r="20.36"/><rect x="46" y="5" width="8" height="18.41"/><rect x="76.59" y="46" width="18.41" height="8"/><rect x="66.36" y="20.43" width="18.41" height="8" transform="translate(4.41 59.7) rotate(-44.28)"/><rect x="71.57" y="66.36" width="8" height="18.41" transform="translate(-31.3 76.43) rotate(-45.46)"/><rect x="46" y="76.59" width="8" height="18.41"/><rect x="5" y="46" width="18.41" height="8"/><rect x="15.23" y="71.57" width="18.41" height="8" transform="translate(-45.82 38.53) rotate(-44.28)"/><rect x="20.43" y="15.23" width="8" height="18.41" transform="translate(-10.12 24.71) rotate(-45.46)"/></g></svg>

  {% elsif name == 'sun-2' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M95,64.62,83.2,50,95,35.37,76.85,30.49l1-18.77-17.54,6.7L50,2.69,39.73,18.42l-17.55-6.7,1,18.77L5,35.37,16.79,50,5,64.62l18.14,4.89L22.2,88.28l17.54-6.7L50,97.31,60.27,81.58l17.55,6.7-1-18.77Zm-45,11A25.62,25.62,0,1,1,75.63,50,25.65,25.65,0,0,1,50,75.62ZM70.63,50A20.63,20.63,0,1,1,50,29.38,20.64,20.64,0,0,1,70.63,50Z"/></g></svg>

  {% elsif name == 'tag' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M96.75,9.24,91.09,3.58l-15.5,15.5-1.25-.18A44.7,44.7,0,0,0,68,18.45H49.69A15.86,15.86,0,0,0,38.46,23.1L4.86,56.69a5.49,5.49,0,0,0,0,7.76L35.22,94.8a5.46,5.46,0,0,0,7.75,0L77.24,60.54a15.88,15.88,0,0,0,4.65-11.23V31.51a45.33,45.33,0,0,0-.54-6.87ZM61.83,46.53a8,8,0,0,1,0-16,7.93,7.93,0,0,1,5.59,2.29l.14.13a8,8,0,0,1-5.73,13.63Z"/></g></svg>

  {% elsif name == 'tiktok' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M52.9749 0H72.2473C72.2473 0 71.1479 21.9414 98.9569 23.5497V40.494C98.9569 40.494 84.0823 41.2981 72.2473 33.2568L72.4413 68.2941C72.4413 74.5674 70.3459 80.6997 66.4204 85.9149C62.4949 91.1302 56.9156 95.194 50.3887 97.592C43.8618 99.9901 36.6805 100.615 29.7538 99.3865C22.8271 98.1585 16.4663 95.1331 11.4763 90.6932C6.48623 86.2533 3.09133 80.5985 1.72119 74.4443C0.351042 68.2902 1.06724 61.9134 3.77914 56.1209C6.49104 50.3284 11.0767 45.3806 16.9559 41.9036C22.8351 38.4267 29.7435 36.5768 36.8069 36.5882H41.7866V53.992C38.5375 53.0978 35.0535 53.1365 31.8305 54.1026C28.6076 55.0688 25.8098 56.9131 23.8354 59.3732C21.8609 61.8333 20.8105 64.7839 20.8334 67.8049C20.8563 70.8259 21.9515 73.7636 23.9631 76.1998C25.9747 78.6361 28.8002 80.4468 32.0375 81.3743C35.2748 82.3017 38.7591 82.2987 41.9944 81.3657C45.2297 80.4327 48.0513 78.6171 50.0575 76.1775C52.0638 73.7378 53.1526 70.7982 53.169 67.7772L52.9749 0Z" fill="currentColor"/></svg>

  {% elsif name == 'tree' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="65.6 54.78 74.08 54.78 56.74 28.21 65.15 28.21 50 5 34.85 28.21 43.26 28.21 25.92 54.78 34.4 54.78 17.07 81.34 46.06 81.34 46.06 95 50 95 53.94 95 53.94 81.34 82.93 81.34 65.6 54.78"/></g></svg>

  {% elsif name == 'triangle' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="50 11.03 72.5 50 95 88.97 50 88.97 5 88.97 27.5 50 50 11.03"/></g></svg>

  {% elsif name == 'truck' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M26.39,65.23a5.82,5.82,0,0,0-5.73,4.88,5,5,0,0,0-.09,1v0a5.47,5.47,0,0,0,.1,1,5.82,5.82,0,0,0,11.45,0,5.47,5.47,0,0,0,.1-1v0a5,5,0,0,0-.09-1A5.84,5.84,0,0,0,26.39,65.23Z"/><path d="M72.37,65.23a5.84,5.84,0,0,0-5.74,4.83,5.6,5.6,0,0,0,0,2,5.83,5.83,0,0,0,11.48,0,5.6,5.6,0,0,0,0-2A5.84,5.84,0,0,0,72.37,65.23Z"/><path d="M88.13,48.94l-.27-.47-9.73-17a1,1,0,0,0-.87-.5H59V24.11a1,1,0,0,0-1-1H11a1,1,0,0,0-1,1v47a1,1,0,0,0,1,1h5.63a8.26,8.26,0,0,1-.06-1v0c0-.32,0-.64.05-1a9.82,9.82,0,0,1,19.55,0c0,.31,0,.63,0,1v0a8.26,8.26,0,0,1-.06,1H58a1,1,0,0,0,.3,0h4.29c0-.33-.05-.66-.05-1s0-.67.05-1a9.83,9.83,0,0,1,19.56,0c0,.33,0,.66,0,1s0,.67,0,1H89a1,1,0,0,0,1-1v-19ZM69.93,44a.44.44,0,0,1,.44-.43H80.91l5.48,9.32h-16a.44.44,0,0,1-.44-.44Z"/></g></svg>

  {% elsif name == 'tumblr' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M71.25,76.57a10.64,10.64,0,0,1-7.75,3.07c-5.07,0-7.34-3.07-7.34-7.61V46.31H72.57V30.72H56.16V5H43.8A34.83,34.83,0,0,1,23.94,30.87V46.31H36V75.93C36,80,39.87,95,59.63,95c11.62,0,16.43-7.48,16.43-7.48Z"/></g></svg>

  {% elsif name == 'twitch' %}

    <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.913 0L6.25 17.8571V82.1429H27.4457V100L45.1087 82.1429H59.2391L91.0326 50V0H23.913ZM83.9674 46.4286L69.837 60.7143H55.7065L43.3424 73.2143V60.7143H27.4457V7.14286H83.9674V46.4286Z" fill="currentColor"/><path d="M73.3696 19.6428H66.3044V41.0714H73.3696V19.6428Z" fill="currentColor"/><path d="M53.9402 19.6428H46.875V41.0714H53.9402V19.6428Z" fill="currentColor"/></svg>


  {% elsif name == 'x-social' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
      <path d="M59.5135833,42.3214344 L96.7408333,0 L87.9191667,0 L55.59475,36.7471068 L29.7773333,0 L0,0 L39.041,55.5681337 L0,99.9486553 L8.82216667,99.9486553 L42.9575833,61.1424613 L70.2226667,99.9486553 L100,99.9486553 L59.5114167,42.3214344 L59.5135833,42.3214344 Z M47.4304167,56.0577017 L43.47475,50.5243684 L12.0009167,6.49506112 L25.55125,6.49506112 L50.951,42.0281174 L54.9066667,47.5614507 L87.9233333,93.7489813 L74.373,93.7489813 L47.4304167,56.0598207 L47.4304167,56.0577017 Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'umbrella' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M85.59,47A35.59,35.59,0,0,0,54,11.65V5H46v6.65A35.59,35.59,0,0,0,14.41,47H46v34.6a5.39,5.39,0,0,1-10.78,0h-8a13.39,13.39,0,0,0,26.78,0V47Z"/></g></svg>

  {% elsif name == 'up-arrow' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="83.22 42.91 50 4.96 16.78 42.91 22.8 48.18 46 21.68 46 95.04 54 95.04 54 21.68 77.2 48.18 83.22 42.91"/></g></svg>

  {% elsif name == 'up-caret' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="92.15 77.1 50 34.3 7.85 77.1 2.15 71.49 50 22.9 97.85 71.49 92.15 77.1"/></g></svg>

  {% elsif name == 'vimeo' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M92.67,14.61c-4.21-5.42-13.24-4.51-16-4.07-4.2.76-17.44,6.93-22.1,20.92L54,33.56h2.4c4.5-.45,6.92,0,8.11,1.2s1.5,3,1.21,6.17c-.3,3.46-2.11,7.36-3.91,10.67l-.16.3c-1.35,2.56-3.9,7.37-6.46,7.67-1,.15-2.11-.44-3.15-1.65-3.46-3.76-4.22-10.53-4.82-16.54-.29-2.1-.45-3.92-.75-5.71L46.21,34a96.46,96.46,0,0,0-2.42-11.42C42.58,18.82,39.89,14,36.12,13c-3.61-1.06-8.12.29-11,2.1A98.78,98.78,0,0,0,11.47,25.89C9.8,27.54,8,29.19,6.19,30.7A3.13,3.13,0,0,0,5,33.11a4.3,4.3,0,0,0,.59,1.79c.16.16.16.31.31.46a4.86,4.86,0,0,0,3.46,2.86,11.4,11.4,0,0,0,5.57-.76c2.85-.89,4.05-1.2,5.25.9a30.46,30.46,0,0,1,2.4,6.47,28.9,28.9,0,0,0,1.06,3.31c1.51,4.51,2.71,9.32,4.07,14.6l.44,1.94c2.11,8.88,5.11,21.06,12.78,24.37a8.23,8.23,0,0,0,3.76.75c3.77,0,7.82-1.81,10.09-3.16C63.64,81.23,70,73.71,74.61,67.54c12.64-17.29,19.1-36.39,20-41.36S94.77,17.32,92.67,14.61Z"/></g></svg>

  {% elsif name == 'whatsapp' %}

    <svg width="100" height="97" viewBox="0 0 100 97" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M0.419355 47.9516C0.429032 21.5129 22.7645 0 50.2097 0C63.529 0.00322581 76.029 5.00323 85.429 14.0677C94.8322 23.129 100.006 35.1839 100 47.9903C99.9903 74.429 77.6548 95.9452 50.2097 95.9452H50.1903C41.9038 95.9508 33.7394 93.9473 26.3968 90.1064L0 96.7742L7.06452 71.9258C2.70968 64.6581 0.412903 56.4064 0.419355 47.9516ZM72.9096 57.9903C71.6645 57.3935 65.5484 54.4935 64.4064 54.0903C63.2677 53.6935 62.4387 53.4871 61.6096 54.6871C60.7838 55.8871 58.3967 58.5871 57.6742 59.3839C56.9451 60.1839 56.2193 60.2806 54.9742 59.6839C54.7709 59.5859 54.4938 59.4702 54.1519 59.3274C52.3999 58.5958 48.944 57.1525 44.9709 53.7355C41.2709 50.5613 38.7742 46.6387 38.0484 45.4355C37.3226 44.2387 37.9709 43.5903 38.5935 42.9935C38.9731 42.6315 39.4124 42.1157 39.8495 41.6026C40.055 41.3613 40.26 41.1206 40.458 40.8968C40.9782 40.3089 41.2071 39.8621 41.5155 39.2601C41.5743 39.1453 41.636 39.0249 41.7032 38.8968C42.1161 38.1 41.9096 37.3968 41.5967 36.8C41.3996 36.4199 40.3405 33.9381 39.3233 31.5545C38.7261 30.1552 38.1434 28.7898 37.758 27.9032C36.8693 25.8409 35.9655 25.8437 35.2398 25.8459C35.1437 25.8462 35.0507 25.8464 34.9613 25.8419C34.2355 25.8097 33.4096 25.8 32.5742 25.8C31.7516 25.8 30.4 26.1 29.258 27.3C29.1838 27.3782 29.1007 27.4637 29.0102 27.5568C27.7135 28.8906 24.9032 31.7813 24.9032 37.2935C24.9032 43.1631 29.3154 48.8315 29.9739 49.6775L29.9838 49.6903C30.0255 49.7437 30.101 49.8478 30.2093 49.9972C31.7178 52.0778 39.594 62.9414 51.2387 67.7871C54.2064 69.0161 56.5226 69.7548 58.3322 70.3097C61.3129 71.2226 64.0258 71.0903 66.1677 70.7839C68.5548 70.4387 73.529 67.8871 74.5613 65.0903C75.6 62.2935 75.6 59.8935 75.2903 59.3935C75.0472 58.9926 74.4638 58.7203 73.6002 58.3171C73.3869 58.2174 73.1564 58.1098 72.9096 57.9903Z" fill="currentColor"/>
    </svg>

  {% elsif name == 'wrench' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M94.26,29.06A18.81,18.81,0,0,1,71,41.93L41.9,71A18.81,18.81,0,0,1,17.05,93.7a1.52,1.52,0,0,1-.55-.35,1.56,1.56,0,0,1,0-2.21l9.83-9.83-2.45-4.85-4.35-2L9.33,84.65A1.6,1.6,0,0,1,8,85.08a1.62,1.62,0,0,1-1.15-.87A18.83,18.83,0,0,1,29.65,58.26L58.29,29.64A18.81,18.81,0,0,1,84.24,6.85a1.43,1.43,0,0,1,.44.3,1.56,1.56,0,0,1,0,2.21L75.14,18.9l2.45,4.86,4.35,2,9.24-9.23a1.23,1.23,0,0,1,.53-.35,1.56,1.56,0,0,1,2,.88A18.87,18.87,0,0,1,94.26,29.06Z"/></g></svg>

  {% elsif name == 'x' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="97.83 7.83 92.17 2.17 50 44.34 7.83 2.17 2.17 7.83 44.34 50 2.17 92.17 7.83 97.83 50 55.66 92.17 97.83 97.83 92.17 55.66 50 97.83 7.83"/></g></svg>

  {% elsif name == 'yen' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><polygon points="54 43.19 81.7 10.91 75.63 5.7 50 35.57 24.37 5.7 18.3 10.91 46 43.19 46 43.71 25.84 43.71 25.84 51.71 46 51.71 46 59.03 25.84 59.03 25.84 67.03 46 67.03 46 94.3 54 94.3 54 67.03 74.17 67.03 74.17 59.03 54 59.03 54 51.71 74.17 51.71 74.17 43.71 54 43.71 54 43.19"/></g></svg>

  {% elsif name == 'youtube' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M79.77,18.85H20.23A15.22,15.22,0,0,0,5,34.08V65.92A15.22,15.22,0,0,0,20.23,81.15H79.77A15.22,15.22,0,0,0,95,65.92V34.08A15.22,15.22,0,0,0,79.77,18.85Zm-26,38.09L42.36,62.81a1.41,1.41,0,0,1-2-1.26V38.45a1.41,1.41,0,0,1,2-1.26l11.45,5.87,11.06,5.69a1.4,1.4,0,0,1,0,2.5Z"/></g></svg>

  {% elsif name == 'zoom' %}

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g><path d="M92.83,90.18,71.47,68.83a38.58,38.58,0,1,0-6.29,5l22,22ZM14,41.46A30.47,30.47,0,1,1,44.47,71.93,30.51,30.51,0,0,1,14,41.46Z"/><polygon points="48.47 20.31 40.47 20.31 40.47 37.46 23.32 37.46 23.32 45.46 40.47 45.46 40.47 62.61 48.47 62.61 48.47 45.46 65.62 45.46 65.62 37.46 48.47 37.46 48.47 20.31"/></g></svg>

  {% elsif name == 'video-thumbnail' %}

    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 25H25V1H1V25Z" fill="{{ settings.shop_bg_color }}"/><path class="media-badge__outline" d="M0.5 25V25.5H1H25H25.5V25V1V0.5H25H1H0.5V1V25Z" stroke="{{ settings.regular_color }}" stroke-opacity="0.05"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8.19995 5.8V20.2L19.3999 12.5858L8.19995 5.8Z" fill="{{ settings.regular_color }}" fill-opacity="0.6"/></svg>

  {% elsif name == '3d-thumbnail' %}

  <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 25H25V1H1V25Z" fill="{{ settings.shop_bg_color }}"/><path class="media-badge__outline" d="M0.5 25V25.5H1H25H25.5V25V1V0.5H25H1H0.5V1V25Z" stroke="{{ settings.regular_color }}" stroke-opacity="0.05"/><g opacity="0.6"><path fill-rule="evenodd" clip-rule="evenodd" d="M13 6L19.0622 9.5V16.5L13 20L6.93782 16.5V9.5L13 6Z" stroke="{{ settings.regular_color }}" stroke-width="1.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13 20V12.6024C13.6225 12.2002 13.6225 12.2002 13.6225 12.2002L19 9V16.4082L13 20Z" fill="{{ settings.regular_color }}"/></g></svg>

  {% else %}
    <p>?</p>
  {% endif %}
</span>
