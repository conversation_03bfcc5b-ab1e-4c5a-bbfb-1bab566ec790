{%- comment -%}
  Updated: 09/01/2023
  This file is system-generated and should not be modified. We reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost
{%- endcomment -%}
{% assign zp_template_suffix = template.suffix %}{% if zp_template_suffix contains 'zipifypages' %}{% assign zp_entity_marker = template.name %}{% if zp_entity_marker == 'article' %}{% assign zp_current_entity = article %}{% elsif zp_entity_marker == 'blog' %}{% assign zp_current_entity = blog %}{% elsif zp_entity_marker == 'page' %}{% assign zp_current_entity = page %}{% elsif zp_entity_marker == 'product' %}{% assign zp_current_entity = product %}{% else %}{% assign zp_current_entity = nil %}{% endif %}{% if zp_current_entity != blank %}{% assign zp_current_entity_metafields = zp_current_entity.metafields['zipifypages'] %}{% if zp_current_entity_metafields['splittest'] != blank and zp_current_entity_metafields['splittest'].type == 'json' %}{% assign zp_split_test_data = zp_current_entity_metafields['splittest'].value %}{% assign zp_split_test_type = zp_split_test_data.type %}{% if zp_split_test_type == 'redirect' or zp_split_test_type == 'viewtyperedirect' %}{% assign zp_alternative_entity_handle = '' | append: zp_split_test_data.alternative_handle %}{% assign zp_split_dataset = zp_split_test_data.dataset | json %}{% assign zp_split_dataset = '' | append: zp_split_dataset %}{% assign zp_split_data_string = '{' %}{% assign zp_replacement_value = zp_entity_marker | json %}{% assign zp_split_data_string = zp_split_data_string | append: 'entityMarker:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_split_test_data.token | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',testToken:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_split_test_type | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',testType:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_current_entity.handle | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',handles:{main:' | append: zp_replacement_value %}{% assign zp_view_type_url_param_name = 'view' %}{% if zp_split_test_type == 'viewtyperedirect' %}{% assign zp_replacement_value = zp_current_entity.handle | append: '?' | append: zp_view_type_url_param_name | append: '=' | append: zp_split_test_data.view_type | json %}{% else %}{% assign zp_replacement_value = zp_alternative_entity_handle | json %}{% endif %}{% assign zp_split_data_string = zp_split_data_string | append: ',alternative:' | append: zp_replacement_value %}{% assign zp_replacement_value = zp_split_dataset | json %}{% assign zp_split_data_string = zp_split_data_string | append: '},dataSet:' | append: zp_replacement_value %}{% if zp_split_test_type == 'viewtyperedirect' %}{% assign zp_replacement_value = zp_split_test_data.view_type | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',viewType:' | append: zp_replacement_value %}{% endif %}{% assign zp_replacement_value = routes.root_url | json %}{% assign zp_split_data_string = zp_split_data_string | append: ',locale:{root:' | append: zp_replacement_value | append: '}}' %}{% assign zp_replacement_value = nil %}{% assign zp_split_dataset = nil %}<script>!function(t){function e(t){this.defaultVariation="a";try{this.data=JSON.parse(t)}catch(t){this.data=null}}function s(){}function n(){this.storage=new s}function a(t,e){this.mainState="main",this.scopes={"article":"articles","blog":"blogs","page":"pages","product":"products"},this.testToken=""+e,this.entityType=""+t,this.availableStates={a:"main",b:"alternative"},this.stateStorage=new n,this.state=this.getStoredState()}function i(t){this.localeData=t}function r(t){this.locale=new i(t)}function o(t){t=t||{};this.state=t.state,this.scope=t.scope,this.handles=t.handles||{},this.titles=t.titles||{},this.styles=t.styles||{},this.wrapperClasses=t.wrapperClasses||{},this.fonts=t.fonts||{},this.fontsCss=t.fontsCss||{},this.analyticsSettings=t.analyticsSettings||{},this.route=new r(t.localeData)}function h(){}e.prototype.selectVariation=function(){if(!this.data)return this.defaultVariation;try{var t,e=[];for(r in this.data)this.data.hasOwnProperty(r)&&(t=parseFloat(this.data[r]),isNaN(t)||e.push([t,r]));if(1<e.length){for(var s,n,a=0,i=null,r=0,o=e.length;r<o;r++)a+=e[r][0];for(n=Math.random()*a,r=0,o=e.length;r<o;r++){if(n<=(s=e[r][0])){i=e[r][1];break}n-=s}return i||this.defaultVariation}return this.defaultVariation}catch(t){return console.error("selectVariation error",t),this.defaultVariation}},s.prototype.getItem=function(t){t=document.cookie.match(new RegExp("(?:^|; )"+t.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g,"\\$1")+"=([^;]*)"));return t?decodeURIComponent(t[1]):void 0},s.prototype.setItem=function(t,e,s){var n,a=(s=s||{}).expires;"number"==typeof a&&a&&((n=new Date).setTime(n.getTime()+1e3*a),a=s.expires=n),a&&a.toUTCString&&(s.expires=a.toUTCString());var i,r,o=t+"="+(e=encodeURIComponent(e));for(i in s)s.hasOwnProperty(i)&&(o+="; "+i,!0!==(r=s[i])&&(o+="="+r));document.cookie=o},s.prototype.removeItem=function(t){this.setItem(t,"",{expires:-1})},n.prototype.getItem=function(){return this.storage.getItem.apply(this.storage,arguments)},n.prototype.setItem=function(){this.storage.setItem.apply(this.storage,arguments)},a.prototype.getAvailableState=function(t){return this.availableStates[t]||this.mainState},a.prototype._getStoredKey=function(){return"zpCurrentState"+this.entityType+this.testToken},a.prototype.getState=function(){return this.state},a.prototype.setState=function(t){return this.state=t,this.stateStorage.setItem(this._getStoredKey(),this.state, {expires:31536e3}),this.state},a.prototype.setMainState=function(){return this.setState(this.mainState)},a.prototype.getStoredState=function(){return this.stateStorage.getItem(this._getStoredKey())},a.prototype.currentScope=function(){return this.scopes[this.entityType]},a.prototype.isMainState=function(){return this.getState()===this.mainState},a.prototype.getMainStateValue=function(){return this.mainState},i.prototype.rootPath=function(){return this.localeData.root},i.prototype.baseRootPath=function(){return"/"},i.prototype.isChanged=function(){return this.baseRootPath()!==this.rootPath()},r.prototype.getRequestPath=function(t){return this.locale.isChanged()?this.locale.rootPath()+t:t},o.prototype.setState=function(t){return this.state=t,this.state},o.prototype.entityHandle=function(){return this.handles[this.state]},o.prototype.entityTitle=function(){return this.titles[this.state]},o.prototype.entityStylesLink=function(){return this.styles[this.state]},o.prototype.entityWrapperClasses=function(){return this.wrapperClasses[this.state]},o.prototype.entityFontsSettings=function(){return this.fonts[this.state]},o.prototype.entityFontsCssState=function(){return this.fontsCss[this.state]},o.prototype.entityAnalyticsSettings=function(){return this.analyticsSettings[this.state]},o.prototype.entityPath=function(){if(this.scope&&this.entityHandle()){var t="/"+this.scope+"/"+this.entityHandle();return this.route.getRequestPath(t)}return null},o.prototype.entityUrl=function(){var t=this.entityPath();return t?"https://{{ shop.domain }}"+t:null},h.prototype.parseUrl=function(t){var e=(""+t).trim();if(!/^\//.test(e)&&!/^https?:\/\//.test(e))return{};e=document.createElement("a");return e.href=t,{protocol:e.protocol,host:e.host,hostname:e.hostname,port:e.port,pathname:e.pathname,hash:e.hash,search:e.search,origin:e.origin}},h.prototype.searchQueryParams=function(t){t=this.parseUrl(t);return t.search?(""+t.search).replace(/^\?+/,""):""},h.prototype.filterSearchParams=function(t,e){if(!(t&&e instanceof RegExp))return t;for(var s,n=this.splitSearchParams(t),a=[],i=0,r=n.length;i<r;i++)s=n[i],e.test(s)||a.push(s);return a.join("&")},h.prototype.splitSearchParams=function(t){return t?(""+(""+t).replace(/^\?+/,"")).split("&"):[]};var p=new a(t.entityMarker,t.testToken);p.getState()||(c=new e(t.dataSet).selectVariation(),c=p.getAvailableState(c),p.setState(c));var u=new h,c="";if(!p.isMainState()){if("viewtyperedirect"===t.testType){c=u.searchQueryParams(window.location.href).trim();if(new RegExp("\\b{{ zp_view_type_url_param_name }}="+t.viewType+"\\b").test(c))return}t=new o({state:p.getState(),scope:p.currentScope(),localeData:t.locale,handles:t.handles}).entityPath();if(0<c.length){for(var l,y=u.searchQueryParams(t).trim(),f=u.splitSearchParams(y),y=c,g=0,S=f.length;g<S;g++)(l=(""+f[g]).split("=")[0])&&l!==f[g]&&"undefined"!==l&&(l=new RegExp("^"+l+"="),y=u.filterSearchParams(y,l));c=0<f.length?"&":"?";t+=0<y.length?c+y:""}window.location.replace(t)}}({{ zp_split_data_string }});</script>{% assign zp_split_data_string = nil %}{% endif %}{% endif %}{% endif %}{% endif %}