{% if block.settings.title != blank %}
  <div class="sidebar-block__toggle" data-has-toggle-option>
    <h3 class="sidebar-block__heading" {% if settings.toggle_sidebar %}data-sidebar-block__toggle="closed" aria-label="toggle"{% endif %}>
      {{ block.settings.title }}
      {% if settings.toggle_sidebar %}
        <button class="sidebar-block__toggle-icon icon-style--{{ settings.toggle_icon_style }}">
          {% if settings.toggle_icon_style == 'plus_and_minus' %}
            {% render 'icon',
                    name: 'plus',
                    icon_class: 'icon--active'
            %}
            {% render 'icon',
                    name: 'minus',
                    icon_class: 'icon--inactive'
            %}
          {% else %}
            {% render 'icon',
                    name: 'down-caret',
                    icon_class: 'icon--active'
            %}
          {% endif %}
        </button>
      {% endif %}
    </h3>
  </div>
{% endif %}
<div class="sidebar-block__content" {% if settings.toggle_sidebar %}data-sidebar-block__content--collapsible{% endif %}>
  <ul class="sidebar-block__vendor-list">
    {% for product_vendor in shop.vendors %}
      <li {% if product_vendor == collection.title %}class="is-active"{% endif %}>{{ product_vendor | link_to_vendor }}</li>
    {% endfor %}
  </ul>
</div>
