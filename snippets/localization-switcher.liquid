{% comment %}
Required values
id: <string>
currency_selector: <boolean>
locale_selector: <boolean>
show_currency: <boolean>
Optional values
additional_classes: <string>
{% endcomment %}

{%- form 'localization', class: 'selectors-form', id: id -%}
<div class="localization {{ additional_classes }}">
  <div class="selectors-form__wrap">
    {%- if locale_selector -%}
      <div class="selectors-form__item selectors-form__locale">
        <h2 class="hidden" id="lang-heading-{{ id }}">
          {{ 'general.language.dropdown_label' | t }}
        </h2>

        <input type="hidden" name="locale_code" value="{{ form.current_locale.iso_code }}" data-disclosure-input/>

        <div class="disclosure disclosure--i18n disclosure-text-style-{{ settings.nav_font_style }}" data-disclosure data-disclosure-locale>
          <button type="button" class="disclosure__toggle disclosure__toggle--i18n" aria-expanded="false" aria-controls="lang-list-{{ id }}" aria-describedby="lang-heading-{{ id }}" data-disclosure-toggle>
            <span class="disclosure__toggle-content">{{ form.current_locale.endonym_name }}</span> {% render 'icon', name: 'down-caret' %}
          </button>
          <div class="disclosure__list-wrap">
            <ul id="lang-list-{{ id }}" class="disclosure-list">
              {%- for locale in form.available_locales -%}
                <li class="disclosure-list__item {% if locale.iso_code == form.current_locale.iso_code %}disclosure-list__item--current{% endif %}">
                  <button type="submit" class="disclosure__button" name="locale_code" value="{{ locale.iso_code }}">
                    {{ locale.endonym_name }}
                  </button>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        </div>
      </div>
    {%- endif -%}
    {%- if settings.show_multiple_currencies and show_currency -%}
      {% capture codes %},USD,EUR,GBP,CAD,ARS,AUD,ILS,BBD,BDT,BSD,BHD,BRL,BOB,BND,BGN,MMK,KYD,CLP,CNY,COP,CRC,HRK,CZK,DKK,DOP,XCD,EGP,XPF,FJD,GHS,GTQ,GYD,GEL,HKD,HUF,ISK,INR,IDR,NIS,JMD,JPY,JOD,KZT,KES,KWD,LVL,LTL,MXN,MYR,MUR,MDL,MAD,MNT,MZN,ANG,NZD,NGN,NOK,OMR,PKR,PYG,PEN,PHP,PLN,QAR,RON,RUB,SAR,RSD,SCR,SGD,SYP,ZAR,KRW,LKR,SEK,CHF,TWD,THB,TZS,TTD,TRY,TND,UAH,AED,UYU,VEB,VND,ZMK,{% endcapture %}
      {%- assign supported_codes = settings.supported_currencies | split: ' '-%}

      <div class="selectors-form__item selectors-form__currency" value="{{ shop.currency }}" data-default-shop-currency="{{ shop.currency }}">
        <h2 class="hidden" id="currency-heading-{{ id }}">
          {{ 'general.currency.dropdown_label' | t }}
        </h2>

        <div class="disclosure disclosure--currency disclosure-text-style-{{ settings.nav_font_style }}" data-disclosure>
          <button type="button" class="disclosure__toggle disclosure__toggle--currency" aria-expanded="false" aria-controls="currency-list-{{ id }}" aria-describedby="currency-heading-{{ id }}" data-disclosure-toggle>
            <span class="currency-code">{{ shop.currency }}</span> {% render 'icon', name: 'down-caret' %}
          </button>
          <div class="disclosure__list-wrap">
            <ul id="currency-list-{{ id }}" class="disclosure-list" data-default-shop-currency="{{ shop.currency }}">
              {%- for code in supported_codes -%}
                <li class="disclosure-list__item {% if code == shop.currency %}disclosure-list__item--current{% endif %}">
                  <button class="disclosure__button" name="currency_code" value="{{ code }}" data-currency-converter>
                    {{ code }}
                  </button>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        </div>
      </div>
    {%- elsif currency_selector -%}
      <div
        class="
          selectors-form__item
          selectors-form__country
        "
      >
        <h2
          class="hidden"
          id="country-heading-{{ id }}"
        >
          {{ 'general.country.dropdown_label' | t }}
        </h2>

        <input
          type="hidden"
          name="country_code"
          value="{{ localization.country.iso_code }}"
          data-country-selector
          data-disclosure-input
        />

        <div
          class="
            disclosure
            disclosure--country
            disclosure-text-style-{{ settings.nav_font_style }}
          "
          data-disclosure
          data-disclosure-country
        >
          <button
            type="button"
            class="
              disclosure__toggle
              disclosure__toggle--country
            "
            aria-expanded="false"
            aria-controls="country-list-{{ id }}"
            aria-describedby="country-heading-{{ id }}"
            data-disclosure-toggle
          >
          <span class="disclosure__toggle-content">{{ localization.country.name }} ({{ localization.country.currency.iso_code }}{% if localization.country.currency.symbol %} {{ localization.country.currency.symbol }}{%- endif -%})</span> {% render 'icon', name: 'down-caret' %}
          </button>
          <div class="disclosure__list-wrap">
            <ul
              id="country-list-{{ id }}"
              class="disclosure-list"
            >
              {%- for country in localization.available_countries -%}
                <li
                  class="
                    disclosure-list__item
                    {% if country.iso_code == localization.country.iso_code %}
                      disclosure-list__item--current
                    {% endif %}
                  "
                >
                  <button
                    type="submit"
                    class="disclosure__button"
                    name="country_code"
                    value="{{ country.iso_code }}"
                  >
                    {{ country.name }} ({{ country.currency.iso_code }}{% if country.currency.symbol %} {{ country.currency.symbol }}{%- endif -%})
                  </button>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        </div>
      </div>
    {%- endif -%}
  </div>
</div>
{%- endform -%}

