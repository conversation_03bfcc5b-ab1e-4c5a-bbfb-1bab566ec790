{% liquid
  assign heading = block.settings.heading
  assign products_per_slide = block.settings.products_per_slide

  if settings.currency_format == 'money_with_currency_format'
    assign format_currency = true
  endif
%}

{% comment %}Inject @pixelunion/pxs-complementary-products/complementary-product-block begin{% endcomment %}
{% comment %}
  @param block {Object}
  @param wrapper_class {String}
  @param heading {String}
  @param products_per_slide {Integer}
  @param format_currency {Boolean}
  @param crop_thumbnails {Boolean}
  @param wrap_heading {Boolean}
{% endcomment %}

{% liquid
  assign products_per_slide = products_per_slide | default: 2
  assign format_currency = format_currency | default: false
  assign wrap_heading = wrap_heading | default: false
  assign crop_thumbnails = crop_thumbnails | default: false
%}

<div
  class="
    complementary-products
    {{ wrapper_class }}
  "
  {{ block.shopify_attributes }}
  data-complementary-products
  data-html
>
{% if recommendations.performed and recommendations.products_count > 0 %}
    {% assign slider = false %}

    {% if recommendations.products_count > products_per_slide %}
      {% assign slider = true %}
    {% endif %}

    {% if heading != blank %}
      {%- if wrap_heading -%}
        <div class="complementary-products__title-wrapper">
      {%- endif -%}
      <h3 class="complementary-products__title">
        {{ heading | escape }}
      </h3>
      {%- if wrap_heading -%}
        </div>
      {%- endif -%}
    {% endif %}

    <div
        class="
          complementary-products__container
          {% if slider %}
            complementary-products__container--slider
          {% else %}
            complementary-products__container--grid
          {% endif %}
        "
      data-slider-wrapper
    >
      <div
        class="complementary-products__slider"
        data-slider
      >
      {% for product in recommendations.products %}
          {% assign slide_index_remainder = forloop.index | modulo: products_per_slide %}
          {% if slide_index_remainder == 1 or products_per_slide == 1 %}
            <div
              class="
                {% if slider %}
                  complementary-products__slide
                {% else %}
                  complementary-products__grid
                {% endif %}
              "
              {% if slider %}
                data-slide
              {% endif %}
            >
          {% endif %}

          <div data-slide-item>
            {%
              render 'complementary-product',
              product: product,
              format_currency: format_currency,
              crop_thumbnails: crop_thumbnails,
            %}
          </div>

          {% if forloop.last or slide_index_remainder == 0 or products_per_slide == 1 %}
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>
  {% endif %}
</div>
{% comment %}Inject @pixelunion/pxs-complementary-products/complementary-product-block end{% endcomment %}