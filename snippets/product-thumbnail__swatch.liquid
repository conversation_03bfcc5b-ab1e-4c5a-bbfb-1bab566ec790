{% if settings.collection_swatches %}
  {% assign file_extension = 'png' %}

  {% for option in product.options_with_values %}
    {% assign swatch_trigger = settings.swatch_trigger | strip | downcase %}
    {% assign option_name = option.name | downcase %}

    {% if option_name == swatch_trigger %}
      {% assign option_index = forloop.index0 %}
      {% assign values = '' %}
      <div class="thumbnail-swatch is-justify-{{ settings.thumbnail_text_alignment }} is-flex-wrap">

        {% for variant in product.variants %}
          {% assign value = variant.options[option_index] %}
          {% unless values contains value %}
            {% assign values = values | join: ',' %}
            {% assign values = values | append: ',' | append: value %}
            {% assign values = values | split: ',' %}

            {% assign swatch_file_url = variant.image | img_url: 'small' %}

            <a href="{{ variant.url | within: collection }}" class="swatch swatch__style--{{ settings.collection_swatches_shape }}" data-swatch-name="meta-{{ option_name }}_{{ value | replace: ' ', '_' | downcase }}">
              <span {% if section.settings.products_per_row == '2'%}
                      data-image="{{ variant.featured_image | img_url: '600x' }}"
                    {% elsif section.settings.products_per_row == '3' %}
                      data-image="{{ variant.featured_image | img_url: '500x' }}"
                    {% else %}
                      data-image="{{ variant.featured_image | img_url: '400x' }}"
                    {% endif %}
                    style="
                      {% if settings.swatches_option_style == 'variant_image' and product.variants[forloop.index0].image != blank %}
                        background-image: url({{ swatch_file_url }});
                      {% else %}
                        background-color: {{ value | split: ' ' | last | handle }};
                      {% endif %}
                    ">
                  {% assign image_name = value | handle | append: '.' | append: file_extension %}
                  {% assign swatch = images[image_name] %}

                  <img class="swatch__image {% if swatch == empty %}swatch__image--empty{% endif %}" src="{{ swatch | img_url: '50x' }}" alt="{{ variant.title }}">
                </span>
            </a>
          {% endunless %}
        {% endfor %}

      </div>
    {% endif %}
  {% endfor %}
{% endif %}
