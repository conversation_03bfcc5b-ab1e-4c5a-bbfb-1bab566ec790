{% comment %}Assign object as block or section{% endcomment %}
{% if type == 'block' %}
  {%- assign object = block -%}
{% else %}
  {%- assign object = section -%}
{% endif %}
{%- assign icon = settings.icon -%}

{%- assign locale_selector = false -%}
{%- assign currency_selector = false -%}

{%- if object.settings.show_currency_selector and shop.enabled_currencies.size > 1 or localization.available_countries.size > 1 -%}
  {%- assign currency_selector = true -%}
{%- endif -%}

{%- if object.settings.show_locale_selector and shop.published_locales.size > 1 -%}
  {%- assign locale_selector = true -%}
{%- endif -%}

<div class="header__icons header__icon-style-{{ icon_style | replace: '_', '-' }} {{ header_icon_class }}">


  {% comment %} Currency converter select {% endcomment %}
  <div class="header__link">
    {% render 'localization-switcher',
      additional_classes: 'header-menu__disclosure',
      id: 'header__selector-form--action-icons',
      currency_selector: currency_selector,
      locale_selector: locale_selector,
      show_currency: section.settings.show_currency_selector
    %}
  </div>

  {% if display_search %}
    <a
      class="
        header__link
        action-area__link
      "
      data-show-search-trigger
      tabindex="0"
    >
      {%
        render 'icon',
        name: 'search',
        icon_class: 'header__icon',
      %}

      <span class="icon-caption">
        {{- 'general.search.title' | t | escape -}}
      </span>
    </a>
  {% endif %}

  {% if shop.customer_accounts_enabled %}
    {% comment %} Account icon {% endcomment %}
    {% if customer %}
      {% comment %} Logout {% endcomment %}
      <a href="{{ routes.account_url }}" class="header__link action-area__link" >
          {% render 'icon',
                  name: 'avatar',
                  icon_class: 'header__icon'
          %}
          <span class="icon-caption">{{ 'layout.customer.my_account' | t | escape }}</span>
      </a>
    {% else %}
      {% comment %} Login {% endcomment %}
      <a href="{{ routes.account_login_url }}" class="header__link action-area__link" >
          {% render 'icon',
                  name: 'avatar',
                  icon_class: 'header__icon'
          %}
          <span class="icon-caption">{{ 'layout.customer.log_in' | t | escape }}</span>
      </a>
    {% endif %}
  {% endif %}

  {% comment %} Cart icon {% endcomment %}
  <div class="header-cart action-area__link {% if cart.item_count != 0 %}has-cart-count{% endif %}" data-ajax-cart-trigger>
    <a class="header__link" href="{{ routes.cart_url }}" >
      <span class="header-cart__icon">
        {% render 'icon',
                name: settings.cart_icon,
                icon_class: 'header__icon'
        %}
        <span class="header-cart__count header-cart__count--badge badge" data-bind="itemCount">
          {{ cart.item_count }}
        </span>
      </span>
      <span class="header-cart__caption icon-caption">
        {{ 'layout.general.cart' | t | escape }}
        {%- if icon_style == 'text' -%}
          <span class="header-cart__count cart__count--text" data-bind="itemCount">{{ cart.item_count }}</span>
        {%- endif -%}
      </span>
    </a>
    {% if settings.cart_action == 'mini_cart' %}
      {% render 'ajax-cart' %}
    {% endif %}
  </div>
</div>
