/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "zp-product-header-content": {
      "type": "product-header-content-zipifypages",
      "settings": {}
    },
    "breadcrumbs": {
      "type": "product__breadcrumb",
      "disabled": true,
      "settings": {
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "sidebar": {
      "type": "product__sidebar",
      "settings": {
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "main": {
      "type": "product__main",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {}
        },
        "judge_me_reviews_preview_badge_nxpdpj": {
          "type": "shopify://apps/judge-me-reviews/blocks/preview_badge/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        },
        "price": {
          "type": "price",
          "settings": {
            "display_savings": true
          }
        },
        "shelter_pet_8wYVCR": {
          "type": "shelter_pet",
          "settings": {}
        },
        "form": {
          "type": "form",
          "settings": {
            "show_payment_button": true,
            "show_gift_card_recipient_form": false
          }
        },
        "description": {
          "type": "description",
          "settings": {}
        },
        "share": {
          "type": "share",
          "settings": {}
        }
      },
      "block_order": [
        "title",
        "judge_me_reviews_preview_badge_nxpdpj",
        "price",
        "shelter_pet_8wYVCR",
        "form",
        "description",
        "share"
      ],
      "settings": {
        "new_pet_bowl_img": "shopify://shop_images/Pet_Bowl_Image_d9be647f-5490-48a4-826f-a0a3a4d02a1d.png",
        "new_pet_bowl_text": "This product feeds shelter pets",
        "product_images_position": "left",
        "set_product_height": false,
        "product_height": 500,
        "video_looping": false,
        "gallery_arrows": true,
        "enable_zoom": true,
        "enable_product_lightbox": true,
        "slideshow_speed": 0,
        "slideshow_transition": "slide",
        "display_thumbnails": true,
        "thumbnail_position": "bottom-thumbnails",
        "enable_thumbnail_slider": true,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_heading_6XgHhm": {
      "type": "index__heading",
      "settings": {
        "preheading": "",
        "title": "How To Customize",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "grid_QgjECR": {
      "type": "grid",
      "blocks": {
        "grid_item_GUxf4a": {
          "type": "grid_item",
          "settings": {
            "grid_image": "shopify://shop_images/How_to_Customize_Select_Background_Pjs_400px.png",
            "link": "",
            "heading": "Choose Your Style",
            "text": "Choose from multiple backgrounds and find the one that perfectly showcases your pet's personality!",
            "button_label": "Button",
            "button_style": "button--primary",
            "text_color": "rgba(0,0,0,0)",
            "overlay_background": "rgba(0,0,0,0)",
            "overlay_background_opacity": 0
          }
        },
        "grid_item_rEJJDa": {
          "type": "grid_item",
          "settings": {
            "grid_image": "shopify://shop_images/How_to_Customize_Upload_photo_400px.png",
            "link": "",
            "heading": "Upload Your Pet's Photo",
            "text": "Add your favorite photos of your pet (the more photos you add the more accurate your plush will be!)",
            "button_label": "Button",
            "button_style": "button--primary",
            "text_color": "rgba(0,0,0,0)",
            "overlay_background": "rgba(0,0,0,0)",
            "overlay_background_opacity": 0
          }
        },
        "grid_item_mrckMh": {
          "type": "grid_item",
          "settings": {
            "grid_image": "shopify://shop_images/How_to_Customize_Cherish_Pjs_400px.png",
            "link": "",
            "heading": "Cherish Your Custom Creation",
            "text": "Unbox your custom pajama or give as a gift! Don't forget to share on social and tag us @cuddleclones!",
            "button_label": "Button",
            "button_style": "button--primary",
            "text_color": "rgba(0,0,0,0)",
            "overlay_background": "rgba(0,0,0,0)",
            "overlay_background_opacity": 0
          }
        }
      },
      "block_order": [
        "grid_item_GUxf4a",
        "grid_item_rEJJDa",
        "grid_item_mrckMh"
      ],
      "settings": {
        "column_count": 3,
        "align_height": false,
        "image_height": 300,
        "border_radius": 0,
        "enable_zoom": true,
        "show_text_below_image": true,
        "heading_size": "regular",
        "text_alignment": "center",
        "text_horizontal_position": "center",
        "text_vertical_position": "middle",
        "text_width": 80,
        "text_color": "rgba(0,0,0,0)",
        "overlay_background": "rgba(0,0,0,0)",
        "overlay_background_opacity": 0,
        "section_background_color": "rgba(0,0,0,0)",
        "width": "standard",
        "show_gutter": true,
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "mobile_heading_size": "same_as_desktop",
        "mobile_text_alignment": "same_as_desktop",
        "mobile_text_horizontal_position": "same_as_desktop",
        "mobile_text_vertical_position": "same_as_desktop",
        "mobile_text_width": 100,
        "mobile_layout": "slider",
        "show_arrows": false,
        "show_nav_buttons": true,
        "mobile_padding_top": 20,
        "mobile_padding_bottom": 20
      }
    },
    "video_section_UryJeR": {
      "type": "video-section",
      "settings": {
        "title": "Over 5,000+ Reviews!",
        "review_description": "\"I bought a set of the pajama shorts for my daughter. When I received the item I could not believe how adorable they looked. My daughter screamed when she opened the gift and saw her beloved Bucky’s face all over. The photo quality was fantastic. Would absolutely buy again. \"",
        "review_stars": 5,
        "review_video_1": "https://cdn.shopify.com/videos/c/o/v/92195cb466e04abab2749c4f0c1ce2e5.mov",
        "review_video_2": "https://cdn.shopify.com/videos/c/o/v/ecb578c64b2641fcb7f88bc7c4e34f84.mov",
        "review_video_3": "https://cdn.shopify.com/videos/c/o/v/6fd3b6cf975d4eda90accd63c42b62db.mp4",
        "review_video_4": "https://cdn.shopify.com/videos/c/o/v/b0ce956ae964467a81c669391ad3b4ec.mov",
        "review_video_5": "",
        "review_video_6": "",
        "review_star_checked_color": "",
        "review_star_empty_color": "",
        "review_header_color": "",
        "review_description_color": "",
        "video_section_background_color": "rgba(0,0,0,0)",
        "review_container_background_color": "rgba(0,0,0,0)"
      }
    },
    "how_to_submit_pictures_QRf9eE": {
      "type": "how-to-submit-pictures",
      "settings": {
        "title": "How to submit the best picture of your pet!",
        "tab_1_image": "shopify://shop_images/Submit-Image-Three_600x_a70ae4da-47a6-4654-a7cb-d58195a6a7a9.webp",
        "tab_1_name": "One Pet Front and Center",
        "tab_1_description": "For the perfect custom plush, we need clear, close-up images of your buddy’s face and chest. To ensure we accurately capture their unique features and turn them into an awesome piece of art, please make sure the pictures you submit feature only the pet you are customizing. This helps us focus solely on their details and create a truly one-of-a-kind custom pajamas for you!",
        "tab_2_image": "shopify://shop_images/Submit-Image-Two.png",
        "tab_2_name": "Positioning",
        "tab_2_description": "Sitting down or standing up works best to center your pet’s features on our products!",
        "tab_3_image": "shopify://shop_images/Submit-Image-Three.png",
        "tab_3_name": "Lighting",
        "tab_3_description": "Good lighting makes a brown dog look brown, not black. Make sure there is adequate lighting to let your furballs colors shine thru!"
      }
    },
    "index_heading_TXCMmP": {
      "type": "index__heading",
      "settings": {
        "preheading": "",
        "title": "Why Cuddle Clones?",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_gallery_7aQVRP": {
      "type": "index__gallery",
      "blocks": {
        "image_CLbNEA": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Why_CC_USP_icons_pajamas_Unique_pet.png",
            "link": ""
          }
        },
        "image_VCWr6F": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Why_CC_USP_icons_pajamas_High_Quality_Material.png",
            "link": ""
          }
        },
        "image_XGHDbV": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Why_CC_USP_icons_pajamas_Entire_Fam_Sizing.png",
            "link": ""
          }
        },
        "image_JHgfGD": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Why_CC_USP_icons_pajamas_High_Definition_Prints.png",
            "link": ""
          }
        }
      },
      "block_order": [
        "image_CLbNEA",
        "image_VCWr6F",
        "image_XGHDbV",
        "image_JHgfGD"
      ],
      "settings": {
        "gallery_type": "classic",
        "images_per_row": 4,
        "crop_images": false,
        "enable_lightbox": true,
        "overlay_color": "#000000",
        "link_color": "#ffffff",
        "width": "standard",
        "show_gutter": true,
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "mobile_layout": "stacked",
        "crop_images_mobile": false,
        "show_arrows": true,
        "show_navigation_dots": true,
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "170894921511d6b216": {
      "type": "apps",
      "blocks": {
        "judge_me_reviews_review_widget_MG7dK8": {
          "type": "shopify://apps/judge-me-reviews/blocks/review_widget/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        }
      },
      "block_order": ["judge_me_reviews_review_widget_MG7dK8"],
      "settings": {
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "recommendations": {
      "type": "product__recommendations",
      "settings": {
        "show_product_recommendations": true,
        "product_recommendations_style": "grid",
        "product_recommendations_heading": "You may also like",
        "products_per": 3,
        "recommended_products_limit": 6,
        "align_height": false,
        "collection_height": 200,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "zp-product-footer-content": {
      "type": "product-footer-content-zipifypages",
      "settings": {}
    }
  },
  "order": [
    "zp-product-header-content",
    "breadcrumbs",
    "sidebar",
    "main",
    "index_heading_6XgHhm",
    "grid_QgjECR",
    "video_section_UryJeR",
    "how_to_submit_pictures_QRf9eE",
    "index_heading_TXCMmP",
    "index_gallery_7aQVRP",
    "170894921511d6b216",
    "recommendations",
    "zp-product-footer-content"
  ]
}
