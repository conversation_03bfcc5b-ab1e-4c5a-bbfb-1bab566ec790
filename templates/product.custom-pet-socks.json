/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "zp-product-header-content": {
      "type": "product-header-content-zipifypages",
      "settings": {}
    },
    "breadcrumbs": {
      "type": "product__breadcrumb",
      "disabled": true,
      "settings": {
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "sidebar": {
      "type": "product__sidebar",
      "settings": {
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "main": {
      "type": "product__main",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {}
        },
        "judge_me_reviews_preview_badge_nxpdpj": {
          "type": "shopify://apps/judge-me-reviews/blocks/preview_badge/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        },
        "price": {
          "type": "price",
          "settings": {
            "display_savings": true
          }
        },
        "shelter_pet_8wYVCR": {
          "type": "shelter_pet",
          "settings": {}
        },
        "text_WAFNDm": {
          "type": "text",
          "settings": {
            "text": "<p><em>Please select all product options to see price and image example</em></p>"
          }
        },
        "form": {
          "type": "form",
          "settings": {
            "show_payment_button": true,
            "show_gift_card_recipient_form": false
          }
        },
        "rebuy_personalization_engine_rebuy_widget_RH73mT": {
          "type": "shopify://apps/rebuy-personalization-engine/blocks/rebuy-widget/a26ae9b9-933b-40bf-bbc6-bf1220bbc4dc",
          "settings": {
            "enabled": true,
            "id": "150802",
            "product_id_enabled": true,
            "collection_id_enabled": false
          }
        },
        "description": {
          "type": "description",
          "settings": {}
        },
        "share": {
          "type": "share",
          "settings": {}
        }
      },
      "block_order": [
        "title",
        "judge_me_reviews_preview_badge_nxpdpj",
        "price",
        "shelter_pet_8wYVCR",
        "text_WAFNDm",
        "form",
        "rebuy_personalization_engine_rebuy_widget_RH73mT",
        "description",
        "share"
      ],
      "settings": {
        "new_pet_bowl_img": "shopify://shop_images/Pet_Bowl_Image_d9be647f-5490-48a4-826f-a0a3a4d02a1d.png",
        "new_pet_bowl_text": "This product feeds shelter pets",
        "product_images_position": "left",
        "set_product_height": false,
        "product_height": 500,
        "video_looping": false,
        "gallery_arrows": true,
        "enable_zoom": true,
        "enable_product_lightbox": true,
        "slideshow_speed": 0,
        "slideshow_transition": "slide",
        "display_thumbnails": true,
        "thumbnail_position": "bottom-thumbnails",
        "enable_thumbnail_slider": true,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_heading_TXCMmP": {
      "type": "index__heading",
      "settings": {
        "preheading": "",
        "title": "Why Cuddle Clones?",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 0,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 0,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_image_with_text_overlay_aRmEC6": {
      "type": "index__image-with-text-overlay",
      "settings": {
        "image": "shopify://shop_images/Why_CC_USP_icons_POD_desktop-_4_set_v2_1.png",
        "mobile_image": "shopify://shop_images/Why_CC_USP_icons_POD_mobile_-4_set-800x1150.png",
        "link": "",
        "pretext": "",
        "title": "",
        "subtitle": "",
        "pretext_color": "#000000",
        "heading_color": "#000000",
        "subheading_color": "#000000",
        "text_alignment": "center",
        "text_horizontal_position": "center",
        "text_vertical_position": "middle",
        "text_width": 40,
        "background_color": "#ffffff",
        "background_opacity": 77,
        "border_color": "#ffffff",
        "border_width": 0,
        "button_1": "",
        "button_1_link": "",
        "button_1_style": "button--secondary",
        "button_2": "",
        "button_2_link": "",
        "button_2_style": "button--secondary",
        "width": "wide",
        "padding_top": 3,
        "padding_bottom": 40,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_below_image": true,
        "mobile_preheading": "",
        "mobile_heading": "",
        "mobile_subheading": "",
        "pretext_color_mobile": "rgba(0,0,0,0)",
        "heading_color_mobile": "rgba(0,0,0,0)",
        "subheading_color_mobile": "rgba(0,0,0,0)",
        "text_alignment_mobile": "same_as_desktop",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 40,
        "css_class": "",
        "custom_css": ""
      }
    },
    "how_to_submit_pictures_QRf9eE": {
      "type": "how-to-submit-pictures",
      "settings": {
        "title": "How to Submit the Best Picture of your Pet!",
        "tab_1_image": "shopify://shop_images/Photos_of_Pet_example_600x_62ecd047-60fb-4655-a953-f1ecf237cffe.jpg",
        "tab_1_name": "One Pet Front and Center",
        "tab_1_description": "For the perfect custom socks , please submit clear, close-up images of your pet’s face and chest, ensuring that the photos feature only the pet you wish to replicate, to accurately capture their unique features and create a one-of-a-kind pair of socks.",
        "tab_2_image": "shopify://shop_images/Submit-Image-Two.png",
        "tab_2_name": "Positioning",
        "tab_2_description": "Images of your pet sitting down or standing up is ideal to center their features prominently on the custom socks!",
        "tab_3_image": "shopify://shop_images/Submit-Image-Three.png",
        "tab_3_name": "Lighting",
        "tab_3_description": "Ensure there is good lighting in the photos to accurately capture your pet's true colors, allowing their natural hues to shine through."
      }
    },
    "index_heading_YET4ef": {
      "type": "index__heading",
      "settings": {
        "preheading": "",
        "title": "Created by Pet Lovers - For Pet Lovers",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 0,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 0,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_gallery_DB6qRC": {
      "type": "index__gallery",
      "blocks": {
        "image_emNfe7": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/socks_PDP_Reviews_4.png",
            "link": ""
          }
        },
        "image_QG4BMX": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/socks_PDP_Reviews_3.png",
            "link": ""
          }
        },
        "image_i3yxbi": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/socks_PDP_Reviews_2.png",
            "link": ""
          }
        },
        "image_8izbjr": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/socks_PDP_Reviews_1.png",
            "link": ""
          }
        }
      },
      "block_order": [
        "image_emNfe7",
        "image_QG4BMX",
        "image_i3yxbi",
        "image_8izbjr"
      ],
      "settings": {
        "gallery_type": "classic",
        "images_per_row": 4,
        "crop_images": false,
        "enable_lightbox": true,
        "overlay_color": "#000000",
        "link_color": "#ffffff",
        "width": "standard",
        "show_gutter": true,
        "padding_top": 0,
        "padding_bottom": 20,
        "animation": "none",
        "mobile_layout": "stacked",
        "crop_images_mobile": false,
        "show_arrows": false,
        "show_navigation_dots": true,
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 15,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_image_with_text_wbLbFj": {
      "type": "index__image-with-text",
      "blocks": {
        "image_ppDVRQ": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/socks1000x1000_2_5000x_1200x_63a3b18d-7d76-49a1-b892-889e2ac63af5.webp",
            "image_link": ""
          }
        },
        "text_qyiL7U": {
          "type": "text",
          "settings": {
            "title": "A Celebration of Your Pet",
            "heading_size": "regular",
            "text": "<p>The bond and affection we share with our pets are truly special. At Cuddle Clones, we aim to honor this special connection with a one-of-a-kind, cuddly keepsake that is a mirror image of your pet.</p><ul><li>Memorialize a beloved pet</li><li>Gift joy to those missing their pets during life's transitions</li><li>A thoughtful gift for those heading to college or moving away, missing their family pet.</li><li>An ideal present for anyone with a deep love for their pet</li></ul>",
            "alignment": "left",
            "vertical_position": "center",
            "button_label": "",
            "link": "",
            "button_style": "button--primary",
            "background": "rgba(0,0,0,0)",
            "text_color": "rgba(0,0,0,0)",
            "mobile_title": "",
            "mobile_heading_size": "same_as_desktop",
            "mobile_text": "",
            "mobile_alignment": "same_as_desktop"
          }
        }
      },
      "block_order": ["image_ppDVRQ", "text_qyiL7U"],
      "settings": {
        "width": "standard",
        "show_gutter": true,
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "mobile_text_position": "same_as_desktop",
        "mobile_padding_top": 18,
        "mobile_padding_bottom": 10,
        "css_class": "",
        "custom_css": ""
      }
    },
    "170894921511d6b216": {
      "type": "apps",
      "blocks": {
        "judge_me_reviews_review_widget_MG7dK8": {
          "type": "shopify://apps/judge-me-reviews/blocks/review_widget/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        }
      },
      "block_order": ["judge_me_reviews_review_widget_MG7dK8"],
      "settings": {
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_heading_TxWWDK": {
      "type": "index__heading",
      "settings": {
        "preheading": "",
        "title": "Frequently Asked Questions",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 0,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 0,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_faq_QYgUen": {
      "type": "index__faq",
      "blocks": {
        "content_GiV4mF": {
          "type": "content",
          "settings": {
            "title": "What photos should I submit for the best outcome?",
            "answer": "<p>We carefully review each photo as orders come in. If we find any issues with the submitted images, we’ll contact you via email to request additional pictures of your pet. To ensure the highest quality custom socks, we recommend submitting photos that are taken in natural daylight (without flash) and at your pet’s eye level.</p>"
          }
        },
        "content_3Yy9AB": {
          "type": "content",
          "settings": {
            "title": "How many pet faces can I have on a  pair of socks?",
            "answer": "<p>Each pair of custom pet socks can feature the face of three pets.</p>"
          }
        },
        "content_dKypgi": {
          "type": "content",
          "settings": {
            "title": "What is the return policy?",
            "answer": "<p>Due to the custom nature of these printed products, refunds and alterations are not available for these products.</p>"
          }
        },
        "content_eiwcVJ": {
          "type": "content",
          "settings": {
            "title": "Do you offer expedited shipping options?",
            "answer": "<p>Expedited shipping options available for your order will be listed at checkout. All tracking numbers are generated immediately after creation is completed. Tracking numbers will begin receiving updates after your order is transferred to our sorting facility.</p>"
          }
        }
      },
      "block_order": [
        "content_GiV4mF",
        "content_3Yy9AB",
        "content_dKypgi",
        "content_eiwcVJ"
      ],
      "settings": {
        "title": "",
        "width": "standard",
        "padding_top": 0,
        "padding_bottom": 56,
        "animation": "none",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 56,
        "css_class": "",
        "custom_css": ""
      }
    },
    "recommendations": {
      "type": "product__recommendations",
      "settings": {
        "show_product_recommendations": true,
        "product_recommendations_style": "grid",
        "product_recommendations_heading": "You May Also Like",
        "products_per": 4,
        "recommended_products_limit": 4,
        "align_height": true,
        "collection_height": 300,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "zp-product-footer-content": {
      "type": "product-footer-content-zipifypages",
      "settings": {}
    }
  },
  "order": [
    "zp-product-header-content",
    "breadcrumbs",
    "sidebar",
    "main",
    "index_heading_TXCMmP",
    "index_image_with_text_overlay_aRmEC6",
    "how_to_submit_pictures_QRf9eE",
    "index_heading_YET4ef",
    "index_gallery_DB6qRC",
    "index_image_with_text_wbLbFj",
    "170894921511d6b216",
    "index_heading_TxWWDK",
    "index_faq_QYgUen",
    "recommendations",
    "zp-product-footer-content"
  ]
}
