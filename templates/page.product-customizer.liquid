{% assign button_primary_color = settings.button_primary_bg_color %}
{% assign button_secondary_color = settings.button_primary_bg_color--highlight %}
{% assign button_font = settings.button__font %}
{% assign regular_font = settings.regular__font %}
{% assign button_letter_spacing = settings.button_letter_spacing %}
{% assign button_border_color = settings.button_primary_border_color %}
{% assign button_border_color_hover = settings.button_primary_border_color--highlight %}
{% assign button_border_radius = settings.button_primary_border_radius %}
{% assign add_cart_button_color = settings.button_cart_bg_color %}
{% assign add_cart_button_color_hover = settings.button_cart_bg_color--highlight %}
{% assign button_text_color = settings.button_primary_text_color %}
<head>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css" />
  <script async src="https://sdk.postscript.io/sdk.bundle.js?shopId=194751"></script>
</head>
<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900 &display=swap');
  .dynamic-sections {
    display: unset;
  }
  .top-banner {
    background-color: rgba(217, 217, 217, 0.5);
    text-align: center;
    padding: 40px 0;
  }
  .mt-40 {
    margin-top: 40px;
  }
  .mt-20 {
    margin-top: 20px;
  }
  .top-banner h2 {
    color: #5461c8;
    font-family: {{ regular_font.family }};
    font-size: 34px;
    font-weight: bold;
  }
  .page-content {
    padding-top: 50px;
  }
  .page-content h3 {
    font-family: {{ regular_font.family }};
    font-size: 22px;
  }
  .page-content form .step-1,
  .page-content form .step-2 {
    margin-top: 40px;
    margin-left: auto;
    margin-right: auto;
    max-width: 570px;
  }
  .page-content form .step-3 {
    margin-top: 30px;
    margin-left: auto;
    margin-right: auto;
    max-width: 949px;
  }
  .step-3 {
    border-radius: 55px;
    background: #d9d9d9;
    padding: 53px 30px 110px;
    width: fit-content;
  }
  .step-4 {
    text-align: center;
  }
  .step-4 input {
    width: 35px !important;
    height: 35px;
    max-width: 50px !important;
    margin-right: 15px;
  }
  #different-eye-color {
    max-width: 40px !important;
    width: 100% !important;
    height: 40px;
    box-shadow: none;
    border-radius: 0;
  }
  #different-eye-color-label {
    color: #000;
    font-family: {{ regular_font.family }};
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
  .custom-flex-container {
    display: flex;
    flex-wrap: wrap;
    padding: 0 30px;
  }
  .color-container {
    position: relative;
    max-width: 960px;
    margin: 40px auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
  }
  .custom-container .color[title="Missing"]:after {
    content: "";
    display: block;
    width: 2px;
    height: 100%;
    background: red;
    transform: rotate(40deg) translate(-50%, -50%);
    position: absolute;
    top: 37%;
    left: 19%;
  }
  .one-half {
    width: calc(50% - 20px);
  }
  .second-half {
    width: calc(50% - 20px);
    display: none;
  }
  .custom-flex-item {
    width: 24%;
    padding: 10px 5px;
    text-align: center;
    transition: all 0.3s ease;
  }
  .custom-flex-item .color {
    display: inline-flex;
    width: 40px;
    height: 40px;
    border-radius: 9999px;
    justify-content: center;
    align-items: center;
    color: #fff;
    position: relative;
  }
  .custom-flex-item .title {
    display: block;
    font-family: {{ regular_font.family }};
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    line-height: 1.6em;
  }
  .step-4 h4 {
    font-family: {{ regular_font.family }};
  }
  .step-4 h4:first-of-type {
    margin-top: 20px;
    font-weight: bold;
  }
  .multi-color-select {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .multi-color-select h4 {
    margin-top: unset !important;
  }
  .page-content form .form-field {
    display: flex;
    flex-direction: column;
  }
  .page-content form .input-label {
    margin-bottom: 5px;
  }
  .page-content form .step-1 > div {
    margin-bottom: 20px;
  }
  .page-content form label {
    color: #000;
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .page-content form input {
    width: 100%;
    max-width: 100%;
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.46);
    background: rgba(217, 217, 217, 0.45);
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    padding: 20px;
  }
  .page-content form button {
    cursor: pointer;
    background: {{ button_primary_color }};
    padding: 20px;
    width: 300px;
    max-width: 100%;
    border: 1px solid{{button_border_color}};
    border-radius: {{ button_border_radius |  append: 'px'}};
    color: {{button_text_color }};
    font-size: 20px;
    font-family: {{ button_font.family }};
    font-weight: 600;
    letter-spacing: {{ button_letter_spacing |  append: 'px'}};
  }
  .page-content form button:hover {
    background: {{ button_secondary_color }};
    border: 1px solid{{button_border_color_hover}};
  }
  .page-content form .form-button {
    text-align: center;
    margin-top: 60px;
  }
  .horizontal-line {
    color: #000;
    margin: 50px 0;
    border: 1.5px solid;
    max-width: 100%;
    width: 100%;
    background-color: black;
  }
  .pet-images {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    margin: 75px 0;
  }
  .pet-images div {
    text-align: center;
  }
  .pet-images img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
  }
  .pet-images .pet-image {
    margin-bottom: 20px;
    border-radius: 50%;
    height: 206px;
    width: 206px;
    cursor: pointer;
  }

  .pet-image.active {
    border: 3px solid #5461C8;
  }


  .pet-image.active,
  .pet-image:hover {
    border: 3px solid #5461C8;
    position: relative;
  }
  .pet-image.active::after,
  .pet-image:hover::after  {
    position: absolute;
    content: "";
    display: block;
    z-index: 2;
    top: 0;
    left: 0;
    right: 0;
    bottom: 2px;
    border-radius: 9999px;
    background: #5461c87a;
    opacity: 1;
    transition: all 0.3s ease;
  }

  .pet-images span {
    color: #000;
    text-align: center;
    font-family: {{ regular_font.family }};
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }


  .page-content .custom-container h3 {
    text-align: center;
    font-weight: bold;
  }
  form .step-2 select {
    width: 100%;
    max-width: 100%;
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.46);
    background: rgba(217, 217, 217, 0.45);
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    padding: 20px;
  }
  .rushSection {
    max-width: 570px;
    display: flex;
    flex-direction: column;
    margin: auto;
  }

  form .step-2 > div {
    margin-bottom: 20px;
  }
  form .step-2 > div + div .form-field {
    margin-top: 10px;
  }
  .upload-photo-content {
    max-width: 456px;
    margin: 50px auto 70px;
  }
  .upload-photo-content p:first-of-type {
    font-family: {{ regular_font.family }};
    font-size: 20px;
    text-align: center;
    margin-bottom: 20px;
  }
  .upload-photo-content p:last-of-type {
    font-family: {{ regular_font.family }};
    font-size: 20px;
    text-align: center;
  }
  .unique-characteristic {
    max-width: 570px;
    margin: 80px auto auto;
  }
  .unique-characteristic h3 {
    margin-bottom: 40px;
  }
  .unique-characteristic p {
    margin-bottom: 40px;
    text-align: center;
    font-family: {{ regular_font.family }};
    font-size: 20px;
  }
  .flex-box {
    display: flex;
    margin-bottom: 40px;
  }
  .flex-box .number {
    display: flex;
    align-items: center;
    border: 3px solid #5461c8;
    border-radius: 50%;
    height: 50px;
    width: 50px;
    min-width: 50px;
    margin-right: 25px;
  }
  .flex-box .number span {
    margin: auto;
    color: #5461c8;
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-weight: 800;
  }
  .unique-characteristic-uploader {
    width: 90%;
  }
  .image-uploader {
    background-color: #eaeaea;
    padding: 20px;
    border-radius: 20px;
  }
  .image-uploader textarea {
    background-color: #eaeaea;
    width: 100%;
    resize: none;
    border: unset;
    outline: unset;
    font-family: {{ regular_font.family }};
    font-size: 16px;
    margin-bottom: 25px;
  }
  .image-uploader textarea:focus-visible {
    border: unset;
  }
  .add-btn {
    text-align: right;
    max-width: 570px;
    margin: auto;
    padding: 0 25px;
  }
  #addUniqueButton {
    background-color: rgba(217, 217, 217, 0.52) !important;
    border: 3px solid #5461c8 !important;
    color: #5461c8 !important;
    width: 450px !important;
    max-width: 100% !important;
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-weight: 700;
    cursor: pointer;
    padding: 20px;
    letter-spacing: 0;
  }
  .dragBox {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: baseline;
    width: 100%;
    padding: 25px 0;
    margin: 0 auto;
    position: relative;
    text-align: center;
    font-weight: 500;
    color: #000;
    transition: transform 0.3s;
    border-radius: 20px;
    border: 1px solid #000;
    background: #edebf0;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    font-family: {{ regular_font.family }};
    font-size: 22px;
    input[type="file"] {
      position: absolute;
      border-radius: 20px;
      height: 100%;
      width: 100%;
      opacity: 0;
      top: 0;
      left: 0;
      padding: 0;
    }
  }
  .dragBoxUnique {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 25px 0;
    margin: 0 auto;
    position: relative;
    text-align: center;
    font-weight: 500;
    color: #000;
    transition: transform 0.3s;
    border-radius: 20px;
    border: 1px solid #000;
    background: #edebf0;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    font-family: {{ regular_font.family }};
    font-size: 22px;
    input[type="file"] {
      position: absolute;
      border-radius: 20px;
      height: 100%;
      width: 100%;
      opacity: 0;
      top: 0;
      left: 0;
      padding: 0;
    }
  }
    .labelAlert_hide{
    opacity:0;
    visibility: hidden;
    }
  @media (max-width: 770px) {
    .orText {
      display: none;
    }
    .dragDropText {
      display: none;
    }
    .dragBoxUnique {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding: 0;
      margin: 0 auto;
      position: relative;
      text-align: center;
      font-weight: 500;
      transition: transform 0.3s;
      font-family: {{ regular_font.family }};
      font-size: 22px;
      border: 0;
      background: transparent;
      box-shadow: none;
      input[type="file"] {
        position: absolute;
        border-radius: 20px;
        height: 100%;
        width: 100%;
        opacity: 0;
        top: 0;
        left: 0;
        padding: 0;
      }
    }
  }
  .uploadOuter .dragBox label {
    border: 3px solid #5461c8;
    border-radius: 16px;
    padding: 8px 20px;
    color: #5461c8;
  }
  .uploadOuter .dragBox label:hover {


    /*
   border: 3px solid black;
   border-radius: 16px;
   padding: 8px 20px;
   color: black;
*/
    color: white;
    background-color: rgb(197, 195, 195);
  }
  .uploadOuter .dragBox strong {
    margin: 0 20px;
    font-weight: 500;
  }
  .uploadOuter .dragBoxUnique label {
    border: 3px solid #5461c8;
    border-radius: 16px;
    padding: 8px 20px;
    color: #5461c8;
  }
  .uploadOuter .dragBoxUnique strong {
    margin: 0 20px;
    font-weight: 500;
  }
  #dragBox-characteristics {
    display: flex;
    flex-direction: column;
    position: relative;
    text-align: center;
    font-weight: 500;
    color: #5461C8;
    padding: 12px 15px;
    transition: transform 0.3s;
    border-radius: 20px;
    border: 3px solid #5461C8;
    background: #d9d9d9;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    font-family: {{ regular_font.family }};
    font-size: 22px;
    input[type="file"] {
      position: absolute;
      border-radius: 20px;
      height: 100%;
      width: 100%;
      opacity: 0;
      top: 0;
      left: 0;
      padding: 0;
    }
  }
  #pet-characteristic-label {
    color: #5461C8;
  }
  #preview {
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    margin-top: 60px;
    row-gap: 50px;
    column-gap: 15px;
    /* grid-template-columns: auto auto auto; */
    img {
      border-radius: 5px;
      width: 270px;
      height: 340px;
    }
  }
  @media (max-width: 480px) {
    .pet-images {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      margin: 75px 0;
    }
    #preview {
      display: grid;
      text-align: center;
      margin-top: 60px;
      row-gap: 20px;
      column-gap: 6px;
      grid-template-columns: auto auto;
      img {
        border-radius: 5px;
        width: 150px;
        height: 180px;
      }
    }
    .pet-images div {
      background-color: #D9D9D9;
      border-radius: 15px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }
    .pet-images .pet-image {
      border-radius: 50%;
      height: 65px;
      width: 100px;
      cursor: pointer;
      padding-top: 0;
      margin: 10px 10px 10px 25px;
    }
    .pet-images img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
    }
    .pet-image-container {
      margin-bottom: 15px;
      max-width: 248px;
      height: 70px;
    }
    .pet-image-container.active,
    .pet-image-container.active::after {
      margin-bottom: 15px;
      max-width: 248px;
      height: 70px;
      border: 2px solid #5461C8;
    }
    .pet-image.active {
      border: unset;
    }


    .pet-image {
      padding-top: 15px;
    }
  }
  .pet-image-preview {
    max-width: 270px;
    max-height: 340px;
  }
  .custom-container {
    max-width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
  }
  @media (min-width: 576px) {
    .custom-container {
      max-width: 540px;
    }
  }
  @media (min-width: 768px) {
    .custom-container {
      max-width: 720px;
    }
  }
  @media (min-width: 992px) {
    .custom-container {
      max-width: 960px;
    }
    .bandanaInput {
      margin-top: 15px;
    }
  }
  @media (min-width: 1200px) {
    .custom-container {
      max-width: 1140px;
    }

  }
  @media (min-width: 1400px) {
    .custom-container {
      max-width: 1320px;
    }
  }
  .ear-images {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
    gap: 30px;
  }
  .right-col {
    display: flex;
    flex-direction: column;
  }
  .left-col {
    display: flex;
    flex-direction: column;
  }
  .ear-image {
    display: flex;
    height: 140px;
    width: 140px;
    align-items: center;
    justify-content: center;
  }
  .right-label {
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-weight: 600;
    line-height: normal;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
  }
  .left-label {
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-weight: 600;
    font-style: normal;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
  }
  .next-button-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  #next-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: {{ button_border_radius |  append: 'px'}};
    background: {{ button_primary_color }};
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    margin-top: 20px;
    font-family: {{ button_font.family }};
    border: 1px solid{{button_border_color}};
    letter-spacing: {{ button_letter_spacing |  append: 'px'}};
  }
  #next-button:hover {
    background: {{ button_secondary_color }};
    border: 1px solid{{button_border_color_hover}};
  }
  .product-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .product-options-label {
    font-family: {{ regular_font.family }};
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
  .body-position {
    font-family: {{ regular_font.family }};
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-top: 40px;
  }
  .body-position-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 186px;
    max-height: 183px;
    flex-shrink: 0;
  }

  .position-label {
    font-size: 20px;
    font-family: {{ regular_font.family }};
    font-weight: 500;
    line-height: normal;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    margin-bottom: 20px;
    text-align: center
  }
  .section-container {
    display: flex;
    flex-direction: column;

  }
  .section-heading {
    display: flex;
    justify-content: center;
    font-size: 22px;
    font-family: {{ regular_font.family }};
    font-weight: 700;
    line-height: normal;
    font-style: normal;
    gap: 67px;
    margin-top: 100px;
    margin-bottom: 40px;
  }
  .productPositions {
    display: flex;
    margin-top: 20px;
    flex-wrap: wrap;
    gap: 50px;
  }
  .product-option-title {
    font-size: 24px;
    font-family: {{ regular_font.family }};
    font-weight: 700;
    line-height: normal;
    font-style: normal;
  }
  #final-step-button {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 137px;
    margin-bottom: 100px;
  }
  .final-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .final-options-label {
    font-family: {{ regular_font.family }};
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    display: flex;
    justify-content: center;
    margin-top: 90px;
  }
  .upgrade-label {
    font-family: {{ regular_font.family }};
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    display: flex;
    justify-content: center;
    margin-top: 23px;
  }
  .final-product-card {
    max-width: 949px;
    /* max-height: 460px; */
    margin-top: 90px;
    display: flex;
    flex-direction: row;
    border-radius: 15px;
    background: rgba(217, 217, 217, 0.50);
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    flex-wrap: wrap;
  }
  .final-product-card-details {
    padding: 60px 30px 60px 90px;
    width: 50%;
  }
  .final-product-card-label {
    font-family: {{ regular_font.family }};
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .final-product-card-price {
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    margin-top: 9px;
  }
  .zipper-pouch-description {
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 10px;
    margin-bottom: 40px;
  }
  .final-product-card-description {
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 10px;
  }
  .zipper-pouch-checkbox-container {
    display: flex;
    border-radius: 15px;
    border: 1px solid #5461C8;
    background: #f1f3f2;
  }
  #zipper-pouch-checkbox {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    border-radius: 5px;
    border: 1px solid #5461C8;
    background: #D9D9D9;
    margin: 13px 19px;
  }
  .zipper-pouch-checkbox-label {
    color: #5461C8;
    font-family: {{ regular_font.family }};
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding: 21px 26px 21px 0;
  }
  .zipper-pouch-image {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    width: 50%;
  }

  .zipper-image {
    max-width: 340px;
    max-height: 340px;
    width: 100%;
    height: 100%;
  }
  .dropdown-bandanna {
    margin-top: 24px;
    margin-bottom: 16px;
    padding: 12px 122px 13px 17px;
    background: white;
    color: rgba(0, 0, 0);
    font-family: {{ regular_font.family }};
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    border-radius: 5px;
  }
  .count-cuddle-label {
    font-family: {{ regular_font.family }};
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    margin-top: 120px;
  }

  #quantity-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  #quantity {
    text-align: center;
    margin: 0;
    background-color: white;
    color: black;
    border-radius: 0;
    border: 1px solid black;
    box-shadow: 0 0 0 0;
  }
  .add-to-cart-button {
    margin-top: 63px;
    margin-bottom: 120px;
    background: {{ add_cart_button_color }};
  }
  .add-to-cart-button:hover {
    background: {{ add_cart_button_color_hover }};
  }
  .image-capacity-full-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .image-capacity-full {
    /* display: none; */
    border-radius: 16px;
    border: 3px solid #5461C8;
    background: #D9D9D9;
    max-width: 486px;
    max-height: 125px;
  }
  .image-capacity-full-content {
    color: #5461C8;
    text-align: center;
    font-family: {{ regular_font.family }};
    font-size: 22px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding: 4.37px 60px 9px 54px;
  }
  .delete-button {
    width: 30px;
    height: 30px;
    padding: 3px 3px 0;
    background-color: #898181;
    top: 6px;
    left: 12px;
    cursor: pointer;
    color: #fff;
    border-radius: 50%;
    position: absolute;
    z-index: 1;
  }
  .image-container {
    /* flex: 0 0 calc(33.33% - 10px); */
    position: relative;
    cursor: pointer;
  }
  .nextStep-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  }


  /*
   .ear-position {
   display: none;
   }
*/


  /*
   #earImagesContainer {
   display: block;
   }
*/@media (max-width: 990px) {
    .color-container {
      flex-direction: column-reverse;
    }
    .pet-type-container {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      gap: 32px;
      width: 100%;
      background-color: #D9D9D9;
      border-radius: 15px;
      margin-bottom: 15px;
    }
    .input-label {
      display: flex;
      justify-content: center;
    }
    .input-label-description {
      padding: 9px 39px 11px 38px;
    }
    .productPositions {
      display: grid;
      grid-template-columns: auto auto;
      row-gap: 71px;
      column-gap: 19px;

    }
    .custom-flex-container {
      display: grid;
      grid-template-columns: auto auto auto;
      column-gap: 40px;
      row-gap: 20px;
    }
    .custom-flex-item {
      width: 100%;
      padding: 10px 0;
      text-align: center;
      transition: all 0.3s ease;
    }
    .one-half {
      width: 100%;
    }
    .second-half {
      width: 100%;
    }
    #multi-select {
      width: 40px;
      height: 40px;
      background-color: #D9D9D9;
    }
    .material-icons {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      /* Example color for tick icon */
      font-size: 24px;
      /* Example font size for tick icon */
    }

  }
  #quantity-buttons {
    display: flex;
    margin-top: 23px;
    flex: 1;
  }

  #quantity-button-increment {
    cursor: pointer;
    border-radius: 0;
    color: black;
    border: 1px solid black;
    padding: 5px;
    font-size: 16px;
    background-color: white;
    max-width: 50px;
  }

  #quantity-button-decrement {
    cursor: pointer;
    border-radius: 0;
    color: black;
    border: 1px solid black;
    padding: 5px;
    font-size: 16px;
    background-color: white;
    max-width: 50px;
  }

  #quantity-button:hover {
    background-color: black;
    color: white;
  }
  #quantity-container {
    max-width: 171px;
    width: 100%;
  }
  .quantity-box {
    width: 50px;
  }


  /* HIDE RADIO */
  .ear-images [type=radio] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  /* IMAGE STYLES */
  .ear-images .ear-label img {
    cursor: pointer;
    width: 110px;
    height: 110px;
    padding: 10px;
    border-radius: 50%;
  }


  .ear-images div.ear-label {
    position: relative;
  }


  /* CHECKED STYLES */


  .ear-images [type=radio]:checked + div.ear-label img {
    outline: 3px solid #5461c8;
    position: relative;
  }

  .ear-images [type=radio]:checked + img {
    outline: 2px solid #5461C8;
  }
  .ear-images [type=radio]:hover + img {
    outline: 2px solid #5461C8;
  }


  .ear-images [type=radio]:hover + div.ear-label img {
    outline: 3px solid #5461c8;
  }

  .ear-images [type=radio]:checked + div.ear-label::after,
  .ear-images [type=radio]:hover + div.ear-label::after {
    position: absolute;
    content: "";
    display: block;
    z-index: 2;
    top: 0;
    left: 0;
    right: 0;
    bottom: 2px;
    border-radius: 9999px;
    background: #5461C8;
    opacity: 0.5;
    transition: all 0.3s ease;
  }


  /* HIDE RADIO ------------------*/
  #template-page-product-customizer [type=radio] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }
  /* IMAGE STYLES */
  #template-page-product-customizer [type=radio] + div img {
    cursor: pointer;
    width: 128px;
    height: 128px;
    padding: 10px;
    border-radius: 50%;
  }
  #template-page-product-customizer [type=radio] + div {
    position: relative;
  }
  /* CHECKED STYLES */


  #template-page-product-customizer [type=radio]:checked + div img {
    outline: 3px solid #5461c8;
  }
  #template-page-product-customizer [type=radio]:hover + div img {
    outline: 3px solid #5461c8;
  }

  #template-page-product-customizer [type=radio]:checked + div.final-product-positions::after,
  #template-page-product-customizer [type=radio]:hover + div.final-product-positions::after {
    position: absolute;
    content: "";
    display: block;
    z-index: 2;
    top: 0;
    left: 0;
    right: 0;
    bottom: 2px;
    border-radius: 9999px;
    background: #5461c84d;
    opacity: 1;
    transition: all 0.3s ease;
  }

  .bothEyesText {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    margin-bottom: 29px;
    color: #000;
    font-family: {{ regular_font.family }};
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
  .leftEyeText {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    margin-bottom: 29px;
    color: #000;
    font-family: {{ regular_font.family }};
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
  .rightEyeText {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    margin-bottom: 29px;
    color: #000;
    font-family: {{ regular_font.family }};
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
  .disabled {
    pointer-events: none;
    /* Disable pointer events */
    opacity: 0.1;
    /* Make it visually indicate it's disabled */
  }
  .error-msg {
    color: red;
    padding: 5px;
    display: none;
  }
  .error {
    color: red;
    padding: 5px;
    display: block;
  }

  #email-label {
    display: flex;
    justify-content: flex-start;
  }
  #phone-label {
    display: flex;
    justify-content: flex-start;
  }
  .image-uploader .selected-image-container {
    display: block;
    margin: auto auto 15px;
    max-width: 270px;
    max-height: 340px;
    position: relative;
  }
  .image-uploader .selected-image-container img {
    width: 270px;
    height: 340px;
    border-radius: 10px;
  }
  .delete-button-unique {
    position: absolute;
    left: 5px;
    top: 5px;
    cursor: pointer;
    height: 25px;
    width: 25px;
    text-align: center;
    border-radius: 50%;
    background-color: #898181;
    color: white;
  }

  input[type="number"] {
    -moz-appearance: textfield;
    /* Firefox */
    appearance: textfield;
    /* Other browsers */
    width: 100%;
    /* Optional: Ensure input takes up full width */
  }
  input[type="checkbox"]#different-eye-color {
    -webkit-appearance: initial;
    appearance: initial;
    background: white;
    width: 40px;
    height: 40px;
    border: none;
    position: relative;
    border: 1px solid black;
  }
  input[type="checkbox"]#different-eye-color:checked {
    background: #5461C8;
  }
  input[type="checkbox"]#different-eye-color:checked:after {
    /* Heres your symbol replacement */
    content: "X";
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }
  input[type="checkbox"]#zipper-pouch-checkbox {
    -webkit-appearance: initial;
    appearance: initial;
    background: white;
    width: 40px;
    height: 40px;
    border: none;
    position: relative;
  }
  input[type="checkbox"]#zipper-pouch-checkbox:checked {
    background: #5461C8;
  }
  input[type="checkbox"]#zipper-pouch-checkbox:checked:after {
    /* Heres your symbol replacement */
    content: "X";
    color: #fff;


    /* The following positions my tick in the center,
     * but you could just overlay the entire box
     * with a full after element with a background if you want to */
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }
  #image-links {
    display: none;
  }
  #dragArea {
    width: 100%;
    max-width: 500px;
    margin: auto;
  }
  #popUpButton {
    border: 1px solid #5461C8;
    border-radius: 50%;
    padding: 5px;
    color: #5461C8;
    cursor: pointer;
  }
  .sizeDropdownDiv {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  .popup {
    display: none;
    position: fixed;
    z-index: 999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
  }

  /* Popup content */
  .popup-content {
    background-color: #fefefe;
    margin: 10% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 75%;
    max-width: 800px;
    position: relative;
  }

  /* Close button */
  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 35px;
    cursor: pointer;
    background-color: transparent;
  }
  .close:hover {
    background-color: transparent;
  }

  /* Styles for tabs */
  .tablinks-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .tablinks {
    display: inline-block;
    padding: 10px 45px;
    cursor: pointer;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin: 5px;
    float: left;
  }


  .tablinks.active {
    background-color: #5461C8;
    color: white;
  }

  /* Styles for tables */


  .tabcontent {
    display: none;
  }
  table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 20px;
    border-radius: 4px;
    overflow: hidden;
  }
  table td {
    text-align: center;
    vertical-align: top;
  }

  th,
  td {
    padding: 12px 15px;
    text-align: center;
    border: 1px solid gray;
  }

  th {
    background-color: #f5f5f5;
    font-weight: bold;
  }

  td {
    background-color: #fff;
  }

  tr:nth-child(even) td {
    background-color: #f9f9f9;
  }
  .sizechartTitle {
    text-align: center;
    text-transform: uppercase;
    font-size: 20px;
    margin: 0 0 20px;
    line-height: 1.2em;
    font-weight: bold;
  }

  .viewSizeChart {
    font-size: 18px;
    text-decoration: underline;
    color: {{ button_primary_color }};
    cursor: pointer;
    padding-top: 2px;
  }
  #size-chart-button {
    width: 128px;
    height: 128px;
    border-radius: 50%;
    margin-right: 10px;
    background-color: white;
    color: {{ button_primary_color }};
    cursor: pointer;
    border: 4px solid{{ button_primary_color }};
    cursor: pointer;
    font-family: {{ button_font.family }};
  }
  #size-chart-button:hover {
    width: 128px;
    height: 128px;
    border-radius: 50%;
    margin-right: 10px;
    background-color: {{ button_primary_color }};
    color: white;
    cursor: pointer;
    border: 4px solid{{ button_primary_color }};
    font-family: {{ button_font.family }};
  }
  #size-chart-button.selected {
    background-color: {{ button_primary_color }};
    color: white;
  }
  .imagesCheckbox {
    position: absolute;
    height: 30px;
    width: 30px !important;
    margin: 10px;
  }
  .thanks_message {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    display: none;
  }
  .petDetails_message {
    font-size: 20px;
    text-align: center;
    padding: 20px 180px;
    background-color: #5461C8;
    color: white;
  }
  .image-container {
    display: inline-block;
  }
  .image-container input[type="radio"] {
    display: none;
  }
  .image-container img.questionSelected {
    border: 3px solid #5461C8;
  }
  .thanks-message-content {
    color: white;
    margin-top: 20px;
  }
  .stepButtonDiv,
  .startSelectionButtonDiv {
    display: flex;
    justify-content: center;
  }
  .stepButton,
  .startSelectionButton {
    display: flex;
    justify-content: center;
    gap: 7px;
    align-items: center;
  }
  .radio-label {
    position: relative;
    display: inline-block;
    cursor: pointer;
  }

  .radio-label input[type="radio"] {
    display: none;
  }

  .radio-label img {
    display: block;
    width: 100%;
  }

  .radio-label::before {
    content: "";
    position: absolute;
    top: 10px;
    left: 10px;
    width: 15px;
    height: 15px;
    border: 2px solid transparent;
    border-radius: 50%;
    background: white;
    box-shadow: 0 0 0 1px #000;
    transition: background-color 0.3s
    , border-color 0.3s;
  }

  .radio-label.selected::before {
    background: #5461c8;
    border-color: #5461c8;
  }

  .radio-label:checked {
    background: #5461c8;
    border-color: #5461c8;
  }

  .image-container {
    position: relative;
    display: inline-block;
  }

  .delete-button-with-radio {
    width: 30px;
    height: 30px;
    padding: 3px 3px 0;
    background-color: #898181;
    top: 5px;
    left: 230px;
    cursor: pointer;
    color: #fff;
    border-radius: 50%;
    position: absolute;
    z-index: 1;
  }
  .rush_heading {
    font-size: 24px;
    font-weight: 600;
  }
  .rush_subheading {
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 64px;
    font-size: 22px;
  }
  .rush_message {
    text-align: center;
    font-size: 15px;
    font-style: italic;
    margin-top: 18px;
  }

  .rushSection select {
    width: 100%;
    max-width: 100%;
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.46);
    background: rgba(217, 217, 217, 0.45);
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    padding: 20px;
    margin-top: 95px;
    margin-bottom: 25px;
  }

  /*the container must be positioned relative:*/
  .custom-select {
    position: relative;
    margin-top: 95px;
  }

  .custom-select select {
    display: none;
    /*hide original SELECT element:*/
  }

  .select-selected {
    background-color: #c2c4c3;
    color: #43494d !important;
    border-radius: 15px;
    padding: 20px !important;
    box-shadow: 0 4px 4px 1px rgba(0, 0, 0, 0.25);
    border-color: transparent !important;
  }

  /*style the arrow inside the select element:*/
  .select-selected:after {
    position: absolute;
    content: "";
    top: 32px;
    right: 30px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-color: #000000 transparent transparent transparent;
  }

  /*point the arrow upwards when the select box is open (active):*/
  .select-selected.select-arrow-active:after {
    border-color: transparent transparent #000000 transparent;
    top: 25px;
  }

  /*style the items (options), including the selected item:*/
  .select-items div,
  .select-selected {
    color: #000000;
    padding: 12px 16px;
    border: 1px solid transparent;
    border-color: transparent;
    cursor: pointer;
    user-select: none;
    font-weight: 600;
    font-size: 16px;
  }

  .select-selected.select-arrow-active {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    box-shadow: none;
  }
  .select-selected.black {
    color: black;
  }

  /*style items (options):*/
  .select-items {
    position: absolute;
    background-color: #d9d9d9;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 99;
    border: 1px solid #d9d9d9;
    border-radius: 0 0 20px 20px;
    padding: 10px;
  }

  /*hide the items when the select box is closed:*/
  .select-hide {
    display: none;
  }

  .select-items div:hover,
  .same-as-selected {
    background-color: rgba(0, 0, 0, 0.1);
  }

  #rushSelect {
    color: black;
  }

  #rushSelect option:disabled {
    color: gray;
  }

  .iti__flag-container {
    left: 5px !important;
  }
  @media (max-width: 480px) {
    .delete-button-with-radio {
      left: 100px;
    }
    .tablinks-container {
      display: flex;
      flex-wrap: wrap;
    }
    .tablinks {
      display: inline-block;
      flex-grow: 1;
      flex-basis: calc(100% / 3);
      padding: 10px 45px;
      cursor: pointer;
      border: 1px solid #ccc;
      border-radius: 5px;
      margin: 5px;
      float: left;
    }
    .petDetails_message {
      padding: 0;
    }

  }
  @media (max-width: 575px) {
    .petDetails_message {
      padding: 0;
    }
    #different-eye-color-label {
      max-width: 219px;
    }
    .image-uploader .dragBox-label {
      display: none;
    }
    .dragBoxForUnique {
      display: flex;
      flex-direction: row;
      justify-content: center;
      padding: 0;
      border: 0;
      background-color: transparent;
      box-shadow: 0 0 0 0;
      max-width: 210px;
    }
  }
  @media (max-width: 998px) {
    .delete-button-with-radio {
      left: 100px;
    }
    .petDetails_message {
      padding: 0;
    }
    .dragBox {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding: 25px 0;
      margin: 0 auto;
      position: relative;
      text-align: center;
      font-weight: 500;
      color: #000;
      transition: transform 0.3s;
      border-radius: 20px;
      border: 1px solid #000;
      background: #d9d9d9;
      box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
      font-family: {{ regular_font.family }};
      font-size: 22px;
      input[type="file"] {
        position: absolute;
        border-radius: 20px;
        height: 100%;
        width: 100%;
        opacity: 0;
        top: 0;
        left: 0;
        padding: 0;
      }
    }
    .final-product-card {
      margin-top: 90px;
      display: flex;
      flex-direction: column-reverse;
      border-radius: 15px;
      background: rgba(217, 217, 217, 0.50);
      box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
      flex-wrap: wrap;
      max-width: 100%;
      max-height: 100%;
    }
    .final-product-card-details {
      width: 100%;
      justify-content: center;
      padding-left: 28px;
      padding-right: 37px;
      padding-top: 0;
    }
    .final-product-card-label,
    .final-product-card-price,
    .zipper-pouch-description {
      display: flex;
      justify-content: center;
      text-align: center;
    }
    .zipper-pouch-checkbox-outer {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .zipper-pouch-checkbox-container {
      margin-bottom: 60px;
      width: 100%;
      height: 66px;
      display: flex;
      align-items: center;
    }
    .zipper-pouch-image {
      width: 100%;
    }
    .final-product-card-description {
      display: flex;
      text-align: center;
      margin-bottom: 54px;
    }
    .zipper-pouch-checkbox-label {
      padding: 0;
    }
    .flex-box {
      display: flex;
      flex-wrap: wrap;
      padding: 0 10px;
    }
    .flex-box .number {
      position: relative;
      top: 35px;
      left: -10px;
      background-color: #5461c8;
    }
    .flex-box .number span {
      color: white;
    }
    .image-uploader textarea {
      margin-left: 30px;
      width: 82%;
    }
    .unique-characteristic-uploader {
      width: 100%;
    }
    .pet-breed-description {
      text-align: center;
    }
    #preview {
      display: flex;
      flex-wrap: wrap;
      text-align: center;
      margin-top: 60px;
      row-gap: 20px;
      column-gap: 6px;
      img {
        border-radius: 5px;
        width: 150px;
        height: 180px;
      }
    }
    .image-container {
      flex-basis: calc(50% - 5px);
    }
    .page-content form .step-3 {
      margin-top: 40px;
      margin-left: auto;
      margin-right: auto;
      max-width: 346px;
    }
    .delete-button {
      left: 6px;
    }
  }


  /* free product style */
  .create-pet1 {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 10000;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);

    input,
    select {
      display: block;
      width: 100%;
      height: 40px;
      margin: 20px 0;
      padding: 0 10px;
      border: 1px solid #dedede;
      background-color: transparent;

       &:focus {
        outline: none;
      }
    }
  }

  .create-pet__form1 {
    background: #fff;
    max-width: 400px;
    width: calc(100% - 60px);
    padding: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow-y: auto;
  }

  .create-pet__form1::-webkit-scrollbar {
    width: 12px;
    /* Width of the entire scrollbar */
  }
  .create-pet__form1::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* Background of the scrollbar track */
    border-radius: 16px;
    /* Rounded corners of the track */
    margin: 10px;
  }

  .create-pet__form1::-webkit-scrollbar-thumb {
    background-color: #888;
    /* Color of the scrollbar thumb */
    border-radius: 16px;
    /* Rounded corners of the thumb */
    border: 3px solid #f1f1f1;
    /* Adds space around the thumb for padding */
  }

  .create-pet__form1::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* Color of the thumb on hover */
    cursor: pointer;
  }


  .free-product1 {
    max-width: 450px !important;
  }
  .gift-button1 {
    cursor: pointer;

  }
  .create-pet__error1 {
    margin: -15px 0 0;
    padding: 0;
    color: red;
  }

  .create-pet__form1 {
    background: #fff;
    max-width: 400px;
    width: calc(100% - 60px);
    padding: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .free-product1 {
    max-width: 900px !important;
    max-height: 700px;
    height: 100%;
    border: 1px solid black;
    border-radius: 15px;
    text-align: center;
  }
  .gift-button1 {
    cursor: pointer;

  }
  .create-pet__error1 {
    margin: -15px 0 0;
    padding: 0;
    color: red;
  }
  .popUpContainer {
    display: flex;
  }

  #free_product_title {
    font-weight: 700 !important;
    margin: 10px 0;
    font-size: 22px;
  }

  .freeProductName {
    font-weight: bold;
  }

  .productImageContainer {
    display: flex;
    width: 50%;
    justify-content: center;
    align-items: center;
  }

  .productImage {
    margin-top: 20%;
    width: 400px;
    height: 400px;
    object-fit: fill;
    border-radius: 10px;
  }

  .productDetailsContainer {
    width: 50%;
    padding-top: 35px;
  }

  .productName {
    text-align: left;
    margin-left: 25px;
    font-weight: 700;
    font-size: 22px;
    margin-bottom: 10px;
  }

  .productPriceContainer {
    text-align: left;
    margin: 10px 25px;
  }

  .productOriginalPrice {
    font-weight: 500;
    font-size: 22px;
  }

  .productComparePrice {
    font-size: 22px;
    font-weight: 500;
    color: rgb(0, 0, 0, 0.32);
    text-decoration: line-through;
  }

  .productReviews {
    text-align: left;
    margin-left: 25px;
  }

  .selector-wrapper-free-product {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: baseline;
    margin-bottom: 10px;
    margin-left: 25px;
  }

  .selector-wrapper-free-product label {
    font-weight: 700;
    color: #363636;
    font-size: 1rem;
  }

  .selector-wrapper-free-product label:after {
    content: ":";
  }

  .selectFreeVariant select {
    margin: 8px 0;
  }

  .selectFreeVariant:not(.is-multiple) {
    height: unset;
  }

  .selectFreeVariant select {
    border: 1px solid black;
    border-radius: 8px;
    font-size: 17px;
  }

  .selectFreeVariant {
    position: relative;
    display: inline-block;
    width: auto;
    max-width: 100%;
    vertical-align: top;
  }

  .close-button-free-product-popup {
    display: flex;
    position: relative;
    top: -70px;
    left: 100%;
    cursor: pointer;
    background-color: #5461c8;
    border: 1px solid #5461c8;
    border-radius: 50%;
    font-size: 20px;
    padding: 2px;
    margin: 0;
    line-height: 1;
    color: white;
    width: 30px;
    height: 30px;
    justify-content: center;
  }

  .close-button-uploader-free-product {
    display: none;
    position: relative;
    top: -8px;
    left: 100%;
    cursor: pointer;
    background-color: #5461c8;
    border: 1px solid #5461c8;
    border-radius: 50%;
    font-size: 16px;
    padding: 2px;
    margin: 0;
    line-height: 1;
    color: white;
  }

  .close-button-uploader-free-product:hover {
    color: white;
    border: 1px solid #5461c8;
    padding: 2px;
  }

  .dragDropText {
    font-weight: unset;
    cursor: pointer;
  }

  .productUploaderHeading {
    font-size: 20px;
    font-weight: 400;
    text-align: left;
    margin-left: 25px;
    margin-bottom: 10px;
  }

  .uploadOuterFree {
    margin: 10px 25px;
  }

  .uploadOuterFree .dragBox label {
    border: 3px solid #5461c8;
    border-radius: 16px;
    padding: 8px 20px;
    color: #5461c8;
  }
  .uploadOuterFree .dragBox strong {
    margin: 0 20px;
    font-weight: 500;
  }
  .uploadOuterFree .dragBoxFree label {
    border: 3px solid #5461c8;
    border-radius: 10px;
    padding: 0 20px;
    color: white;
    background-color: #5461C8;
  }
  .uploadOuterFree .dragBoxFree strong {
    margin: 0 20px;
    font-weight: 500;
  }

  .swatches-label-free-product {
    text-align: left;
    margin-left: 25px;
    font-weight: 700;
  }

  .customRadioInputs {
    width: 0 !important;
  }

  .dragBoxFree {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 10px 0;
    margin: 0 auto;
    position: relative;
    text-align: center;
    font-weight: 500;
    color: #000;
    transition: transform 0.3s;
    border-radius: 10px;
    background: #d9d9d9;
    font-size: 22px;
    cursor: pointer;
    input[type="file"] {
      position: absolute;
      border-radius: 20px;
      height: 100%;
      width: 100%;
      opacity: 0;
      top: 0;
      left: 0;
      padding: 0;
    }
  }

  .add-to-cart-button-free-product {
    background-color: #5461c8;
    padding: 10px 20px;
    max-width: 315px;
    width: 100%;
    border: 1px solid #5461c8;
    border-radius: 24px;
    color: #fff;
    font-size: 20px;
    font-family: Inter;
    font-weight: 600;
    letter-spacing: 3px;
    margin-top: 30px;
    cursor: pointer;
  }

  .close-button-free-product-popup {
    left: 95%;
  }

  .swatchesImage input[type="radio"] + img {
    border-radius: 50%;
    cursor: pointerselectBackground;
  }

  .swatchesImage input[type="radio"]:checked + img {
    outline: 2px solid #5461c8;
    border-radius: 50%;
  }

  .rounded-button {
    border-radius: 50%;
    margin-bottom: 7px;
    padding: 5px;
    background-color: white;
    color: black;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
    border: 1px solid gray;
    width: 40px;
    height: 40px;
    margin-right: 5px;
  }
  .rounded-button:hover {
    border-color: #5461c8;
    border: 2px solid #5461c8;
  }

  .rectangle-button {
    border-radius: 10px;
    padding: 5px;
    background-color: white;
    color: black;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
    border: 1px solid gray;
    /* width: 80px; */
    height: 30px;
    margin: 3px;
  }
  .rectangle-button:hover {
    border-color: #5461c8;
    border: 2px solid #5461c8;
  }
  .swatches-select {
    display: flex;
    flex-direction: column;
  }
  .swatches-label {
    font-weight: bold;
    color: #363636;
    margin-bottom: 5px;
  }
  [type=radio] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  /* IMAGE STYLES */
  [type=radio] + img {
    cursor: pointer;
    border-radius: 50%;
  }

  /* CHECKED STYLES */
  [type=radio]:checked + img {
    outline: 2px solid #5461c8;
    border-radius: 50%;
  }
  #selected-swatches {
    font-weight: 400;
  }
  .custom-option-input-label-image-1 {
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }

  .frame-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .frame-heading {
    text-align: center;
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 100px;
  }
  .frame-options-container {
    display: grid;
    gap: 100px;
    grid-template-columns: repeat(2, 1fr);
    justify-items: center;
  }
  .frame-image-container {
    position: relative;
  }
  .frame-image {
    width: 234px !important;
    height: 234px !important;
    border-radius: 10px;
  }
  .background-image {
    width: 234px !important;
    height: 234px !important;
    border-radius: 10px;
  }
  .frame-additional-price {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 80px;
    height: 40px;
    background-color: white;
    color: #8F288F;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
  }
  .frame-option-heading {
    text-align: center;
    font-size: 20px;
    font-weight: 500;
  }
  .frame-details-heading {
    color: black;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    text-decoration: underline;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    font-weight: 500;
  }
  .frame-tooltip {
    display: none;
    width: 120px !important;
    background-color: black !important;
    color: #fff !important;
    text-align: center !important;
    border-radius: 6px !important;
    padding: 5px 10px !important;
    position: absolute !important;
    z-index: 1 !important;
    bottom: 35px !important;
    left: 50% !important;
    margin-left: -60px !important;
  }
  .frame-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: black transparent transparent transparent;
  }

  .frame-details-heading:hover .frame-tooltip {
    display: block;
  }

  .background-options-heading {
    text-align: center;
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 100px;
    margin-top: 100px;
  }
  .background-options-container {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(3, 1fr);
    justify-items: center;
  }

  @media (max-width: 800px) {
    .productReviews {
      margin: 10px 0;
      text-align: center;
    }
    .close-button-free-product-popup {
      left: 45%;
    }
    .selector-wrapper-free-product {
      flex-direction: column;
    }
    .popUpContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .productImage {
      width: 226px;
      height: 222px;
    }
    .productName {
      text-align: center;
      margin: 0;
      font-size: 18px;
    }
    .productPriceContainer {
      text-align: center;
    }
    .orText {
      display: none;
    }
    .dragBoxText {
      display: none;
    }
    .dragDropText {
      display: none;
    }
    .dragBoxFree {
      display: flex;
      flex-direction: column;
    }
    .uploadOuterFree {
      margin: 0;
    }

    .dragBoxFree {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding: 10px;
      margin: 0 auto;
      position: relative;
      text-align: center;
      font-weight: 500;
      transition: transform 0.3s;
      font-size: 22px;
      border: 0;
      background: #d9d9d9;
      box-shadow: none;
      input[type="file"] {
        position: absolute;
        border-radius: 20px;
        height: 100%;
        width: 100%;
        opacity: 0;
        top: 0;
        left: 0;
        padding: 0;
      }
    }
  }
  @media (max-width: 480px) {
    .close-button-free-product-popup {
      top: -50px;
      left: 45%;
    }

    .productDetailsContainer {
      width: 100% !important;
    }
    .productReviews {
      margin: 10px 0;
      text-align: center;
    }
    .selector-wrapper-free-product {
      align-items: center;
      margin-left: 0;
      gap: 6px;
      margin-bottom: 25px;
    }
    .swatches-label-free-product {
      margin-left: 0;
      margin-bottom: 15px;
      text-align: center;
    }
    .productImageContainer {
      width: 85%;
    }
    .create-pet__form1 {
      padding: 20px;
    }
  }
</style>

{%- capture contentForQuerystring -%}{{ content_for_header }}{%- endcapture -%}
{%- assign pageUrl = contentForQuerystring | split: '"pageurl":"' | last | split: '"' | first | split: '.myshopify.com' | last | replace: '\/', '/' | replace: '%20', ' ' | replace: '\u0026', '&' -%}{%- assign productId = pageUrl | split: '?' | last | split: '=' | last | strip -%}
{% assign plushProduct = all_products[productId] %}
{% if plushProduct.metafields.cuddleclones.product_customizer.type == "json" %}
  {% assign customizer = plushProduct.metafields.cuddleclones.product_customizer.value %}{% endif %}
{% comment %} <p>{{customizer}}</p> {% endcomment %}
{% assign oms = shop.metafields.cuddleclones.api_details.value %}<section class="top-banner">
  <h2>{{ customizer[0].customizer_main_heading }}</h2>
</section>
<section class="page-content">
  <div class="custom-container">
    <h3>Your Information:</h3>
    {% comment %} <p>========== {{ customizer[5].final_options.products }}</p> {% endcomment %}
    <form
      method="post"
      action="/cart/add"
      id="customizer_form">
        {% comment %} {% if plushProduct.tags contains "offer-free-gift" %} {% endcomment %}
      <input
        type="hidden"
        name="properties[_original_unique_key]"
        value=""
        id="originalUniqueKey" />
        {% comment %} {% endif %} {% endcomment %}
      <input
        type="hidden"
        name="id"
        id="formId"
        value="{{plushProduct.first_available_variant.id}}" />
      {% if plushProduct.tags contains "RUSH" %}
        <input
          type="hidden"
          name="properties[_rush]"
          value="true">
      {% endif %}
      <div class="step-1 step1">
        <div>
          <div class="input-label" id="email-label">
            <label for="email">Email*:</label>
          </div>
          <div class="form-field">
            <input
              id="email"
              class="email"
              type="email"
              name="properties[email]"
              placeholder="Enter Your Email Here"
              autofocus 
              onkeydown="focusPhoneOnEnter(event)" />

          </div>
        </div>
        <div>
          <div
            class="input-label"
            id="phone-label"
            style="{% if localization.country.iso_code != 'US' %}display: none;{% endif %}">
            <label for="phone">Phone Number:</label>
          </div>
          <div class="form-field">
            <input
              id="phone"
              maxlength="13"
              type="{% if localization.country.iso_code == 'US' %}tel{% else %}hidden{% endif %}"
              placeholder="Enter Your Phone Number"
              onkeydown="focusNextBtn_1(event)" />
            <small id="error-message" style="color: red; display: none;">Please enter a valid US phone number.</small>
          </div>
        </div>

        <div class="form-button nextStep-button">
          <button
            type="button"
            data-id="get-to-know"
            class="nextStep-button">
            Next Step
            <span class="material-symbols-outlined">
              arrow_forward
            </span>
          </button>
        </div>
      </div>
      <div class="step2">
        <hr class="horizontal-line" />
        <h3 id="get-to-know">{{ customizer[1].pet_type[0].main_heading }}</h3>
        <div class="pet-images">
          <input
            id="pet-type"
            type="hidden"
            name="properties[_pet_type]"
            value="" />
          {% for specie in customizer[1].pet_type[1].specie %}
            <div
              class="pet-image-container"
              onclick="selectedPetType(event)"
              data-value="{{specie.name}}">
              <div
                onclick="selectedPetType(event)"
                onkeydown="handlePetImageKeydown(event)"
                tabindex="0"
                data-value="{{specie.name}}"
                class="pet-image"><img src="{{specie.image}}" alt="{{specie.name}}_PetType" /></div>
              <div style="justify-content: center;">
                <span>{{ specie.name | capitalize }}</span>
              </div>
            </div>
          {% endfor %}
        </div>
        <h3>{{ customizer[1].pet_type[0].second_heading }}</h3>

        <div class="step-2">
          <div>
            <div class="input-label">
              <label for="petname">Your Pet’s Name</label>
            </div>
            <div>
              <p class="pet-breed-description">{{ customizer[1].pet_type[0].pet_name_description }}</p>
            </div>
            <div class="form-field">
              <input
                id="petname"
                class="petName"
                type="type"
                tabindex="0"
                name="properties[_pet_name]"
                onkeydown="goToBreedSelect(event)"
                placeholder="Enter Your Pet’s Name Here" />
            </div>
          </div>
          <div>
            <div class="input-label">
              <label for="breedSelect">Your Pet’s Breed</label>
            </div>
            <div>
              <p class="pet-breed-description">{{ customizer[1].pet_type[0].pet_breed_subheading }}</p>
            </div>
            <div class="form-field">
              <select id="breedSelect" name="properties[_pet_breed_specie]" onkeydown="goToPetAge(event)">
                <option
                  tabindex="0"
                  value=""
                  disabled
                  selected>Pug, Beagle, Great Dane, etc
                </option>
              </select>
            </div>

            <div class="form-field otherMixedDiv" style="display: none;">
              <div class="input-label">
                <label for="other-breed">Breed(s)</label>
              </div>
              <input
                id="other-breed"
                class="otherMixedInput"
                type="text"
                placeholder="Enter Other/Mixed Breed"
                name="properties[Breed/Other]"
                value=""
                onkeydown= "MixedBreedtoPetAge(event)"
                oninput="this.setAttribute('value', this.value);">
            </div>
          </div>
          <div class="mt-40">
            <div class="input-label">
              <label for="petAge">Your Pet’s Age</label>
            </div>
            <div>
              <p class="pet-breed-description">{{ customizer[1].pet_type[0].pet_age_subheading }}</p>
            </div>
            <div class="form-field">
              <select name="properties[_pet_age]" id="petAge">
                <option
                  tabindex="0"
                  selected
                  disabled
                  value="">Select Age</option>
                {% for age in customizer[1].pet_type[2].pet_ages %}
                  <option value="{{age.name}}">{{ age.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-button nextStep-button">
            <button
              data-id="get-to-know"
              onclick="scrollToElement(event)"
              class="nextStep-button">
              Next Step
              <span class="material-symbols-outlined">
                arrow_forward
              </span>
            </button>
          </div>
        </div>
        <hr class="horizontal-line" />
      </div>
      <div class="step3">
        <h3 id="pet-details">{{ customizer[2].pet_detail[0].main_heading }}</h3>
        <div class="step-3">
          <h3>{{ customizer[2].pet_detail[0].upload_pet_photos_heading }}</h3>
          <div class="upload-photo-content" id="photo-upload-instructions">
            <p>
              {{ customizer[2].pet_detail[0].upload_pet_photo_description_1 }}
            </p>
            <p>{{ customizer[2].pet_detail[0].upload_pet_photo_description_2 }}</p>
          </div>
          <div class="uploadOuter" id="dragArea"onkeydown="clickImageINput(event)" tabindex="0" aria-describedby="photo-upload-instructions">
            <span class="dragBox">
              <div>
                <span class="dragDropText">
                  Drag + Drop
                </span>
                <input
                  style="cursor: pointer;"
                  onclick="this.value=null;"
                  type="file"
                  aria-label="imageUploader"
                  onchange="dragNdrop(event)"
                  ondragover="drag()"
                  ondrop="drop()"
                  id="uploadFile"
                  class="imageUploader"
                  multiple />
              </div>
              <span style="margin-top: 9px;" class="orText">
                <strong>OR</strong>
              </span>
              <div style="margin-top: 9px;">
                <input type="file" id="uploadFileImage" name="uploadFileImage" accept="image/*" style="display: none;" />
                <label for="uploadFileImage" class="btn btn-primary ">
                  <img
                    style="height: 19px; width: 25px"
                    src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/7500a60f83aabde22eb73dc843c72079.png?v=1706695966"
                    alt="Cameralogo" />
                  Select File
                </label>
              </div>
            </span>
          </div>
          <div class="image-capacity-full-container" id="image-capacity-full-container">
            <div class="image-capacity-full">
              <p class="image-capacity-full-content">Max Number of Images (10) Already Uploaded</p>
            </div>
          </div>
          <div class="thanks-message-content" aria-live="polite">
            <p class="petDetails_message thanks_message">{{ customizer[2].pet_detail[0].thanks_message }}</p>
          </div>

          <div class="" aria-live="polite" aria-atomic="true">
            {% for question in customizer[2].pet_detail[0].questions %}
              {% assign labelText = question.label | replace: " ", "" %}
              <p  class="petDetails_message {{labelText}}_message" style="display: none;">{{ question.question_text }}</p>
              {% if question.example_text and question.example_text != "" %}
                <p  class="petDetails_message {{labelText}}_example_message" style="display: none;padding:20px 0px;">{{ question.example_text }}</p>
              {% endif %}
            {% endfor %}
          </div>
          <div id="preview"></div>
          {% for nextButton in customizer[2].pet_detail[0].questions %}
            {% assign buttonName = nextButton.label | replace: " ", "" %}
            <div class="form-button nextStep{{buttonName}}ButtonDiv stepButtonDiv" style="display: none;">
              <button
                data-id="{{nextButton.label}}"
                class="stepButton nextStep{{buttonName}}Button"
                name="{{nextButton.label}}">
                Next Step
                <span class="material-symbols-outlined">
                  arrow_forward
                </span>
              </button>
            </div>
          {% endfor %}
          <div class="form-button startSelectionButtonDiv" style="display: none;">
            <button data-id="get-to-know" class="startSelectionButton">
              Next Step
              <span class="material-symbols-outlined">
                arrow_forward
              </span>
            </button>
          </div>
        </div>
        <hr class="horizontal-line" />
      </div>

      <div class="step3section2">
        <div class="unique-characteristic" id="unique_characteristic">
          <h3>{{ customizer[2].pet_detail[0].unique_characteristics_heading }}
            <br>
            <span style="color: #5461c8;">(Optional Step)</span>
          </h3>

          <p>
            {{ customizer[2].pet_detail[0].unique_characteristics_description }}
          </p>
          <div class="flex-box">
            <div class="number">
              <span>1</span>
            </div>

            <div class="unique-characteristic-uploader">
              <div class="image-uploader" id="image-uploader-1">
                <textarea
                  aria-label="unique_characteristic"
                  name="properties[_pet_characteristic_1]"
                  rows="5"
                  cols="20"
                  placeholder="Describe your pets unique characteristic here..."></textarea>
                <input
                  type="hidden"
                  value=""
                  class="uniqueUploadedImage-1"
                  name="properties[characteristic_1]" />

                <div class="selected-image-1 selected-image-container">
                  {% comment %} <div class="delete-button-unique" onclick="deleteUniqueImage(event)">X</div> {% endcomment %}
                </div>
                <div class="uploadOuter">
                  <span class="dragBoxUnique">
                    <span class="dragDropText">Drag + Drop</span>
                    <input
                      style="cursor: pointer;"
                      onclick="this.value=null;"
                      type="file"
                      aria-label="file_uploader"
                      class="image_uploader"
                      onChange="dragNdropUnique(event, 'image-uploader-1',1)"
                      ondragover="drag()"
                      ondrop="drop()"
                      id="uploadFile-1" />
                    <span class="orText">
                      <strong>OR</strong>
                    </span>
                    <label for="uploadFile-1" class="btn btn-primary">
                      <img
                        style="height: 19px; width: 25px"
                        src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/7500a60f83aabde22eb73dc843c72079.png?v=1706695966"
                        alt="Cameralogo" />
                      Select File
                    </label>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="add-btn">
          <button onclick="addUploader(event)" id="addUniqueButton">
            + Add Another Unique Characteristics
          </button>
        </div>
        <div class="form-button nextStep-button">
          <button
            data-id="get-to-know"
            onclick="scrollToElement(event)"
            class="nextStep-button">
            Next Step
            <span class="material-symbols-outlined">
              arrow_forward
            </span>
          </button>
        </div>
        <hr class="horizontal-line" />
      </div>
      <div class="step4">
        <div id="pet-info">
          <div class="step-4">
            <h3>{{ customizer[3].pet_info[0].main_heading }}</h3>
            <h3 class="mt-20">{{ customizer[3].pet_info[0].eye_color_heading }}</h3>
            <h4>{{ customizer[3].pet_info[0].select_eye_color_for_both_eyes_heading }}</h4>
            <div class="multi-color-select">
              <input
                id="different-eye-color"
                aria-label="different-eye-color"
                type="checkbox"
                onchange="showHideSecondPalette(this)" />
              <p id="different-eye-color-label">Select if your pet has 2 different colored eyes</p>
            </div>
          </div>
          <input
            type="hidden"
            name="properties[_pet_left_eye]"
            id="left-eye-color"
            value="">
          <input
            type="hidden"
            name="properties[_pet_right_eye]"
            id="right-eye-color"
            value="">
          <div class="color-container" id="color_container">
            <div class="one-half">
              <div>
                <p class="bothEyesText" id="bothEyesText">Both Eyes</p>
              </div>
              <div>
                <p class="leftEyeText" id="leftEyeText">Left Eye Color</p>
              </div>
              <div class="custom-flex-container" id="first-palette">
                {% for eye_type in customizer[3].pet_info[1].eye_types %}
                  {% if eye_type.label == 'Missing' %}
                    <div class="custom-flex-item" onclick="setEyeColor(this,'left', '{{ eye_type.label }}');">
                      <span
                        title="{{ eye_type.label }}"
                        class="color"
                        tabindex="0"
                        aria-label="{{ eye_type.label }}"
                        onkeydown="eyeColorKeydown(event)"
                        style="background: {{ eye_type.color }}"></span>
                      <span class="title">{{ eye_type.label }}</span>
                    </div>
                  {% else %}
                    <div class="custom-flex-item" onclick="setEyeColor(this,'left', '{{ eye_type.label }}');">
                      <span class="color" tabindex="0" aria-label="{{ eye_type.label }}"  onkeydown="eyeColorKeydown(event)" style="background: {{ eye_type.color }}"></span>
                      <span class="title">{{ eye_type.label }}</span>
                    </div>
                  {% endif %}
                {% endfor %}
              </div>
            </div>
            <div
              class="second-half"
              id="second-palette"
              style="display: none;">
              <div>
                <p class="rightEyeText" id="rightEyeText">Right Eye Color</p>
              </div>
              <div class="custom-flex-container">
                {% for eye_type in customizer[3].pet_info[1].eye_types %}
                  {% if eye_type.label == 'Missing' %}
                    <div class="custom-flex-item" onclick="setEyeColor(this,'right', '{{ eye_type.label }}');">
                      <span
                        title="{{ eye_type.label }}"
                        class="color"
                         tabindex="0"
                         aria-label="{{ eye_type.label }}"
                        onkeydown="eyeColorKeydown(event)"
                        style="background: {{ eye_type.color }}"></span>
                      <span class="title">{{ eye_type.label }}</span>
                    </div>
                  {% else %}
                    <div class="custom-flex-item" onclick="setEyeColor(this,'right', '{{ eye_type.label }}');">
                      <span class="color"  tabindex="0" aria-label="{{ eye_type.label }}"  onkeydown="eyeColorKeydown(event)" style="background: {{ eye_type.color }}"></span>
                      <span class="title">{{ eye_type.label }}</span>
                    </div>
                  {% endif %}
                {% endfor %}
              </div>
            </div>
          </div>
        </div>

        <div class="ear-position" id="earImagesContainer">
          {% comment %} Ear images preview here...  {% endcomment %}
        </div>
        <div class="next-button-container ">
          <button id="next-button" class="nextStep-button">
            Next Step
            <span class="material-symbols-outlined">
              arrow_forward
            </span>
          </button>
        </div>
        <hr class="horizontal-line" />
      </div>
      <div class="step5">
        <div class="product-options" id="productOptionsContainer">
          <!-- here is content of pet images -->
        </div>
      </div>
      {% if customizer[7] and customizer[7].display %}
        <div class="plush-frame-options">
          <div class="frame-container">
            <p class="frame-heading">{{ customizer[7].frame_heading }}</p>
            <div class="frame-options-container">
              {% assign className = customizer[7].frame_heading | downcase | replace: ' ', '' | append: '_frame' %}
              <input
                type="hidden"
                value=""
                name="properties[frame_option]"
                class={{ className }}>
              {% for option in customizer[7].frame_options %}
                {% if option.display %}
                  <div>
                    <div class="frame-image-container">
                      <img
                        alt="{{  option.label }}"
                        src="{{option.image}}"
                        class="frame-image"
                        tabindex="0"
                        onkeydown="handleFrameKeyDown(event)"
                        onclick="selectPlushFrameOptions('{{option.label | downcase}}', '{{ className }}', this, {{option.value}})">
                      <div class="frame-additional-price">
                        + ${{ option.additional_price }}</div>
                      <p class="frame-option-heading">{{ option.label }}</p>
                      <div class="frame-details-heading">
                        {{ option.details_heading }}
                        <img
                          width="15px"
                          height="15px"
                          src="{{option.tooltip_icon}}"
                          alt="questionMark">
                        <span class="frame-tooltip">{{ option.tooltip }}</span>
                      </div>
                    </div>
                  </div>
                {% endif %}
              {% endfor %}
            </div>
          </div>
          <div>
            <p class="background-options-heading">{{ customizer[7].backgrounds_heading }}</p>
            <div class="background-options-container">
              {% assign className = customizer[7].backgrounds_heading | downcase | replace: ' ', '' | append: '_background' %}
              <input
                type="hidden"
                value=""
                name="properties[background_option]"
                class={{ className }}>
              {% for option in customizer[7].background_options %}
                {% if option.display %}
                  <div>
                    <img
                      alt="{{ option.label }} Background"
                      src="{{option.image}}"
                      class="background-image"
                      tabindex="0"
                      onkeydown ="handleFrame_BGkeydown(event)"
                      onclick="selectPlushFrameOptions('{{option.label | downcase}}', '{{ className }}', this, 0)">
                    <p class="frame-option-heading">{{ option.label }}</p>
                  </div>
                {% endif %}
              {% endfor %}
            </div>
          </div>
          <div class="next-button-container ">
            <button id="next-button" class="nextStep-button">
              Next Step
              <span class="material-symbols-outlined">
                arrow_forward
              </span>
            </button>
          </div>
          <hr class="horizontal-line" />
        </div>
      {% endif %}

      {% if customizer[6].available_us_only and localization.country.iso_code == 'US' %}
        {% if customizer[6] and customizer[6].display %}
          <div class="rushContainer">
            <div class="rushSection">
              <h3 class="rush_heading">{{ customizer[6].rush_heading }}</h3>
              <p class="rush_subheading">{{ customizer[6].rush_subheading }}</p>
              <p class="rush_message">{{ customizer[6].rush_note }}</p>
                
              <label class="labelAlert_hide" for="rushSelect">Select Creation Time</label>
              <div class="form-field custom-select">
                <select class="rushSelect" id="rushSelect">
                  <option
                    selected
                    disabled
                    style="color: gray !important;"
                    value="">Select Creation Time</option>
                  {% for option in customizer[6].options %}
                    {% if option.display %}
                      <option value="{{option.value}}">{{ option.label }}</option>
                    {% endif %}
                  {% endfor %}
                </select>
              </div>

              <div class="next-button-container ">
                <button
                  id="next-button"
                  class="nextStep-button"
                  style="margin-top: 100px !important;">
                  Final Step
                  <span class="material-symbols-outlined">
                    arrow_forward
                  </span>
                </button>
              </div>
            </div>
            <hr class="horizontal-line" />
          </div>
        {% endif %}
      {% elsif customizer[6].display and customizer[6].available_us_only == false %}
        <div class="rushContainer">
          <div class="rushSection">
            <h3 class="rush_heading">{{ customizer[6].rush_heading }}</h3>
            <p class="rush_subheading">{{ customizer[6].rush_subheading }}</p>
            <p class="rush_message">{{ customizer[6].rush_note }}</p>

            <div class="form-field custom-select">
              <select class="rushSelect" id="rushSelect">
                <option
                  selected
                  disabled
                  style="color: gray !important;"
                  value="">Select Creation Time</option>
                {% for option in customizer[6].options %}
                  {% if option.display %}
                    <option value="{{option.value}}">{{ option.label }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>

            <div class="next-button-container ">
              <button
                id="next-button"
                class="nextStep-button"
                style="margin-top: 100px !important;">
                Final Step
                <span class="material-symbols-outlined">
                  arrow_forward
                </span>
              </button>
            </div>
          </div>
          <hr class="horizontal-line" />
        </div>

      {% endif %}

      {% assign products = customizer[5].final_options.products %}
      {% if products.size > 0 %}
        {% for product in products %}
          {% if product.name == "cuddle_crate" %}
            <input
              type="hidden"
              class="cuddleCrateInput"
              name="properties[cuddle_crate]"
              value="No">
          {% endif %}
        {% endfor %}
      {% endif %}

      <div class="step6">
        <div class="final-options">
          {% assign products = customizer[5].final_options.products %}
          {% if products.size > 0 %}

            <div>
              <p class="final-options-label">{{ customizer[5].final_options.final_option_main_heading }}</p>
            </div>
            <div>
              <p class="upgrade-label">{{ customizer[5].final_options.final_option_upgrade_clone_heading }}</p>
            </div>


            {% for product in products %}
              {% if product.display and product.available_us_only %}
                {% if localization.country.iso_code == 'US' %}
                  <div class="zipper-pouch-card final-product-card">
                    <div class="zipper-details final-product-card-details">
                      <p class="zipper-pouch-label final-product-card-label">{{ product.detail.heading }}</p>
                      <p class="zipper-pouch-price final-product-card-price">{{ product.detail.price }}</p>
                      <p class="custom-bandana-description final-product-card-description">{{ product.detail.description }}</p>
                      {% if product.name == "custom_bandana" %}
                        <div class="bandanaInput">
                          <label class="labelAlert_hide" for="BandannaInput">Enter Bandana Name</label>
                          <input
                            id="BandannaInput"
                            class="bandannaInput"
                            type="type"
                            value=""
                            oninput="this.setAttribute('value', this.value);"
                            placeholder="Enter Bandana Pet Name"
                            oninput="validateInput(this)" />
                        </div>
                      {% endif %}
                      {% if product.detail.variant.size > 0 %}
                        <div class="form-field">
                          <select class="dropdown-bandanna {{product.name}}_select" name="Bandanna_Color {{product.name}}_Color">
                            <option selected disabled>Select {{ product.detail.heading }} Variant</option>
                            {% for variant in product.detail.variant %}
                              {% if variant.display %}
                                <option value="{{ variant.value }}">{{ variant.name }}</option>
                              {% endif %}
                            {% endfor %}
                          </select>
                        </div>
                      {% else %}
                        <input
                          type="hidden"
                          value={{product.detail.default_variant_id}}
                          class="input_{{product.name}}">
                        <div class="dropdown-bandanna" style="background:transparent;"></div>
                      {% endif %}

                      <div class="zipper-pouch-checkbox-outer">
                        <div class="zipper-pouch-checkbox-container">
                          <input
                            id="zipper-pouch-checkbox"
                            aria-label="{{ product.detail.heading }}. {{ product.detail.description }}. for {{ product.detail.price }}"
                            type="checkbox"
                            class="add_custom_bandanna {{product.name}}_checkbox" />
                          <p class="zipper-pouch-checkbox-label">Add {{ product.detail.heading }}</p>
                        </div>
                      </div>
                    </div>
                    <div class="zipper-pouch-image">
                      <img
                        src={{ product.detail.image }}
                        alt=""
                        class="zipper-image">
                    </div>
                    {% if product.name == "zipper_pouch" %}
                        <input
                        type="hidden"
                        name="properties[{{product.name}}]"
                        id="{{product.name}}"
                        value="No" />
                    {% elsif  product.name == "heartbeat_box"  %}
                        <input
                        type="hidden"
                        name="properties[{{product.name}}]"
                        id="{{product.name}}"
                        value="No" />
                    {% elsif product.name == "bobblehead_giftBox"  %}
                        <input
                        type="hidden"
                        name="properties[{{product.name}}]"
                        id="{{product.name}}"
                        value="No" />
                    {% elsif product.name == "weighted_insert"  %}
                        <input
                        type="hidden"
                        name="properties[{{product.name}}]"
                        id="{{product.name}}"
                        value="No" />  
                    {% endif %}
                  </div>
                {% endif %}
              {% elsif product.display %}
                <div class="zipper-pouch-card final-product-card">
                  <div class="zipper-details final-product-card-details">
                    <p class="zipper-pouch-label final-product-card-label">{{ product.detail.heading }}</p>
                    <p class="zipper-pouch-price final-product-card-price">{{ product.detail.price }}</p>
                    <p class="custom-bandana-description final-product-card-description">{{ product.detail.description }}</p>
                    {% if product.name == "custom_bandana" %}
                      <div class="bandanaInput">
                         <label class="labelAlert_hide" for="BandannaInput">Select Bandana</label>
                        <input
                          id="BandannaInput"
                          aria-label="bandannaInput"
                          class="bandannaInput"
                          type="type"
                          value=""
                          oninput="this.setAttribute('value', this.value);"
                          placeholder="Enter Bandana Pet Name"
                          oninput="validateInput(this)" />
                      </div>
                    {% endif %}
                    {% if product.detail.variant.size > 0 %}
                      <div class="form-field">
                        <label class="labelAlert_hide" for="bandana_DropSelect">Select Bandana Color</label>
                        <select class="dropdown-bandanna {{product.name}}_select" id="bandana_DropSelect" name="Bandanna_Color {{product.name}}_Color">
                          <option selected disabled>Select {{ product.detail.heading }} Variant</option>
                          {% for variant in product.detail.variant %}
                            {% if variant.display %}
                              <option value="{{ variant.value }}">{{ variant.name }}</option>
                            {% endif %}
                          {% endfor %}
                        </select>
                      </div>
                    {% else %}
                      <input
                        type="hidden"
                        value={{product.detail.default_variant_id}}
                        class="input_{{product.name}}">
                      <div class="dropdown-bandanna" style="background:transparent;"></div>
                    {% endif %}

                    <div class="zipper-pouch-checkbox-outer">
                      <div class="zipper-pouch-checkbox-container">
                        <input
                          id="zipper-pouch-checkbox"
                          type="checkbox"
                          aria-label="{{ product.detail.heading }}. {{ product.detail.description }}. for {{ product.detail.price }}"
                          class="add_custom_bandanna {{product.name}}_checkbox" />
                        <p class="zipper-pouch-checkbox-label">Add {{ product.detail.heading }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="zipper-pouch-image">
                    <img
                      src={{ product.detail.image }}
                      alt=""
                      class="zipper-image">
                  </div>
                    {% if product.name == "zipper_pouch" %}
                        <input
                        type="hidden"
                        name="properties[{{product.name}}]"
                        id="{{product.name}}"
                        value="No" />
                    {% elsif  product.name == "heartbeat_box"  %}
                        <input
                        type="hidden"
                        name="properties[{{product.name}}]"
                        id="{{product.name}}"
                        value="No" />
                    {% elsif product.name == "bobblehead_giftBox"  %}
                        <input
                        type="hidden"
                        name="properties[{{product.name}}]"
                        id="{{product.name}}"
                        value="No" />
                    {% elsif product.name == "weighted_insert"  %}
                        <input
                        type="hidden"
                        name="properties[{{product.name}}]"
                        id="{{product.name}}"
                        value="No" /> 
                    {% endif %}
                </div>
              {% endif %}
            {% endfor %}
          {% endif %}


          <div>
            <p class="count-cuddle-label">How Many Cuddle Clones in this Exact Design Would You Like?</p>
          </div>

          <div id="quantity-container">
            <div id="quantity-buttons">
              <button id="quantity-button-increment" onclick="decreaseQuantity(event)">-</button>
              <input
                type="text"
                class="quantity-box"
                id="quantity"
                aria-label="quantity"
                name="quantity"
                value="1"
                readonly>
              <button id="quantity-button-decrement" onclick="increaseQuantity(event)">+</button>
            </div>
          </div>
          <div>
            <button
              type="submit"
              class="add-to-cart-button"
              id="add_to_cart">ADD TO CART</button>
          </div>
        </div>
      </div>
    </form>
    <div id="popup" class="popup">
      <div class="popup-content">
        <!-- Close button -->
        <span class="close" onclick="closePopup()">&times;</span>

        <div class="sizechartTitle" id="sizeChartTitle"></div>

        <!-- Tabs for countries -->
        <div id="sizeChartTabs" class="tablinks-container">
          <!-- Tabs will be dynamically generated here -->
        </div>

        <!-- Tables for size details -->
        <div id="sizeTables">
          <!-- Tables will be dynamically generated here -->
        </div>
      </div>
    </div>
  </div>
</section>
<section class="section
        {{ css_class }}
        is-width-{{ width }} freeProductSection" style="display: none;">
  <div class="pet-product__confirm" id="pet-product__confirm ">
    <div
      class="create-pet1"
      id="free_product_div"
      data-product_id="{{ plushProduct.id }}"
      style="visibility: hidden;">
      <input
        type="hidden"
        class="freeProductVariants"
        value="">
      <input
        type="hidden"
        class="freeProductKeys"
        value="">
      <form
        method="POST"
        action="/cart/add"
        class="create-pet__form1 free-product1 freeProductForm">
        <input
          type="hidden"
          id="freeFormId"
          value=""
          name="id">
        <input
          type="hidden"
          id="freeUniqueId"
          value=""
          name="properties[_unique_key]">
        <input
          type="hidden"
          id="freeFormQuantity"
          value="1"
          name="quantity">
        <h3 id="free_product_title">Free Gift Unlocked!</h3>
        <div class="popUpContainer">
          <button
            class="close-button-free-product-popup"
            type="button"
            onclick="closeFreeProductDiv(this)">✖</button>
          <div class="productImageContainer"><img class="productImage" alt="Product Image"></div>
          <div class="productDetailsContainer">
            <h1 class="productName"></h1>
            <div class="productReviews"></div>
            <div class="productPriceContainer">
              <span class="productOriginalPrice">FREE</span>
              <span class="productComparePrice"></span>
            </div>
            <div class="variantsContainer"></div>
            <div>
              <div class="productUploaderHeading">Upload Image:</div>
              <div class="free-image-uploader" id="free-image-uploader-1">
                <button class="close-button-uploader-free-product">✖</button>
                <div class="selected-image-free-1 selected-image-container-free"></div>
                <input
                  type="hidden"
                  name="properties[_image_url]"
                  id="hiddenInputFree"
                  value="">
                <div class="uploadOuterFree">
                  <span class="dragBoxFree">
                    <span class="dragDropText">Drag + Drop</span>
                    <input
                      onclick="this.value=null;"
                      type="file"
                      class="image_uploader_free"
                      onChange="dragNdropFreeProduct(event, 'free-image-uploader-1',1)"
                      ondragover="drag()"
                      ondrop="drop()"
                      id="free-uploadFile-1" />
                    <span class="orText">
                      <strong>or</strong>
                    </span>
                    <label for="free-uploadFile-1" class="btn btn-primary">
                      Select File
                    </label>
                  </span>
                </div>
              </div>
            </div>
            <div>
              <button
                type="submit"
                class="add-to-cart-button-free-product"
                id="add_to_cart_free_product">ADD TO CART</button>
            </div>
          </div>
        </div>
      {% comment %} <button type="submit" class="gift-button1"><a id="free_product_href" href="" style="text-decoration: none; color: white;" >Customize FREE Gift Now ⟶</a></button> {% endcomment %}
      </form>
    </div>
  </div>

  <div class="product-{{ plushProduct.id }}">
    {%
      render 'product'
      , product: plushProduct
      , sold_out_options: settings.sold_out_options
      , selected_variant: selected_variant
      , width: width
      , css_class: css_class
      , display_thumbnails: section.settings.display_thumbnails
      , enable_product_lightbox: section.settings.enable_product_lightbox
      , enable_shopify_product_badges: section.settings.enable_shopify_product_badges
      , enable_thumbnail_slider: section.settings.enable_thumbnail_slider
      , enable_zoom: section.settings.enable_zoom
      , gallery_arrows: section.settings.gallery_arrows
      , product_height: section.settings.product_height
      , product_images_position: section.settings.product_images_position
      , set_product_height: section.settings.set_product_height
      , slideshow_transition: section.settings.slideshow_transition
      , stickers_enabled: settings.stickers_enabled
      , tag_style: settings.tag_style
      , thumbnail_position: section.settings.thumbnail_position
      , video_looping: section.settings.video_looping
      ,
    %}
  </div>
</section>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>
{% comment %} {% if plushProduct.tags contains "offer-free-gift" %} {% endcomment %}
<script>
    document.getElementById('originalUniqueKey').value = Date.now();
</script>
{% comment %} {% endif %} {% endcomment %}

<script>
  const omsDetails = {{oms |  json}};
  const header = {
    'api-token': omsDetails[0].api_token,
    // 'ngrok-skip-browser-warning': 6024,
  }

  const container = document.getElementById("productOptionsContainer");

  function generateSection(title, imagesAndPositions) {
    let sizeData = {{plushProduct.metafields.cuddleclones.options.value | json}};
    const sectionContainer = document.createElement("div");
    sectionContainer.className = "section-container";

    if (imagesAndPositions && imagesAndPositions.length > 0) {
      const heading = document.createElement("div");
      heading.classList.add("section-heading");
      if(title==="Size"){
        heading.textContent = title;
        if (isSizeChartAvailable(sizeData)) {
          var popUpButton=document.createElement("p");
          popUpButton.textContent="View Size Chart";
          popUpButton.setAttribute("tabindex", "0"); 
          popUpButton.classList.add("viewSizeChart");
          popUpButton.addEventListener("keydown", handleSizeChartKeyDown);
          popUpButton.addEventListener("click",function(){
            openPopup();
          });
          heading.appendChild(popUpButton);
        }
      }else{
        heading.textContent = title+' Position';
      }
      sectionContainer.appendChild(heading);
    }

    const section = document.createElement("div");
    section.classList.add('productPositions', title.toLowerCase());

    sectionContainer.appendChild(section);
    container.appendChild(sectionContainer);
    
    if (imagesAndPositions && imagesAndPositions.length > 0) {
      generatePetImages(imagesAndPositions, section,title);
    }
  }

  function generatePetImages(imagesAndPositions, section,title) {
    imagesAndPositions.forEach(item => {
      const positionTitle=title.toLowerCase();
      const div = document.createElement("div");
      const label=document.createElement('label');

      const radioInput=document.createElement('input');
      radioInput.setAttribute('type', 'radio');
      radioInput.setAttribute('name', 'properties['+positionTitle+']');
      radioInput.setAttribute('value', item.value);
      radioInput.setAttribute("id",positionTitle+"-position");
      radioInput.tabIndex = -1;

      if(title==="Size"){
        const sizeButton=document.createElement("button");
        sizeButton.classList.add("size-chart-button");
        sizeButton.setAttribute("id","size-chart-button")
        sizeButton.textContent=item.name;

        const newDiv = document.createElement("div");
        newDiv.classList.add(title.toLowerCase()+'-label');
        newDiv.appendChild(sizeButton)
        label.appendChild(radioInput);
        label.appendChild(newDiv);

        sizeButton.addEventListener("click",  (event)=> {
          event.preventDefault();
          // Remove border from all images
          document.querySelectorAll('.size-label button').forEach(button => {
              button.classList.remove('selected');
          });
          var closestInput = document.getElementById('size-chart-button').closest('label').querySelector('input[type="radio"]');
          closestInput.checked = true;
          sizeButton.classList.add('selected');

          // Set the position value to the input field
          const positionInput = document.getElementById(positionTitle + "-position");
          if (positionInput) {
              positionInput.value = item.value;
          } else {
              console.error("Input field for", title, "not found.");
          }
        });

        div.classList.add("size-chart-buttons");
        div.appendChild(label);
      }else{
        const img = document.createElement("img");
        img.src = item.src;
        img.alt = item.alt;
        img.setAttribute("aria-label", `${positionTitle}position ${item.alt}`);
        
        img.setAttribute("tabindex", "0"); 
        img.addEventListener("keydown", handlePetPositionKeydown);

        const newDiv = document.createElement("div");
        newDiv.classList.add(title.toLowerCase()+'-label','final-product-positions');
        newDiv.appendChild(img)
        label.appendChild(radioInput);
        label.appendChild(newDiv);

        const p = document.createElement("p");
        p.classList.add("position-label");
        p.textContent = item.name;

        // Add click event listener to the image
        img.addEventListener("click", function () {
          // Remove border from all images
          document.querySelectorAll('.body-position-image img').forEach(image => {
              image.classList.remove('selected');
          });

          // Add border to the clicked image
          img.classList.add('selected');

          // Set the position value to the input field
          const positionInput = document.getElementById(positionTitle + "-position");
          if (positionInput) {
              positionInput.value = item.value;
          } else {
              console.error("Input field for", title, "not found.");
          }
        });

        div.classList.add("body-position-image");
        div.appendChild(label);
        div.appendChild(p);
      }
      section.appendChild(div);
    });
  }

  let selectedPetTypeName='';
  var selectedBreedValue;
  let productOptionDisplay={{customizer[4].product_options[2].display | json}};

  function selectedPetType(event){
    const step3 = document.querySelector('.step3');
    const step3Section2 = document.querySelector('.step3section2');
    const step4 = document.querySelector('.step4');
    const step5 = document.querySelector('.step5');
    const step6 = document.querySelector('.step6');
    const plushFrameSection = document.querySelector('.plush-frame-options');
    step3.classList.add('disabled');
    step3Section2.classList.add('disabled');
    step4.classList.add('disabled');
    step5.classList.add('disabled');
    step6.classList.add('disabled');
    if(plushFrameSection){
      plushFrameSection.classList.add('disabled');
    }
    document.querySelectorAll('.pet-image-container').forEach(petImage => petImage.classList.remove('active'));
    document.querySelectorAll('.pet-image').forEach(petImage => petImage.classList.remove('active'));
    var element = event.target;
    var container = element.closest('.pet-image-container');
    if (container) {
      container.classList.add('active');

      var image = container.querySelector('.pet-image');
      if (image) {
        image.classList.add('active');
      }
    }
    selectedPetTypeName = element.closest('.pet-image-container').getAttribute('data-value') || element.closest('.pet-image').getAttribute('data-value');
    fetch(`${omsDetails[0].url}breeds?specie=${selectedPetTypeName}`, {
      method: 'GET',
      headers: header,
    })
    .then(response => {
        if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
      var select = document.getElementById('breedSelect');
      select.innerHTML = '';
      var defaultOption = document.createElement('option');
      defaultOption.value = '';
      defaultOption.text = 'Pug, Beagle, Great Dane, etc';
      defaultOption.disabled = true;
      defaultOption.selected = true; 
      select.appendChild(defaultOption);
      data.breeds.forEach(function (breed) {
        var option = document.createElement('option');
        option.value = breed?.description;
        option.text = breed?.description;
        select.appendChild(option);
      });

      select.addEventListener('change', function(event) {
        const divStep2=document.querySelector('.step-2');
        selectedBreedValue = event.target.value;
      
        if(selectedBreedValue==='Other/Mixed'){
          document.querySelector('.otherMixedDiv').style.display='block';
        }else{
          document.querySelector('.otherMixedDiv').style.display='none';
        }
      });
    })
    .catch(error => {
        console.error('Fetch error:', error);
    });

    document.getElementById('pet-type').value = selectedPetTypeName;

    if(productOptionDisplay){
      updatePetImages(selectedPetTypeName);
    }
    if(selectedPetTypeName!=="other"){
      handleEarImages(selectedPetTypeName);
    }else{
      document.getElementById('earImagesContainer').style.display='none';
    }
  }

  function scrollToElement(event) {
    event.preventDefault();
    var element = document.getElementById('get-to-know');
    
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  async function dragNdrop(event) {    
    var preview = document.getElementById("preview");
    let totalImages=preview.children.length;
    const uploadedImages = preview.querySelectorAll("img");
    
    var files = event.target.files;
    let imageName;

    if (((preview.children.length-1) + files.length) >= maxImages) {
      alert("Maximum allowed images is " + maxImages);
      return;
    }

    for (var i = 0; i < files.length; i++) {
      var previewImg = document.createElement("img");
      
      if (!files[i].type.startsWith('image/')) {
        alert("Please select only image files.");
        return;
      }

      if (files[i].type !== "image/jpg" && files[i].type !== "image/png" && files[i].type !== "image/bmp" && files[i].type !== "image/jpeg") {
        alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
        return;
      }

      if (files[i].size > 15 * 1024 * 1024) {
        alert("Please select images smaller than 15MB.");
        return;
      }

      const formData = new FormData();
      formData.append('image', files[i]); 

      previewImg.setAttribute("src", "https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif");
      previewImg.setAttribute("tabindex", "0");
      previewImg.addEventListener("keydown", handleQuestionImages);
      previewImg.alt = `Questionimage_${totalImages+1}`;
      preview.appendChild(previewImg);



      try {
        const response = await fetch(`${omsDetails[0].url}image_uploader/print_image`, {
          method: 'POST',
          body: formData,
          headers: {
            'api-token':omsDetails[0].api_token
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        previewImg.src= data.imageUrl;
        const parts = data.imageUrl.split('/');
        imageName = parts[parts?.length - 1];

        const previewElement = document.getElementById("preview");
        const step3 = document.querySelector('.step3');
        const nextButtonStep3 = step3.querySelector('.startSelectionButtonDiv');

        handleImages();        

        const imageTags = previewElement.querySelectorAll("img");
        const setHiddenInputs=document.querySelectorAll(".uploadedHiddenImages");
        
        if (uploadedImages.length+files.length===setHiddenInputs.length) {
          if(imageTags.length>=minimumImages){
            nextButtonStep3.style.display="flex";
          }
        }

      } catch (error) {
        console.log('Error:', error);
      }

      
      var deleteButton = document.createElement("div");
      deleteButton.innerHTML = '<span class="material-icons-outlined">close</span>';
      deleteButton.classList.add("delete-button");
      deleteButton.addEventListener("click", function() {
        // fetch(`${omsDetails[0].url}image_uploader/delete_image?image_name=${imageName}`, {
        //     headers: headerForImage,
        //     method:'DELETE'
        //   })
        //   .then(response => {
        //     if (!response.ok) {
        //         throw new Error(`HTTP error! Status: ${response.status}`);
        //     }
        //     return response.json();
        //   })
        //   .then(data => {
        //     handleImages();
        //   })
        //   .catch(error => {
        //     console.error("Error deleting image:", error);
        //   });
        var index = Array.from(this.parentElement.parentElement.children).indexOf(this.parentElement);
        deleteImage(index);
        if((preview.children.length-1)<3){
          document.querySelector(".startSelectionButtonDiv").style.display="none";
        }
      });

      var imageContainer = document.createElement("div");
      imageContainer.classList.add("image-container");

      imageContainer.appendChild(deleteButton);
      imageContainer.appendChild(previewImg);

      preview.appendChild(imageContainer);
    }

    if ((preview.children.length-1) >= maxImages) {
      uploadDiv.style.display = "none";
      capacityDiv.style.display = "flex";
    }
  }

  function handleImages(){    
    const previewElement = document.getElementById("preview");
    // Check if the element exists
    if (previewElement) {
      const imageTags = previewElement.querySelectorAll("img");
      // Convert NodeList to array (if needed)
      const imageTagsArray = Array.from(imageTags);
      const imageLinksElement = document.getElementById("image-links");
      // imageLinksElement.style.display="none";
        if (imageLinksElement) {
          imageLinksElement.remove();
        }
      const divElement = document.createElement("div");

      // Set the id attribute to "image-lnks"
      divElement.setAttribute("id", "image-links");

      imageTagsArray.forEach((element, index) => {
        const hiddenInput=document.createElement('input');
        hiddenInput.setAttribute("type", "hidden");
        hiddenInput.setAttribute("class", "uploadedHiddenImages");
        hiddenInput.setAttribute("name", "properties[image_"+ (index+1) +"]");
        hiddenInput.setAttribute("value", element.src);
        divElement.appendChild(hiddenInput);
      });
      const imagePreview = document.getElementById('preview');
      imagePreview.appendChild(divElement);
    }
  }

  function handleQuestionsImages(){    
    const previewElement = document.getElementById("preview");
    // Check if the element exists
    if (previewElement) {
      const imageTags = previewElement.querySelectorAll("img");
      // Convert NodeList to array (if needed)
      const imageTagsArray = Array.from(imageTags);
      const imageLinksElement = document.getElementById("image-links");
      // imageLinksElement.style.display="none";
        if (imageLinksElement) {
          imageLinksElement.remove();
        }
      const divElement = document.createElement("div");

      // Set the id attribute to "image-lnks"
      divElement.setAttribute("id", "image-links");

      imageTagsArray.forEach((element, index) => {
        if(element.id.startsWith("image_")){
          const hiddenInput=document.createElement('input');
          hiddenInput.setAttribute("type", "hidden");
          hiddenInput.setAttribute("class", "uploadedHiddenImages");
          hiddenInput.setAttribute("name", "properties[image_"+ (index+1) +"]");
          hiddenInput.setAttribute("value", element.src);
          divElement.appendChild(hiddenInput);
        }else{
          const hiddenInput=document.createElement('input');
          hiddenInput.setAttribute("type", "hidden");
          hiddenInput.setAttribute("class", "uploadedHiddenImages");
          hiddenInput.setAttribute("name", "properties["+ element.id +"]");
          hiddenInput.setAttribute("value", element.src);
          divElement.appendChild(hiddenInput);
        }
      });
      const imagePreview = document.getElementById('preview');
      imagePreview.appendChild(divElement);
    }
  }

  async function dragNdropForQuestions(event) {    
    var preview = document.getElementById("preview");
    let totalImages=preview.children.length;
    var files = event.target.files;
    let imageName;

    if (((preview.children.length-1) + files.length) > maxImages) {
      alert("Maximum allowed images is " + maxImages);
      return;
    }

    for (var i = 0; i < files.length; i++) {
      var previewElements = document.getElementById("image-links");
      let totalImages=previewElements.children.length;
      var previewImg = document.createElement("img");
      previewImg.id=`image_${totalImages+1}`;
      previewImg.alt = `Questionimage_${totalImages+1}`;

      var labelContainer=document.createElement("label");
      labelContainer.setAttribute("for",`imageRadio${totalImages+1}`);
      labelContainer.classList.add('radio-label');
      

      var radioInput=document.createElement("input");
      radioInput.type="radio";
      radioInput.name="imagesCheckbox";
      radioInput.id=`imageRadio${totalImages+1}`;
      radioInput.classList.add("imagesCheckbox");

      labelContainer.onclick=()=>selectImage(radioInput.id,previewImg);
      
      if (!files[i].type.startsWith('image/')) {
        alert("Please select only image files.");
        return;
      }

      if (files[i].type !== "image/jpg" && files[i].type !== "image/png" && files[i].type !== "image/bmp" && files[i].type !== "image/jpeg") {
        alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
        return;
      }

      if (files[i].size > 15 * 1024 * 1024) { // Convert MB to bytes
        alert("Please select images smaller than 15MB.");
        return;
      }

      const formData = new FormData();
      formData.append('image', files[i]); 

      previewImg.setAttribute("src", "https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif");
      labelContainer.appendChild(radioInput);
      labelContainer.appendChild(previewImg);
      var deleteButton = document.createElement("div");
      deleteButton.innerHTML = '<span class="material-icons-outlined">close</span>';
      deleteButton.classList.add("delete-button-with-radio");
      deleteButton.onclick = () => deleteImageRadio(labelContainer);
      labelContainer.appendChild(deleteButton)
      preview.appendChild(labelContainer);
      
      
      try {
        const response = await fetch(`${omsDetails[0].url}image_uploader/print_image`, {
          method: 'POST',
          body: formData,
          headers: {
              'api-token':omsDetails[0].api_token
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        previewImg.src= data.imageUrl;

        handleQuestionsImages();

        // const newHiddenImageInput=document.createElement("input");
        // newHiddenImageInput.setAttribute("type", "hidden");
        // newHiddenImageInput.setAttribute("name", "properties[image_"+ (totalImages+1) +"]");
        // newHiddenImageInput.setAttribute("value", data.imageUrl);

        // const hiddenImageInputsDiv=document.querySelector("#image-links");
        // hiddenImageInputsDiv.appendChild(newHiddenImageInput);

      } catch (error) {
        console.log('Error:', error);
      }

      var imageContainer = document.createElement("div");
      imageContainer.classList.add("image-container");

      imageContainer.appendChild(labelContainer);

      preview.appendChild(imageContainer);
      addRadioInputClassesToNewImages();
    }

    if ((preview.children.length-1) >= maxImages) {
      uploadDiv.style.display = "none";
      capacityDiv.style.display = "flex";
    }
  }

  function addRadioInputClassesToNewImages(){
    const imageContainers = document.querySelectorAll(".image-container");
    imageContainers.forEach((container,index) => {
      const img = container.querySelector('img');
      if (img) {
        const radioId = img.id;
        img.onclick = () => selectImage(radioId, img);
      }else {
        if (img) {
          img.style.display = 'none';
          const closestRadio = container.querySelector('input[type="radio"]');
          if (closestRadio) {
            closestRadio.checked = false;
            closestRadio.name=closestRadio.name+"checked";
          }
        }
      }
    });
  }

  function deleteImage(index) {
    preview.removeChild(preview.children[index]);

    if ((preview.children.length-1) < maxImages) {
      uploadDiv.style.display = "block";
      capacityDiv.style.display = "none";
    }
  }


  function drag() {
    document.getElementById("uploadFile").parentNode.parentNode.className = "dragBox"; 
  }
    
  function drop() {
    document.getElementById("uploadFile").parentNode.parentNode.className = "dragBox";
  }

  function showHideTwoPallets(checkbox) {
    var newElement = document.querySelectorAll(".color-container .second-half");
    const secondHalf=document.getElementsByClassName("second-half");
    if(checkbox.checked){
      secondHalf.style.display="block";
    }
    // Update colorItems after cloning
    const colorItems = document.querySelectorAll('.color');
    colorItems.forEach(item => {
        item.onclick = selectColor; // Reassign onclick event
    });
  }

  // Update colorItems after cloning
  const colorItems = document.querySelectorAll('.color');
  colorItems.forEach(item => {
      item.onclick = selectColor; // Reassign onclick event
  });

  function selectColor(event) {
    const selectedColor = event.currentTarget;

    // Check if the color is in the first half or the second half
    const firstHalf = document.getElementById("color_container").children[0];
    const secondHalf = document.getElementById("color_container").children[1];

    const isFirstHalf = firstHalf.contains(selectedColor);
    const isSecondHalf = secondHalf && secondHalf.contains(selectedColor);

    // If the color is already selected, remove the selection
    if (selectedColor.classList.contains('selected')) {
      selectedColor.classList.remove('selected');
      const tickIcon = selectedColor.querySelector('.material-icons');
      if (tickIcon) {
        selectedColor.removeChild(tickIcon);
      }
      return;
    }

    // If the color is from the first half, deselect all colors in the first half
    if (isFirstHalf) {
      firstHalf.querySelectorAll('.color.selected').forEach(item => {
        item.classList.remove('selected');
        const tickIcon = item.querySelector('.material-icons');
        if (tickIcon) {
            item.removeChild(tickIcon);
        }
      });
    }

    // If the color is from the second half, deselect all colors in the second half
    if (isSecondHalf) {
      secondHalf.querySelectorAll('.color.selected').forEach(item => {
        item.classList.remove('selected');
        const tickIcon = item.querySelector('.material-icons');
        if (tickIcon) {
          item.removeChild(tickIcon);
        }
      });
    }

    // Add selection to the clicked color
    selectedColor.classList.add('selected');
    const tickIcon = document.createElement('i');
    tickIcon.classList.add('material-icons');
    tickIcon.textContent = 'done';
    selectedColor.appendChild(tickIcon);
  }

  function addUploader(event) {
    event.preventDefault();
    var elements = document.querySelectorAll(
      "#unique_characteristic input.image_uploader"
    );
    var newElement = parseInt(elements.length)+1;
    if (newElement <= 10) {
      createUploader(newElement);
    }else{
      const addButton = document.getElementById("addUniqueButton");
      addButton.textContent = "Max Number of Unique Characteristics (10) Added";
    }
  }

  function dragNdropUnique(event, containerId, number) {
    var preview = document.getElementById(containerId);
    var files = event.target.files;

    const uploadOuterElement = event.target.closest('.uploadOuter');
    uploadOuterElement.style.display="none";

    var previewImg = document.createElement("img");
    const hiddenUniqueImage = document.querySelector(".selected-image-" + number + " img");
    const hiddenUniqueInput=document.querySelector(".uniqueUploadedImage-"+number);

    const deleteButtonDiv = document.createElement("div");
    deleteButtonDiv.classList.add("delete-button-unique");
    deleteButtonDiv.textContent="X";
    deleteButtonDiv.addEventListener("click", function(event) {
      deleteUniqueImage(event);
      uploadOuterElement.style.display="block";
      hiddenUniqueInput.value="";
    });
        
    let imageName;

    if (files.length > 1) {
      alert("Please select only one image file.");
      return;
    }

    var file = files[0];
    var fileName = URL.createObjectURL(files[0]);      

    if (!file.type.startsWith('image/')) {
      alert("Please select only image files.");
      return;
    }

    if (file.type !== "image/jpg" && file.type !== "image/png" && file.type !== "image/bmp" && file.type !== "image/jpeg" && file.type !== "image/jpeg") {
      alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
      return;
    }

    if (files[0].size > 15 * 1024 * 1024) { // Convert MB to bytes
      alert("Please select images smaller than 15MB.");
      return;
    }

    previewImg.setAttribute("src", "https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif");
    previewImg.setAttribute('alt', "characteristic-image-" + number);
    previewImg.setAttribute("tabindex", "0");
    previewImg.addEventListener("keydown", handleCharacteristicsImage);
    

    const formData = new FormData();
    formData.append('image', files[0]);

    // Upload the new image
    fetch(`${omsDetails[0].url}image_uploader/print_image`, {
      method: 'POST',
      body: formData,
      headers: {
          'api-token':omsDetails[0].api_token
      }
    })
    .then(response => {
      if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      previewImg.setAttribute("src", data.imageUrl);
      hiddenUniqueInput.value=data.imageUrl;         
    })
    .catch(error => {
      console.error('Error uploading new image:', error);
    });

    // Find existing image container if it exists
    var existingImageContainer = preview.querySelector(".selected-image-" + number);

    if (existingImageContainer) {
      // If there's already an image, replace it with the new one
      var existingImage = existingImageContainer.querySelector("img");
      
      existingImageContainer.appendChild(deleteButtonDiv); // Add new image
      existingImageContainer.appendChild(previewImg); // Add new image
    } else {
      // If there's no existing image, create a new image container
      existingImageContainer = document.createElement("div");
      existingImageContainer.classList.add("selected-image-" + number);
      existingImageContainer.appendChild(previewImg); // Add new image
    }
  }

  function createUploader(newElement) {
    // Create main container
    const flexBox = document.createElement("div");
    flexBox.classList.add("flex-box");

    // Create number div{{ customizer[5].zipper_pouch.heading }}
    const numberDiv = document.createElement("div");
    numberDiv.classList.add("number");
    numberDiv.innerHTML = "<span>" + newElement + "</span>";

    // Create unique-characteristic-uploader div
    const uniqueCharacteristicUploaderDiv = document.createElement("div");
    uniqueCharacteristicUploaderDiv.classList.add(
      "unique-characteristic-uploader"
    );

    // Create image-uploader div
    const imageUploaderDiv = document.createElement("div");
    imageUploaderDiv.classList.add("image-uploader");
    imageUploaderDiv.setAttribute("id", "image-uploader-" + newElement);

    //create hidden input
    const inputHiddenUnique=document.createElement('input');
    inputHiddenUnique.classList.add("uniqueUploadedImage-"+newElement);
    inputHiddenUnique.setAttribute('type', 'hidden');
    inputHiddenUnique.setAttribute('name', `properties[characteristic_${newElement}]`); // Use 'number' parameter to set unique name
    inputHiddenUnique.setAttribute('value', ""); // Set the image name as the value

    // Create textarea
    const textarea = document.createElement("textarea");
    textarea.name = "properties[_pet_characteristic_" + newElement+"]";
    textarea.rows = "5";
    textarea.cols = "20";
    textarea.placeholder =
      "Describe your pets unique characteristic here...";

    // Create div for displaying selected image
    const selectedImageDiv = document.createElement("div");
    selectedImageDiv.classList.add("selected-image-"+newElement,"selected-image-container");
    
    // Create uploadOuter div
    const uploadOuterDiv = document.createElement("div");
    uploadOuterDiv.classList.add("uploadOuter");

    // Create dragBox span
    const dragBoxSpan = document.createElement("span");
    dragBoxSpan.classList.add("dragBoxUnique");

    // Create "Drag + Drop" text
    const dragDropText = document.createElement("span");
    dragDropText.textContent = "Drag + Drop";

    // Create "OR" text
    const orText = document.createElement("span");
    orText.textContent = "OR";

    dragBoxSpan.innerHTML =
    '<span class="dragDropText">Drag + Drop</span>'+
    '<input style="cursor:pointer" type="file" class="image_uploader" onclick="this.value=null;" onChange="dragNdropUnique(event, \'image-uploader-' + newElement + '\', ' + newElement + ')" name="image ' +
    newElement +
    '" ondragover="drag()" ondrop="drop()" id="uploadFile-' + newElement + '" />' +
    '<span class="orText"><strong>OR</strong></span>'+
    '<label for="uploadFile-' + newElement + '" class="btn btn-primary">' +
    '<img style="height: 19px; width: 25px" ' +
    'src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/7500a60f83aabde22eb73dc843c72079.png?v=1706695966" ' +
    'alt="Cameralogo"/> Select File</label>';
    
    // Append elements to the document
    imageUploaderDiv.appendChild(textarea);
    imageUploaderDiv.appendChild(inputHiddenUnique);
    imageUploaderDiv.appendChild(selectedImageDiv);
    imageUploaderDiv.appendChild(uploadOuterDiv);
    uploadOuterDiv.appendChild(dragBoxSpan);
    uniqueCharacteristicUploaderDiv.appendChild(imageUploaderDiv);
    flexBox.appendChild(numberDiv);
    flexBox.appendChild(uniqueCharacteristicUploaderDiv);

    // Append the main container to the body
    document.getElementById("unique_characteristic").appendChild(flexBox);
  }

  var maxImages = 10;
  var preview = document.getElementById("preview");
  var uploadDiv = document.querySelector(".uploadOuter");
  var capacityDiv = document.querySelector(".image-capacity-full-container");
  
  var sizeData = {{plushProduct.metafields.cuddleclones.options.value | json}};

  capacityDiv.style.display = "none";

  function updatePetImages(selectedPetTypeName) {
    container.innerHTML = "";

    var earImages=document.getElementById("earImagesContainer");
    if(selectedPetTypeName==="dog"){
      earImages.style.display="block";
    }
    else{
      earImages.style.display="none";
    }

    var productOptions = {{customizer[4].product_options | json}};

    // Iterate over each product option
    productOptions.forEach(function(option) {
      // Check if the selected pet type exists in the option
      if (selectedPetTypeName in option) {
        var petData = option[selectedPetTypeName];
        if (checkDisplayTrue(petData)) {
          if (Object.keys(petData).length > 0) {
            const productOptionsLabel = document.createElement("p");
            productOptionsLabel.className = "product-option-title";
            productOptionsLabel.textContent = {{customizer[4].product_options[0].main_heading | json}};
            container.appendChild(productOptionsLabel);

            Object.keys(petData).forEach(function(position) {
              if (petData[`${position}`].display){
                generateSection(position, petData[`${position}`].images_and_positions);
              }
            });

            const finalStepButton = document.createElement("button");
            finalStepButton.id ="final-step-button";
            finalStepButton.classList.add("nextStep-button");
            finalStepButton.setAttribute('type','button');
            if(rushSection){
              finalStepButton.innerHTML = 'Next Step <span class="material-icons-outlined">arrow_forward</span>'; 
            }else{
              finalStepButton.innerHTML = 'Final Step <span class="material-icons-outlined">arrow_forward</span>';
            }
            var selectedPetOptions = petData[selectedPetTypeName];

            finalStepButton.addEventListener('click',(event)=>{
              event.preventDefault();
              if (selectedPetTypeName) {
                // Get the product options array from the JSON object
                var productOptions = {{customizer[4].product_options |  json}}

                // Iterate over each product option
                productOptions.forEach(function(option) {
                  // Check if the selected pet type exists in the option
                  if (selectedPetTypeName in option) {
                    var petData = option[selectedPetTypeName];
                    var error = false
                    Object.keys(petData).forEach(function(position) {
                      if (!error) {
                        if (petData[`${position}`].display) {
                          const positionSelector = '.' + position.toLowerCase();
                          const positionDiv = document.querySelector(positionSelector);
                          if (petData[position].images_and_positions.length) {
                            const bodyInputs = positionDiv.querySelectorAll('input');
                            bodyPositionSelected = Array.from(bodyInputs).some(input => input.checked);
                            if (!bodyPositionSelected) {
                              error = true;
                              event.preventDefault();
                              if(position==="Size"){
                                alert(`Please select ${position} for Slipper.`);
                              }else{
                                alert(`Please select positions for ${position}.`);
                              }
                              positionDiv.scrollIntoView({ behavior: 'smooth' });
                              positionDiv.focus();
                              return;
                            }
                          }
                        }
                      }
                    });
                    if (!error) {
                      if(plushFrameSection){
                        plushFrameSection.classList.remove('disabled');
                        plushFrameSection.scrollIntoView({ behavior: 'smooth' });
                      }else if(rushSection){
                        rushSection.classList.remove('disabled');
                        rushSection.scrollIntoView({ behavior: 'smooth' });
                      }else{
                        step6.classList.remove('disabled');
                        step6.scrollIntoView({ behavior: 'smooth' });
                      }
                    }
                  }
                });
              }
            });
            container.appendChild(finalStepButton);
            const divider = '<hr class="horizontal-line" />';
            container.insertAdjacentHTML('beforeend', divider);
          }
        }
      }
    });
  }

  function decreaseQuantity(event) {
    event.preventDefault();
    var quantityInput = document.getElementById('quantity');
    var quantity = parseInt(quantityInput.value);
    if (quantity > 1) {
      quantity--;
      quantityInput.value = quantity;
    }
  }

  function increaseQuantity(event) {
    event.preventDefault();
    var quantityInput = document.getElementById('quantity');
    var quantity = parseInt(quantityInput.value);
    quantity++;
    quantityInput.value = quantity;
  }

  function selectRightEar(element) {
    // Remove 'selected' class from all ear images
    document.querySelectorAll('.ear-image').forEach(function(ear) {
      ear.classList.remove('selected');
    });

    // Add 'selected' class to the clicked ear image
    element.classList.add('selected');
  }

  function selectLeftEar(element) {
    // Remove 'selected' class from all ear images
    document.querySelectorAll('.ear-image').forEach(function(ear) {
      ear.classList.remove('selected');
    });

    // Add 'selected' class to the clicked ear image
    element.classList.add('selected');
  }

  const radioButtons = document.querySelectorAll('input[type="radio"]');
    
  // Add click event listener to each radio button
  radioButtons.forEach(button => {
    button.addEventListener('click', function() {
      // Get the value of the selected radio button
      const selectedValue = this.value;
      
      // Set the value of the input field with the selected ear value
      const inputField = document.getElementById(this.name);
      if (inputField) {
          inputField.value = selectedValue;
      }
    });
  });

  const bothHalf=document.getElementById("bothEyesText");
  const leftHalf=document.getElementById("leftEyeText");
  const rightHalf=document.getElementById("rightEyeText");
  leftHalf.style.display="none";

  function showHideSecondPalette(checkbox) {
    var secondPalette = document.getElementById("second-palette");
    if (checkbox.checked) {
      bothHalf.style.display='none';
      leftHalf.style.display="flex";
      secondPalette.style.display = "block";
      document.getElementById("right-eye-color").value = '';
    } else {
      leftHalf.style.display="none";
      bothHalf.style.display='flex';
      secondPalette.style.display = "none";
      // Clear the right eye color input and reset its value
      var left_eye_color = document.getElementById("left-eye-color").value;
      var right_eye_color = document.getElementById("right-eye-color").value;
      if ((left_eye_color) && (left_eye_color != right_eye_color)) {
        document.getElementById("right-eye-color").value = left_eye_color
      }
    }
  }   

  function setEyeColor(checkbox,eyeSide, color) {
    const eyeCheckbox=document.getElementById("different-eye-color");

    let colorName = color.toLowerCase().replace(/\s/g, "_");
    // Set the color for the specified eye
    document.getElementById(`${eyeSide}-eye-color`).value = colorName;

    if (!eyeCheckbox.checked) {
      document.getElementById("right-eye-color").value = colorName;
      document.getElementById("left-eye-color").value = colorName;
    } else {
      // Set the color for the specified eye
      if (eyeSide === 'right') {
          document.getElementById("right-eye-color").value = colorName;
      } else if (eyeSide === 'left') {
          document.getElementById("left-eye-color").value = colorName;
      }
    }
  }

  const step1 = document.querySelector('.step1');
  const step2 = document.querySelector('.step2');
  const step3 = document.querySelector('.step3');
  const step3Section2 = document.querySelector('.step3section2');
  const step4 = document.querySelector('.step4');
  const step5 = document.querySelector('.step5');
  const step6 = document.querySelector('.step6');
  const plushFrameSection = document.querySelector('.plush-frame-options');
  const rushSection = document.querySelector('.rushContainer');

  step2.classList.add('disabled');
  step3.classList.add('disabled');
  step3Section2.classList.add('disabled');
  step4.classList.add('disabled');
  step5.classList.add('disabled');
  step6.classList.add('disabled');
  if(plushFrameSection){
    plushFrameSection.classList.add('disabled');
  }
  if(rushSection){
    rushSection.classList.add('disabled');
  }

  const emailInput = step1.querySelector('.email');
  const nextButtonStep1 = step1.querySelector('.nextStep-button');
  const nextButtonStep2 = step2.querySelector('.nextStep-button');
  const nextButtonStep3 = step3.querySelector('.startSelectionButton');
  const nextButtonStep3Section2 = step3Section2.querySelector('.nextStep-button');
  const nextButtonStep4 = step4.querySelector('.nextStep-button');
  const nextButtonStep5 = step5.querySelector('.nextStep-button');
  let nextButtonRushSection;
  if(rushSection){
    nextButtonRushSection = rushSection.querySelector('.nextStep-button');
  }

  let nextButtonOnPlushFrameSection;
  if(plushFrameSection){
    nextButtonOnPlushFrameSection = plushFrameSection.querySelector('.nextStep-button')
  }

  const errorMsg=document.querySelector('.error-msg');
   
  const petTypeInput = document.getElementById('pet-type');

  // Get the pet name input element
  const petNameInput = step2.querySelector('input[name="properties[_pet_name]"]');
  // Get the breed select element
  const breedSelect = document.getElementById('breedSelect');
  // Get the age select element
  const ageSelect = step2.querySelector('select[name="properties[_pet_age]"]');

  const mixedBreed=document.querySelector('.otherMixedInput');

  nextButtonStep2.addEventListener('click', (event) => {
    const emailValue=document.querySelector(".email").value.trim();
    const phoneNumberValue=document.querySelector("#phone").value.trim();
    const specieValue=document.querySelector("#pet-type").value.trim();
    const petNameValue=document.querySelector(".petName").value.trim();
    const breedValue=document.querySelector("#breedSelect").value;
    const petAgeValue=document.querySelector("#petAge").value;

    const klaviyouData={
      email:emailValue,
      phone_number:phoneNumberValue,
      name:petNameValue,
      species:specieValue,
      breed:breedValue,
      pet_age:petAgeValue
    }

    const klaviyoBody=JSON.stringify(klaviyouData);

    const sendDataToKlaviyo=fetch(`${omsDetails[0].url}klaviyo_api/send_pet_detail_to_klaviyo`,{
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-token':omsDetails[0].api_token
      },
      body: klaviyoBody
    })
    .then(response => response.json())
    .then(data => console.log('Success:'))
    .catch(error => console.log('Error:', error));

    // Check if a pet type is selected
    const petTypeSelected = petTypeInput.value.trim() !== '';
    const petTypeDiv=document.querySelector(".pet-images");

    if (!petTypeSelected) {
      alert("Select valid pet type.");
      event.preventDefault();
      petTypeDiv.scrollIntoView({behavior:'smooth'});
    } else if (petNameInput.value.trim() === '') {
      alert("Please enter pet name.");
      petNameInput.focus();
      event.preventDefault();
    } else if(breedSelect.value === '' && selectedPetTypeName!=='other'){
      alert("Please select any breed specie.");
      breedSelect.focus();
      event.preventDefault();
    }else if(ageSelect.value === '' ){
      alert("Please select age of pet.");
      ageSelect.focus();
      event.preventDefault();
    }else if(selectedBreedValue==='Other/Mixed' && mixedBreed.value.trim()===''){
      alert("Please enter Other/Mixed breed name");
      mixedBreed.scrollIntoView({behavior:'smooth'});
      mixedBreed.focus();
    }else {
      step3.classList.remove('disabled');
      step3.scrollIntoView({ behavior: 'smooth' });
    }
  });

  const uploadContainer = document.getElementById('dragArea');

  function camelize(str) {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, function(word, index) {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    }).replace(/\s+/g, '');
  }

  const thanksMessage=document.querySelector(".thanks_message");
  let questionsData={{customizer[2].pet_detail[0].questions | json}};

  const imageTags = preview.querySelectorAll("img");

  const minimumImages={{customizer[2].pet_detail[0].require_images | json}};

  nextButtonStep3.addEventListener('click', (event) => {
    if(questionsData && questionsData.length){
      event.preventDefault();
      thanksMessage.style.display="block";

      thanksMessage.style.backgroundColor="#5461c8";
      thanksMessage.scrollIntoView({ behavior: 'smooth' });

      const firstQuestionLabel=questionsData[0].label;
      const firstQuestionLabelClass=firstQuestionLabel.replace(/ /g,"");
      const firstQuestionLabelElement=document.querySelector(`.${firstQuestionLabelClass}_message`);
      firstQuestionLabelElement.style.display="block";

      let firstQuestionExampleLabelElement="";
      if(questionsData[0].example_text && questionsData[0].example_text!==""){
        const firstQuestionExampleLabel=questionsData[0].example_text;
        firstQuestionExampleLabelElement=document.querySelector(`.${firstQuestionLabelClass}_example_message`);
        firstQuestionExampleLabelElement.style.display="block";
      }

      const fileInputFunction=document.querySelector(".imageUploader");
      fileInputFunction.onchange=dragNdropForQuestions;

      const previewElement = document.getElementById("preview");
      const imageTags = previewElement.querySelectorAll("img");

      if (imageTags.length<minimumImages) {
        alert("Please upload at least 3 images of your pet.");
        uploadContainer.scrollIntoView({ behavior: 'smooth' });
        event.preventDefault();
      } else {
        const imageContainers = document.querySelectorAll(".image-container");

        imageContainers.forEach((container,index) => {
          const deleteButtonDiv = container.querySelector(".delete-button");

          if (deleteButtonDiv) {
            const checkbox = document.createElement("input");
            checkbox.type = "radio";
            checkbox.setAttribute("name", "imagesCheckbox");
            checkbox.setAttribute("id", `imageRadio${index + 1}`);
            checkbox.setAttribute("class", "imagesCheckbox");
            deleteButtonDiv.replaceWith(checkbox);
          }

          var deleteButton = document.createElement("div");
          deleteButton.innerHTML = '<span class="material-icons-outlined">close</span>';
          deleteButton.classList.add("delete-button-with-radio");
          deleteButton.onclick = () => deleteImageRadio(container);

          // Create label element
          const label = document.createElement('label');
          const radioId = `imageRadio${index + 1}`;
          label.setAttribute('for', radioId);
          label.classList.add('radio-label');
          

          // Select existing input and img elements
          const input = container.querySelector('input[type="radio"]');
          const img = container.querySelector('img');

          // Update the ID of the input
          input.id = radioId;
          img.id = `image_${index + 1}`;

          // Add click event to image
          img.onclick = () => selectImage(radioId, img);
          label.onclick=()=>selectImage(radioId,img);
          // Append input and img to label
          label.appendChild(input);
          label.appendChild(img);

          // Append label to container
          container.appendChild(label);
          container.appendChild(deleteButton);
        });

        nextButtonStep3.style.display="none";

        const stepsButtons=document.querySelectorAll(".stepButton");
        stepsButtons.forEach((button,index)=>{
          const parentDiv = button.parentElement; 
          if(index===0){
            button.style.display="flex";
            parentDiv.style.display = "flex";
          }else{
            button.style.display="none";
            parentDiv.style.display = "none";
          }
        });

        const hiddenImagesLinks=document.querySelector("#image-links");

        stepsButtons.forEach((button, index) => {
          button.addEventListener("click", function(event) {
            event.preventDefault();

            button.style.display = "none";

            if (index === 0) {
              resetImageSelection();
              firstQuestionLabelElement.style.display="none";
              if(firstQuestionExampleLabelElement!==""){
                firstQuestionExampleLabelElement.style.display="none";
              }

              const buttonName = button.getAttribute('name');
              const buttonNameWithoutSpace = buttonName.replace(/ /g,"");

              nextQuestionLabelShow(index,"block");
              nextQuestionExampleLabelShow(index,"block");
              thanksMessage.scrollIntoView({ behavior: 'smooth' });

              const selectedRadio = document.querySelector('input[name="imagesCheckbox"]:checked');
              if (selectedRadio) {
                const selectedQuestionImage = selectedRadio.closest('div').querySelector('img');
                if (selectedQuestionImage) {
                  replaceHiddenInputImageName(selectedQuestionImage.id, buttonName);
                  if (selectedQuestionImage.id.startsWith('image_') ) {
                    selectedQuestionImage.id = buttonName; 
                  } else {
                    selectedQuestionImage.id = selectedQuestionImage.id + '&' + buttonName;
                  }
                }
              } else {
                alert("Please select an image before proceeding.");
                button.style.display = "flex";
                firstQuestionLabelElement.style.display="block";
                nextQuestionLabelShow(index,"none");
                nextQuestionExampleLabelShow(index,"none");
                return;
              }

              const imageContainers = document.querySelectorAll(".image-container");
              
              imageContainers.forEach((container,index) => {
                const img = container.querySelector('img');
                const label=container.querySelector('label');
                
                // Check if the image ID starts with 'image_'
                if (img) {
                  // Update the ID of the input
                  const radioId = img.id;

                  // Add click event to image
                  img.onclick = () => selectImage(radioId, img);
                  label.onclick=()=>selectImage(radioId,img);
                }else {
                  // Hide images that do not start with 'image_'
                  if (img) {
                    img.style.display = 'none';
                  }
                }
              });

              // Show the next button
              if (index + 1 < stepsButtons.length) {
                const nextButton = stepsButtons[index + 1];
                nextButton.style.display = "flex";
                nextButton.parentElement.style.display = "flex";
              }
            }else if (index > 0 && index < (stepsButtons.length - 1)) {
              resetImageSelection();

              const buttonName = button.getAttribute('name');
              const buttonNameWithoutSpace = buttonName.replace(/ /g,"");

              prevQuestionLabelShow(index,"none");
              nextQuestionLabelShow(index,"block");
              prevQuestionExampleLabelShow(index,"none");
              nextQuestionExampleLabelShow(index,"block");
              thanksMessage.scrollIntoView({ behavior: 'smooth' });

              const imageContainers = document.querySelectorAll(".image-container");
              
              imageContainers.forEach((container,index) => {
                const img = container.querySelector('img');
                const label=container.querySelector('label');
                

                if (img) {
                  const radioId = img.id;

                  img.onclick = () => selectImage(radioId, img);
                  label.onclick=()=>selectImage(radioId,img);
                }else {
                  if (img) {
                    img.style.display = 'none';
                    const closestRadio = container.querySelector('input[type="radio"]');
                    if (closestRadio) {
                      closestRadio.checked = false;
                      closestRadio.name=closestRadio.name+"checked";
                    }
                  }
                }
              });

              const selectedRadio = document.querySelector('input[name="imagesCheckbox"]:checked');
              if (selectedRadio) {
                const selectedQuestionImage = selectedRadio.closest('div').querySelector('img');
                if (selectedQuestionImage) {
                  replaceHiddenInputImageName(selectedQuestionImage.id, buttonName);
                  if (selectedQuestionImage.id.startsWith('image_') ) {
                    selectedQuestionImage.id = buttonName; 
                  } else {
                    selectedQuestionImage.id = selectedQuestionImage.id + '&' + buttonName;
                  }
                }
              } else {
                alert("Please select an image before proceeding.");
                button.style.display = "flex";
                prevQuestionLabelShow(index,"block");
                nextQuestionLabelShow(index,"none");
                prevQuestionExampleLabelShow(index,"block");
                nextQuestionExampleLabelShow(index,"none");
                return; 
              }
              
              imageContainers.forEach((container,index) => {
                const img = container.querySelector('img');
                const label=container.querySelector('label');
               
                if (img) {
                  const radioId = img.id;
                  img.onclick = () => selectImage(radioId, img);
                  label.onclick=()=>selectImage(radioId,img);
                }else {
                  if (img) {
                    img.style.display = 'none';
                    const closestRadio = container.querySelector('input[type="radio"]');
                    if (closestRadio) {
                      closestRadio.checked = false;
                      closestRadio.name=closestRadio.name+"checked";
                    }
                  }
                }
              });

              
              if (index + 1 < stepsButtons.length) {
                const nextButton = stepsButtons[index + 1];
                nextButton.style.display = "flex";
                nextButton.parentElement.style.display = "flex";
              }
              // Handle logic for last button
            } else if (index === (stepsButtons.length - 1)) {
              const imageContainers = document.querySelectorAll(".image-container");
              
              imageContainers.forEach((container,index) => {
                const img = container.querySelector('img');

                if (img ) {
                  const radioId = img.id;

                  img.onclick = () => selectImage(radioId, img);
                }else {
                  if (img) {
                    img.style.display = 'none';
                    const closestRadio = container.querySelector('input[type="radio"]');
                    if (closestRadio) {
                      closestRadio.checked = false;
                      closestRadio.name=closestRadio.name+"checked";
                    }
                  }
                }
              });

              // Check if any radio button is selected
              const buttonName = button.getAttribute('name');
              const selectedRadio = document.querySelector('input[name="imagesCheckbox"]:checked');
              if (selectedRadio) {
                // Handle the last button click
                const selectedQuestionImage = selectedRadio.closest('div').querySelector('img');
                if (selectedQuestionImage) {
                  replaceHiddenInputImageName(selectedQuestionImage.id, buttonName);
                  if (selectedQuestionImage.id.startsWith('image_') ) {
                    selectedQuestionImage.id = buttonName; 
                  } else {
                    selectedQuestionImage.id = selectedQuestionImage.id + '&' + buttonName;
                  }
                }
                step3Section2.classList.remove('disabled');
                step3Section2.scrollIntoView({ behavior: 'smooth' });
              } else {
                alert("Please select an image before proceeding.");
                button.style.display = "flex";
                prevQuestionLabelShow(index,"block");
                return;
              }
            }
          });
        });
      }
    }else{
      event.preventDefault();
      const previewElement = document.getElementById("preview");
      const imageTags = previewElement.querySelectorAll("img");
      if(imageTags && imageTags.length>=3){
        step3Section2.classList.remove('disabled');
        step3Section2.scrollIntoView({ behavior: 'smooth' });
      }else{
        disableRemainingSections();
        alert("Please upload at least 3 images of your pet.");
        return;
      }
    }
  });

  function disableRemainingSections(){
    const step3Section2 = document.querySelector('.step3section2');
    const step4 = document.querySelector('.step4');
    const step5 = document.querySelector('.step5');
    const step6 = document.querySelector('.step6');
    step3Section2.classList.add('disabled');
    step4.classList.add('disabled');
    step5.classList.add('disabled');
    step6.classList.add('disabled');
  }

  function replaceHiddenInputImageName(id,name){
    const hiddenInput=document.querySelector(`input[name="properties[${id}]"]`);
    hiddenInputName  = extractStringInsideBrackets(hiddenInput.name);
    if(hiddenInput){
      if (hiddenInputName.startsWith("image_")) { 
        hiddenInput.name=`properties[${name}]`;
      }else {
        hiddenInputName = hiddenInputName + '&' + name
        hiddenInput.name = `properties[${hiddenInputName}]`
      }
    }
  }
  function extractStringInsideBrackets(str) {
    const match = str.match(/\[(.*?)\]/);
    return match ? match[1] : null;
  }
  function nextQuestionLabelShow(index,displayValue){
    const nextQuestionLabelClass=questionsData[index+1].label;
    const nextQuestionLabel=nextQuestionLabelClass.replace(/ /g,"");
    const nextQuestionLabelElement=document.querySelector(`.${nextQuestionLabel}_message`);
    nextQuestionLabelElement.style.display=displayValue;
  }

  function prevQuestionLabelShow(index,displayValue){
    const prevQuestionLabelClass=questionsData[index].label;
    const prevQuestionLabel=prevQuestionLabelClass.replace(/ /g,"");
    const prevQuestionLabelElement=document.querySelector(`.${prevQuestionLabel}_message`);
    prevQuestionLabelElement.style.display=displayValue;
  }

  function nextQuestionExampleLabelShow(index,displayValue){
    const nextQuestionExampleLabelClass=questionsData[index+1].label;
    const nextQuestionExampleLabel=nextQuestionExampleLabelClass.replace(/ /g,"");
    const nextQuestionExampleLabelElement=document.querySelector(`.${nextQuestionExampleLabel}_example_message`);
    if(nextQuestionExampleLabelElement){
      nextQuestionExampleLabelElement.style.display=displayValue;
    }
  }

  function prevQuestionExampleLabelShow(index,displayValue){
    const prevQuestionExampleLabelClass=questionsData[index].label;
    const prevQuestionExampleLabel=prevQuestionExampleLabelClass.replace(/ /g,"");
    const prevQuestionExampleLabelElement=document.querySelector(`.${prevQuestionExampleLabel}_example_message`);
    if(prevQuestionExampleLabelElement){
      prevQuestionExampleLabelElement.style.display=displayValue;
    }
  }

  function deleteImageRadio(container) {
    const previewElement = document.getElementById("preview");
    const previewImages = previewElement.querySelectorAll("img");
    deletedImageId = container.querySelector('img').id;
    let imagesLength;
    if(previewImages){
      imagesLength=previewImages.length;
    }
    if (deletedImageId.startsWith("image_") && imagesLength>3) {
      container.remove();
      document.querySelector(`input[name="properties[${deletedImageId}]"]`).remove();
    } else if(!deletedImageId.startsWith("image_") && imagesLength>=3) {
      alert("This image is selected against the previous question");
      return;
    }else{
      alert("Minimum 3 images are required to proceed further");
      return;
    }
  }

  function selectImage(radioId, imgElement) {
    document.getElementById(radioId).checked = true;
    // Remove the 'questionSelected' class from all images
    const images = document.querySelectorAll('.image-container img');
    images.forEach(img => img.classList.remove('questionSelected'));
    // Add the 'questionSelected' class to the clicked image
    imgElement.classList.add('questionSelected');
    const closestInput = imgElement.closest('.image-container').querySelector('input[type="radio"]');
    const imageId = closestInput.id

    // Update the label styling
    const radios = document.querySelectorAll('input[name="imagesCheckbox"]');
    radios.forEach(radio => {
      const label = document.querySelector(`label[for="${radio.id}"]`);
      if (radio.id === radioId || radio.id === imageId) {
        radio.checked = true;
        label.classList.add('selected');
      } else {
        label.classList.remove('selected');
      }
    });
  }

  function resetImageSelection(){
    const images = document.querySelectorAll('.image-container img');
    images.forEach(img => img.classList.remove('questionSelected'));

    const radios = document.querySelectorAll('input[name="imagesCheckbox"]');
    radios.forEach(radio => {
      const label = document.querySelector(`label[for="${radio.id}"]`);
      label.classList.remove('selected');
    });
  }

  function hideCheckedImages(){
    imageContainers.forEach((container,index) => {
      const img = container.querySelector('img');

      if (img) {
        const radioId = img.id;

        img.onclick = () => selectImage(radioId, img);
      }else {
        if (img) {
          img.style.display = 'none';
          const closestRadio = container.querySelector('input[type="radio"]');
          if (closestRadio) {
            closestRadio.checked = false;
            closestRadio.name=closestRadio.name+"checked";
          }
        }
      }
    });
  }

  nextButtonStep3Section2.addEventListener('click', (event) => {
    step4.classList.remove('disabled');
    step4.scrollIntoView({ behavior: 'smooth' });
  });

  let rushSelector;

  if(rushSection){
    rushSelector = document.querySelector('.rushSelect');
  } 
  

  if(rushSection){
    nextButtonRushSection.addEventListener('click', (event) => {
      event.preventDefault();
      if(rushSelector.value.trim() !==""){
        step6.classList.remove('disabled');
        step6.scrollIntoView({ behavior: 'smooth' });
      }else{
        event.preventDefault();
        alert("Please select rush creation time");
        return;
      }
    });
  }

  if(plushFrameSection){
    nextButtonOnPlushFrameSection.addEventListener('click', (event) => {
      event.preventDefault();
      const backgroundOptions = document.getElementsByClassName('background-image');
      const frameOptions = document.getElementsByClassName('frame-image');

      const isBackgroundSelected = isAnySelected(backgroundOptions);
      const isFrameSelected = isAnySelected(frameOptions);
      if (!isBackgroundSelected || !isFrameSelected) {
        alert('Please select Plush Frame and Background');
        return;
      } else if (rushSection) {
        rushSection.classList.remove('disabled');
        rushSection.scrollIntoView({ behavior: 'smooth' });
      } else {
        step6.classList.remove('disabled');
        step6.scrollIntoView({ behavior: 'smooth' });
      }
    });
  }

  function isAnySelected(elements) {
    for (let i = 0; i < elements.length; i++) {
      if (elements[i].classList.contains('selected')) {
        return true;
      }
    }
    return false;
  }

  nextButtonStep4.addEventListener('click', (event) => {
    event.preventDefault();

    // Get the color input elements
    const leftEyeColorInput = document.getElementById('left-eye-color');
    const rightEyeColorInput = document.getElementById('right-eye-color');

    // Get the radio input elements for ear position
    const earImagesContainer=document.querySelector("#earImagesContainer");
    let leftEarInput;
    let rightEarInput;
    if (earImagesContainer.innerHTML.trim() !== "") {
      // Container is empty
      leftEarInput = step4.querySelectorAll('input[name="properties[_pet_left_ear]"]');
      rightEarInput = step4.querySelectorAll('input[name="properties[_pet_right_ear]"]');
    }


    // Check if eye colors are selected
    const leftEyeColor = leftEyeColorInput.value.trim();
    const rightEyeColor = rightEyeColorInput.value.trim();
    const eyeColorsSelected = leftEyeColor !== '' && rightEyeColor !== '';

    // const petData = {{ customizer[4].product_options[0] | json }};
    let petEarDetails={{customizer[3].pet_info[2] | json}};

    var productOptions = {{customizer[4].product_options | json}};
   
    if (!eyeColorsSelected) {
      alert("Please select eye colors for both eyes.");
      return; // Prevent further execution
    }
    if(selectedPetTypeName && isPetTypeAvailable(selectedPetTypeName,productOptions)){
      const selectedPetOptions = petEarDetails[selectedPetTypeName];
      const earPositions = selectedPetOptions?.ear_position;
     
      // Check if ear positions are selected
      let earPositionSelected = true; 
      if(earPositions){
        if (Object.keys(earPositions).length) {
          const leftEarSelected = Array?.from(leftEarInput).some(input => input.checked);
          const rightEarSelected = Array?.from(rightEarInput).some(input => input.checked);
          earPositionSelected = leftEarSelected && rightEarSelected;
        }else{
          earPositionSelected=true;
        }
      }

      // Validate eye colors and ear positions
      if (!eyeColorsSelected) {
        alert("Please select eye colors for both eyes.");
        return; // Prevent further execution
      } else if (earPositions && !earPositionSelected ) {
        alert("Please select ear positions for both left and right ears.");
        return; // Prevent further execution
      }else if(!productOptionDisplay){
        if(plushFrameSection){
          plushFrameSection.classList.remove('disabled');
          plushFrameSection.scrollIntoView({ behavior: 'smooth' });
        }else if(rushSection){
          rushSection.classList.remove('disabled');
          rushSection.scrollIntoView({ behavior: 'smooth' });
        }else{
          step6.classList.remove('disabled');
          step6.scrollIntoView({ behavior: 'smooth' });
        }
      }
      step5.classList.remove('disabled');
      step5.scrollIntoView({ behavior: 'smooth' });
    }else{
      if(plushFrameSection){
        plushFrameSection.classList.remove('disabled');
        plushFrameSection.scrollIntoView({ behavior: 'smooth' });
      }else if(rushSection){
        rushSection.classList.remove('disabled');
        rushSection.scrollIntoView({ behavior: 'smooth' });
      }else{
        step6.classList.remove('disabled');
        step6.scrollIntoView({ behavior: 'smooth' });
      }
    }
  });

  function isPetTypeAvailable(selectedPetType, options) {
    // Iterate over each item in the productOptions array
    for (var i = 0; i < options.length; i++) {
      var option = options[i];
        
      // Check if the current option object contains the selected pet type
      if (selectedPetType in option) {
        var petType = option[selectedPetType];
        
        // Iterate over each position dynamically
        for (var position in petType) {
          if (petType.hasOwnProperty(position) && typeof petType[position] === 'object') {
            // Check if the position has the display property set to true
            if (petType[position].display == true) {
              return true; // At least one position is not displayed
            }
          }
        }
          
        return false; // All positions are displayed
      }
    }
    
    return false; // Pet type not found
  }

  const petData = {{ customizer[4].product_options[1] | json }};
  function handleEarImages(petName){
    const earImagesContainer = document.getElementById('earImagesContainer');

    let pet_data={{customizer[3].pet_info[2] | json}};  
    
    if (petName) {
      const selectedPetOptions = pet_data[petName];

      const earPositions = selectedPetOptions.ear_position;
      const earPostionLabel=selectedPetOptions.ear_positions_heading;


        if (Object.keys(earPositions).length) {
            // Check if ear positions are available for the selected pet type
            const rightEarPositions = earPositions.Right;
            const leftEarPositions = earPositions.Left;

            if (rightEarPositions && leftEarPositions) {
                // Clear existing content inside the earImagesContainer
                earImagesContainer.innerHTML = '';
                // Create HTML elements for right ear positions
                const earPositionLabel=document.createElement('h3');
                earPositionLabel.textContent=earPostionLabel;
                earPositionLabel.id = 'ear-position-heading'; 
                earPositionLabel.classList.add("mt-20");

                const earPositionDiv=document.createElement('div');
                earPositionDiv.classList.add("ear-images");

                const rightColDiv = document.createElement('div');
                rightColDiv.classList.add('right-col');
                const rightLabel = document.createElement('p');
                rightLabel.classList.add('right-label', 'mt-20');
                rightLabel.textContent = 'Right';
                rightColDiv.appendChild(rightLabel);
                rightEarPositions.forEach(position => {
                    const label = document.createElement('label');
                    const input = document.createElement('input');
                    const newDiv = document.createElement("div");
                    newDiv.classList.add('ear-label');
                    input.id = 'right-ear';
                    input.type = 'radio';
                    input.name = 'properties[_pet_right_ear]';
                    input.tabIndex = -1;
                    input.value = position.value;
                    const img = document.createElement('img');
                    img.src = position.src;
                    img.alt = position.alt;
                    img.setAttribute("aria-label", `${rightLabel.textContent} ${earPostionLabel} ${position.alt}`);
                    img.setAttribute("tabindex", "0");
                    img.addEventListener("keydown", handlePetEarKeyDown);
                    label.appendChild(input);
                    newDiv.appendChild(img);
                    label.appendChild(newDiv);
                    rightColDiv.appendChild(label);
                });

                // Create HTML elements for left ear positions
                const leftColDiv = document.createElement('div');
                leftColDiv.classList.add('left-col');
                const leftLabel = document.createElement('p');
                leftLabel.classList.add('left-label', 'mt-20');
                leftLabel.textContent = 'Left';
                leftColDiv.appendChild(leftLabel);
                leftEarPositions.forEach(position => {
                    const label = document.createElement('label');
                    const input = document.createElement('input');
                    const newDiv = document.createElement("div");
                    newDiv.classList.add('ear-label');
                    input.id = 'left-ear';
                    input.type = 'radio';
                    input.name = 'properties[_pet_left_ear]';
                    input.value = position.value;
                    input.tabIndex = -1;
                    const img = document.createElement('img');
                    img.src = position.src;
                    img.alt = position.alt;
                    img.setAttribute("aria-label", `${leftLabel.textContent} ${earPostionLabel} ${position.alt}`);
                    img.setAttribute("tabindex", "0");
                    img.addEventListener("keydown", handlePetEarKeyDown);
                    label.appendChild(input);
                    newDiv.appendChild(img)
                    label.appendChild(newDiv);
                    leftColDiv.appendChild(label);
                });

                // Append the created elements to earImagesContainer
                earPositionDiv.appendChild(leftColDiv);
                earPositionDiv.appendChild(rightColDiv);
                earImagesContainer.appendChild(earPositionLabel);
                earImagesContainer.appendChild(earPositionDiv);
                earImagesContainer.style.display = 'block';

            } else {
                // Hide the entire "Ear Position" section if ear positions are not available
                earImagesContainer.style.display = 'none';
                earImagesContainer.innerHTML='';
            }
      }
    }else{
      earImagesContainer.style.display = 'none';
      earImagesContainer.innerHTML='';
    }
  }
  const formInput=document.getElementById("formId");
  const zipperInput=document.querySelector("#zipperHiddenInput");


  function zipperPouchCheckbox(checkbox){

   let data={{ plushProduct | json}};
   
   if (checkbox.checked) {
    zipperInput.value=true;
      // If the checkbox is checked and the title is included, set the form input value to the corresponding ID
      formInput.value = data.variants.find(variant => variant.title === "Included").id;
    }else {
    zipperInput.value=false;

    // If the checkbox is not checked, set a default value
    formInput.value = data.variants.find(variant => variant.title === "Not Included").id;
    }
  }

  let products = {{customizer[5].final_options.products | json }};

  let originalUniqueKey=document.querySelector("#originalUniqueKey").value;
  let isPresentFreeProduct=false;
  {% if plushProduct.tags contains "offer-free-gift" %}
    isPresentFreeProduct=true;
  {%endif%}  

  $('#add_to_cart').on('click', function(event){
    event.preventDefault();

    var shopifyCartUrl = '{{shop.metafields.cuddleclones.store_url}}/cart/add.js';    

    var checkboxStatus = {};
    var itemsArray=[];
    var itemQuantity = document.getElementById('quantity').value;
    var checkedWithoutSelect=false;
    
    if(products){
      for (let i = 0; i < products.length; i++) {
      
        var productName = products[i].name;

        // Get the selected dropdown value
        var selectedDropdown = document.querySelector('.'+productName+'_select');
          
        var selectedIndex;
        var selectedValue;

        if (selectedDropdown !== null) {
          selectedIndex = selectedDropdown.selectedIndex;
          selectedValue = selectedDropdown.value;
        }

        // Assign values to variables with dynamic names
        var selectedIndexVarName = productName + '_selectedIndex';
        var selectedValueVarName = productName + '_selectedValue';
        window[selectedIndexVarName] = selectedIndex;
        window[selectedValueVarName] = selectedValue;     

        // Get all checkbox elements
        var checkbox = document.querySelectorAll('.' + productName + '_checkbox');

        checkbox.forEach(function(checkbox) {
          // Get the value of the checkbox
          var value = checkbox.value;
          var status = checkbox.checked ? 'checked' : 'unchecked';
          checkboxStatus[productName] = status;

          if (status === 'checked' && (!selectedDropdown || selectedIndex === 0) && selectedDropdown!==null ) {
            alert("Please select " + productName+" Variant");
            checkedWithoutSelect=true;
            return;
          }else{
            if (status === 'checked' && selectedDropdown!==null) {
              if(productName==="rush"){
                var item = {
                  quantity: itemQuantity,
                  id: window[selectedValueVarName],
                  properties: {
                    _unique_key:originalUniqueKey,
                    _rush_product: true,
                  }
                };
              }else{
                if(productName==="zipper_pouch" || productName==="heartbeat_box" || productName==="bobblehead_giftBox" || productName==="weighted_insert"){
                  var item = {
                    quantity: itemQuantity,
                    id: window[selectedValueVarName],
                    properties: {
                      _unique_key:originalUniqueKey,
                      _accessories_product: true,
                      _accessories_product_info: selectedPetTypeName,
                    }
                  };
                }else{
                  var item = {
                    quantity: itemQuantity,
                    id: window[selectedValueVarName],
                    properties: {
                      _unique_key:originalUniqueKey,
                      _accessories_product: true,
                      _accessories_product_info: productName === 'custom_bandana' ? document.querySelector('.bandannaInput').value : selectedPetTypeName,
                      text: productName === 'custom_bandana' ? document.querySelector('.bandannaInput').value : "",
                    }
                  };
                }
              }
              itemsArray.push(item);
            }else if(status === 'checked' && selectedDropdown===null && productName!=='cuddle_crate'){
              document.getElementById(productName).value = 'Yes';
              let inputValue=document.querySelector('.input_'+productName);
              var item={
                quantity: itemQuantity, 
                id: inputValue.value, 
                properties: {
                  _unique_key:originalUniqueKey,
                  _accessories_product: true,
                  _accessories_product_info: selectedPetTypeName,
                }
              }
              itemsArray.push(item);  
            }else if(status === 'checked' && selectedDropdown===null && productName==='cuddle_crate'){
              let cuddleCrateHiddenInput=document.querySelector(".cuddleCrateInput");
              if(cuddleCrateHiddenInput){
                cuddleCrateHiddenInput.value = 'Yes';
              }
              let inputValue=document.querySelector('.input_'+productName);
              var item={
                quantity: itemQuantity, 
                id: inputValue.value, 
                properties: {
                  _unique_key:originalUniqueKey,
                  _accessories_product: true,
                  _accessories_product_info: selectedPetTypeName
                }
              }
              itemsArray.push(item);
            }
          }
        });
      }
    }
    
    let rushSelect=document.querySelector(".rushSelect");
    
    if(rushSelect && rushSelect.selectedIndex>1){
      var rushItem={
        quantity:itemQuantity,
        id:rushSelect.value,
        properties: {
          _rush_product: true,
          _unique_key:originalUniqueKey,
        }
      };
      itemsArray.push(rushItem);
    }

    // Call the function
    if(!checkedWithoutSelect){
      handleAddToCartPostscript();
      addToCart(itemsArray, shopifyCartUrl);
    }else{
      return;
    }

    async function addToCart(itemsArray, shopifyCartUrl) {
      try {
        const formData = $('#customizer_form').serializeArray();

        let mainItemId=null;

        const primaryProduct = formData.reduce((acc, { name, value }) => {
          const match = name.match(/^properties\[(.+?)\]$/);

          if (match) {
            const extractedName = match[1];

            if (acc[extractedName]) {
              if (Array.isArray(acc[extractedName])) {
                acc[extractedName].push(value);
              } else {
                acc[extractedName] = [acc[extractedName], value];
              }
            } else {
              acc[extractedName] = value;
            }
          }else if (name === 'id') {
            mainItemId = value;
          }
          return acc;
        }, {});

        const mainItem = {
          quantity: itemQuantity || 1,
          id: mainItemId, 
          properties: { ...primaryProduct },
        };

        itemsArray.unshift(mainItem);

        const combinedItems = itemsArray.map(item => ({
          quantity: item.quantity || 1, 
          id: item.id, 
          properties: item.properties || {}, 
        }));

        const combinedOptions = {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ items: combinedItems })
        };

        const response = await fetch(shopifyCartUrl, combinedOptions);

        if(isPresentFreeProduct){
          addFreeProduct();
        }else{
          window.location.href = "{{ shop.url }}/cart";
        }
      } catch (error) {
        console.error('An error occurred in the addToCart function:', error);
      } 
    }
  });

// dependency Logic
  if (products) {
    for (let i = 0; i < products.length; i++) {
      var product = products[i];
      var productName = product.name;
      let mainCheckboxes = document.querySelectorAll(`.${productName}_checkbox`);

      if (product.detail.dependency?.length > 0) {
        product.detail.dependency.forEach(function (item) {
          const itemName = item.product_name;
          const itemMessage = item.message;
          const itemCheckboxes = document.querySelectorAll(`.${itemName}_checkbox`);

          mainCheckboxes.forEach(function (mainCheckbox) {
            mainCheckbox.addEventListener('change', function () {
              if (mainCheckbox.checked) {
                itemCheckboxes.forEach(function (itemCheckbox) {
                  if (!itemCheckbox.checked) {
                    itemCheckbox.checked = true;
                    alert(itemMessage);
                  }
                });
              }
            });
          });
          // if user tries to uncheck the dependent product then forcefully check the dependent product if main product is checked
          itemCheckboxes.forEach(function(itemCheckbox){
           itemCheckbox.addEventListener('change', function(){
             mainCheckboxes.forEach(function (mainCheckbox) {
                if (mainCheckbox.checked && !itemCheckbox.checked) {
                  itemCheckbox.checked = true;
                }
              });
           })
          })
        });
      }
    }
  }

  const hostname = window.location.hostname;
  productionDomain = 'cuddleclones.com';
  
  function handleAddToCartPostscript(){
    let plush_product={{plushProduct|json}};
    if(hostname == productionDomain){
        window.postscript.event('add_to_cart', {
          "shop_id": "194751", // Postscript Shop ID
          "url": "{{shop.metafields.cuddleclones.store_url}}/products/{{plush_product.handle}}", // absolute URL of the current page
          "search_params": { "variant": plush_product.variants[0].id }, 
          "page_type": "product",
          "referrer": "{{shop.metafields.cuddleclones.store_url}}/collections/all", // absolute URL of the referring page
          "resource": { // information about the product
            "category": "Uncategorized", 
            "name": plush_product.title, 
            "price_in_cents": plush_product.price, 
            "resource_id": plush_product.id, 
            "resource_type": "product", 
            "sku": null, 
            "variant_id": plush_product.variants[0].id, 
            "vendor": plush_product.vendor 
          }
        });
    }
  }
  
  function addFreeProduct(){

    var values = {};
          $.each($("form").serializeArray(), function (i, field) {
            values[field.name] = field.value;
          });

    let product_id = $("#free_product_div").data("product_id");
        let mainUrl = "";
        let hostUrl = window.location.host;
        const currentUrl = window.location.href;
        const baseUrl = currentUrl.replace(/\/[^/]*$/, "/");
        let nativeOptionsKeys = [];
        let freeInputKey = document.querySelector("#freeUniqueId");

        // check the host url for end point
        if (hostUrl == "cuddleclones-dev.myshopify.com") {
          mainUrl = "https://oms-dev.cuddleclones.com/";
        } else if (hostUrl == "cuddleclones.com") {
          mainUrl = "https://oms.cuddleclones.com/";
        } else {
          mainUrl = "https://oms-uat.cuddleclones.com/";
        }
        const endpoint = `${mainUrl}shopify_api/get_product_metafields?product_id=${product_id}`;

        // product _id in metafiled ajax
        let product_check = false;
        let variant_check = false;
        let offer_all_product = false;
        let free_product_id = "";
        let metafieldsUrl = "";
        let endpoint2 = "";

        $.ajax({
          url: endpoint,
          method: "GET",
          headers: {
           'api-token':omsDetails[0].api_token
          },
          success: function (response) {
            
            const productId = response;
            const variant_id = response["id"];
            $.each(productId.response.metafields, (index, value) => {
              if (value.key == "product_id") {
                var json_obj = JSON.parse(value.value);
                free_product_id=json_obj;
              }

              if (free_product_id) {
                $(".freeProductSection").show();
                var freeProductDiv =
                  document.getElementById("free_product_div");
                if (freeProductDiv) {
                  freeProductDiv.style.visibility = "visible";
                }
                $("#free_product_title").append("");
                const meta_product_id = free_product_id;

                // get handle of free product
                endpoint2 = `${mainUrl}shopify_api/get_product?product_id=${meta_product_id}`;
                metafieldsUrl = `${mainUrl}shopify_api/get_product_metafields?product_id=${meta_product_id}`;
              }else{
                $(".freeProductSection").hide();
              }
            });
            if(free_product_id){
              $.ajax({
                url: endpoint2,
                method: "GET",
                headers: {
                 'api-token':omsDetails[0].api_token
                },
                success: function (response) {
                  $(".freeProductVariants").val(
                    JSON.stringify(response.response.product.variants)
                  );
                  $(".productName").html("");
                  $(".productComparePrice").html("");
                  const data = response;
                  const title = data.response.product.title;
                  const imageUrl = data.response.product.image.src;
                  const compareAtPrice =
                    data.response.product.variants[0].compare_at_price;
                  $(".productName").append(title);
                  $(".productImage").attr("src", imageUrl);
                  $(".productComparePrice").append(`$${compareAtPrice}`);
                },
              });
              $.ajax({
                url: metafieldsUrl,
                method: "GET",
                headers: {
                 'api-token':omsDetails[0].api_token
                },
                success: function (response) {
                  metafieldsData = response.response.metafields;
                  $(".productReviews").html("");
                  $(".variantsContainer").html("");
                  
                  displayMetafields(metafieldsData);
                  $(".freeProductKeys").val(JSON.stringify(nativeOptionsKeys));
                },
                error: function (error) {
                  console.log("Error:", error);
                },
              });
            }else{
              window.location.href = "{{ shop.url }}/cart";
            }
          },
        });

        let metafieldsData = null;

        function displayMetafields(metafields) {
          let customOptions = [];
          let nativeValues = [];
          freeInputKey.value = values["properties[_original_unique_key]"];
          metafields.forEach((metafield) => {
            if (metafield.key === "badge") {
              let reviews = metafield.value;
              $(".productReviews").append(reviews);
            } else if (metafield.key === "custom_options") {

              customOptions = JSON.parse(metafield.value);

            } else if (metafield.key === "native_options") {
              nativeValues = JSON.parse(metafield.value);
              nativeOption=JSON.parse(metafield.value);
            }
          });
          displayCustomOptions(customOptions);
          displayNativeOptions(nativeValues);
          
         
      // end free product code

  }

  function displayNativeOptions(nativeValues) {
    for (var key in nativeValues) {
                if (nativeValues.hasOwnProperty(key)) {
                  let option = nativeValues[key];

                  nativeOptionsKeys.push(option.name);

                  if (option.type === "dropdown") {
                    const hiddenInput = document.createElement("input");
                    hiddenInput.type = "hidden";
                    hiddenInput.name = `properties[${option.name}]`;
                    hiddenInput.classList.add(`${option.name}HiddenInput`);
                    hiddenInput.value = option.options[0].value;

                    const div = document.createElement("div");
                    div.classList.add(
                      "selector-wrapper-free-product",
                      option.name
                    );

                    // Create the label element
                    const label = document.createElement("label");
                    label.setAttribute("for", `data-variant-option-`);
                    label.setAttribute(
                      "data-variant-option-name",
                      option.heading
                    );
                    label.setAttribute(
                      "data-variant-option-choose-name",
                      option.heading
                    );
                    label.textContent = option.heading;
                    div.appendChild(label);

                    // Create the span element
                    const span = document.createElement("span");
                    span.classList.add("selectFreeVariant");
                    span.setAttribute("data-dropdown-form-style", "");
                    div.appendChild(span);

                    // Create the select element
                    const select = document.createElement("select");
                    select.classList.add(
                      "single-option-selector",
                      "nativeLengthOption"
                    );
                    select.id = `data-variant-option-`;
                    select.setAttribute(
                      "onchange",
                      `handleNativeAction("${option.heading}", this, "", "dropdown")`
                    );
                    // select.setAttribute('onchange', `handleNativeAction('${option.heading}', this, '', '${option.name}');`);
                    select.setAttribute("data-variant-option", "");

                    select.style.color = "black";
                    span.appendChild(select);

                    // Create and append the default option
                    const defaultOption = document.createElement("option");
                    defaultOption.value = option.options[0].value; // Set default value to the first option
                    defaultOption.selected = true;
                    defaultOption.setAttribute(
                      "data-variant-option-value-wrapper",
                      ""
                    );
                    defaultOption.setAttribute("data-variant-option-value", "");
                    defaultOption.setAttribute(
                      "data-variant-option-value-index",
                      ""
                    );
                    defaultOption.innerHTML = option.options[0].name; // Display the name of the first option
                    select.appendChild(defaultOption);

                    // Create the options
                    option.options.forEach((value, index) => {
                      if (value.display && index !== 0) {
                        const optionElement = document.createElement("option");
                        optionElement.value = value.value;
                        // if (
                        //   selectedVariant &&
                        //   option.selected_value == value.value
                        // ) {
                        //   optionElement.selected = true;
                        // }
                        optionElement.setAttribute(
                          "data-variant-option-value-wrapper",
                          ""
                        );
                        optionElement.setAttribute(
                          "data-variant-option-value",
                          ""
                        );
                        optionElement.setAttribute(
                          "data-variant-option-value-index",
                          ""
                        );
                        optionElement.textContent = value.name;
                        select.appendChild(optionElement);
                      }
                    });

                    // Append the div to a target container
                    const targetContainer = $(".variantsContainer");
                    targetContainer.append(hiddenInput);
                    targetContainer.append(div);
                  } else if (option.type === "button") {
                    const hiddenInput = document.createElement("input");
                    hiddenInput.type = "hidden";
                    hiddenInput.name = `properties[${option.name}]`;
                    hiddenInput.classList.add(`${option.name}HiddenInput`);
                    hiddenInput.value = option.options[0].value;

                    if (option.shape === "circle") {
                      // Create the main div element
                      const div = document.createElement("div");
                      div.classList.add("selector-wrapper-free-product");

                      // Create the label element
                      const label = document.createElement("label");
                      label.setAttribute("for", `data-variant-option-`);
                      label.setAttribute(
                        "data-variant-option-name",
                        option.heading
                      );
                      label.setAttribute(
                        "data-variant-option-choose-name",
                        option.heading
                      );
                      label.textContent = option.heading;
                      div.appendChild(label);

                      // Create the span element
                      const span = document.createElement("span");
                      div.appendChild(span);

                      // Loop through the options and create buttons
                      option.options.forEach((value, index) => {
                        if (value.display) {
                          const headingWithHyphen = option.heading.replace(
                            /\s+/g,
                            "_"
                          );
                          const button = document.createElement("button");
                          button.type = "button";
                          button.classList.add(
                            "rounded-button",
                            `${headingWithHyphen}CircleButton`
                          );
                          button.setAttribute(
                            "onclick",
                            `handleNativeAction('${option.heading}', this, '.rounded-button', 'button', 'Circle')`
                          );
                          button.textContent = value.name;
                          if (index === 0) {
                            button.classList.add("selected"); // Add a class for selected state
                            button.style.border = "2px solid rgb(84, 97, 200)"; // Apply specific style
                          }
                          span.appendChild(button);
                        }
                      });

                      const targetContainer = $(".variantsContainer");
                      targetContainer.append(hiddenInput);
                      targetContainer.append(div);
                    } else if (option.shape === "rectangle") {
                      // Create the main div element
                      const div = document.createElement("div");
                      div.classList.add(
                        "selector-wrapper-free-product",
                        "defaultNumberOfOptions"
                      );

                      // Create the label element
                      const label = document.createElement("label");
                      label.setAttribute("for", `data-variant-option-`);
                      label.setAttribute(
                        "data-variant-option-name",
                        option.heading
                      );
                      label.setAttribute(
                        "data-variant-option-choose-name",
                        option.heading
                      );
                      label.textContent = option.heading;
                      div.appendChild(label);

                      // Create the span element
                      const span = document.createElement("span");
                      div.appendChild(span);

                      // Loop through the options and create buttons
                      option.options.forEach((value, index) => {
                        if (value.display) {
                          const headingWithHyphen = option.heading.replace(
                            /\s+/g,
                            "_"
                          );
                          const button = document.createElement("button");
                          button.type = "button";
                          button.classList.add(
                            "rectangle-button",
                            `${headingWithHyphen}RectangleButton`
                          );
                          button.setAttribute(
                            "onclick",
                            `handleNativeAction('${option.heading}', this, '.rectangle-button', 'button', 'Rectangle')`
                          );
                          button.textContent = value.name;
                          if (index === 0) {
                            button.classList.add("selected"); // Add a class for selected state
                            button.style.border = "2px solid rgb(84, 97, 200)"; // Apply specific style
                          }
                          span.appendChild(button);
                        }
                      });

                      // Append the hidden input and the divs to a target container
                      const targetContainer = $(".variantsContainer");
                      targetContainer.append(hiddenInput);
                      targetContainer.append(div);
                    }
                  } else if (option.type === "swatches") {
                    const hiddenInput = document.createElement("input");
                    hiddenInput.type = "hidden";
                    hiddenInput.name = `properties[${option.name}]`;
                    hiddenInput.classList.add(`${option.name}HiddenInput`);
                    hiddenInput.value = option.options[0].name;

                    // Create the main swatches-select div element
                    const swatchesSelectDiv = document.createElement("div");
                    swatchesSelectDiv.classList.add("swatches-select");
                    swatchesSelectDiv.style.marginBottom = "10px";

                    // Create the swatches-label div element
                    const swatchesLabelDiv = document.createElement("div");
                    swatchesLabelDiv.classList.add(
                      "swatches-label-free-product"
                    );

                    // Create the label element
                    const label = document.createElement("label");
                    label.setAttribute("for", `data-variant-option-`);
                    label.setAttribute(
                      "data-variant-option-name",
                      option.heading
                    );
                    label.setAttribute(
                      "data-variant-option-choose-name",
                      option.heading
                    );
                    label.innerHTML = `${option.heading}: <span id="${option.heading}-selected-swatches">${option.options[0].name}</span>`;
                    swatchesLabelDiv.appendChild(label);

                    // Append the swatches-label div to the swatches-select div
                    swatchesSelectDiv.appendChild(swatchesLabelDiv);

                    // Create the inner div for the radio buttons and images
                    const innerDiv = document.createElement("div");
                    innerDiv.style.marginBottom = "15px";
                    innerDiv.style.marginLeft = "25px";

                    // Loop through the options and create radio buttons with images
                    option.options.forEach((value, index) => {
                      if (value.display) {
                        const label = document.createElement("label");
                        label.style.marginRight = "10px";

                        const radioInput = document.createElement("input");
                        radioInput.type = "radio";
                        radioInput.name = option.name;
                        radioInput.value = value.name;
                        radioInput.style.width = "0px";
                        label.appendChild(radioInput);

                        if (index === 0) {
                          radioInput.checked = true;
                        }

                        const img = document.createElement("img");
                        img.src = value.image;
                        img.alt = value.name;
                        img.title = value.name;
                        img.width = 50;
                        img.height = 50;
                        img.onclick = function () {
                          handleNativeAction(
                            option.heading,
                            value.name,
                            "",
                            "swatches"
                          );
                        };
                        label.appendChild(img);

                        innerDiv.appendChild(label);
                      }
                    });

                    // Append the inner div to the swatches-select div
                    swatchesSelectDiv.appendChild(innerDiv);

                    // Append the hidden input and the swatches-select div to a target container
                    const targetContainer = $(".variantsContainer");
                    targetContainer.append(hiddenInput);
                    targetContainer.append(swatchesSelectDiv);
                  }
                }
              }

  }

  function displayCustomOptions(customOptions) {
    for (var key in customOptions) {
                let option = customOptions[key];

                if (option.display && option.type === "dropdown") {
                  // Create the hidden input element
                  let hiddenInput = document.createElement("input");
                  hiddenInput.type = "hidden";
                  hiddenInput.name = `properties[${option.heading}]`;
                  hiddenInput.value = option.options[0].value;
                  hiddenInput.className = `${option.name}HiddenInput`;

                  // Create the selector wrapper div
                  let selectorWrapper = document.createElement("div");
                  selectorWrapper.className = "selector-wrapper-free-product";

                  // Create the label element
                  let label = document.createElement("label");
                  label.setAttribute("for", `data-variant-option`);
                  label.setAttribute("data-variant-option-name", option.name);
                  label.setAttribute(
                    "data-variant-option-choose-name",
                    option.name
                  );
                  label.innerHTML = option.heading;

                  // Create the span element
                  let span = document.createElement("span");
                  span.className = "selectFreeVariant";
                  span.setAttribute("data-dropdown-form-style", "");

                  // Create the select element
                  let select = document.createElement("select");
                  select.className = "single-option-selector";
                  select.setAttribute(
                    "onchange",
                    `getSelectedValue('${option.name}', this, '${option.heading}');`
                  );
                  select.id = `data-variant-option`;
                  select.setAttribute("data-variant-option", "");
                  select.setAttribute("data-variant-option-index", "");
                  select.style.color = "black";

                  // Create and append the default option
                  const defaultOption = document.createElement("option");
                  defaultOption.value = option.options[0].value; // Set default value to the first option
                  defaultOption.selected = true;
                  defaultOption.setAttribute(
                    "data-variant-option-value-wrapper",
                    ""
                  );
                  defaultOption.setAttribute("data-variant-option-value", "");
                  defaultOption.setAttribute(
                    "data-variant-option-value-index",
                    ""
                  );
                  defaultOption.innerHTML = option.options[0].name; // Display the name of the first option

                  select.appendChild(defaultOption);

                  // Create and append the other options
                  option.options.forEach((value, idx) => {
                    if (idx !== 0) {
                      // Skip the first option as it's already added
                      const optionElement = document.createElement("option");
                      optionElement.value = value.value;
                      if (option.selected_value === value.value) {
                        optionElement.selected = true;
                        hiddenInput.value = value.value; // Update hidden input with selected value
                      }
                      optionElement.setAttribute(
                        "data-variant-option-value-wrapper",
                        ""
                      );
                      optionElement.setAttribute(
                        "data-variant-option-value",
                        ""
                      );
                      optionElement.setAttribute(
                        "data-variant-option-value-index",
                        ""
                      );
                      optionElement.innerHTML = value.name;

                      select.appendChild(optionElement);
                    }
                  });

                  // Append the select element to the span
                  span.appendChild(select);

                  // Append the label and span to the selector wrapper
                  selectorWrapper.appendChild(label);
                  selectorWrapper.appendChild(span);

                  // Append the hidden input and selector wrapper to the desired parent element
                  const targetContainer = $(".variantsContainer");
                  targetContainer.append(hiddenInput);
                  targetContainer.append(selectorWrapper);
                } else if (option.display && option.type === "swatches") {
                  // Create the hidden input element
                  let hiddenInput = document.createElement("input");
                  hiddenInput.type = "hidden";
                  hiddenInput.name = `properties[${option.heading}]`;
                  hiddenInput.className = `${option.name}HiddenInput`;
                  hiddenInput.value = option.options[0].name;

                  // Create the main wrapper div
                  let mainWrapper = document.createElement("div");
                  mainWrapper.className = "swatches-select-free-product";
                  mainWrapper.style.marginBottom = "10px";

                  // Create the label wrapper div
                  let labelWrapper = document.createElement("div");
                  labelWrapper.className = "swatches-label-free-product";

                  // Create the label element
                  let label = document.createElement("label");
                  label.setAttribute("for", `data-variant-option-`);
                  label.setAttribute("data-variant-option-name", option.name);
                  label.setAttribute(
                    "data-variant-option-choose-name",
                    option.name
                  );

                  label.innerHTML = `${option.heading}: <span id="${option.name}-selected-swatches">${option.options[0].name}</span>`;

                  // Append the label to the label wrapper
                  labelWrapper.appendChild(label);

                  // Create the inner wrapper div
                  let innerWrapper = document.createElement("div");
                  innerWrapper.classList.add("free-swatches");
                  innerWrapper.style.marginBottom = "15px";
                  innerWrapper.style.marginLeft = "25px";

                  // Create the radio buttons and images
                  option.options.forEach((value, index) => {
                    let labelElement = document.createElement("label");
                    labelElement.style.marginRight = "10px";

                    let radioInput = document.createElement("input");
                    radioInput.classList.add("customRadioInputs");
                    radioInput.id = option.name;
                    radioInput.type = "radio";
                    radioInput.name = option.name;
                    radioInput.value = value.name;

                    if (index === 0) {
                      radioInput.checked = true;
                    }

                    let image = document.createElement("img");
                    image.src = value.image;
                    image.alt = value.name;
                    image.title = value.name;
                    image.classList.add("swatchesImage")
                    image.width = 50;
                    image.height = 50;
                    image.setAttribute(
                      "onclick",
                      `selectBackground('${option.name}', '${value.name}', '${option.heading}')`
                    );

                    // Append the radio input and image to the label element
                    labelElement.appendChild(radioInput);
                    labelElement.appendChild(image);

                    // Append the label element to the inner wrapper
                    innerWrapper.appendChild(labelElement);
                  });

                  // Append the label wrapper and inner wrapper to the main wrapper
                  mainWrapper.appendChild(labelWrapper);
                  mainWrapper.appendChild(innerWrapper);

                  // Append the hidden input and main wrapper to the desired parent element
                  const targetContainer = $(".variantsContainer");
                  targetContainer.append(hiddenInput);
                  targetContainer.append(mainWrapper);
                } else if (option.display && option.type === "button") {
                  // Create the hidden input element
                  let hiddenInput = document.createElement("input");
                  hiddenInput.type = "hidden";
                  hiddenInput.name = `properties[${option.heading}]`;
                  hiddenInput.className = `${option.name}HiddenInput`;
                  hiddenInput.value = option.options[0].value;
                  if (option.shape === "circle") {
                    // Replace spaces with underscores in the heading
                    const headingWithHyphen = option.heading.replace(/ /g, "_");

                    // Create the main wrapper div
                    const mainWrapper = document.createElement("div");
                    mainWrapper.className = "selector-wrapper-free-product";

                    // Create the label element
                    const label = document.createElement("label");
                    label.setAttribute("for", `data-variant-option-`);
                    label.setAttribute("data-variant-option-name", option.name);
                    label.setAttribute(
                      "data-variant-option-choose-name",
                      option.name
                    );

                    label.innerHTML = option.heading;

                    // Create the span wrapper
                    const span = document.createElement("span");

                    // Create the buttons
                    option.options.forEach((value, index) => {
                      const button = document.createElement("button");

                      button.type = "button";
                      button.className = `rounded-button ${headingWithHyphen}CircleButtonCustom`;
                      button.setAttribute(
                        "onclick",
                        `changeBorderColor('${option.name}', this, '.rounded-button', '${option.heading}', 'Circle')`
                      );

                      button.innerHTML = value.name;
                      if (index === 0) {
                        button.classList.add("selected"); // Add a class for selected state
                        button.style.border = "2px solid rgb(84, 97, 200)"; // Apply specific style
                      }

                      // Append the button to the span
                      span.appendChild(button);
                    });

                    // Append the label and span to the main wrapper
                    mainWrapper.appendChild(label);
                    mainWrapper.appendChild(span);

                    // Append the main wrapper to the desired parent element
                    const targetContainer = $(".variantsContainer");
                    targetContainer.append(hiddenInput);
                    targetContainer.append(mainWrapper);
                  } else if (option.shape === "rectangle") {
                    // Replace spaces with underscores in the heading
                    const headingWithHyphen = option.heading.replace(/ /g, "_");

                    // Create the main wrapper div
                    const mainWrapper = document.createElement("div");
                    mainWrapper.className = "selector-wrapper-free-product";

                    // Create the label element
                    const label = document.createElement("label");
                    label.setAttribute("for", `data-variant-option-`);
                    label.setAttribute("data-variant-option-name", option.name);
                    label.setAttribute(
                      "data-variant-option-choose-name",
                      option.name
                    );

                    label.innerHTML = option.heading;

                    // Create the span wrapper
                    const span = document.createElement("span");

                    // Create the buttons
                    option.options.forEach((value, index) => {
                      const button = document.createElement("button");
                      button.type = "button";
                      button.className = `rectangle-button ${headingWithHyphen}RectangleButtonCustom`;
                      button.setAttribute(
                        "onclick",
                        `changeBorderColor('${option.name}', this, '.rectangle-button', '${option.heading}', 'Rectangle')`
                      );
                      button.innerHTML = value.name;

                      if (index === 0) {
                        button.classList.add("selected"); // Add a class for selected state
                        button.style.border = "2px solid rgb(84, 97, 200)"; // Apply specific style
                      }

                      // Append the button to the span
                      span.appendChild(button);
                    });

                    // Append the label and span to the main wrapper
                    mainWrapper.appendChild(label);
                    mainWrapper.appendChild(span);

                    // Append the main wrapper to the desired parent element
                    const targetContainer = $(".variantsContainer");
                    targetContainer.append(hiddenInput);
                    targetContainer.append(mainWrapper);
                  }
                }
              }
          }
  }

  function deleteUniqueImage(event) {
    // Get the parent image container
    const imageContainer = event.target.closest('.selected-image-container');
    
    const imageElement = imageContainer.querySelector('img');

    // Get the image URL
    const imageUrl = imageElement.src;
    const parts = imageUrl.split('/');

    // Get the last part of the URL
    let imageName = parts[parts?.length - 1];
   
    if(imageName){
      imageContainer.innerHTML="";

      // fetch(`${omsDetails[0].url}image_uploader/delete_image?image_name=${imageName}`, {
      //                 headers: headerForImage,
      //                 method: 'DELETE'
      //             })
      //             .then(response => {
      //                 if (!response.ok) {
      //                     throw new Error(`HTTP error! Status: ${response.status}`);
      //                 }
      //                 return response.json();
      //             })
      //             .then(data => {
      //                 console.log('Previous image deleted:', data);
      //             })
      //             .catch(error => {
      //                 console.error('Error deleting previous image:', error);
      //             });
    }
  }

  // ------------Event Listner to validate the bandana field to only accept alphabets
  document.addEventListener("DOMContentLoaded", function () {
    let inputElement = document.querySelector(".bandannaInput");

    inputElement?.addEventListener("input", function (event) {
        let enteredValue = event.target.value;

        // Restrict input to alphabets only and capitalize them
        event.target.value = enteredValue.replace(/[^a-zA-Z]/g, '').toUpperCase();

        // Check if input exceeds 18 characters
        const maxLength = 18;
        if (event.target.value.length > maxLength) {
            alert(`Input cannot exceed ${maxLength} characters.`);
            event.target.value = event.target.value.substring(0, maxLength); // Trim excess characters
        }
    });
  });


    function checkDisplayTrue(obj) {
    for (let key in obj) {
       if (obj[key].display){
        return true

       }
    }
    return false;
  }

   // Function to open the popup
   function openPopup() {
    document.getElementById("popup").style.display = "block";
    generateSizeChart(); // Generate size chart tabs and tables
  }

  // Function to close the popup
  function closePopup() {
    document.getElementById("popup").style.display = "none";
  }

  // Function to dynamically generate tabs and tables
  function generateSizeChart() {
    var sizeChartTitle = document.getElementById("sizeChartTitle");
    var sizeChartTabs = document.getElementById("sizeChartTabs");
    var sizeTables = document.getElementById("sizeTables");

    sizeChartTitle.innerHTML = ''; 
    sizeChartTabs.innerHTML = ''; 
    sizeTables.innerHTML = '';

    sizeChartTitle.textContent=sizeData.variant_options[0].size_chart.title;


    // Loop through each country in sizeData
    for (var country in sizeData.variant_options[0].size_chart.data) {
        // Generate tab for each country
        var tab = document.createElement('button');
        tab.className = 'tablinks';
        tab.style.fontWeight ='bold'
        tab.textContent = country;
        tab.onclick = function(countryName) {
            return function() {
                openTab(countryName);
            };
        }(country);
        sizeChartTabs.appendChild(tab);

        // Generate table for size details of each country
        var table = document.createElement('table');
        table.className = 'tabcontent';
        table.id = country;
        var countryData = sizeData.variant_options[0].size_chart.data[country];

        // Create table header row
        var headerRow = table.insertRow();
        var headerCell1 = headerRow.insertCell(0);
        var headerCell2 = headerRow.insertCell(1);
        var headerCell3 = headerRow.insertCell(2);
        headerCell1.textContent = sizeData.variant_options[0].size_chart.key_name;
        headerCell2.textContent = "Female";
        headerCell3.textContent = "Male";
        headerCell1.style.fontWeight="bold";
        headerCell2.style.fontWeight="bold";
        headerCell3.style.fontWeight="bold";
        headerCell1.style.width="231px";
        headerCell2.style.width="231px";
        headerCell3.style.width="231px";

        for (var size in countryData) {
            var row = table.insertRow();
            var cell1 = row.insertCell(0);
            var cell2 = row.insertCell(1);
            var cell3 = row.insertCell(2);
            cell1.textContent = size;
            cell2.textContent = countryData[size].Female;
            cell3.textContent = countryData[size].Male;
           
        }
        sizeTables.appendChild(table);
    }

    // Show the first tab by default
    var firstTab = sizeChartTabs.querySelector('.tablinks');
    
    if (firstTab) {
        firstTab.classList.add('active');
        openTab1(firstTab.textContent); // Show corresponding table for the first tab
    }
  }
  var tabButtons = document.querySelectorAll('.tablinks');
  tabButtons.forEach(function(button) {
    button.style.width = (100 / tabButtons.length) + '%'; // Distribute width equally
  });

  // Function to switch between tabs
   
  function openTab1(tabName) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }
    
    document.getElementById(tabName).style.display = "flex";
    document.getElementById(tabName).style.justifyContent = "center";
    event.currentTarget.classList.add("active");
  }
  // Function to switch between tabs
  function openTab(tabName) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }
    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].classList.remove("active");
    }
    document.getElementById(tabName).style.display = "flex";
    document.getElementById(tabName).style.justifyContent = "center";
    event.currentTarget.classList.add("active");
  }

  function isSizeChartAvailable(data) {
    if (data.size === true) {
      return true;
    } else {
      return false;
    }
  }










  var x, i, j, l, ll, selElmnt, a, b, c;
  /*look for any elements with the class "custom-select":*/
  x = document.getElementsByClassName("custom-select");
  l = x.length;
  for (i = 0; i < l; i++) {
  selElmnt = x[i].getElementsByTagName("select")[0];
  ll = selElmnt.length;
  /*for each element, create a new DIV that will act as the selected item:*/
  a = document.createElement("DIV");
  a.setAttribute("class", "select-selected");
  a.setAttribute("tabindex", "0");
  a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML;
  x[i].appendChild(a);
  /*for each element, create a new DIV that will contain the option list:*/
  b = document.createElement("DIV");
  b.setAttribute("class", "select-items select-hide");
  for (j = 1; j < ll; j++) {
    /*for each option in the original select element,
    create a new DIV that will act as an option item:*/
    c = document.createElement("DIV");
    c.setAttribute("tabindex", "0");
    c.innerHTML = selElmnt.options[j].innerHTML;
    c.addEventListener("click", function(e) {
        /*when an item is clicked, update the original select box,
        and the selected item:*/
        var y, i, k, s, h, sl, yl;
        s = this.parentNode.parentNode.getElementsByTagName("select")[0];
        sl = s.length;
        h = this.parentNode.previousSibling;
        for (i = 0; i < sl; i++) {
          if (s.options[i].innerHTML == this.innerHTML) {
            s.selectedIndex = i;
            h.innerHTML = this.innerHTML;
            y = this.parentNode.getElementsByClassName("same-as-selected");
            yl = y.length;
            for (k = 0; k < yl; k++) {
              y[k].removeAttribute("class");
            }
            this.setAttribute("class", "same-as-selected");
            break;
          }
        }
        h.click();
    });
    c.addEventListener("keydown", function(e) {
      if (e.key === "Enter") {
        e.preventDefault();
        this.click(); 
      } else if (e.key === "ArrowDown") {
        e.preventDefault();
        if (this.nextSibling) {
          this.nextSibling.focus();
        }
      } else if (e.key === "ArrowUp") {
        e.preventDefault();
        if (this.previousSibling) {
          this.previousSibling.focus();
        }
      }
    });
    
    b.appendChild(c);
  }
  x[i].appendChild(b);
  a.addEventListener("click", function(e) {
      /*when the select box is clicked, close any other select boxes,
      and open/close the current select box:*/
      e.stopPropagation();
      closeAllSelect(this);
      this.nextSibling.classList.toggle("select-hide");
      this.classList.toggle("select-arrow-active");
    });
    a.addEventListener("keydown", function(e) {
      if (e.key === "Enter" ) {
        e.preventDefault(); 
        this.click(); 
      }
    });  
  }
  function closeAllSelect(elmnt) {
  /*a function that will close all select boxes in the document,
  except the current select box:*/
  var x, y, i, xl, yl, arrNo = [];
  x = document.getElementsByClassName("select-items");
  y = document.getElementsByClassName("select-selected");
  xl = x.length;
  yl = y.length;
  for (i = 0; i < yl; i++) {
    if (elmnt == y[i]) {
      arrNo.push(i)
    } else {
      y[i].classList.remove("select-arrow-active");
    }
  }
  for (i = 0; i < xl; i++) {
    if (arrNo.indexOf(i)) {
      x[i].classList.add("select-hide");
    }
  }
  }

  document.addEventListener("click", closeAllSelect);
</script>


<script>
  function closeFreeProductDiv(event) {
    document.getElementById("free_product_div").style.visibility = "hidden";
    document.querySelector(".freeProductSection").style.display="none";
    window.location.href = "{{ shop.url }}/cart";
  }

  const cartButton = document.getElementById("add_to_cart_free_product");
  cartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
  cartButton.setAttribute("disabled", "true");

  async function dragNdropFreeProduct(event, containerId, number){
      var files = event.target.files;

      if (files.length > 1) {
        alert("Please select only one image file.");
        return;
      }

      var file = files[0];
      var fileName = URL.createObjectURL(files[0]);

      if (!file.type.startsWith("image/")) {
        alert("Please select only image files.");
        return;
      }

      if (file.type !== "image/jpg" && file.type !== "image/png" && file.type !== "image/bmp" && file.type !== "image/jpeg" && file.type !== "image/jpeg") {
        alert(`This type of image is not allowed. Please use only jpg, png, or bmp.`);
        return;
      }
      if (files[0].size > 15 * 1024 * 1024) {
        // Convert MB to bytes
        alert("Please select images smaller than 15MB.");
        return;
      }

      let uploadedImageName;
      var previewImg = document.createElement("img");
      previewImg.src="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Spinner-1s-200px.gif";


      const imageContainer=document.querySelector(".selected-image-container-free");
      document.querySelector(".close-button-uploader-free-product").style.display="block";
      document.querySelector(".free-image-uploader").style.padding="20px";

      previewImg.setAttribute("alt", "Image Free Product Uploader");
      previewImg.setAttribute("id", "previewImageFreeProduct");
      imageContainer.appendChild(previewImg);

      document.querySelector(".uploadOuterFree").style.display = "none";

      let hiddenInputFree=document.getElementById("hiddenInputFree");
      // const cartButton = document.getElementById("add_to_cart_free_product");


      const formData = new FormData();
      formData.append('image', files[0]); 

      try {
        const response = await fetch(`${omsDetails[0].url}/image_uploader/print_image`, {
          method: "POST",
          body: formData,
          headers: {
            'api-token':omsDetails[0].api_token
          },
        })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          previewImg.src = data.imageUrl;

          hiddenInputFree.value=data.imageUrl;

          cartButton.style.cssText = "cursor:pointer;opacity:1;pointer-events:auto";
          cartButton.removeAttribute("disabled");

          const containerWidth = 250;
          const containerHeight = 250;
          const imageAspectRatio = previewImg.naturalWidth / previewImg.naturalHeight;

          let imageWidth, imageHeight;
          if (imageAspectRatio >= 1) {
            // Landscape or square image
            imageWidth = containerWidth;
            imageHeight = containerWidth / imageAspectRatio;
          } else {
            // Portrait image
            imageHeight = containerHeight;
            imageWidth = containerHeight * imageAspectRatio;
          }

          // Set image dimensions and quality
          previewImg.style.width = `${imageWidth}px`;
          previewImg.style.height = `${imageHeight}px`;
          previewImg.style.objectFit = "contain"; // Maintain aspect ratio and fill container

          const imageUrl = data.imageUrl;
          const parts = imageUrl.split('/');
          uploadedImageName = parts[parts?.length - 1];
        })
        .catch((error) => {
          console.error("Error uploading new image:", error);
        });           
      } catch (error) {
          console.log('Error:', error);
      }

      document.querySelector(".close-button-uploader-free-product").addEventListener("click",function(event){
        event.preventDefault();
        if (uploadedImageName) {
          imageContainer.innerHTML="";
          document.querySelector(".uploadOuterFree").style.display = "block";
          document.querySelector(".close-button-uploader-free-product").style.display="none";
          document.getElementById("hiddenInputFree").value="";
          document.querySelector(".free-image-uploader").style.padding="0px";
          const cartButton = document.getElementById("add_to_cart_free_product");
          cartButton.style.cssText="cursor:not-allowed;opacity:0.5;pointer-events:none";
          cartButton.setAttribute("disabled", "true");
        }
      });
    }

    

  function extractVariantAndAdd(){
    let variantsString=document.querySelector(".freeProductVariants").value;
    let variantsData=JSON.parse(variantsString);
    // Select all input elements with names starting with 'properties[_option_'
    let nativeKeys = document.querySelector(".freeProductKeys").value;
    let nativeKeysData=JSON.parse(nativeKeys);
    let concatenatedValue="";
    nativeKeysData.forEach((key,index)=>{
      let inputValue=document.querySelector(`.${key}HiddenInput`).value;
      concatenatedValue+=inputValue+" / ";
    });
    if (concatenatedValue.endsWith(" / ")) {
      concatenatedValue = concatenatedValue.slice(0, -3);
    }
    let foundVariant = variantsData.find(variant => variant.title === concatenatedValue);
    if(foundVariant){
      document.querySelector("#freeFormId").value=foundVariant.id;
    }else{
      document.querySelector("#freeFormId").value=variantsData[0].id;
    }

  }

  function selectBackground(name,value,key){
    let hiddenInput = document.querySelector(`.${name}HiddenInput`);
    let selectedSwatches = document.getElementById(`${name}-selected-swatches`);
    hiddenInput.value = value;
    selectedSwatches.textContent = value;
  }

  function getSelectedValue(name,selectElement,key) {
    let selectedValue = selectElement.value;
    let validSelectedValue=selectedValue != 'not-selected' ? selectedValue : ''
    document.querySelector(`.${name}HiddenInput`).value = validSelectedValue;
  }

  let nativeOption="";

  function handleNativeAction(name, value, element, type,shape) {

    let withHyphenName=name;
    if(withHyphenName.includes(' ')){
    withHyphenName=name.replace(/ /g,"_");
    }

    const hiddenInput = document.querySelector(`._option_${withHyphenName}HiddenInput`);

    if (type === "dropdown") {
      if (nativeOption) {
        const findOptions = nativeOption.find(
          (item) => item.conditional_subOptions && item.heading == name
        );
        if (findOptions) {
          let subOption = findOptions.options.find(
            (item) => item.name == value.value
          );
          if (subOption && subOption.sub_options) {
            const className = `.${findOptions.conditional_subOptions}`;
            const selectDiv = document.querySelector(className);
            if (selectDiv) {
              selectDiv.style.display = "flex";
            }
          } else {
            const className = `.${findOptions.conditional_subOptions}`;
            const selectDiv = document.querySelector(className);
            if (selectDiv) {
              selectDiv.style.display = "none";
            }
          }
        }
        const findNumberOfOptions = nativeOption.find(
          (item) => item.conditional_NumberOptions && item.heading == name
        );
        if(findNumberOfOptions){
          const conditionalContainerName=findNumberOfOptions.conditional_NumberOptions;
        }
        value.style.color = "black";
        hiddenInput.value = value.value;
      }
    
    } else if (type === "button") {
      const buttons = document.querySelectorAll(`.${withHyphenName}${shape}Button`);
      buttons.forEach(function (btn) {
        btn.style.border = "1px solid gray";
      });
      hiddenInput.value = value.textContent;
      value.style.border = "2px solid #5461c8";
    } else if (type === "swatches") {
      hiddenInput.value = value;
      const swatchesElement = document.getElementById(
        `${name}-selected-swatches`
      );
      if (swatchesElement) {
        swatchesElement.textContent = value;
      }

    }
  }

  function changeBorderColor(name,button, className,key,shape) {
    var buttons = document.querySelectorAll(className);
    buttons.forEach(function(btn) {
      btn.style.border = '1px solid gray';
    });
    if(button.textContent!=="No"){
      document.querySelector(`.${name}HiddenInput`).value=button.textContent;
    }else{
      document.querySelector(`.${name}HiddenInput`).value="";
    }
    button.style.border = '2px solid #5461c8';
  }

  document.getElementById('add_to_cart_free_product').addEventListener('click', function(event){
    event.preventDefault();

    extractVariantAndAdd();

    document.querySelector('.freeProductForm').submit();
  });

  const phoneInputField = document.querySelector("#phone");
   
  const emailRegex = /^[^\s@]+@(?!.*\.\.)(?!.*\.@)[^\s@.]+(\.[^\s@.]+)*\.[a-zA-Z]{2,}$/;

   // for keyboard events
   const petImages = Array.from(document.querySelectorAll('.pet-image'));

  nextButtonStep1.addEventListener("click", (event) => {
    event.preventDefault();
    const isEmailValid = emailRegex.test(emailInput.value.trim());
    const phoneNumber = phoneInputField.value.trim();

    if (isEmailValid) {
      if (phoneNumber === "") {
        step2.classList.remove('disabled');
        step2.scrollIntoView({ behavior: 'smooth' });
      } else {
        if (validateUSPhoneNumber(phoneNumber)) {
          step2.classList.remove('disabled');
          step2.scrollIntoView({ behavior: 'smooth' });
          petImages[0].focus();
        } else {
          let errorMessage = "Invalid phone number.";

          alert(errorMessage);
        }
      }
    } else {
      alert("Enter a valid email first");
      emailInput.scrollIntoView({ behavior: 'smooth' });
      emailInput.focus();
    }
  });
</script>
<script>
  function selectPlushFrameOptions(label, class_name, event, value) {
    removeSelectedClassFromChildren(event);

    event.classList.add("selected");
    event.style.border = "5px solid rgb(84, 97, 200)";
    var getHiddenFields = document.getElementsByClassName(class_name);
    if (event.className.includes("frame")){
      document.getElementById("formId").value = value;
      getHiddenFields[0].value = label;
    } else {
      getHiddenFields[0].value = label;
    }
  }

  function removeSelectedClassFromChildren(event) {
    const className = event.className;
    const selectedOption = document.querySelectorAll(`.${className}.selected`);

    if (selectedOption.length > 0) {
      selectedOption.forEach(child => {
        child.classList.remove("selected");
        child.style.border = '';
      });
    }
  }
</script>
<script>
  // Function to validate US phone number
  function validateUSPhoneNumber(phoneNumber) {
      // Regular expression to match US phone number formats:
      // - ******-456-7890
      // - **************
      // - (*************
      // - ************
      // - ************
      // - 1234567890
      const usPhoneRegex = /^(?:\+?1\s?)?(\(?\d{3}\)?[\s.-]?)?\d{3}[\s.-]?\d{4}$/;
      
      // Test if the phone number matches the pattern
      return usPhoneRegex.test(phoneNumber);
  }

  // Function to add +1 if it's not present
  function addCountryCodeIfMissing(phoneNumber) {
    // Trim any leading/trailing spaces
    phoneNumber = phoneNumber ? phoneNumber.trim() : ''; // Check if phoneNumber has a value

    // If phoneNumber is empty, return it as is
    if (!phoneNumber) {
        return phoneNumber; // Return empty value
    }

    // If the phone number starts with '1' and not '+1', replace it with '+1'
    if (phoneNumber.startsWith('1') && !phoneNumber.startsWith('+1')) {
        return '+1 ' + phoneNumber.substring(1).trim(); // Remove the leading '1' and add '+1'
    }
    
    // If the phone number doesn't start with +1 or 1, add +1
    if (!phoneNumber.startsWith('+1') && !phoneNumber.startsWith('1')) {
        return '+1 ' + phoneNumber;
    }

    return phoneNumber;
  }

  // Event listener for the input field
  document.getElementById('phone').addEventListener('blur', function() {
    let phoneInput = document.getElementById('phone').value;
    const errorMessage = document.getElementById('error-message');
    
    // Add +1 if missing
    phoneInput = addCountryCodeIfMissing(phoneInput);
    document.getElementById('phone').value = phoneInput; // Update the input field with country code

    // Validate the phone number only if it's not empty
    if (phoneInput.trim() === '') {
        errorMessage.style.display = 'none'; // Hide error message if input is empty
    } else if (validateUSPhoneNumber(phoneInput)) {
        errorMessage.style.display = 'none'; // Hide error message if valid
    } else {
        errorMessage.style.display = 'block'; // Show error message if invalid
    }
  });


  document.getElementById("petname").addEventListener("input", () => {
      const maxLength = 18;

      // Convert input to uppercase
      petNameInput.value = petNameInput.value.toUpperCase();

      // Check character length and show alert if exceeded
      if (petNameInput.value.length > maxLength) {
          alert(`Pet name cannot exceed ${maxLength} characters.`);
          petNameInput.value = petNameInput.value.substring(0, maxLength); // Trim excess characters
      }
  });


  // for keyboard events

  function focusNextBtn_1(event){
    if (phoneInputField && window.getComputedStyle(phoneInputField).display !== "none"){
      if(event.key === 'Enter'){
        event.preventDefault();
        nextButtonStep1.click();
      }
    }
  }

  function focusPhoneOnEnter(event) {
    if (event.key === "Enter") {
      event.preventDefault(); 
      if (phoneInputField && window.getComputedStyle(phoneInputField).display !== "none") {
        phoneInputField.focus();
      }
      else if (phoneInputField && window.getComputedStyle(phoneInputField).display == "none"){
        nextButtonStep1.click();
      }
    }
  }
  


  function handlePetImageKeydown(event) {
    const currentIndex = petImages.indexOf(event.currentTarget);
    const divStep2=document.querySelector('.step-2');

    if (event.key === "Enter" ) {
      event.preventDefault();
      petImages[currentIndex].click();
      petNameInput.focus();
    }
    petImages[currentIndex].addEventListener('click', function () {
      divStep2.scrollIntoView({ behavior: 'smooth' });
    });

    if (event.key === "ArrowRight" && currentIndex < petImages.length - 1) {
      petImages[currentIndex + 1].focus();
    }

    if (event.key === "ArrowLeft" && currentIndex > 0) {
      petImages[currentIndex - 1].focus();
    }
  }

  function goToBreedSelect(event){
    if(event.key === 'Enter'){
       breedSelect.focus();
    }
  }

  function goToPetAge(event){
    const petAge=document.querySelector("#petAge");
    if(event.key === 'Enter'){
      event.preventDefault();
      if(selectedBreedValue==='Other/Mixed'){
         mixedBreed.scrollIntoView({behavior:'smooth'});
         mixedBreed.focus();
      }else{
        petAge.focus();
        petAge.scrollIntoView({behavior:'smooth'});
      }
    }
  
  }

  function MixedBreedtoPetAge(event){
    if(event.key === 'Enter'){
     if(selectedBreedValue==='Other/Mixed'){
        petAge.focus();
        petAge.scrollIntoView({behavior:'smooth'});
      }
    }
  }

  function clickImageINput(event){
    const fileInputFunction=document.querySelector(".imageUploader");
    if(event.key === 'Enter'){
      fileInputFunction.click();
    }
  }

  function eyeColorKeydown(event) {
    const checkbox = document.getElementById("different-eye-color");
    let containers;
  
    if (checkbox.checked) {
      containers = document.querySelectorAll('#first-palette .color, #second-palette .color');
    } else {
      containers = document.querySelectorAll('#first-palette .color');
    }
  
    const eyeColors = Array.from(containers);
    const currentIndex = eyeColors.indexOf(event.currentTarget);
  
    if (event.key === "Enter") {
      event.preventDefault();
      eyeColors[currentIndex].click();
    }
  
    if (event.key === "ArrowRight" && currentIndex < eyeColors.length - 1) {
      eyeColors[currentIndex + 1].focus();
    }
  
    if (event.key === "ArrowLeft" && currentIndex > 0) {
      eyeColors[currentIndex - 1].focus();
    }
  }



  function handlePetPositionKeydown(event){
    const bodyPosition = Array.from(document.querySelectorAll('.final-product-positions img'));
    const currentIndex = bodyPosition.indexOf(event.currentTarget);

    if (event.key === "Enter") {
      event.preventDefault();
      bodyPosition[currentIndex].click(); 
       
    }

  
  }
  
  function handlePetEarKeyDown(event){
    const EarPosition = Array.from(document.querySelectorAll('.ear-label img'));
    const currentIndex = EarPosition.indexOf(event.currentTarget);

    if (event.key === "Enter") {
      event.preventDefault();
      EarPosition[currentIndex].click(); 
       
    }
  
  }

  function handleQuestionImages(event) {
    if (event.key === "Delete") {
      event.preventDefault();
  
      const img = event.currentTarget;
      const imageContainer = img.closest('.image-container');
      const deleteButton = imageContainer?.querySelector('.delete-button');
      const Radio_DelBtn = imageContainer?.querySelector('.delete-button-with-radio');
  
      if (deleteButton) {
        deleteButton.click();
      }

      if (Radio_DelBtn) {
        Radio_DelBtn.click();
      }

    }

    if (event.key === "Enter") {
      event.preventDefault();

      const image = event.currentTarget;
      const label = image.closest("label");
  
      if (label) {
        const radioInput = label.querySelector('input[type="radio"]');
        if (radioInput) {
          radioInput.click(); 
        }
      }
    }
  }

  function handleCharacteristicsImage(event){
    if (event.key === "Delete") {
      event.preventDefault();
      const img = event.currentTarget;
      const container = img.closest('.selected-image-container');
      const deleteBtn = container.querySelector('.delete-button-unique');
      if (deleteBtn) {
        deleteBtn.click();
      }
    }
  }

  function handleFrameKeyDown(event){
    const portrait = Array.from(document.querySelectorAll('.frame-image-container img'));
    const currentIndex = portrait.indexOf(event.currentTarget);
    if (event.key === "Enter" ) {
      event.preventDefault();
      portrait[currentIndex].click();
    }
  }

  function handleFrame_BGkeydown(event){
    const background =  Array.from(document.querySelectorAll('.background-options-container img'));
    const currentIndex =  background.indexOf(event.currentTarget);

     if (event.key === "Enter" ) {
      event.preventDefault();
       background[currentIndex].click();
    }

  }

  let isPopupOpen = false;
  function handleSizeChartKeyDown(event){
    if(event.key === "Enter" ){
      event.preventDefault();
       if (isPopupOpen) {
         closePopup();
         isPopupOpen = false;
       } else {
          openPopup();
          isPopupOpen = true;
       }
    }
  }

</script>