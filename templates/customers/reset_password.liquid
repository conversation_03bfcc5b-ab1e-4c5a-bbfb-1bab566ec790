{% comment %}
  Main view that allows customers to reset their password for their account.

  - https://help.shopify.com/en/themes/development/templates/customers-reset-password
{% endcomment %}

<section class="section section__wrapper reset-password">
  <header class="container is-justify-center">
    <div class="one-half column medium-down--one-whole">
      <h1 class="title">{{ 'customer.reset_password.title' | t }}</h1>
      <div class="heading-divider heading-divider--short"></div>
    </div>
  </header>

  <div class="container is-justify-center">
    <div class="one-half column medium-down--one-whole">
      {% form 'reset_customer_password' %}
        <p class="reset-password__message">
          <em>{{ 'customer.reset_password.subtext' | t: email: email }}</em>
        </p>

        {{ form.errors | default_errors }}

        <div id="password" class="field">
          <label class="large" for="password">{{ 'customer.reset_password.password' | t }}</label>
          <div class="control">
            <input type="password" value="" name="customer[password]" id="customer_password" class="input" size="16" />
          </div>
        </div>

        <div id="password_confirm" class="field">
          <label class="large" for="password_confirmation">{{ 'customer.reset_password.password_confirm' | t }}</label>
          <div class="control">
            <input type="password" value="" name="customer[password_confirmation]" id="customer_password_confirmation" class="input" size="16" />
          </div>
        </div>

        <input class="button" type="submit" value="{{ 'customer.reset_password.submit' | t }}" />
      {% endform %}
    </div>
  </div>
</section>
