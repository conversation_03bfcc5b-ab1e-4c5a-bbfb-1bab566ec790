{% comment %}
  Main view that shows the form for activating a customer account.

  - https://help.shopify.com/en/themes/development/templates/customers-activate-account
{% endcomment %}

<section class="section has-padding-bottom has-padding-top">
  <header class="container  is-justify-center">
    <div class="one-half medium-down--one-whole column">
      {%- capture title -%}{{ 'customer.activate_account.title' | t }}{%- endcapture -%}
      {% render 'heading',
              title: title,
              heading_tag: 'h1',
              context: 'activate-account',
              text_alignment: 'left'
      %}
    </div>
  </header>

  <div class="container is-justify-center">
    <div class="one-half column medium-down--one-whole">
      <p><em>{{ 'customer.activate_account.subtext' | t }}</em></p>

      {% form 'activate_customer_password' %}
        {{ form.errors | default_errors }}

        <div id="password" class="field">
          <label for="password">{{ 'customer.activate_account.password' | t }}</label>
          <div class="control">
            <input type="password" value="" name="customer[password]" id="customer_password" class="input" size="16" />
          </div>
        </div>

        <div id="password_confirm" class="field">
          <label for="password_confirmation">{{ 'customer.activate_account.password_confirm' | t }}</label>
          <div class="control">
            <input type="password" value="" name="customer[password_confirmation]" id="customer_password_confirmation" class="input" size="16" />
          </div>
        </div>

        <input class="button" type="submit" value="{{ 'customer.activate_account.submit' | t }}" />
        <input type="submit" class="cancel button" name="decline" id="customer_decline" value="{{ 'customer.activate_account.cancel' | t }}" />
      {% endform %}
    </div>
  </div>
</section>
