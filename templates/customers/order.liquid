{% comment %}
  Main view that displays the details of a visitor's past order.

  - https://help.shopify.com/en/themes/development/templates/customers-order
{% endcomment %}

{% assign id = section.id %}
{% comment %}Layout{% endcomment %}
{% assign width = 'standard' %}
{% assign padding_top = 40 %}
{% assign padding_bottom = 40 %}
{% comment %}Advanced{% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% style %}
  .section__wrapper {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
  }

  {%
    render 'css-loop',
    css: section_css,
    id: id
  %}

  {%
    render 'css-loop',
    css: custom_css,
    id: id
  %}
{% endstyle %}

<section
  class="
    section
    section__wrapper
    is-width-{{ width }}
    {{ css_class }}
  "
>
  <header class="container">
    {%- capture title -%}{{ 'customer.order.title' | t: name: order.name }} - {{ 'customer.order.date' | t: date: order.created_at | date: "%B %d, %Y %I:%M%p" }}{%- endcapture -%}
    {%
      render 'heading',
      title: title,
      heading_tag: 'h1',
      context: 'account-details',
      text_alignment: 'left'
    %}
  </header>

  <div class="container has-padding-top">
    <aside
      class="
        one-fourth
        column
        account-sidebar
      "
    >
      <a href="{{ routes.account_url }}">{{ 'customer.account.return' | t }}</a>
    </aside>

    <main
      class="
        container
        three-fourths
        column
      "
    >
      <div class="one-whole column">
        {% if order.cancelled %}
          <div class="errors">
            {% assign cancelled_at = order.cancelled_at | date: "%B %d, %Y %I:%M%p" %}
            <p>{{ 'customer.order.cancelled' | t: date: cancelled_at }}</p>
            <p>{{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason }}</p>
          </div>
        {% endif %}

        <table
          class="table is-bordered"
          id="order-details"
          width="100%"
        >
          <thead>
            <tr>
              <th>{{ 'customer.order.product' | t }}</th>
              <th>{{ 'customer.order.sku' | t }}</th>
              <th>{{ 'customer.order.price' | t }}</th>
              <th>{{ 'customer.order.quantity' | t }}</th>
              <th>{{ 'customer.order.total' | t }}</th>
            </tr>
          </thead>

          <tbody>
            {% for line_item in order.line_items %}
            <tr id="{{ line_item.id }}">
              <td>
                {{ line_item.title | link_to: line_item.product.url }}
                {% if line_item.fulfillment %}
                  <div>
                    {% assign created_at = line_item.fulfillment.created_at | date: "%b %d" %}
                    {{ 'customer.order.fulfilled_at' | t: date: created_at }}
                    {% if line_item.fulfillment.tracking_number %}
                      <a href="{{ line_item.fulfillment.tracking_url }}">{{ line_item.fulfillment.tracking_company }} #{{ line_item.fulfillment.tracking_number}}</a>
                    {% endif %}
                  </div>
                {% endif %}
              </td>
              <td>{{ line_item.sku }}</td>
              <td>
                <span>
                  {%
                    render 'price-element',
                    price: line_item.price
                  %}

                  {%
                    render 'unit-price',
                    item: line_item,
                    class: 'order__unit-price'
                  %}
                </span>
              </td>
              <td>{{ line_item.quantity }}</td>
              <td>
                <span>
                  {% assign line_quantity = line_item.quantity | times: line_item.price %}
                  {%
                    render 'price-element',
                    price: line_quantity
                  %}
                </span>
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="4">{{ 'customer.order.subtotal' | t }}</td>
              <td>
                {%
                  render 'price-element',
                  price: order.subtotal_price
                %}
              </td>
            </tr>

            {% for discount in order.discounts %}
              <tr class="order-details__summary">
                <td colspan="4">{{ discount.code }} {{ 'customer.order.discount' | t }}</td>
                <td>
                  <span>
                    {%
                      render 'price-element',
                      price: discount.savings
                    %}
                  </span>
                </td>
              </tr>
            {% endfor %}

            {% for shipping_method in order.shipping_methods %}
            <tr>
              <td colspan="4">{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }})</td>
              <td>
                <span>
                  {%
                    render 'price-element',
                    price: shipping_method.price
                  %}
                </span>
              </td>
            </tr>
            {% endfor %}

            {% for tax_line in order.tax_lines %}
              <tr>
                <td colspan="4">{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%)</td>
                <td>
                  <span>
                    {%
                      render 'price-element',
                      price: tax_line.price
                    %}
                  </span>
                </td>
              </tr>
            {% endfor %}

            <tr>
              <td colspan="4"><strong>{{ 'customer.order.total' | t }}</strong></td>
              <td>
                <strong>
                  <span>
                    {%
                      render 'price-element',
                      price: order.total_price
                    %}
                    {{ order.currency }}
                  </span>
                </strong>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
      <div
        class="
          {% if settings.display_special_instructions %}
            one-third
          {% else %}
            one-half
          {% endif %}
          medium-down--one-whole
          column
          has-padding-top content
        "
      >
        <h4 class="subtitle">{{ 'customer.order.billing_address' | t }}</h4>

        <p><strong>{{ 'customer.order.payment_status' | t }}:</strong> {{ order.financial_status_label }}</p>

        <p>
          <strong>{{ order.billing_address.name }}</strong>
          {% if order.billing_address.company != '' %}
            {{ order.billing_address.company }}<br>
          {% endif %}
          {{ order.billing_address.street }}<br>
          {{ order.billing_address.city }},
          {% if order.billing_address.province != '' %}
            {{ order.billing_address.province }}<br>
          {% endif %}
          {{ order.billing_address.zip | upcase }}<br>
          {{ order.billing_address.country }}<br>
          {{ order.billing_address.phone }}
        </p>
      </div>

      <div
        class="
          {% if settings.display_special_instructions %}
            one-third
          {% else %}
            one-half
          {% endif %}
          medium-down--one-whole
          column
          has-padding-top content
        "
      >
        <h4 class="subtitle">{{ 'customer.order.shipping_address' | t }}</h4>

        <p><strong>{{ 'customer.order.fulfillment_status' | t }}:</strong> {{ order.fulfillment_status_label }}</p>

        <p>
          <strong>{{ order.shipping_address.name }}</strong>
          {% if order.shipping_address.company != '' %}
            {{ order.shipping_address.company }}<br>
          {% endif %}
          {{ order.shipping_address.street }}<br>
          {{ order.shipping_address.city }},
          {% if order.shipping_address.province != '' %}
            {{ order.shipping_address.province }}<br>
          {% endif %}
          {{ order.shipping_address.zip | upcase }}<br>
          {{ order.shipping_address.country }}<br>
          {{ order.shipping_address.phone }}
        </p>
      </div>

      {% if settings.display_special_instructions %}
        <div
          class="
            one-third
            medium-down--one-whole
            column
            has-padding-top content
          "
        >
          <h4 class="subtitle">
            {{ 'customer.order.note' | t }}
          </h4>

          {% if order.note != blank %}
            {{ order.note }}
          {% else %}
            {{ 'customer.order.note_placeholder' | t }}
          {% endif %}
        </div>
      {% endif %}
    </main>
  </div>
</section>
