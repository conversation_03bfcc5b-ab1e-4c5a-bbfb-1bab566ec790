{"sections": {"image_with_text_overlay": {"type": "index__image-with-text-overlay", "settings": {"link": "", "pretext": "<p>Preheading</p>", "title": "Image with Text Overlay", "subtitle": "<p>Additional info in the subheading</p>", "pretext_color": "#000000", "heading_color": "#000000", "subheading_color": "#000000", "text_alignment": "center", "text_horizontal_position": "center", "text_vertical_position": "middle", "text_width": 40, "background_color": "#FFFFFF", "background_opacity": 77, "border_color": "#FFFFFF", "border_width": 0, "button_1": "Button 1", "button_1_link": "", "button_1_style": "button--secondary", "button_2": "Button 2", "button_2_link": "", "button_2_style": "button--secondary", "width": "wide", "padding_top": 0, "padding_bottom": 0, "padding_left": 0, "padding_right": 0, "animation": "none", "mobile_text_below_image": true, "pretext_color_mobile": "rgba(0,0,0,0)", "heading_color_mobile": "rgba(0,0,0,0)", "subheading_color_mobile": "rgba(0,0,0,0)", "css_class": "", "custom_css": ""}}, "main": {"type": "page-content__main", "disabled": true, "settings": {"show_heading": true, "heading_alignment": "center", "width": "standard", "padding_top": 40, "padding_bottom": 40, "css_class": "", "custom_css": ""}}, "collection": {"type": "index__featured-collection", "settings": {"title": "Featured collection", "collection": "", "products_per": 5, "products_limit": 10, "align_height": false, "collection_height": 200, "collection_style": "grid", "width": "standard", "show_gutter": true, "padding_top": 20, "padding_bottom": 20, "animation": "none", "mobile_products_per_row": "1", "css_class": "", "custom_css": ""}}, "blog": {"type": "index__blog-posts", "settings": {"title": "Recent blog posts", "blog_widget_select": "", "home_page_articles": 3, "blog_show_excerpt": true, "blog_show_tags": true, "blog_author": true, "blog_date": true, "read_time": true, "blog_comment_count": false, "show-border": true, "button_label": "Read more", "button_type": "button--primary", "width": "standard", "padding_top": 20, "padding_bottom": 0, "animation": "none", "css_class": "", "custom_css": ""}}, "richtext": {"type": "index__rich-text", "settings": {"title": "Rich text", "heading_font": "open_sans_n4", "heading_alignment": "center", "heading_size": 30, "text": "<p>Use this section to create some callout text on your page, or add more details about your shop, services, promotions, etc. If you have a large amount&nbsp;of text, you can opt to have it display in <strong>two columns</strong> instead so that it's much easier to read.</p>\n<p></p>\n<p>This section also includes&nbsp;the option to add a button&nbsp;in primary or secondary style, and also control the colors, including the option for a gradient background. Since this is rich text, you can also <em>format it as you wish</em> and&nbsp;<a href=\"/collections/all\" title=\"All Products\">include links</a>.</p>", "text_font": "open_sans_n4", "text_alignment": "left", "text_size": 14, "columns": 2, "button_label": "", "button_link": "", "button_style": "button--primary", "button_alignment": "center", "heading_color": "rgba(0,0,0,0)", "text_color": "rgba(0,0,0,0)", "background": "#EEEEEE", "gradient": "#C0C0C0", "gradient_rotation": 180, "width": "wide", "padding_top": 20, "padding_bottom": 0, "padding_left": 0, "padding_right": 0, "animation": "none", "css_class": "", "custom_css": ""}}}, "order": ["image_with_text_overlay", "main", "collection", "blog", "richtext"]}