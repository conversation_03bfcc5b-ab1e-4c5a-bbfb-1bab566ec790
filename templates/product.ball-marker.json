/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "zp-product-header-content": {
      "type": "product-header-content-zipifypages",
      "settings": {}
    },
    "breadcrumbs": {
      "type": "product__breadcrumb",
      "disabled": true,
      "settings": {
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "sidebar": {
      "type": "product__sidebar",
      "settings": {
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "main": {
      "type": "product__main",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {}
        },
        "judge_me_reviews_preview_badge_nxpdpj": {
          "type": "shopify://apps/judge-me-reviews/blocks/preview_badge/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        },
        "price": {
          "type": "price",
          "settings": {
            "display_savings": true
          }
        },
        "form": {
          "type": "form",
          "settings": {
            "show_payment_button": true,
            "show_gift_card_recipient_form": false
          }
        },
        "description": {
          "type": "description",
          "settings": {}
        },
        "share": {
          "type": "share",
          "settings": {}
        }
      },
      "block_order": [
        "title",
        "judge_me_reviews_preview_badge_nxpdpj",
        "price",
        "form",
        "description",
        "share"
      ],
      "settings": {
        "new_pet_bowl_img": "shopify://shop_images/Pet_Bowl_Image_d9be647f-5490-48a4-826f-a0a3a4d02a1d.png",
        "new_pet_bowl_text": "This product feed 20 shelter pets",
        "product_images_position": "left",
        "set_product_height": false,
        "product_height": 500,
        "video_looping": false,
        "gallery_arrows": true,
        "enable_zoom": true,
        "enable_product_lightbox": true,
        "slideshow_speed": 0,
        "slideshow_transition": "slide",
        "display_thumbnails": true,
        "thumbnail_position": "bottom-thumbnails",
        "enable_thumbnail_slider": true,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_heading_TXCMmP": {
      "type": "index__heading",
      "settings": {
        "preheading": "",
        "title": "Why Cuddle Clones?",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_image_with_text_overlay_JE37cF": {
      "type": "index__image-with-text-overlay",
      "settings": {
        "image": "shopify://shop_images/Why_CC_USP_icons_POD_desktop-_4_set_v2_48be4c99-2b62-47d8-9f2c-61116975e712.png",
        "mobile_image": "shopify://shop_images/Why_CC_USP_icons_POD_mobile_-4_set-800x1150.png",
        "link": "",
        "pretext": "",
        "title": "",
        "subtitle": "",
        "pretext_color": "#000000",
        "heading_color": "#000000",
        "subheading_color": "#000000",
        "text_alignment": "center",
        "text_horizontal_position": "center",
        "text_vertical_position": "middle",
        "text_width": 40,
        "background_color": "#ffffff",
        "background_opacity": 77,
        "border_color": "#ffffff",
        "border_width": 0,
        "button_1": "",
        "button_1_link": "",
        "button_1_style": "button--secondary",
        "button_2": "",
        "button_2_link": "",
        "button_2_style": "button--secondary",
        "width": "wide",
        "padding_top": 0,
        "padding_bottom": 0,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_below_image": true,
        "mobile_preheading": "",
        "mobile_heading": "",
        "mobile_subheading": "",
        "pretext_color_mobile": "rgba(0,0,0,0)",
        "heading_color_mobile": "rgba(0,0,0,0)",
        "subheading_color_mobile": "rgba(0,0,0,0)",
        "text_alignment_mobile": "same_as_desktop",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "css_class": "",
        "custom_css": ""
      }
    },
    "how_to_submit_pictures_QRf9eE": {
      "type": "how-to-submit-pictures",
      "settings": {
        "title": "How to Submit the Best Picture of your Pet!",
        "tab_1_image": "shopify://shop_images/Submit-Image-Three_600x_a70ae4da-47a6-4654-a7cb-d58195a6a7a9.webp",
        "tab_1_name": "One Pet Front and Center",
        "tab_1_description": "For the perfect custom ball markers, please submit clear, close-up images of your pet’s face and chest, ensuring that the photos feature only the pet you wish to replicate, to accurately capture their unique features and create one-of-a-kind ball markers.",
        "tab_2_image": "shopify://shop_images/Submit-Image-Two.png",
        "tab_2_name": "Positioning",
        "tab_2_description": "Images of your pet sitting down or standing up is ideal to center their features prominently on the custom ball markers!",
        "tab_3_image": "shopify://shop_images/Submit-Image-Three.png",
        "tab_3_name": "Lighting",
        "tab_3_description": "Ensure there is good lighting in the photos to accurately capture your pet's true colors, allowing their natural hues to shine through."
      }
    },
    "170894921511d6b216": {
      "type": "apps",
      "blocks": {
        "judge_me_reviews_review_widget_MG7dK8": {
          "type": "shopify://apps/judge-me-reviews/blocks/review_widget/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        }
      },
      "block_order": ["judge_me_reviews_review_widget_MG7dK8"],
      "settings": {
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "recommendations": {
      "type": "product__recommendations",
      "settings": {
        "show_product_recommendations": true,
        "product_recommendations_style": "grid",
        "product_recommendations_heading": "You May Also Like",
        "products_per": 4,
        "recommended_products_limit": 4,
        "align_height": false,
        "collection_height": 200,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "zp-product-footer-content": {
      "type": "product-footer-content-zipifypages",
      "settings": {}
    }
  },
  "order": [
    "zp-product-header-content",
    "breadcrumbs",
    "sidebar",
    "main",
    "index_heading_TXCMmP",
    "index_image_with_text_overlay_JE37cF",
    "how_to_submit_pictures_QRf9eE",
    "170894921511d6b216",
    "recommendations",
    "zp-product-footer-content"
  ]
}
