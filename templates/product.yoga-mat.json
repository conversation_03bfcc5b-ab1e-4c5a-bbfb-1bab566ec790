/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "zp-product-header-content": {
      "type": "product-header-content-zipifypages",
      "settings": {}
    },
    "breadcrumbs": {
      "type": "product__breadcrumb",
      "disabled": true,
      "settings": {
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "sidebar": {
      "type": "product__sidebar",
      "settings": {
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "main": {
      "type": "product__main",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {}
        },
        "judge_me_reviews_preview_badge_nxpdpj": {
          "type": "shopify://apps/judge-me-reviews/blocks/preview_badge/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        },
        "price": {
          "type": "price",
          "settings": {
            "display_savings": true
          }
        },
        "shelter_pet_8wYVCR": {
          "type": "shelter_pet",
          "settings": {}
        },
        "text_Bg6Bfn": {
          "type": "text",
          "settings": {
            "text": "<p><em>Please select all product options to see price and image example</em></p>"
          }
        },
        "form": {
          "type": "form",
          "settings": {
            "show_payment_button": true,
            "show_gift_card_recipient_form": false
          }
        },
        "description": {
          "type": "description",
          "settings": {}
        },
        "share": {
          "type": "share",
          "settings": {}
        }
      },
      "block_order": [
        "title",
        "judge_me_reviews_preview_badge_nxpdpj",
        "price",
        "shelter_pet_8wYVCR",
        "text_Bg6Bfn",
        "form",
        "description",
        "share"
      ],
      "settings": {
        "new_pet_bowl_img": "shopify://shop_images/Pet_Bowl_Image_d9be647f-5490-48a4-826f-a0a3a4d02a1d.png",
        "new_pet_bowl_text": "This product feeds shelter pets",
        "product_images_position": "left",
        "set_product_height": false,
        "product_height": 500,
        "video_looping": false,
        "gallery_arrows": true,
        "enable_zoom": true,
        "enable_product_lightbox": true,
        "slideshow_speed": 0,
        "slideshow_transition": "slide",
        "display_thumbnails": true,
        "thumbnail_position": "bottom-thumbnails",
        "enable_thumbnail_slider": true,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_heading_TXCMmP": {
      "type": "index__heading",
      "settings": {
        "preheading": "",
        "title": "Why Cuddle Clones?",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 0,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 0,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_image_with_text_overlay_N4qixp": {
      "type": "index__image-with-text-overlay",
      "settings": {
        "image": "shopify://shop_images/Why_CC_USP_icons_Yoga_Mat_desktop_-4set_v2.jpg",
        "mobile_image": "shopify://shop_images/Why_CC_USP_icons_Yoga_Mat_mobile_-_4set-v2_d5f41d26-22f3-4d34-8b0f-f5d2f6397cba.png",
        "link": "",
        "pretext": "",
        "title": "",
        "subtitle": "",
        "pretext_color": "#000000",
        "heading_color": "#000000",
        "subheading_color": "#000000",
        "text_alignment": "center",
        "text_horizontal_position": "center",
        "text_vertical_position": "middle",
        "text_width": 40,
        "background_color": "#ffffff",
        "background_opacity": 77,
        "border_color": "#ffffff",
        "border_width": 0,
        "button_1": "",
        "button_1_link": "",
        "button_1_style": "button--secondary",
        "button_2": "",
        "button_2_link": "",
        "button_2_style": "button--secondary",
        "width": "wide",
        "padding_top": 3,
        "padding_bottom": 40,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_below_image": true,
        "mobile_preheading": "",
        "mobile_heading": "",
        "mobile_subheading": "",
        "pretext_color_mobile": "rgba(0,0,0,0)",
        "heading_color_mobile": "rgba(0,0,0,0)",
        "subheading_color_mobile": "rgba(0,0,0,0)",
        "text_alignment_mobile": "same_as_desktop",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 40,
        "css_class": "",
        "custom_css": ""
      }
    },
    "video_section_UryJeR": {
      "type": "video-section",
      "disabled": true,
      "settings": {
        "title": "Over 6,000 5 Star Reviews!",
        "review_description": "\"I got a picture of our dog done for my daughter’s room! It was amazing to see how well the photo was turned into a piece of art! Working with the artist was super easy! I would definitely get another!\"",
        "review_stars": 5,
        "review_video_1": "https://cdn.shopify.com/videos/c/o/v/54eb74c150fc4b039451525ee060b7a4.mov",
        "review_video_2": "https://cdn.shopify.com/videos/c/o/v/c8b2dc0e9f91410497d6e3312bb62013.mov",
        "review_video_3": "https://cdn.shopify.com/videos/c/o/v/095878c978374f03aba950b910a78a94.mp4",
        "review_video_4": "https://cdn.shopify.com/videos/c/o/v/00defda86689482f90e79802d0ec194d.mov",
        "review_video_5": "",
        "review_video_6": "",
        "review_star_checked_color": "#47d7ac",
        "review_star_empty_color": "",
        "review_header_color": "",
        "review_description_color": "",
        "video_section_background_color": "#e9eafd",
        "review_container_background_color": "#ffffff"
      }
    },
    "how_to_submit_pictures_QRf9eE": {
      "type": "how-to-submit-pictures",
      "settings": {
        "title": "How to Submit the Best Picture of your Pet!",
        "tab_1_image": "shopify://shop_images/Photos_of_Pet_example_600x_62ecd047-60fb-4655-a953-f1ecf237cffe.jpg",
        "tab_1_name": "One Pet Front and Center",
        "tab_1_description": "For the perfect custom yoga mat , please submit clear, close-up images of your pet’s face and chest, ensuring that the photos feature only the pet you wish to replicate, to accurately capture their unique features and create a one-of-a-kind yoga mat.",
        "tab_2_image": "shopify://shop_images/Submit-Image-Two.png",
        "tab_2_name": "Positioning",
        "tab_2_description": "Images of your pet sitting down or standing up is ideal to center their features prominently on the custom yoga mat!",
        "tab_3_image": "shopify://shop_images/Submit-Image-Three.png",
        "tab_3_name": "Lighting",
        "tab_3_description": "Ensure there is good lighting in the photos to accurately capture your pet's true colors, allowing their natural hues to shine through."
      }
    },
    "index_image_with_text_M4MBCY": {
      "type": "index__image-with-text",
      "blocks": {
        "text_MDgEjR": {
          "type": "text",
          "settings": {
            "title": "Hand-Illustrated Artistry",
            "heading_size": "regular",
            "text": "<p>Using the photo you provide, our artists will meticulously hand-illustrate every detail and feature of your cherished pet, transforming your yoga practice into a truly personal experience</p><p>No shortcuts, no filters, and certainly no AI. We believe that the genuine essence of your furry friend can only be captured by the human touch, translating their unique personality into a stunning piece of art that you'll treasure for a lifetime.</p>",
            "alignment": "left",
            "vertical_position": "center",
            "button_label": "",
            "link": "",
            "button_style": "button--primary",
            "background": "rgba(0,0,0,0)",
            "text_color": "rgba(0,0,0,0)",
            "mobile_title": "",
            "mobile_heading_size": "same_as_desktop",
            "mobile_text": "",
            "mobile_alignment": "same_as_desktop"
          }
        },
        "image_MnJgLG": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/yoga-mat_all-colors_1000px_1.gif",
            "image_link": ""
          }
        }
      },
      "block_order": ["text_MDgEjR", "image_MnJgLG"],
      "settings": {
        "width": "standard",
        "show_gutter": true,
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "mobile_text_position": "same_as_desktop",
        "mobile_padding_top": 18,
        "mobile_padding_bottom": 10,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_heading_tUt7C6": {
      "type": "index__heading",
      "disabled": true,
      "settings": {
        "preheading": "",
        "title": "Created by Pet Lovers - For Pet Lovers",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 0,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 0,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_gallery_6cMDQq": {
      "type": "index__gallery",
      "blocks": {
        "image_bCX8cR": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/portrait_PDP_Reviews_3.png",
            "link": ""
          }
        },
        "image_jhb7Ma": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/portrait_PDP_Reviews_2.png",
            "link": ""
          }
        },
        "image_T4ecWX": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/portrait_PDP_Reviews_1.png",
            "link": ""
          }
        },
        "image_kiEDbP": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/portrait_PDP_Reviews_4.png",
            "link": ""
          }
        }
      },
      "block_order": [
        "image_bCX8cR",
        "image_jhb7Ma",
        "image_T4ecWX",
        "image_kiEDbP"
      ],
      "disabled": true,
      "settings": {
        "gallery_type": "classic",
        "images_per_row": 4,
        "crop_images": false,
        "enable_lightbox": false,
        "overlay_color": "#000000",
        "link_color": "#ffffff",
        "width": "standard",
        "show_gutter": true,
        "padding_top": 0,
        "padding_bottom": 20,
        "animation": "none",
        "mobile_layout": "slider",
        "crop_images_mobile": false,
        "show_arrows": false,
        "show_navigation_dots": true,
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 15,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_image_with_text_Gr4GLj": {
      "type": "index__image-with-text",
      "blocks": {
        "image_W4Pwhy": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/yoga_mat_3.png",
            "image_link": ""
          }
        },
        "text_7yRRWe": {
          "type": "text",
          "settings": {
            "title": "A Celebration of Your Pet",
            "heading_size": "regular",
            "text": "<p>The bond and affection we share with our pets are truly special. At Cuddle Clones, we aim to honor this special connection with a one-of-a-kind, cuddly keepsake that is a mirror image of your pet.</p><ul><li>Memorialize a beloved pet</li><li>Gift joy to those missing their pets during life's transitions</li><li>A thoughtful gift for those heading to college or moving away, missing their family pet.</li><li>An ideal present for anyone with a deep love for their pet</li></ul>",
            "alignment": "left",
            "vertical_position": "center",
            "button_label": "",
            "link": "",
            "button_style": "button--primary",
            "background": "rgba(0,0,0,0)",
            "text_color": "rgba(0,0,0,0)",
            "mobile_title": "",
            "mobile_heading_size": "same_as_desktop",
            "mobile_text": "",
            "mobile_alignment": "same_as_desktop"
          }
        }
      },
      "block_order": ["image_W4Pwhy", "text_7yRRWe"],
      "settings": {
        "width": "standard",
        "show_gutter": true,
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "mobile_text_position": "same_as_desktop",
        "mobile_padding_top": 18,
        "mobile_padding_bottom": 10,
        "css_class": "",
        "custom_css": ""
      }
    },
    "170894921511d6b216": {
      "type": "apps",
      "blocks": {
        "judge_me_reviews_review_widget_MG7dK8": {
          "type": "shopify://apps/judge-me-reviews/blocks/review_widget/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        }
      },
      "block_order": ["judge_me_reviews_review_widget_MG7dK8"],
      "settings": {
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_heading_AQJ8ni": {
      "type": "index__heading",
      "settings": {
        "preheading": "",
        "title": "Frequently Asked Questions",
        "subheading": "",
        "heading_alignment": "center",
        "vertical_spacing": "medium",
        "preheading_color": "rgba(0,0,0,0)",
        "heading_color": "rgba(0,0,0,0)",
        "subheading_color": "rgba(0,0,0,0)",
        "background": "rgba(0,0,0,0)",
        "gradient": "rgba(0,0,0,0)",
        "gradient_rotation": 0,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 0,
        "padding_left": 0,
        "padding_right": 0,
        "animation": "none",
        "mobile_text_alignment": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 0,
        "css_class": "",
        "custom_css": ""
      }
    },
    "index_faq_XTnpfU": {
      "type": "index__faq",
      "blocks": {
        "content_JcCiqW": {
          "type": "content",
          "settings": {
            "title": "What material is the yoga mat made of?",
            "answer": "<p>The Cuddle Clones yoga mat features a cushioning and textured grip, made from an FSC™-certified natural rubber base.</p>"
          }
        },
        "content_RWDVqf": {
          "type": "content",
          "settings": {
            "title": "What are the dimensions and weight of the yoga mat?",
            "answer": "<p><strong>Dimensions:</strong> 66 cm x 183 cm (25\" x 72\", 5mm thickness (0.19\")</p><p><strong>Weight:</strong> 3.30 kg (7.28 lbs)</p>"
          }
        },
        "content_rAwFaa": {
          "type": "content",
          "settings": {
            "title": "How do I care for my yoga mat?",
            "answer": "<p>After each use, clean your mat with a natural cleanser, ensure it is completely dry before rolling, and allow it to air dry. Store the mat away from direct sunlight. Over time, the color of your mat may change.</p>"
          }
        },
        "content_LVQL6n": {
          "type": "content",
          "settings": {
            "title": "What types of pets can you illustrate?",
            "answer": "<p>From scales to tails, we illustrate all pets with love and care!</p>"
          }
        },
        "content_XGa8JX": {
          "type": "content",
          "settings": {
            "title": "What type of photos should I submit for the best outcome?",
            "answer": "<p>We carefully review each photo as orders come in. If we find any issues with the submitted images, we'll contact you via email to request additional pictures of your pet. To ensure the highest quality portrait, we recommend submitting photos that are taken in natural daylight (without flash) and at your pet's eye level.</p>"
          }
        },
        "content_qPcFzg": {
          "type": "content",
          "settings": {
            "title": "Do you offer expedited shipping options?",
            "answer": "<p>Expedited shipping options available for your order will be listed at checkout. All tracking numbers are generated immediately after creation is completed. Tracking numbers will begin receiving updates after your order is transferred to our sorting facility.</p>"
          }
        }
      },
      "block_order": [
        "content_JcCiqW",
        "content_RWDVqf",
        "content_rAwFaa",
        "content_LVQL6n",
        "content_XGa8JX",
        "content_qPcFzg"
      ],
      "settings": {
        "title": "",
        "width": "standard",
        "padding_top": 6,
        "padding_bottom": 56,
        "animation": "none",
        "padding_top_mobile": 7,
        "padding_bottom_mobile": 56,
        "css_class": "",
        "custom_css": ""
      }
    },
    "recommendations": {
      "type": "product__recommendations",
      "settings": {
        "show_product_recommendations": true,
        "product_recommendations_style": "slider",
        "product_recommendations_heading": "You May Also Like",
        "products_per": 4,
        "recommended_products_limit": 4,
        "align_height": true,
        "collection_height": 300,
        "width": "standard",
        "padding_top": 20,
        "padding_bottom": 20,
        "animation": "none",
        "padding_top_mobile": 20,
        "padding_bottom_mobile": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "zp-product-footer-content": {
      "type": "product-footer-content-zipifypages",
      "settings": {}
    }
  },
  "order": [
    "zp-product-header-content",
    "breadcrumbs",
    "sidebar",
    "main",
    "index_heading_TXCMmP",
    "index_image_with_text_overlay_N4qixp",
    "video_section_UryJeR",
    "how_to_submit_pictures_QRf9eE",
    "index_image_with_text_M4MBCY",
    "index_heading_tUt7C6",
    "index_gallery_6cMDQq",
    "index_image_with_text_Gr4GLj",
    "170894921511d6b216",
    "index_heading_AQJ8ni",
    "index_faq_XTnpfU",
    "recommendations",
    "zp-product-footer-content"
  ]
}
