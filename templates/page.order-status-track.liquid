{% comment %} Order Status Tracking Page {% endcomment %}

{% liquid
  if settings.form_button_style contains 'primary'
    assign field_input_size = settings.button_primary_padding
    assign field_input_style = 'primary-btn-style'
  elsif settings.form_button_style contains 'secondary'
    assign field_input_size = settings.button_secondary_padding
    assign field_input_style = 'secondary-btn-style'
  else
    assign field_input_size = settings.button_link_padding
  endif
%}

<section class="section is-width-standard">
  <div class="container content contact-form contact-form--center">
    <div class="two-thirds offset-by-three medium-down--one-whole column">
      <header class="page-header">
        <h1 class="page-title">{{ page.title }}</h1>
        {% if page.content != blank %}
          <div class="page-content">
            {{ page.content }}
          </div>
        {% endif %}
      </header>

      <div class="contact-form__form">
        <div class="form__success-message" id="tracking-success" style="display: none;"></div>
        <div class="one-whole column contact-form__form-errors">
          <p class="form__error" id="tracking-error" style="display: none;"></p>
        </div>

        <form id="order-tracking-form" class="contact-form contact-form--contact-section">
          <input type="hidden" name="challenge" value="false">

          <div class="contact-form__blocks">
            <div class="container">
              <!-- Order Number Field -->
              <div class="one-whole column">
                <div class="contact-form__block contact-form__block--textfield">
                  <label for="order-number" class="label {% if settings.use_placeholders %}is-sr-only{% endif %}">
                    Order Number <span class="required">*</span>
                  </label>
                  <input
                    type="text"
                    placeholder="{% if settings.use_placeholders %}Order Number*{% endif %}"
                    name="orderNumber"
                    id="order-number"
                    class="input is-{{ field_input_style }} is-{{ field_input_size }}"
                    required="required"
                  >
                </div>
              </div>

              <!-- Customer Email Field -->
              <div class="one-whole column">
                <div class="contact-form__block contact-form__block--email">
                  <label for="customer-email" class="label {% if settings.use_placeholders %}is-sr-only{% endif %}">
                    Customer Email <span class="required">*</span>
                  </label>
                  <input
                    type="email"
                    placeholder="{% if settings.use_placeholders %}Customer Email*{% endif %}"
                    id="customer-email"
                    class="input is-{{ field_input_style }} is-{{ field_input_size }}"
                    name="customerEmail"
                    autocorrect="off"
                    autocapitalize="off"
                    required="required"
                  >
                </div>
              </div>
            </div>
          </div>

          <div class="container">
            <div class="one-whole column">
              {% assign submit_label = 'Track Order' %}
              {% render 'button',
                label: submit_label,
                type: 'submit',
                style: settings.form_button_style,
                class: 'is-within-form',
                attribute: 'id="track-order-btn"'
              %}
            </div>
          </div>
        </form>

        <!-- Order Status Results -->
        <div id="order-status-results" class="order-status-results" style="display: none;">
          <div class="order-status-content">
            <!-- Results will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{% style %}
  .order-status-results {
    margin-top: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #ddd;
  }

  .order-status-content {
    line-height: 1.6;
  }

  .order-status-content h3 {
    margin-bottom: 1rem;
    color: #333;
  }

  .order-status-content .status-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
  }

  .order-status-content .status-item:last-child {
    border-bottom: none;
  }

  .order-status-content .status-label {
    font-weight: 600;
    display: inline-block;
    min-width: 120px;
  }

  .form__success-message {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border: 1px solid #c3e6cb;
  }

  .status-url-item {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1rem !important;
    margin: 1rem 0;
  }

  .status-url-container {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 0.5rem;
  }

  .status-url-link {
    background: #007bff;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .status-url-link:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
  }

  .copy-url-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
  }

  .copy-url-btn:hover {
    background: #545b62;
  }

  .line-item-details {
    margin-left: 1rem;
    font-size: 0.9rem;
  }

  .line-item-details div {
    margin-bottom: 0.25rem;
  }

  .item-status {
    font-weight: 600;
    color: #28a745;
  }

  .item-priority {
    font-weight: 600;
    color: #dc3545;
  }

  .status-unfulfilled {
    color: #ffc107;
    font-weight: 600;
  }

  .status-fulfilled {
    color: #28a745;
    font-weight: 600;
  }

  @media only screen and (max-width: 798px) {
    .order-status-results {
      margin-top: 1rem;
      padding: 1rem;
    }

    .status-url-container {
      flex-direction: column;
      align-items: stretch;
    }

    .status-url-link,
    .copy-url-btn {
      text-align: center;
      width: 100%;
    }
  }
{% endstyle %}

<script src="{{ 'order-tracking.js' | asset_url }}" defer></script>
