{% comment %} Order Status Tracking Page {% endcomment %}

{% liquid
  if settings.form_button_style contains 'primary'
    assign field_input_size = settings.button_primary_padding
    assign field_input_style = 'primary-btn-style'
  elsif settings.form_button_style contains 'secondary'
    assign field_input_size = settings.button_secondary_padding
    assign field_input_style = 'secondary-btn-style'
  else
    assign field_input_size = settings.button_link_padding
  endif
%}

<div class="container main content">
  <div class="one-whole column">
    <div class="featured-collection">
      <div class="container">
        <div class="one-whole column">
          <header class="page-header">
            <h1 class="page-title">{{ page.title }}</h1>
            {% if page.content != blank %}
              <div class="page-content">
                {{ page.content }}
              </div>
            {% endif %}
          </header>

          <div class="order-tracking-form-wrapper">
            <div class="order-tracking-form">
              <h2>Track Your Order</h2>
              <p>Enter your order details below to track your order status:</p>

              <form id="order-tracking-form" class="contact-form">
                <div class="form__success-message" id="tracking-success" style="display: none;"></div>
                <div class="form__error-message" id="tracking-error" style="display: none;"></div>

                <!-- Order Number Field -->
                <div class="field">
                  <label class="label {% if settings.use_placeholders %}is-sr-only{% endif %}" for="order-number">
                    Order Number <span class="required">*</span>
                  </label>
                  <div class="control">
                    <input
                      class="input is-{{ field_input_style }} is-{{ field_input_size }}"
                      type="text"
                      id="order-number"
                      name="orderNumber"
                      placeholder="{% if settings.use_placeholders %}Order Number*{% endif %}"
                      required
                    >
                  </div>
                </div>

                <!-- Email Field -->
                <div class="field">
                  <label class="label {% if settings.use_placeholders %}is-sr-only{% endif %}" for="customer-email">
                    Email Address <span class="required">*</span>
                  </label>
                  <div class="control">
                    <input
                      class="input is-{{ field_input_style }} is-{{ field_input_size }}"
                      type="email"
                      id="customer-email"
                      name="customerEmail"
                      placeholder="{% if settings.use_placeholders %}Email Address*{% endif %}"
                      required
                    >
                  </div>
                </div>

                <!-- Search Button -->
                <div class="field">
                  <div class="control">
                    {% render 'button',
                      label: 'Track Order',
                      type: 'submit',
                      style: 'button is-primary',
                      class: 'is-fullwidth',
                      attribute: 'id="track-order-btn"'
                    %}
                  </div>
                </div>
              </form>

              <!-- Order Status Results -->
              <div id="order-status-results" class="order-status-results" style="display: none;">
                <div class="order-status-content">
                  <!-- Results will be populated by JavaScript -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .order-tracking-form-wrapper {
    max-width: 600px;
    margin: 2rem auto;
    padding: 2rem;
    background: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .order-tracking-form h2 {
    text-align: center;
    margin-bottom: 1rem;
    color: #333;
  }

  .order-tracking-form p {
    text-align: center;
    margin-bottom: 2rem;
    color: #666;
  }

  .order-tracking-form .field {
    margin-bottom: 1.5rem;
  }

  .order-tracking-form .label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
  }

  .required {
    color: #e74c3c;
  }

  .form__success-message {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border: 1px solid #c3e6cb;
  }

  .form__error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border: 1px solid #f5c6cb;
  }

  .order-status-results {
    margin-top: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #ddd;
  }

  .order-status-content {
    line-height: 1.6;
  }

  .order-status-content h3 {
    margin-bottom: 1rem;
    color: #333;
  }

  .order-status-content .status-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
  }

  .order-status-content .status-item:last-child {
    border-bottom: none;
  }

  .order-status-content .status-label {
    font-weight: 600;
    display: inline-block;
    min-width: 120px;
  }

  @media (max-width: 768px) {
    .order-tracking-form-wrapper {
      margin: 1rem;
      padding: 1rem;
    }
  }
</style>

<script src="{{ 'order-tracking.js' | asset_url }}" defer></script>
