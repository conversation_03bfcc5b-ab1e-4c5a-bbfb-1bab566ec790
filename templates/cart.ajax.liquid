{% layout none %}

<template data-options>
  {
    "item_count": {{ cart.item_count | json }}
  }
</template>

<template data-html="content">
  {% comment %} No items in the cart message {% endcomment %}
  <div class="ajax-cart__cart-count">
    <p class="ajax-cart__cart-title">
      {{ 'cart.general.title' | t }}
      <span
        class="ajax-cart__cart-item-count"
        data-bind="itemCount"
      >
        {{- cart.item_count -}}
      </span>
    </p>

    <div
      class="ajax-cart__close-icon"
      data-ajax-cart-close
    >
      {%
        render 'icon',
        name: 'x',
      %}
    </div>
  </div>

  <div
    class="
      ajax-cart__empty-cart-message
      text-align-center
      has-margin-top
      {% if cart.item_count > 0 %}
        is-hidden
      {% endif %}
    "
  >
    {%
      render 'icon',
      name: settings.cart_icon
    %}
    <p class="ajax-cart__empty-text">{{ 'layout.general.empty_cart' | t }}</p>
    <a href="{{ routes.all_products_collection_url }}">{{ 'cart.general.continue_shopping_link_html' | t }}</a>
  </div>

  <form action="{{ routes.cart_url }}" method="post"
          class="ajax-cart__form {% if cart.item_count == 0 %}is-hidden{% endif %}">
    <div class="ajax-cart__list">
      {% assign total_saving = 0 %}

      {% comment %} Loop through cart products {% endcomment %}
      {% for item in cart.items %}
        {% if item.variant.compare_at_price > item.variant.price %}
          {% assign saving = item.variant.compare_at_price | minus: item.variant.price | times: item.quantity %}
          {% assign total_saving = saving | plus: total_saving %}
        {% endif %}

        <div class="ajax-cart__product media" data-cart-item="{{ item.key }}" data-line-item="{{ forloop.index }}">
          {% comment %} Image {% endcomment %}
          <figure class="ajax-cart__product-image media-left">
            {% if item.product.media %}
              {% if item.product.featured_media.preview_image %}
                <a href="{{ item.url }}">
                  <img src="{{ item.image | img_url: '320x' }}" alt="{{ item.title | escape }}" />
                </a>
              {% endif %}
            {% else %}
              {% if item.image %}
                <a href="{{ item.url }}">
                  <img src="{{ item | img_url: '320x' }}" alt="{{ item.title | escape }}" />
                </a>
              {% endif %}
            {% endif %}
          </figure>

          <div class="ajax-cart__product-content media-content">
            <div class="ajax-cart__product-title">
              <a href="{{ item.url }}">
                {{ item.product.title }}
                {% unless item.product.has_only_default_variant or item.variant.title contains "Title" %}
                  -
                  {% for option in item.product.options %}
                    {{ item.variant.options[forloop.index0] }} {% unless forloop.last %}/{% endunless %}
                  {% endfor %}
                {% endunless %}
              </a>
            </div>

            <div class="ajax-cart__line-items">
              {% if item.properties %}
                {% for p in item.properties %}
                  {% if item.product.type == 'Plush'%}
                    {% if p.first contains '_pet_type' %}
                      <div class="ajax-cart__line-item">
                         Pet Profile: {{ p.last |  capitalize }}
                      </div>
                    {% elsif p.first contains 'mouth' %}
                      <div class="ajax-cart__line-item">
                        Mouth Postition: {{ p.last |  capitalize |  replace: '-',' ' }}
                      </div>
                      {% elsif p.first contains 'tail' %}
                        <div class="ajax-cart__line-item">
                          Tail Postition: {{ p.last |  capitalize |  replace: '-',' ' }}
                        </div>
                        {% elsif p.first contains 'body' %}
                          <div class="ajax-cart__line-item">
                            Body Postition: {{ p.last |  capitalize |  replace: '-',' ' }}
                          </div>
                    {% endif %}
                  {% else %}
                    {% assign property_first_char = p.first | slice: 0 %}
                    {% if p.last != blank and property_first_char != '_' %}
                      <div class="ajax-cart__line-item">
                        {{ p.first |  capitalize}}: {{ p.last }}
                      </div>
                    {% endif %}

                  {% endif %}
                 
                {% endfor %}
              {% endif %}
            </div>

            {% if item.selling_plan_allocation.selling_plan %}
              <p class="ajax-cart__selling-plan-name">{{ item.selling_plan_allocation.selling_plan.name }}</p>
            {% endif %}

            <div class="ajax-cart__line-items-discount">
              {% if item.line_level_discount_allocations.size > 0 %}
                <div class="ajax-cart__price-comparison">
                  <p class="sale">
                    <span class="money">
                      {%
                        render 'price-element',
                        price: item.final_price
                      %}
                    </span>
                  </p>
                  {% if item.original_line_price > item.final_line_price %}
                    <s class="original-price">
                      <span class="money">
                        {%
                          render 'price-element',
                          price: item.original_price
                        %}
                      </span>
                    </s>
                  {% endif %}
                </div>
                {% for discount_allocation in item.line_level_discount_allocations %}
                  <div class="line-item-discount__container">
                    <p>{{ discount_allocation.discount_application.title }}</p>
                  </div>
                {% endfor %}
              {% endif %}
            </div>

            {% if item.line_level_discount_allocations.size < 1 %}
              <div class="ajax-cart__price price">
                <span class="money {% if item.original_price < item.variant.compare_at_price %}sale{% endif %}">
                  {%
                    render 'price-element',
                    price: item.original_line_price
                  %}
                </span>
                {% if item.original_price < item.variant.compare_at_price %}
                  {%- assign total_line_saving = 0 -%}
                  {%- assign line_saving = item.variant.compare_at_price | times: item.quantity -%}
                  {%- assign total_line_saving = line_saving | plus: total_line_saving -%}
                  <span class="money compare-at-price">
                    {%
                      render 'price-element',
                      price: total_line_saving
                    %}
                  </span>
                {% endif %}
              </div>
            {% endif %}

            {%
              render 'unit-price',
              item: item,
              class: 'ajax-cart__unit-price'
            %}

            {% if settings.display_ajax_quantity_box %}
              <div class="ajax-cart__quantity-box product-quantity-box" data-line-item-key={{ forloop.index }}>
                {%
                  render 'quantity-box',
                  item: item,
                  size: 'is-medium',
                  variant: item.variant
                %}
              </div>
              <p class="ajax-cart__quantity-warning quantity-warning">
            {% endif %}
          </div>

          <div class="ajax-cart__right-content media-right">
            <a class="ajax-cart__delete" data-ajax-cart-delete data-cart-item-key="{{ item.key }}" href="{{ routes.cart_change_url }}?line={{ forloop.index }}&amp;quantity=0" title="{{ 'cart.general.remove' | t }}" >
              <button class="close" aria-label="close">
                {%
                  render 'icon',
                  name: 'x'
                %}
              </button>
            </a>
          </div>
        </div>
      {% endfor %}
    </div>

    <div class="ajax-cart__details-wrapper">
      {%- if cart.cart_level_discount_applications != blank -%}
        <div class="cart__discounts ajax-cart__details-row is-flex is-justify-space-between sale">
        {%- for discount_application in cart.cart_level_discount_applications -%}
          <div class="ajax-cart__row-description">
            <p>{{- discount_application.title -}}</p>
          </div>
          <p class="ajax-cart__discount">
            <span class="money">
              -
              {%
                render 'price-element',
                price: discount_application.total_allocated_amount
              %}
            </span>
          </p>
        {%- endfor -%}
        </div>
      {%- endif -%}

      {% comment %} Subtotal {% endcomment %}
      <div class="ajax-cart__subtotal ajax-cart__details-row is-flex is-justify-space-between">
        <div class="ajax-cart__row-description">
          <span>{{ 'layout.general.subtotal' | t }}:</span>
        </div>
        <span class="money">
          {%
            render 'price-element',
            price: cart.total_price
          %}
        </span>
      </div>

      {% comment %} Total savings {% endcomment %}
      {% if settings.display_savings and total_saving > 0 %}
        <div class="ajax-cart__savings ajax-cart__details-row is-flex is-justify-space-between sale">
          <div class="ajax-cart__row-description">
            <span>{{ 'layout.general.savings' | t }}:</span>
          </div>
          <span class="money">
            {% assign total_savings = total_saving | plus: cart.total_discount %}
            {%
              render 'price-element',
              price: total_savings
            %}
          </span>
        </div>
      {% endif %}

      {% comment %} Special instructions {% endcomment %}
      {% if settings.display_special_instructions and settings.go_to_checkout %}
        <textarea id="note" name="note" rows="2" placeholder="{{ 'layout.general.cart_note' | t }}" class="ajax-cart__note">{{ cart.note }}</textarea>
      {% endif %}

      {%- comment -%} Taxes and shipping notice {%- endcomment -%}
      <div class="is-align-center text-align-center ajax-cart__taxes-shipping">
        {%- capture taxes_shipping_checkout -%}
          {%- if cart.taxes_included and shop.shipping_policy.body != blank -%}
            {{ 'cart.general.taxes_included_and_shipping_policy_html' | t: link: shop.shipping_policy.url }}
          {%- elsif cart.taxes_included -%}
            {{ 'cart.general.taxes_included_but_shipping_at_checkout' | t }}
          {%- elsif shop.shipping_policy.body != blank -%}
            {{ 'cart.general.taxes_and_shipping_policy_at_checkout_html' | t: link: shop.shipping_policy.url }}
          {%- else -%}
            {{ 'cart.general.tax_and_shipping' | t }}
          {%- endif -%}
        {%- endcapture -%}

        <p class="ajax-cart__taxes-shipping-message has-padding-top">{{ taxes_shipping_checkout }}</p>
      </div>

      {% comment %} Terms of service checkbox {% endcomment %}
      {% if settings.display_tos_checkbox and settings.go_to_checkout %}
        <div class="ajax-cart__tos-checkbox text-align-center">
          <p class="tos">
          <input type="checkbox" class="tos_agree" id="sliding_agree" required />
            <label for="sliding_agree" class="tos_label">
              {{ 'cart.general.agree_to_terms_html' | t }}
            </label>
            {% if settings.tos_page != blank %}
              <a href="{{ pages[settings.tos_page].url }}" target="_blank" class="tos_icon">{{ 'cart.general.view_terms' | t }}</a>
            {% endif %}
        </p>
        </div>

      {% endif %}

      {% comment %} Cart message {% endcomment %}
      {% if settings.cart_message != blank %}
        <div class="ajax-cart__cart-message text-align-center">
          {{ settings.cart_message }}
        </div>
      {% endif %}

      {% comment %} Checkout {% endcomment %}
      <div class="ajax-cart__button-wrapper text-align-center">
        {% if settings.go_to_checkout %}
          <button type="submit" name="checkout" class="ajax-cart__button button--add-to-cart button">
            {% if settings.show_lock_icon %}
              {%
                render 'icon',
                name: 'lock'
              %}
            {% endif %}
            {{ 'layout.general.checkout' | t }}
          </button>
          <a class="ajax-cart__cart-link" href="{{ routes.cart_url }}" >{{ 'layout.general.go_to_cart' | t }}</a>
        {% else %}
          <a href="{{ routes.cart_url }}" class="ajax-cart__button button--add-to-cart button">
            {% if settings.show_lock_icon %}
              {%
                render 'icon',
                name: 'lock'
              %}
            {% endif %}
            {{ 'layout.general.go_to_cart' | t }}
          </a>
        {% endif %}
      </div>

    </div>
  </form>
</template>
